package com.wonderslate.commons

class UIConstants {
    companion object{
        const val shareTo = "Share To"
        const val resourceAddedToChapter = "Resource added to chapter"
        const val urlNotValid = "URL not valid"
        const val notificationsArrayConversionError = "Could not convert notification string to array"
        const val chapterDetailsLoadingAnimationText ="Preparing Chapter List"
        const val chapterDetailsErrorAnimationText ="Error Fetching Chapter List"
        const val chapterListFetchError = "Problem while getting chapter list"
        const val offlineDataNotFoundError = "No Offline Data Found"
        const val shareToError = "Couldn't share now."
        const val addBookToLibraryError = "There was a problem while adding book.. "
        const val paymentProcessError = "Error in payment process"
        const val paymentFailedError = "Sorry, we are facing some problem regarding payment.\nPlease try again later."
        const val paymentConfirmationError = "Error while sending payment confirmation to server"
        const val paymentConfirmationUserError = "Sorry, we are facing some problems regarding payment.\nPlease try again later."
        const val notificationObjectNotFound ="error while getting notification object"
        const val chapterListLoading = "Please Wait.."
        const val shareBookMessage = "Preparing to Share Book"
        const val clientError = "internal server error"
        const val serverError = "server not responding "
        const val noConnectionError = "could not reach server"
        const val somethingWentWrong = "something went wrong"
        const val serverUnderMaintenance = "Server under maintenance! please try after some time."
    }
}