package com.wonderslate.commons.utils

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.commons.interfaces.JSONParser
import org.json.JSONArray

class GsonJSONParser(private val gson: Gson = Gson()) : JSONParser {

    override fun <T> fromJson(json: String, classOfT: Class<T>): T? {
        return try {
            gson.fromJson(json, classOfT)
        } catch (e: Exception) {
            null
        }
    }

    override fun toJson(src: Any): String {
        return gson.toJson(src)
    }

    override fun <T> parseList(jsonArray: JSONArray, classOfT: Class<T>): List<T> {
        return try {
            val jsonString = jsonArray.toString()
            val listType = TypeToken.getParameterized(List::class.java, classOfT).type
            gson.fromJson(jsonString, listType) ?: emptyList()
        } catch (e: Exception) {
            emptyList()
        }
    }
}
