package com.ws.book_details.ui.adapters

import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.ws.book_details.R
import com.ws.book_details.data.models.BookChapterItem
import com.ws.commons.extensions.clearAndAddAll
import com.ws.core_ui.extensions.newLayoutInflater

class ChaptersBookDetailsAdapter(private val data: ArrayList<BookChapterItem>) : RecyclerView.Adapter<ChaptersBookDetailsAdapter.ViewHolder>() {
    private var mClickListener: ItemClickListener? = null
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = parent.context.newLayoutInflater().inflate(R.layout.item_chapters_name, parent, false)
        return ViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val chapter = data[position]
        val title = "${position + 1}. ${chapter.name}"
        holder.myTextView.text = title
    }
    
    override fun getItemCount(): Int {
        return data.size
    }
    
    fun updateData(newData: List<BookChapterItem>?) {
        data.clearAndAddAll(newData)
        notifyDataSetChanged()
    }
    
    inner class ViewHolder internal constructor(itemView: View) : RecyclerView.ViewHolder(itemView), View.OnClickListener {
        var myTextView: TextView = itemView.findViewById(R.id.textView_chapter)
        override fun onClick(view: View) {
            mClickListener?.onItemClick(view, adapterPosition)
        }
        
        init {
            itemView.setOnClickListener(this)
        }
    }
    
    fun setClickListener(itemClickListener: ItemClickListener?) {
        mClickListener = itemClickListener
    }
    
    interface ItemClickListener {
        fun onItemClick(view: View?, position: Int)
    }
    
}