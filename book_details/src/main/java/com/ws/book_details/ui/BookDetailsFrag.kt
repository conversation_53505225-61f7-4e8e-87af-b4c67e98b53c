package com.ws.book_details.ui

import android.os.Bundle
import android.util.Log
import android.view.*
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ws.access_code.ui.AccessCodeDialog
import com.ws.book_details.R
import com.ws.book_details.data.models.*
import com.ws.book_details.databinding.LayoutBookDetailsBinding
import com.ws.book_details.ui.adapters.ChaptersBookDetailsAdapter
import com.ws.book_details.ui.adapters.ContainsBookDetailsAdapter
import com.ws.book_details.ui.adapters.RelatedBooksAdapt
import com.ws.commons.Status
import com.ws.commons.Templates
import com.ws.commons.extensions.isFreeBook
import com.ws.commons.interfaces.HTMLParser
import com.ws.commons.interfaces.ImageUrlProvider
import com.ws.commons.models.BookCoverUrlRequest
import com.ws.commons.models.BookDetailsCoverUrlRequest
import com.ws.core_ui.extensions.*
import com.ws.core_ui.utils.IntentConstants.PARAM_BOOK_DETAILS
import com.ws.core_ui.utils.TextViewHelper
import com.ws.deeplink.data.createDeeplinkIntent
import com.ws.deeplink.data.creator.DeeplinkCreator
import com.ws.deeplink.data.models.DeeplinkCreatorData
import com.ws.core_ui.base.BaseFragmentWithListener
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class BookDetailsFrag: BaseFragmentWithListener<LayoutBookDetailsBinding, BookDetailsFrag.OnBookDetailsFragListener>() {
    
    private val viewModel by viewModel<BookDetailsFragViewModel>()
    
    private val deeplinkCreator by inject<DeeplinkCreator>()
    
    private val htmlParser by inject<HTMLParser>()
    
    private val imageUrlProvider by inject<ImageUrlProvider>()
    
    private val bookDetailsLiveData = MutableLiveData<BookDetails?>()
    
    private var fullCoverUrl = ""
    
    private val bookContainsAdapt: ContainsBookDetailsAdapter by lazy {
        ContainsBookDetailsAdapter(arrayListOf())
    }
    
    private val bookChapterAdapt: ChaptersBookDetailsAdapter by lazy {
        ChaptersBookDetailsAdapter(arrayListOf())
    }
    
    private val relatedBooksAdapt by inject<RelatedBooksAdapt>()
    
    override fun inflateViewBinding(inflater: LayoutInflater, container: ViewGroup?): LayoutBookDetailsBinding {
        return LayoutBookDetailsBinding.inflate(inflater, container, false)
    }
    
    override fun initArguments(bundle: Bundle?) {
        bookDetailsLiveData.value = bundle?.getSerializable(PARAM_BOOK_DETAILS) as BookDetails?
    }
    
    override fun initView() {
        initHeader()
        initClicks()
        showInitialValues()
        initTabs()
        initRecyclers()
        initObservers()
    }
    
    override fun load() {
        viewModel.getRelatedBooks(
            RelatedBooksRequest(
                bookId = bookDetailsLiveData.value?.bookId?.toString() ?: ""
            )
        )
        requestBookDetails()
    }
    
    private fun initTabs() {
        binding?.bookDetailsTabLayout?.addOnTabSelectedListener { tab ->
            binding?.textViewBookDetail?.visibility(getString(R.string.tab_book_details_overview).equals(tab?.text.toString(), true))
            binding?.tabChapterContents?.visibility(getString(R.string.tab_book_details_features).equals(tab?.text.toString(), true))
            binding?.tabChapterDetails?.visibility(getString(R.string.tab_book_details_contents).equals(tab?.text.toString(), true))
        }
    }
    
    private fun initClicks() {
        binding?.apply {
            val bookDetails = bookDetailsLiveData.value
            textViewOpenInLib.setOnClickListener { view ->
                listener?.onOpenInLibrary(bookDetails?.bookId?.toString())
            }
            textViewOpenBook.setOnClickListener { view ->
                listener?.onPreviewBook(bookDetails?.bookId?.toString())
            }
            textViewBuyAddToLib.setOnClickListener { view ->
                listener?.onAddBookToLibrary(bookDetails?.bookId?.toString())
            }
            textViewAccessCodeClick.setOnClickListener {
                //Show access code dialog
                showAccessCodeDialog()
            }
            btnShare.setOnClickListener { view ->
                shareBook()
            }
            relatedBooksAdapt.onBookClicked = { data ->
                bookDetailsLiveData.value = BookDetails(
                    bookId = data.id,
                    bookName = data.title,
                    coverImage = data.coverImage,
                    publisherId = data.publisherId,
                    publisherName = data.publisher,
                    sellPrintBook = false,
                    currentStock = 0,
                    bookPriceDtls = emptyList()
                )
                showInitialValues()
                requestBookDetails()
            }
        }
    }
    
    private fun showInitialValues() {
        val bookDetails = bookDetailsLiveData.value
        updateBasicBookDetails(
            bookDetails?.bookId.toString(),
            bookDetails?.coverImage ?: "",
            bookDetails?.bookName ?: "",
            bookDetails?.publisherName ?: ""
        )
    }
    
    private fun initRecyclers() {
        binding?.recyclerViewContains?.let {
            it.layoutManager = LinearLayoutManager(requireContext())
            it.adapter = bookContainsAdapt
        }
        binding?.recyclerViewChapters?.let {
            it.layoutManager = LinearLayoutManager(requireContext())
            it.adapter = bookChapterAdapt
        }
        binding?.recyclerrelatedEbooks?.let {
            it.layoutManager = LinearLayoutManager(requireContext(), RecyclerView.HORIZONTAL, false)
            it.isNestedScrollingEnabled = false
            it.adapter = relatedBooksAdapt
        }
    }
    
    private fun initHeader() {
        binding?.customHeader?.let {
            it.pageTitle.text = getString(R.string.book_details)
            it.layoutBackIcon.setOnClickListener {
                listener?.onBackButtonPressed()
            }
            try {
                val window: Window = requireActivity().window
                window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
                window.navigationBarColor = ContextCompat.getColor(requireContext(), R.color.colorAccent)
                window.statusBarColor = ContextCompat.getColor(requireContext(), R.color.colorAccent)
                val decorView: View = window.decorView
                decorView.systemUiVisibility = decorView.systemUiVisibility and View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv() //set status text  light
            } catch (e: Exception) {
                Log.e(TAG, "initHeader: ", e)
            }
        }
    }
    
    private fun initObservers() {
        
        viewModel.bookDetails.asLiveData().observe(this) { data ->
            when(data.responseType) {
                Status.LOADING -> {
                    // Using shimmer animation instead of external loader
                    binding?.llBookDetails?.hideView()
                    binding?.noDatalayout?.hideView()
                    binding?.shimmer?.apply {
                        showView()
                        startShimmer()
                    }
                }
        
                Status.SUCCESSFUL -> {
                    // Using shimmer animation instead of external loader
                    binding?.btnShare?.showView()
                    binding?.llBookDetails?.showView()
                    binding?.shimmer?.apply {
                        hideView()
                        stopShimmer()
                    }
                    data.data?.let { response ->
                        handleBookDetailsResponse(response)
                    }
                }
        
                Status.ERROR, Status.HTTP_UNAVAILABLE -> {
                    // Using shimmer animation instead of external loader
                    binding?.noDatalayout?.showView()
                    binding?.shimmer?.apply {
                        hideView()
                        stopShimmer()
                    }
                }
        
                else -> {}
            }
        }
        
        viewModel.relatedBooks.asLiveData().observe(this) { data ->
            when(data.responseType) {
                Status.LOADING -> {
                    binding?.llRelatedBooks?.hideView()
                }
        
                Status.SUCCESSFUL -> {
                    binding?.llRelatedBooks?.showView()
                    //Removes duplicate books use this Function ( distinctBy )
                    val books = data.data?.books?.distinctBy{ it.title} ?: listOf()
                    relatedBooksAdapt.updateBooks(books)
                }
        
                Status.ERROR, Status.HTTP_UNAVAILABLE -> {
                    binding?.llRelatedBooks?.hideView()
                }
                else -> {}
            }
        }
        
        bookDetailsLiveData.observe(this) {
            it?.let {
                listener?.onBookDetailsChanged(it)
                arguments = bundleOf(
                    PARAM_BOOK_DETAILS to it
                )
            }
        }
    }
    
    private fun handleBookDetailsResponse(response: BookDetailsResponse) {
        
        bookDetailsLiveData.value = BookDetails(
            response.bookId,
            response.title,
            response.coverImage,
            false,
            -1,
            response.publisherName,
            response.price.toString(),
            response.bookDesc ?: "",
            language = response.bookLangauge ?: ""
        )
        
        //Update basic book details
        updateBasicBookDetails(
            bookId = response.bookId.toString(),
            bookName = response.title,
            coverImage = response.coverImage ?: "",
            publisherName = response.publisherName ?: ""
        )

        /*if (BookType.getBookTypeFor(response.bookType) == BookType.MCQ_EBOOK) {
            binding?.tvBookType?.text = "Online Tests"
        }
        else {
            binding?.tvBookType?.text = "eBook"
        }*/

        if (response.hasQuiz == "true") {
            binding?.tvBookType?.text = "Online Tests"
        }
        else {
            binding?.tvBookType?.text = "eBook"
        }

        binding?.flBookTag?.visibility = View.VISIBLE
        
        binding?.textViewBookDetail?.text = if(response.bookDesc.isNullOrBlank()) {
            getString(R.string.no_book_description_available)
        } else {
            htmlParser.toSimpleText(response.bookDesc)
        }
        if((binding?.textViewBookDetail?.text?.length ?: 0) > 250) {
            TextViewHelper.makeTextViewResizable(binding?.textViewBookDetail, 3, "..Read more", true)
        }
        
        val bookDetails = bookDetailsLiveData.value
        binding?.textViewPublisherName?.text = when {
            !response.publisherName.isNullOrBlank() -> response.publisherName
            bookDetails?.publisherName?.isNotBlank() == true -> bookDetails.publisherName
            else -> getString(R.string.app_name)
        }
        
        binding?.textViewBadgeFree?.apply {
            visibility(!response.price.isFreeBook())
            text = Templates.PAYMENT_AMOUNT_TEMPLATE.format(response.price)
        }
        binding?.llAccessCodeParent?.visibility(!response.inLibrary)
        binding?.layoutButtonOpenInLib?.visibility(response.inLibrary)
        binding?.layoutButtonOpenBuyAddtolib?.visibility(!response.inLibrary)
        binding?.textViewBuyAddToLib?.text = if(response.price.isFreeBook()) TEXT_ADD_TO_LIBRARY else TEXT_BUY_NOW
        binding?.textViewOpenBook?.text = TEXT_PREVIEW
    
        val totalChapters = response.chapters.size
        binding?.textViewChapterCount?.text = when (totalChapters) {
            0 -> ""
            1 -> "1 Chapter"
            else -> "$totalChapters Chapters"
        }
        
        bookChapterAdapt.updateData(response.chapters)
        val resources = response.bookResources.map {
            val textAndIcon = it.res_type.getFormattedResTypeAndIcon()
            val count = if(textAndIcon.first == "MCQ's" && response.totalMcqs != null) response.totalMcqs else it.res_count
            BookResource(
                textAndIcon.first,
                count,
                ContextCompat.getDrawable(requireContext(), textAndIcon.second)
            )
        }
        bookContainsAdapt.updateData(resources)
    }
    
    private fun updateBasicBookDetails(
        bookId: String,
        coverImage: String,
        bookName: String,
        publisherName: String
    ) {
        binding?.apply {
            fullCoverUrl = imageUrlProvider.getBookDetailsCoverUrl(BookDetailsCoverUrlRequest(
                fileName = coverImage,
                bookId = bookId
            ))
            bookcoverimagelibrary.loadImage(fullCoverUrl, animate = true)
            textViewBookTitle.text = bookName
            textViewPublisherName.text = let {
                if(publisherName.isBlank())
                    getString(R.string.app_name)
                else
                    publisherName
            }
        }
    }
    
    private fun requestBookDetails() {
        bookDetailsLiveData.value?.let {
            viewModel.getBooksDetails(BookDetailsRequest(it.bookId.toString()))
        } ?: showToast("Problem while loading book details. Please try again.")
    }
    
    private fun showAccessCodeDialog() {
        val bookDetails = bookDetailsLiveData.value
        val dialog = bookDetails?.bookId?.let {
            AccessCodeDialog.newInstance(bookId = it.toString())
        }
        dialog?.show(childFragmentManager, ACCESS_CODE_TAG)
        receiveResultFromChildFragment(AccessCodeDialog.ACCESS_CODE_RESULT_KEY) { _, _ ->
            //Can use "result[AccessCodeDialog.ACCESS_CODE_SUCCESS_RESPONSE]" for accessing result data
            dialog?.dismissAllowingStateLoss()
            listener?.onBookAddedByAccessCode(bookDetails)
        }
    }
    
    private fun shareBook() {
        viewModel.bookDetails.value.data?.let { details ->
            binding?.libraryLoader?.showView()
            deeplinkCreator.createBookDetailsDeeplink(
                DeeplinkCreatorData(
                    bookId = details.bookId.toString(),
                    isPaidBook = !details.price.isFreeBook(),
                    bookName = details.title,
                    bookDesc = details.bookDesc ?: "",
                    bookCoverImage = fullCoverUrl
                )
            ) { status, link ->
                binding?.libraryLoader?.hideView()
                try {
                    if(status)
                        startActivity(createDeeplinkIntent(link, requireActivity().packageManager))
                    else
                        throw Exception("Failed while creating deeplink")
                } catch (e: Exception) {
                    showToast("Problem while creating link. Please try again.")
                }
            }
        }
    }
    
    override fun whenResumed() {
        if(isFragmentLoaded()) {
            requestBookDetails()
        }
    }
    
    interface OnBookDetailsFragListener {
        fun onOpenInLibrary(bookId: String?)
        fun onPreviewBook(bookId: String?)
        fun onAddBookToLibrary(bookId: String?)
        fun onBookAddedByAccessCode(bookDetails: BookDetails?)
        fun onBackButtonPressed()
        fun onBookDetailsChanged(bookDetails: BookDetails)
    }
    
    companion object {
        private const val TAG = "BookDetailsFrag"
        private const val ACCESS_CODE_TAG = "BookDetailsAccessCode"
        
        private const val TEXT_BUY_NOW = "Buy now"
        private const val TEXT_ADD_TO_LIBRARY = "Add to library"
        private const val TEXT_PREVIEW = "Preview"
        
        @JvmStatic
        fun newInstance(bookDetails: BookDetails) = BookDetailsFrag().also {
            it.arguments = bundleOf(
                PARAM_BOOK_DETAILS to bookDetails
            )
        }
    }
}