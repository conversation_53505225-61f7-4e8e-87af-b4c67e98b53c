package com.ws.book_details.data.services

import com.ws.book_details.data.models.BookDetailsRequest
import com.ws.book_details.data.models.RelatedBooksRequest
import com.ws.commons.Result

interface BookDetailsService {
    
    /**
     * For getting a book details from server
     * @param request params required for this API
     * @return JSONObject in string format
     */
    suspend fun getBookDetails(request: BookDetailsRequest): Result<String>

    /**
     * For getting affiliated price details
     * @param request params required for this API
     * @return JSONObject in string format
     */
    suspend fun getAffiliatedPrice(request: BookDetailsRequest): Result<String>
    
    /**
     * For getting related books or related bestsellers books from server
     * @param request params required for this API
     * @return JSONObject in string format
     */
    suspend fun getRelatedBooks(request: RelatedBooksRequest): Result<String>
}