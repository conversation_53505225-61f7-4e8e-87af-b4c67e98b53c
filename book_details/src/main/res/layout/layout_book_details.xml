<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:id="@+id/coordinatorLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/appBarLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.google.android.material.appbar.CollapsingToolbarLayout
                    android:id="@+id/header"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_scrollFlags="scroll|exitUntilCollapsed">

                    <include
                        android:id="@+id/customHeader"
                        layout="@layout/curved_header" />

                </com.google.android.material.appbar.CollapsingToolbarLayout>

            </com.google.android.material.appbar.AppBarLayout>

            <RelativeLayout
                android:id="@+id/relativeLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior"
                app:layout_collapseMode="pin">

                <androidx.core.widget.NestedScrollView
                    android:id="@+id/nested_seroll_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/white"
                    android:fillViewport="true">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <RelativeLayout
                            android:id="@+id/cover_layout"
                            android:layout_width="match_parent"
                            android:layout_height="300dp">

                            <RelativeLayout
                                android:layout_width="260dp"
                                android:layout_height="300dp"
                                android:layout_centerHorizontal="true"
                                android:layout_marginTop="5dp">

                                <FrameLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content">

                                    <androidx.cardview.widget.CardView
                                        android:id="@+id/placeHolderCardView"
                                        android:layout_width="250dp"
                                        android:layout_height="300dp"
                                        app:cardCornerRadius="6dp"
                                        app:cardElevation="5dp"
                                        app:cardPreventCornerOverlap="true"
                                        app:cardUseCompatPadding="true">

                                        <ImageView
                                            android:id="@+id/bookcoverimagelibrary"
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:adjustViewBounds="true"
                                            android:scaleType="centerCrop"
                                            android:src="@drawable/book_cover_placeholder" />
                                    </androidx.cardview.widget.CardView>
                                </FrameLayout>

                                <FrameLayout
                                    android:id="@+id/flBookTag"
                                    android:layout_width="70dp"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentBottom="true"
                                    android:layout_marginBottom="20dp"
                                    android:visibility="gone">

                                    <ImageView
                                        android:id="@+id/bgBookType"
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:scaleType="fitXY"
                                        android:src="@drawable/ic_paid_batch" />

                                    <TextView
                                        android:id="@+id/tvBookType"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity=""
                                        android:layout_marginHorizontal="8dp"
                                        android:layout_marginVertical="8dp"
                                        android:paddingHorizontal="8dp"
                                        android:paddingVertical="4dp"
                                        android:textColor="@color/price_simmer_badge_color"
                                        android:textSize="10sp"
                                        tools:text="MCQs eBook" />

                                </FrameLayout>
                            </RelativeLayout>

                            <androidx.cardview.widget.CardView
                                android:id="@+id/btnShare"
                                android:layout_width="36dp"
                                android:layout_height="36dp"
                                android:layout_alignParentTop="true"
                                android:layout_alignParentEnd="true"
                                android:layout_margin="16dp"
                                android:clickable="true"
                                android:contentDescription="@string/share_book"
                                android:focusable="true"
                                android:foreground="?selectableItemBackground"
                                android:visibility="gone"
                                app:cardCornerRadius="18dp">

                                <ImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:contentDescription="@string/share_book"
                                    android:padding="6dp"
                                    android:src="@drawable/ic_share_res"
                                    app:tint="@color/colorAccent" />
                            </androidx.cardview.widget.CardView>
                        </RelativeLayout>

                        <LinearLayout
                            android:id="@+id/llBookTitle"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:layout_below="@id/cover_layout">
                            <com.ws.core_ui.custom_views.WSTextView
                                android:id="@+id/textView_book_title"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/book_details_element_margin"
                                android:layout_marginTop="10dp"
                                android:layout_marginEnd="@dimen/book_details_element_margin"
                                android:ellipsize="end"
                                android:fontFamily="@font/poppins_medium"
                                android:maxLines="3"
                                android:textColor="@color/black"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                app:autoSizeMaxTextSize="20sp"
                                app:autoSizeMinTextSize="12sp"
                                app:autoSizeTextType="uniform"
                                app:enableGradient="true"
                                app:ws_font_weight="bold"
                                tools:text="Book name" />

                            <TextView
                                android:id="@+id/textView_publisher_name"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/book_details_element_margin"
                                android:fontFamily="@font/poppins_regular"
                                android:textAllCaps="true"
                                android:textColor="@color/colorActionBarTextLight"
                                android:textSize="12sp"
                                tools:text="Publisher name" />
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="start"
                                android:layout_marginStart="@dimen/book_details_element_margin"
                                android:orientation="horizontal">

                                <TextView
                                    android:id="@+id/textView_badge_free"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/colorAccent"
                                    android:textSize="18sp"
                                    android:textStyle="bold"
                                    tools:text="100" />

                                <TextView
                                    android:id="@+id/textView_badge_paid"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="8dp"
                                    android:textColor="@color/app_gray"
                                    android:textSize="18sp"
                                    android:textStyle="normal"
                                    android:visibility="visible"
                                    tools:text="150" />
                            </LinearLayout>
                        </LinearLayout>

                        <com.facebook.shimmer.ShimmerFrameLayout
                            android:id="@+id/shimmer"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="20dp"
                            android:layout_marginHorizontal="16dp"
                            android:visibility="gone"
                            android:layout_below="@id/llBookDetails">

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <View
                                    android:id="@+id/view"
                                    android:layout_width="0dp"
                                    android:layout_height="50dp"
                                    android:background="#E5E5E5"
                                    app:layout_constraintEnd_toStartOf="@+id/view2"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toTopOf="parent" />

                                <View
                                    android:id="@+id/view2"
                                    android:layout_width="0dp"
                                    android:layout_height="50dp"
                                    android:background="#E5E5E5"
                                    android:layout_marginHorizontal="16dp"
                                    app:layout_constraintEnd_toStartOf="@+id/view3"
                                    app:layout_constraintStart_toEndOf="@+id/view"
                                    app:layout_constraintTop_toTopOf="parent" />

                                <View
                                    android:id="@+id/view3"
                                    android:layout_width="0dp"
                                    android:layout_height="50dp"
                                    android:background="#E5E5E5"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintStart_toEndOf="@+id/view2"
                                    app:layout_constraintTop_toTopOf="parent" />

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="150dp"
                                    android:layout_marginTop="16dp"
                                    android:background="#E5E5E5"
                                    app:layout_constraintTop_toBottomOf="@+id/view2" />

                            </androidx.constraintlayout.widget.ConstraintLayout>

                        </com.facebook.shimmer.ShimmerFrameLayout>

                        <LinearLayout
                            android:id="@+id/llBookDetails"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/llBookTitle"
                            android:visibility="gone"
                            android:orientation="vertical">
                            <RelativeLayout
                                android:id="@+id/relative_above_bottom"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <LinearLayout
                                    android:id="@+id/layout_button_open_in_lib"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="2dp"
                                    android:layout_marginEnd="@dimen/book_details_element_margin"
                                    android:layout_marginStart="@dimen/book_details_element_margin"
                                    android:visibility="gone"
                                    android:weightSum="1">

                                    <com.ws.core_ui.custom_views.WSTextView
                                        android:id="@+id/textView_open_in_lib"
                                        android:layout_width="0dp"
                                        android:layout_height="40dp"
                                        android:layout_gravity="center"
                                        android:layout_marginTop="15dp"
                                        android:layout_marginBottom="15dp"
                                        android:layout_weight="1"
                                        android:gravity="center"
                                        android:text="Open"
                                        android:textAllCaps="true"
                                        android:textColor="@color/black"
                                        android:textSize="14sp"
                                        android:textStyle="bold"
                                        app:enableGradient="true"
                                        android:background="@drawable/bg_textview_action"
                                        app:fontFamily="@font/poppins_regular" />

                                </LinearLayout>

                                <LinearLayout
                                    android:id="@+id/layout_button_open_buy_addtolib"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:visibility="gone"
                                    android:layout_marginEnd="@dimen/book_details_element_margin"
                                    android:layout_marginStart="@dimen/book_details_element_margin"
                                    android:weightSum="2">

                                    <com.ws.core_ui.custom_views.WSTextView
                                        android:id="@+id/textView_open_book"
                                        android:layout_width="match_parent"
                                        android:layout_height="40dp"
                                        android:layout_gravity="center"
                                        android:layout_weight="1"
                                        android:gravity="center"
                                        android:layout_marginEnd="10dp"
                                        android:paddingTop="2dp"
                                        android:text="OPEN"
                                        android:textColor="@color/black"
                                        android:textSize="14sp"
                                        android:textStyle="bold"
                                        app:enableGradient="true"
                                        android:background="@drawable/bg_textview_action"
                                        app:fontFamily="@font/poppins_regular" />

                                    <!--                                    <View-->
                                    <!--                                        android:layout_width="10dp"-->
                                    <!--                                        android:layout_height="45dp"-->
                                    <!--                                        android:layout_gravity="center"-->
                                    <!--                                        android:background="@color/gradientColorStart"-->
                                    <!--                                        android:gravity="center" />-->

                                    <com.ws.core_ui.custom_views.WSTextView
                                        android:id="@+id/textView_buy_add_to_lib"
                                        android:layout_width="match_parent"
                                        android:layout_height="40dp"
                                        android:layout_gravity="center"
                                        android:layout_marginTop="15dp"
                                        android:layout_marginBottom="15dp"
                                        android:layout_weight="1"
                                        android:gravity="center"
                                        android:paddingTop="2dp"
                                        android:text="BUY NOW"
                                        android:textColor="@color/black"
                                        android:textSize="14sp"
                                        android:textStyle="bold"
                                        app:enableGradient="true"
                                        android:background="@drawable/bg_textview_action"
                                        app:fontFamily="@font/poppins_regular" />
                                </LinearLayout>
                            </RelativeLayout>

                            <LinearLayout
                                android:id="@+id/llAccessCodeParent"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="10dp"
                                android:layout_marginBottom="3dp"
                                android:layout_marginStart="@dimen/book_details_element_margin"
                                android:visibility="gone"
                                android:orientation="horizontal">

                                <TextView
                                    android:id="@+id/textView_access_code"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:fontFamily="@font/poppins_medium"
                                    android:justificationMode="inter_word"
                                    android:maxLines="1"
                                    android:text="Do you have access code?"
                                    android:textColor="@color/text"
                                    android:textSize="12sp" />

                                <TextView
                                    android:id="@+id/textView_access_code_click"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:layout_marginStart="5dp"
                                    android:fontFamily="@font/poppins_medium"
                                    android:justificationMode="inter_word"
                                    android:maxLines="1"
                                    android:text="Click here"
                                    android:textColor="@color/live_video_audio_text"
                                    android:textSize="14sp" />
                            </LinearLayout>

                            <com.google.android.material.tabs.TabLayout
                                android:id="@+id/book_details_tab_layout"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="14dp">
                                <com.google.android.material.tabs.TabItem
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/tab_book_details_overview"/>
                                <com.google.android.material.tabs.TabItem
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/tab_book_details_features"/>
                                <com.google.android.material.tabs.TabItem
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/tab_book_details_contents"/>
                            </com.google.android.material.tabs.TabLayout>

                            <RelativeLayout
                                android:id="@+id/book_details_parent_container"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="20dp"
                                android:visibility="gone">

                                <RelativeLayout
                                    android:id="@+id/layout_chapter"
                                    android:layout_width="match_parent"
                                    android:layout_height="45dp"
                                    android:layout_marginLeft="20dp"
                                    android:layout_marginRight="20dp"
                                    android:background="@drawable/all_course_list_item_background"
                                    android:paddingLeft="15dp"
                                    android:paddingRight="15dp">

                                    <ImageView
                                        android:id="@+id/imageView_chapter_menu"
                                        android:layout_width="30dp"
                                        android:layout_height="30dp"
                                        android:layout_alignParentLeft="true"
                                        android:layout_centerVertical="true"
                                        android:src="@drawable/ic_list"
                                        app:tint="@color/colorAccentDark" />



                                    <ImageView
                                        android:id="@+id/imageView_arrow"
                                        android:layout_width="15dp"
                                        android:layout_height="15dp"
                                        android:layout_alignParentEnd="true"
                                        android:layout_centerVertical="true"
                                        android:src="@drawable/ic_arrow_down"
                                        app:tint="@color/colorAccentDark" />

                                </RelativeLayout>

                                <RelativeLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_below="@+id/layout_chapter"
                                    android:layout_marginLeft="12dp"
                                    android:layout_marginTop="15dp"
                                    android:layout_marginRight="12dp"
                                    android:layout_marginBottom="20dp"
                                    android:background="@drawable/shadow_full_curved_layout_bg_lite"
                                    android:padding="10dp">

                                    <TextView
                                        android:id="@+id/textview_connect"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_centerHorizontal="true"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginTop="10dp"
                                        android:text="Contains"
                                        android:textColor="@color/colorActionBarTextLight"
                                        android:textSize="16sp"
                                        android:textStyle="bold"
                                        android:typeface="serif" />



                                    <com.facebook.shimmer.ShimmerFrameLayout
                                        android:id="@+id/shimmer_view_container_contains"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_below="@+id/textview_connect"
                                        android:layout_marginStart="8dp"
                                        android:layout_marginTop="10dp"
                                        android:layout_marginBottom="10dp"
                                        android:visibility="visible">

                                        <LinearLayout
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:orientation="horizontal"
                                            android:weightSum="2">

                                            <LinearLayout
                                                android:layout_width="match_parent"
                                                android:layout_height="wrap_content"
                                                android:layout_weight="1"
                                                android:orientation="horizontal">

                                                <View
                                                    android:layout_width="18dp"
                                                    android:layout_height="18dp"
                                                    android:layout_gravity="center"
                                                    android:background="@color/light_grey" />

                                                <View
                                                    android:layout_width="100dp"
                                                    android:layout_height="30dp"
                                                    android:layout_gravity="center"
                                                    android:layout_marginLeft="5dp"
                                                    android:layout_marginRight="5dp"
                                                    android:background="@color/light_grey" />

                                                <View
                                                    android:layout_width="25dp"
                                                    android:layout_height="25dp"
                                                    android:layout_gravity="center"
                                                    android:background="@color/light_grey" />

                                            </LinearLayout>

                                            <LinearLayout
                                                android:layout_width="match_parent"
                                                android:layout_height="wrap_content"
                                                android:layout_weight="1"
                                                android:orientation="horizontal">

                                                <View
                                                    android:layout_width="18dp"
                                                    android:layout_height="18dp"
                                                    android:layout_gravity="center"
                                                    android:background="@color/light_grey" />

                                                <View
                                                    android:layout_width="100dp"
                                                    android:layout_height="30dp"
                                                    android:layout_gravity="center"
                                                    android:layout_marginLeft="5dp"
                                                    android:layout_marginRight="5dp"
                                                    android:background="@color/light_grey" />

                                                <View
                                                    android:layout_width="25dp"
                                                    android:layout_height="25dp"
                                                    android:layout_gravity="center"
                                                    android:background="@color/light_grey" />

                                            </LinearLayout>
                                        </LinearLayout>
                                    </com.facebook.shimmer.ShimmerFrameLayout>
                                </RelativeLayout>
                            </RelativeLayout>

                            <ImageView
                                android:id="@+id/imageView_addMore"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="10dp"
                                android:layout_marginRight="10dp"
                                android:adjustViewBounds="true"
                                android:scaleType="centerCrop"
                                android:visibility="gone" />

                            <TextView
                                android:id="@+id/textView_book_detail"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center|start"
                                android:layout_marginLeft="35dp"
                                android:layout_marginTop="10sp"
                                android:layout_marginRight="35dp"
                                android:layout_marginBottom="20dp"
                                android:fontFamily="@font/poppins_regular"
                                android:justificationMode="inter_word"
                                android:text=""
                                android:textColor="@color/colorActionBarTextLight"
                                android:textSize="14sp" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:visibility="gone"
                                android:orientation="vertical"
                                android:id="@+id/tab_chapter_contents">

                                <com.ws.core_ui.custom_views.WSTextView
                                    android:id="@+id/textView_content"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="3dp"
                                    android:layout_marginTop="10dp"
                                    android:ellipsize="end"
                                    android:gravity="center"
                                    android:maxLines="3"
                                    android:paddingStart="12dp"
                                    android:paddingEnd="12dp"
                                    android:textSize="18sp"
                                    android:textStyle="bold"
                                    app:autoSizeMaxTextSize="20sp"
                                    app:autoSizeMinTextSize="12sp"
                                    app:autoSizeTextType="uniform"
                                    app:enableGradient="true"
                                    app:ws_font_weight="bold"
                                    android:text="Book Contains" />

                                <androidx.recyclerview.widget.RecyclerView
                                    android:id="@+id/recyclerView_contains"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:layout_marginBottom="10dp" />

                            </LinearLayout>



                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:id="@+id/tab_chapter_details"
                                android:orientation="vertical"
                                android:visibility="gone">
                                <com.ws.core_ui.custom_views.WSTextView
                                    android:id="@+id/textView_chapter_count"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="3dp"
                                    android:layout_marginTop="10dp"
                                    android:ellipsize="end"
                                    android:maxLines="3"
                                    android:paddingStart="12dp"
                                    android:paddingEnd="12dp"
                                    android:textSize="18sp"
                                    android:textStyle="bold"
                                    app:autoSizeMaxTextSize="20sp"
                                    app:autoSizeMinTextSize="12sp"
                                    app:autoSizeTextType="uniform"
                                    app:enableGradient="true"
                                    app:ws_font_weight="bold"
                                    tools:text="" />
                                <androidx.recyclerview.widget.RecyclerView
                                    android:id="@+id/recyclerView_chapters"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="32dp"
                                    android:layout_marginTop="5dp"
                                    android:layout_marginRight="32dp" />
                            </LinearLayout>

                            <com.ws.core_ui.custom_views.WSTextView
                                android:id="@+id/bestSellersDetails"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center|start"
                                android:layout_marginStart="12dp"
                                android:layout_marginTop="20dp"
                                android:layout_marginEnd="40dp"
                                android:ellipsize="end"
                                android:gravity="center"
                                android:maxLines="3"
                                android:paddingStart="12dp"
                                android:paddingEnd="12dp"
                                android:text="Best Sellers"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:visibility="gone"
                                app:autoSizeMaxTextSize="20sp"
                                app:autoSizeMinTextSize="12sp"
                                app:autoSizeTextType="uniform"
                                app:enableGradient="true"
                                app:ws_font_weight="bold" />

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/recyclerBestSellers"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="30dp"
                                android:visibility="gone"
                                android:paddingStart="15dp"
                                android:paddingEnd="15dp" />

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llRelatedBooks"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@id/shimmer"
                            android:orientation="vertical">

                            <com.ws.core_ui.custom_views.WSTextView
                                android:id="@+id/relatedEbooksDetails"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center|start"
                                android:layout_marginStart="12dp"
                                android:layout_marginTop="10dp"
                                android:layout_marginEnd="40dp"
                                android:ellipsize="end"
                                android:gravity="center"
                                android:maxLines="3"
                                android:paddingStart="12dp"
                                android:paddingEnd="12dp"
                                android:text="More Books by Publisher"
                                android:textSize="15sp"
                                android:textStyle="bold"
                                app:autoSizeMaxTextSize="20sp"
                                app:autoSizeMinTextSize="12sp"
                                app:autoSizeTextType="uniform"
                                app:enableGradient="true"
                                app:fontFamily="@font/poppins_regular"
                                app:ws_font_weight="bold" />

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/recyclerrelatedEbooks"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="40dp"
                                android:paddingStart="15dp"
                                android:paddingEnd="15dp" />

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/noDatalayout"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:gravity="center"
                            android:padding="16dp"
                            android:background="@drawable/all_course_list_item_background"
                            android:elevation="6dp"
                            android:orientation="vertical"
                            android:visibility="gone">

                            <ImageView
                                android:id="@+id/emptyImage"
                                android:layout_width="60dp"
                                android:layout_height="60dp"
                                android:gravity="center"
                                android:src="@drawable/ic_book" />

                            <com.ws.core_ui.custom_views.WSTextView
                                android:id="@+id/emptytextview"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:padding="3dp"
                                android:text="Books details not available now,\nPlease check after some time."
                                android:textColor="@color/black"
                                android:textSize="12sp"
                                android:typeface="normal" />

                        </LinearLayout>
                    </RelativeLayout>
                </androidx.core.widget.NestedScrollView>
            </RelativeLayout>
        </androidx.coordinatorlayout.widget.CoordinatorLayout>



        <ProgressBar
            android:id="@+id/libraryLoader"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginTop="120dp"
            android:visibility="gone"
            android:indeterminateTint="@color/colorAccent" />
    </RelativeLayout>
</RelativeLayout>