<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginTop="14dp"
    android:layout_marginBottom="14dp"
    android:weightSum="3"
    android:orientation="horizontal">

    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="3dp"
        android:layout_weight="1"
        android:paddingLeft="12dp"
        android:paddingRight="12dp">

        <ImageView
            android:id="@+id/imageView_contains_icon"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            tools:src="@drawable/ic_notes_book"
            app:tint="@color/colorActionBarTextLight" />

        <TextView
            android:id="@+id/textView_contains_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_gravity="center"
            android:layout_marginStart="5dp"
            android:layout_toEndOf="@id/imageView_contains_icon"
            android:autoSizeMaxTextSize="12sp"
            android:autoSizeMinTextSize="10sp"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="Notes"
            android:textColor="@color/colorActionBarTextLight"
            android:textSize="12sp" />
    </RelativeLayout>

        <TextView
            android:id="@+id/textView_contains_separator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="20dp"
            android:layout_weight="0.3"
            android:autoSizeMaxTextSize="12sp"
            android:autoSizeMinTextSize="10sp"
            android:textColor="@color/colorActionBarText"
            android:text=":"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/textView_contains_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:text="(0)"
            android:layout_weight="0.5"
            android:textColor="@color/colorActionBarText"
            android:textSize="14sp"
            android:textStyle="bold" />

</LinearLayout>

