package com.wonderslate.android.module.rssmanager;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;


class RssParser {
    private static final int CONNECT_TIMEOUT_SECS = 5;
    private static final int READ_TIMEOUT_SECS = 15;
    private static final int WRITE_TIMEOUT_SECS = 15;

    private static final OkHttpClient OK_HTTP_CLIENT = new OkHttpClient.Builder()
            .connectTimeout(CONNECT_TIMEOUT_SECS, TimeUnit.SECONDS)
            .readTimeout(READ_TIMEOUT_SECS, TimeUnit.SECONDS)
            .writeTimeout(WRITE_TIMEOUT_SECS, TimeUnit.SECONDS)
            .build();

    private RssParser() {

    }

    static Response parse(String feedUrl) throws IOException {
        Request request = new Request.Builder()
                .url(feedUrl)
                .get()
                .build();

        return OK_HTTP_CLIENT.newCall(request).execute();
    }
}
