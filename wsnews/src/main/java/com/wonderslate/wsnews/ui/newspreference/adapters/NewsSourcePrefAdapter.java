package com.wonderslate.wsnews.ui.newspreference.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.wonderslate.wsnews.R;
import com.wonderslate.wsnews.domain.NewsSource;

import java.util.List;

public class NewsSourcePrefAdapter extends RecyclerView.Adapter<NewsSourcePrefAdapter.ViewHolder> {
    private List<NewsSource> mData;
    private LayoutInflater mInflater;
    private ItemClickListener mClickListener;
    private Context mContext;

    public NewsSourcePrefAdapter(Context context) {
        this.mInflater = LayoutInflater.from(context);
        this.mContext = context;
    }

    // inflates the row layout from xml when needed
    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View v = mInflater.inflate(R.layout.news_source_list_item, parent, false);
        return new ViewHolder(v);
    }

    // binds the data to the TextView in each row
    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        NewsSource mainFeedModel = mData.get(position);
        holder.feedTitle.setText(mainFeedModel.getName());
        holder.feedTitle.setTextColor(ContextCompat.getColor(mContext, R.color.white));
        if (mainFeedModel.isChecked()) {
            holder.selectionTick.setVisibility(View.VISIBLE);
            holder.feedTitle.setTextColor(ContextCompat.getColor(mContext, R.color.primary_bg_red));
        }
        else {
            holder.selectionTick.setVisibility(View.GONE);
            holder.feedTitle.setTextColor(ContextCompat.getColor(mContext, R.color.white));
        }
    }

    // total number of rows
    @Override
    public int getItemCount() {
        return mData.size();
    }


    // stores and recycles views as they are scrolled off screen
    public class ViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {
        TextView feedTitle;
        ImageView selectionTick;

        ViewHolder(View view) {
            super(view);
            feedTitle = view.findViewById(R.id.main_feed_title);
            selectionTick = view.findViewById(R.id.selection_tick);
            itemView.setOnClickListener(this);
        }

        @Override
        public void onClick(View view) {
            if (mClickListener != null) {
                if (selectionTick.getVisibility() == View.VISIBLE) {
                    selectionTick.setVisibility(View.INVISIBLE);
                    feedTitle.setTextColor(ContextCompat.getColor(mContext, R.color.white));

                }
                else {
                    selectionTick.setVisibility(View.VISIBLE);
                    feedTitle.setTextColor(ContextCompat.getColor(mContext, R.color.primary_bg_red));
                }
                mClickListener.onItemClick(view, getAdapterPosition());
            }
        }
    }

    // convenience method for getting data at click position
    /*String getItem(int id) {
        return mData.get(id);
    }*/

    // allows clicks events to be caught
    public void setClickListener(ItemClickListener itemClickListener) {
        this.mClickListener = itemClickListener;
    }

    public void setmData(List<NewsSource> data) {
        mData.clear();
        mData.addAll(data);
        notifyDataSetChanged();
    }

    // parent activity will implement this method to respond to click events
    public interface ItemClickListener {
        void onItemClick(View view, int position);
    }
}
