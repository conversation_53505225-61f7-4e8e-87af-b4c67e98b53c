package com.wonderslate.wsnews.ui.newspreference.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.wonderslate.wsnews.R;
import com.wonderslate.wsnews.domain.NewsLanguage;

import java.util.List;

public class LanguagePrefAdapter extends RecyclerView.Adapter<LanguagePrefAdapter.ViewHolder> {
    private List<NewsLanguage> mData;
    private LayoutInflater mInflater;
    private LangItemClickListener mClickListener;
    NewsLanguage mainFeedModel;
    private Context mContext;

    // data is passed into the constructor
    public LanguagePrefAdapter(Context context) {
        this.mInflater = LayoutInflater.from(context);
        this.mContext = context;
    }

    // inflates the row layout from xml when needed
    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View v = mInflater.inflate(R.layout.lang_pref_list_item, parent, false);
        return new ViewHolder(v);
    }

    // binds the data to the TextView in each row
    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        mainFeedModel = mData.get(position);
        holder.feedLang.setText(mainFeedModel.getDisplayLanguage());
        holder.feedLang.setTextColor(ContextCompat.getColor(mContext, R.color.white));
        if (mainFeedModel.isChecked()) {
            holder.selectionTick.setVisibility(View.VISIBLE);
            holder.feedLang.setTextColor(ContextCompat.getColor(mContext, R.color.primary_bg_red));
        }
        else {
            holder.selectionTick.setVisibility(View.GONE);
            holder.feedLang.setTextColor(ContextCompat.getColor(mContext, R.color.white));
        }
    }

    // total number of rows
    @Override
    public int getItemCount() {
        return mData.size();
    }


    // stores and recycles views as they are scrolled off screen
    public class ViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {
        TextView feedLang;
        ImageView selectionTick;

        ViewHolder(View view) {
            super(view);
            feedLang = view.findViewById(R.id.main_feed_lang);
            selectionTick = view.findViewById(R.id.selection_tick);
            itemView.setOnClickListener(this);
        }

        @Override
        public void onClick(View view) {
            if (mClickListener != null) {
                if (selectionTick.getVisibility() == View.VISIBLE) {
                    selectionTick.setVisibility(View.INVISIBLE);
                    feedLang.setTextColor(ContextCompat.getColor(mContext, R.color.white));
                }
                else {
                    selectionTick.setVisibility(View.VISIBLE);
                    feedLang.setTextColor(ContextCompat.getColor(mContext, R.color.primary_bg_red));
                }
                mClickListener.onLangItemClick(view, getAdapterPosition());
            }
        }
    }

    // convenience method for getting data at click position
    NewsLanguage getItem(int id) {
        return mData.get(id);
    }

    // allows clicks events to be caught
    public void setClickListener(LangItemClickListener itemClickListener) {
        this.mClickListener = itemClickListener;
    }

    // parent activity will implement this method to respond to click events
    public interface LangItemClickListener {
        void onLangItemClick(View view, int position);
    }

    public void setData(List<NewsLanguage> pData) {
        mData.clear();
        mData = pData;
        notifyDataSetChanged();
    }
}
