package com.wonderslate.wsnews.ui.base;

public class BasePresenterImpl<V extends BaseContract.IBaseView, I extends BaseContract.IBaseInteractor> implements BaseContract.IBasePresenter<V, I> {
    private static final String TAG = "IBasePresenter";

    private V mBaseView;
    private I mBaseInteractor;

    public BasePresenterImpl(I baseInteractor) {
        mBaseInteractor = baseInteractor;
    }

    @Override
    public void onAttach(V baseView) {
        mBaseView = baseView;
    }

    @Override
    public void onDetach() {
        mBaseView = null;
        mBaseInteractor = null;
    }

    @Override
    public V getBaseView() {
        return mBaseView;
    }

    @Override
    public I getInteractor() {
        return mBaseInteractor;
    }

    @Override
    public boolean isViewAttached() {
        return mBaseView != null;
    }

    @Override
    public void checkViewAttached() throws BaseViewNotAttachedException {
        if (!isViewAttached()) throw new BaseViewNotAttachedException();
    }

    @Override
    public void handleError(String message) {

    }

    public static class BaseViewNotAttachedException extends RuntimeException {
        public BaseViewNotAttachedException() {
            super("Please call Presenter.onAttach(baseView) before" +
                    " requesting data to the Presenter");
        }
    }
}
