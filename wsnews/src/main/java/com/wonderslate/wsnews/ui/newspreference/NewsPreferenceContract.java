package com.wonderslate.wsnews.ui.newspreference;

import com.wonderslate.wsnews.domain.NewsLanguage;
import com.wonderslate.wsnews.domain.NewsPreferenceOptions;
import com.wonderslate.wsnews.domain.NewsSource;
import com.wonderslate.wsnews.ui.base.BaseContract;

import java.util.List;

public interface NewsPreferenceContract {
    interface NewsPrefsInteractor extends BaseContract.IBaseInteractor {
        List<NewsPreferenceOptions> getUserSelectedNewsPrefs();
        void setUserSelectedNewsPrefs(List<NewsPreferenceOptions> userSelectedNews);

        List<NewsPreferenceOptions> getAllAvailableNewsPrefs();

        List<NewsLanguage> getUserSelectedNewsLanguages();
        void setUserSelectedNewsLanguages(List<NewsLanguage> userSelectedNewsLanguages);

        List<NewsSource> getUserSelectedNewsSource();
        void setUserSelectedNewsSource(List<NewsSource> userSelectedNewsSource);

        List<NewsLanguage> getAvailableNewsLanguages();

        List<NewsSource> getAvailableNewsSource();
    }

    interface NewsPreferenceView extends BaseContract.IBaseView {
        void loadNewsPreferenceFragment();
    }

    interface NewsPreferencePresenter<V extends NewsPreferenceContract.NewsPreferenceView, I extends NewsPreferenceContract.NewsPrefsInteractor>
            extends BaseContract.IBasePresenter<V, I> {

        void loadNewsPreferenceFragment();

        List<NewsPreferenceOptions> getUserSelectedNewsPreference();

        List<NewsPreferenceOptions> getAvailableNewsOptions();


    }
}
