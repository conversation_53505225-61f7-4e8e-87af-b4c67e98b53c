package com.wonderslate.wsnews.ui.base;

import com.wonderslate.wsnews.data.localdata.sharedprefs.PreferencesHelper;

public class BaseInteractorImpl implements BaseContract.IBaseInteractor {

    private final PreferencesHelper preferencesHelper;

    public BaseInteractorImpl(PreferencesHelper preferencesHelper) {

        this.preferencesHelper = preferencesHelper;
    }

    @Override
    public PreferencesHelper getNewsPreferenceHelper() {
        return preferencesHelper;
    }

    @Override
    public void setAuthToken(String authToken) {
        getNewsPreferenceHelper().setAuthToken(authToken);
    }
}
