package com.wonderslate.wsnews.ui.newspreference;

import android.os.Build;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.TextView;

import com.wang.avi.AVLoadingIndicatorView;
import com.wonderslate.wsnews.R;
import com.wonderslate.wsnews.domain.NewsLanguage;
import com.wonderslate.wsnews.domain.NewsSource;
import com.wonderslate.wsnews.ui.base.BaseFragment;
import com.wonderslate.wsnews.ui.newspreference.adapters.LanguagePrefAdapter;
import com.wonderslate.wsnews.ui.newspreference.adapters.NewsSourcePrefAdapter;

import java.util.List;

/**
 * A simple {@link Fragment} subclass.
 * Use the {@link NewsPreferenceFragment#newInstance} factory method to
 * create an instance of this fragment.
 */
public class NewsPreferenceFragment extends BaseFragment implements NewsPreferenceContract.NewsPreferenceView {

    // the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
    private static final String ARG_PARAM1 = "param1";
    private static final String ARG_PARAM2 = "param2";

    private String mParam1;
    private String mParam2;

    View view;
    String navMode =  "";
    private List<NewsLanguage> langPrefsList;
    private List<NewsLanguage> templangPrefsList;
    private List<NewsSource> newsSourceList;
    List<NewsSource> refinedSourceList;
    private static RecyclerView langPrefsRecyclerView;
    private static RecyclerView newsSourceRecyclerView;
    private TextView headerTitle, headerSubTitle;
    //private FloatingActionButton userPrefsFab;
    private boolean isNewsSourcePage;
    private int newsSourceSelectedPosition;
    Button userPrefsFab,buttonBack,buttonClear;
    LanguagePrefAdapter languagePrefAdapter;
    NewsSourcePrefAdapter newsSourcePrefAdapter;
    boolean isLangChecked, isNewsSourceChecked;
    AVLoadingIndicatorView loader;

    NewsPreferenceActivity newsPreferenceActivity;


    public NewsPreferenceFragment() {
        // Required empty public constructor
    }

    /**
     * Use this factory method to create a new instance of
     * this fragment using the provided parameters.
     *
     * @param param1 Parameter 1.
     * @param param2 Parameter 2.
     * @return A new instance of fragment NewsPreferenceFragment.
     */
    public static NewsPreferenceFragment newInstance(String param1, String param2) {
        NewsPreferenceFragment fragment = new NewsPreferenceFragment();
        Bundle args = new Bundle();
        args.putString(ARG_PARAM1, param1);
        args.putString(ARG_PARAM2, param2);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            mParam1 = getArguments().getString(ARG_PARAM1);
            mParam2 = getArguments().getString(ARG_PARAM2);
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        view = inflater.inflate(R.layout.fragment_news_preference, container, false);
        init();
        return view;
    }

    @Override
    public void init() {
        super.init();


        if (getActivity() != null) {
            newsPreferenceActivity = (NewsPreferenceActivity) getActivity();
        }
    }

    /**
     * Called immediately after {@link #onCreateView(LayoutInflater, ViewGroup, Bundle)}
     * has returned, but before any saved state has been restored in to the view.
     * This gives subclasses a chance to initialize themselves once
     * they know their view hierarchy has been completely created.  The fragment's
     * view hierarchy is not however attached to its parent at this point.
     *
     * @param view               The View returned by {@link #onCreateView(LayoutInflater, ViewGroup, Bundle)}.
     * @param savedInstanceState If non-null, this fragment is being re-constructed
     */
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

    }


    @Override
    public void loadNewsPreferenceFragment() {}
}