package com.wonderslate.wsnews.ui.newspreference;

import com.wonderslate.wsnews.domain.NewsPreferenceOptions;
import com.wonderslate.wsnews.ui.base.BasePresenterImpl;

import java.util.List;

public class NewsPreferencePresenterImpl<V extends NewsPreferenceContract.NewsPreferenceView, I extends NewsPreferenceContract.NewsPrefsInteractor> extends BasePresenterImpl<V, I>
        implements NewsPreferenceContract.NewsPreferencePresenter<V, I>{
    public NewsPreferencePresenterImpl(I baseInteractor) {
        super(baseInteractor);
    }

    @Override
    public boolean isViewAttached() {
        return super.isViewAttached();
    }

    @Override
    public void checkViewAttached() throws BaseViewNotAttachedException {
        super.checkViewAttached();
    }

    @Override
    public void handleError(String message) {
        super.handleError(message);
    }

    @Override
    public void loadNewsPreferenceFragment() {

    }

    @Override
    public List<NewsPreferenceOptions> getUserSelectedNewsPreference() {
        return null;
    }

    @Override
    public List<NewsPreferenceOptions> getAvailableNewsOptions() {
        return null;
    }
}
