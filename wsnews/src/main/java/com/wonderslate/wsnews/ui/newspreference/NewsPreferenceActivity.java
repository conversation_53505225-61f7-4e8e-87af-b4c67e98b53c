package com.wonderslate.wsnews.ui.newspreference;

import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;

import androidx.core.content.ContextCompat;

import com.wonderslate.wsnews.R;
import com.wonderslate.wsnews.ui.base.BaseActivity;
import com.wonderslate.wsnews.ui.base.BaseFragment;

public class NewsPreferenceActivity extends BaseActivity implements NewsPreferenceContract.NewsPreferenceView, BaseFragment.Callback {
    private static final String TAG = "NewsPrefsActivity";

    private FrameLayout newsPreferenceFrame;
    String navMode =  "";

    private NewsPreferenceContract.NewsPreferencePresenter<NewsPreferenceContract.NewsPreferenceView, NewsPreferenceContract.NewsPrefsInteractor> newsPreferencePresenter;
    private NewsPreferenceContract.NewsPrefsInteractor newsPrefsInteractor;

    @Override
    protected int getLayoutResource() {
        return R.layout.activity_news_preference;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    protected void onViewReady(Bundle savedInstanceState) {
        newsPreferencePresenter.onAttach(this);
        init();
    }

    @Override
    public void init() {
        super.init();
        setScreenTagForLog(TAG);
        newsPreferenceFrame = findViewById(R.id.news_preference_frame);
        if (Build.VERSION.SDK_INT >= 23) {
            Window window = this.getWindow();
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setNavigationBarColor(ContextCompat.getColor(this, R.color.primary_bg_dark));
            getWindow().setStatusBarColor(ContextCompat.getColor(this, R.color.primary_bg_dark));
            View decorView = getWindow().getDecorView();
            decorView.setSystemUiVisibility(decorView.getSystemUiVisibility() & ~View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING);
        }
        if( getIntent().hasExtra("Mode"))
            navMode = getIntent().getStringExtra("Mode");

        loadNewsPreferenceFragment();
    }

    @Override
    public boolean isNetworkConnected() {
        return super.isNetworkConnected();
    }


    @Override
    public void loadNewsPreferenceFragment() {
        NewsPreferenceFragment newsPreferenceFragment = NewsPreferenceFragment.newInstance("", "");
        getSupportFragmentManager().beginTransaction().replace(R.id.news_preference_frame, newsPreferenceFragment).commit();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        newsPreferencePresenter.onDetach();
    }
}