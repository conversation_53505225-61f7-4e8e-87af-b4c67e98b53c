package com.wonderslate.wsnews.ui.base;

import android.content.DialogInterface;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.wonderslate.wsnews.data.localdata.sharedprefs.PreferencesHelper;

public interface BaseContract {
    interface IBaseView {
        void init();

        void showSnackBar(@NonNull View view, @NonNull String message, @NonNull Boolean actionBtn, @NonNull String actionBtnText,
                          @Nullable View.OnClickListener onClickListener, @NonNull Integer duration);

        void showLoading();

        void hideLoading();

        void onError(int errorID, String message);

        void showMessage(String message);

        boolean isNetworkConnected();

        void showAlertDialog(@NonNull String title, @NonNull String message, @NonNull Integer alertIcon, @NonNull Boolean positiveBtn,
                             @NonNull Boolean negativeBtn, @NonNull String positiveBtnText, @NonNull String negativeBtnText,
                             @Nullable DialogInterface.OnClickListener onPositiveBtnClickListener,
                             @Nullable DialogInterface.OnClickListener onNegativeBtnClickListener);

        void setScreenTagForLog(@NonNull String tag);

        String getScreenTagForLog();
    }

    interface IBasePresenter<V extends IBaseView, I extends IBaseInteractor> {
        void onAttach(V BaseView);

        void onDetach();

        V getBaseView();

        I getInteractor();

        boolean isViewAttached();

        void checkViewAttached() throws BasePresenterImpl.BaseViewNotAttachedException;

        void handleError(String message);
    }

    interface IBaseInteractor {
        PreferencesHelper getNewsPreferenceHelper();
        void setAuthToken(String authToken);
    }
}
