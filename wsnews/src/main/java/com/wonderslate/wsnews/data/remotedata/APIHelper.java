package com.wonderslate.wsnews.data.remotedata;

import com.wonderslate.wsnews.data.remotedata.callbacks.NewsFeedFromSourceCallback;
import com.wonderslate.wsnews.data.remotedata.callbacks.NewsPreferenceOptionsCallback;
import com.wonderslate.wsnews.data.remotedata.callbacks.UpdateUserNewsLanguageCallback;
import com.wonderslate.wsnews.data.remotedata.callbacks.UpdateUserNewsSourceCallback;

public interface APIHelper {
    void getAllNewsPreferenceOptions(NewsPreferenceOptionsCallback newsPreferenceOptionsCallback);
    void updateUserNewslanguage(UpdateUserNewsLanguageCallback updateUserNewsLanguageCallback);
    void updateuserNewsSource(UpdateUserNewsSourceCallback updateUserNewsSourceCallback);
    void getNewsFeedItemsFromSource(String sourceUrl, NewsFeedFromSourceCallback newsFeedFromSourceCallback);
}
