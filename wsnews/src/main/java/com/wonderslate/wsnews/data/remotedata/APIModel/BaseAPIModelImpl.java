package com.wonderslate.wsnews.data.remotedata.APIModel;

import org.json.JSONObject;

import java.util.HashMap;

public abstract class BaseAPIModelImpl{
   private String httpMethods ="", siteId = "", token ="", baseUrl = "";
   JSONObject bodyParams = new JSONObject();
   HashMap<String, String> urlParams = new HashMap<>();

   
   public String getHttpMethods() {
      return httpMethods;
   }

   
   public void setHttpMethods(String httpMethods) {
      this.httpMethods = httpMethods;
   }

   
   public String getSiteId() {
      return siteId;
   }

   
   public void setSiteId(String siteId) {
      this.siteId = siteId;
   }

   
   public String getToken() {
      return token;
   }

   
   public void setToken(String token) {
      this.token = token;
   }

   
   public String getBaseUrl() {
      return baseUrl;
   }

   
   public void setBaseUrl(String baseUrl) {
      this.baseUrl = baseUrl;
   }

   public JSONObject getBodyParams() {
      return bodyParams;
   }

   public void setBodyParams(JSONObject bodyParams) {
      this.bodyParams = bodyParams;
   }

   public HashMap<String, String> getUrlParams() {
      return urlParams;
   }

   public void setUrlParams(HashMap<String, String> urlParams) {
      this.urlParams = urlParams;
   }
}
