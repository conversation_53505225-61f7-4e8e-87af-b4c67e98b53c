package com.wonderslate.wsnews.data.remotedata;

public final class ServerUrlManager {
    private static ServerUrlManager mInstance;

    public static String SERVICE;

    public static Servers currentServer;

    public enum Servers {
        QA, QADEV, STAGING, PUBLISH, LIVE, PJLIVE
    }

    private ServerUrlManager() {

    }

    public static synchronized void init() {
        if (mInstance == null) {
            mInstance = new ServerUrlManager();
        }
    }

    public void setService() {
        //Select Base Service URL According To Server.
        currentServer = Servers.PJLIVE; //Use QA, STAGING, PUBLISH, LIVE

        switch (currentServer){
            case QA:
                SERVICE = ServerURLs.SERVICE_QA;
                break;
            case QADEV:
                SERVICE = ServerURLs.SERVICE_QADEV;
                break;
            case STAGING:
                SERVICE = ServerURLs.SERVICE_STAGING;
                break;
            case PUBLISH:
                SERVICE = ServerURLs.SERVICE_PUBLISH;
                break;
            case LIVE:
                SERVICE = ServerURLs.SERVICE_LIVE;
                break;
            case PJLIVE:
                SERVICE = ServerURLs.SERVICE_PJ_LIVE;
                break;
            default:
                break;
        }
    }

    public static ServerUrlManager getInstance() {
        return mInstance;
    }

    class ServerURLs {
        static final String SERVICE_QA = "https://qa.wonderslate.com/";
        static final String SERVICE_QADEV = "https://dev.wonderslate.com/";
        static final String SERVICE_STAGING = "https://staging.wonderslate.com/";
        static final String SERVICE_PUBLISH = "https://publish.wonderslate.com/";
        static final String SERVICE_LIVE = "https://www.wonderslate.com/";
        static final String SERVICE_PJ_LIVE = "https://www.prejoy.com/";
    }
}
