package com.wonderslate.wsnews.data.remotedata.APIModel;

public class UpdateUserNewsPreference extends BaseAPIModelImpl{
    String userPreferredNewsLanguages = "", userPreferredNewsSources ="", apiUrl="";

    public UpdateUserNewsPreference() {}

    public String getUserPreferredNewsLanguages() {
        return userPreferredNewsLanguages;
    }

    public void setUserPreferredNewsLanguages(String userPreferredNewsLanguages) {
        this.userPreferredNewsLanguages = userPreferredNewsLanguages;
    }

    public String getUserPreferredNewsSources() {
        return userPreferredNewsSources;
    }

    public void setUserPreferredNewsSources(String userPreferredNewsSources) {
        this.userPreferredNewsSources = userPreferredNewsSources;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getCompleteUrl() {
        return getBaseUrl() + getApiUrl();
    }
}
