package com.wonderslate.wsnews.data.remotedata.APIModel;

import org.json.JSONObject;

import java.util.HashMap;

public class GetNewsPrefs extends BaseAPIModelImpl {
   String apiUrl="";

   public GetNewsPrefs() {}

   public String getApiUrl() {
      return apiUrl;
   }

   public void setApiUrl(String apiUrl) {
      this.apiUrl = apiUrl;
   }

   public String getCompleteUrl() {
      return getBaseUrl() + getApiUrl();
   }
}
