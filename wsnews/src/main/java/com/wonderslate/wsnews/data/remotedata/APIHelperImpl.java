package com.wonderslate.wsnews.data.remotedata;

import com.wonderslate.wsnews.data.remotedata.callbacks.NewsFeedFromSourceCallback;
import com.wonderslate.wsnews.data.remotedata.callbacks.NewsPreferenceOptionsCallback;
import com.wonderslate.wsnews.data.remotedata.callbacks.UpdateUserNewsLanguageCallback;
import com.wonderslate.wsnews.data.remotedata.callbacks.UpdateUserNewsSourceCallback;

public class APIHelperImpl implements APIHelper {
    @Override
    public void getAllNewsPreferenceOptions(NewsPreferenceOptionsCallback newsPreferenceOptionsCallback) {

    }

    @Override
    public void updateUserNewslanguage(UpdateUserNewsLanguageCallback updateUserNewsLanguageCallback) {

    }

    @Override
    public void updateuserNewsSource(UpdateUserNewsSourceCallback updateUserNewsSourceCallback) {

    }

    @Override
    public void getNewsFeedItemsFromSource(String sourceUrl, NewsFeedFromSourceCallback newsFeedFromSourceCallback) {

    }
}
