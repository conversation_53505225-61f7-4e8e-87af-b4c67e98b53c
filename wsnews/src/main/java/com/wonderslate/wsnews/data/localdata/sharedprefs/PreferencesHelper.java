package com.wonderslate.wsnews.data.localdata.sharedprefs;

public interface PreferencesHelper {
    String getUserName();
    String getUserMobile();
    String getAuthToken();
    void setUserName(String userName);
    void setUserMobile(String userMobile);
    void setAuthToken(String authToken);
    void setSiteId(String siteId);
    String getSiteId();
    void setNewsPreferredLanguage(String preferredLanguage);
    String getNewsPreferredLanguage();
    void setNewsPreferredSource(String preferredSource);
    String getNewsPreferredSource();
    void setAllNewsFeedItems(String allNewsFeedItems);
    String getAllNewsFeedItems();
}
