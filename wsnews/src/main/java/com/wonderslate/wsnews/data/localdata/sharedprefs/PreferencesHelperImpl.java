package com.wonderslate.wsnews.data.localdata.sharedprefs;

import android.content.Context;
import android.content.SharedPreferences;

public class PreferencesHelperImpl implements PreferencesHelper{
    private static PreferencesHelperImpl mInstance;
    private final SharedPreferences mPrefs;

    //Shared Preference Constants
    private static final String PREF_KEY_NAME = "PREF_KEY_PJ_NEWS";
    private static final String PREF_KEY_USER_NAME = "PREF_KEY_USER_NAME";
    private static final String PREF_KEY_USER_MOBILE = "PREF_KEY_USER_MOBILE";
    private static final String PREF_KEY_AUTH_TOKEN = "PREF_KEY_AUTH_TOKEN";
    private static final String PREF_KEY_SITE_ID = "PREF_KEY_SITE_ID";
    private static final String PREF_KEY_NEWS_PREF_LANG = "PREF_KEY_NEWS_PREF_LANG";
    private static final String PREF_KEY_NEWS_PREF_SOURCE = "PREF_KEY_NEWS_PREF_SOURCE";
    private static final String PREF_KEY_NEWS_FEED_ITEMS = "PREF_KEY_NEWS_FEED_ITEMS";

    private PreferencesHelperImpl(Context context, String prefsFileName) {
        mPrefs = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE);
    }

    public static PreferencesHelperImpl getInstance(Context applicationContext, String preferenceFileName) {
        if (mInstance == null) {
            synchronized(PreferencesHelperImpl.class) {
                mInstance = new PreferencesHelperImpl(applicationContext, preferenceFileName);
            }
        }
        return mInstance;
    }

    @Override
    public String getUserName() {
        return mPrefs.getString(PREF_KEY_USER_NAME, "");
    }

    @Override
    public String getUserMobile() {
        return mPrefs.getString(PREF_KEY_USER_MOBILE, "");
    }

    @Override
    public String getAuthToken() {
        return mPrefs.getString(PREF_KEY_AUTH_TOKEN, "");
    }

    @Override
    public void setUserName(String userName) {
        mPrefs.edit().putString(PREF_KEY_USER_NAME, userName).commit();
    }

    @Override
    public void setUserMobile(String userMobile) {
        mPrefs.edit().putString(PREF_KEY_USER_MOBILE, userMobile).commit();
    }

    @Override
    public void setAuthToken(String authToken) {
        mPrefs.edit().putString(PREF_KEY_AUTH_TOKEN, authToken).commit();
    }

    @Override
    public void setSiteId(String siteId) {
        mPrefs.edit().putString(PREF_KEY_SITE_ID, siteId).commit();
    }

    @Override
    public String getSiteId() {
        return mPrefs.getString(PREF_KEY_SITE_ID, "");
    }

    @Override
    public void setNewsPreferredLanguage(String preferredLanguage) {
        mPrefs.edit().putString(PREF_KEY_NEWS_PREF_LANG, preferredLanguage).commit();
    }

    @Override
    public String getNewsPreferredLanguage() {
        return mPrefs.getString(PREF_KEY_NEWS_PREF_LANG, "");
    }

    @Override
    public void setNewsPreferredSource(String preferredSource) {
        mPrefs.edit().putString(PREF_KEY_NEWS_PREF_SOURCE, preferredSource).commit();
    }

    @Override
    public String getNewsPreferredSource() {
        return mPrefs.getString(PREF_KEY_NEWS_PREF_SOURCE, "");
    }

    @Override
    public void setAllNewsFeedItems(String allNewsFeedItems) {
        mPrefs.edit().putString(PREF_KEY_NEWS_FEED_ITEMS, allNewsFeedItems).commit();
    }

    @Override
    public String getAllNewsFeedItems() {
        return mPrefs.getString(PREF_KEY_NEWS_FEED_ITEMS, "");
    }
}
