package com.wonderslate.wsnews.utils;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

public class NetworkUtils {
    private NetworkUtils() {}

    public static NetworkUtils networkUtils;

    public static synchronized NetworkUtils getInstance() {
        if (networkUtils == null) {
            networkUtils = new NetworkUtils();
        }
        return networkUtils;
    }

    public boolean isNetworkAvailable(@NonNull Context context) {
        ConnectivityManager connectivityManager
                = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
        return activeNetworkInfo != null && activeNetworkInfo.isConnected();
    }

    private void netViewController(@NonNull Boolean isConnected, @NonNull View view) {
        if (isConnected) {
            view.postDelayed(new Runnable() {
                @Override
                public void run() {
                    view.animate()
                            .setDuration(1000)
                            .alpha(0.0f)
                            .setListener(new AnimatorListenerAdapter() {
                                @Override
                                public void onAnimationEnd(Animator animation) {
                                    super.onAnimationEnd(animation);
                                    view.setVisibility(View.GONE);
                                }
                            });
                }
            }, 1500);
        } else {
            view.animate()
                    .setDuration(0)
                    .alpha(1.0f)
                    .setListener(null);
        }
    }
}
