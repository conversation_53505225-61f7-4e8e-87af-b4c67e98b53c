<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="10dp">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardBackgroundColor="@color/purple_700"
        app:cardCornerRadius="6dp"
        app:cardElevation="4dp"
        app:cardUseCompatPadding="true"
        app:cardPreventCornerOverlap="true"
        app:contentPadding="5dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/selection_tick"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:srcCompat="@drawable/ic_selection_done"
                android:layout_alignParentTop="true"
                android:layout_alignParentEnd="true"
                android:visibility="gone"/>

            <TextView
                android:id="@+id/main_feed_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_toStartOf="@id/selection_tick"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:padding="15dp"
                android:textStyle="bold"
                android:gravity="center" />

        </RelativeLayout>

    </androidx.cardview.widget.CardView>

</LinearLayout>