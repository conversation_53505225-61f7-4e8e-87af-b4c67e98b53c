<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.newspreference.NewsPreferenceFragment"
    android:background="@color/primary_bg_dark">

    <LinearLayout
        android:id="@+id/header_container_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_marginBottom="5dp">
        <TextView
            android:id="@+id/header_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:layout_gravity="center"
            android:gravity="center"
            android:padding="10dp"
            android:text="Choose your language"
            android:textStyle="bold"/>

        <TextView
            android:id="@+id/header_sub_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="13sp"
            android:padding="10dp"
            android:visibility="gone"
            android:textStyle="italic"/>
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/rrltBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_below="@+id/header_container_layout"
        android:visibility="visible">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:visibility="visible"
            app:srcCompat="@drawable/ic_back" />

        <Button
            android:id="@+id/button_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="3dp"
            android:layout_toEndOf="@+id/imgBack"
            android:background="@color/primary_bg_dark"
            android:gravity="center|left"
            android:text="Back"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="17dp"
            android:textStyle="bold" />

    </RelativeLayout>


    <Button
        android:id="@+id/button_clear"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/header_container_layout"
        android:layout_alignParentRight="true"
        android:layout_marginRight="10dp"
        android:background="@color/primary_bg_dark"
        android:gravity="center|right"
        android:text="Clear"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="17dp"
        android:textStyle="bold"
        android:visibility="visible" />



    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/langPrefRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/rrltBack"
        android:layout_above="@+id/user_pref_next_fab"
        android:visibility="gone"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/newsSourcePrefRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/rrltBack"
        android:layout_above="@+id/user_pref_next_fab"
        android:visibility="gone"/>

    <Button
        android:id="@+id/user_pref_next_fab"
        android:layout_width="150dp"
        android:layout_height="40dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="10dp"
        android:background="@drawable/button_shape_disabled"
        android:gravity="center"
        android:text="Next"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="16sp" />

    <ProgressBar
        android:id="@+id/loaderMain"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="gone"
        android:indeterminateTint="@color/primary_bg_red" />

</RelativeLayout>