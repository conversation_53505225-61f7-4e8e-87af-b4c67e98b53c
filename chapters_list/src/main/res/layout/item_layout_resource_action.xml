<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.cardview.widget.CardView
        android:id="@+id/btnAction"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_4sdp"
        android:backgroundTint="@color/colorActionSheetActionCardBg"
        app:cardCornerRadius="8dp"
        android:foreground="?selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:elevation="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_resource_icon"
            android:gravity="center_vertical">

            <ImageView
                android:id="@+id/ivRes"
                android:layout_width="@dimen/_32sdp"
                android:layout_height="@dimen/_32sdp"
                android:layout_margin="@dimen/_8sdp"
                android:src="@drawable/ic_notes_res"
                android:padding="@dimen/_4sdp"
                app:tint="@color/colorActionSheetActionIconColor" />

            <TextView
                android:id="@+id/tvAction"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:text="Action name"
                android:padding="10dp"
                android:textSize="16sp"
                android:textColor="@color/colorActionSheetActionText"/>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</FrameLayout>