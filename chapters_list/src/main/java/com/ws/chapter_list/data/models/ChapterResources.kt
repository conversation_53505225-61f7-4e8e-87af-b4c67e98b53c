package com.ws.chapter_list.data.models

import com.ws.database.room.entity.Resource
import java.io.Serializable

data class ChapterResources(
    val chapterName: String,
    val chapterId: String,
    val bookId: Int,
    val isPreview: Boolean,
    val resourceCounts: List<ResourceCounts>,
    val resources: List<Resource>
): Serializable


data class ResourceCounts(
    val resourceType: String,
    val count: Int
): Serializable