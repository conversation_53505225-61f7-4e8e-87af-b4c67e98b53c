package com.ws.chapter_list.data.network

import com.ws.chapter_list.data.models.ChaptersAndResourcesRequest
import com.ws.chapter_list.data.models.LogBookActivityRequest
import com.ws.chapter_list.data.models.TestSeriesPurchaseStatusRequest
import com.ws.chapter_list.data.models.UserAddedResourceRequest
import com.ws.commons.interfaces.TokenProvider
import com.ws.commons.models.NetworkConfig
import com.ws.networking.NetworkRequest
import com.ws.networking.utils.ApiParams

class ChaptersListRequests (
    private val networkConfig: NetworkConfig,
    private val tokenProvider: TokenProvider
) {

    fun getChaptersListRequest(request: ChaptersAndResourcesRequest) = NetworkRequest (
        baseUrl = networkConfig.service6,
        apiEndPoint = ChaptersListEndpoints.allChaptersListMetaInfoEndPoint,
        token = tokenProvider.getToken(),
        siteId = networkConfig.siteId,
        params = hashMapOf(
            ApiParams.PARAM_BOOK_ID to request.bookId.toString()
        )
    )

    fun logBookActivityRequest(request: LogBookActivityRequest) = NetworkRequest (
        baseUrl = networkConfig.service6,
        apiEndPoint = ChaptersListEndpoints.logBookActivityEndPoint,
        token = tokenProvider.getToken(),
        siteId = networkConfig.siteId,
        params = hashMapOf(
            ApiParams.PARAM_BOOK_ID to request.bookId.toString(),
            ApiParams.PARAM_BOOK_LOGGING_SOURCE to request.viewSource,
            ApiParams.PARAM_BOOK_LOGGING_TYPE to request.viewType
        )
    )

    fun testSeriesPurchaseStatusRequest(request: TestSeriesPurchaseStatusRequest) = NetworkRequest (
        baseUrl = networkConfig.service6,
        apiEndPoint = ChaptersListEndpoints.testSeriesPurchaseStatusEndPoint,
        token = tokenProvider.getToken(),
        siteId = networkConfig.siteId,
        params = hashMapOf(
            ApiParams.PARAM_BOOK_ID to request.bookId.toString()
        )
    )

    fun userAddedResourceRequest(request: UserAddedResourceRequest) = NetworkRequest (
            baseUrl = networkConfig.service6,
            apiEndPoint = ChaptersListEndpoints.userAddedResourceEndPoint,
            token = tokenProvider.getToken(),
            siteId = networkConfig.siteId,
            params = hashMapOf(
                    ApiParams.PARAM_BOOK_ID to request.bookId.toString()
            )
    )

}