package com.ws.chapter_list.ui.adapters

import android.util.Log
import android.view.View
import android.view.ViewGroup
import com.h6ah4i.android.widget.advrecyclerview.utils.AbstractExpandableItemAdapter
import com.h6ah4i.android.widget.advrecyclerview.utils.AbstractExpandableItemViewHolder
import com.wonderslate.data.network.Wonderslate
import com.ws.chapter_list.data.models.ChapterResources
import com.ws.chapter_list.databinding.ItemLayoutChapterBinding
import com.ws.chapter_list.databinding.ItemLayoutResourceBinding
import com.ws.chapter_list.ui.interfaces.OnResourceClickListener
import com.ws.commons.extensions.clearAndAddAll
import com.ws.commons.interfaces.UpdateNotificationManager
import com.ws.core_ui.extensions.getResTypeIcon
import com.ws.core_ui.extensions.newLayoutInflater
import com.ws.core_ui.extensions.visibility

class ChaptersListAdapter(
    private val chapters: ArrayList<ChapterResources>,
    private val notificationManager: UpdateNotificationManager
): AbstractExpandableItemAdapter<ChaptersListAdapter.ChapterViewHolder, ChaptersListAdapter.ResourceViewHolder>() {

    private var isPreviewMode = true
    private var isTestSeries = true
    private var hasBoughtTestSeries: String = ""

    private var onResourceClickListener: OnResourceClickListener? = null

    fun update(chapters: List<ChapterResources>) {
        this.chapters.clearAndAddAll(chapters)
        // TODO: Use DiffUtil for better performance instead of notifyDataSetChanged()
        notifyDataSetChanged()
    }

    fun setPreviewMode(isPreviewMode: Boolean) {
        this.isPreviewMode = isPreviewMode
    }

    fun isTestSeries(isTestSeries: Boolean) {
        this.isTestSeries = isTestSeries
    }

    fun hasBoughtTestSeries(hasBoughtTestSeries: String) {
        this.hasBoughtTestSeries = hasBoughtTestSeries
        Log.e("Chap List Adapter", "has bought test: " + this.hasBoughtTestSeries)
        //notifyDataSetChanged()
    }

    fun setOnResourceClickListener(onResourceClicked: OnResourceClickListener) {
        this.onResourceClickListener = onResourceClicked
    }

    override fun getGroupCount(): Int {
        return chapters.size
    }

    override fun getChildCount(groupPosition: Int): Int {
        return chapters[groupPosition].resources.size
    }

    override fun getGroupId(groupPosition: Int): Long {
        return chapters[groupPosition].chapterId.toLong()
    }

    override fun getChildId(groupPosition: Int, childPosition: Int): Long {
        return chapters[groupPosition].resources[childPosition].id.toLong()
    }

    override fun onCreateGroupViewHolder(parent: ViewGroup, viewType: Int): ChapterViewHolder {
        return ChapterViewHolder(ItemLayoutChapterBinding.inflate(parent.context.newLayoutInflater(), parent, false))
    }

    override fun onCreateChildViewHolder(parent: ViewGroup, viewType: Int): ResourceViewHolder {
        return ResourceViewHolder(ItemLayoutResourceBinding.inflate(parent.context.newLayoutInflater(), parent, false))
    }

    override fun onBindGroupViewHolder(
        holder: ChapterViewHolder,
        groupPosition: Int,
        viewType: Int
    ) {

        val chapter = chapters[groupPosition]
        val number = "${groupPosition + 1} ."
        holder.binding.tvNumber.text = number

        holder.binding.tvChapterName.text = chapter.chapterName

        holder.binding.ivLock.visibility(isPreviewMode && !chapter.isPreview)

        holder.binding.vNotification.visibility(notificationManager.isChapterUpdated(chapter.chapterId))

        // TODO: cache this in the model itself and avoid processing power
        holder.binding.tvResources.text = if(chapter.resourceCounts.isNotEmpty()) {
            var resourcesText = "${chapter.resourceCounts[0].count} ${chapter.resourceCounts[0].resourceType}"
            if(chapter.resourceCounts.size > 1) {
                for (index in 1..chapter.resourceCounts.lastIndex) {
                    resourcesText += "  |  ${chapter.resourceCounts[index].count} ${chapter.resourceCounts[index].resourceType}"
                }
            }

            resourcesText
        } else {
            ""
        }

        holder.binding.indicator.rotation = if(holder.expandState.isExpanded) 90F else -90F
    }

    override fun onBindChildViewHolder(
        holder: ResourceViewHolder,
        groupPosition: Int,
        childPosition: Int,
        viewType: Int
    ) {
        val resource = chapters[groupPosition].resources[childPosition]
        val isPreview = chapters[groupPosition].isPreview
        holder.binding.tvResource.text = resource.resName
        holder.binding.ivRes.setImageResource(resource.resType.getResTypeIcon())
        holder.binding.ivLock.visibility(isPreviewMode && !isPreview)
        if (resource.resType.equals("Multiple Choice Questions", true) && isTestSeries) {
            Log.e("Chap List Adapter", "has bought test: " + this.hasBoughtTestSeries)
            if (!isPreview && !Wonderslate.getInstance().sharedPrefs.testSeriesPurchaseStatus) {
                holder.binding.ivLock.visibility = View.VISIBLE
            }
            else {
                holder.binding.ivLock.visibility = View.GONE
            }
            //holder.binding.ivLock.visibility(!isPreview && !hasBoughtTestSeries.equals("true", true))
        }
        holder.binding.vNotification.visibility(notificationManager.isResourceUpdated(resource.id))
        holder.binding.resCard.setOnClickListener {
            onResourceClickListener?.onResourceClicked(isPreview, resource)
        }
    }

    override fun onCheckCanExpandOrCollapseGroup(
        holder: ChapterViewHolder,
        groupPosition: Int,
        x: Int,
        y: Int,
        expand: Boolean
    ): Boolean {
        return true
    }

    //View holders
    inner class ChapterViewHolder(val binding: ItemLayoutChapterBinding): AbstractExpandableItemViewHolder(binding.root)
    inner class ResourceViewHolder(val binding: ItemLayoutResourceBinding): AbstractExpandableItemViewHolder(binding.root)

}