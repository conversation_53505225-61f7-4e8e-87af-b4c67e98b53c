package com.ws.chapter_list.ui.action_sheet

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import androidx.core.os.bundleOf
import androidx.fragment.app.FragmentManager
import com.wonderslate.data.network.Wonderslate
import com.ws.chapter_list.data.models.ChaptersListConfig
import com.ws.chapter_list.databinding.BsUpgradeActionSheetBinding
import com.ws.core_ui.base.MaterialBottomSheet

class UpgradeActionSheet: MaterialBottomSheet<BsUpgradeActionSheetBinding>() {
    private var upgradePrice: String = ""
    private var bookId: String? = null
    private var listener: OnUpgradeInteractionListener? = null
    var chapterListConfig: ChaptersListConfig? = null

    override fun inflateBinding(layoutInflater: LayoutInflater): BsUpgradeActionSheetBinding {
        return BsUpgradeActionSheetBinding.inflate(layoutInflater)
    }

    override fun initArguments(arguments: Bundle?) {
        bookId = arguments?.getString(ARG_BOOK_ID)
        chapterListConfig = arguments?.getSerializable(ARG_CHAPTER_LIST_CONFIG) as ChaptersListConfig?
    }

    override fun onViewReady(view: View, savedInstanceState: Bundle?) {
        upgradePrice = Wonderslate.getInstance().sharedPrefs.bookUpgradePrice
        binding?.upgradeBtn?.text = buildString {
            append("Upgrade ")
            append("@ ₹")
            append(getParsedPrice(upgradePrice))
        }

        binding?.upgradeBtn?.setOnClickListener {
            chapterListConfig?.let { it1 -> listener?.onUpgradeAction(upgradePrice, it1) }
            //dismiss()
        }

    }

    private fun getParsedPrice(price: String): String {
        var price = price
        if (price.contains(".0") || price.contains(".00")) {
            price = price.substring(0, price.indexOf("."))
        }
        return price
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        if(context is OnUpgradeInteractionListener)
            listener = context
    }

    interface OnUpgradeInteractionListener {
        fun onUpgradeAction(upgradePrice: String, chapterListConfig: ChaptersListConfig)
    }

    companion object {

        private const val TAG = "UpgradeActionSheet"

        private const val ARG_BOOK_ID = "arg_book_id"
        private const val ARG_CHAPTER_LIST_CONFIG = "arg_chapter_list_config"

        @JvmStatic
        fun showUpgradeDialog(fragmentManager: FragmentManager,
                              tag: String = TAG,
                              chapterListConfig: ChaptersListConfig) {
            val sheet = UpgradeActionSheet()
            sheet.arguments = bundleOf(
                ARG_BOOK_ID to chapterListConfig.bookId,
                ARG_CHAPTER_LIST_CONFIG to chapterListConfig
            )
            sheet.show(fragmentManager, tag)

        }
    }
}