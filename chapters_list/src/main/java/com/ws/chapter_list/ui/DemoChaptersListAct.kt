package com.ws.chapter_list.ui

import android.content.Context
import android.content.Intent
import com.ws.chapter_list.R
import com.ws.chapter_list.data.models.ActionType
import com.ws.chapter_list.data.models.ChapterResources
import com.ws.chapter_list.data.models.ChaptersListConfig
import com.ws.chapter_list.databinding.ActivityDemoChaptersListBinding
import com.ws.chapter_list.ui.action_sheet.ResourceActionSheet
import com.ws.core_ui.base.ActBase
import com.ws.core_ui.extensions.replaceFragment
import com.ws.database.room.entity.Resource
import kotlinx.coroutines.ExperimentalCoroutinesApi

/**
 * This is a demo implementation of [ChaptersListFrag],
 * Use this class for for ONLY reference .
 */
@ExperimentalCoroutinesApi

class DemoChaptersListAct : ActBase<ActivityDemoChaptersListBinding>(),
    ChaptersListFrag.OnChaptersListFragInteraction,
    ResourceActionSheet.OnResourceActionInteractionListener{

    override fun bindView(): ActivityDemoChaptersListBinding {
        return ActivityDemoChaptersListBinding.inflate(layoutInflater)
    }

    override fun init() {
        supportActionBar?.hide()
        val chapterConfig = intent.getSerializableExtra(CHAPTER_LIST_CONFIG) as ChaptersListConfig
        replaceFragment(
            R.id.fragmentContainer,
            ChaptersListFrag.newInstance(chapterConfig)
        )
    }

    override fun onBackButtonClicked() {
        onBackPressed()
    }

    override fun onReadClicked(resource: Resource) {
        // Do something when read is clicked
    }

    override fun onWebLinkClicked(resource: Resource) {
        // Do something when weblink is clicked
    }

    override fun onFlashCardClicked(resource: Resource) {
        // Do something when weblink is clicked
    }

    override fun onQuizAction(action: ActionType, resource: Resource, bookId: String?) {
        // Do something when quiz is clicked
    }

    override fun onVideoAction(action: ActionType, resource: Resource, resSheet: ResourceActionSheet) {
        // Do something when video is clicked
    }

    override fun onBookDetails(chapterListConfig: ChaptersListConfig) {
        // Do something when book cover image is clicked
    }

    override fun onStopAudioClick() {
        TODO("Not yet implemented")
    }

    override fun upgradeEbook() {

    }

    override fun onGenericReadClicked(chapterList: List<ChapterResources>) {

    }

    companion object {
        @JvmStatic
        fun createIntent(context: Context, chaptersListConfig: ChaptersListConfig) = Intent(context, DemoChaptersListAct::class.java).also {
            it.putExtra(CHAPTER_LIST_CONFIG, chaptersListConfig)
        }
    }
}