// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext {
        kotlin_version = '1.9.22'
        core_ktx_version = '1.12.0'
        volley_version = '1.2.1'
        gson_version = '2.10.1'
        koin_version = "3.5.3"
        coroutine_version = "1.7.3"
        lottie_version = "6.3.0"
        lifecycle_version = "2.7.0"
        glide_version = "4.16.0"
        shimmer_version = "0.5.0"
        avloader_version = "2.1.3"
        deeplink_version = "16.1.5"
        razor_pay_version = "1.6.38"
        room_version = "2.6.1"
        swipe_refresh_version = "1.1.0"
        app_compat_version = "1.6.1"
        sdp_ssp_version = "1.1.0"
    }
    repositories {
        google()
        mavenCentral()
        maven {
            url "https://plugins.gradle.org/m2/"
        }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.2'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
        classpath "ca.cutterslade.gradle:gradle-dependency-analyze:1.10.0"
        classpath 'com.google.gms:google-services:4.4.0'
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.22'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.9'
    }
}

allprojects {
    configurations.all {
        resolutionStrategy {
            force "org.jetbrains.kotlin:kotlin-stdlib:1.9.22"
            force "org.jetbrains.kotlin:kotlin-stdlib-common:1.9.22"
            force "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.22"
            force "org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.22"
            force "joda-time:joda-time:2.12.5"
            eachDependency { details ->
                if (details.requested.group == 'joda-time' && details.requested.name.contains('no-tzdb')) {
                    details.useTarget group: 'joda-time', name: 'joda-time', version: '2.12.5'
                }
            }
        }
    }

    repositories {
        google()
        mavenCentral()
        maven { url "https://maven.google.com" }
        maven { url "https://jitpack.io" }
        flatDir {
            dirs("libs")
        }
    }
}
configurations {
    all*.exclude group: 'org.apache.httpcomponents', module: 'httpclient'
    all*.exclude group: 'org.apache.httpcomponents', module: 'httpcore'
    all*.exclude group: 'joda-time', module: 'joda-time-no-tzdb'
    all {
        resolutionStrategy {
            eachDependency { details ->
                if (details.requested.group == 'joda-time' && details.requested.name.contains('no-tzdb')) {
                    details.useTarget group: 'joda-time', name: 'joda-time', version: '2.12.5'
                }
            }
        }
    }
}
/*
task clean(type: Delete) {
    delete rootProject.buildDir
}*/
