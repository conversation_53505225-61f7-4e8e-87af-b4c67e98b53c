package com.ws.networking.gson

import android.util.Log
import com.google.gson.Gson
import com.ws.core_ui.extensions.forEachJSONObject
import com.wonderslate.commons.interfaces.JSONParser
import org.json.JSONArray
import org.json.JSONObject
import java.lang.Exception

class GsonParser: JSONParser {
    override fun <T> fromJson(json: String, classOfT: Class<T>): T? {
        return try {
            Gson().fromJson(json, classOfT)
        } catch (e: Exception) {
            Log.e(TAG, "fromJson: ", e)
            null
        }
    }

    override fun <T> parseList(jsonArray: JSONArray, classOfT: Class<T>): List<T> {
        val list = mutableListOf<T>()
        try {
            jsonArray.forEachJSONObject {
                try {
                    if(it != null) {
                        val model = Gson().fromJson(it.toString(), classOfT)
                        if (model != null) {
                            list.add(model)
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "parseList: ", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "parseList: ", e)
        }
        return list
    }

    override fun toJson(src: Any): String {
        return Gson().toJson(src)
    }

    // Legacy methods for backward compatibility
    fun <T : Any> parse(json: String, classType: Class<T>): T {
        return fromJson(json, classType) ?: throw IllegalArgumentException("Failed to parse JSON")
    }

    fun <T : Any> parseOrNull(json: String, classType: Class<T>): T? {
        return fromJson(json, classType)
    }

    companion object {
        private const val TAG = "GsonParser"
    }
}