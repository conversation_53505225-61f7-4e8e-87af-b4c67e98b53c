package com.ws.networking.volley

import com.android.volley.*
import com.android.volley.toolbox.HttpHeaderParser
import com.wonderslate.commons.Result
import com.ws.networking.utils.NetworkConstants
import com.ws.networking.utils.NetworkConstants.APPLICATION_JSON_TYPE
import org.json.JSONObject
import kotlin.coroutines.Continuation
import kotlin.coroutines.resumeWithException


internal class ByteArrayRequestWithParameters(
        url: String,
        continuation: Continuation<Result<ByteArray>>,
        private val token: String,
        private val requestBody: JSONObject? = null,
        method: Int = Method.GET
) : CustomVolleyRequest(
    method,
    url,
    { response: ByteArray? ->
        continuation.resumeWith(kotlin.Result.success(Result.Success(response ?: byteArrayOf())))
    }, { error: VolleyError ->
        val response = error.networkResponse
        val errorMsg = when {
            error is ClientError -> NetworkConstants.clientError
            error is ServerError && response != null -> NetworkConstants.serverError
            error is NoConnectionError -> NetworkConstants.noConnectionError
            else -> NetworkConstants.somethingWentWrong
        }
        continuation.resumeWithException(Exception(errorMsg))
    }
) {
    override fun getBodyContentType(): String {
        return APPLICATION_JSON_TYPE
    }

    override fun getBody(): ByteArray {
        return requestBody?.toString()?.toByteArray() ?: JSONObject().toString().toByteArray()
    }

    override fun getHeaders(): MutableMap<String, String> {
        val headers: MutableMap<String, String> = HashMap()
        headers["Accept"] = APPLICATION_JSON_TYPE
        headers["Accept-Charset"] = Charsets.UTF_8.name()
        headers["Content-Type"] = APPLICATION_JSON_TYPE
        if (token != "nil")
            headers["X-Auth-Token"] = token

        return headers
    }
}

internal open class CustomVolleyRequest constructor(
    requestMethod: Int, fileUrl: String?, listener: Response.Listener<ByteArray>,
    onErrorListener: Response.ErrorListener?
) : Request<ByteArray?>(requestMethod, fileUrl, onErrorListener) {
    private var mParams: Map<String, String> = HashMap()
    private val mListener: Response.Listener<ByteArray>
    override fun parseNetworkResponse(response: NetworkResponse): Response<ByteArray?>? {
        return Response.success(response.data, HttpHeaderParser.parseCacheHeaders(response))
    }

    override fun getParams(): Map<String, String> {
        return mParams
    }

    override fun deliverResponse(response: ByteArray?) {
        mListener.onResponse(response)
    }

    init {
        setShouldCache(false)
        mListener = listener
    }
}