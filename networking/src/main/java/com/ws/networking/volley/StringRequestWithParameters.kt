package com.ws.networking.volley

import com.android.volley.ClientError
import com.android.volley.NoConnectionError
import com.android.volley.ServerError
import com.android.volley.VolleyError
import com.android.volley.toolbox.StringRequest
import com.wonderslate.commons.Result
import com.ws.networking.utils.NetworkConstants
import com.ws.networking.utils.NetworkConstants.APPLICATION_JSON_TYPE
import org.json.JSONObject
import kotlin.coroutines.Continuation
import kotlin.coroutines.resumeWithException

internal class StringRequestWithParameters(
        url: String,
        continuation: Continuation<Result<String>>,
        private val token: String,
        private val requestBody: JSONObject? = null,
        method: Int = Method.GET
) : StringRequest(
    method,
    url,
    { response: String? ->
        continuation.resumeWith(kotlin.Result.success(Result.Success(response ?: "")))
    }, { error: VolleyError ->
        val response = error.networkResponse
        val errorMsg = when {
            error is ClientError -> NetworkConstants.clientError
            error is ServerError && response != null -> NetworkConstants.serverError
            error is NoConnectionError -> NetworkConstants.noConnectionError
            else -> NetworkConstants.somethingWentWrong
        }
        continuation.resumeWithException(Exception(errorMsg))
    }
) {
    override fun getBodyContentType(): String {
        return APPLICATION_JSON_TYPE
    }

    override fun getBody(): ByteArray {
        return requestBody?.toString()?.toByteArray() ?: JSONObject().toString().toByteArray()
    }

    override fun getHeaders(): MutableMap<String, String> {
        val headers: MutableMap<String, String> = HashMap()
        headers["Accept"] = APPLICATION_JSON_TYPE
        headers["Accept-Charset"] = Charsets.UTF_8.name()
        headers["Content-Type"] = APPLICATION_JSON_TYPE
        if (token != "nil")
            headers["X-Auth-Token"] = token

        return headers
    }
}