package com.ws.networking.volley

import com.android.volley.Request
import com.ws.commons.Result
import com.wonderslate.commons.exceptions.NoInternetException
import com.ws.networking.Networking
import com.ws.networking.NetworkRequest
import com.ws.networking.connnection_checker.ConnectionChecker
import com.ws.networking.utils.NetworkConstants
import kotlinx.coroutines.suspendCancellableCoroutine
import java.lang.Exception

internal class VolleyNetworkImpl(
    private val volleyHelper: VolleyHelper,
    private val connectionChecker: ConnectionChecker
): Networking {

    override suspend fun getRequest(request: NetworkRequest): Result<String> {
        return try {
            if(!connectionChecker.isNetworkConnected())
                throw NoInternetException(NetworkConstants.noInternetError)

            suspendCancellableCoroutine { continuation ->
                volleyHelper.addToRequestQueue(
                    StringRequestWithParameters(
                        url = request.getUrlWithParams(),
                        continuation = continuation,
                        token = request.token,
                        requestBody = request.body,
                        method = Request.Method.GET
                    )
                )
            }
        } catch (e: Exception) {
            Result.Failure(e)
        }

    }

    override suspend fun postRequest(request: NetworkRequest): Result<String> {
        return try {
            if(!connectionChecker.isNetworkConnected())
                throw NoInternetException(NetworkConstants.noInternetError)

            suspendCancellableCoroutine { continuation ->
                volleyHelper.addToRequestQueue(
                    StringRequestWithParameters(
                        url = request.getUrlWithParams(),
                        continuation = continuation,
                        token = request.token,
                        requestBody = request.body,
                        method = Request.Method.POST
                    )
                )
            }
        } catch (e: Exception) {
            Result.Failure(e)
        }
    }

    override suspend fun getByteArrayRequest(request: NetworkRequest): Result<ByteArray> {
        return try {
            if(!connectionChecker.isNetworkConnected())
                throw NoInternetException(NetworkConstants.noInternetError)

            suspendCancellableCoroutine { continuation ->
                volleyHelper.addToRequestQueue(
                    ByteArrayRequestWithParameters(
                        url = request.getUrlWithParams(),
                        continuation = continuation,
                        token = request.token,
                        requestBody = request.body,
                        method = Request.Method.GET
                    )
                )
            }
        } catch (e: Exception) {
            Result.Failure(e)
        }
    }

}