package com.ws.networking.di

import com.wonderslate.commons.interfaces.JSONParser
import com.ws.networking.Networking
import com.ws.networking.connnection_checker.ConnectionChecker
import com.ws.networking.connnection_checker.ConnectionCheckerImpl
import com.ws.networking.gson.GsonParser
import com.ws.networking.volley.VolleyHelper
import com.ws.networking.volley.VolleyNetworkImpl
import org.koin.core.context.loadKoinModules
import org.koin.dsl.module

object NetworkingModuleSetup {
    private val networkingModules = module {
        single { VolleyHelper(get()) }
        single<Networking> { VolleyNetworkImpl(get(), get()) }
        single<JSONParser> { GsonParser() }
        single<ConnectionChecker> { ConnectionCheckerImpl(get()) }
    }

    fun inject() {
        loadKoinModules(networkingModules)
    }
}