package com.ws.networking

import com.wonderslate.commons.Result

interface Networking {

    /**
     * Used for GET request
     *
     * @param request details of an API
     * @return result of API call
     */
    suspend fun getRequest(request: NetworkRequest): Result<String>

    /**
     * Used for POST request
     *
     * @param request details of an API
     * @return result of API call
     */
    suspend fun postRequest(request: NetworkRequest): Result<String>

    /**
     * Used for GET ByteArray request
     *
     * @param request details of an API
     * @return result of API call
     */
    suspend fun getByteArrayRequest(request: NetworkRequest): Result<ByteArray>
}