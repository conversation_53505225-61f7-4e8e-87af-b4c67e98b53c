package com.ws.networking.config

import android.content.Context
import com.wonderslate.commons.enums.Flavor
import com.wonderslate.commons.enums.Server
import com.wonderslate.commons.interfaces.NetworkConfigProvider
import com.wonderslate.commons.models.ModuleData
import com.wonderslate.commons.models.NetworkConfig
import com.ws.networking.R
import java.lang.IllegalArgumentException

class NetworkConfigProviderImpl(
    private val context: Context,
    private val data: ModuleData
): NetworkConfigProvider {
    override fun getNetworkConfig(): NetworkConfig {
        val (flavor, server) = data
        val siteId = flavor.siteId
        return when (server) {
            Server.DEV -> {
                NetworkConfig(
                    server = server,
                    siteId = siteId,
                    service = context.getString(R.string.service_dev)
                )
            }
            Server.QA -> {
                NetworkConfig(
                    server = server,
                    siteId = siteId,
                    service = context.getString(R.string.service_qa)
                )
            }
            Server.STAGING -> {
                NetworkConfig(
                    server = server,
                    siteId = siteId,
                    service = context.getString(R.string.service_staging)
                )
            }
            Server.PUBLISH -> {
                NetworkConfig(
                    server = server,
                    siteId = siteId,
                    service = context.getString(R.string.service_publish)
                )
            }
            Server.LIVE -> when (flavor) {
                Flavor.PREPJOY, Flavor.KARNATAKA, Flavor.ENGINEERING, Flavor.NEET, Flavor.CTET, Flavor.CA -> {
                    NetworkConfig(
                        server = server,
                        siteId = siteId,
                        service = context.getString(R.string.service_prepjoy_live)
                    )
                }
                Flavor.YCT -> {
                    NetworkConfig(
                            server = server,
                            siteId = siteId,
                            service = context.getString(R.string.service_yct_live)
                    )
                }
                Flavor.PRABHAT -> {
                    NetworkConfig(
                            server = server,
                            siteId = siteId,
                            service = context.getString(R.string.service_prabhat_live)
                    )
                }
                Flavor.KIRAN -> {
                    NetworkConfig(
                            server = server,
                            siteId = siteId,
                            service = context.getString(R.string.service_kiran_live)
                    )
                }
                Flavor.SPARDHA -> {
                    NetworkConfig(
                            server = server,
                            siteId = siteId,
                            service = context.getString(R.string.service_spardha_live)
                    )
                }
                Flavor.LIBWONDER -> {
                    NetworkConfig(
                        server = server,
                        siteId = siteId,
                        service = context.getString(R.string.service_ws_live),
                        chatService = context.getString(R.string.service_ws_live_comm)
                    )
                }
                Flavor.ARIHANT -> {
                    NetworkConfig(
                        server = server,
                        siteId = siteId,
                        service = context.getString(R.string.service_arihant_live)
                    )
                }
                else -> throw IllegalArgumentException("Invalid flavor type: $flavor, add a case for handling $flavor.")
            }
        }
    }
    
}