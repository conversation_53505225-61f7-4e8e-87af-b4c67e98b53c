<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center">

    <androidx.cardview.widget.CardView
        android:id="@+id/librarybookitemframe"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:focusable="true"
        android:foreground="?selectableItemBackground"
        app:cardCornerRadius="4dp"
        app:cardElevation="3dp"
        android:backgroundTint="@color/colorLibraryCardBg"
        app:cardPreventCornerOverlap="true"
        app:cardUseCompatPadding="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="8dp">

            <androidx.cardview.widget.CardView
                android:id="@+id/placeHolderCardView"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_margin="8dp"
                android:elevation="0dp"
                android:innerRadius="0dp"
                android:shape="rectangle"
                app:cardCornerRadius="6dp"
                app:cardElevation="0dp"
                app:layout_constraintDimensionRatio="13:16"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/bookcoverimagelibrary"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:scaleType="fitXY"
                    android:src="@drawable/book_cover_placeholder" />

            </androidx.cardview.widget.CardView>

            <ImageView
                android:id="@+id/notification_flag"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_margin="4dp"
                android:elevation="5dp"
                android:src="@drawable/circle_notification_label"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvBookType"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:background="@drawable/bg_book_type"
                android:paddingHorizontal="8dp"
                android:paddingVertical="5dp"
                android:textColor="@color/price_simmer_badge_color"
                android:textSize="12sp"
                android:layout_marginStart="-5dp"
                android:layout_marginBottom="40dp"
                app:layout_constraintBottom_toBottomOf="@+id/placeHolderCardView"
                app:layout_constraintStart_toStartOf="@id/placeHolderCardView"
                tools:text="ebook" />

            <TextView
                android:id="@+id/textBookTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginTop="4dp"
                android:ellipsize="end"
                android:fontFamily="@font/poppins_medium"
                android:gravity="center|left"
                android:lines="2"
                android:maxLines="2"
                android:textColor="@color/colorLibraryCardTitle"
                android:textSize="14sp"
                android:typeface="normal"
                app:autoSizeMaxTextSize="14sp"
                app:autoSizeMinTextSize="10sp"
                app:autoSizeTextType="uniform"
                app:layout_constraintEnd_toStartOf="@+id/imageViewMenu"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/placeHolderCardView"
                tools:text="Book name" />

            <ImageView
                android:id="@+id/imageViewMenu"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginEnd="8dp"
                android:padding="5dp"
                android:scaleType="fitXY"
                android:src="@drawable/ic_more_vert_lib"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/textBookTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/textBookTitle" />

            <TextView
                android:id="@+id/textPublisherName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginTop="4dp"
                android:fontFamily="@font/poppins_light"
                android:textColor="@color/lib_line_view_color"
                android:textSize="10sp"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/textBookTitle"
                tools:text="Publisher name" />

            <TextView
                android:id="@+id/textOfferPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginTop="8dp"
                android:fontFamily="@font/poppins_medium"
                android:textColor="@color/main_theme_red"
                android:textSize="13sp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textPublisherName"
                tools:text="100" />

            <TextView
                android:id="@+id/textOriginalPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:fontFamily="@font/poppins_regular"
                android:textColor="@color/text_stike_throught"
                android:textSize="12sp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/textOfferPrice"
                app:layout_constraintStart_toEndOf="@+id/textOfferPrice"
                app:layout_constraintTop_toTopOf="@+id/textOfferPrice"
                tools:text="150" />

            <ImageView
                android:id="@+id/imageViewPackageBook"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_marginEnd="4dp"
                android:src="@drawable/ic_package_book"
                app:tint="@color/colorLibraryCardTitle"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/textViewPackageBookCount"
                app:layout_constraintTop_toBottomOf="@+id/textOfferPrice" />

            <TextView
                android:id="@+id/textViewPackageBookCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:layout_marginEnd="8dp"
                android:text="(0)"
                android:textColor="@color/colorLibraryCardTitle"
                android:textSize="10sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textOfferPrice" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/vNotification"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:background="@drawable/circle_notification_label"
            android:elevation="8dp"
            android:visibility="gone"
            android:layout_margin="4dp"/>
    </androidx.cardview.widget.CardView>
</FrameLayout>