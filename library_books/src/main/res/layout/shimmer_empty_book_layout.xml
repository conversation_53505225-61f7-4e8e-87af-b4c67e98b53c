<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:padding="12dp">

    <View
        android:id="@+id/view"
        android:layout_width="0dp"
        android:layout_height="225dp"
        android:background="@drawable/bg_extra_rounded"
        android:backgroundTint="@color/light_gray"
        android:layout_marginEnd="6dp"
        app:layout_constraintEnd_toStartOf="@+id/view3"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/view3"
        android:layout_width="0dp"
        android:layout_height="225dp"
        android:background="@drawable/bg_extra_rounded"
        android:backgroundTint="@color/light_gray"
        android:layout_marginStart="6dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/view"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/view4"
        android:layout_width="0dp"
        android:layout_height="225dp"
        android:background="@drawable/bg_extra_rounded"
        android:backgroundTint="@color/light_gray"
        android:layout_marginEnd="6dp"
        android:layout_marginTop="12dp"
        app:layout_constraintEnd_toStartOf="@+id/view5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view" />

    <View
        android:id="@+id/view5"
        android:layout_width="0dp"
        android:layout_height="225dp"
        android:background="@drawable/bg_extra_rounded"
        android:backgroundTint="@color/light_gray"
        android:layout_marginStart="6dp"
        android:layout_marginTop="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/view4"
        app:layout_constraintTop_toBottomOf="@id/view3" />

</androidx.constraintlayout.widget.ConstraintLayout>
