<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:id="@+id/rlLibrary"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorLibraryBg"
    android:animateLayoutChanges="true"
    xmlns:tools="http://schemas.android.com/tools">

    <AutoCompleteTextView
        android:id="@+id/library_search"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="8dp"
        android:layout_marginRight="12dp"
        android:background="@drawable/all_course_list_item_background"
        android:hint="Search title"
        android:maxLines="1"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:layout_marginBottom="8dp"
        android:singleLine="true"
        android:textColor="@color/colorLibrarySearchText"
        android:textColorHint="@color/colorLibrarySearchHint"
        android:textSize="12sp"
        android:visibility="visible" />

    <LinearLayout
        android:id="@+id/layout_self_institute_layout"
        android:layout_below="@id/library_search"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/layout_self"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:background="@drawable/shadow_full_curved_layout_bg"
            android:orientation="horizontal"
            android:padding="10dp"
            android:visibility="visible">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_gravity="center"
                android:layout_margin="3dp">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/avatar_bg_circle"
                    android:gravity="center">

                    <androidx.cardview.widget.CardView
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_centerHorizontal="true"
                        android:elevation="12dp"
                        app:cardCornerRadius="60dp">

                        <ImageView
                            android:id="@+id/imageView_user_image"
                            android:layout_width="25dp"
                            android:layout_height="25dp"
                            android:scaleType="centerInside"
                            app:srcCompat="@drawable/ic_place_holder_profile" />
                    </androidx.cardview.widget.CardView>
                </RelativeLayout>
            </RelativeLayout>

            <TextView
                android:id="@+id/textview_my_self"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="3dp"
                android:layout_marginRight="5dp"
                android:paddingRight="8dp"
                android:text="My Shelf"
                android:textColor="@color/black"
                android:textSize="12sp"
                android:textStyle="bold" />
        </LinearLayout>

        <View
            android:id="@+id/view_line"
            android:layout_width="1dp"
            android:layout_height="20dp"
            android:layout_marginLeft="5dp"
            android:layout_marginTop="18dp"
            android:layout_marginRight="0dp"
            android:alpha=".3"
            android:background="@color/colorAccentDark"
            android:visibility="visible" />

        <androidx.appcompat.widget.AppCompatSpinner
            android:id="@+id/institute_spinner"
            style="@style/CustomSpinner"
            android:layout_width="match_parent"
            android:layout_height="55dp"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="5dp"
            android:background="@drawable/custom_spinner_shadow_bg_library_primery"
            android:overlapAnchor="false" />
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/rlEmpty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:visibility="gone"
        android:paddingHorizontal="16dp">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/empty_lottie"
            android:layout_width="150dp"
            android:layout_height="150dp"
            android:layout_centerHorizontal="true"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/empty_lottie" />

        <TextView
            android:id="@+id/tvEmptyMsg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/empty_lottie"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:padding="3dp"
            android:text="@string/nobooklibrary"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/colorLibraryErrorText"
            android:typeface="normal"
            android:visibility="visible" />

    </RelativeLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeToRefresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/layout_self_institute_layout">

        <androidx.core.widget.NestedScrollView
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:id="@+id/lltEbookBody"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="visible">

                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="14dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="14dp"
                    android:elevation="0dp"
                    app:cardElevation="0dp"
                    app:contentPadding="0dp"
                    android:backgroundTint="@color/colorLibraryBg"
                    android:visibility="visible">

                    <androidx.appcompat.widget.AppCompatSpinner
                        android:id="@+id/sortSpinner"
                        style="@style/CustomSpinner"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:background="@drawable/custom_spinner_selected_bg_lite"
                        android:overlapAnchor="false" />

                </androidx.cardview.widget.CardView>

                <LinearLayout
                    android:id="@+id/llAccessCode"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="3dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/textView_access_code"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginLeft="15dp"
                        android:fontFamily="@font/poppins_medium"
                        android:justificationMode="inter_word"
                        android:maxLines="1"
                        android:text="Do you have access code?"
                        android:textColor="@color/text"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/textView_access_code_click"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginLeft="5dp"
                        android:fontFamily="@font/poppins_medium"
                        android:justificationMode="inter_word"
                        android:maxLines="1"
                        android:text="Click here"
                        android:textColor="@color/live_video_audio_text"
                        android:textSize="14sp" />
                </LinearLayout>

                <com.facebook.shimmer.ShimmerFrameLayout
                    android:id="@+id/shimmerBooks"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="visible"
                    android:padding="4dp"
                    tools:visibility="gone">

                    <include
                        layout="@layout/shimmer_empty_book_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </com.facebook.shimmer.ShimmerFrameLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="5dp">

                    <LinearLayout
                        android:id="@+id/lltDoubtsRechargeLibrary"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:background="@drawable/all_course_list_item_background"
                        android:gravity="start"
                        android:visibility="gone"
                        tools:visibility="visible"
                        android:paddingTop="10dp"
                        android:paddingBottom="10dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginEnd="10dp"
                        android:layout_marginStart="3dp">

                        <TextView
                            android:id="@+id/tvPaidTokensCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_gravity="center_vertical"
                            android:textSize="14sp"
                            android:textColor="@color/white"
                            android:fontFamily="@font/poppins_medium"
                            android:layout_marginEnd="50dp"
                            tools:text="Doubts Balance: 200"
                            android:drawableEnd="@drawable/doubts_coins_icon"
                            android:drawablePadding="5dp"/>

                        <Button
                            android:id="@+id/btnRechargeLibrary"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_marginEnd="8dp"
                            android:text="Recharge"
                            android:textAllCaps="false"
                            android:backgroundTint="@color/ws_new_solid_color"
                            android:background="@drawable/bg_red_rounded"
                            android:textColor="@android:color/white"
                            android:elevation="4dp"
                            android:paddingStart="10dp"
                            android:paddingEnd="10dp"
                            android:drawableEnd="@drawable/recharge_doubts"
                            android:layout_gravity="end"/>

                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/EbookRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/lltDoubtsRechargeLibrary"
                        android:paddingTop="8dp"
                        android:paddingBottom="80dp"
                        android:clipToPadding="false" />

                    <ProgressBar
                        android:id="@+id/idPBLoading"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/EbookRecyclerView"
                        android:visibility="gone" />
                </RelativeLayout>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <ProgressBar
        android:id="@+id/libraryLoader"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="gone"
        android:indeterminateTint="@color/colorAccent" />
</RelativeLayout>