package com.ws.library.books.ui.adapters

import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.recyclerview.widget.RecyclerView
import androidx.cardview.widget.CardView
import com.ws.commons.enums.BookType
import com.ws.commons.enums.Flavor
import com.ws.commons.extensions.clearAndAddAll
import com.ws.commons.interfaces.FlavorConfig
import com.ws.commons.interfaces.ImageUrlProvider
import com.ws.commons.interfaces.UpdateNotificationManager
import com.ws.commons.models.BookCoverUrlRequest
import com.ws.core_ui.extensions.hideView
import com.ws.core_ui.extensions.loadImage
import com.ws.core_ui.extensions.showView
import com.ws.core_ui.extensions.visibility
import com.ws.database.room.entity.LibraryBook
import com.ws.library.books.R
import com.ws.library.books.ui.enums.UserBooksSortOption
import java.lang.Exception
import kotlin.collections.ArrayList

class UserBooksAdapter(
    private val bookList: ArrayList<LibraryBook>,
    private val imageUrlProvider: ImageUrlProvider,
    private val flavorConfig: FlavorConfig,
    private val updateNotificationManager: UpdateNotificationManager
) : RecyclerView.Adapter<UserBooksAdapter.ViewHolder>(), Filterable {

    private var searchBooksList: List<LibraryBook> = listOf()
    private var isInstituteBooks = false

    var onNoBooksFoundListener: ((Boolean) -> Unit)? = null

    var onItemClicked: ((LibraryBook) -> Unit)? = null

    override fun onCreateViewHolder(viewGroup: ViewGroup, i: Int): ViewHolder {
        val v = LayoutInflater.from(viewGroup.context).inflate(R.layout.library_ebook_grid, viewGroup, false)
        return ViewHolder(v)
    }

    override fun onBindViewHolder(viewHolder: ViewHolder, i: Int) {
        try {
            val book = searchBooksList[i]

            viewHolder.mBookName.text = book.title

            viewHolder.vNotification.visibility(updateNotificationManager.isBookUpdated(book.id.toString()))

            if (flavorConfig.showBookTypeTag) {
                if(flavorConfig.currentFlavor == Flavor.PREPJOY) {
                    if (book.hasQuiz == "true") {
                        viewHolder.tvBookType.text = "Online Tests"
                    }
                    else {
                        viewHolder.tvBookType.text = "eBook"
                    }
                    if (BookType.getBookTypeFor(book.bookType) == BookType.BOOKGPT) {
                        viewHolder.tvBookType.text = "iBookGPT"
                    }
                    if (BookType.getBookTypeFor(book.bookType) == BookType.EBOOKWITHAI) {
                        viewHolder.tvBookType.text = "eBook (with AI Doubt Solver)"
                    }
                } else {
                    viewHolder.tvBookType.showView()
                    viewHolder.tvBookType.text = BookType.getBookTypeFor(book.bookType).formatted
                }
            } else {
                viewHolder.tvBookType.hideView()
            }

            var coverImgLoc = ""

            coverImgLoc = if (book.coverImage.startsWith("https://")) {
                book.coverImage
            } else {
                imageUrlProvider.getBookCoverUrl(BookCoverUrlRequest(book.coverImage, book.id.toString()))
            }
            viewHolder.mBookCover.loadImage(coverImgLoc, animate = true)

            //Fro Package book logic
            if (book.packageBooks?.isNotEmpty() == true) {
                viewHolder.imageViewPackageBook.visibility = View.VISIBLE
                viewHolder.packageBookCount.visibility = View.GONE //Make visible to see count.
                val sizeText ="(${book.packageBooks!!.size})"
                viewHolder.packageBookCount.text = sizeText
            } else {
                viewHolder.imageViewPackageBook.visibility = View.GONE
                viewHolder.packageBookCount.visibility = View.GONE
            }
            //Fro Package book logic
            viewHolder.libraryBookItemFrame.setOnClickListener { view: View? ->
                onItemClicked?.invoke(book)
            }

            viewHolder.imageViewMenu.visibility = View.GONE
        } catch (e: Exception) {
            Log.e(TAG, "onBindViewHolder: ", e)
        }
    }

    override fun getItemCount(): Int {
        return searchBooksList.size
    }

    override fun getFilter(): Filter {

        val filteredBooksList = arrayListOf<LibraryBook>()

        return object : Filter() {
            override fun performFiltering(constraint: CharSequence): FilterResults {
                filteredBooksList.clear()

                val charString = constraint.toString()

                if (charString.isEmpty()) {
                    filteredBooksList.addAll(bookList)
                } else {
                    // name match condition.
                    filteredBooksList.addAll(bookList.filter { book ->
                        book.title.lowercase().contains(charString.lowercase())
                    })
                }

                val filterResults = FilterResults()
                filterResults.values = filteredBooksList
                return filterResults
            }

            override fun publishResults(constraint: CharSequence, results: FilterResults) {
                searchBooksList = results.values as ArrayList<LibraryBook>
                if (searchBooksList.isEmpty())
                    onNoBooksFoundListener?.invoke(true)
                else
                    onNoBooksFoundListener?.invoke(false)
                notifyDataSetChanged()
            }
        }

    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        var mBookName: TextView = itemView.findViewById(R.id.textBookTitle)
        var packageBookCount: TextView = itemView.findViewById(R.id.textViewPackageBookCount)
        var mBookCover: ImageView = itemView.findViewById(R.id.bookcoverimagelibrary)
        var vNotification: View = itemView.findViewById(R.id.vNotification)
        var imageViewPackageBook: ImageView = itemView.findViewById(R.id.imageViewPackageBook)
        var imageViewMenu: ImageView = itemView.findViewById(R.id.imageViewMenu)
        var imageContainerCardView: CardView = itemView.findViewById(R.id.placeHolderCardView)
        var libraryBookItemFrame: CardView = itemView.findViewById(R.id.librarybookitemframe)
        var body: FrameLayout = itemView.findViewById(R.id.container_view)
        var tvBookType: TextView = itemView.findViewById(R.id.tvBookType)
    }

    fun sortBy(option: UserBooksSortOption) {
        val sortedList = when(option) {
            UserBooksSortOption.SORT_BY -> {
                bookList.toList()
            }

            UserBooksSortOption.A_Z_TITLE -> {
                bookList.toList().sortedBy { it.title.lowercase() }
            }

            UserBooksSortOption.Z_A_TITLE -> {
                bookList.toList().sortedByDescending { it.title.lowercase() }
            }
        }
        searchBooksList = sortedList
        notifyDataSetChanged()
    }

    @JvmOverloads
    fun setBooks(list: List<LibraryBook>, isInstitute: Boolean = false) {
        bookList.clearAndAddAll(list)
        searchBooksList = list
        isInstituteBooks = isInstitute
        notifyDataSetChanged()
    }

    companion object {
        private const val TAG = "EbookContentAdapter"
    }
}