package com.ws.library.books.ui

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.ArrayAdapter
import androidx.core.os.bundleOf
import androidx.core.widget.doOnTextChanged
import androidx.recyclerview.widget.GridLayoutManager
import com.wonderslate.data.network.Wonderslate
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.ws.commons.Status
import com.ws.core_ui.base.BaseFragmentWithListener
import com.ws.core_ui.extensions.*
import com.ws.core_ui.utils.IntentConstants
import com.ws.core_ui.utils.TempConfig
import com.ws.library.books.R
import com.ws.library.books.data.models.LibraryBooksRequest
import com.ws.library.books.databinding.LayoutLibraryBooksBinding
import com.ws.library.books.ui.adapters.UserBooksAdapter
import com.ws.library.books.ui.enums.UserBooksSortOption
import kotlinx.coroutines.*
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.lang.String

class LibraryBooksFrag: BaseFragmentWithListener<LayoutLibraryBooksBinding, LibraryBooksFrag.LibraryBooksFragmentListener>() {

    private val viewModel by viewModel<LibraryBooksFragViewModel>()

    private val booksAdapter by inject<UserBooksAdapter>()

    private var showSearchBox = false

    private val sortOptions = UserBooksSortOption.values()

    override fun inflateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): LayoutLibraryBooksBinding {
        return LayoutLibraryBooksBinding.inflate(inflater, container, false)
    }

    override fun initArguments(bundle: Bundle?) {
        showSearchBox = bundle?.getBoolean(IntentConstants.SHOW_SEARCH_BOX) ?: false
    }

    override fun initView() {
        //This is required because "rlLibrary" is using "animateLayoutChanges=true"
        binding?.rlLibrary?.layoutTransition?.setAnimateParentHierarchy(false)

        initRecycler()
        initSwipeToRefresh()
        initClicks()
        initSearchBox()
        initSortBy()
        initAccessCode()
        initObserver()
    }

    override fun load() {
        binding?.shimmerBooks?.apply {
            showView()
            startShimmer()
        }

        if (WonderPubSharedPrefs.getInstance(requireContext()).shouldRedirectToLibrary()) {
            viewModel.getLibraryBooks(LibraryBooksRequest(forceRefresh = true))
        }
        else {
            viewModel.getLibraryBooks()
        }
        WonderPubSharedPrefs.getInstance(requireContext()).setRedirectToLibrary(false)
    }

    override fun whenResumed() {
        if(isFragmentLoaded())
            viewModel.getLibraryBooks()
    }

    private fun initClicks() {
        booksAdapter.onItemClicked = {
            if(it.packageBooks.isNullOrEmpty()) {

                val language = it.bookLanguage ?: ""
                TempConfig.isEnglishBook = language.equals("english", true) || language.isEmpty()

                listener?.onLibraryBookItemClicked(it.id)
            } else {
                // Show package books bottom sheet if package books are present
                PackageBottomSheetFragment.show(childFragmentManager, it) { view, book ->

                    val language = it.bookLanguage ?: ""
                    TempConfig.isEnglishBook = language.equals("english", true) || language.isEmpty()

                    listener?.onLibraryBookItemClicked(book.id)
                }
            }
        }
        binding?.textViewAccessCodeClick?.setOnClickListener {
            //TODO: Show access code dialog
        }
        binding?.sortSpinner?.onItemSelectedListener { adapterView, i -> 
            booksAdapter.sortBy(sortOptions[i])
        }
    }

    private fun initSwipeToRefresh() {
        binding?.swipeToRefresh?.setOnRefreshListener {
            viewModel.getLibraryBooks(LibraryBooksRequest(forceRefresh = true))
        }
    }

    private fun initRecycler() {
        booksAdapter.onNoBooksFoundListener = {
            handleOnNoBooksFound(it)
        }
        binding?.EbookRecyclerView?.apply {
            layoutManager = GridLayoutManager(requireContext(), 2, GridLayoutManager.VERTICAL, false)
            adapter = booksAdapter
        }
    }

    private fun initSearchBox() {
        binding?.librarySearch?.apply {
            visibility(showSearchBox)

            setOnFocusChangeListener { v, hasFocus ->
                if (!hasFocus) {
                    context?.let {
                        val inputMethodManager = it.getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
                        inputMethodManager.hideSoftInputFromWindow(v.windowToken, 0)
                    }
                }
            }

            doOnTextChanged { text, _, before, count ->
                booksAdapter.filter.filter(text.toString())
            }
        }
    }

    private fun initSortBy() {
        binding?.sortSpinner?.apply {
            hideView()
            val adapt = ArrayAdapter(requireContext(), R.layout.filter_spinner_item, sortOptions.map { it.text })
            adapt.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            adapter = adapt
        }
    }

    fun onSearchIconClicked() {
        binding?.librarySearch?.toggleViewVisibility()
    }

    fun showSearchBar(show: Boolean) {
        binding?.librarySearch?.visibility(show)
    }

    private fun initAccessCode() {
        binding?.llAccessCode?.hideView()
        //TODO: init access code
    }

    private fun initObserver() {

        viewModel.libraryBooks.collectLatestWithLifecycle(this) { result ->
            when(result.responseType) {
                Status.LOADING -> {
                    binding?.swipeToRefresh?.isRefreshing = false
                    binding?.libraryLoader?.showView()
                }

                Status.SUCCESSFUL -> {

                    val books = result.data ?: emptyList()

                    //Show sorting option only if more than 4 books are present
                    binding?.sortSpinner?.visibility(books.size > 4)

                    //if no books are present show empty page
                    handleOnEmptyBooks(books.isEmpty())

                    binding?.swipeToRefresh?.isRefreshing = false

                    //Delay loading books so changing tabs can be smoother
                    delay(300)
                    binding?.shimmerBooks?.hideView()
                    binding?.libraryLoader?.hideView()
                    booksAdapter.setBooks(books)
                }

                Status.ERROR, Status.HTTP_UNAVAILABLE -> {
                    binding?.shimmerBooks?.hideView()
                    binding?.libraryLoader?.hideView()

                    //if no books are present show empty page
                    handleOnEmptyBooks(booksAdapter.itemCount == 0)

                    showToast(result.error?.message)
                }

                else -> {}
            }
        }
    }

    private fun handleOnNoBooksFound(noBooksFound: Boolean) {
        if (noBooksFound) {
            binding?.rlEmpty?.showView()
            binding?.tvEmptyMsg?.setText(R.string.no_result_found)
            binding?.EbookRecyclerView?.hideView()
        } else {
            binding?.rlEmpty?.hideView()
            binding?.tvEmptyMsg?.setText(R.string.nobooklibrary)
            binding?.EbookRecyclerView?.showView()
        }
    }

    private fun handleOnEmptyBooks(emptyBooks: Boolean) {
        if(emptyBooks) {
            binding?.rlEmpty?.showView()
            binding?.EbookRecyclerView?.hideView()
        } else {
            binding?.rlEmpty?.hideView()
            binding?.EbookRecyclerView?.showView()
        }
    }

    interface LibraryBooksFragmentListener {
        fun onLibraryBookItemClicked(bookId: Int)
    }

    companion object {
        @JvmStatic
        @JvmOverloads
        fun newInstance(showSearchBox: Boolean = false) = LibraryBooksFrag().also {
            it.arguments = bundleOf(IntentConstants.SHOW_SEARCH_BOX to showSearchBox)
        }
    }
}