package com.ws.library.books.data.repositories

import com.ws.commons.Result
import com.ws.database.room.entity.LibraryBook
import com.ws.library.books.data.models.LibraryBooksRequest
import com.ws.library.books.data.models.UserLibraryBooksResponse
import kotlinx.coroutines.flow.Flow

interface LibraryBooksRepository {
    suspend fun fetchUserLibraryBooks(libraryBooksRequest: LibraryBooksRequest): Result<UserLibraryBooksResponse>
    suspend fun cacheLibraryBooks(books: List<LibraryBook>)
    suspend fun getCachedUserLibraryBooks(): List<LibraryBook>
    suspend fun findCachedUserLibraryBook(bookId: Int): List<LibraryBook>
    fun getCachedUserLibraryBooksAsFlow(): Flow<List<LibraryBook>>
}