package com.wonderslate.data.db;


import static com.wonderslate.data.db.WonderPublishDBContract.ANNOTATION_ACTION;
import static com.wonderslate.data.db.WonderPublishDBContract.ANNOTATION_ACTION_TABLE_NAME;
import static com.wonderslate.data.db.WonderPublishDBContract.ANNOTATION_DATE_TIME;
import static com.wonderslate.data.db.WonderPublishDBContract.ANNOTATION_ID;
import static com.wonderslate.data.db.WonderPublishDBContract.ANNOTATION_JSON;
import static com.wonderslate.data.db.WonderPublishDBContract.ANNOTATION_RES_ID;
import static com.wonderslate.data.db.WonderPublishDBContract.CHAPTER_DETAILS_CHAPTER_ID_COL;
import static com.wonderslate.data.db.WonderPublishDBContract.CHAPTER_DETAILS_COL;
import static com.wonderslate.data.db.WonderPublishDBContract.CHAPTER_DETAILS_TABLE_NAME;
import static com.wonderslate.data.db.WonderPublishDBContract.CHAPTER_LIST_BOOK_ID_COL;
import static com.wonderslate.data.db.WonderPublishDBContract.CHAPTER_LIST_COL;
import static com.wonderslate.data.db.WonderPublishDBContract.CHAPTER_LIST_TABLE_NAME;
import static com.wonderslate.data.db.WonderPublishDBContract.CHECKING_USERNAME_TABLE_NAME;
import static com.wonderslate.data.db.WonderPublishDBContract.DATA_STATUS;
import static com.wonderslate.data.db.WonderPublishDBContract.DATA_UPDATE_USER_LIBRARY_BOOKS;
import static com.wonderslate.data.db.WonderPublishDBContract.EPUB_HTML_LINK_TABLE_NAME;
import static com.wonderslate.data.db.WonderPublishDBContract.EPUB_HTML_TYPE_COL;
import static com.wonderslate.data.db.WonderPublishDBContract.GENERAL_DATA_TABLE;
import static com.wonderslate.data.db.WonderPublishDBContract.NOTES_ANNOTATION_TABLE_NAME;
import static com.wonderslate.data.db.WonderPublishDBContract.NOTES_JSON;
import static com.wonderslate.data.db.WonderPublishDBContract.RESPONSE_DATA;
import static com.wonderslate.data.db.WonderPublishDBContract.RES_ID;
import static com.wonderslate.data.db.WonderPublishDBContract.USAGE_HISTORY_TABLE_NAME;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class WonderPublishDataSource {
    private static final String BOOK_TABLE = WonderPublishDBContract.BOOK_TABLE_NAME;
    private static final String USER_BOOK_TABLE = WonderPublishDBContract.USER_BOOK_TABLE_NAME;
    private static final String CHAPTER_TABLE = WonderPublishDBContract.CHAPTER_TABLE_NAME;
    private static final String QUIZ_TABLE = WonderPublishDBContract.QUIZ_TABLE_NAME;
    //private static final String CHALLENGE_TABLE = WonderPublishDBContract.QUIZ_CHALLENGES_TABLE_NAME;
    private static final String REF_LINK_TABLE = WonderPublishDBContract.REFERENCE_LINK_TABLE_NAME;
    private static final String EPUB_HTML_TABLE = EPUB_HTML_LINK_TABLE_NAME;
    private static final String UPDATE_WITH_QUIZ_ANSWER_TABLE = WonderPublishDBContract.UPDATE_WITH_QUIZ_ANSWER_TABLE_NAME;
    private static final String USAGE_HISTORY_TABLE = USAGE_HISTORY_TABLE_NAME;
    private final Context context;
    private static WonderPublishDBHelper dbHelper;
    private static SQLiteDatabase database;

    public WonderPublishDataSource(Context context, String name) {

        this.context = context;
        if (dbHelper == null) {
            dbHelper = new WonderPublishDBHelper(context, name);
        }
    }

    /**
     * Opens database in write mode
     *
     * @throws SQLException
     */
    public synchronized void open() throws SQLException {
        if (database == null) {
            String myPath = dbHelper.getReadableDatabase().getPath();
            database = SQLiteDatabase.openDatabase(myPath, null, SQLiteDatabase.NO_LOCALIZED_COLLATORS | SQLiteDatabase.OPEN_READWRITE);
        }
    }


    public synchronized void close() {
        if (database != null && database.isOpen())
            dbHelper.close();
    }


    public String getJSONDataById(String searchId) {
        final String searchStr =
                WonderPublishDBContract.DATA_ID + " =? ";
        String[] args = new String[1];
        args[0] = searchId;
        Cursor cursor = database.query(
                GENERAL_DATA_TABLE, new String[]{RESPONSE_DATA},
                searchStr, args, null, null, null);
        if (cursor.getCount() == 0) {
            cursor.close();
            return null;
        }
        cursor.moveToFirst();
        String returnString = cursor.getString(0);
        cursor.close();
        return returnString;
    }


    public void storeChapterListData(String bookId, String chapterList) {
        ContentValues values = new ContentValues();
        values.put(CHAPTER_LIST_COL, chapterList);
        if (!database.isOpen())
            open();

        if (getChapterListData(bookId) != null) {
            String searchString = CHAPTER_LIST_BOOK_ID_COL + " =? ";
            String[] args = new String[1];
            args[0] = bookId;
            database.update(CHAPTER_LIST_TABLE_NAME, values, searchString, args);
        } else {
            values.put(CHAPTER_LIST_BOOK_ID_COL, bookId);
            database.insert(CHAPTER_LIST_TABLE_NAME, null, values);
        }

    }

    public String getChapterListData(String bookId) {
        final String searchStr =
                CHAPTER_LIST_BOOK_ID_COL + " =? ";
        String[] args = new String[1];
        args[0] = bookId;
        Cursor cursor = database.query(
                CHAPTER_LIST_TABLE_NAME, new String[]{CHAPTER_LIST_COL},
                searchStr, args, null, null, null);
        if (cursor.getCount() == 0) {
            cursor.close();
            return null;
        }
        cursor.moveToFirst();
        String returnString = cursor.getString(0);
        cursor.close();
        return returnString;
    }


    public String getChapterDetailsData(String chapterId) {
        final String searchStr =
                CHAPTER_DETAILS_CHAPTER_ID_COL + " =? ";
        String[] args = new String[1];
        args[0] = chapterId;
        Cursor cursor = database.query(
                CHAPTER_DETAILS_TABLE_NAME, new String[]{CHAPTER_DETAILS_COL},
                searchStr, args, null, null, null);
        if (cursor.getCount() == 0) {
            cursor.close();
            return null;
        }
        cursor.moveToFirst();
        String returnString = cursor.getString(0);
        cursor.close();
        return returnString;
    }

    public void updateBookJSONData(String id, String value) {
        try {
            ContentValues values = new ContentValues();
            values.put(RESPONSE_DATA, value);
            if (!database.isOpen())
                open();
            if (getJSONDataById(DATA_UPDATE_USER_LIBRARY_BOOKS) != null) {
                String searchStr =
                        WonderPublishDBContract.DATA_ID + " =? ";
                String[] args = new String[1];
                args[0] = id;
                database.update(GENERAL_DATA_TABLE, values, searchStr, args);
            }
        } catch (Exception e) {
            Log.e("DATA SOURCE", e.getMessage());
        }
    }

    public void updateNotificationDeleteState(String id) {
        try {
            //Update Notification Data State To Deleted
            ContentValues values = new ContentValues();
            values.put(DATA_STATUS, "Deleted");
            if (!database.isOpen())
                open();
            if (getJSONDataById(id) != null) {
                String searchStr =
                        WonderPublishDBContract.DATA_ID + " =? ";
                String[] args = new String[1];
                args[0] = id;
                database.update(GENERAL_DATA_TABLE, values, searchStr, args);
            }
        } catch (Exception e) {
            Log.e("DATA SOURCE", e.getMessage());
        }
    }


    public void removeJSONData(String id) {
        if (getJSONDataById(id) != null) {
            String selection =
                    WonderPublishDBContract.DATA_ID + " =?";
            String[] args = new String[1];
            args[0] = id;
            database.delete(GENERAL_DATA_TABLE, selection, args);
        }
    }

    public void addNotesAnnotationToDatabase(String resId, JSONObject jsonResponse) {
        ContentValues values = new ContentValues();

        values.put(NOTES_JSON, jsonResponse.toString());
        if (getNotesAnnotationByResId(resId) != null) {
            final String searchStr =
                    RES_ID + " = " + resId;
            database.update(NOTES_ANNOTATION_TABLE_NAME, values, searchStr, null);
        } else {
            values.put(RES_ID, resId);
            database.insert(NOTES_ANNOTATION_TABLE_NAME, null, values);
        }
    }

    public String storeAnnotationAction(String resId, JSONObject annotationData, String action) {
        String annotationId = "";
        ContentValues values = new ContentValues();
        values.put(ANNOTATION_RES_ID, resId);
        values.put(ANNOTATION_JSON, annotationData.toString());
        values.put(ANNOTATION_DATE_TIME, getDate());
        values.put(ANNOTATION_ACTION, action);
        if (!action.equalsIgnoreCase("create")) {
            try {
                values.put(ANNOTATION_ID, annotationData.getString("id"));
            } catch (JSONException e) {
                Log.e("WonderDataSource", e.getMessage());
            }
        }
        long id = database.insert(ANNOTATION_ACTION_TABLE_NAME, null, values);
        annotationId = id + "";
        if (id != -1 && action.equalsIgnoreCase("create")) {
            String searchStr =
                    "_id" + " = " + id;
            values.clear();
            annotationId = "local" + id;
            values.put(ANNOTATION_ID, annotationId);
            database.update(ANNOTATION_ACTION_TABLE_NAME, values, searchStr, null);
        }
        return annotationId;
    }

    public void updateAnnotationAction(String annotationId, String resId, JSONObject updatedJson) {
        if (getAnnotationAction(annotationId, resId) != null) {
            ContentValues values = new ContentValues();
            values.put(ANNOTATION_JSON, updatedJson.toString());
            values.put(ANNOTATION_DATE_TIME, getDate());
            if (!annotationId.contains("local"))
                values.put(ANNOTATION_ACTION, "update");
            String selection =
                    ANNOTATION_RES_ID + " =? and " + ANNOTATION_ID + " =?";
            String[] args = new String[2];
            args[0] = resId;
            args[1] = annotationId;
            database.update(ANNOTATION_ACTION_TABLE_NAME, values, selection, args);
        }
    }

    public void deleteAnnotationAction(String annotationId, String resId) {
        if (getAnnotationAction(annotationId, resId) != null) {
            String selection =
                    ANNOTATION_RES_ID + " =? and " + ANNOTATION_ID + " =?";
            String[] args = new String[2];
            args[0] = resId;
            args[1] = annotationId;
            database.delete(ANNOTATION_ACTION_TABLE_NAME, selection, args);
        }
    }

    private String getDate() {
        LocalDateTime date = LocalDateTime.now();
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("dd-MMMM-yyyy HH:mm:ss");
        return date.format(fmt);
    }

    public String getAnnotationAction(String annotationId, String resId) {
        String selection =
                ANNOTATION_RES_ID + " =? and " + ANNOTATION_ID + " =?";
        String[] args = new String[2];
        args[0] = resId;
        args[1] = annotationId;
//        String searchStr =
//                WonderPublishDBContract.ANNOTATION_ID + " = " + annotationId + " and " + ANNOTATION_RES_ID + " = " + resId;
        if (!database.isOpen())
            open();
        Cursor cursor = database.query(
                ANNOTATION_ACTION_TABLE_NAME, WonderPublishDBContract.annotationActionAllColumns,
                selection, args, null, null, null);
        if (cursor.getCount() == 0) {
            return null;
        }
        cursor.moveToFirst();
        String dataStr = cursor.getString(2);
        cursor.close();
        return dataStr;
    }

    public void storeReadData(String urlId, String jsonEncryptString) {
        ContentValues values = new ContentValues();
        String idString = urlId + "CreatedOnline";
        values.put(EPUB_HTML_TYPE_COL, jsonEncryptString);
        String selection =
                WonderPublishDBContract.EPUB_ID_COL + " =?";
        String[] args = new String[1];
        args[0] = idString;
        Cursor cursor = database.query(EPUB_HTML_LINK_TABLE_NAME, null, selection, args, null, null, null);
        if (cursor.getCount() == 0) {
            values.put(WonderPublishDBContract.EPUB_ID_COL, idString);
            long newRowId = database.insert(EPUB_HTML_LINK_TABLE_NAME, null, values);
        } else {
            String searchStr =
                    WonderPublishDBContract.EPUB_ID_COL + " = \"" + idString + "\"";
            database.update(EPUB_HTML_LINK_TABLE_NAME, values, searchStr, null);
        }
        cursor.close();
    }


    public List<JSONObject> getAnnotationActionByResId(String resId) {
        List<JSONObject> jsonList = new ArrayList<>();
        try {
            String selection =
                    ANNOTATION_RES_ID + " =? ";
            String[] args = new String[1];
            args[0] = resId;
            if (!database.isOpen())
                open();
            Cursor cursor = database.query(
                    ANNOTATION_ACTION_TABLE_NAME, WonderPublishDBContract.annotationActionAllColumns,
                    selection, args, null, null, null);
            if (cursor != null)
                cursor.moveToFirst();
            while (!cursor.isAfterLast()) {
                JSONObject annotationJson = new JSONObject(cursor.getString(2));
                annotationJson.put("id", cursor.getString(1));
                jsonList.add(annotationJson);
                cursor.moveToNext();
            }
            cursor.close();
        } catch (JSONException e) {
            Log.e("WonderDataSource", e.getMessage());
        }
        return jsonList;
    }

    public String getNotesAnnotationByResId(String resId) {
        String selection =
                RES_ID + " =? ";
        String[] args = new String[1];
        args[0] = resId;
        if (!database.isOpen())
            open();
        Cursor cursor = database.query(
                NOTES_ANNOTATION_TABLE_NAME, WonderPublishDBContract.notesAllColumns,
                selection, args, null, null, null);
        if (cursor.getCount() == 0) {
            cursor.close();
            return null;
        }
        cursor.moveToFirst();
        String returnString = cursor.getString(2);
        cursor.close();
        return returnString;
    }

    public void deleteNotesByResId(String resId) {
        String selection =
                RES_ID + " =?";
        String[] args = new String[1];
        args[0] = resId;
        database.delete(NOTES_ANNOTATION_TABLE_NAME, selection, args);

    }


    public void deleteBookInUserDatabase(String bookId) {
        final String searchStr =
                WonderPublishDBContract.USER_BOOK_ID_COL + " =?";
        String[] args = new String[1];
        args[0] = bookId;
        database.delete(USER_BOOK_TABLE, searchStr, args);
    }


    public void deleteAllUserBookEntries() {
        database.delete(USER_BOOK_TABLE, null, null);
    }

    private void deleteAllUserNotesAndAnnotation() {
        database.delete(NOTES_ANNOTATION_TABLE_NAME, null, null);
    }

    private void deleteAllGeneralTablesEntry() {
        database.delete(GENERAL_DATA_TABLE, null, null);
    }

    private void deleteAllUserAnnotationActions() {
        database.delete(ANNOTATION_ACTION_TABLE_NAME, null, null);
    }

    public void deleteAllBookEntries() {
        database.delete(BOOK_TABLE, null, null);
    }


    public void deleteAllChapterEntries() {
        database.delete(CHAPTER_TABLE, null, null);
    }

    public boolean isDBOpen() {
        return database.isOpen();
    }

    public void deleteAllQuizEntries() {
        database.delete(QUIZ_TABLE, null, null);
    }

    /*
    public long addChallengeToDatabase(WonderChallenge challenge) {
        ContentValues values =
                getChallengeTableCVFromProjection(challenge, WonderPublishDBContract.challengeAllColumns);

        // Inserts the row into the table and returns the new row'chapterSelSpinner _id value
        long id = database.insert(CHALLENGE_TABLE, null, values);
        // If the insert succeeded return the new row id else throw an exception.
        if (-1 == id) {
//            throw new SQLiteException("Insert error: " + CHALLENGE_TABLE);
        }
        return id;
    }*/

    /*
    public void updateChallengeInDatabase(WonderChallenge challenge, String[] projection) {
        final String searchStr =
                WonderPublishDBContract.QC_ID_COL + " = " + challenge.getID();
        ContentValues values = getChallengeTableCVFromProjection(challenge, projection);
        database.update(CHALLENGE_TABLE, values, searchStr, null);
    }*/

    /*
    private ContentValues getChallengeTableCVFromProjection(
            WonderChallenge challenge, String[] projection) {

        String question = challenge.getQuestion();//+"<p>$"+challenge.getID()+"</p>";
        // String question= challenge.getQuestion()+"<html><head><style type='text/css'>html,body {margin: 0;padding: 0;width: 100%;height: 100%;}html {display: table;}body {display: table-cell;vertical-align: middle;text-align: left|center;font-size:110%;}</style></head><body><p style=\"color:green\">" + challenge.getID() + "</p></body></html>";

        ContentValues values = new ContentValues();
        for (String colName : projection) {
            switch (colName) {
                case WonderPublishDBContract.QC_ID_COL:
                    values.put(colName, challenge.getID());
                    break;
                case WonderPublishDBContract.QC_QUIZ_ID_COL:
                    values.put(colName, challenge.getQuizId());
                    break;
                case WonderPublishDBContract.QC_JSON_RESPONSE_COL:
                    values.put(colName, challenge.getQuizJsonString());
                /*
                case WonderPublishDBContract.QC_QUESTION_COL:
                    values.put(colName, question);
                    //SS......................................................
                    break;
                case WonderPublishDBContract.QC_DIRECTIONS_COL:
                    values.put(colName, challenge.getDirectionText());
                    break;
                case WonderPublishDBContract.QC_SECTION_COL:
                    values.put(colName, challenge.getSectionText());
                    break;
                case WonderPublishDBContract.QC_ANSWER_DESCRIPTION_COL:
                    values.put(colName, challenge.getAnswerDescription());
                    break;
                case WonderPublishDBContract.QC_ANSWER_NUM_COL:
                    values.put(colName, challenge.getAnsNum());
                    break;
                case WonderPublishDBContract.QC_ANSWERS_COL:
                    String[] answers = challenge.getAnswersArray();
                    if (answers.length > 0) {
                        JSONObject json = new JSONObject();
                        Utils.addArrayToJson(json, WonderChallenge.DB_ANSWER_KEY_PREFIX, answers);
                        values.put(colName, json.toString());
                    }
                    break;
                case WonderPublishDBContract.QC_CHOICES_COL:
                    String[] choices = challenge.getChoices();
                    if (choices.length > 0) {
                        JSONObject json = new JSONObject();
                        Utils.addArrayToJson(json, WonderChallenge.DB_CHOICES_KEY_PREFIX, choices);
                        values.put(colName, json.toString());
                    }
                    break;
                case WonderPublishDBContract.QC_FILE_NAME_COL:
                    values.put(colName, challenge.getFileName());
                    break;
                case WonderPublishDBContract.QC_FILE_OPS_COL:
                    String[] fileOps = challenge.getFileOps();
                    if (fileOps.length > 0) {
                        JSONObject json = new JSONObject();
                        Utils.addArrayToJson(json, WonderChallenge.DB_FILEOPS_KEY_PREFIX, fileOps);
                        values.put(colName, json.toString());
                    }
                    break;*/
    /*
            }
        }
        return values;
    }*/

    /*
    public WonderChallenge findChallengeByID(String challengeId) {
        final String searchStr =
                WonderPublishDBContract.QC_ID_COL + " = " + challengeId;
        Cursor cursor = database.query(
                CHALLENGE_TABLE, WonderPublishDBContract.challengeAllColumns,
                searchStr, null, null, null, null);
        if (cursor.getCount() == 0) {
            return null;
        }
        cursor.moveToFirst();
        WonderChallenge challenge = cursorToQuizChallenge(cursor);
        return challenge;
    }

    public List<WonderChallenge> getAllChallengeEntries() {
        List<WonderChallenge> challengeList = new ArrayList<>();
        Cursor cursor = database.query(CHALLENGE_TABLE, WonderPublishDBContract.challengeAllColumns,
                null, null, null, null, null);
        cursor.moveToFirst();
        WonderChallenge challenge;
        while (!cursor.isAfterLast()) {
            challenge = cursorToQuizChallenge(cursor);
            challengeList.add(challenge);
            cursor.moveToNext();
        }
        return challengeList;
    }*/

    /*
    public List<WonderChallenge> getAllQuizChallenges(String quizId) {
        final String searchStr =
                WonderPublishDBContract.QC_QUIZ_ID_COL + " = " + quizId;

        List<WonderChallenge> challengeList = new ArrayList<>();
        Cursor cursor = database.query(CHALLENGE_TABLE, WonderPublishDBContract.challengeAllColumns,
                searchStr, null, null, null, null);
        cursor.moveToFirst();
        WonderChallenge challenge;
        while (!cursor.isAfterLast()) {
            challenge = cursorToQuizChallenge(cursor);
            challengeList.add(challenge);
            cursor.moveToNext();
        }
        return challengeList;
    }

    public int deleteQuizChallengeEntries(WonderQuiz quiz) {
        return database.delete(CHALLENGE_TABLE,
                WonderPublishDBContract.QC_QUIZ_ID_COL + " = " + quiz.getID(), null);
    }*/

    /*
    public void deleteAllChallengesentries() {
        database.delete(CHALLENGE_TABLE, null, null);
    }*/

    public void deleteAllRefLinks() {
        database.delete(REF_LINK_TABLE, null, null);
    }

    public void deleteUserTables() {
        deleteAllChapterEntries();
        deleteAllQuizEntries();
        deleteAllRefLinks();
        deleteAllEpubHtmlLinkEntries();
        deleteAllQuizAnswersEntries();
        deleteUserNameEntry();
        deleteAllUserBookEntries();
        deleteAllUserAnnotationActions();
        deleteAllUserNotesAndAnnotation();
        deleteAllUsageHistoryEntries();

        //Not clearing all entries in General table because
        // we need to retain Notifications after logout and login

        //deleteAllGeneralTablesEntry();

        //We need to clear user books data from General table to avoid data ambiguity
        removeJSONData(DATA_UPDATE_USER_LIBRARY_BOOKS);

    }

    public void deleteAllEpubHtmlLinkEntries() {
        database.delete(EPUB_HTML_LINK_TABLE_NAME, null, null);
    }

    public void deleteAllQuizAnswersEntries() {
        database.delete(WonderPublishDBContract.UPDATE_WITH_QUIZ_ANSWER_TABLE_NAME, null, null);
    }

    public void deleteUserNameEntry() {
        database.delete(CHECKING_USERNAME_TABLE_NAME, null, null);
    }


    private boolean isMasterEmpty() {
        String quString = "select exists(select 1 from " + CHECKING_USERNAME_TABLE_NAME + ");";
        Cursor cursor = database.rawQuery(quString, null);
        cursor.moveToFirst();
        boolean flag = !(cursor.getInt(0) == 1);
        cursor.close();

        return flag;
    }

    public void deleteAllUsageHistoryEntries() {
        database.delete(USAGE_HISTORY_TABLE_NAME, null, null);
    }
}
