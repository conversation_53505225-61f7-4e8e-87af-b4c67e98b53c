package com.wonderslate.data.helper

import android.util.Log
import com.wonderslate.data.interfaces.WSCallback
import com.wonderslate.data.network.ApplicationMode
import com.wonderslate.data.network.OpenUtils
import com.wonderslate.data.network.ServerTimeCallBack
import com.wonderslate.data.network.Wonderslate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import org.json.JSONObject
import java.util.*

class ServerTimeHelper private constructor() {

    /**
     * States if we got time from API successfully or not.
     *
     * TRUE: Successfully got time from API.
     * FALSE: API call failed or got invalid response.
     */
    private var isServerTimeAvailable = false

    /**
     * States if app was backgrounded (paused or user has switched the app) or not.
     *
     * If app was in background then update the local timer upon next
     * usage (i.e if time is required then update the local timer and then use it).
     *
     * TRUE: User might have switched the app or locked the phone previously (decided by AppLifecycleObserver).
     * FALSE: App was not backgrounded since last update.
     */
    private var appBackgrounded = false

    /**
     * Task which will add 1 second at a fixed interval in the
     * @see serverTime
     */
    private var task: TimerTask? = null

    /**
     * Timer which will execute
     * @see task
     * at the interval of 1 second
     */
    private var timer: Timer? = null

    private val listOfCallBacks = mutableListOf<ServerTimeCallBack>()

    private var callBack: ServerTimeCallBack? = null

    /**
     * Used to get current server time
     *
     * @param serverTimeCallBack for getting current time or error message.
     */
    @Synchronized
    fun getServerTime(serverTimeCallBack: ServerTimeCallBack?) {


        callBack = serverTimeCallBack
        /*serverTimeCallBack?.let {
            listOfCallBacks.add(it)
        }*/

        //Check if local current time is present and app was not
        // backgrounded earlier then use the local time
        if (currentServerTime != null && !appBackgrounded) {
           success(currentServerTime!!)

        }
        //Check if app is in Offline mode then don't make API call
        else if (Wonderslate.getInstance().appMode === ApplicationMode.OFFLINE) {
            failed("App is in offline mode", -2)
        }
        //Check if no API call is in progress then call API
        else if (!fetchingServerTime) {
            appBackgrounded = false
            getEUServerTime(object:
                ServerTimeCallBack {
                override fun onSuccess(time: LocalDateTime) {
                    success(time)
                }

                override fun onFailed(resString: String?, responseCode: Int) {

                    failed(resString ?: "", responseCode)
                }

            })
        }
        /*//If no conditions are satisfied then call onFail
        else {
            Log.d(TAG, "getServerTime: Not able to get time.")
            serverTimeCallBack?.onFailed("Please wait and try again after some time.", -2)
        }*/
    }

    private fun success(time: LocalDateTime) {
        callBack?.onSuccess(time)
    }

    private fun failed(msg: String, responseCode: Int) {
        callBack?.onFailed(msg, responseCode)
    }

    /**
     * Used to update internal time with the server time
     *
     * IMPORTANT: This method should only to be called when app comes to foreground from background
     */
    @Synchronized
    fun updateTime() {
        appBackgrounded = true
    }

    /**
     * Use this function for fetching server time from API
     */
    private fun getEUServerTime(serverTimeCallBack: ServerTimeCallBack?) {
        fetchingServerTime = true
        Log.d(TAG, "getServerTime: Updating time from Server")
        OpenUtils.getEUCurrentTime(object : WSCallback {
            override fun onWSResultSuccess(jsonObject: JSONObject, responseCode: Int) {
                serverTime = stringToDate(jsonObject.optString("serverTime"))
                appBackgrounded = false
                fetchingServerTime = false
                serverTime?.let {
                    Log.d(TAG, "getServerTime: Updated time from Server - $serverTime")
                    startTimer()
                    isServerTimeAvailable = true
                    serverTimeCallBack?.onSuccess(it)
                } ?: let {
                    Log.d(TAG, "getServerTime: Wrong time format.")
                    serverTime = null
                    isServerTimeAvailable = false
                    serverTimeCallBack?.onFailed("Failed while getting time", responseCode)
                }
            }

            override fun onWSResultFailed(resString: String, responseCode: Int) {
                Log.d(TAG, "getServerTime: Failed while getting time from server.")
                serverTime = null
                isServerTimeAvailable = false
                fetchingServerTime = false
                serverTimeCallBack?.onFailed(resString, responseCode)
            }
        })
    }

    /**
     * NOTE: Only use in Adapters
     *
     * To get current server time
     */
    val currentServerTime: LocalDateTime?
        get() = if (isServerTimeAvailable && serverTime != null) {
            serverTime
        } else {
            null
        }

    /**
     * Used to start process of
     * @see timer
     *
     * Once timer is started it keeps adding 1 second at the interval of 1 second
     * to maintain local server time.
     */
    private fun startTimer() {
        cancelTimer()
        task = object : TimerTask() {
            override fun run() {
                serverTime = if (serverTime != null) {
                    val copy = serverTime
                    copy!!.plusSeconds(1)
                } else {
                    currentServerTime
                }
            }
        }
        timer = Timer()
        timer?.scheduleAtFixedRate(task, 1000, 1000)
    }

    /**
     * Used to cancel the process of
     * @see timer
     */
    fun cancelTimer() {
        try {
            task = null
            timer?.cancel()
            timer = null
        } catch (ignored: NullPointerException) {
        }
    }

    companion object {

        private const val TAG = "ServerTimeHelper"

        /**
         * Local server time which is updated every second using a timer
         */
        @Volatile
        private var serverTime: LocalDateTime? = null

        /**
         * States if any API call is in progress
         *
         * TRUE: fetching server time from API.
         * FALSE: currently not fetching anything.
         */
        private var fetchingServerTime = false

        /**
         * Used for getting a NEW instance of ServerTimeHelper.
         *
         * @see Singletons.serverTimeHelper for using the singleton version of this class.
         */
        @JvmStatic
        val newInstance: ServerTimeHelper by lazy { ServerTimeHelper() }

        /**
         * For getting current device time based on "Asia/kolkata" timezone.
         */
        @JvmStatic
        fun currentDeviceTime(): LocalDateTime {
            val kolkataZone = ZoneId.of("Asia/Kolkata")
            return LocalDateTime.now(kolkataZone)
        }

        /**
         * For getting parsed LocalDateTime object from string.
         */
        @JvmStatic
        fun stringToDate(dateString: String?): LocalDateTime {
            var dateStr = dateString
            return if (dateStr != null && !dateStr.isEmpty()) {
                try {
                    val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")
                    LocalDateTime.parse(dateStr, formatter)
                } catch (ex: Exception) {
                    try {
                        // Handle UTC format with Z
                        if (dateStr.endsWith("Z")) {
                            dateStr = dateStr.replace("Z", "")
                            val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")
                            val utcDateTime = LocalDateTime.parse(dateStr, formatter)
                            // Convert UTC to Asia/Kolkata (+5:30)
                            utcDateTime.plusHours(5).plusMinutes(30)
                        } else {
                            // Try other common formats
                            val formatters = listOf(
                                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S"),
                                DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"),
                                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                            )

                            dateStr = dateStr.replace(".000", "").replace("T", " ")

                            var result: LocalDateTime? = null
                            for (formatter in formatters) {
                                try {
                                    result = LocalDateTime.parse(dateStr, formatter)
                                    break
                                } catch (e: Exception) {
                                    // Continue to next formatter
                                }
                            }
                            result ?: LocalDateTime.now(ZoneId.of("Asia/Kolkata"))
                        }
                    } catch (e: Exception) {
                        LocalDateTime.now(ZoneId.of("Asia/Kolkata"))
                    }
                }
            } else {
                LocalDateTime.now(ZoneId.of("Asia/Kolkata"))
            }
        }
    }
}