package com.wonderslate.data.request

import android.util.Log
import com.android.volley.Request
import com.android.volley.VolleyError
import com.android.volley.toolbox.JsonObjectRequest
import org.json.JSONObject
import java.util.*
import kotlin.coroutines.Continuation
import kotlin.coroutines.resumeWithException
import com.wonderslate.commons.Result
import com.wonderslate.data.network.Wonderslate

class JSONObjectRequestWithParameters(
        url:String,
        continuation: Continuation<Result<JSONObject>>,
        request:JSONObject):
        JsonObjectRequest(Request.Method.POST,url,request,

            { response: JSONObject? ->
                Log.e("Login_Service", response!!.toString())
//                        val responseObject = GsonBuilder().create().fromJson(response, StatusResponse::class.java)
                continuation.resumeWith(kotlin.Result.success(Result.Success(response) ))
            }, { error: VolleyError ->
            Log.d("Login_Service", error!!.toString())

            try {
                if (error.networkResponse.statusCode == 401) {
                    continuation.resumeWithException(Exception(error.networkResponse.statusCode.toString()))
                } else {
                    continuation.resumeWithException(Exception(error.message))
                }
            } catch (e : Exception) {
                continuation.resumeWithException(Exception(error.message))
            }
               // continuation.resumeWithException(Exception(error.message))
            }
            ){

            override fun getBodyContentType(): String {
                return "application/json"
            }

            override fun getHeaders(): MutableMap<String, String> {
                val headers: MutableMap<String, String> = HashMap()
                headers["Accept"] = "application/json"
                headers["Accept-Charset"] = "utf-8"
                headers["Content-Type"] = "application/json"
               // headers["X-Auth-Token"] = "a93b9918-eb33-4190-a537-89e717437f91"
                var token = ""
                Wonderslate.getInstance()?.let { wonderslate ->
                    wonderslate.sharedPrefs.accessToken?.let {
                        token = it
                    }
                }
                if(token != "nil")
                    headers["X-Auth-Token"] = token

                Log.e("token",":"+token)

                return headers
            }

        }