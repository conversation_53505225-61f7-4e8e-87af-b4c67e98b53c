package com.wonderslate.data.network;

import android.content.Context;

import androidx.lifecycle.Lifecycle;

import com.wonderslate.data.R;
import com.wonderslate.data.preferences.WonderPubSharedPrefs;


public final class Wonderslate {

    private static Wonderslate mInstance;
    private Context mContext;
    private String mSiteID = "";
    private String currentAffairsType = "";
    private WonderPubSharedPrefs sharedPrefs = null;
    public static final int RESULT_STATUS_FAILED = -2;
    public static final int RESULT_STATUS_SUCCESS = 0;
    public static final String BROADCAST_ACTION_DOWNLOAD_FILE = "fileDownload";
    private static ApplicationMode appMode = ApplicationMode.UNDEFINED;

    public static String SERVICE;
    public static String SERVICE1;
    public static String SERVICE2;
    public static String SERVICE3;
    public static String SERVICE4;
    public static String SERVICE5;
    public static String SERVICE6;
    public static String CHATSERVICE;
    public static String CONTROL_SERVICE;
    public static String UTKARSH_SERVICE;

    public static Servers currentServer;

    public enum Servers {
        QA, STAGING, PUBLISH, LIVE, DIRECT,DEV
    }


    public static Lifecycle.Event APP_STATE;
    public static Class aClass;
    public static final Integer LIBRARY_TYPE_STORE = 1;

    private Wonderslate(Context mContext) {
        this.mContext = mContext;
    }

    public static synchronized void init(Context context, String siteId) {
        if (mInstance == null) {
            mInstance = new Wonderslate(context);
            mInstance.setSiteId(siteId);
            mInstance.setContext(context);
        }
    }

    public void setService(Servers server) {
        //Select Base Service URL According To Server.
        currentServer = server; //Use QA, STAGING, PUBLISH, LIVE

        switch (currentServer){
            case QA:
                SERVICE = SERVICE1 = SERVICE2 = SERVICE3 = SERVICE4 = SERVICE5 = SERVICE6 = mContext.getResources().getString(R.string.utkarsh_service_qa);
                break;
            case DEV:
                SERVICE = SERVICE1 = SERVICE2 = SERVICE3 = SERVICE4 = SERVICE5 = SERVICE6 = mContext.getResources().getString(R.string.utkarsh_service_dev);
                break;
            case STAGING:
                SERVICE = SERVICE1 = SERVICE2 = SERVICE3 = SERVICE4 = SERVICE5 = SERVICE6 = mContext.getResources().getString(R.string.utkarsh_service_staging);
                break;
            case PUBLISH:
                SERVICE = SERVICE1 = SERVICE2 = SERVICE3 = SERVICE4 = SERVICE5 = SERVICE6 = mContext.getResources().getString(R.string.utkarsh_service_publish);
                break;
            case LIVE:
                if (mInstance.mSiteID.equalsIgnoreCase("52")) {
                    SERVICE = mContext.getResources().getString(R.string.service_yct_live);
                    SERVICE1 = mContext.getResources().getString(R.string.service_yct_live);
                    SERVICE2 = mContext.getResources().getString(R.string.service_yct_live);
                    SERVICE3 = mContext.getResources().getString(R.string.service_yct_live);
                    SERVICE4 = mContext.getResources().getString(R.string.service_yct_live);
                    SERVICE5 = mContext.getResources().getString(R.string.service_yct_live);
                    SERVICE6 = mContext.getResources().getString(R.string.service_yct_live);
                }
                else if (mInstance.mSiteID.equalsIgnoreCase("57")) {
                    SERVICE = mContext.getResources().getString(R.string.service_prabhat_live);
                    SERVICE1 = mContext.getResources().getString(R.string.service_prabhat_live);
                    SERVICE2 = mContext.getResources().getString(R.string.service_prabhat_live);
                    SERVICE3 = mContext.getResources().getString(R.string.service_prabhat_live);
                    SERVICE4 = mContext.getResources().getString(R.string.service_prabhat_live);
                    SERVICE5 = mContext.getResources().getString(R.string.service_prabhat_live);
                    SERVICE6 = mContext.getResources().getString(R.string.service_prabhat_live);
                }
                else if (mInstance.mSiteID.equalsIgnoreCase("71")) {
                    /*SERVICE = mContext.getResources().getString(R.string.service_kiran_live);
                    SERVICE1 = mContext.getResources().getString(R.string.service_kiran_live);
                    SERVICE2 = mContext.getResources().getString(R.string.service_kiran_live);
                    SERVICE3 = mContext.getResources().getString(R.string.service_kiran_live);
                    SERVICE4 = mContext.getResources().getString(R.string.service_kiran_live);
                    SERVICE5 = mContext.getResources().getString(R.string.service_kiran_live);
                    SERVICE6 = mContext.getResources().getString(R.string.service_kiran_live);*/

                    SERVICE = mContext.getResources().getString(R.string.service_kiran_live);
                    SERVICE1 = mContext.getResources().getString(R.string.service_kiran_live);
                    SERVICE2 = mContext.getResources().getString(R.string.service_kiran_live);
                    SERVICE3 = mContext.getResources().getString(R.string.service_kiran_live);
                    SERVICE4 = mContext.getResources().getString(R.string.service_kiran_live);
                    SERVICE5 = mContext.getResources().getString(R.string.service_kiran_live);
                    SERVICE6 = mContext.getResources().getString(R.string.service_kiran_live);
                }
                else if (mInstance.mSiteID.equalsIgnoreCase("75")) {
                    SERVICE = mContext.getResources().getString(R.string.service_spardha_live);
                    SERVICE1 = mContext.getResources().getString(R.string.service_spardha_live);
                    SERVICE2 = mContext.getResources().getString(R.string.service_spardha_live);
                    SERVICE3 = mContext.getResources().getString(R.string.service_spardha_live);
                    SERVICE4 = mContext.getResources().getString(R.string.service_spardha_live);
                    SERVICE5 = mContext.getResources().getString(R.string.service_spardha_live);
                    SERVICE6 = mContext.getResources().getString(R.string.service_spardha_live);
                } else if (mInstance.mSiteID.equalsIgnoreCase("27")) {
                    SERVICE = mContext.getResources().getString(R.string.service_prepjoy_live);
                    SERVICE1 = mContext.getResources().getString(R.string.service_prepjoy_live);
                    SERVICE2 = mContext.getResources().getString(R.string.service_prepjoy_live);
                    SERVICE3 = mContext.getResources().getString(R.string.service_prepjoy_live);
                    SERVICE4 = mContext.getResources().getString(R.string.service_prepjoy_live);
                    SERVICE5 = mContext.getResources().getString(R.string.service_prepjoy_live);
                    SERVICE6 = mContext.getResources().getString(R.string.service_prepjoy_live);
                } else {
                    SERVICE = mContext.getResources().getString(R.string.service_live);
                    SERVICE1 = mContext.getResources().getString(R.string.service_live);
                    SERVICE2 = mContext.getResources().getString(R.string.service_live);
                    SERVICE3 = mContext.getResources().getString(R.string.service_live);
                    SERVICE4 = mContext.getResources().getString(R.string.service_live);
                    SERVICE5 = mContext.getResources().getString(R.string.service_live);
                    SERVICE6 = mContext.getResources().getString(R.string.service_live);
                }
                break;
            case DIRECT: SERVICE = SERVICE1 = SERVICE2 = SERVICE3 =
                    SERVICE4 = SERVICE5 = SERVICE6 = mContext.getResources().getString(R.string.service_prepjoy_live);
                break;
        }
        CHATSERVICE = SERVICE4;
        CONTROL_SERVICE = mContext.getResources().getString(R.string.service_prepjoy_live);
    }

    private void setSiteId(String siteId) {
        mSiteID = siteId;
    }

    private void setContext(Context context) {
        mContext = context;
    }

    public static Wonderslate getInstance() {
        return mInstance;
    }

    public Context getContext() {
        return mContext;
    }

    public String getSiteID() {
        return mSiteID;
    }

    public void setCurrentAffairsType(String currentAffairsType) {
        this.currentAffairsType = currentAffairsType;
    }

    public String getCurrentAffairsType() {
        return currentAffairsType;
    }

    public WonderPubSharedPrefs getSharedPrefs() {
        if (this.sharedPrefs == null) {
            this.sharedPrefs = WonderPubSharedPrefs.getInstance(mContext);
        }
        return this.sharedPrefs;
    }

    public void setAppMode(ApplicationMode mode) {
        appMode = mode;
    }

    public ApplicationMode getAppMode() {
        return appMode;
    }
}
