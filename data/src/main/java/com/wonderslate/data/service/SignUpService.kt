package com.wonderslate.data.service

import android.content.Context
import com.wonderslate.commons.Result
import com.wonderslate.data.network.VolleyHelper
import com.wonderslate.data.request.JSONObjectRequestWithParamsWithoutHeaders
import kotlinx.coroutines.suspendCancellableCoroutine
import org.json.JSONObject

class SignUpService(private val context: Context) {
    suspend fun signup(requestBody: JSONObject, url: String) : Result<JSONObject> {
        return suspendCancellableCoroutine { continuation ->
            run {
                val queue = VolleyHelper.getInstance(context).requestQueue
                val stringRequest = JSONObjectRequestWithParamsWithoutHeaders(url, continuation, requestBody)
                queue.add(stringRequest)
            }
        }
    }
}