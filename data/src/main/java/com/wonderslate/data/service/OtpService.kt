package com.wonderslate.data.service

import android.content.Context
import com.wonderslate.commons.Result
import com.wonderslate.data.network.VolleyHelper
import com.wonderslate.data.request.JSONObjectRequestWithParamsWithoutHeaders
import kotlinx.coroutines.suspendCancellableCoroutine
import org.json.JSONObject

class OtpService (private val context : Context) {
    companion object {
        //  private const val TAG = "LoginService"
    }

    suspend fun sendOtp(requestBody: JSONObject, url: String) : Result<JSONObject> {
        return suspendCancellableCoroutine { continuation ->
            run {
                val queue = VolleyHelper.getInstance(context).requestQueue
                val stringRequest = JSONObjectRequestWithParamsWithoutHeaders(url, continuation, requestBody)
                queue.add(stringRequest)
            }
        }
    }

    suspend fun submitOpt(requestBody: JSONObject, url: String) : Result<JSONObject> {
        return suspendCancellableCoroutine { continuation ->
            run {
                val queue = VolleyHelper.getInstance(context).requestQueue
                val stringRequest = JSONObjectRequestWithParamsWithoutHeaders(url, continuation, requestBody)
                queue.add(stringRequest)
            }
        }
    }
}