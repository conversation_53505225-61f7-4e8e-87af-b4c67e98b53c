package com.wonderslate.data.preferences;

import android.content.Context;
import android.content.SharedPreferences;
import android.net.Uri;
import android.util.Log;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.wonderslate.data.db.WonderPublishDBContract;
import com.wonderslate.data.db.WonderPublishDataSource;
import com.wonderslate.data.helper.ConstantsHelper;
import com.wonderslate.data.network.Encryption;
import com.wonderslate.data.network.Wonderslate;

import net.danlew.android.joda.BuildConfig;

import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.jsoup.helper.StringUtil;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * This is a singleton class manages all application specific preferences data.
 */
public class WonderPubSharedPrefs {
    private static final String TAG = "WPSharedPrefs";

    private static final String SHARED_PREFS_NAME = "WonderPublishSP";
    private static final String SHARED_PREFS_USERNAME = "UserName";
    private static final String SHARED_PREFS_USERIMAGE = "UserImage";
    private static final String SHARED_PREFS_USERID = "UserId";
    private static final String SHARED_PREFS_USER_MIGRATION = "userMigration";
    private static final String SHARED_PREFS_USEREMAIL = "UserEmail";
    private static final String SHARED_PREFS_USER_STATE = "UserState";
    private static final String SHARED_PREFS_USER_DISTRICT = "UserDistrict";
    private static final String SHARED_PREFS_TOKEN_DATE = "tokenStoredDate";
    private static final String SHARED_PREFS_USER_PRIVILEGES = "userPrivileges";
    private static final String SHARED_PREFS_HSHD = "hshd";
    private static final String SHARED_PREFS_MIGRATION_PASSWORD_SET = "migrationPasswordSet";
    private static final String SHARED_PREFS_USERMOBILE = "UserMobile";
    private static final String SHARED_PREFS_LOGINTYPE = "loginType";
    private static final String SHARED_PREFS_USERTYPE = "userType";
    private static final String SHARED_PREFS_ACCEPTED_TOU = "acceptedTOUVersion";
    private static final String SHARED_PREFS_LATEST_TOU = "latestTOUVersion";
    private static final String SHARED_PREFS_GOOGLE_ACCOUNT_NAME = "googleAccountName";
    private static final String SHARED_PREFS_USER_CHAPTERS = "userChapters";
    private static final String SHARED_PREFS_USER_CHAPTERS_DETAILS = "userChaptersDetails";
    private static final String SHARED_PREFS_USER_LAST_READ_CHAPTER = "userLastReadChapter";
    private static final String SHARED_PREFS_USER_LAST_READ_SECTION = "userLastReadSection";
    private static final String SHARED_PREFS_USER_LAST_READ_BOOK = "userLastReadBook";
    private static final String SHARED_PREFS_USER_LAST_READ_BOOKS = "userLastReadBooks";
    private static final String SHARED_PREFS_USER_LAST_READ_CHAPTER_OBJECT = "userLastReadChapterObject";
    private static final String SHARED_PREFS_USER_LAST_READ_TOPIC_ID = "userLastReadTopicId";
    private static final String SHARED_PREFS_USER_LAST_READ_RES_LINK = "userLastReadResLink";
    private static final String SHARED_PREFS_USER_LAST_READ_RES_NAME = "userLastReadResName";
    private static final String SHARED_PREFS_USER_PERM_DENIED_STATUS = "userPermStatus";
    private static final String SHARED_PREFS_USER_LANGUAGE_PREFERENCE = "userLangPref";
    private static final String SHARED_PREFS_APP_VERSION = "appVersion";
    private static final String SHARED_PLAYSTORE_VERSION = "playStoreVersion";

    private static final String SHARED_PREFS_PERM_STORAGE = "userPermStorage";
    private static final String SHARED_PREFS_SEEN_RESOURCES = "seenResources";
    private static final String SHARED_PREFS_PERM_SMS = "userPermSMS";
    private static final String SHARED_PREFS_USERE_LOCATION = "UserLocation";
    private static final String SHARED_PREFS_SCREEN_RESUME = "ResumeScreen";
    private static final String SHARED_PREFS_ACCESS_TOKEN = "AccessToken";
    private static final String SHARED_PREFS_DEVICE_REGISTRATION = "DeviceRegistration";
    private static final String SHARED_PREFS_TOPIC_SUBSCRIPTION = "TopicSubscription";
    private static String SHARED_PREFS_RES_ID;
    private static final String SHARED_PREFS_SERVICE_SITE_ID = "SiteId";
    private static final String SHARED_PREFS_DATA_GROUP = "DataGroup";
    private static final String SHARED_PREFS_DATA = "Data";
    private static final String SHARED_PREFS_MYPREF = "MyPref";
    private static final String SHARED_PREFS_BOOK_HISTORY = "bookHistory";
    private static final String SHARED_PREFS_BOOK_HISTORY_VALID = "bookHistoryValid";
    private static final String SHARED_PREFS_HISTORY_PREFIX = "histPerfs";
    private static final String SHARED_PREFS_KEYS = "keys";
    private static final String FEATURE_DISCOVERY = "featureDiscovery";
    private static final String SHARED_PREFS_FIRSTRUN = "firstrun";
    private static final String SHARED_PREFS_LOG_USER = "userLog";
    private static final String SHARED_PREFS_PENDING_API_REQUESTS = "pendingAPIRequests";
    private static final String SHARED_PREFS_DEVICE_ID = "deviceID";
    private static final String SHARED_PREFS_DEEP_LINK = "deepLink";
    private static final String SHARED_PREFS_NOTIF_INTENT = "notifIntent";
    private static final String SHARED_PREFS_NOTIF_ID = "notifId";
    private static final String SHARED_PREFS_VIDEO_RESUME_POSITION = "video_resume_position";
    private static final String SHARED_RECORDED_VIDEO_AS_LIVE_TIME_ELAPSED = "recorded_video_time_elapsed";
    private static final String SHARED_PREFS_USER_PREFERRED_VIDEO_QUALITY = "user_preferred_video_quality";
    private static final String SHARED_PREFS_CURRENT_AUDIO_TRACK = "current_audio_track";
    private static final String SHARED_PREFS_CURRENT_AUDIO_PLAYBACK_SPEED = "current_audio_speed";
    private static final String SHARED_PREFS_USER_SELECTED_COURSE = "user_selected_course";
    private static final String SHARED_PREFS_ALL_COURSE = "all_course";
    private static final String SHARED_PREFS_ALL_COURSE_ARRAY = "all_course_array";
    private static final String SHARED_PREFS_WEEKLY_WINNERS = "weekly_winners";
    private static final String SHARED_PREFS_WEEKLY_WINNERS_VISIBILITY = "weekly_winners_visibility";
    private static final String SHARED_PREFS_FEATURED_COURSE = "featured_course";
    private static final String SHARED_PREFS_USER_COURSE_IDS = "user_course_ids";
    private static final String SHARED_PREFS_ALL_GRADE_IDS = "all_grade_ids";
    private static final String SHARED_PREFS_ALL_GRADE_VALUES = "all_grade_values";
    private static final String SHARED_PREFS_CLASS_MAP = "class_map";
    private static final String SHARED_PREFS_LIVE_VIDEOS = "liveVideos";
    private static final String SHARED_PREFS_SITE_MST = "siteMst";
    private static final String SHARED_PREFS_API_CALL_DATE = "APICallDate";
    private static final String SHARED_PREFS_FILTER_RESPONSE = "filterResponse";
    private static final String SHARED_PREFS_LATEST_RELEASE_BOOKS = "LatestReleaseBooks";
    private static final String SHARED_PREFS_VIDEO_DOWNLOAD_PAUSED_MAP = "videoDownloadPausedMap";
    private static final String SHARED_PREFS_VIDEO_DOWNLOAD_PAUSED_LINK_MAP = "videoDownloadPausedLinkMap";
    private static final String SHARED_PREFS_PURCHASED_BOOK_IDS = "purchasedBookIds";
    private static final String SHARED_PREFS_VIDEO_DOWNLOAD_SIZE_MAP = "videoDownloadSizeMap";
    private static final String SHARED_PREFS_USER_LIBRARY_BOOKS = "userLibraryBooks";
    private static final String SHARED_PREFS_USER_BATCHES = "userBatches";
    private static final String SHARED_PREFS_DOWNLOAD_VIDEO_CHAPTER_OBJECT = "downloadVideoChapterObject";
    private static final String SHARED_PREFS_DOWNLOAD_PERCENTAGE_VIDEO = "downloadVideoPercentage";
    private static final String SHARED_PREFS_DOWNLOAD_VIDEO_FIRST_TIME = "downloadVideoFirstTime";
    private static final String SHARED_PREFS_VIDEO_LENGTH_MAP = "videoFileLength";
    private static final String SHARED_PREFS_QUIZ_INSTRUCTION_JSON = "quizInstructionJSON";
    private static final String SHARED_PREFS_LIVE_TESTS = "liveTests";
    private static final String SHARED_PREFS_SELECTED_LEVEL = "selectedLevel";
    private static final String SHARED_PREFS_SELECTED_SYLLABUS = "selectedSyllabus";
    private static final String SHARED_PREFS_SELECTED_GRADE = "selectedGrade";
    private static final String SHARED_PREFS_SELECTED_GRADEID = "selectedGradeId";
    private static final String SHARED_PREFS_SELECTED_SCHOOL_SYLLABUS = "selectedSchoolSyllabus";
    private static final String SHARED_PREFS_SELECTED_SCHOOL_GRADE = "selectedSchoolGrade";
    private static final String SHARED_PREFS_SELECTED_SCHOOL_GRADEID = "selectedSchoolGradeId";
    private static final String SHARED_PREFS_TEST_GEN_QUIZ_ARRAY = "testGenQuizArray";
    private static final String SHARED_PREFS_TEST_GEN_CHAPTERS_ARRAY = "testGenChaptersArray";
    private static final String SHARED_PREFS_HIDDEN_BOOKS = "hiddenBooks";
    private static final String SHARED_PREFS_DOWNLOADED_VIDEOS = "downloadedVideos";
    private static final String SHARED_PREFS_TEMP_BLOCKED_TIME = "tbt";
    private static final String SHARED_PREFS_SELECTED_BOOK_ID = "selectedBookId";
    private static final String SHARED_PREFS_USER_SELECTED_POLL_ANSWER = "userSelectedPollAnswer";
    private static final String SHARED_PREFS_USER_LIBRARY_LOAD_FAILED = "userLibraryLoadFailed";
    private static final String SHARED_PREFS_LAST_USER_ONLINE = "lastUserOnline";
    private static final String SHARED_PREFS_CLOSE_SHOP_BOOK_DETAIL = "closeShopBookDetail";
    private static final String SHARED_PREFS_IS_USER_LOGGEDIN = "UserLoggedin";
    private static final String SHARED_PREFS_USER_GAME_SOUND = "userGamesound";

    private static final String SHARED_PREFS_TOTAL_POINTS = "total_points";
    private static final String SHARED_PREFS_TOTAL_MEDALS = "total_medals";
    private static final String SHARED_PREFS_BADES = "bades";
    private static final String SAVE_SWIPE_INSTRUCTION = "save_swipe_instruction";

    private static final String SHARED_PREFS_LATEST_DATE = "currentaffairslatestDate";
    private static final String SHARED_PREFS_FIREBASE_TOKEN = "firebase_token";
    private static final String SHARED_PREFS_FIREBASE_NOTIFICATION = "firebase_notification";
    private static final String SHARED_PREFS_FIREBASE_NOTIFICATION_COUNT = "firebase_notification_count";

    private static final String SHARED_PREFS_UPDATE_NOTIFICATIONS = "prefsUpdateNotifications";

    private static final String SHARED_PREFS_DATE_RESPONSE = "dateresponse";
    private static final String SHARED_PREFS_TEST_DATE_RESPONSE = "test_dateresponse";
    private static final String SHARED_PREFS_QUIZ_AB_TEST = "quizABtestValue";
    private static final String SHARED_PREFS_DAILY_TEST_AB_TEST = "dailyTestABtestValue";

    private static final String SHARED_PREFS_FLAVOUR = "FlavourValue";
    private static final String SHARED_PREFS_NEWS_LANG_PREF = "news_lang_pref";
    private static final String SHARED_PREFS_NEWS_SOURCE_PREF = "news_source_pref";
    private static final String SHARED_PREFS_USER_NEWS_PREF = "user_news_pref";
    private static final String SHARED_PREFS_LAST_FILTER_CLASS_SEARCH = "LastClassSearch";



    private static final String SHARED_PREFS_USER_CONTENT_LANGUAGE_PREFERENCE = "userContentLangPref";
    private static final String SHARED_PREFS_USER_QUIZ_DURATION_PREFERENCE = "userQuizTimePref";
    private static final String USER_LAST_USAGE_TIME_STAMP = "lastUsageTimeStamp";
    private static final String USER_SESSION_COUNT = "userSessionCount";

    private static final String SHARED_PREFS_EBOOK_CLICK_FIRST_TIME = "EbookClickFirstTime";
    private static final String SHARED_PREFS_CAFE_CLICK_FIRST_TIME = "CafeClickFirstTime";

    private static final String SHARED_PREFS_CAFE_NOTIFICATION_COUNT = "cafe_count";
    private static final String SHARED_PREFS_LAST_FILTER_GLOBAL_SEARCH = "LastGlobalSearch";
    private static final String SHARED_PREFS_LAST_FILTER_LEVEL_SEARCH = "LastLevelSearch";
    private static final String SHARED_PREFS_QUIZ_FILTER_LEVEL_SEARCH = "QuizLevelSearch";
    private static final String SHARED_PREFS_LAST_FILTER_SYLLABUS_SEARCH = "LastSyllabusSearch";
    private static final String SHARED_PREFS_LAST_FILTER_GRADE_SEARCH = "LastGradeSearch";
    private static final String USER_LAST_QUIZ_RESULTS = "user_last_quiz_results";
    private static final String USER_LAST_QUIZ_DATA = "user_last_quiz_data";
    private static final String SHARED_PREFS_LAST_FILTER_PUBLISHER_SEARCH = "LastPublisherSearch";
    private static final String USER_AFFILIATION_CONTENT = "user_affiliation_content";
    private static final String USER_AFFILIATION_TIME_STAMP = "user_affiliation_time_stamp";





    //For user preferance capture
    private static final String SHARED_PREFS_USER_LEVEL_PREFERNCE = "UserLevelPref";
    private static final String SHARED_PREFS_USER_SYLLABUS_PREFERNCE = "UserSyllabusPref";
    private static final String SHARED_PREFS_USER_GRADE_PREFERNCE = "UserGradePref";
    private static final String SHARED_PREFS_USER_SUBJECT_PREFERNCE = "UserSubjectPref";
    private static final String SHARED_PREFS_USER_SUB_SUBJECT_PREFERNCE = "UserSubSubjectPref";
    private static final String SHARED_PREFS_USER_PREFERNCE = "UserPrefrencevalues";
    private static final String SHARED_PREFS_USER_ACCESS_CODE = "userAccessCode";
    private static final String SHARED_PREFS_USER_CHALLENGER_INFO = "userChallengerInfo";
    private static final String SHARED_PREFS_USER_CHALLENGER_BOOK_ID = "userChallengeBookId";

    private static final String SHARED_PREFS_USER_PREFFERED_DAILY_TEST = "userPrefDailyTest";

    private static final String SHARED_PREFS_IS_TEST_SERIES = "isTestSeries";

    private static final String SHARED_PREFS_BOOK_UPGRADE_PRICE = "bookUpgradePrice";

    private static final String SHARED_PREFS_TEST_SERIES_BOUGHT = "testSeriesBought";

    private static final String SHARED_PREFS_GPT_BOUGHT = "gptBought";

    private static final String SHARED_PREFS_LIBRARY_PAID_TOKEN_COUNT = "libraryPaidTokenCount";

    private static final String SHARED_PREFS_DEFAULT_CATEGORY = "defaultCategory";

    private static final String SHARED_PREFS_USER_SELECTED_PREFS = "userSelectedPrefs";

    private static final String SHARED_PREFS_CART_COUNT = "shoppingCartCount";

    private static final String SHARED_PREFS_REDIRECT_TO_LIBRARY = "redirectToLibrary";

    private static final String SHARED_PREFS_PRINT_BOOK_ADDED_TO_CART = "printBookAdded";
    private static final String SHARED_PREFS_BOOK_IN_LIBRARY = "isBookInLibrary";

    private static final String IS_FIRST_TIME_LAUNCH = "IsFirstTimeLaunch";

    private static final String SHARED_PREFS_LAST_READ_PAGE = "lastReadPage";

    private static final String SHARED_PREFS_QUIZ_DATA = "quizData";

    private Context appContext = null;
    private static WonderPubSharedPrefs wonderPubSharedPrefs;
    private SharedPreferences sharedPrefs = null;


    private String acceptedTOU;  // Terms Of Use
    private String latestTOU;
    private Boolean extStoragePermission;
    private Boolean smsPermission;
    private String accessToken;
    private boolean isImageReplaced;
    private String username;
    private String useremail;
    private String usermobile;
    private String userImage;
    private String userId;
    private String googleAccountName;
    private String serviceSiteId;
    private Boolean bookHistoryValid;
    private String bookHistoryJson;
    private List<String> bookHistoryArr;
    private String key;
    private Boolean firstRun;
    private String pendingAPIRequests;

    private WonderPubSharedPrefs(Context context) {
        this.appContext = context;
    }

    public static WonderPubSharedPrefs getInstance(Context context) {
        if (wonderPubSharedPrefs == null) {
            wonderPubSharedPrefs = new WonderPubSharedPrefs(context);

            wonderPubSharedPrefs.sharedPrefs =
                    context.getSharedPreferences(SHARED_PREFS_NAME, Context.MODE_PRIVATE);

            wonderPubSharedPrefs.acceptedTOU =
                    wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_ACCEPTED_TOU, "invalid");
            wonderPubSharedPrefs.latestTOU =
                    wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_LATEST_TOU, "unknown");
            wonderPubSharedPrefs.extStoragePermission =
                    wonderPubSharedPrefs.sharedPrefs.getBoolean(SHARED_PREFS_PERM_STORAGE, false);
            wonderPubSharedPrefs.smsPermission =
                    wonderPubSharedPrefs.sharedPrefs.getBoolean(SHARED_PREFS_PERM_SMS, false);

            wonderPubSharedPrefs.accessToken =
                    wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_ACCESS_TOKEN, "nil");
            wonderPubSharedPrefs.serviceSiteId =
                    wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_SERVICE_SITE_ID, "1");

            wonderPubSharedPrefs.bookHistoryValid =
                    wonderPubSharedPrefs.sharedPrefs.getBoolean(SHARED_PREFS_BOOK_HISTORY_VALID, false);

            wonderPubSharedPrefs.firstRun =
                    wonderPubSharedPrefs.sharedPrefs.getBoolean(SHARED_PREFS_FIRSTRUN, true);

            wonderPubSharedPrefs.key =
                    wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_KEYS, "nil");


            //wonderPubSharedPrefs.username = wonderPubSharedPrefs
            //      .sharedPrefs.getString(SHARED_PREFS_USERNAME, "");
            wonderPubSharedPrefs.userImage = wonderPubSharedPrefs
                    .sharedPrefs.getString(SHARED_PREFS_USERIMAGE, "");

            wonderPubSharedPrefs.userId = wonderPubSharedPrefs
                    .sharedPrefs.getString(SHARED_PREFS_USERID, "");

            //wonderPubSharedPrefs.useremail = wonderPubSharedPrefs
            //      .sharedPrefs.getString(SHARED_PREFS_USEREMAIL, "");

            //wonderPubSharedPrefs.usermobile = wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USERMOBILE, "");

            wonderPubSharedPrefs.googleAccountName = wonderPubSharedPrefs.
                    sharedPrefs.getString(SHARED_PREFS_GOOGLE_ACCOUNT_NAME, null);

            wonderPubSharedPrefs.pendingAPIRequests = wonderPubSharedPrefs.sharedPrefs
                    .getString(SHARED_PREFS_PENDING_API_REQUESTS, "");

        }
        return wonderPubSharedPrefs;
    }

    public void setUserLastQuizResults(String results) {
        wonderPubSharedPrefs.sharedPrefs.edit().putString(USER_LAST_QUIZ_RESULTS, results).apply();
    }

    public void setUserLastQuizData(String data) {
        wonderPubSharedPrefs.sharedPrefs.edit().putString(USER_LAST_QUIZ_DATA, data).apply();
    }

    public String getUserLastQuizResults() {
        return wonderPubSharedPrefs.sharedPrefs.getString(USER_LAST_QUIZ_RESULTS, "");
    }

    public String getUserLastQuizData() {
        return wonderPubSharedPrefs.sharedPrefs.getString(USER_LAST_QUIZ_DATA, "");
    }

    public String getUserAffiliationTimeStamp() {
        return wonderPubSharedPrefs.sharedPrefs.getString(USER_AFFILIATION_TIME_STAMP, "");
    }

    public void setFirstTimeLaunch(boolean isFirstTime) {
        wonderPubSharedPrefs.sharedPrefs.edit().putBoolean(IS_FIRST_TIME_LAUNCH, isFirstTime).commit();
    }

    public boolean isFirstTimeLaunch() {
        return wonderPubSharedPrefs.sharedPrefs.getBoolean(IS_FIRST_TIME_LAUNCH, true);
    }

    public void setUserAffiliationTimeStamp(String timeStamp) {
        wonderPubSharedPrefs.sharedPrefs.edit().putString(USER_AFFILIATION_TIME_STAMP,
                timeStamp).apply();
    }

    public String getUserAffiliationContent() {
        return wonderPubSharedPrefs.sharedPrefs.getString(USER_AFFILIATION_CONTENT, "");
    }

    public void setUserAffiliationContent(String content) {
        Log.d("Affiliation code", content);
        wonderPubSharedPrefs.sharedPrefs.edit().putString(USER_AFFILIATION_CONTENT,
                content).apply();
    }

    public String getSharedPrefsSeenResources() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_SEEN_RESOURCES, "");
    }

    public void setAccessCode(String code) {
        wonderPubSharedPrefs.sharedPrefs.edit().putString(SHARED_PREFS_USER_ACCESS_CODE, code).apply();
    }

    public String getAccessCode() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USER_ACCESS_CODE, "");
    }

    public void setSharedPrefsUserChallengerBookId(String bookId) {
        wonderPubSharedPrefs.sharedPrefs.edit().
                putString(SHARED_PREFS_USER_CHALLENGER_BOOK_ID, bookId).apply();
    }

    public String getSharedPrefsUserChallengerBookId() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USER_CHALLENGER_BOOK_ID, "");
    }

    public void setChallenger(String challenger) {
        wonderPubSharedPrefs.sharedPrefs.edit().putString(SHARED_PREFS_USER_CHALLENGER_INFO, challenger).apply();
    }

    public String getChallenger() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USER_CHALLENGER_INFO, "");
    }

    public void storeLastUserOnline(String userName) {
        wonderPubSharedPrefs.sharedPrefs.edit().putString(SHARED_PREFS_LAST_USER_ONLINE, userName).
                apply();
    }

    public String getLastUserOnline() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_LAST_USER_ONLINE, "");
    }

    public void addSharedPrefsSeenResources(String resourceId) {
        if (getSharedPrefsSeenResources().contains(resourceId) || getAccessToken().equalsIgnoreCase("nil"))
            return;
        if (getSharedPrefsSeenResources().isEmpty())
            resourceId = "," + resourceId + ",";
        else if (resourceId.charAt(resourceId.length() - 1) != ',')
            resourceId = getSharedPrefsSeenResources() + resourceId + ",";

        this.sharedPrefs.edit().putString(SHARED_PREFS_SEEN_RESOURCES, resourceId).commit();
    }

    public void setSiteMaster(String siteMstJson) {
        sharedPrefs.edit().putString(SHARED_PREFS_SITE_MST, siteMstJson).commit();
    }

    public String getSiteMst() {
        return sharedPrefs.getString(SHARED_PREFS_SITE_MST, "");
    }


    public void setlatestDate(String latestDate) {
        sharedPrefs.edit().putString(SHARED_PREFS_LATEST_DATE, latestDate).commit();
    }

    public String getlatestDate() {
        return sharedPrefs.getString(SHARED_PREFS_LATEST_DATE, "");
    }

    public void setDefaultCategory(String defaultCategory) {
        sharedPrefs.edit().putString(SHARED_PREFS_DEFAULT_CATEGORY, defaultCategory).commit();
    }

    public String getDefaultCategory() {
        return sharedPrefs.getString(SHARED_PREFS_DEFAULT_CATEGORY, "");
    }

    public void setUserPrefs(String userPrefs) {
        sharedPrefs.edit().putString(SHARED_PREFS_USER_SELECTED_PREFS, userPrefs).commit();
    }

    public boolean isPrintBookAddedToCart() {
        return this.sharedPrefs.getBoolean(SHARED_PREFS_PRINT_BOOK_ADDED_TO_CART, false);
    }

    public void printBookAddedToCart(boolean value) {
        this.sharedPrefs.edit().putBoolean(SHARED_PREFS_PRINT_BOOK_ADDED_TO_CART, value).commit();
    }

    public String getSharedPrefsLastReadPage() {
        return this.sharedPrefs.getString(SHARED_PREFS_LAST_READ_PAGE, "");
    }

    public void setSharedPrefsLastReadPage(String value) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_LAST_READ_PAGE, value).commit();
    }

    public String getSharedPrefsQuizData() {
        return this.sharedPrefs.getString(SHARED_PREFS_QUIZ_DATA, "");
    }

    public void setSharedPrefsQuizData(String value) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_QUIZ_DATA, value).commit();
    }

    public String getUserPrefs() {
        return sharedPrefs.getString(SHARED_PREFS_USER_SELECTED_PREFS, "");
    }

    public boolean termOfUseAccepted() {
        return this.acceptedTOU.equals(this.latestTOU);
    }

    public boolean getStoragePermissions() {
        return this.extStoragePermission;
    }

    public void storeAPICallDate(String date) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_API_CALL_DATE, date).commit();
    }

    public LocalDate getAPICallDate() {
        LocalDate date;
        try {
            String dateStr = this.sharedPrefs.getString(SHARED_PREFS_API_CALL_DATE, "");
            DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd");
            date = LocalDate.parse(dateStr, format);
        } catch (Exception e) {
            return null;
        }
        return date;
    }

    public void setDatesResponse(JSONObject response) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_DATE_RESPONSE, response.toString()).apply();
    }

    public String getDatesResponse() {
        return this.sharedPrefs.getString(SHARED_PREFS_DATE_RESPONSE, "");
    }

    public void setTestDatesResponse(String response) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_TEST_DATE_RESPONSE, response).apply();
    }

    public String getTestDatesResponse() {
        return  this.sharedPrefs.getString(SHARED_PREFS_TEST_DATE_RESPONSE, "");
    }

    public void clearAllSharePref() {
        try {
            /*String userLevel = getSharedPrefsUserLevelPref();
            List<String> userSyllabus = getSharedPrefsUserSyllabusPref();
            List<String> userGrade = getSharedPrefsUserGradePref();*/
            appContext.getSharedPreferences("WonderPublishSP", 0).edit().clear().commit();
            /*setSharedPrefsUserLevelPref(userLevel);
            setSharedPrefsUserSyllabusPref(userSyllabus);
            setSharedPrefsUserGradePref(userGrade);*/
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public synchronized String getAccessToken() {
        return this.accessToken;
    }

    public void setAccessToken(String token) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_ACCESS_TOKEN, token).commit();
        setTokenTime();
        Log.e("WonderPublish: ", "Update Access Token to " + token);
        this.accessToken = token;
    }

    public void deviceRegistered(boolean value) {
        this.sharedPrefs.edit().putBoolean(SHARED_PREFS_DEVICE_REGISTRATION, value).commit();
    }

    public boolean isDeviceRegistered() {
        return this.sharedPrefs.getBoolean(SHARED_PREFS_DEVICE_REGISTRATION, false);
    }

    public void setisUserLoggedIn(boolean value) {
        this.sharedPrefs.edit().putBoolean(SHARED_PREFS_IS_USER_LOGGEDIN, value).commit();
    }

    public boolean getisUserLoggedIn() {
        return this.sharedPrefs.getBoolean(SHARED_PREFS_IS_USER_LOGGEDIN, false);
    }

    public void subscribedToTopic(boolean value) {
        this.sharedPrefs.edit().putBoolean(SHARED_PREFS_TOPIC_SUBSCRIPTION, value).commit();
    }

    public boolean isSubscribedToTopic() {
        return this.sharedPrefs.getBoolean(SHARED_PREFS_TOPIC_SUBSCRIPTION, false);
    }

    public void setSharedPrefsAppVersion(String version) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_APP_VERSION, version).commit();
    }

    public String getAppSharedPrefsVersion() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_APP_VERSION, "");
    }

    public void setPlaysoreVersion(String version) {
        this.sharedPrefs.edit().putString(SHARED_PLAYSTORE_VERSION, version).commit();
    }

    public void setMigrationPasswordSet(boolean version) {
        this.sharedPrefs.edit().putBoolean(SHARED_PREFS_MIGRATION_PASSWORD_SET, version).commit();
    }

    public void setSharedPrefsUserPrivileges(String preivilegesStr) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_USER_PRIVILEGES, preivilegesStr).commit();
    }

    public String getSharedPrefsUserPrivileges() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USER_PRIVILEGES, "");
    }

    public void setTotalBades(String points) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_BADES, points).apply();
    }

    public String getTotalBades() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_BADES, "pHunter");
    }

    public void setTotalMedals(String points) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_TOTAL_MEDALS, points).apply();
    }

    public void setUserPreferredDailyTest(String userPreferredDailyTest) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_USER_PREFFERED_DAILY_TEST, userPreferredDailyTest).apply();
    }

    public String getUserPreferredDailyTest() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USER_PREFFERED_DAILY_TEST, "");
    }

    public String getTotalMedals() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_TOTAL_MEDALS, "0");
    }

    public void setTotalPoints(String points) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_TOTAL_POINTS, points).apply();
    }

    public String getTotalPoints() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_TOTAL_POINTS, "10");
    }

    public boolean getMigrationPasswordSet() {
        return wonderPubSharedPrefs.sharedPrefs.getBoolean(SHARED_PREFS_MIGRATION_PASSWORD_SET, true);
    }

    public String getPlaystoreVersion() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PLAYSTORE_VERSION, "");
    }

    public boolean isImageReplaced(String resId) {
        SHARED_PREFS_RES_ID = resId;
        return this.sharedPrefs.getBoolean(SHARED_PREFS_RES_ID + "_READ_IMAGE", false);
    }

    public void setImageReplaced(String resId, boolean imageReplaced) {
        SHARED_PREFS_RES_ID = resId;
        this.sharedPrefs.edit().putBoolean(SHARED_PREFS_RES_ID + "_READ_IMAGE", imageReplaced).commit();
    }

    public void setSVGUpdated(String resId, boolean imageReplaced) {
        SHARED_PREFS_RES_ID = resId;
        this.sharedPrefs.edit().putBoolean(SHARED_PREFS_RES_ID + "_SVG_UPDATED", imageReplaced).commit();
    }

    public boolean isSVGUpdated(String resId) {
        SHARED_PREFS_RES_ID = resId;
        return this.sharedPrefs.getBoolean(SHARED_PREFS_RES_ID + "_SVG_UPDATED", false);
    }

    public void setDeviceID(String id) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_DEVICE_ID, id).commit();
    }

    public String getDeviceID() {
        return this.sharedPrefs.getString(SHARED_PREFS_DEVICE_ID, "");
    }

    public void setLiveClasses(String liveVideosString) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_LIVE_VIDEOS, liveVideosString).commit();
    }

    public String getLiveClasses() {
        return this.sharedPrefs.getString(SHARED_PREFS_LIVE_VIDEOS, "");
    }

    public void setDeepLinkUri(Uri deepLinkUri) {
        if (deepLinkUri != null) {
            this.sharedPrefs.edit().putString(SHARED_PREFS_DEEP_LINK, deepLinkUri.toString()).commit();
        } else {
            this.sharedPrefs.edit().putString(SHARED_PREFS_DEEP_LINK, "").commit();
        }
    }

    public Uri getDeepLinkUri() {
        return Uri.parse(this.sharedPrefs.getString(SHARED_PREFS_DEEP_LINK, ""));
    }

    public void setQuizInstructionJSON(JSONObject jsonObject) {
        if (jsonObject != null) {
            this.sharedPrefs.edit().putString(SHARED_PREFS_QUIZ_INSTRUCTION_JSON, jsonObject.toString()).commit();
        } else {
            this.sharedPrefs.edit().putString(SHARED_PREFS_QUIZ_INSTRUCTION_JSON, "").commit();
        }
    }

    public JSONObject getQuizInstructionJSON() {
        if (!this.sharedPrefs.getString(SHARED_PREFS_QUIZ_INSTRUCTION_JSON, "").isEmpty()) {
            try {
                return new JSONObject(this.sharedPrefs.getString(SHARED_PREFS_QUIZ_INSTRUCTION_JSON, ""));
            } catch (JSONException e) {
                Log.d(TAG, "Exception while getting quiz instruction json", e);
                return new JSONObject();
            }
        } else {
            return new JSONObject();
        }
    }

    public void setNoDeepLinkNotifIntent(boolean value) {
        if (value) {
            this.sharedPrefs.edit().putBoolean(SHARED_PREFS_NOTIF_INTENT, true).commit();
        } else {
            this.sharedPrefs.edit().putBoolean(SHARED_PREFS_NOTIF_INTENT, false).commit();
        }
    }

    public boolean getNoDeepLinkNotifIntent() {
        return this.sharedPrefs.getBoolean(SHARED_PREFS_NOTIF_INTENT, false);
    }

    public void setNotificationId(String id) {
        if (id != null && !id.isEmpty() && !id.equalsIgnoreCase("null") && Integer.parseInt(id) > getNotificationId()) {
            this.sharedPrefs.edit().putInt(SHARED_PREFS_NOTIF_ID, Integer.valueOf(id)).commit();
        }
    }

    public int getNotificationId() {
        return this.sharedPrefs.getInt(SHARED_PREFS_NOTIF_ID, 0);
    }

    public void setCurrentAudioTrack(String resLink) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_CURRENT_AUDIO_TRACK, resLink).commit();
        if (resLink.isEmpty()) {
            setCurrentAudioSpeed("");
        } else {
            setCurrentAudioSpeed("NORMAL");
        }
    }

    public void setIsTestSeries(boolean value) {
        this.sharedPrefs.edit().putBoolean(SHARED_PREFS_IS_TEST_SERIES, value).commit();
    }

    public boolean getIsTestSeries() {
        return this.sharedPrefs.getBoolean(SHARED_PREFS_IS_TEST_SERIES, false);
    }

    public void setBookUpgradePrice(String value) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_BOOK_UPGRADE_PRICE, value).commit();
    }

    public String getBookUpgradePrice() {
        return this.sharedPrefs.getString(SHARED_PREFS_BOOK_UPGRADE_PRICE, "");
    }

    public void setCartCount(Integer value) {
        this.sharedPrefs.edit().putInt(SHARED_PREFS_CART_COUNT, value).commit();
    }

    public Integer getCartCount() {
        return this.sharedPrefs.getInt(SHARED_PREFS_CART_COUNT, 0);
    }

    public void setRedirectToLibrary(Boolean value) {
        this.sharedPrefs.edit().putBoolean(SHARED_PREFS_REDIRECT_TO_LIBRARY, value).commit();
    }

    public Boolean shouldRedirectToLibrary() {
        return this.sharedPrefs.getBoolean(SHARED_PREFS_REDIRECT_TO_LIBRARY, false);
    }

    public void setBookInLibrary(Boolean value) {
        this.sharedPrefs.edit().putBoolean(SHARED_PREFS_BOOK_IN_LIBRARY, value).commit();
    }

    public Boolean isBookInLibrary() {
        return this.sharedPrefs.getBoolean(SHARED_PREFS_BOOK_IN_LIBRARY, false);
    }

    public void setTestSeriesPurchaseStatus(boolean value) {
        this.sharedPrefs.edit().putBoolean(SHARED_PREFS_TEST_SERIES_BOUGHT, value).commit();
    }

    public boolean getTestSeriesPurchaseStatus() {
        return this.sharedPrefs.getBoolean(SHARED_PREFS_TEST_SERIES_BOUGHT, false);
    }

    public void setGPTPurchaseStatus(String value) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_GPT_BOUGHT, value).commit();
    }

    public String getGPTPurchaseStatus() {
        return this.sharedPrefs.getString(SHARED_PREFS_GPT_BOUGHT, "");
    }

    public void setLibraryPaidTokenCount(String value) {
        sharedPrefs.edit().putString(SHARED_PREFS_LIBRARY_PAID_TOKEN_COUNT, value).commit();
    }

    public String getLibraryPaidTokenCount() {
        return sharedPrefs.getString(SHARED_PREFS_LIBRARY_PAID_TOKEN_COUNT, "");
    }

    public String getCurrentAudioTrack() {
        return this.sharedPrefs.getString(SHARED_PREFS_CURRENT_AUDIO_TRACK, "");
    }

    public void setUserSelectedCourse(String courseString) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_USER_SELECTED_COURSE, courseString).commit();
    }

    public String getUserSelectedCourse() {
        return this.sharedPrefs.getString(SHARED_PREFS_USER_SELECTED_COURSE, "");
    }

    public void setUserSelectedPollSubmission(int pollId, int answerId) {
        HashMap<Integer, Integer> userSelectedPollAnswers;
        userSelectedPollAnswers = getUserSelectedPollAnswers();
        if (userSelectedPollAnswers == null){
            userSelectedPollAnswers = new HashMap<>();
        }
        userSelectedPollAnswers.put(pollId, answerId);
        Gson gson = new Gson();
        String hashMapString = gson.toJson(userSelectedPollAnswers);
        this.sharedPrefs.edit().putString(SHARED_PREFS_USER_SELECTED_POLL_ANSWER, hashMapString).commit();
    }

    public HashMap<Integer, Integer> getUserSelectedPollAnswers() {
        Gson gson = new Gson();
        String storedHashMapString = this.sharedPrefs.getString(SHARED_PREFS_USER_SELECTED_POLL_ANSWER, "");
        java.lang.reflect.Type type = new TypeToken<HashMap<Integer, Integer>>() {
        }.getType();
        return gson.fromJson(storedHashMapString, type);
    }

    public void setSelectedParentBookId(String bookId) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_SELECTED_BOOK_ID, bookId).commit();
    }

    public String getSelectedParentBookId() {
        return this.sharedPrefs.getString(SHARED_PREFS_SELECTED_BOOK_ID, "");
    }

    public void setAllCourse(HashMap<String, String> courseMap) {
        //convert to string using gson
        Gson gson = new Gson();
        String hashMapString = gson.toJson(courseMap);
        this.sharedPrefs.edit().putString(SHARED_PREFS_ALL_COURSE, hashMapString).commit();
    }

    public void setHiddenBooks(HashMap<String, String> courseMap) {
        //convert to string using gson
        Gson gson = new Gson();
        String hashMapString = gson.toJson(courseMap);
        this.sharedPrefs.edit().putString(SHARED_PREFS_HIDDEN_BOOKS, hashMapString).commit();
    }

    public void setDownloadedVideoMap(HashMap<String, String> videosMap) {
        //convert to string using gson
        Gson gson = new Gson();
        String hashMapString = gson.toJson(videosMap);
        this.sharedPrefs.edit().putString(SHARED_PREFS_DOWNLOADED_VIDEOS, hashMapString).commit();
    }

    /**
     * Downloaded Videos data is no longer maintained using SharedPrefs
     */
    @Deprecated
    public HashMap<String, String> getDownloadedVideos() {
        //get from shared prefs
        Gson gson = new Gson();
        String storedHashMapString = this.sharedPrefs.getString(SHARED_PREFS_DOWNLOADED_VIDEOS, "");
        java.lang.reflect.Type type = new TypeToken<HashMap<String, String>>() {
        }.getType();
        return gson.fromJson(storedHashMapString, type);
    }

    public HashMap<String, String> getHiddenBooks() {
        //get from shared prefs
        Gson gson = new Gson();
        String storedHashMapString = this.sharedPrefs.getString(SHARED_PREFS_HIDDEN_BOOKS, "");
        java.lang.reflect.Type type = new TypeToken<HashMap<String, String>>() {
        }.getType();
        return gson.fromJson(storedHashMapString, type);
    }

    public HashMap<String, String> getAllCourse() {
        //get from shared prefs
        Gson gson = new Gson();
        String storedHashMapString = this.sharedPrefs.getString(SHARED_PREFS_ALL_COURSE, "");
        java.lang.reflect.Type type = new TypeToken<HashMap<String, String>>() {
        }.getType();
        return gson.fromJson(storedHashMapString, type);
    }

    public void setAllCourseArray(String courseArrayString) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_ALL_COURSE_ARRAY, courseArrayString).commit();
    }

    public String getAllCourseArray() {
        //get from shared prefs
        return this.sharedPrefs.getString(SHARED_PREFS_ALL_COURSE_ARRAY, "");
    }

    public void setWeeklyWinners(String weeklyWinnersString) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_WEEKLY_WINNERS, weeklyWinnersString).commit();
    }

    public String getWeeklyWinners() {
        //get from shared prefs
        return this.sharedPrefs.getString(SHARED_PREFS_WEEKLY_WINNERS, "");
    }

    public void setTickerVisibilisty(boolean value) {
        this.sharedPrefs.edit().putBoolean(SHARED_PREFS_WEEKLY_WINNERS_VISIBILITY, value).commit();
    }

    public boolean getTickerVisibility() {
        //get from shared prefs
        return this.sharedPrefs.getBoolean(SHARED_PREFS_WEEKLY_WINNERS_VISIBILITY, false);
    }

    public void setTestGenQuizArray(String array){
        this.sharedPrefs.edit().putString(SHARED_PREFS_TEST_GEN_QUIZ_ARRAY, array).commit();
    }

    public String getTestGenQuizArray(){
        return this.sharedPrefs.getString(SHARED_PREFS_TEST_GEN_QUIZ_ARRAY, "");
    }

    public void setTestGenChaptersArray(String array){
        this.sharedPrefs.edit().putString(SHARED_PREFS_TEST_GEN_CHAPTERS_ARRAY, array).commit();
    }

    public String getTestGenChaptersArray(){
        return this.sharedPrefs.getString(SHARED_PREFS_TEST_GEN_CHAPTERS_ARRAY, "");
    }

    public void setUserCourseIdMap(HashMap<String, String> userCourseMap) {
        //convert to string using gson
        Gson gson = new Gson();
        String hashMapString = gson.toJson(userCourseMap);
        this.sharedPrefs.edit().putString(SHARED_PREFS_USER_COURSE_IDS, hashMapString).commit();
    }

    public HashMap<String, String> getUserCourseIdMap() {
        //get from shared prefs
        Gson gson = new Gson();
        String storedHashMapString = this.sharedPrefs.getString(SHARED_PREFS_USER_COURSE_IDS, "");
        java.lang.reflect.Type type = new TypeToken<HashMap<String, String>>() {
        }.getType();
        return gson.fromJson(storedHashMapString, type);
    }

    public void setUserLastUsageTimeStamp(String timeStamp) {
        this.sharedPrefs.edit().putString(USER_LAST_USAGE_TIME_STAMP, timeStamp).apply();
    }

    public String getUserLastUsageTimeStamp() {
        return this.sharedPrefs.getString(USER_LAST_USAGE_TIME_STAMP, "");
    }

    public void incUserSessionCount() {
        int currentCount = this.sharedPrefs.getInt(USER_SESSION_COUNT, 0);
        currentCount++;
        this.sharedPrefs.edit().putInt(USER_SESSION_COUNT, currentCount).apply();
    }

    public int getUserSessionCount() {
        return this.sharedPrefs.getInt(USER_SESSION_COUNT, 0);
    }

    public void resetUserSessionCount() {
        this.sharedPrefs.edit().putInt(USER_SESSION_COUNT, -1).apply();
    }

    //for new shop UI - Competitive
    public void setUserSelectedLevel(String levelString) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_SELECTED_LEVEL, levelString).commit();
    }

    public String getUserSelectedLevel() {
        return this.sharedPrefs.getString(SHARED_PREFS_SELECTED_LEVEL, "");
    }

    public void setUserSelectedSyllabus(String levelString) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_SELECTED_SYLLABUS, levelString).commit();
    }

    public String getUserSelectedSyllabus() {
        return this.sharedPrefs.getString(SHARED_PREFS_SELECTED_SYLLABUS, "");
    }

    public void setUserSelectedGrade(String levelString) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_SELECTED_GRADE, levelString).commit();
    }

    public String getUserSelectedGrade() {
        return this.sharedPrefs.getString(SHARED_PREFS_SELECTED_GRADE, "");
    }

    public void setUserSelectedGradeId(String levelString) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_SELECTED_GRADEID, levelString).commit();
    }

    public String getUserSelectedGradeId() {
        return this.sharedPrefs.getString(SHARED_PREFS_SELECTED_GRADEID, "");
    }
    //for new shop UI - Competitive

    //for new shop UI - School
    public void setUserSelectedSchoolSyllabus(String levelString) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_SELECTED_SCHOOL_SYLLABUS, levelString).commit();
    }

    public String getUserSelectedSchoolSyllabus() {
        return this.sharedPrefs.getString(SHARED_PREFS_SELECTED_SCHOOL_SYLLABUS, "");
    }

    public void setUserSelectedSchoolGrade(String levelString) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_SELECTED_SCHOOL_GRADE, levelString).commit();
    }

    public String getUserSelectedSchoolGrade() {
        return this.sharedPrefs.getString(SHARED_PREFS_SELECTED_SCHOOL_GRADE, "");
    }

    public void setUserSelectedSchoolGradeId(String levelString) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_SELECTED_SCHOOL_GRADEID, levelString).commit();
    }

    public String getUserSelectedSchoolGradeId() {
        return this.sharedPrefs.getString(SHARED_PREFS_SELECTED_SCHOOL_GRADEID, "");
    }
    //for new shop UI - School

    public void setUserPurchasedBooks(List<String> purchasedBookIds) {
        //convert to string using gson
        this.sharedPrefs.edit().putString(SHARED_PREFS_PURCHASED_BOOK_IDS, purchasedBookIds.toString()).commit();
    }

    public List<String> getUserPurchasedBooks() {
        //get from shared prefs
        String purchasedBookIdsString = this.sharedPrefs.getString(SHARED_PREFS_PURCHASED_BOOK_IDS, "");
        List<String> purchasedBookIds;
        if (!purchasedBookIdsString.isEmpty()) {
            purchasedBookIdsString = purchasedBookIdsString.replace("[", "").replace("]", "")
                    .replace(" ", "");
            purchasedBookIds = new ArrayList<>(Arrays.asList(purchasedBookIdsString.split(",")));
        } else {
            purchasedBookIds = new ArrayList<>();
        }
        return purchasedBookIds;
    }

    public void setUserLibraryBooks(String userLibraryBooks) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_USER_LIBRARY_BOOKS, userLibraryBooks).commit();
    }

    public List<String> getUserLibraryBooks() {
        //get from shared prefs
        String userLibraryBooks = this.sharedPrefs.getString(SHARED_PREFS_USER_LIBRARY_BOOKS, "");
        List<String> list;
        if (!userLibraryBooks.isEmpty()) {
            list = new ArrayList<>(Arrays.asList(userLibraryBooks.split(",")));
        } else {
            list = new ArrayList<>();
        }
        return list;
    }

    public void setUserBatches(String userBatches) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_USER_BATCHES, userBatches).commit();
    }

    public List<String> getUserBatches() {
        //get from shared prefs
        String userBatches = this.sharedPrefs.getString(SHARED_PREFS_USER_BATCHES, "");
        List<String> list;
        if (!userBatches.isEmpty()) {
            list = new ArrayList<>(Arrays.asList(userBatches.split(",")));
        } else {
            list = new ArrayList<>();
        }
        return list;
    }

    /*public void setUserPurchasedBooks(List<String> purchasedBookIds) {
        //convert to string using gson
        this.sharedPrefs.edit().putString(SHARED_PREFS_PURCHASED_BOOK_IDS, purchasedBookIds.toString()).commit();
    }

    public List<String> getUserPurchasedBooks() {
        //get from shared prefs
        String purchasedBookIdsString = this.sharedPrefs.getString(SHARED_PREFS_PURCHASED_BOOK_IDS, "");
        List<String> purchasedBookIds;
        if (!purchasedBookIdsString.isEmpty()){
            purchasedBookIdsString = purchasedBookIdsString.replace("[", "").replace("]", "")
                    .replace(" ", "");
            purchasedBookIds = new ArrayList<>(Arrays.asList(purchasedBookIdsString.split(",")));
        }
        else {
            purchasedBookIds = new ArrayList<>();
        }
        return purchasedBookIds;
    }*/

    public void setGradeIdMap(HashMap<String, String> gradeMap) {
        //convert to string using gson
        Gson gson = new Gson();
        String hashMapString = gson.toJson(gradeMap);
        this.sharedPrefs.edit().putString(SHARED_PREFS_ALL_GRADE_IDS, hashMapString).commit();
    }

    public HashMap<String, String> getGradeIdMap() {
        //get from shared prefs
        Gson gson = new Gson();
        String storedHashMapString = this.sharedPrefs.getString(SHARED_PREFS_ALL_GRADE_IDS, "");
        java.lang.reflect.Type type = new TypeToken<HashMap<String, String>>() {
        }.getType();
        return gson.fromJson(storedHashMapString, type);
    }

    public void setGradeValuesMap(HashMap<String, String> gradeMap) {
        //convert to string using gson
        Gson gson = new Gson();
        String hashMapString = gson.toJson(gradeMap);
        this.sharedPrefs.edit().putString(SHARED_PREFS_ALL_GRADE_VALUES, hashMapString).commit();
    }

    public HashMap<String, String> getGradeValuesMap() {
        //get from shared prefs
        Gson gson = new Gson();
        String storedHashMapString = this.sharedPrefs.getString(SHARED_PREFS_ALL_GRADE_VALUES, "");
        java.lang.reflect.Type type = new TypeToken<HashMap<String, String>>() {
        }.getType();
        return gson.fromJson(storedHashMapString, type);
    }

    public void setclassMap(HashMap<String, List<String>> classMap) {
        //convert to string using gson
        Gson gson = new Gson();
        String hashMapString = gson.toJson(classMap);
        this.sharedPrefs.edit().putString(SHARED_PREFS_CLASS_MAP, hashMapString).commit();
    }

    public HashMap<String, Long> getVideoDownloadPausedMap() {
        String mapString = this.sharedPrefs.getString(SHARED_PREFS_VIDEO_DOWNLOAD_PAUSED_MAP, "");
        if (mapString != null && !mapString.isEmpty()) {
            Gson gson = new Gson();
            java.lang.reflect.Type type = new TypeToken<HashMap<String, Long>>() {
            }.getType();
            return gson.fromJson(mapString, type);
        }
        return new HashMap<>();
    }

    public HashMap<String, Integer> getVideoDownloadPercentageMap() {
        String mapString = this.sharedPrefs.getString(SHARED_PREFS_DOWNLOAD_PERCENTAGE_VIDEO, "");
        if (mapString != null && !mapString.isEmpty()) {
            Gson gson = new Gson();
            java.lang.reflect.Type type = new TypeToken<HashMap<String, Integer>>() {
            }.getType();
            try {
                return gson.fromJson(mapString, type);
            }catch (Exception e) {
                Log.e(TAG, "getVideoDownloadPercentageMap: ", e);
                return new HashMap<>();
            }
        }
        return new HashMap<>();
    }

    public void clearVideoDownloadFromMap(String resId) {
        String mapString = this.sharedPrefs.getString(SHARED_PREFS_VIDEO_DOWNLOAD_PAUSED_MAP, "");
        Gson gson = new Gson();
        HashMap<String, Integer> downloadingPercentageMap;
        java.lang.reflect.Type type = new TypeToken<HashMap<String, Integer>>() {
        }.getType();
        if (mapString != null && !mapString.isEmpty() && !StringUtil.isNumeric(mapString)) {
            downloadingPercentageMap = gson.fromJson(mapString, type);
            downloadingPercentageMap.remove(resId);
            this.sharedPrefs.edit().putString(SHARED_PREFS_VIDEO_DOWNLOAD_PAUSED_MAP, gson.toJson(downloadingPercentageMap)).commit();
            return;
        } else {
            this.sharedPrefs.edit().putString(SHARED_PREFS_VIDEO_DOWNLOAD_PAUSED_MAP, "").commit();
        }
    }

    public void addToVideoDownloadPausedMap(String resId, Long percentageDownloaded) {
        String mapString = this.sharedPrefs.getString(SHARED_PREFS_VIDEO_DOWNLOAD_PAUSED_MAP, "");
        Gson gson = new Gson();
        HashMap<String, Long> downloadingPercentageMap;
        java.lang.reflect.Type type = new TypeToken<HashMap<String, Long>>() {
        }.getType();
        if (mapString != null && !mapString.isEmpty() && !StringUtil.isNumeric(mapString)) {
            downloadingPercentageMap = gson.fromJson(mapString, type);
            downloadingPercentageMap.put(resId, percentageDownloaded);
            this.sharedPrefs.edit().putString(SHARED_PREFS_VIDEO_DOWNLOAD_PAUSED_MAP, gson.toJson(downloadingPercentageMap)).commit();
            return;
        } else {
            this.sharedPrefs.edit().putString(SHARED_PREFS_VIDEO_DOWNLOAD_PAUSED_MAP, "").commit();
        }
        HashMap<String, Long> videoMap = new HashMap<>();
        videoMap.put(resId, percentageDownloaded);
        this.sharedPrefs.edit().putString(SHARED_PREFS_VIDEO_DOWNLOAD_PAUSED_MAP, gson.toJson(videoMap)).commit();
    }

    public HashMap<String, String> getVideoDownloadPausedLinkMap() {
        String mapString = this.sharedPrefs.getString(SHARED_PREFS_VIDEO_DOWNLOAD_PAUSED_LINK_MAP, "");
        if (mapString != null && !mapString.isEmpty()) {
            Gson gson = new Gson();
            java.lang.reflect.Type type = new TypeToken<HashMap<String, String>>() {
            }.getType();
            HashMap<String, String> map = gson.fromJson(mapString, type);
            if(map != null)
                return map;
        }
        return new HashMap<>();
    }

    public void addToVideoDownloadSizeMap(String resId, String downloadSIze) {
        String mapString = this.sharedPrefs.getString(SHARED_PREFS_VIDEO_DOWNLOAD_SIZE_MAP, "");
        Gson gson = new Gson();
        HashMap<String, String> downloadingSizeMap;
        java.lang.reflect.Type type = new TypeToken<HashMap<String, String>>() {
        }.getType();
        if (mapString != null && !mapString.isEmpty() && !StringUtil.isNumeric(mapString)) {
            downloadingSizeMap = gson.fromJson(mapString, type);
            downloadingSizeMap.put(resId, downloadSIze);
            this.sharedPrefs.edit().putString(SHARED_PREFS_VIDEO_DOWNLOAD_SIZE_MAP, gson.toJson(downloadingSizeMap)).commit();
            return;
        } else {
            this.sharedPrefs.edit().putString(SHARED_PREFS_VIDEO_DOWNLOAD_SIZE_MAP, "").commit();
        }
        HashMap<String, String> videoMap = new HashMap<>();
        videoMap.put(resId, downloadSIze);
        this.sharedPrefs.edit().putString(SHARED_PREFS_VIDEO_DOWNLOAD_SIZE_MAP, gson.toJson(videoMap)).commit();
    }

    public HashMap<String, String> getVideoDownloadSizeMap() {
        String mapString = this.sharedPrefs.getString(SHARED_PREFS_VIDEO_DOWNLOAD_SIZE_MAP, "");
        if (mapString != null && !mapString.isEmpty()) {
            Gson gson = new Gson();
            java.lang.reflect.Type type = new TypeToken<HashMap<String, String>>() {
            }.getType();
            return gson.fromJson(mapString, type);
        }
        return new HashMap<>();
    }


    public void setVideoDownloadFirstTime(boolean value) {
        this.sharedPrefs.edit().putBoolean(SHARED_PREFS_DOWNLOAD_VIDEO_FIRST_TIME, value).commit();
    }

    public boolean getDownloadingVideoFirstTime() {
        return this.sharedPrefs.getBoolean(SHARED_PREFS_DOWNLOAD_VIDEO_FIRST_TIME, false);
    }


    public void setEbooksClickFirstTime(boolean value) {
        this.sharedPrefs.edit().putBoolean(SHARED_PREFS_EBOOK_CLICK_FIRST_TIME, value).commit();
    }

    public boolean getEbookClickFirstTime() {
        return this.sharedPrefs.getBoolean(SHARED_PREFS_EBOOK_CLICK_FIRST_TIME, false);
    }


    public void setCafeClickFirstTime(boolean value) {
        this.sharedPrefs.edit().putBoolean(SHARED_PREFS_CAFE_CLICK_FIRST_TIME, value).commit();
    }

    public boolean getCafeClickFirstTime() {
        return this.sharedPrefs.getBoolean(SHARED_PREFS_CAFE_CLICK_FIRST_TIME, false);
    }



    public void clearDownloadPercentageMap() {
        String mapString = this.sharedPrefs.getString(SHARED_PREFS_DOWNLOAD_PERCENTAGE_VIDEO, "");
        Gson gson = new Gson();
        HashMap<String, Integer> downloadingPercentageMap;
        java.lang.reflect.Type type = new TypeToken<HashMap<String, Integer>>() {
        }.getType();

        if (mapString != null && !mapString.isEmpty()) {
            downloadingPercentageMap = gson.fromJson(mapString, type);
            if(downloadingPercentageMap != null && !downloadingPercentageMap.isEmpty()) {
                this.sharedPrefs.edit().putString(SHARED_PREFS_DOWNLOAD_PERCENTAGE_VIDEO, "").commit();
            }
        }
    }

    public void saveDownloadPercentageMap(String resId, int percentage) {
        String mapString = this.sharedPrefs.getString(SHARED_PREFS_DOWNLOAD_PERCENTAGE_VIDEO, "");
        Gson gson = new Gson();
        HashMap<String, Integer> downloadingPercentageMap;
        java.lang.reflect.Type type = new TypeToken<HashMap<String, Integer>>() {
        }.getType();
        if (mapString != null && !mapString.isEmpty()) {
            downloadingPercentageMap = gson.fromJson(mapString, type);
            downloadingPercentageMap.put(resId, percentage);
            this.sharedPrefs.edit().putString(SHARED_PREFS_DOWNLOAD_PERCENTAGE_VIDEO, gson.toJson(downloadingPercentageMap)).commit();
            return;
        } else {
            this.sharedPrefs.edit().putString(SHARED_PREFS_DOWNLOAD_PERCENTAGE_VIDEO, "").commit();
        }
        HashMap<String, Integer> percentageMap = new HashMap<>();
        percentageMap.put(resId, percentage);
        this.sharedPrefs.edit().putString(SHARED_PREFS_DOWNLOAD_PERCENTAGE_VIDEO, gson.toJson(percentageMap)).commit();
    }

    public void saveOriginalFileLength(String resId, String fileLength) {
        String mapString = this.sharedPrefs.getString(SHARED_PREFS_VIDEO_LENGTH_MAP, "");
        Gson gson = new Gson();
        HashMap<String, String> fileLengthMap;
        java.lang.reflect.Type type = new TypeToken<HashMap<String, String>>() {
        }.getType();
        if (mapString != null && !mapString.isEmpty() && !StringUtil.isNumeric(mapString)) {
            fileLengthMap = gson.fromJson(mapString, type);
            fileLengthMap.put(resId, fileLength);
            this.sharedPrefs.edit().putString(SHARED_PREFS_VIDEO_LENGTH_MAP, gson.toJson(fileLengthMap)).commit();
            return;
        } else {
            this.sharedPrefs.edit().putString(SHARED_PREFS_VIDEO_LENGTH_MAP, "").commit();
        }
        HashMap<String, String> videoMap = new HashMap<>();
        videoMap.put(resId, fileLength);
        this.sharedPrefs.edit().putString(SHARED_PREFS_VIDEO_LENGTH_MAP, gson.toJson(videoMap)).commit();
    }

    public HashMap<String, String> getOriginalFileLength() {
        String mapString = this.sharedPrefs.getString(SHARED_PREFS_VIDEO_LENGTH_MAP, "");
        if (mapString != null && !mapString.isEmpty()) {
            Gson gson = new Gson();
            java.lang.reflect.Type type = new TypeToken<HashMap<String, String>>() {
            }.getType();
            return gson.fromJson(mapString, type);
        }
        return new HashMap<>();
    }

    public void addToVideoDownloadPausedLinkMap(String resId, String link) {
        String mapString = this.sharedPrefs.getString(SHARED_PREFS_VIDEO_DOWNLOAD_PAUSED_LINK_MAP, "");
        Gson gson = new Gson();
        HashMap<String, String> downloadingLinkMap;
        java.lang.reflect.Type type = new TypeToken<HashMap<String, String>>() {
        }.getType();
        if (mapString != null && !mapString.isEmpty() && !StringUtil.isNumeric(mapString)) {
            downloadingLinkMap = gson.fromJson(mapString, type);
            downloadingLinkMap.put(resId, link);
            this.sharedPrefs.edit().putString(SHARED_PREFS_VIDEO_DOWNLOAD_PAUSED_LINK_MAP, gson.toJson(downloadingLinkMap)).commit();
            return;
        } else {
            this.sharedPrefs.edit().putString(SHARED_PREFS_VIDEO_DOWNLOAD_PAUSED_LINK_MAP, "").commit();
        }
        HashMap<String, String> videoMap = new HashMap<>();
        videoMap.put(resId, link);
        this.sharedPrefs.edit().putString(SHARED_PREFS_VIDEO_DOWNLOAD_PAUSED_LINK_MAP, gson.toJson(videoMap)).commit();
    }

    public HashMap<String, List<String>> getclassMap() {
        //get from shared prefs
        Gson gson = new Gson();
        String storedHashMapString = this.sharedPrefs.getString(SHARED_PREFS_CLASS_MAP, "");
        java.lang.reflect.Type type = new TypeToken<HashMap<String, List<String>>>() {
        }.getType();
        return gson.fromJson(storedHashMapString, type);
    }

    public void setFeaturedCourse(String courseString) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_FEATURED_COURSE, courseString).commit();
    }

    public String getFeaturedCourse() {
        return this.sharedPrefs.getString(SHARED_PREFS_FEATURED_COURSE, "");
    }

    public void setCurrentAudioSpeed(String speed) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_CURRENT_AUDIO_PLAYBACK_SPEED, speed).commit();
    }

    public String getCurrentAudioSpeed() {
        return this.sharedPrefs.getString(SHARED_PREFS_CURRENT_AUDIO_PLAYBACK_SPEED, "");
    }

    public void setFeatureShwon(String screenName) {
        String previouslyDone = getFeatureDiscoveryDone();
        if (!previouslyDone.contains(screenName)) {
            previouslyDone += "," + screenName;
            sharedPrefs.edit().putString(FEATURE_DISCOVERY, previouslyDone);
        }
    }

    public String getFeatureDiscoveryDone() {
        return sharedPrefs.getString(FEATURE_DISCOVERY, "");
    }

    public void setUserPreferredVideoResolution(int quality) {
        this.sharedPrefs.edit().putInt(SHARED_PREFS_USER_PREFERRED_VIDEO_QUALITY, quality).commit();
    }

    public int getUserPreferredVideoResolution() {
        return this.sharedPrefs.getInt(SHARED_PREFS_USER_PREFERRED_VIDEO_QUALITY, 0);
    }

    public void setLatestReleaseBooks(String latestReleaseBooks) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_LATEST_RELEASE_BOOKS, latestReleaseBooks).commit();
    }

    public String getLatestReleaseBooks() {
        return this.sharedPrefs.getString(SHARED_PREFS_LATEST_RELEASE_BOOKS, "");
    }

    public void setVideoResumePosition(Long position, String resLink) {
        try {
            if (!resLink.isEmpty() && position > 0) {
                if (!getVideoResumePositionList().isEmpty()) {
                    HashMap<String, Long> videoResumePosList = new HashMap<>();
                    String[] pairs = getVideoResumePositionList().split(",");
                    for (int i = 0; i < pairs.length; i++) {
                        String pair = pairs[i];
                        int index = pair.lastIndexOf("=");
                        String link = pair.substring(0, index);
                        String videoPlayBackPosition = pair.substring(index).replace("=", "");
                        //String[] keyValue = pair.split("=");
                        videoResumePosList.put(link.replace("{", "").trim(),
                                Long.valueOf(videoPlayBackPosition.replace("}", "").trim()));
                    }
                    if (!videoResumePosList.isEmpty()) {
                        videoResumePosList.remove(resLink);
                        videoResumePosList.put(resLink, position);
                        this.sharedPrefs.edit().putString(SHARED_PREFS_VIDEO_RESUME_POSITION, videoResumePosList.toString()).commit();
                    }
                } else {
                    HashMap<String, Long> videoResumePosList = new HashMap<>();
                    videoResumePosList.put(resLink, position);
                    this.sharedPrefs.edit().putString(SHARED_PREFS_VIDEO_RESUME_POSITION, videoResumePosList.toString()).commit();
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Exception while saving video resume position", e);
        }
    }

    public String getVideoResumePositionList() {
        return this.sharedPrefs.getString(SHARED_PREFS_VIDEO_RESUME_POSITION, "");
    }

    @NonNull
    public HashMap<String, Long> getVideoResumePositionsMap() {
        String positions = this.sharedPrefs.getString(SHARED_PREFS_VIDEO_RESUME_POSITION, "");
        HashMap<String, Long> videoResumePosList = new HashMap<>();
        if(positions != null && !positions.isEmpty()) {
            String[] pairs = positions.split(",");
            for (String pair : pairs) {
                try {
                    int index = pair.lastIndexOf("=");
                    String link = pair.substring(0, index);
                    String videoPlayBackPosition = pair.substring(index).replace("=", "");
                    videoResumePosList.put(link.replace("{", "").trim(),
                            Long.valueOf(videoPlayBackPosition.replace("}", "").trim()));
                }catch (IndexOutOfBoundsException e) {
                    Log.d(TAG, "getVideoResumePositionsMap: Error while converting video resume positions");
                }
            }
        }
        return videoResumePosList;
    }

    //For Recorded video as Live
    public void setRecorderVideoTimeElapsed(Long timeElapsed, String resLink) {
        try {
            if (!resLink.isEmpty() && timeElapsed > 0) {
                if (!getVideoResumePositionList().isEmpty()) {
                    HashMap<String, Long> videoResumePosList = new HashMap<>();
                    String[] pairs = getVideoResumePositionList().split(",");
                    for (int i = 0; i < pairs.length; i++) {
                        String pair = pairs[i];
                        int index = pair.lastIndexOf("=");
                        String link = pair.substring(0, index);
                        String videoPlayBackPosition = pair.substring(index).replace("=", "");
                        //String[] keyValue = pair.split("=");
                        videoResumePosList.put(link.replace("{", "").trim(),
                                Long.valueOf(videoPlayBackPosition.replace("}", "").trim()));
                    }
                    if (!videoResumePosList.isEmpty()) {
                        videoResumePosList.remove(resLink);
                        videoResumePosList.put(resLink, timeElapsed);
                        this.sharedPrefs.edit().putString(SHARED_RECORDED_VIDEO_AS_LIVE_TIME_ELAPSED, videoResumePosList.toString()).commit();
                    }
                } else {
                    HashMap<String, Long> videoResumePosList = new HashMap<>();
                    videoResumePosList.put(resLink, timeElapsed);
                    this.sharedPrefs.edit().putString(SHARED_RECORDED_VIDEO_AS_LIVE_TIME_ELAPSED, videoResumePosList.toString()).commit();
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Exception while saving video resume position", e);
        }
    }

    public String getRecorderVideoTimeElapsed() {
        return this.sharedPrefs.getString(SHARED_RECORDED_VIDEO_AS_LIVE_TIME_ELAPSED, "");
    }
    //For Recorded video as Live

    public void clearAccessToken() {
        this.sharedPrefs.edit().putString(SHARED_PREFS_ACCESS_TOKEN, "nil").commit();
        this.sharedPrefs.edit().putString(SHARED_PREFS_DATA, "nil").commit();
        Log.e("WonderPublish: ", "Clear Access Token");
        this.accessToken = "nil";
    }

    public boolean getSMSPermissions() {
        return this.smsPermission;
    }

    public void setSMSPermissions(boolean permissions) {
        this.sharedPrefs.edit().putBoolean(SHARED_PREFS_PERM_SMS, permissions).commit();
        this.smsPermission = permissions;
    }

    public String getKey() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_KEYS, "");
    }

    public void setKey(String aeskey) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_KEYS, aeskey).commit();
        this.key = aeskey;
    }

    public boolean getfirstRun() {
        return this.firstRun;
    }

    public void setfirstRun(boolean permission) {
        this.sharedPrefs.edit().putBoolean(SHARED_PREFS_FIRSTRUN, permission).commit();
        this.firstRun = permission;
    }

    public String getServiceSiteId() {
        return serviceSiteId;
    }

    public void setUsername(String token) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_USERNAME, token).apply();
        Log.e("WonderPublish: ", "Update Username to " + token);
        this.username = token;
    }

    public void setMigrationState(boolean state) {
        this.sharedPrefs.edit().putBoolean(SHARED_PREFS_USER_MIGRATION, state).commit();
        Log.e("WonderPublish: ", "Update Username to " + state);
    }


    public String getUserId() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USERID, "");
    }

    public boolean getMigrationState() {
        return wonderPubSharedPrefs.sharedPrefs.getBoolean(SHARED_PREFS_USER_MIGRATION, false);
    }

    public void setUserId(String userId) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_USERID, userId).commit();
        Log.e("WonderPublish: ", "Update UserId to " + userId);
        this.userId = userId;
    }

    public void setUserImage(String token) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_USERIMAGE, token).commit();
        Log.e("WonderPublish: ", "Update UserImage to " + token);
        this.userImage = token;
    }

    private void setTokenTime() {
        this.sharedPrefs.edit().putString(SHARED_PREFS_TOKEN_DATE, getDate()).commit();
    }

    public String getTokenTime() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_TOKEN_DATE, "");
    }

    public String getUserImage() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USERIMAGE, "");
    }


    public synchronized void storeLog(JSONObject jsonObject) {
        String userLog = getUserLog();
        JSONArray storedJson = new JSONArray();
        if (userLog.isEmpty()) {
            storedJson.put(jsonObject);
            this.sharedPrefs.edit().putString(SHARED_PREFS_LOG_USER, storedJson.toString()).commit();
        } else {
            try {
                storedJson = new JSONArray(userLog);
                storedJson.put(jsonObject);
                this.sharedPrefs.edit().putString(SHARED_PREFS_LOG_USER, storedJson.toString()).commit();
            } catch (JSONException e) {
                Log.e(TAG, "JSON user log storing", e);
            }
        }
    }

    public String getUserLog() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_LOG_USER, "");
    }

    public void deleteUserLog() {
        this.sharedPrefs.edit().putString(SHARED_PREFS_LOG_USER, "").commit();
    }

    public String getUsername() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USERNAME, "");
        //return this.username;
    }

    public void setUserLoginType(String token) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_LOGINTYPE, token).commit();
        this.userImage = token;
    }

    public String getUserLoginType() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_LOGINTYPE, "");
    }

    public void setUserType(String value) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_USERTYPE, value).commit();
    }

    public String getUserType() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USERTYPE, "");
    }

    public void setUsermobile(String token) {
        if (token != null && !token.isEmpty() && !token.equalsIgnoreCase("null")) {
            //do nothing
        } else
            token = "";
        this.sharedPrefs.edit().putString(SHARED_PREFS_USERMOBILE, token).commit();
        this.usermobile = token;
    }

    public String getUsermobile() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USERMOBILE, "");
        //return this.usermobile;
    }

    public void setChapterFirstTimeStatus(String id, boolean value) {
        this.sharedPrefs.edit().putBoolean(id, value).commit();
    }

    public void setCookie(String id, String value) {
        this.sharedPrefs.edit().putString(id, value).commit();
    }

    public String getCookie(String id) {
        return wonderPubSharedPrefs.sharedPrefs.getString(id, "");
    }

    public boolean getChapterFirstTimeStatus(String id) {
        return wonderPubSharedPrefs.sharedPrefs.getBoolean(id, false);
    }

    public void setSharedPrefsUserLastReadBooks(String books) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_USER_LAST_READ_BOOKS, books).commit();
    }

    public String getSharedPrefsUserLastReadBooks() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USER_LAST_READ_BOOKS, "");
    }

    public void setSharedPrefsUserLastReadBook(String book) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_USER_LAST_READ_BOOK, book).commit();
    }

    public String getSharedPrefsUserLastReadBook() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USER_LAST_READ_BOOK, "");
    }

    public void setSharedPrefsUserPermDenied(boolean status) {
        this.sharedPrefs.edit().putBoolean(SHARED_PREFS_USER_PERM_DENIED_STATUS, status).commit();
    }

    public boolean getSharedPrefsUserPermDenied() {
        return wonderPubSharedPrefs.sharedPrefs.getBoolean(SHARED_PREFS_USER_PERM_DENIED_STATUS, false);
    }

    public void setSharedPrefsUserLanguagePref(String language) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_USER_LANGUAGE_PREFERENCE, language).commit();
    }

    public String getSharedPrefsUserLanguagePref() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USER_LANGUAGE_PREFERENCE, "en");
    }



    public void setSharedPrefsContentLanguagePref(String language) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_USER_CONTENT_LANGUAGE_PREFERENCE, language).commit();
    }

    public String getSharedPrefsContentLanguagePref() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USER_CONTENT_LANGUAGE_PREFERENCE, "English");
    }

    public void setSharedPrefsQuizDurationPref(String language) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_USER_QUIZ_DURATION_PREFERENCE, language).commit();
    }

    public String getSharedPrefsQuizDurationPref() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USER_QUIZ_DURATION_PREFERENCE, "30");
    }


    public void setSharedPrefsGameSound(boolean status) {
        this.sharedPrefs.edit().putBoolean(SHARED_PREFS_USER_GAME_SOUND, status).commit();
    }

    public boolean getSharedPrefsGameSound() {
        return wonderPubSharedPrefs.sharedPrefs.getBoolean(SHARED_PREFS_USER_GAME_SOUND, false);
    }



    public void setUserEmail(String token) {
        Log.e("WonderPublish: ", "Update Useremail to " + token);
        if (token != null && !token.isEmpty() && !token.equalsIgnoreCase("null")) {
            //do nothing
        } else
            token = "";
        this.sharedPrefs.edit().putString(SHARED_PREFS_USEREMAIL, token).commit();
        this.useremail = token;
    }

    public void setLocation(String location) {
        Log.e("WonderPublish: ", "Update Location to " + location);
        this.sharedPrefs.edit().putString(SHARED_PREFS_USERE_LOCATION, location).commit();
    }

    public String getLocation() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USERE_LOCATION, "");
    }

    public void setScreenResume(String jsonString) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_SCREEN_RESUME, jsonString).commit();
    }

    public String getScreenResume() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_SCREEN_RESUME, "");
    }

    public String getUseremail() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USEREMAIL, "");
        //return this.useremail;
    }

    public String getUserState() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USER_STATE, "");
        //return this.useremail;
    }

    public void setUserDistrict(String district) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_USER_DISTRICT, district).commit();
    }

    public String getUserDistrict() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USER_DISTRICT, "");
    }

    public void setUserState(String state) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_USER_STATE, state).commit();
    }

    public String getGoogleAccountName() {
        return this.googleAccountName;
    }

    public void setGoogleAccountName(String accountName) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_GOOGLE_ACCOUNT_NAME, accountName).commit();
        this.googleAccountName = accountName;
    }

    public String getPendingAPIRequests() {
        if (pendingAPIRequests != null && pendingAPIRequests != "") {
            return pendingAPIRequests;
        } else {
            pendingAPIRequests = this.sharedPrefs.getString(SHARED_PREFS_PENDING_API_REQUESTS, "");
            return pendingAPIRequests;
        }
    }

    public void setPendingAPIRequests(String pendingAPIRequests) {
        if (pendingAPIRequests.equalsIgnoreCase(ConstantsHelper.PENDING_SUBMIT_QUIZ_SCORE_API_REQUEST)) {
            Log.d(TAG, "storing pending api requests: " + pendingAPIRequests);
            this.sharedPrefs.edit().putString(SHARED_PREFS_PENDING_API_REQUESTS, pendingAPIRequests).commit();
            this.pendingAPIRequests = pendingAPIRequests;
        }
    }

    public void savePasswordHashed(String pswd, String salt) {
        if (Encryption.getDefault(getKey(), salt, new byte[16]) != null) {
            if (pswd != null && !pswd.isEmpty() && !pswd.equalsIgnoreCase("null")) {
                String tokenHash = Encryption.getDefault(getKey(), salt, new byte[16])
                        .encryptOrNull(pswd);
                if (tokenHash != null) {
                    this.sharedPrefs.edit().putString(SHARED_PREFS_HSHD, tokenHash).commit();
                }
            }
        }
    }

    public String getPasswordHashed(String salt) {
        String encryptedString = this.sharedPrefs.getString(SHARED_PREFS_HSHD, "");
        if (Encryption.getDefault(getKey(), salt, new byte[16]) != null) {
            if (!encryptedString.isEmpty()) {
                encryptedString = Encryption.getDefault(getKey(), salt, new byte[16])
                        .decryptOrNull(encryptedString);
            }
        }
        return encryptedString;
    }

    private String getDate() {
        LocalDateTime date = LocalDateTime.now();
        DateTimeFormatter fmt = DateTimeFormat.forPattern("dd-MM-yyyy HH:mm:ss");
        return date.toString(fmt);
    }

    public void setLiveTests(String liveVideosString) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_LIVE_TESTS, liveVideosString).commit();
    }

    public String getLiveTests() {
        return this.sharedPrefs.getString(SHARED_PREFS_LIVE_TESTS, "");
    }

    public void setLibraryLoadFailed(boolean failed) {
        this.sharedPrefs.edit().putBoolean(SHARED_PREFS_USER_LIBRARY_LOAD_FAILED, failed).commit();
    }

    public boolean getLibraryLoadFailed() {
        return this.sharedPrefs.getBoolean(SHARED_PREFS_USER_LIBRARY_LOAD_FAILED, false);
    }

    public void setIsShopBookDetailClose(boolean isOpen) {
        this.sharedPrefs.edit().putBoolean(SHARED_PREFS_CLOSE_SHOP_BOOK_DETAIL, isOpen).apply();
    }

    public boolean isShopBookDetailClose() {
        return this.sharedPrefs.getBoolean(SHARED_PREFS_CLOSE_SHOP_BOOK_DETAIL, false);
    }

    public  void saveCurrentAffairsReadingMaterial(String key, boolean isAdded) {
        sharedPrefs.edit().putBoolean(key, isAdded).apply();
    }

    public boolean isSavedCurrentAffairsReadingMaterial(String key) {
        return sharedPrefs.getBoolean(key, false);
    }

    public  void saveSwipeInstruction(boolean isAdded) {
        sharedPrefs.edit().putBoolean(SAVE_SWIPE_INSTRUCTION, isAdded).apply();
    }

    public boolean getSwipeInstruction() {
        return sharedPrefs.getBoolean(SAVE_SWIPE_INSTRUCTION, false);
    }

    public String getFirebaseToken() {
        return sharedPrefs.getString(SHARED_PREFS_FIREBASE_TOKEN, "");
    }

    public  void setFirebaseToken(String token) {
        sharedPrefs.edit().putString(SHARED_PREFS_FIREBASE_TOKEN, token).apply();
    }

    public String getFirebaseNotification() {
        return sharedPrefs.getString(SHARED_PREFS_FIREBASE_NOTIFICATION, "[]");
    }

    public  void setFirebaseNotification(String token) {
        sharedPrefs.edit().putString(SHARED_PREFS_FIREBASE_NOTIFICATION, token).apply();
    }

    public int getFirebaseNotificationCount() {
        return sharedPrefs.getInt(SHARED_PREFS_FIREBASE_NOTIFICATION_COUNT, 0);
    }

    public  void setFirebaseNotificationCount(int count) {
        sharedPrefs.edit().putInt(SHARED_PREFS_FIREBASE_NOTIFICATION_COUNT, count).apply();
    }

    public void setUpdateNotifications(String notifications) {
        sharedPrefs.edit().putString(SHARED_PREFS_UPDATE_NOTIFICATIONS, notifications).apply();
    }

    public String getUpdateNotifications() {
        return sharedPrefs.getString(SHARED_PREFS_UPDATE_NOTIFICATIONS, "[]");
    }


    public int getCafeNotificationCount() {
        return sharedPrefs.getInt(SHARED_PREFS_CAFE_NOTIFICATION_COUNT, 0);
    }

    public  void setCafeNotificationCount(int count) {
        sharedPrefs.edit().putInt(SHARED_PREFS_CAFE_NOTIFICATION_COUNT, count).apply();
    }

    public void setQuizABTestValue(String abTestVal) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_QUIZ_AB_TEST, abTestVal).apply();
    }

    public String getQuizABTestValue() {
        return  this.sharedPrefs.getString(SHARED_PREFS_QUIZ_AB_TEST, "Questions");

    }

    public void setDailyTestABTestValue(String abTestVal) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_DAILY_TEST_AB_TEST, abTestVal).apply();
    }

    public String getDailyTestABTestValue() {

        if(getFlaverValue().equalsIgnoreCase("karnataka"))
            return  this.sharedPrefs.getString(SHARED_PREFS_DAILY_TEST_AB_TEST, "ದೈನಂದಿನ ಪರೀಕ್ಷೆ");
        else
            return  this.sharedPrefs.getString(SHARED_PREFS_DAILY_TEST_AB_TEST, "");
    }

    public void setFlavourValue(String Val) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_FLAVOUR, Val).apply();
    }

    public String getFlaverValue() {
        return  this.sharedPrefs.getString(SHARED_PREFS_FLAVOUR, "");
    }
    //For user preferance capture
    //Level
    public void setSharedPrefsUserLevelPref(String level) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_USER_LEVEL_PREFERNCE, level).commit();
    }

    public String getSharedPrefsUserLevelPref() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USER_LEVEL_PREFERNCE, "");
    }

    //Syllabus
    public void setSharedPrefsUserSyllabusPref(List<String> userSyllabusPref) {
        Gson gson = new Gson();
        String json = gson.toJson(userSyllabusPref);
        this.sharedPrefs.edit().putString(SHARED_PREFS_USER_SYLLABUS_PREFERNCE, json).apply();
    }

    public List<String> getSharedPrefsUserSyllabusPref() {
        Gson gson = new Gson();
        String json = wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USER_SYLLABUS_PREFERNCE, null);
        Type type = new TypeToken<List<String>>() {
        }.getType();
        return null != json ? gson.fromJson(json, type): new ArrayList<>();
    }

    //Grade
    public void setSharedPrefsUserGradePref(List<String> userGradePref) {
        Gson gson = new Gson();
        String json = gson.toJson(userGradePref);
        this.sharedPrefs.edit().putString(SHARED_PREFS_USER_GRADE_PREFERNCE, json).apply();
    }

    public List<String> getSharedPrefsUserGradePref() {
        Gson gson = new Gson();
        String json = wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USER_GRADE_PREFERNCE, null);
        Type type = new TypeToken<List<String>>() {
        }.getType();
        return null != json ? gson.fromJson(json, type): new ArrayList<>();
    }

    //Subject
    public void setSharedPrefsUserSubjectPref(List<String> userGradePref) {
        Gson gson = new Gson();
        String json = gson.toJson(userGradePref);
        this.sharedPrefs.edit().putString(SHARED_PREFS_USER_SUBJECT_PREFERNCE, json).apply();
    }

    public List<String> getSharedPrefsUserSubjectPref() {
        Gson gson = new Gson();
        String json = wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USER_SUBJECT_PREFERNCE, null);
        Type type = new TypeToken<List<String>>() {
        }.getType();
        return null != json ? gson.fromJson(json, type): new ArrayList<>();
    }

    //Sub Subject
    public void setSharedPrefsUserSubSubjectPref(List<String> userGradePref) {
        Gson gson = new Gson();
        String json = gson.toJson(userGradePref);
        this.sharedPrefs.edit().putString(SHARED_PREFS_USER_SUB_SUBJECT_PREFERNCE, json).apply();
    }

    public List<String> getSharedPrefsUserSubSubjectPref() {
        Gson gson = new Gson();
        String json = wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USER_SUB_SUBJECT_PREFERNCE, null);
        Type type = new TypeToken<List<String>>() {
        }.getType();
        return gson.fromJson(json, type);
    }

    public void setLastLogDate(String logDate) {
        this.sharedPrefs.edit().putString("logDate", logDate).apply();
    }

    public String getLastLogDate() {
        return this.sharedPrefs.getString("logDate", "");
    }
    //For user preferance capture

    /*For Filter in Ebooks store*/
    //Level
    public void setSharedPrefsLastFilterLevelSearch(String levelSearchId) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_LAST_FILTER_LEVEL_SEARCH, levelSearchId).apply();
    }

    public String getSharedPrefsLastFilterLevelSearch() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_LAST_FILTER_LEVEL_SEARCH, "");
    }

    public void setQuizFilterLevelSearch(String levelSearchId) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_QUIZ_FILTER_LEVEL_SEARCH, levelSearchId).apply();
    }

    public String getQuizFilterLevelSearch() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_QUIZ_FILTER_LEVEL_SEARCH, "");
    }

    //Syllabus
    public void setSharedPrefsLastFilterSyllabusSearch(String syllabusSearchId) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_LAST_FILTER_SYLLABUS_SEARCH, syllabusSearchId).apply();
    }

    public String getSharedPrefsLastFilterSyllabusSearch() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_LAST_FILTER_SYLLABUS_SEARCH, "");
    }
    //Grade
    public void setSharedPrefsLastFilterGradeSearch(String syllabusSearchId) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_LAST_FILTER_GRADE_SEARCH, syllabusSearchId).apply();
    }

    public String getSharedPrefsLastFilterGradeSearch() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_LAST_FILTER_GRADE_SEARCH, "");
    }

    //Subject
    public void setSharedPrefsLastFilterSubjectSearch(String syllabusSearchId) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_LAST_FILTER_CLASS_SEARCH, syllabusSearchId).apply();
    }

    public String getSharedPrefsLastFilterSubjectSearch() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_LAST_FILTER_CLASS_SEARCH, "");
    }

    public void setPrefrenceValue(String value) {
        this.sharedPrefs.edit().putString(SHARED_PREFS_USER_PREFERNCE, value).apply();
    }

    public String getPrefrenceValue() {
        return wonderPubSharedPrefs.sharedPrefs.getString(SHARED_PREFS_USER_PREFERNCE, "");
    }

    public void setNewsLang(String language) {
        sharedPrefs.edit().putString(SHARED_PREFS_NEWS_LANG_PREF, language).commit();
    }

    public String getNewsLangPref() {
        return sharedPrefs.getString(SHARED_PREFS_NEWS_LANG_PREF, "");
    }

    public void setNewsSource(String source) {
        sharedPrefs.edit().putString(SHARED_PREFS_NEWS_SOURCE_PREF, source).commit();
    }

    public String getNewsSource() {
        return sharedPrefs.getString(SHARED_PREFS_NEWS_SOURCE_PREF, "");
    }

    public void setUserNewsPreference(String preference) {
        sharedPrefs.edit().putString(SHARED_PREFS_USER_NEWS_PREF, preference).commit();
    }

    public String getUserNewsPreference() {
        return sharedPrefs.getString(SHARED_PREFS_USER_NEWS_PREF, "");
    }
}
