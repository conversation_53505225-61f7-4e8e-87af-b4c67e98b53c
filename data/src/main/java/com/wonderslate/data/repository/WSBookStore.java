package com.wonderslate.data.repository;


import static java.net.HttpURLConnection.HTTP_OK;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import android.util.Log;

import androidx.annotation.NonNull;

import com.android.volley.Request;
import com.android.wslibrary.models.cart.CartBook;
import com.android.wslibrary.models.cart.CartBooksResponse;
import com.google.gson.Gson;
import com.wonderslate.data.Utils.Utils;
import com.wonderslate.data.interfaces.WSCallback;
import com.wonderslate.data.interfaces.WSResponseCallback;
import com.wonderslate.data.network.VolleyHelper;
import com.wonderslate.data.network.WSAPIManager;
import com.wonderslate.data.network.Wonderslate;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;

public final class WSBookStore {

   private static final String TAG = "WSLib StoreBooks";
   private HandlerThread handlerThread;
   private final WSAPIManager apiManager;


   public WSBookStore() {
      apiManager = WSAPIManager.getInstance();
   }

   public void getStoreBooksJson(int serviceCode, int index, int requiredBooks, final WSCallback wsCallback) {
      HashMap<String, String> params = new HashMap<>();
      params.put("startIndex", String.valueOf(index));
      params.put("noOfBooksRequired", String.valueOf(requiredBooks));
      String url = apiManager.getServiceURL(serviceCode, params);
      VolleyHelper.getInstance(Wonderslate.getInstance().getContext()).sendRequestToApi(Request.Method.POST, url, null, new VolleyHelper.VolleyCallback() {
         @Override
         public void onSuccessResponse(String result, int responseCode, boolean error) {
            if (responseCode == HTTP_OK) {
               try {
                  JSONObject responseObject = new JSONObject(result);
                  wsCallback.onWSResultSuccess(responseObject, responseCode);
               } catch (JSONException e) {
                  Log.e(TAG, e.getMessage());
               }
            }
         }

         @Override
         public void onErrorResponse(String result, int responseCode) {
            wsCallback.onWSResultFailed(result, responseCode);
         }
      });
   }

   public void getCartDetails(final WSResponseCallback<CartBooksResponse> callback) {
      String url = apiManager.getServiceURL(WSAPIManager.SERVICE_GET_CART_DETAILS);
      VolleyHelper.getInstance(Wonderslate.getInstance().getContext()).sendRequestToApi(Request.Method.GET, url, null, new VolleyHelper.VolleyCallback() {
         @Override
         public void onSuccessResponse(String result, int responseCode, boolean error) {
            try {
               if (responseCode == HTTP_OK) {
                  CartBooksResponse object = parseJSON(result, CartBooksResponse.class);
                  callback.onWSResultSuccess(object, responseCode);
               }
            } catch (Exception e) {
               Log.e(TAG, "onSuccessResponse: ", e);
               callback.onWSResultFailed("Problem while preparing details", responseCode);
            }
         }

         @Override
         public void onErrorResponse(String result, int responseCode) {
            callback.onWSResultFailed(result, responseCode);
         }
      });
   }

   public void removeBookFromCart(String bookId, final WSResponseCallback<Boolean> callback) {
      HashMap<String, String> params = new HashMap<>();
      params.put("bookId", bookId);
      String url = apiManager.getServiceURL(WSAPIManager.SERVICE_DELETE_CART_BOOK, params);
      VolleyHelper.getInstance(Wonderslate.getInstance().getContext()).sendRequestToApi(Request.Method.POST, url, null, new VolleyHelper.VolleyCallback() {
         @Override
         public void onSuccessResponse(String result, int responseCode, boolean error) {
            try {
               if (responseCode == HTTP_OK) {
                  JSONObject response = new JSONObject(result);
                  if ("OK".equalsIgnoreCase(response.optString("status"))) {
                     callback.onWSResultSuccess(true, responseCode);
                  } else {
                     callback.onWSResultSuccess(false, responseCode);
                  }
               }
            } catch (Exception e) {
               callback.onWSResultSuccess(false, responseCode);
            }
         }

         @Override
         public void onErrorResponse(String result, int responseCode) {
            callback.onWSResultFailed(result, responseCode);
         }
      });
   }

   public void addBookToCart(String bookId, String bookType, final WSResponseCallback<JSONObject> callback) {
      HashMap<String, String> params = new HashMap<>();
      params.put("bookId", bookId);
      params.put("bookType", bookType);
      String url = apiManager.getServiceURL(WSAPIManager.SERVICE_ADD_BOOK_IN_CART, params);
      VolleyHelper.getInstance(Wonderslate.getInstance().getContext()).sendRequestToApi(Request.Method.POST, url, null, new VolleyHelper.VolleyCallback() {
         @Override
         public void onSuccessResponse(String result, int responseCode, boolean error) {
            try {
               if (responseCode == HTTP_OK) {
                  JSONObject response = new JSONObject(result);
                  callback.onWSResultSuccess(response, responseCode);
               }
            } catch (Exception e) {
               callback.onWSResultFailed("Problem while adding book in cart", responseCode);
            }
         }

         @Override
         public void onErrorResponse(String result, int responseCode) {
            callback.onWSResultFailed(result, responseCode);
         }
      });
   }

   public void createCartId(List<CartBook> books, Double totalPrice, final WSResponseCallback<JSONObject> callback) {
      String url = apiManager.getServiceURL(WSAPIManager.SERVICE_CREATE_CART_ID);
      JSONObject body = Utils.cartBooksToJSONObject(books, totalPrice);
      VolleyHelper.getInstance(Wonderslate.getInstance().getContext()).sendRequestToApi(Request.Method.POST, url, body, new VolleyHelper.VolleyCallback() {
         @Override
         public void onSuccessResponse(String result, int responseCode, boolean error) {
            try {
               if (responseCode == HTTP_OK) {
                  JSONObject response = new JSONObject(result);
                  callback.onWSResultSuccess(response, responseCode);
               }
            } catch (Exception e) {
               callback.onWSResultFailed("Problem while buying", responseCode);
            }
         }

         @Override
         public void onErrorResponse(String result, int responseCode) {
            callback.onWSResultFailed(result, responseCode);
         }
      });
   }

   public void getCartCount(final WSResponseCallback<Integer> callback) {
      String url = apiManager.getServiceURL(WSAPIManager.SERVICE_GET_CART_COUNT);
      VolleyHelper.getInstance(Wonderslate.getInstance().getContext()).sendRequestToApi(Request.Method.GET, url, null, new VolleyHelper.VolleyCallback() {
         @Override
         public void onSuccessResponse(String result, int responseCode, boolean error) {
            try {
               if (responseCode == HTTP_OK) {
                  JSONObject response = new JSONObject(result);
                  int count = Integer.parseInt(response.optString("userCartCount"));
                  callback.onWSResultSuccess(count, responseCode);
               }
            } catch (Exception e) {
               callback.onWSResultFailed("Problem while adding book in cart", responseCode);
            }
         }

         @Override
         public void onErrorResponse(String result, int responseCode) {
            callback.onWSResultFailed(result, responseCode);
         }
      });
   }

   public static void getAffiliationPrice(String bookId, final WSCallback wsCallback) {
      final Handler responseHandler = new Handler();
      final HandlerThread[] handlerThread = {new HandlerThread("WSAffiliationPriceThread")};
      handlerThread[0].start();
      try {
         final Handler mHandler = new Handler(handlerThread[0].getLooper()) {
            @Override
            public void handleMessage(@NonNull Message msg) {
               handlerThread[0] = null;
               Thread.currentThread().interrupt();
            }
         };
         mHandler.post(() -> {
            HashMap<String, String> params = new HashMap<>();
            params.put("bookId", bookId);
            params.put("siteId", Wonderslate.getInstance().getSiteID());
            String url = WSAPIManager.getInstance().getServiceURL(WSAPIManager.SERVICE_URL_GET_AFFILIATION_PRICE, params);
            VolleyHelper.getInstance(Wonderslate.getInstance().getContext()).sendRequestToApi(Request.Method.GET, url, null, new VolleyHelper.VolleyCallback() {
               @Override
               public void onSuccessResponse(String result, int responseCode, boolean error) {
                  if (responseCode == HTTP_OK) {
                     try {
                        JSONObject responseObject = new JSONObject(result);
                        responseHandler.post(() -> wsCallback.onWSResultSuccess(responseObject, responseCode));
                        mHandler.sendMessage(new Message());
                     } catch (JSONException e) {
                        responseHandler.post(() -> wsCallback.onWSResultFailed(result, responseCode));
                        mHandler.sendMessage(new Message());
                     }
                  }
               }

               @Override
               public void onErrorResponse(String result, int responseCode) {
                  responseHandler.post(() -> wsCallback.onWSResultFailed(result, responseCode));
                  mHandler.sendMessage(new Message());
               }
            });
         });
      } catch (NullPointerException e) {
         wsCallback.onWSResultFailed("", -2);
      }
   }

   public static void getStateCityFromPin(String pinCode, final WSCallback wsCallback) {
      final Handler responseHandler = new Handler();
      final HandlerThread[] handlerThread = {new HandlerThread("WSAffiliationPriceThread")};
      handlerThread[0].start();
      try {
         final Handler mHandler = new Handler(handlerThread[0].getLooper()) {
            @Override
            public void handleMessage(@NonNull Message msg) {
               handlerThread[0] = null;
               Thread.currentThread().interrupt();
            }
         };
         mHandler.post(() -> {
            HashMap<String, String> params = new HashMap<>();
            params.put("pincode", pinCode);
            params.put("siteId", Wonderslate.getInstance().getSiteID());
            String url = WSAPIManager.getInstance().getServiceURL(WSAPIManager.SERVICE_URL_GET_STATE_CITY_FROM_PIN_CODE, params);
            VolleyHelper.getInstance(Wonderslate.getInstance().getContext()).sendRequestToApi(Request.Method.GET, url, null, new VolleyHelper.VolleyCallback() {
               @Override
               public void onSuccessResponse(String result, int responseCode, boolean error) {
                  if (responseCode == HTTP_OK) {
                     try {
                        JSONObject responseObject = new JSONObject(result);
                        responseHandler.post(() -> wsCallback.onWSResultSuccess(responseObject, responseCode));
                        mHandler.sendMessage(new Message());
                     } catch (JSONException e) {
                        responseHandler.post(() -> wsCallback.onWSResultFailed(result, responseCode));
                        mHandler.sendMessage(new Message());
                     }
                  }
               }

               @Override
               public void onErrorResponse(String result, int responseCode) {
                  responseHandler.post(() -> wsCallback.onWSResultFailed(result, responseCode));
                  mHandler.sendMessage(new Message());
               }
            });
         });
      } catch (NullPointerException e) {
         wsCallback.onWSResultFailed("", -2);
      }
   }

   public static void addBillShipAddress(String billName, String billLastName, String billMobile, String billEmail,
                                         String billAddressLine1, String billAddressLine2, String billCity, String billState, String billPincode,
                                         String shipName, String shipLastName, String shipMobile, String shipEmail, String shipAddressLine1, String shipAddressLine2,
                                         String shipCity, String shipState, String shipPincode, final WSCallback wsCallback) {
      final Handler responseHandler = new Handler();
      final HandlerThread[] handlerThread = {new HandlerThread("WSAffiliationPriceThread")};
      handlerThread[0].start();
      try {
         final Handler mHandler = new Handler(handlerThread[0].getLooper()) {
            @Override
            public void handleMessage(@NonNull Message msg) {
               handlerThread[0] = null;
               Thread.currentThread().interrupt();
            }
         };
         mHandler.post(() -> {
            HashMap<String, String> params = new HashMap<>();
            params.put("billName", billName);
            params.put("billLastName", billLastName);
            params.put("billMobile", billMobile);
            params.put("billEmail", billEmail);
            params.put("billAddressLine1", billAddressLine1);
            params.put("billAddressLine2", billAddressLine2);
            params.put("billCity", billCity);
            params.put("billState", billState);
            params.put("billCountry", "India");
            params.put("billPincode", billPincode);
            params.put("shipName", shipName);
            params.put("shipLastName", shipLastName);
            params.put("shipAddressLine1", shipAddressLine1);
            params.put("shipAddressLine2", shipAddressLine2);
            params.put("shipCity", shipCity);
            params.put("shipState", shipState);
            params.put("shipCountry", "India");
            params.put("shipPincode", shipPincode);
            params.put("shipMobile", shipMobile);
            params.put("shipEmail", shipEmail);
            params.put("siteId", Wonderslate.getInstance().getSiteID());
            String url = WSAPIManager.getInstance().getServiceURL(WSAPIManager.SERVICE_URL_ADD_BILL_SHIP_ADDRESS, params);
            VolleyHelper.getInstance(Wonderslate.getInstance().getContext()).sendRequestToApi(Request.Method.GET, url, null, new VolleyHelper.VolleyCallback() {
               @Override
               public void onSuccessResponse(String result, int responseCode, boolean error) {
                  if (responseCode == HTTP_OK) {
                     try {
                        JSONObject responseObject = new JSONObject(result);
                        responseHandler.post(() -> wsCallback.onWSResultSuccess(responseObject, responseCode));
                        mHandler.sendMessage(new Message());
                     } catch (JSONException e) {
                        responseHandler.post(() -> wsCallback.onWSResultFailed(result, responseCode));
                        mHandler.sendMessage(new Message());
                     }
                  }
               }

               @Override
               public void onErrorResponse(String result, int responseCode) {
                  responseHandler.post(() -> wsCallback.onWSResultFailed(result, responseCode));
                  mHandler.sendMessage(new Message());
               }
            });
         });
      } catch (NullPointerException e) {
         wsCallback.onWSResultFailed("", -2);
      }
   }

   public static <T> T parseJSON(String jsonString, Type type) {
      Gson gson = new Gson();
      return gson.fromJson(jsonString, type);
   }

}
