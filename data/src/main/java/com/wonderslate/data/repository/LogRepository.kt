package com.wonderslate.data.repository

import com.wonderslate.commons.Result
import com.wonderslate.data.IRepository
import com.wonderslate.data.network.WSAPIManager
import com.wonderslate.data.service.LogService
import com.wonderslate.data.service.OtpService
import org.json.JSONObject

class LogRepository(private val logService: LogService): IRepository {
    suspend fun logResourceActivity(request: JSONObject): Result<JSONObject> {
        val param = HashMap<String, String>()

        param["id"] = request["id"] as String
        param["source"] = request["source"] as String
        param["viewFrom"] = request["viewFrom"] as String
        param["siteId"] = request["siteId"] as String
        param["action"] = request["action"] as String

        val url = WSAPIManager.getInstance().getServiceURL(WSAPIManager.SERVICE_UPDATE_USER_VIEW, param)
        return logService.submitResourceLog(request, url)
    }
}