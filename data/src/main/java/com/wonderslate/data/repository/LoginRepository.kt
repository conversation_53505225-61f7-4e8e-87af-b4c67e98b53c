package com.wonderslate.data.repository

import com.wonderslate.commons.Result
import com.wonderslate.data.IRepository
import com.wonderslate.data.network.WSAPIManager
import com.wonderslate.data.service.LoginService
import org.json.JSONObject

class LoginRepository(private val loginService: LoginService) :
    IRepository {

    suspend fun login(request: JSONObject) : Result<JSONObject> {
        val params = HashMap<String, String>()
        params["username"] = request["username"] as String
        params["password"] = request["password"] as String
        params["siteId"] = request["siteId"] as String

        val url: String = WSAPIManager.getInstance().getServiceURL(WSAPIManager.SERVICE_LOGIN)
        return loginService.login(request, url)
    }

}