package com.wonderslate.data.repository;

import static java.net.HttpURLConnection.HTTP_OK;

import android.util.Log;

import com.android.volley.Request;
import com.wonderslate.data.Utils.ConstantsHelper;
import com.wonderslate.data.interfaces.WSCallback;
import com.wonderslate.data.network.VolleyHelper;
import com.wonderslate.data.network.WSAPIManager;
import com.wonderslate.data.network.Wonderslate;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;

public final class WSBuyBookOrChapters {

    private static final String TAG = "WSLib Buy";

    public WSBuyBookOrChapters() {}

    public void buyBookOrChapters(String bookId, String chaptersId, String amount, String currancy, String paymentId, String bookType, String paymentFrom, String discountId, final WSCallback wsCallback) {
        buyBookOrChapters("", bookId, chaptersId, amount, currancy, paymentId, bookType, paymentFrom, discountId, wsCallback);
    }
    public void buyBookOrChapters(String cartId, String bookId, String chaptersId, String amount, String currancy, String paymentId, String bookType, String paymentFrom, String discountId, final WSCallback wsCallback) {
        HashMap<String, String> params = new HashMap<>();
        if(cartId != null && !cartId.isEmpty()) {
            params.put(ConstantsHelper.DATA_CART_ID, cartId);
        }
        params.put(ConstantsHelper.DATA_BOOK_ID, bookId);
        params.put(ConstantsHelper.DATA_CHAPTERS_ID, chaptersId);
        params.put(ConstantsHelper.DATA_BOOK_AMT, amount);
        params.put(ConstantsHelper.DATA_BOOK_CURR, currancy);
        params.put(ConstantsHelper.DATA_BOOK_PAY_ID, paymentId);
        params.put(ConstantsHelper.DATA_BOOK_TYPE, bookType);
        params.put(ConstantsHelper.DATA_BOOK_PAYMENT_FROM, paymentFrom);
        if (discountId != null && !discountId.isEmpty()) {
            params.put("discountId", discountId);
        }

        String url = WSAPIManager.getInstance().getServiceURL(WSAPIManager.SERVICE_SUBMIT_PAYMENT, params);
        VolleyHelper.getInstance(Wonderslate.getInstance().getContext()).sendRequestToApi(Request.Method.POST, url, null, new VolleyHelper.VolleyCallback() {
            @Override
            public void onSuccessResponse(String result, int responseCode, boolean error) {
                if (responseCode == HTTP_OK) {
                    try {
                        JSONObject responseObject = new JSONObject(result);
                        wsCallback.onWSResultSuccess(responseObject, responseCode);
                    } catch (JSONException e) {
                        Log.e(TAG, e.getMessage());
                    }
                }
            }

            @Override
            public void onErrorResponse(String result, int responseCode) {
                wsCallback.onWSResultFailed(result, responseCode);
            }
        });
    }


    public void addFreeBook(String bookId, final WSCallback wsCallback) {
        HashMap<String, String> params = new HashMap<>();
        params.put("bookId", bookId);
        String url = WSAPIManager.getInstance().getServiceURL(WSAPIManager.SERVICE_ADD_FREE_BOOK, params);
        VolleyHelper.getInstance(Wonderslate.getInstance().getContext()).sendRequestToApi(Request.Method.POST, url, null, new VolleyHelper.VolleyCallback() {
            @Override
            public void onSuccessResponse(String result, int responseCode, boolean error) {
                if (responseCode == HTTP_OK) {
                    try {
                        JSONObject responseObject = new JSONObject(result);
                        wsCallback.onWSResultSuccess(responseObject, responseCode);
                    } catch (JSONException e) {
                        Log.e(TAG, e.getMessage());
                    }
                }
            }

            @Override
            public void onErrorResponse(String result, int responseCode) {
                wsCallback.onWSResultFailed(result, responseCode);
            }
        });
    }

}
