package com.wonderslate.data.models;

public class GPTPrompts {
    private int id;
    private String promptType;
    private String promptLabel;
    private String prompt;
    private String iconPath;
    private int localIconPath;
    private String iconId;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getPromptType() {
        return promptType;
    }

    public void setPromptType(String promptType) {
        this.promptType = promptType;
    }

    public String getPromptLabel() {
        return promptLabel;
    }

    public void setPromptLabel(String promptLabel) {
        this.promptLabel = promptLabel;
    }

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }

    public String getIconPath() {
        return iconPath;
    }

    public void setIconPath(String iconPath) {
        this.iconPath = iconPath;
    }

    public int getLocalIconPath() {
        return localIconPath;
    }

    public void setLocalIconPath(int localIconPath) {
        this.localIconPath = localIconPath;
    }

    public String getIconId() {
        return iconId;
    }

    public void setIconId(String iconId) {
        this.iconId = iconId;
    }
}
