package com.wonderslate.data.models;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

public class Comments implements Serializable {

    @SerializedName("id")
    @Expose
    private Integer id;
    @SerializedName("dateCreated")
    @Expose
    private String dateCreated;
    @SerializedName("createdBy")
    @Expose
    private String createdBy;
    @SerializedName("description")
    @Expose
    private String description;
    @SerializedName("name")
    @Expose
    private String name;
    @SerializedName("userId")
    @Expose
    private Integer userId;
    @SerializedName("profilepic")
    @Expose
    private String profilepic;
    @SerializedName("username")
    @Expose
    private String username;
    @SerializedName("repliedDetailsByCommentsId")
    @Expose
    private List<ReplayListDate> repliedDetailsByCommentsId = null;
    @SerializedName("repliedCount")
    @Expose
    private String repliedCount;

    private Integer postId;
    private int adapterPosition;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(String dateCreated) {
        this.dateCreated = dateCreated;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getProfilepic() {
        return profilepic;
    }

    public void setProfilepic(String profilepic) {
        this.profilepic = profilepic;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public List<ReplayListDate> getRepliedDetailsByCommentsId() {
        return repliedDetailsByCommentsId;
    }

    public void setRepliedDetailsByCommentsId(List<ReplayListDate> repliedDetailsByCommentsId) {
        this.repliedDetailsByCommentsId = repliedDetailsByCommentsId;
    }

    public String getRepliedCount() {
        return repliedCount;
    }

    public void setRepliedCount(String repliedCount) {
        this.repliedCount = repliedCount;
    }

    public Integer getPostId() {
        return postId;
    }

    public void setPostId(Integer postId) {
        this.postId = postId;
    }

    public int getAdapterPosition() {
        return adapterPosition;
    }

    public void setAdapterPosition(int adapterPosition) {
        this.adapterPosition = adapterPosition;
    }
}
