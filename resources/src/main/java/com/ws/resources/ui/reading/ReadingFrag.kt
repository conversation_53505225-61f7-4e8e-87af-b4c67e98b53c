package com.ws.resources.ui.reading

import android.annotation.SuppressLint
import android.content.ActivityNotFoundException
import android.content.Intent
import android.content.res.Configuration
import android.content.res.Configuration.ORIENTATION_LANDSCAPE
import android.content.res.Configuration.ORIENTATION_PORTRAIT
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.text.TextUtils
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.webkit.MimeTypeMap
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.SeekBar
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import com.downloader.*
import com.wonderslate.data.network.Wonderslate
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.ws.commons.enums.Flavor
import com.ws.commons.extensions.byteToMB
import com.ws.commons.extensions.toDecoded
import com.ws.commons.interfaces.*
import com.ws.commons.models.NetworkConfig
import com.ws.core_ui.base.BaseFragmentWithListener
import com.ws.core_ui.extensions.*
import com.ws.core_ui.utils.TempConfig
import com.ws.database.room.entity.Chapter
import com.ws.database.room.entity.ReadData
import com.ws.database.room.entity.Resource
import com.ws.resources.R
import com.ws.resources.data.enums.ReadType
import com.ws.resources.data.interfaces.ReadingImageUpdateStatusProvider
import com.ws.resources.data.models.AnnotationsRequest
import com.ws.resources.data.models.ReadingFragConfig
import com.ws.resources.databinding.LayoutReadingFragBinding
import com.ws.resources.utils.showView
import com.ws.resources.utils.hideView
import com.downloader.PRDownloader
import com.downloader.Priority
import com.downloader.Status
import com.downloader.OnDownloadListener
import com.ws.resources.ui.*
import com.ws.resources.ui.reading.utils.Encryption
import com.ws.resources.ui.reading.utils.ReadJSInterface
import com.ws.resources.ui.reading.utils.ReadUnzipHelper
import kotlinx.coroutines.*
import org.json.JSONException
import org.json.JSONObject
import org.jsoup.Jsoup
import org.koin.android.ext.android.inject
import java.io.*


class ReadingFrag: BaseFragmentWithListener<LayoutReadingFragBinding, ReadingFrag.OnReadingFragInteractionListener>() {

    private var readingFragConfig: ReadingFragConfig? = null

    private var currentReadType = ReadType.UNSPECIFIED

    private val viewModel by inject<ReadingFragViewModel>()

    private val flavorConfig by inject<FlavorConfig>()

    private val tokenProvider by inject<TokenProvider>()

    private val updateNotificationManager by inject<UpdateNotificationManager>()

    private val encryptionKeysProvider by inject<EncryptionKeysProvider>()

    private val readingImageUpdateStatusManager by inject<ReadingImageUpdateStatusProvider>()

    private lateinit var resource: Resource

    private lateinit var chapter: Chapter

    private val networkConfig: NetworkConfig by inject()

    private val readUnzipHelper: ReadUnzipHelper by lazy {
        ReadUnzipHelper()
    }

    private var decryptedPdfFile: File? = null
    private var decryptedPdfFileUri: Uri? = null

    private var epubBase64String: String = ""

    private var isWebViewLoaded = false

    private var renderHighlightString = ""
    private var readNotesJson = JSONObject()

    private var inWebSearchMode = false
    private var inFlashCardMode = false
    private var webLink: String = ""
    private var flCardQuote: String = ""

    private var downloader: Int = 0

    override fun inflateViewBinding(
            inflater: LayoutInflater,
            container: ViewGroup?
    ): LayoutReadingFragBinding {
        return LayoutReadingFragBinding.inflate(inflater, container, false)
    }

    override fun initArguments(bundle: Bundle?) {
        readingFragConfig = bundle?.getSerializable(ARG_READING_CONFIG) as? ReadingFragConfig
    }

    override fun initView() {
        setupClicks()
        setupTextFormatterUI()
        initObservers()
        logReadingActivity()
    }

    private fun logReadingActivity() {

    }

    override fun load() {
        when (Build.VERSION.SDK_INT) {
            in 1..28 -> listener?.checkForStoragePermission()
            else -> {
                onStoragePermissionGranted()
            }
        }
    }

    fun onStoragePermissionGranted() {
        readingFragConfig?.let { config ->
            fragScope?.launch {
                var res = viewModel.findCachedResource(config.resId)

                if (res == null && deepLinkResource != null) {
                    res = deepLinkResource
                }

                if(res == null) {
                    showToast(OPEN_ERROR_MSG)
                    listener?.onBackBtnClicked()
                    return@launch
                }

                // Init resource
                resource = res

                var chap = viewModel.findCachedChapter(resource.topicId)

                if (chap == null && deepLinkChapter != null) {
                    chap = deepLinkChapter
                }

                if(chap == null) {
                    showToast(OPEN_ERROR_MSG)
                    listener?.onBackBtnClicked()
                    return@launch
                }

                // Init chapter
                chapter = chap

                if(tokenProvider.isLoggedIn() && !config.isPreviewMode) {
                    //TODO: show local annotations and also start sync of local Annotations with Server
                    viewModel.getAnnotations(AnnotationsRequest(resource.id, chapter.bookId, resource.ebupChapterLink ?: ""))
                }

                updateReadType()

                configureWebViews()

                binding?.wvReading?.settings?.apply {
                    loadWithOverviewMode = resource.isEbook()
                    useWideViewPort = resource.isEbook()
                    setSupportZoom(true)
                }

                setupToolbar()

                binding?.lvCenter?.showView()

                startLoadingReading()

            }
        }

    }

    private fun setupToolbar() {
        binding?.tvResourceName?.text = if(inWebSearchMode) "Web search" else resource.resName.toDecoded()
        binding?.btnTextFormatter?.hideView()
        binding?.btnNotes?.visibility(readingFragConfig?.isPreviewMode != true && !inWebSearchMode)
    }

    private fun setupClicks() {
        binding?.btnBack?.setOnClickListener {
            listener?.onBackBtnClicked()
        }

        binding?.btnNotes?.setOnClickListener {
            listener?.onOpenNotesList(AnnotationsRequest(
                    resource.id,
                    chapter.bookId,
                    resource.ebupChapterLink ?: "",
                    forceUpdate = viewModel.isConnected() && currentReadType == ReadType.SECURE_PDF
            ))
        }

        binding?.btnTextFormatter?.setOnClickListener {
            // TODO: show formatting tools
        }

        binding?.wvReading?.setOnLongClickListener {
            return@setOnLongClickListener readingFragConfig?.isPreviewMode == true
        }

        binding?.wvReadingPdf?.setOnLongClickListener {
            binding?.wvReadingPdf?.evaluateJavascript("checkConnection(" + viewModel.isConnected() + ")", null)
            return@setOnLongClickListener readingFragConfig?.isPreviewMode == true
        }

        binding?.layoutTextFormatter?.textSizeIncrease?.setOnClickListener {
            // TODO: increase text size
        }

        binding?.layoutTextFormatter?.textSizeDecrease?.setOnClickListener {
            // TODO: Decrease text size
        }

        binding?.layoutTextFormatter?.darkbackbtn?.setOnClickListener {
            // TODO: Switch to Dark theme
            binding?.layoutTextFormatter?.markBlack?.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorAccent))
            binding?.layoutTextFormatter?.markWhite?.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.light_grey))
        }

        binding?.layoutTextFormatter?.whitebackbtn?.setOnClickListener {
            // TODO: Switch to Light theme
            binding?.layoutTextFormatter?.markBlack?.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.light_grey))
            binding?.layoutTextFormatter?.markWhite?.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorAccent))
        }

        binding?.bookGptBtn?.setOnClickListener {
            listener?.onOpenGPTChat(resource, chapter, "")
        }
    }

    private fun initObservers() {
        viewModel.annotations.collectLatestWithLifecycle(this) {
            readNotesJson = it
            updateHighlightString()
            updateWebViewWithHighlights()
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun configureWebViews() {

        binding?.wvReadingPdf?.visibility(isSecurePdfOrEPub())
        binding?.wvReading?.visibility(!isSecurePdfOrEPub())

        binding?.wvReading?.apply {
            configure(
                    enableZoomControls = currentReadType != ReadType.E_PUB,
                    isDebugMode = flavorConfig.isDebugMode
            ) {
                if (it == 100 && !isWebViewLoaded) {
                    isWebViewLoaded = true
                    binding?.lvCenter?.hideView()
                }
            }
            configureForCaching(activity = requireActivity(), isConnected = viewModel.isConnected())
            changeColor(ContextCompat.getColor(requireContext(), R.color.white))

            val readInterface = createReadJSInterface(this)
            readInterface.connectInterface()

            webViewClient = object: WebViewClient() {
                override fun onPageFinished(view: WebView?, url: String?) {
                    configure(
                            enableZoomControls = currentReadType != ReadType.E_PUB,
                            isDebugMode = flavorConfig.isDebugMode
                    )
                }
            }
        }

        binding?.wvReadingPdf?.apply {
            configureForSecurePdf {
                if (it == 100 && !isWebViewLoaded) {
                    try {
                        when(currentReadType) {
                            ReadType.E_PUB -> {
                                val chapterLinkData = resource.ebupChapterLink?.split("#")?.get(0) ?: ""
                                if (WonderPubSharedPrefs.getInstance(context).isBookInLibrary == true) {
                                    evaluateJavascript("displayEpub('$epubBase64String', 0, \"English\", '$renderHighlightString', '$chapterLinkData', 'false')", null)
                                }
                                else {
                                    evaluateJavascript("displayEpub('$epubBase64String', 0, \"English\", '$renderHighlightString', '$chapterLinkData', 'true')", null)
                                }
                            }

                            ReadType.SECURE_PDF -> {
                                val pageValue: Int = getLastReadPDFPage(resource.id)
                                evaluateJavascript(
                                        "displayPdfWithHighlight('$decryptedPdfFileUri','$readNotesJson','$pageValue')",
                                        null
                                )
                                evaluateJavascript("checkConnection(" + viewModel.isConnected() + ")", null)
                            }
                            else -> {}
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "configureWebViews: ", e)
                    }
                    isWebViewLoaded = true
                    binding?.lvCenter?.hideView()
                }
            }

            val readInterface = createReadJSInterface(this)
            readInterface.connectInterface()

            handleUrlLoading()
        }

        binding?.wvSearch?.apply {
            configure(
                    enableZoomControls = true,
                    isDebugMode = flavorConfig.isDebugMode
            ) {
                if(it == 100) {
                    binding?.lvCenter?.hideView()
                }
            }

            configureForCaching(requireActivity(), viewModel.isConnected())

            changeColor(ContextCompat.getColor(requireContext(), R.color.white))

            val readInterface = createReadJSInterface(this)
            readInterface.connectInterface()

            handleUrlLoading()
        }

        binding?.wvFlashCard?.apply {
            configure(
                    enableZoomControls = true,
                    isDebugMode = flavorConfig.isDebugMode
            ) {
                if(it == 100) {
                    evaluateJavascript("createNewSet('$flCardQuote')", null)
                    binding?.lvCenter?.hideView()
                }
            }

            configureForCaching(requireActivity(), viewModel.isConnected())

            changeColor(ContextCompat.getColor(requireContext(), R.color.white))

            val readInterface = createReadJSInterface(this)
            readInterface.connectInterface()

            handleUrlLoading()
        }

    }

    private fun getLastReadPDFPage(urlId: String): Int {
        val lastReadPageMap = stringToHashMap(WonderPubSharedPrefs.getInstance(context).getSharedPrefsLastReadPage())
        return if (lastReadPageMap.isEmpty()) {
            0
        } else {
            try {
                lastReadPageMap[resource.id]!!
            } catch (e: java.lang.Exception) {
                0
            }
        }
    }

    fun stringToHashMap(str: String): java.util.HashMap<String, Int> {
        val hashMap = java.util.HashMap<String, Int>()
        if (!str.isEmpty()) {
            // Split the input string by a delimiter (e.g., comma)
            val keyValuePairs = str.split(",".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
            for (pair in keyValuePairs) {
                val keyValue = pair.split(":".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                if (keyValue.size == 2) {
                    val key = keyValue[0].trim { it <= ' ' }
                    val value = keyValue[1].trim { it <= ' ' }.toInt()
                    hashMap[key] = value
                }
            }
        }
        return hashMap
    }

    fun hashMapToString(hashMap: HashMap<String, Int>): String? {
        val sb = java.lang.StringBuilder()
        if (!hashMap.isEmpty()) {
            for (key in hashMap.keys) {
                val value = hashMap[key]!!
                sb.append(key).append(":").append(value).append(",")
            }

            // Remove the trailing comma
            if (sb.length > 0) {
                sb.deleteCharAt(sb.length - 1)
            }
        }
        return sb.toString()
    }


    private fun createReadJSInterface(webView: WebView?): ReadJSInterface {
        return ReadJSInterface(webView, object: ReadJSInterface.ReadingCallbacks{
            override fun onAnnotationCall(annotationString: String?, action: String?) {
                var jsonObject: JSONObject? = null
                if (annotationString?.isNotEmpty() == true) try {
                    if (currentReadType == ReadType.E_PUB) {
                        jsonObject = JSONObject(annotationString)
                        val dataObject: JSONObject = jsonObject
                        //{"type":"POST","dataType":"json","data":"{\"quote\":\"founded\",\"ranges\":[{\"start\":\"\/p[3]\",\"startOffset\":9,\"end\":\"\/p[3]\",
                        // \"endOffset\":16}],\"uri\":\"0\",\"bookId\":\"${params.bookId}\"}","contentType":"application\/json; charset=utf-8"}
                        if (dataObject.has("quote") && dataObject.getString("quote").isNotEmpty()) {
                            val localId = viewModel.getAnnotationLocalId(dataObject.getString("ranges"))
                            if (localId.isNotEmpty()) {
                                jsonObject.put("id", localId)
                            }
                            handleAnnotation(jsonObject, action ?: "")
                        }
                    } else {
                        jsonObject = JSONObject(annotationString)
                        if (jsonObject.has("quote") && jsonObject.getString("quote").isNotEmpty()) {
                            val localId = viewModel.getAnnotationLocalId(jsonObject.getString("ranges"))
                            if (localId.isNotEmpty()) {
                                jsonObject.put("id", localId)
                            }

                            handleAnnotation(jsonObject, action ?: "")
                        }
                    }
                } catch (e: JSONException) {
                    Log.e(TAG, "onAnnotationCall: ", e)
                }
            }

            override fun onScrolledBottom() {
                //TODO("Not yet implemented")
            }

            override fun onWebSearchTap(query: String?) {
                // TODO: listener?.onWebSearch(resource, query ?: "")
                search(query)
            }

            override fun onAnnotationTap(state: Boolean) {
                //TODO("Not yet implemented")
                Log.e(TAG, "onAnnotationCall: state - $state")
            }

            override fun onScrollDown() {
                activity?.runOnUiThread{
                    Handler(Looper.getMainLooper()).postDelayed({
                        if (resources.configuration.orientation == ORIENTATION_LANDSCAPE) {
                            binding?.clHeader?.visibility = View.VISIBLE
                        }
                    }, 1000)
                }
            }

            override fun onScrollUp() {
                activity?.runOnUiThread{
                    Handler(Looper.getMainLooper()).postDelayed({
                        if (resources.configuration.orientation == ORIENTATION_LANDSCAPE) {
                            binding?.clHeader?.visibility = View.GONE
                        }
                    }, 1000)
                }
            }

            override fun savePDFPage(value: Int) {
                //Store pdf page in shared prefs
                val lastReadPageMap: HashMap<String, Int> = stringToHashMap(WonderPubSharedPrefs.getInstance(context).sharedPrefsLastReadPage)
                lastReadPageMap[resource.id] = value
                WonderPubSharedPrefs.getInstance(context).sharedPrefsLastReadPage = hashMapToString(lastReadPageMap)
            }

            override fun onFlashcardTap(quote: String) {
                activity?.runOnUiThread {
                    Handler(Looper.getMainLooper()).post {
                        flCardQuote = quote
                        openFlashCardWebView("resources/createFlashCards")
                    }
                }
            }

            override fun onFlashCardAdded() {
                activity?.runOnUiThread {
                    Handler(Looper.getMainLooper()).post {
                        onBackPressed()
                    }
                }
            }

        })
    }

    fun openFlashCardWebView(url: String) {
        binding?.wvFlashCard?.let {
            inFlashCardMode = true

            binding?.lvCenter?.showView()

            webLink = Wonderslate.SERVICE + url + "?tokenId=" + WonderPubSharedPrefs.getInstance(context).accessToken + "&appType=android&" +
                    "chapterId=" + resource.topicId + "&prepjoyApp=true" + "&siteId=" + Wonderslate.getInstance().siteID

            setupToolbar()

            it.showView()
            it.loadUrl(webLink)
            binding?.wvReading?.hideView()
            binding?.wvReadingPdf?.hideView()
            binding?.wvSearch?.hideView()
            it.settings.mixedContentMode = WebSettings.MIXED_CONTENT_COMPATIBILITY_MODE
        }

    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        if (newConfig.orientation == ORIENTATION_PORTRAIT) {
            binding?.clHeader?.visibility = View.VISIBLE
        }
    }

    private fun handleAnnotation(annotateJson: JSONObject, action: String) {
        fragScope?.launch {
            when(action) {
                "notesNew", "notesEdit" -> {
                    //{"quote":"Around","ranges":[{"start":"\/h2[1]","startOffset":15,"end":"\/h2[1]","endOffset":21}]}
                    val listener: NotesBottomSheetFragment.OnNoteEditListener = object : NotesBottomSheetFragment.OnNoteEditListener {
                        override fun onNew(note: String) {
                            try {
                                annotateJson.put("text", note)
                                handleAnnotation(annotateJson, "create")
                            } catch (e: JSONException) {
                                Log.e(TAG, "handleAnnotation: ", e)
                            }
                        }

                        override fun onUpdate(note: String) {
                            try {
                                annotateJson.put("text", note)
                                handleAnnotation(annotateJson, "update")
                            } catch (e: JSONException) {
                                Log.e(TAG, "handleAnnotation: ", e)
                            }
                        }

                        override fun onDismiss() {
                            updateWebViewWithHighlights()
                        }
                    }

                    NotesBottomSheetFragment.showWithAnnotation(
                            fManager = childFragmentManager,
                            selected = annotateJson,
                            listener = listener
                    )
                }

                "create" -> {
                    if(viewModel.isConnected()) {
                        binding?.lvCenter?.showView()
                        val isSuccess = viewModel.createAnnotation(AnnotationsRequest(
                                resId = resource.id,
                                bookId = chapter.bookId,
                                epubChapterLink = resource.ebupChapterLink ?: "",
                                annotationJsonString = annotateJson.toString()
                        ))
                        if(isSuccess) {
                            showToast(ANNOTATION_UPDATE_SUCCESS)
                        } else {
                            showToast(PROBLEM_UPDATING_ANNOTATION)
                        }
                        binding?.lvCenter?.hideView()
                    } else {
                        showToast("Device need to be online for highlight and notes feature")
                    }
                }

                "update" -> {
                    if(viewModel.isConnected()) {
                        binding?.lvCenter?.showView()
                        val isSuccess = viewModel.updateAnnotation(AnnotationsRequest(
                                resId = resource.id,
                                bookId = chapter.bookId,
                                epubChapterLink = resource.ebupChapterLink ?: "",
                                annotationJsonString = annotateJson.toString()
                        ))
                        if(isSuccess) {
                            showToast(ANNOTATION_UPDATE_SUCCESS)
                        } else {
                            showToast(PROBLEM_UPDATING_ANNOTATION)
                        }
                        binding?.lvCenter?.hideView()
                    } else {
                        showToast("Device need to be online for highlight and notes feature")
                    }
                }

                "pdfTextUpdate" -> {
                    if(viewModel.isConnected()) {
                        binding?.lvCenter?.showView()
                        val isSuccess = viewModel.updateAnnotation(AnnotationsRequest(
                                resId = resource.id,
                                bookId = chapter.bookId,
                                epubChapterLink = resource.ebupChapterLink ?: "",
                                annotationJsonString = annotateJson.toString()
                        ))
                        if(isSuccess) {
                            showToast(ANNOTATION_UPDATE_SUCCESS)
                        } else {
                            showToast(PROBLEM_UPDATING_ANNOTATION)
                        }
                        binding?.lvCenter?.hideView()
                    } else {
                        showToast("Device need to be online for highlight and notes feature")
                    }
                }

                "delete" -> {
                    if(!annotateJson.has("id"))
                        return@launch

                    if(viewModel.isConnected()) {
                        binding?.lvCenter?.showView()
                        val isSuccess = viewModel.deleteAnnotation(AnnotationsRequest(
                                resId = resource.id,
                                bookId = chapter.bookId,
                                epubChapterLink = resource.ebupChapterLink ?: "",
                                annotationJsonString = annotateJson.toString()
                        ))
                        if(isSuccess) {
                            showToast(ANNOTATION_UPDATE_SUCCESS)
                        } else {
                            showToast(PROBLEM_UPDATING_ANNOTATION)
                        }
                        binding?.lvCenter?.hideView()
                    } else {
                        showToast("Device need to be online for highlight and notes feature")
                    }
                }
            }
        }
    }

    private fun search(query: String?) = fragScope?.launch(Dispatchers.Main) {
        binding?.wvSearch?.let {
            if(it.isVisible)
                return@launch

            inWebSearchMode = true

            setupToolbar()

            it.showView()
            binding?.wvReading?.hideView()
            binding?.wvReadingPdf?.hideView()
            it.settings.mixedContentMode = WebSettings.MIXED_CONTENT_COMPATIBILITY_MODE
            it.loadUrl("https://www.google.com/search?q=$query")
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setupTextFormatterUI() {

        binding?.layoutTextFormatter?.hideView()

        binding?.layoutTextFormatter?.let { formatter ->

            formatter.brightnessSeekBar.apply {
                max = 100
                try {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        setProgress(
                                Settings.System.getInt(
                                        requireActivity().contentResolver,
                                        Settings.System.SCREEN_BRIGHTNESS
                                ), true
                        )
                    } else {
                        progress = Settings.System.getInt(
                                requireActivity().contentResolver,
                                Settings.System.SCREEN_BRIGHTNESS
                        )
                    }
                } catch (e: Settings.SettingNotFoundException) {
                    Log.e(TAG, "setupTextFormatterUI: ", e)
                }
                jumpDrawablesToCurrentState()
                incrementProgressBy(5)

                setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
                    override fun onProgressChanged(seekBar: SeekBar, progress: Int, b: Boolean) {
                        val backLightValue = progress.toFloat() / 100
                        val layoutParams: WindowManager.LayoutParams = requireActivity().window.attributes
                        layoutParams.screenBrightness = backLightValue
                        requireActivity().window.attributes = layoutParams
                    }

                    override fun onStartTrackingTouch(seekBar: SeekBar) {}
                    override fun onStopTrackingTouch(seekBar: SeekBar) {}
                })
            }
        }

        binding?.wvReading?.setOnTouchListener { view, motionEvent ->
            //TODO("Use to hide formatting UI and help detect if user interaction")
            when(motionEvent.action) {
                MotionEvent.ACTION_MOVE -> {
                    binding?.layoutTextFormatter?.hideView()
                }
            }
            return@setOnTouchListener false
        }
    }

    private suspend fun startLoadingReading() {
        val isReadUpdated = updateNotificationManager.isResourceUpdated(resource.id)
        when(currentReadType) {

            ReadType.ZIPPED_PDF -> {
                // Start downloading reading zip or load from cached if available
                loadZippedPdf(isReadUpdated)
            }

            ReadType.E_PUB -> {
                // Start downloading epub or load from cached if available
                loadEpub(isReadUpdated)
            }

            ReadType.SECURE_PDF, ReadType.NON_SECURE_PDF -> {
                // NON_SECURE_PDF: If cached PDF is available then load or
                // SECURE_PDF: Start downloading and load or load from cached
                loadPdf(isReadUpdated)
            }

            else -> {
                showToast(OPEN_ERROR_MSG)
                listener?.onBackBtnClicked()
            }
        }
    }

    /**
     *  Downloads, process, cache and load reading zip
     *
     *  @param isUpdated if true zip will be re-downloaded
     */
    private suspend fun loadZippedPdf(isUpdated: Boolean = false) {
        val downloadUrl = viewModel.getZipDownloadUrl(
                isLoggedIn = tokenProvider.isLoggedIn(),
                isPreviewMode = readingFragConfig?.isPreviewMode ?: true,
                resource = resource
        )

        val resLink = resource.resLink.replace(" ", "").trim().toDecoded()
        val fileName = resLink.guessFileName()

        val path = "${resource.id}_${resLink}"

        val unZipStoragePath = createPath(UNZIPPED_EPUB_FILES, path)
        val unZippedFile = File(unZipStoragePath)

        if(isUpdated) {
            if(unZippedFile.exists())
                unZippedFile.deleteRecursively()
            readingImageUpdateStatusManager.setSVGUpdated(resource.id, false)
            readingImageUpdateStatusManager.setImageReplaced(resource.id, false)
            viewModel.deleteReadData(resource.id)
            updateNotificationManager.onResourceUpdated(resource.id)
        }

        val zipStoragePath = createPath(DOWNLOADED_FILES, path)
        val zippedFile = File(zipStoragePath)

        val cachedData: ReadData? = viewModel.getCachedReadData(resource.id)

        fun show(htmlContent: String) {
            binding?.wvReading?.loadDataWithBaseURL("file:///android_asset/fonts/", htmlContent, "text/html",
                    "UTF-8", "about:blank")
        }

        fun extractReadZipAndLoad() = fragScope?.launch {
            val extractData = readUnzipHelper.unzip(zipStoragePath, unZipStoragePath)
            val htmlContent = withContext(Dispatchers.IO) {
                var html = File(extractData.htmlFilePath).readString()
                html = html.replaceImageExtension(extractData.imageLocation)
                html = html.removeExtraCharacters()
                html = html.replaceImagePaths(extractData.imageLocation)
                html = html.replaceFontPaths(extractData.fontsLocation)
                html = html.replaceStylesPaths(extractData.stylesLocations)
                html = html.replaceTable()
                html
            }

            binding?.alertDialogLayout?.hideView()

            // start loading
            show(htmlContent.offlineKatexConfig(renderHighlightString).replaceHeader())

            // encrypt htmlContent then store
            val encryptedHtmlContent = htmlContent.encryptOrEmpty()
            viewModel.cacheReadData(ReadData(resource.id, chapter.bookId, encryptedHtmlContent))

            try {
                // Delete extra files after extraction process
                withContext(Dispatchers.IO) {
                    zippedFile.deleteRecursively()
                    File(extractData.htmlFilePath).delete()
                }
            } catch (e: Exception) {
                Log.e(TAG, "extractReadZipAndLoad: ", e)
            }
        }

        when {

            cachedData != null && !TextUtils.isEmpty(cachedData.data) -> {
                val decryptedHtmlContent = cachedData.data.decryptOrEmpty()
                show(decryptedHtmlContent.offlineKatexConfig(renderHighlightString).replaceHeader())
            }

            zippedFile.exists() -> {
                extractReadZipAndLoad()
            }
            else -> {
                if(unZippedFile.exists())
                    unZippedFile.deleteRecursively()

                download(downloadUrl, zipStoragePath.replace(fileName, ""), fileName) {
                    // Download completed so start extraction process
                    if (zippedFile.exists()) {
                        extractReadZipAndLoad()
                    }
                    else {
                        Toast.makeText(context, "Problem while getting reading material. Please try again later.", Toast.LENGTH_SHORT).show()
                        binding?.alertDialogLayout?.hideView()
                    }
                }
            }
        }

        binding?.lvCenter?.hideView()

    }

    /**
     *  Downloads, process, cache and load PDFs (Secure/Non secure)
     *
     *  @param isUpdated if true pdf will be re-downloaded
     */
    private fun loadPdf(isUpdated: Boolean) {
        val downloadUrl = viewModel.getPDFDownloadUrl(resource.id)

        val resLink = resource.resLink.replace(" ", "").trim().toDecoded()
        val fileName = resLink.guessFileName()

        val path = "${resource.id}_${resLink}"

        val encryptedPdfPath = createPath(UNZIPPED_EPUB_FILES, path)
        val encryptedPdf = File(encryptedPdfPath)

        if(isUpdated && encryptedPdf.exists()) {
            encryptedPdf.delete()
            updateNotificationManager.onResourceUpdated(resource.id)
        }

        val pdfDownloadPath = createPath(DOWNLOADED_FILES, path)
        val pdfFile = File(pdfDownloadPath)

        fun getDecryptedFileUri(): Uri? {
            val decryptedFileName: String = StringBuilder(fileName).insert(fileName.length - 4, "_dcy").toString()
            decryptedPdfFile = File(encryptedPdfPath.replace(fileName, decryptedFileName))

            if (!decryptedPdfFile!!.exists()) {
                Log.e("ReadingFrag", "Decrypted file does ot exist")
            }

            // Decrypt the `encryptedPdf` and write to `decryptedPdfFile`
            encryptedPdf.decrypt(encryptionKeysProvider.getReadPDFEncryptionKey(), decryptedPdfFile!!)

            return FileProvider.getUriForFile(requireContext(), requireActivity().application.packageName + ".provider", decryptedPdfFile!!)
        }

        fun show() {
            decryptedPdfFileUri = getDecryptedFileUri()

            if(currentReadType == ReadType.SECURE_PDF) {
                binding?.wvReadingPdf?.loadUrl("file:///android_asset/pdf_library/web/viewer.html?bookLang=${TempConfig.isEnglishBook}")
            } else {
                // open out of app
                try {
                    val type = MimeTypeMap.getSingleton().getMimeTypeFromExtension(resLink.guessExtension(fallback = "pdf"))
                    val intent = Intent()
                    intent.action = Intent.ACTION_VIEW
                    intent.setDataAndType(decryptedPdfFileUri, type)
                    intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    startActivity(intent)
                    listener?.onBackBtnClicked()
                } catch (e: ActivityNotFoundException) {
                    showToast("Please install a PDF Reader before opening this reading material")
                    listener?.onBackBtnClicked()
                }
            }
        }

        fun encryptPdfAndLoad() = fragScope?.launch {
            // encrypt file and delete original file
            pdfFile.encrypt(encryptionKeysProvider.getReadPDFEncryptionKey(), encryptedPdf)
            pdfFile.delete()

            // Load the Pdf
            show()

            binding?.alertDialogLayout?.hideView()
        }

        when {
            encryptedPdf.exists() -> {
                show()
            }

            else -> {
                download(downloadUrl, pdfDownloadPath.replace(fileName, ""), fileName) {
                    // Download completed so start encryption process
                    encryptPdfAndLoad()
                }
            }
        }
    }

    /**
     *  Downloads, process, cache and load EPUB
     *
     *  @param isUpdated if true epub will be re-downloaded
     */
    private suspend fun loadEpub(isUpdated: Boolean) {
        val downloadUrl = viewModel.getZipDownloadUrl(
                isLoggedIn = tokenProvider.isLoggedIn(),
                isPreviewMode = readingFragConfig?.isPreviewMode ?: true,
                resource = resource
        )

        val resLink = resource.resLink.replace(" ", "").trim().toDecoded()
        val fileName = resLink.guessFileName()

        val path = "${resource.id}_${resLink}"

        val isSingleEpub = !resource.ebupChapterLink.isNullOrBlank()

        if(isUpdated) {
            if(isSingleEpub)
                viewModel.deleteReadDataByBookId(chapter.bookId)
            else
                viewModel.deleteReadData(resource.id)
            updateNotificationManager.onResourceUpdated(resource.id)
        }

        val zipStoragePath = createPath(DOWNLOADED_FILES, path)
        val zippedFile = File(zipStoragePath)

        val cachedData: ReadData? = if(isSingleEpub)
            viewModel.getCachedReadDataByBookId(chapter.bookId)
        else
            viewModel.getCachedReadData(resource.id)

        fun show() {
            binding?.wvReadingPdf?.loadDataWithBaseURL("null", epubHtmlTemplate, "text/html", "UTF-8", "about:blank")
        }

        fun extract() = fragScope?.launch {
            epubBase64String = zippedFile.toEncodedBase64String()
            zippedFile.delete()
            val encryptedEpubFile: File = File(zipStoragePath.replace(fileName, ""), fileName)
            val stream: FileOutputStream = withContext(Dispatchers.IO) {
                FileOutputStream(encryptedEpubFile)
            }
            stream.use { stream ->
                stream.write(epubBase64String.toByteArray())
            }

            try {
                viewModel.cacheReadData(ReadData(resource.id, chapter.bookId, encryptedEpubFile.absolutePath))
            } catch (e: Exception) {
                Log.e(TAG, "extract: ", e)
            }
            show()
        }

        when {
            cachedData != null && !TextUtils.isEmpty(cachedData.data) -> {
                val epubFilePath = cachedData.data

                val savedEpubFile = File(epubFilePath)

                val bytes = ByteArray(savedEpubFile.length().toInt())

                val `in`: FileInputStream = withContext(Dispatchers.IO) {
                    FileInputStream(savedEpubFile)
                }
                try {
                    withContext(Dispatchers.IO) {
                        `in`.read(bytes)
                    }
                } finally {
                    withContext(Dispatchers.IO) {
                        `in`.close()
                    }
                }

                epubBase64String = String(bytes)
                show()
            }

            else -> {
                download(downloadUrl, zipStoragePath.replace(fileName, ""), fileName) {
                    Log.e(TAG, "Inside load epub")
                    binding?.alertDialogLayout?.hideView()
                    extract()
                }
            }
        }

    }

    /**
     *  Downloads a file to a given download path
     *
     *  @param downloadUrl url of the file
     *  @param downloadPath will be used for storing downloaded file
     *  @param fileName name of the file
     *  @param onDownloadCompleted gives a callback once download is completed
     */
    private fun download(downloadUrl: String, downloadPath: String, fileName: String, onDownloadCompleted: () -> Unit) {
        // TODO:
        //  - Check if previous download status by using downloadId
        //  - If prev download is not completed then try resume else start new

        binding?.downloadMsgTxt?.text = "Downloading ${resource.resName}".toDecoded()
        binding?.downloadPercent?.text = "0%"
        binding?.alertDialogLayout?.showView()

        var lastPercentage = 0
        var downloadId = 0
        downloadId = PRDownloader.download(downloadUrl, downloadPath, fileName)
                .setHeader("X-Auth-Token", tokenProvider.getToken())
                .setPriority(Priority.HIGH)
                .build()
                .setOnProgressListener {
                    // update progress
                    try {
                        val percentage = (it.currentBytes * 100).div(it.totalBytes).toInt()
                        if(lastPercentage != percentage) {
                            lastPercentage = percentage
                            binding?.downloadPercent?.text = "${percentage}%"
                            binding?.progressDialogBar?.progress = lastPercentage
                            Log.i(TAG, "startZipDownload: ${percentage}% (${it.currentBytes.byteToMB()} MB/${it.totalBytes.byteToMB()} MB)")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "startZipDownload: ", e)
                    }
                }
                .start(object : OnDownloadListener {
                    override fun onDownloadComplete() {

                        if(isDetached || !isAdded)
                            return

                        binding?.downloadPercent?.text = "Processing..."

                        Log.e(TAG, "download status: " + PRDownloader.getStatus(downloadId))

                        if (PRDownloader.getStatus(downloadId) == Status.COMPLETED) {
                            PRDownloader.cancel(downloadId)
                        }

                        onDownloadCompleted()

                    }

                    override fun onError(error: Error?) {
                        if (PRDownloader.getStatus(downloadId) != Status.COMPLETED &&
                                PRDownloader.getStatus(downloadId) != Status.UNKNOWN) {
                            Log.e(TAG, "onError: ${error?.responseCode}")
                            Log.e(TAG, "downloadStatus: ${PRDownloader.getStatus(downloadId)}")
                            showToast("Problem while downloading reading material. Please try again")
                            listener?.onBackBtnClicked()
                        }
                    }
                })

        // TODO: store the downloadId in Local storage for later resuming the download
    }

    private fun updateReadType() {
        try {
            when {
                "pdf".equals(resource.fileType, true) -> {
                    currentReadType = if(isSecurePDF(resource.resLink, "no")) ReadType.SECURE_PDF else ReadType.NON_SECURE_PDF
                }
                "epub".equals(resource.fileType, false) || ".epub".contains(resource.resLink) -> {
                    currentReadType = ReadType.E_PUB
                    activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
                }
                else -> currentReadType = ReadType.ZIPPED_PDF
            }
        } catch (e: Exception) {
            Log.e(TAG, "updateReadType: ", e)
        }
    }

    private fun isSecurePDF(resLink: String, videoPlayer: String) =
            (resLink.contains(".pdf") || resLink.contains(".PDF")) && "no".equals(videoPlayer, true)


    private fun updateHighlightString() {
        try {
            renderHighlightString = when (currentReadType) {
                ReadType.E_PUB -> {
                    try {
                        readNotesJson.toString().replace("\\/", "/")
                    } catch (e: NullPointerException) {
                        Log.e(TAG, "updateHighlightString: ", e)
                        "{\"rows\":[]}"
                    }
                }
                else -> {
                    try {
                        readNotesJson.optJSONArray("rows")?.toString()?.replace("\\/", "/") ?: "[]"
                    } catch (e: NullPointerException) {
                        Log.e(TAG, "updateHighlightString: ", e)
                        "[]"
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "updateHighlightString: ", e)
        }
    }

    private fun updateWebViewWithHighlights() {
        if(isWebViewLoaded)
            binding?.wvReadingPdf?.evaluateJavascript("loadAnnotationsFromJavaData(0,'$renderHighlightString')", null)
    }


    // ****** Extension helper functions for Zipped PDFs *****

    private fun String.replaceImagePaths(imageLocation: String): String {
        var content = this
        if(content.contains("data=\"") && !readingImageUpdateStatusManager.isSVGUpdated(resource.id)) {
            if(imageLocation.isNotEmpty()) {
                content = readUnzipHelper.replaceSVG(content, imageLocation, resource, chapter)
                readingImageUpdateStatusManager.setSVGUpdated(resource.id, true)
            }
        }

        val isPreviewMode = readingFragConfig?.isPreviewMode ?: true

        if (content.contains("<img") && content.contains("/funlearn/") && !readingImageUpdateStatusManager.isImageReplaced(resource.id)) {
            content = content.replace("/funlearn/", networkConfig.service + "funlearn/")
        } else if (content.contains("../Images/") && !readingImageUpdateStatusManager.isImageReplaced(resource.id)) {
            if (imageLocation.isNotEmpty()) {
                content = content.replace("../Images/", imageLocation + "Images/")
            } else {
                //Get Images Directly Nothing to download
                val directImagePath: String = if (isPreviewMode) {
                    networkConfig.service1 + "/funlearn/downloadEpubImage?source=upload/resources/" + resource.id + "/extract/OEBPS/"
                } else {
                    networkConfig.service1 + "funlearn/downloadEpubImage?source=" +
                            "upload/books/" + chapter.bookId + "/chapters/" + chapter.chapterId + "/" + resource.id + "/extract/OEBPS/"
                }
                content = content.replace("../Images/".toRegex(), directImagePath + "Images/")
            }
        } else if (content.contains("Images/") && !readingImageUpdateStatusManager.isImageReplaced(resource.id)) {
            if (imageLocation.isNotEmpty()) {
                content = content.replace("Images/", imageLocation)
            } else {
                //Get Images Directly Nothing to download
                val directImagePath = if (isPreviewMode) {
                    networkConfig.service1 + "/funlearn/downloadEpubImage?source=upload/resources/" + resource.id + "/extract/OEBPS/"
                } else {
                    networkConfig.service1 + "funlearn/downloadEpubImage?source=" +
                            "upload/books/" + chapter.bookId + "/chapters/" + chapter.chapterId + "/" + resource.id + "/extract/OEBPS/"
                }
                content = content.replace("Images/".toRegex(), directImagePath + "Images/")
            }
        } else if (content.contains("images/") && !readingImageUpdateStatusManager.isImageReplaced(resource.id)) {
            if (imageLocation.isNotEmpty()) {
                content = content.replace("images/".toRegex(), imageLocation)
            } else {

                //Get Images Directly Nothing to download
                val directImagePath = if (isPreviewMode) {
                    networkConfig.service1 + "/funlearn/downloadEpubImage?source=upload/resources/" + resource.id + "/extract/OEBPS/"
                } else {
                    networkConfig.service1 + "funlearn/downloadEpubImage?source=" +
                            "upload/books/" + chapter.bookId + "/chapters/" + chapter.chapterId + "/" + resource.id + "/extract/OEBPS/"
                }
                content = content.replace("images/".toRegex(), directImagePath + "images/")
            }
        }

        return content
    }

    private fun String.replaceHeader(): String {
        return replace("</head>", "<style>\n" +
                "@font-face { font-family: 'Work Sans Regular'; src: url('file:///android_asset/fonts/WorkSans-Regular.ttf');}" +
                "@font-face { font-family: 'Work Sans Medium'; src: url('file:///android_asset/fonts/WorkSans-Medium.ttf');}" +
                "@font-face { font-family: 'Work Sans Light'; src: url('file:///android_asset/fonts/WorkSans-Light.ttf');}" +
                "@font-face { font-family: 'Work Sans Bold'; src: url('file:///android_asset/fonts/WorkSans-Bold.ttf');}" +
                "@font-face { font-family: 'Work Sans SemiBold'; src: url('file:///android_asset/fonts/WorkSans-SemiBold.ttf');}" +
                "html, body {\n" +
                "  font-family: 'Work Sans Light' !important; \n" +
                "  font-size: 16px;\n" +
                "  line-height: 24px;\n" +
                "  letter-spacing: 0.01em;\n" +
                "  color: #1B2733;\n" +
                "  font-weight: 300;\n" +
                "  overflow: auto;\n" +
                "  overflow-x: hidden;\n" +
                "  -webkit-overflow-scrolling: touch;\n" +
                "}" +
                ".table-responsive {\n" +
                "   display: block;\n" +
                "   width: 100%;\n" +
                "   overflow-x: auto;\n" +
                "   -webkit-overflow-scrolling: touch;\n" +
                "   -ms-overflow-style: -ms-autohiding-scrollbar;\n" +
                "}" +
                "body a, p, span, ul, li {\n" +
                /*"  font-weight: 300 !important;\n" +*/
                "}" +
                ".blackTheme {\n" +
                "color:black !important;\n" +
                "}\n" +

                ".whiteTheme {\n" +
                "color:white !important;\n" +
                "}\n" +
                ".whiteTheme p span{\n" +
                "color:white !important;\n" +
                "}\n" +
                "#htmlreadingcontent img {\n" +
                "   max-width: 100%;\n" +
                "   height: auto;\n" +
                "   border: 0; }\n" +
                " #htmlreadingcontent h1 {\n" +
                "   font-family: “Work Sans”, sans-serif;\n" +
                "   font-size: 30px !important;\n" +
                "   line-height: 36px !important;\n" +
                "   letter-spacing: 0.04em !important;\n" +
                "   color: #1B2733 !important; }\n" +
                " #htmlreadingcontent h2 {\n" +
                "   font-family: “Work Sans”, sans-serif;\n" +
                "   font-size: 24px !important;\n" +
                "   line-height: 30px !important;\n" +
                "   letter-spacing: 0.02em !important;\n" +
                "   color: #1B2733 !important; }\n" +
                " #htmlreadingcontent h3 {\n" +
                "   font-family: “Work Sans”, sans-serif;\n" +
                "   font-size: 18px !important;\n" +
                "   line-height: 28px !important;\n" +
                "   letter-spacing: 0.01em !important;\n" +
                "   color: #1B2733 !important; }\n" +
                " #htmlreadingcontent p, #htmlreadingcontent span {\n" +
                "   font-family: “Work Sans”, sans-serif; }\n" +
                " #htmlreadingcontent span.annotator-hlh {\n" +
                "   font-size: inherit !important;\n" +
                "   font-family: inherit; }\n" +
                " #htmlreadingcontent span.annotator-hl {\n" +
                "   font-size: inherit !important;\n" +
                "   font-family: inherit; }\n" +
                "\n" +
                "#htmlreadingcontent table {\n" +
                "   width: 100% !important;\n" +
                "    border-collapse: collapse;\n" +
                "}\n" +
                "#htmlreadingcontent table td p {\n" +
                "text-align:unset !important;\n" +
                "}\n" +
                " </style>")
    }

    private fun String.replaceFontPaths(fontsLocation: String): String {
        var content = this
        if (content.contains("fonts/") && fontsLocation.isNotEmpty()) {
            content = content.replace(
                    "fonts/",
                    fontsLocation
            )
        }
        return content
    }

    private fun String.replaceStylesPaths(stylesLocation: String): String {
        var content = this
        if (content.contains("../Styles/") && stylesLocation.isNotEmpty()) {
            content = content.replace(
                    "../Styles/",
                    stylesLocation
            )
        }
        return content
    }

    private fun String.replaceTable(): String {
        var content = this
        if (content.contains("<table")) {
            content = content.replace("<table", "<div class=table-responsive><table")
            content = content.replace("</table>", "</table></div>")
        }
        return content
    }

    private fun String.replaceImageExtension(imageLocation: String): String {
        var html = this

        if (html.contains("data=\"")) {
            if (imageLocation.isNotEmpty()) {
                html = readUnzipHelper.replaceSVG(html, imageLocation, resource, chapter)
                readingImageUpdateStatusManager.setSVGUpdated(
                        resource.id,
                        true
                )
            } else {
                readingImageUpdateStatusManager.setSVGUpdated(
                        resource.id,
                        false
                )
            }
        }

        if (html.contains("../Images/")) {
            if (imageLocation.isNotEmpty()) {
                html = html.replace(
                        "../Images/".toRegex(),
                        imageLocation
                )
                val htmlDoc = Jsoup.parse(html)
                val imgElements = htmlDoc.getElementsByTag("img")
                for (i in imgElements.indices) {
                    //get img path from img src tag
                    var imgPath = imgElements[i].attr("src")
                    try {
                        imgPath = imgPath.toDecode()
                    } catch (e: UnsupportedEncodingException) {
                        Log.e(TAG, "getHtmlFromFile: ", e)
                    }
                    //check if that path exists or not
                    try {
                        imgPath = imgPath.toDecode()
                    } catch (e: UnsupportedEncodingException) {
                        Log.e(TAG, "getHtmlFromFile: ", e)
                    }
                    //check if that path exists or not
                    val file = File(imgPath)
                    if (file.exists()) {
                        imgElements[i].attr("src", imgPath)
                    } else {
                        //replace the path accordingly
                        if (imgPath.contains(".jpg")) {
                            imgPath = imgPath.replace(".jpg", ".png")
                        } else if (imgPath.contains(".png")) {
                            imgPath = imgPath.replace(".png", ".jpg")
                        }
                        imgElements[i].attr("src", imgPath)
                    }
                }
                html = htmlDoc.html()
                readingImageUpdateStatusManager.setImageReplaced(
                        resource.id,
                        true
                )
            } else {
                readingImageUpdateStatusManager.setImageReplaced(
                        resource.id,
                        false
                )
            }
        } else if (html.contains("Images/")) {
            if (imageLocation.isNotEmpty()) {
                html = html.replace(
                        "Images/".toRegex(),
                        imageLocation
                )
                val htmlDoc = Jsoup.parse(html)
                val imgElements = htmlDoc.getElementsByTag("img")
                for (i in imgElements.indices) {
                    //get img path from img src tag
                    var imgPath = imgElements.attr("src")
                    //check if that path exists or not
                    val file = File(imgPath)
                    if (file.exists()) {
                        //do nothing
                    } else {
                        //replace the path accordingly
                        if (imgPath.contains(".jpg")) {
                            imgPath = imgPath.replace(".jpg", ".png")
                        } else if (imgPath.contains(".png")) {
                            imgPath = imgPath.replace(".png", ".jpg")
                        }
                        imgElements.attr("src", imgPath)
                    }
                }
                html = htmlDoc.html()
                readingImageUpdateStatusManager.setImageReplaced(
                        resource.id,
                        true
                )
            } else {
                readingImageUpdateStatusManager.setImageReplaced(
                        resource.id,
                        false
                )
            }
        } else if (html.contains("images/")) {
            if (imageLocation.isNotEmpty()) {
                html = html.replace(
                        "images/".toRegex(),
                        imageLocation
                )
                val htmlDoc = Jsoup.parse(html)
                val imgElements = htmlDoc.getElementsByTag("img")
                for (i in imgElements.indices) {
                    //get img path from img src tag
                    var imgPath = imgElements.attr("src")
                    //check if that path exists or not
                    val file = File(imgPath)
                    if (file.exists()) {
                        //do nothing
                    } else {
                        //replace the path accordingly
                        if (imgPath.contains(".jpg")) {
                            imgPath = imgPath.replace(".jpg", ".png")
                        } else if (imgPath.contains(".png")) {
                            imgPath = imgPath.replace(".png", ".jpg")
                        }
                        imgElements.attr("src", imgPath)
                    }
                }
                html = htmlDoc.html()
                readingImageUpdateStatusManager.setImageReplaced(
                        resource.id,
                        true
                )
            } else {
                readingImageUpdateStatusManager.setImageReplaced(
                        resource.id,
                        false
                )
            }
        }
        return html
    }

    private fun String.removeExtraCharacters(): String {
        return this.replace("\uF097", "")
                .replace("\uF0DC", "")
    }

    private fun String.encryptOrEmpty(): String {
        return getEncryptionHelper()?.encryptOrNull(this) ?: ""
    }

    private fun String.decryptOrEmpty(): String {
        return getEncryptionHelper()?.decryptOrNull(this) ?: ""
    }

    private fun getEncryptionHelper(): Encryption? {
        val encryptionKey = encryptionKeysProvider.getReadEncryptionKey()
        return Encryption.getDefault(
                encryptionKey,
                getString(R.string.salt),
                ByteArray(16)
        )
    }

    fun onBackPressed(): Boolean {
        binding?.wvSearch?.let {
            if(inWebSearchMode) {
                inWebSearchMode = false
                setupToolbar()
                it.hideView()
                it.loadUrl("about:blank")
                binding?.wvReadingPdf?.visibility(isSecurePdfOrEPub())
                binding?.wvReading?.visibility(!isSecurePdfOrEPub())
                return true
            }
        }
        binding?.wvFlashCard?.let {
            if (inFlashCardMode) {
                inFlashCardMode = false
                setupToolbar()
                it.hideView()
                it.loadUrl("about:blank")
                binding?.wvReadingPdf?.visibility(isSecurePdfOrEPub())
                binding?.wvReading?.visibility(!isSecurePdfOrEPub())
                binding?.wvSearch?.hideView()
                return true
            }
        }
        return false
    }

    private fun isSecurePdfOrEPub(): Boolean {
        return currentReadType == ReadType.SECURE_PDF || currentReadType == ReadType.E_PUB
    }

    override fun onPause() {
        storeLastReadPDFPage()
        super.onPause()
        if(currentReadType == ReadType.SECURE_PDF)
            decryptedPdfFile?.delete()
    }

    override fun onDestroyView() {
        storeLastReadPDFPage()
        binding?.wvReading?.destroy()
        binding?.wvReadingPdf?.destroy()
        binding?.wvSearch?.destroy()
        binding?.wvFlashCard?.destroy()
        super.onDestroyView()
    }

    private fun storeLastReadPDFPage() {
        //Get Last Read Page
        if (currentReadType == ReadType.SECURE_PDF) {
            if (binding?.wvReadingPdf != null) {
                binding?.wvReadingPdf?.evaluateJavascript("sendCurrentPageNo()", null)
            }
        }
    }


    interface  OnReadingFragInteractionListener {
        fun onBackBtnClicked()

        fun checkForStoragePermission()

        fun onWebSearch(resource: Resource, term: String)

        fun onOpenNotesList(annotationsRequest: AnnotationsRequest)

        fun onOpenGPTChat(resource: Resource, chapter: Chapter, doubts: String)
    }

    companion object {

        private const val TAG = "ReadingFrag"

        const val ARG_READING_CONFIG = "argReadingConfig"

        private const val DOWNLOADED_FILES = "/.BooksMojo/"
        private const val UNZIPPED_EPUB_FILES = "/.Books2Mojo/"

        private const val OPEN_ERROR_MSG = "Problem while opening this reading material. Please try again"
        private const val PROBLEM_UPDATING_ANNOTATION = "Problem while updating annotation. Please try again"
        private const val ANNOTATION_UPDATE_SUCCESS = "Annotation updated successfully"

        private var deepLinkResource: Resource? = null
        private var deepLinkChapter: Chapter? = null

        fun newInstance(readingFragConfig: ReadingFragConfig, readingResource: Resource?, resourceChapter: Chapter?) = ReadingFrag().also {
            it.arguments = bundleOf(ARG_READING_CONFIG to readingFragConfig)
            deepLinkResource = readingResource
            deepLinkChapter = resourceChapter
        }
    }

}
