package com.ws.resources.ui.reading.utils

import android.content.Context
import android.util.Log
import com.ws.database.room.entity.Chapter
import com.ws.database.room.entity.LibraryBook
import com.ws.database.room.entity.Resource
import com.ws.resources.data.models.ReadExtractData
import com.ws.resources.ui.reading.ReadingFrag
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.jsoup.Jsoup
import org.jsoup.nodes.Document
import java.io.*
import java.lang.StringBuilder
import java.util.*
import java.util.zip.ZipEntry
import java.util.zip.ZipInputStream

internal class ReadUnzipHelper {

    companion object {
        private const val TAG = "ReadUnzipHelper"
        private const val IMAGES = "Images/"
        private const val FONTS = "fonts/"
        private const val STYLES = "Styles/"
    }

    @Throws(IOException::class)
    suspend fun unzip(zipPath: String, outputPath: String): ReadExtractData = withContext(Dispatchers.IO) {
        var location = outputPath
        var size: Int
        val buffer = ByteArray(8192)

        var htmlFilePath = ""
        var extractData = ReadExtractData()
        try {
            if (!location.endsWith("/")) {
                location += "/"
            }
            val f = File(location)
            if (!f.isDirectory) {
                f.parentFile?.mkdirs()
            }
            val zipInputStream = ZipInputStream(BufferedInputStream(FileInputStream(zipPath), 8192))
            zipInputStream.use { zis ->
                var ze: ZipEntry?
                while (zis.nextEntry.also { ze = it } != null) {
                    val path = location + ze?.name
                    if (path.contains(".html")) {
                        htmlFilePath = path
                    }
                    val unzipFile = File(path)
                    if (ze?.isDirectory == true) {
                        if (!unzipFile.isDirectory) {
                            unzipFile.mkdirs()
                        }
                    } else {
                        // check for and create parent directories if they don't exist
                        val parentDir = unzipFile.parentFile
                        if (null != parentDir) {
                            if (!parentDir.isDirectory) {
                                parentDir.mkdirs()
                            }
                        }
                        // unzip the file
                        val out = FileOutputStream(unzipFile, false)
                        val fout = BufferedOutputStream(out, 8192)
                        try {
                            while (zis.read(buffer, 0, 8192).also { size = it } != -1) {
                                fout.write(buffer, 0, size)
                            }
                            zis.closeEntry()
                        } finally {
                            fout.flush()
                            fout.close()
                        }
                    }
                }
            }

            val directory = File(location)
            val files = directory.listFiles()
            if (files != null && files.isNotEmpty()) {
                for (a in files.indices) {
                    val from = files[a]
                    if (from.name.contains(".epub")) {
                        val index = from.name.lastIndexOf(".")
                        val name = from.name.substring(0, index)
                        val newZippedFile = File(from?.parent + "/" + name + ".zip")
                        from.renameTo(newZippedFile)
                        from.delete()
                        extractData = unzipEPUB(newZippedFile.absolutePath, location)
                    } else if (from.name.contains(".zip")) {
                        extractData = unzipEPUB(from.absolutePath, location)
                    } else if (!from.name.contains(".epub") && !from.name.contains(".html") && !from.name.contains(".zip")) {
                        val file = File(from?.parent + "/" + from.name + ".zip")
                        from.renameTo(file)
                        from.delete()
                        extractData = unzipEPUB(file.absolutePath, location)
                    } else if (files.size == 1 && files[0].name.contains(".html")) {
                        extractData = ReadExtractData()
                    }
                }
            } else {
                extractData = ReadExtractData()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Unzip: ", e)
        }

        return@withContext ReadExtractData(
            htmlFilePath,
            extractData.imageLocation,
            extractData.fontsLocation,
            extractData.stylesLocations
        )
    }

    @Throws(IOException::class)
    private fun unzipEPUB(zipFile: String, outputPath: String): ReadExtractData {
        var location = outputPath

        val oebpsFilePath = location + "OEBPS/"

        var imageLocation = oebpsFilePath
        var fontsLocation = oebpsFilePath
        var stylesLocation = oebpsFilePath

        var size: Int
        val buffer = ByteArray(8192)
        try {
            if (!location.endsWith("/")) {
                location += "/"
            }
            val f = File(location)
            if (!f.isDirectory) {
                f.parentFile.mkdirs()
            }
            val zin = ZipInputStream(BufferedInputStream(FileInputStream(zipFile), 8192))
            try {
                var ze: ZipEntry?
                while (zin.nextEntry.also { ze = it } != null) {
                    if(ze == null)
                        continue
                    val path = location + ze!!.name
                    val unzipFile = File(path)
                    if (ze!!.isDirectory) {
                        if (!unzipFile.isDirectory) {
                            unzipFile.mkdirs()
                        }
                    } else {
                        // check for and create parent directories if they don't exist
                        val parentDir = unzipFile.parentFile
                        if (null != parentDir) {
                            if (!parentDir.isDirectory) {
                                parentDir.mkdirs()
                            }
                        }
                        // unzip the file
                        val out = FileOutputStream(unzipFile, false)
                        val fout = BufferedOutputStream(out, 8192)
                        try {
                            while (zin.read(buffer, 0, 8192).also { size = it } != -1) {
                                fout.write(buffer, 0, size)
                            }
                            zin.closeEntry()
                        } finally {
                            fout.flush()
                            fout.close()
                        }
                    }
                }
            } finally {
                zin.close()
                val epubZipFile = File(zipFile)
                if (epubZipFile.exists()) {
                    epubZipFile.delete()
                }
            }

            val directory = File(location)
            val files = directory.listFiles()
            val to = File(location, "OEBPS")
            for (a in files.indices) {
                val from = files[a]
                if (from.name.contains(".epub")) {
                    val index = from.name.lastIndexOf(".")
                    val name = from.name.substring(0, index)
                    from.renameTo(File(from.parent + "/" + name + ".zip"))
                    unzipEPUB(from.absolutePath, location)
                } else if (from.name.contains("OEBPS")) {
                    val oebpsFile = File(oebpsFilePath)
                    if (oebpsFile.exists() && oebpsFile.isDirectory) {
                        val imageFile = File(oebpsFilePath + IMAGES)
                        val fontsFile = File(oebpsFilePath + FONTS)
                        val stylesFile = File(oebpsFilePath + STYLES)
                        if (imageFile.exists() && imageFile.isDirectory) {
                            imageLocation = oebpsFilePath + IMAGES
                        }
                        if (fontsFile.exists() && fontsFile.isDirectory) {
                            fontsLocation = oebpsFilePath + FONTS
                        }
                        if (stylesFile.exists() && stylesFile.isDirectory) {
                            stylesLocation = oebpsFilePath + STYLES
                        }
                    }
                } else if (!from.name.equals("OEBPS", ignoreCase = true) && !from.name.contains(".html") && !from.name.contains("mimetype")) {
                    from.renameTo(to)
                    from.delete()
                    val imageFile = File(oebpsFilePath + IMAGES)
                    val fontsFile = File(oebpsFilePath + FONTS)
                    val stylesFile = File(oebpsFilePath + STYLES)

                    if(imageFile.exists() && imageFile.isDirectory) {
                        imageLocation = oebpsFilePath + IMAGES
                    }
                    if(fontsFile.exists() && fontsFile.isDirectory) {
                        fontsLocation = oebpsFilePath + FONTS
                    }
                    if(stylesFile.exists() && stylesFile.isDirectory) {
                        stylesLocation = oebpsFilePath + STYLES
                    }
                }
            }
        } catch (e: EOFException) {
            Log.e(TAG, "EOF Exception", e)
        }

        return ReadExtractData(
            "",
            imageLocation,
            fontsLocation,
            stylesLocation
        )
    }

    fun replaceSVG(content: String, imageLocation: String, resource: Resource, chapter: Chapter): String {
        var htmlDoc: Document? = null
        try {
            val html = content.replace(
                "data=\"",
                "data=\"$imageLocation"
            )
            htmlDoc = Jsoup.parse(html)
            val imgElements = htmlDoc.getElementsByTag("object")
            for (i in imgElements.indices) {
                //get img path from data src tag
                var imgPath = imgElements[i].attr("data")
                //check if that path exists or not
                val file = File(imgPath)
                if (file.exists()) {
                    //check for image paths inside svg
                    try {
                        var c: Int
                        var oldImagePath: String
                        var newSvg: String
                        var imageFileName: String = resource.resName
                        val data = StringBuilder()
                        //read svg and copy to string
                        val svgInputStream: InputStream = FileInputStream(file)
                        while (svgInputStream.read().also { c = it } != -1) {
                            data.append(c.toChar())
                        }
                        newSvg = data.toString()
                        svgInputStream.close()
                        //replace funlearn path to local image path
                        if (newSvg.contains("<image") && newSvg.contains("/funlearn")) {
                            if (imageFileName.contains(".zip")) {
                                imageFileName = imageFileName.replace(".zip", "").trim { it <= ' ' }
                            }
                            imageFileName = imageFileName.replace("\\s+", "").trim()
                            oldImagePath =
                                ("/funlearn/downloadEpubImage?source=upload/" + "books/" + chapter.bookId
                                    + "/chapters/" + chapter.chapterId + "/"
                                    + resource.id + "/" + "extract/" + imageFileName + "/")
                            newSvg = newSvg.replace(
                                oldImagePath,
                                imageLocation
                            )
                        } else if (newSvg.contains("<image")) {
                            val svgDoc = Jsoup.parse(newSvg)
                            val svgElements = svgDoc.getElementsByTag("image")
                            for (j in svgElements.indices) {
                                //get img path from data src tag
                                var svgImgPath = svgElements[j].attr("xlink:href")
                                //check if that path exists or not
                                val fileSVG = File(svgImgPath)
                                if (!fileSVG.exists()) {
                                    //Replace image path inside svg to local image path
                                    svgImgPath = file.parent + "/" + svgImgPath
                                    if (File(svgImgPath).exists()) {
                                        svgElements[j].attr("xlink:href", svgImgPath)
                                    } else {
                                        if (svgImgPath.contains(".jpg")) {
                                            svgImgPath = svgImgPath.replace(".jpg", ".png")
                                        } else if (svgImgPath.contains(".png")) {
                                            svgImgPath = svgImgPath.replace(".png", ".jpg")
                                        }
                                        svgElements[j].attr("xlink:href", svgImgPath)
                                    }
                                }
                            }
                            newSvg = svgDoc.toString()
                            newSvg = newSvg.replace("<html>", "")
                                .replace("<head>", "")
                                .replace("<body>", "").replace("</html>", "")
                                .replace("</head>", "").replace("</body>", "")
                        }

                        //delete old svg file and create new svg file from string at old file location
                        file.delete()
                        val osw = OutputStreamWriter(FileOutputStream(File(imgPath)))

                        // Write the svg string to the file
                        osw.write(newSvg)

                        /* ensure that everything is
                         * really written out and close */
                        osw.flush()
                        osw.close()
                    } catch (e: FileNotFoundException) {
                        Log.e(TAG, "Error while getting svg", e)
                    } catch (e: IOException) {
                        Log.e(TAG, "Error while getting svg", e)
                    }
                } else {
                    //replace the path accordingly
                    if (imgPath.contains(".jpg")) {
                        imgPath = imgPath.replace(".jpg", ".png")
                    } else if (imgPath.contains(".png")) {
                        imgPath = imgPath.replace(".png", ".jpg")
                    }
                    imgElements[i].attr("src", imgPath)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "replaceSVG: ", e)
        }

        return htmlDoc?.html() ?: content
    }
}