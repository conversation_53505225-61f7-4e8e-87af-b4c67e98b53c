package com.ws.resources.ui.reading

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ws.chapter_list.data.repository.ChaptersAndResourcesListRepository
import com.ws.commons.Result
import com.ws.database.room.entity.Chapter
import com.ws.database.room.entity.LibraryBook
import com.ws.database.room.entity.ReadData
import com.ws.database.room.entity.Resource
import com.ws.library.books.data.repositories.LibraryBooksRepository
import com.ws.networking.connnection_checker.ConnectionChecker
import com.ws.resources.data.models.AnnotationsRequest
import com.ws.resources.data.models.EncodedPdfRequest
import com.ws.resources.data.respository.ResourcesRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.lang.Exception

class ReadingFragViewModel(
    private val connectionChecker: ConnectionChecker,
    private val libraryBooksRepository: LibraryBooksRepository,
    private val chaptersAndResourcesListRepository: ChaptersAndResourcesListRepository,
    private val resourcesRepository: ResourcesRepository
): ViewModel() {

    private val _annotations: MutableStateFlow<JSONObject> = MutableStateFlow(JSONObject())
    val annotations = _annotations.asStateFlow()

    private val annotationLocalIdsMap = hashMapOf<String, String>()

    fun isConnected(): Boolean {
        return connectionChecker.isNetworkConnected()
    }

    suspend fun findCachedResource(resId: String): Resource? {
        return chaptersAndResourcesListRepository.findCachedResource(resId).firstOrNull()
    }

    suspend fun findCachedBookByChapterId(topicId: String?): LibraryBook? = topicId?.let{
        val chapter = chaptersAndResourcesListRepository.findCachedChapter(topicId).firstOrNull()
        if(chapter != null)
            libraryBooksRepository.findCachedUserLibraryBook(chapter.bookId.toInt()).firstOrNull()
        else
            null
    }

    suspend fun findCachedChapter(chapterId: String): Chapter? {
        return chaptersAndResourcesListRepository.findCachedChapter(chapterId).firstOrNull()
    }

    fun getZipDownloadUrl(isLoggedIn: Boolean, isPreviewMode: Boolean, resource: Resource): String {
        return when {
            !isPreviewMode && isLoggedIn -> resourcesRepository.getReadZipDownloadUrl(resource.id)
            resource.isPdfType() -> resourcesRepository.getPdfFileUrl(resource.id)
            else -> resourcesRepository.getPublicReadZipDownloadUrl(resource.id)
        }
    }

    fun getPDFDownloadUrl(resId: String): String {
        return resourcesRepository.getPdfFileUrl(resId)
    }

    fun getEncodedPdfDownloadUrl(encodedPdfRequest: EncodedPdfRequest): String {
        return resourcesRepository.getEncodedPdfFileUrl(encodedPdfRequest)
    }

    suspend fun getCachedReadData(resId: String): ReadData? {
        return resourcesRepository.getCachedReadData(resId)
    }

    suspend fun getCachedReadDataByBookId(resId: String): ReadData? {
        return resourcesRepository.getCachedReadDataByBookId(resId)
    }

    suspend fun cacheReadData(readData: ReadData) {
        resourcesRepository.cacheReadData(readData)
    }

    suspend fun deleteReadData(resId: String) {
        resourcesRepository.deleteCachedReadData(resId)
    }

    suspend fun deleteReadDataByBookId(bookId: String) {
        resourcesRepository.deleteCachedReadDataByBookId(bookId)
    }

    fun getAnnotationLocalId(key: String): String {
        var id = ""
        if (annotationLocalIdsMap.containsKey(key))
            id = annotationLocalIdsMap[key] ?: ""
        return id
    }

    fun getAnnotations(annotationsRequest: AnnotationsRequest) = viewModelScope.launch {
        if(annotationsRequest.forceUpdate) {
            fetchAnnotations(annotationsRequest)
        } else {
            val cachedAnnotations = resourcesRepository.getCachedAnnotations(annotationsRequest.resId)
            if(cachedAnnotations?.annotationJson != null)
                _annotations.emit(JSONObject(cachedAnnotations.annotationJson!!))
            else
                fetchAnnotations(annotationsRequest)
        }
    }

    private suspend fun fetchAnnotations(annotationsRequest: AnnotationsRequest) {
        when(val result = resourcesRepository.fetchAnnotations(annotationsRequest)) {
            is Result.Success -> {
                try {
                    result.data.annotationJson?.let {
                        _annotations.emit(JSONObject(it))
                    } ?: _annotations.emit(JSONObject())

                    resourcesRepository.cacheAnnotations(result.data)

                    // TODO: add json from cached actions
                } catch (e: Exception) {
                    _annotations.emit(JSONObject())
                }
            }

            is Result.Failure -> {
                _annotations.emit(JSONObject())
            }
        }
    }

    suspend fun createAnnotation(annotationsRequest: AnnotationsRequest): Boolean {
        return when(resourcesRepository.createAnnotations(annotationsRequest)) {
            is Result.Success -> {
                fetchAnnotations(annotationsRequest)
                true
            }

            is Result.Failure -> {
                false
            }
        }
    }

    suspend fun updateAnnotation(annotationsRequest: AnnotationsRequest): Boolean {
        return when(resourcesRepository.updateAnnotations(annotationsRequest)) {
            is Result.Success -> {
                fetchAnnotations(annotationsRequest)
                true
            }

            is Result.Failure -> {
                // TODO: handle failure
                false
            }
        }
    }

    suspend fun updatePDFAnnotation(annotationsRequest: AnnotationsRequest): Boolean {
        return when(resourcesRepository.updatePDFAnnotations(annotationsRequest)) {
            is Result.Success -> {
                fetchAnnotations(annotationsRequest)
                true
            }

            is Result.Failure -> {
                // TODO: handle failure
                false
            }
        }
    }

    suspend fun deleteAnnotation(annotationsRequest: AnnotationsRequest): Boolean {
        return when(resourcesRepository.deleteAnnotations(annotationsRequest)) {
            is Result.Success -> {
                fetchAnnotations(annotationsRequest)
                true
            }

            is Result.Failure -> {
                // TODO: handle failure
                false
            }
        }
    }

}