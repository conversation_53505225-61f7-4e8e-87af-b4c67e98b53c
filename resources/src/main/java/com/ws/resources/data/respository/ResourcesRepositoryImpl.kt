package com.ws.resources.data.respository

import com.ws.commons.Result
import com.ws.database.room.entity.Annotations
import com.ws.database.room.entity.ReadData
import com.ws.resources.data.ResourcesService
import com.ws.resources.data.models.AnnotationsRequest
import com.ws.resources.data.models.EncodedPdfRequest
import org.json.JSONObject

internal class ResourcesRepositoryImpl(
    private val resourcesService: ResourcesService
): ResourcesRepository {
    override fun getPublicReadZipDownloadUrl(resId: String): String {
        return resourcesService.getPublicReadZipDownloadUrl(resId)
    }

    override fun getReadZipDownloadUrl(resId: String): String {
        return resourcesService.getReadZipDownloadUrl(resId)
    }

    override fun getPdfFileUrl(resId: String): String {
        return resourcesService.getPdfFileUrl(resId)
    }

    override fun getEncodedPdfFileUrl(encodedPdfRequest: EncodedPdfRequest): String {
        return resourcesService.getEncodedPdfFileUrl(encodedPdfRequest)
    }

    override suspend fun getCachedReadData(resId: String): ReadData?{
        return resourcesService.getCachedReadData(resId).firstOrNull()
    }

    override suspend fun getCachedReadDataByBookId(bookId: String): ReadData? {
        return resourcesService.getCachedReadDataByBookId(bookId).firstOrNull()
    }

    override suspend fun cacheReadData(readData: ReadData) {
        resourcesService.cacheReadData(readData)
    }

    override suspend fun deleteCachedReadData(resId: String) {
        resourcesService.deleteCachedReadData(resId)
    }

    override suspend fun deleteCachedReadDataByBookId(bookId: String) {
        resourcesService.deleteCachedReadDataByBookId(bookId)
    }

    override suspend fun fetchAnnotations(annotationsRequest: AnnotationsRequest): Result<Annotations> {
        return when(val result = resourcesService.fetchAnnotations(annotationsRequest)) {
            is Result.Success -> {
                Result.Success(
                    Annotations(
                        annotationsRequest.resId,
                        result.data
                    )
                )
            }

            is Result.Failure -> {
                result
            }
        }
    }

    override suspend fun cacheAnnotations(annotations: Annotations) {
        resourcesService.cacheAnnotations(annotations)
    }

    override suspend fun deleteCachedAnnotations(resId: String) {
        resourcesService.deleteCachedAnnotations(resId)
    }

    override suspend fun getCachedAnnotations(resId: String): Annotations? {
        return resourcesService.getCachedAnnotations(resId).firstOrNull()
    }

    override suspend fun createAnnotations(annotationsRequest: AnnotationsRequest): Result<String> {
        return when(val result = resourcesService.createAnnotation(annotationsRequest)) {
            is Result.Success -> {
                val obj = JSONObject(result.data)
                Result.Success(obj.optString("id"))
            }

            is Result.Failure -> {
                result
            }
        }
    }

    override suspend fun updateAnnotations(annotationsRequest: AnnotationsRequest): Result<String> {
        return when(val result = resourcesService.updateAnnotation(annotationsRequest)) {
            is Result.Success -> {
                val obj = JSONObject(result.data)
                Result.Success(obj.optString("id"))
            }

            is Result.Failure -> {
                result
            }
        }
    }

    override suspend fun updatePDFAnnotations(annotationsRequest: AnnotationsRequest): Result<String> {
        return when(val result = resourcesService.updatePDFAnnotation(annotationsRequest)) {
            is Result.Success -> {
                val obj = JSONObject(result.data)
                Result.Success(obj.optString("id"))
            }

            is Result.Failure -> {
                result
            }
        }
    }

    override suspend fun deleteAnnotations(annotationsRequest: AnnotationsRequest): Result<String> {
        return when(val result = resourcesService.deleteAnnotation(annotationsRequest)) {
            is Result.Success -> {
                Result.Success(JSONObject(annotationsRequest.annotationJsonString).optString("id"))
            }

            is Result.Failure -> {
                result
            }
        }
    }


}