// <asset:javascript src="annotator-full.js"/>
//     <asset:javascript src="annotator.touch.js"/>
/*! jQuery v3.5.1 | (c) JS Foundation and other contributors | jquery.org/license */
! function (e, t) {
    "use strict";
    "object" == typeof module && "object" == typeof module.exports ? module.exports = e.document ? t(e, !0) : function (e) {
        if (!e.document) throw new Error("jQuery requires a window with a document");
        return t(e)
    } : t(e)
}("undefined" != typeof window ? window : this, function (C, e) {
    "use strict";
    var t = [],
        r = Object.getPrototypeOf,
        s = t.slice,
        g = t.flat ? function (e) {
            return t.flat.call(e)
        } : function (e) {
            return t.concat.apply([], e)
        },
        u = t.push,
        i = t.indexOf,
        n = {},
        o = n.toString,
        v = n.hasOwnProperty,
        a = v.toString,
        l = a.call(Object),
        y = {},
        m = function (e) {
            return "function" == typeof e && "number" != typeof e.nodeType
        },
        x = function (e) {
            return null != e && e === e.window
        },
        E = C.document,
        c = {
            type: !0,
            src: !0,
            nonce: !0,
            noModule: !0
        };

    function b(e, t, n) {
        var r, i, o = (n = n || E).createElement("script");
        if (o.text = e, t)
            for (r in c)(i = t[r] || t.getAttribute && t.getAttribute(r)) && o.setAttribute(r, i);
        n.head.appendChild(o).parentNode.removeChild(o)
    }

    function w(e) {
        return null == e ? e + "" : "object" == typeof e || "function" == typeof e ? n[o.call(e)] || "object" : typeof e
    }
    var f = "3.5.1",
        S = function (e, t) {
            return new S.fn.init(e, t)
        };

    function p(e) {
        var t = !!e && "length" in e && e.length,
            n = w(e);
        return !m(e) && !x(e) && ("array" === n || 0 === t || "number" == typeof t && 0 < t && t - 1 in e)
    }
    S.fn = S.prototype = {
        jquery: f,
        constructor: S,
        length: 0,
        toArray: function () {
            return s.call(this)
        },
        get: function (e) {
            return null == e ? s.call(this) : e < 0 ? this[e + this.length] : this[e]
        },
        pushStack: function (e) {
            var t = S.merge(this.constructor(), e);
            return t.prevObject = this, t
        },
        each: function (e) {
            return S.each(this, e)
        },
        map: function (n) {
            return this.pushStack(S.map(this, function (e, t) {
                return n.call(e, t, e)
            }))
        },
        slice: function () {
            return this.pushStack(s.apply(this, arguments))
        },
        first: function () {
            return this.eq(0)
        },
        last: function () {
            return this.eq(-1)
        },
        even: function () {
            return this.pushStack(S.grep(this, function (e, t) {
                return (t + 1) % 2
            }))
        },
        odd: function () {
            return this.pushStack(S.grep(this, function (e, t) {
                return t % 2
            }))
        },
        eq: function (e) {
            var t = this.length,
                n = +e + (e < 0 ? t : 0);
            return this.pushStack(0 <= n && n < t ? [this[n]] : [])
        },
        end: function () {
            return this.prevObject || this.constructor()
        },
        push: u,
        sort: t.sort,
        splice: t.splice
    }, S.extend = S.fn.extend = function () {
        var e, t, n, r, i, o, a = arguments[0] || {},
            s = 1,
            u = arguments.length,
            l = !1;
        for ("boolean" == typeof a && (l = a, a = arguments[s] || {}, s++), "object" == typeof a || m(a) || (a = {}), s === u && (a = this, s--); s < u; s++)
            if (null != (e = arguments[s]))
                for (t in e) r = e[t], "__proto__" !== t && a !== r && (l && r && (S.isPlainObject(r) || (i = Array.isArray(r))) ? (n = a[t], o = i && !Array.isArray(n) ? [] : i || S.isPlainObject(n) ? n : {}, i = !1, a[t] = S.extend(l, o, r)) : void 0 !== r && (a[t] = r));
        return a
    }, S.extend({
        expando: "jQuery" + (f + Math.random()).replace(/\D/g, ""),
        isReady: !0,
        error: function (e) {
            throw new Error(e)
        },
        noop: function () {},
        isPlainObject: function (e) {
            var t, n;
            return !(!e || "[object Object]" !== o.call(e)) && (!(t = r(e)) || "function" == typeof (n = v.call(t, "constructor") && t.constructor) && a.call(n) === l)
        },
        isEmptyObject: function (e) {
            var t;
            for (t in e) return !1;
            return !0
        },
        globalEval: function (e, t, n) {
            b(e, {
                nonce: t && t.nonce
            }, n)
        },
        each: function (e, t) {
            var n, r = 0;
            if (p(e)) {
                for (n = e.length; r < n; r++)
                    if (!1 === t.call(e[r], r, e[r])) break
            } else
                for (r in e)
                    if (!1 === t.call(e[r], r, e[r])) break;
            return e
        },
        makeArray: function (e, t) {
            var n = t || [];
            return null != e && (p(Object(e)) ? S.merge(n, "string" == typeof e ? [e] : e) : u.call(n, e)), n
        },
        inArray: function (e, t, n) {
            return null == t ? -1 : i.call(t, e, n)
        },
        merge: function (e, t) {
            for (var n = +t.length, r = 0, i = e.length; r < n; r++) e[i++] = t[r];
            return e.length = i, e
        },
        grep: function (e, t, n) {
            for (var r = [], i = 0, o = e.length, a = !n; i < o; i++) !t(e[i], i) !== a && r.push(e[i]);
            return r
        },
        map: function (e, t, n) {
            var r, i, o = 0,
                a = [];
            if (p(e))
                for (r = e.length; o < r; o++) null != (i = t(e[o], o, n)) && a.push(i);
            else
                for (o in e) null != (i = t(e[o], o, n)) && a.push(i);
            return g(a)
        },
        guid: 1,
        support: y
    }), "function" == typeof Symbol && (S.fn[Symbol.iterator] = t[Symbol.iterator]), S.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "), function (e, t) {
        n["[object " + t + "]"] = t.toLowerCase()
    });
    var d = function (n) {
        var e, d, b, o, i, h, f, g, w, u, l, T, C, a, E, v, s, c, y, S = "sizzle" + 1 * new Date,
            p = n.document,
            k = 0,
            r = 0,
            m = ue(),
            x = ue(),
            A = ue(),
            N = ue(),
            D = function (e, t) {
                return e === t && (l = !0), 0
            },
            j = {}.hasOwnProperty,
            t = [],
            q = t.pop,
            L = t.push,
            H = t.push,
            O = t.slice,
            P = function (e, t) {
                for (var n = 0, r = e.length; n < r; n++)
                    if (e[n] === t) return n;
                return -1
            },
            R = "checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",
            M = "[\\x20\\t\\r\\n\\f]",
            I = "(?:\\\\[\\da-fA-F]{1,6}" + M + "?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",
            W = "\\[" + M + "*(" + I + ")(?:" + M + "*([*^$|!~]?=)" + M + "*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|(" + I + "))|)" + M + "*\\]",
            F = ":(" + I + ")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|" + W + ")*)|.*)\\)|)",
            B = new RegExp(M + "+", "g"),
            $ = new RegExp("^" + M + "+|((?:^|[^\\\\])(?:\\\\.)*)" + M + "+$", "g"),
            _ = new RegExp("^" + M + "*," + M + "*"),
            z = new RegExp("^" + M + "*([>+~]|" + M + ")" + M + "*"),
            U = new RegExp(M + "|>"),
            X = new RegExp(F),
            V = new RegExp("^" + I + "$"),
            G = {
                ID: new RegExp("^#(" + I + ")"),
                CLASS: new RegExp("^\\.(" + I + ")"),
                TAG: new RegExp("^(" + I + "|[*])"),
                ATTR: new RegExp("^" + W),
                PSEUDO: new RegExp("^" + F),
                CHILD: new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\(" + M + "*(even|odd|(([+-]|)(\\d*)n|)" + M + "*(?:([+-]|)" + M + "*(\\d+)|))" + M + "*\\)|)", "i"),
                bool: new RegExp("^(?:" + R + ")$", "i"),
                needsContext: new RegExp("^" + M + "*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\(" + M + "*((?:-\\d)?\\d*)" + M + "*\\)|)(?=[^-]|$)", "i")
            },
            Y = /HTML$/i,
            Q = /^(?:input|select|textarea|button)$/i,
            J = /^h\d$/i,
            K = /^[^{]+\{\s*\[native \w/,
            Z = /^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,
            ee = /[+~]/,
            te = new RegExp("\\\\[\\da-fA-F]{1,6}" + M + "?|\\\\([^\\r\\n\\f])", "g"),
            ne = function (e, t) {
                var n = "0x" + e.slice(1) - 65536;
                return t || (n < 0 ? String.fromCharCode(n + 65536) : String.fromCharCode(n >> 10 | 55296, 1023 & n | 56320))
            },
            re = /([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,
            ie = function (e, t) {
                return t ? "\0" === e ? "\ufffd" : e.slice(0, -1) + "\\" + e.charCodeAt(e.length - 1).toString(16) + " " : "\\" + e
            },
            oe = function () {
                T()
            },
            ae = be(function (e) {
                return !0 === e.disabled && "fieldset" === e.nodeName.toLowerCase()
            }, {
                dir: "parentNode",
                next: "legend"
            });
        try {
            H.apply(t = O.call(p.childNodes), p.childNodes), t[p.childNodes.length].nodeType
        } catch (e) {
            H = {
                apply: t.length ? function (e, t) {
                    L.apply(e, O.call(t))
                } : function (e, t) {
                    var n = e.length,
                        r = 0;
                    while (e[n++] = t[r++]);
                    e.length = n - 1
                }
            }
        }

        function se(t, e, n, r) {
            var i, o, a, s, u, l, c, f = e && e.ownerDocument,
                p = e ? e.nodeType : 9;
            if (n = n || [], "string" != typeof t || !t || 1 !== p && 9 !== p && 11 !== p) return n;
            if (!r && (T(e), e = e || C, E)) {
                if (11 !== p && (u = Z.exec(t)))
                    if (i = u[1]) {
                        if (9 === p) {
                            if (!(a = e.getElementById(i))) return n;
                            if (a.id === i) return n.push(a), n
                        } else if (f && (a = f.getElementById(i)) && y(e, a) && a.id === i) return n.push(a), n
                    } else {
                        if (u[2]) return H.apply(n, e.getElementsByTagName(t)), n;
                        if ((i = u[3]) && d.getElementsByClassName && e.getElementsByClassName) return H.apply(n, e.getElementsByClassName(i)), n
                    } if (d.qsa && !N[t + " "] && (!v || !v.test(t)) && (1 !== p || "object" !== e.nodeName.toLowerCase())) {
                    if (c = t, f = e, 1 === p && (U.test(t) || z.test(t))) {
                        (f = ee.test(t) && ye(e.parentNode) || e) === e && d.scope || ((s = e.getAttribute("id")) ? s = s.replace(re, ie) : e.setAttribute("id", s = S)), o = (l = h(t)).length;
                        while (o--) l[o] = (s ? "#" + s : ":scope") + " " + xe(l[o]);
                        c = l.join(",")
                    }
                    try {
                        return H.apply(n, f.querySelectorAll(c)), n
                    } catch (e) {
                        N(t, !0)
                    } finally {
                        s === S && e.removeAttribute("id")
                    }
                }
            }
            return g(t.replace($, "$1"), e, n, r)
        }

        function ue() {
            var r = [];
            return function e(t, n) {
                return r.push(t + " ") > b.cacheLength && delete e[r.shift()], e[t + " "] = n
            }
        }

        function le(e) {
            return e[S] = !0, e
        }

        function ce(e) {
            var t = C.createElement("fieldset");
            try {
                return !!e(t)
            } catch (e) {
                return !1
            } finally {
                t.parentNode && t.parentNode.removeChild(t), t = null
            }
        }

        function fe(e, t) {
            var n = e.split("|"),
                r = n.length;
            while (r--) b.attrHandle[n[r]] = t
        }

        function pe(e, t) {
            var n = t && e,
                r = n && 1 === e.nodeType && 1 === t.nodeType && e.sourceIndex - t.sourceIndex;
            if (r) return r;
            if (n)
                while (n = n.nextSibling)
                    if (n === t) return -1;
            return e ? 1 : -1
        }

        function de(t) {
            return function (e) {
                return "input" === e.nodeName.toLowerCase() && e.type === t
            }
        }

        function he(n) {
            return function (e) {
                var t = e.nodeName.toLowerCase();
                return ("input" === t || "button" === t) && e.type === n
            }
        }

        function ge(t) {
            return function (e) {
                return "form" in e ? e.parentNode && !1 === e.disabled ? "label" in e ? "label" in e.parentNode ? e.parentNode.disabled === t : e.disabled === t : e.isDisabled === t || e.isDisabled !== !t && ae(e) === t : e.disabled === t : "label" in e && e.disabled === t
            }
        }

        function ve(a) {
            return le(function (o) {
                return o = +o, le(function (e, t) {
                    var n, r = a([], e.length, o),
                        i = r.length;
                    while (i--) e[n = r[i]] && (e[n] = !(t[n] = e[n]))
                })
            })
        }

        function ye(e) {
            return e && "undefined" != typeof e.getElementsByTagName && e
        }
        for (e in d = se.support = {}, i = se.isXML = function (e) {
                var t = e.namespaceURI,
                    n = (e.ownerDocument || e).documentElement;
                return !Y.test(t || n && n.nodeName || "HTML")
            }, T = se.setDocument = function (e) {
                var t, n, r = e ? e.ownerDocument || e : p;
                return r != C && 9 === r.nodeType && r.documentElement && (a = (C = r).documentElement, E = !i(C), p != C && (n = C.defaultView) && n.top !== n && (n.addEventListener ? n.addEventListener("unload", oe, !1) : n.attachEvent && n.attachEvent("onunload", oe)), d.scope = ce(function (e) {
                    return a.appendChild(e).appendChild(C.createElement("div")), "undefined" != typeof e.querySelectorAll && !e.querySelectorAll(":scope fieldset div").length
                }), d.attributes = ce(function (e) {
                    return e.className = "i", !e.getAttribute("className")
                }), d.getElementsByTagName = ce(function (e) {
                    return e.appendChild(C.createComment("")), !e.getElementsByTagName("*").length
                }), d.getElementsByClassName = K.test(C.getElementsByClassName), d.getById = ce(function (e) {
                    return a.appendChild(e).id = S, !C.getElementsByName || !C.getElementsByName(S).length
                }), d.getById ? (b.filter.ID = function (e) {
                    var t = e.replace(te, ne);
                    return function (e) {
                        return e.getAttribute("id") === t
                    }
                }, b.find.ID = function (e, t) {
                    if ("undefined" != typeof t.getElementById && E) {
                        var n = t.getElementById(e);
                        return n ? [n] : []
                    }
                }) : (b.filter.ID = function (e) {
                    var n = e.replace(te, ne);
                    return function (e) {
                        var t = "undefined" != typeof e.getAttributeNode && e.getAttributeNode("id");
                        return t && t.value === n
                    }
                }, b.find.ID = function (e, t) {
                    if ("undefined" != typeof t.getElementById && E) {
                        var n, r, i, o = t.getElementById(e);
                        if (o) {
                            if ((n = o.getAttributeNode("id")) && n.value === e) return [o];
                            i = t.getElementsByName(e), r = 0;
                            while (o = i[r++])
                                if ((n = o.getAttributeNode("id")) && n.value === e) return [o]
                        }
                        return []
                    }
                }), b.find.TAG = d.getElementsByTagName ? function (e, t) {
                    return "undefined" != typeof t.getElementsByTagName ? t.getElementsByTagName(e) : d.qsa ? t.querySelectorAll(e) : void 0
                } : function (e, t) {
                    var n, r = [],
                        i = 0,
                        o = t.getElementsByTagName(e);
                    if ("*" === e) {
                        while (n = o[i++]) 1 === n.nodeType && r.push(n);
                        return r
                    }
                    return o
                }, b.find.CLASS = d.getElementsByClassName && function (e, t) {
                    if ("undefined" != typeof t.getElementsByClassName && E) return t.getElementsByClassName(e)
                }, s = [], v = [], (d.qsa = K.test(C.querySelectorAll)) && (ce(function (e) {
                    var t;
                    a.appendChild(e).innerHTML = "<a id='" + S + "'></a><select id='" + S + "-\r\\' msallowcapture=''><option selected=''></option></select>", e.querySelectorAll("[msallowcapture^='']").length && v.push("[*^$]=" + M + "*(?:''|\"\")"), e.querySelectorAll("[selected]").length || v.push("\\[" + M + "*(?:value|" + R + ")"), e.querySelectorAll("[id~=" + S + "-]").length || v.push("~="), (t = C.createElement("input")).setAttribute("name", ""), e.appendChild(t), e.querySelectorAll("[name='']").length || v.push("\\[" + M + "*name" + M + "*=" + M + "*(?:''|\"\")"), e.querySelectorAll(":checked").length || v.push(":checked"), e.querySelectorAll("a#" + S + "+*").length || v.push(".#.+[+~]"), e.querySelectorAll("\\\f"), v.push("[\\r\\n\\f]")
                }), ce(function (e) {
                    e.innerHTML = "<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";
                    var t = C.createElement("input");
                    t.setAttribute("type", "hidden"), e.appendChild(t).setAttribute("name", "D"), e.querySelectorAll("[name=d]").length && v.push("name" + M + "*[*^$|!~]?="), 2 !== e.querySelectorAll(":enabled").length && v.push(":enabled", ":disabled"), a.appendChild(e).disabled = !0, 2 !== e.querySelectorAll(":disabled").length && v.push(":enabled", ":disabled"), e.querySelectorAll("*,:x"), v.push(",.*:")
                })), (d.matchesSelector = K.test(c = a.matches || a.webkitMatchesSelector || a.mozMatchesSelector || a.oMatchesSelector || a.msMatchesSelector)) && ce(function (e) {
                    d.disconnectedMatch = c.call(e, "*"), c.call(e, "[s!='']:x"), s.push("!=", F)
                }), v = v.length && new RegExp(v.join("|")), s = s.length && new RegExp(s.join("|")), t = K.test(a.compareDocumentPosition), y = t || K.test(a.contains) ? function (e, t) {
                    var n = 9 === e.nodeType ? e.documentElement : e,
                        r = t && t.parentNode;
                    return e === r || !(!r || 1 !== r.nodeType || !(n.contains ? n.contains(r) : e.compareDocumentPosition && 16 & e.compareDocumentPosition(r)))
                } : function (e, t) {
                    if (t)
                        while (t = t.parentNode)
                            if (t === e) return !0;
                    return !1
                }, D = t ? function (e, t) {
                    if (e === t) return l = !0, 0;
                    var n = !e.compareDocumentPosition - !t.compareDocumentPosition;
                    return n || (1 & (n = (e.ownerDocument || e) == (t.ownerDocument || t) ? e.compareDocumentPosition(t) : 1) || !d.sortDetached && t.compareDocumentPosition(e) === n ? e == C || e.ownerDocument == p && y(p, e) ? -1 : t == C || t.ownerDocument == p && y(p, t) ? 1 : u ? P(u, e) - P(u, t) : 0 : 4 & n ? -1 : 1)
                } : function (e, t) {
                    if (e === t) return l = !0, 0;
                    var n, r = 0,
                        i = e.parentNode,
                        o = t.parentNode,
                        a = [e],
                        s = [t];
                    if (!i || !o) return e == C ? -1 : t == C ? 1 : i ? -1 : o ? 1 : u ? P(u, e) - P(u, t) : 0;
                    if (i === o) return pe(e, t);
                    n = e;
                    while (n = n.parentNode) a.unshift(n);
                    n = t;
                    while (n = n.parentNode) s.unshift(n);
                    while (a[r] === s[r]) r++;
                    return r ? pe(a[r], s[r]) : a[r] == p ? -1 : s[r] == p ? 1 : 0
                }), C
            }, se.matches = function (e, t) {
                return se(e, null, null, t)
            }, se.matchesSelector = function (e, t) {
                if (T(e), d.matchesSelector && E && !N[t + " "] && (!s || !s.test(t)) && (!v || !v.test(t))) try {
                    var n = c.call(e, t);
                    if (n || d.disconnectedMatch || e.document && 11 !== e.document.nodeType) return n
                } catch (e) {
                    N(t, !0)
                }
                return 0 < se(t, C, null, [e]).length
            }, se.contains = function (e, t) {
                return (e.ownerDocument || e) != C && T(e), y(e, t)
            }, se.attr = function (e, t) {
                (e.ownerDocument || e) != C && T(e);
                var n = b.attrHandle[t.toLowerCase()],
                    r = n && j.call(b.attrHandle, t.toLowerCase()) ? n(e, t, !E) : void 0;
                return void 0 !== r ? r : d.attributes || !E ? e.getAttribute(t) : (r = e.getAttributeNode(t)) && r.specified ? r.value : null
            }, se.escape = function (e) {
                return (e + "").replace(re, ie)
            }, se.error = function (e) {
                throw new Error("Syntax error, unrecognized expression: " + e)
            }, se.uniqueSort = function (e) {
                var t, n = [],
                    r = 0,
                    i = 0;
                if (l = !d.detectDuplicates, u = !d.sortStable && e.slice(0), e.sort(D), l) {
                    while (t = e[i++]) t === e[i] && (r = n.push(i));
                    while (r--) e.splice(n[r], 1)
                }
                return u = null, e
            }, o = se.getText = function (e) {
                var t, n = "",
                    r = 0,
                    i = e.nodeType;
                if (i) {
                    if (1 === i || 9 === i || 11 === i) {
                        if ("string" == typeof e.textContent) return e.textContent;
                        for (e = e.firstChild; e; e = e.nextSibling) n += o(e)
                    } else if (3 === i || 4 === i) return e.nodeValue
                } else
                    while (t = e[r++]) n += o(t);
                return n
            }, (b = se.selectors = {
                cacheLength: 50,
                createPseudo: le,
                match: G,
                attrHandle: {},
                find: {},
                relative: {
                    ">": {
                        dir: "parentNode",
                        first: !0
                    },
                    " ": {
                        dir: "parentNode"
                    },
                    "+": {
                        dir: "previousSibling",
                        first: !0
                    },
                    "~": {
                        dir: "previousSibling"
                    }
                },
                preFilter: {
                    ATTR: function (e) {
                        return e[1] = e[1].replace(te, ne), e[3] = (e[3] || e[4] || e[5] || "").replace(te, ne), "~=" === e[2] && (e[3] = " " + e[3] + " "), e.slice(0, 4)
                    },
                    CHILD: function (e) {
                        return e[1] = e[1].toLowerCase(), "nth" === e[1].slice(0, 3) ? (e[3] || se.error(e[0]), e[4] = +(e[4] ? e[5] + (e[6] || 1) : 2 * ("even" === e[3] || "odd" === e[3])), e[5] = +(e[7] + e[8] || "odd" === e[3])) : e[3] && se.error(e[0]), e
                    },
                    PSEUDO: function (e) {
                        var t, n = !e[6] && e[2];
                        return G.CHILD.test(e[0]) ? null : (e[3] ? e[2] = e[4] || e[5] || "" : n && X.test(n) && (t = h(n, !0)) && (t = n.indexOf(")", n.length - t) - n.length) && (e[0] = e[0].slice(0, t), e[2] = n.slice(0, t)), e.slice(0, 3))
                    }
                },
                filter: {
                    TAG: function (e) {
                        var t = e.replace(te, ne).toLowerCase();
                        return "*" === e ? function () {
                            return !0
                        } : function (e) {
                            return e.nodeName && e.nodeName.toLowerCase() === t
                        }
                    },
                    CLASS: function (e) {
                        var t = m[e + " "];
                        return t || (t = new RegExp("(^|" + M + ")" + e + "(" + M + "|$)")) && m(e, function (e) {
                            return t.test("string" == typeof e.className && e.className || "undefined" != typeof e.getAttribute && e.getAttribute("class") || "")
                        })
                    },
                    ATTR: function (n, r, i) {
                        return function (e) {
                            var t = se.attr(e, n);
                            return null == t ? "!=" === r : !r || (t += "", "=" === r ? t === i : "!=" === r ? t !== i : "^=" === r ? i && 0 === t.indexOf(i) : "*=" === r ? i && -1 < t.indexOf(i) : "$=" === r ? i && t.slice(-i.length) === i : "~=" === r ? -1 < (" " + t.replace(B, " ") + " ").indexOf(i) : "|=" === r && (t === i || t.slice(0, i.length + 1) === i + "-"))
                        }
                    },
                    CHILD: function (h, e, t, g, v) {
                        var y = "nth" !== h.slice(0, 3),
                            m = "last" !== h.slice(-4),
                            x = "of-type" === e;
                        return 1 === g && 0 === v ? function (e) {
                            return !!e.parentNode
                        } : function (e, t, n) {
                            var r, i, o, a, s, u, l = y !== m ? "nextSibling" : "previousSibling",
                                c = e.parentNode,
                                f = x && e.nodeName.toLowerCase(),
                                p = !n && !x,
                                d = !1;
                            if (c) {
                                if (y) {
                                    while (l) {
                                        a = e;
                                        while (a = a[l])
                                            if (x ? a.nodeName.toLowerCase() === f : 1 === a.nodeType) return !1;
                                        u = l = "only" === h && !u && "nextSibling"
                                    }
                                    return !0
                                }
                                if (u = [m ? c.firstChild : c.lastChild], m && p) {
                                    d = (s = (r = (i = (o = (a = c)[S] || (a[S] = {}))[a.uniqueID] || (o[a.uniqueID] = {}))[h] || [])[0] === k && r[1]) && r[2], a = s && c.childNodes[s];
                                    while (a = ++s && a && a[l] || (d = s = 0) || u.pop())
                                        if (1 === a.nodeType && ++d && a === e) {
                                            i[h] = [k, s, d];
                                            break
                                        }
                                } else if (p && (d = s = (r = (i = (o = (a = e)[S] || (a[S] = {}))[a.uniqueID] || (o[a.uniqueID] = {}))[h] || [])[0] === k && r[1]), !1 === d)
                                    while (a = ++s && a && a[l] || (d = s = 0) || u.pop())
                                        if ((x ? a.nodeName.toLowerCase() === f : 1 === a.nodeType) && ++d && (p && ((i = (o = a[S] || (a[S] = {}))[a.uniqueID] || (o[a.uniqueID] = {}))[h] = [k, d]), a === e)) break;
                                return (d -= v) === g || d % g == 0 && 0 <= d / g
                            }
                        }
                    },
                    PSEUDO: function (e, o) {
                        var t, a = b.pseudos[e] || b.setFilters[e.toLowerCase()] || se.error("unsupported pseudo: " + e);
                        return a[S] ? a(o) : 1 < a.length ? (t = [e, e, "", o], b.setFilters.hasOwnProperty(e.toLowerCase()) ? le(function (e, t) {
                            var n, r = a(e, o),
                                i = r.length;
                            while (i--) e[n = P(e, r[i])] = !(t[n] = r[i])
                        }) : function (e) {
                            return a(e, 0, t)
                        }) : a
                    }
                },
                pseudos: {
                    not: le(function (e) {
                        var r = [],
                            i = [],
                            s = f(e.replace($, "$1"));
                        return s[S] ? le(function (e, t, n, r) {
                            var i, o = s(e, null, r, []),
                                a = e.length;
                            while (a--)(i = o[a]) && (e[a] = !(t[a] = i))
                        }) : function (e, t, n) {
                            return r[0] = e, s(r, null, n, i), r[0] = null, !i.pop()
                        }
                    }),
                    has: le(function (t) {
                        return function (e) {
                            return 0 < se(t, e).length
                        }
                    }),
                    contains: le(function (t) {
                        return t = t.replace(te, ne),
                            function (e) {
                                return -1 < (e.textContent || o(e)).indexOf(t)
                            }
                    }),
                    lang: le(function (n) {
                        return V.test(n || "") || se.error("unsupported lang: " + n), n = n.replace(te, ne).toLowerCase(),
                            function (e) {
                                var t;
                                do {
                                    if (t = E ? e.lang : e.getAttribute("xml:lang") || e.getAttribute("lang")) return (t = t.toLowerCase()) === n || 0 === t.indexOf(n + "-")
                                } while ((e = e.parentNode) && 1 === e.nodeType);
                                return !1
                            }
                    }),
                    target: function (e) {
                        var t = n.location && n.location.hash;
                        return t && t.slice(1) === e.id
                    },
                    root: function (e) {
                        return e === a
                    },
                    focus: function (e) {
                        return e === C.activeElement && (!C.hasFocus || C.hasFocus()) && !!(e.type || e.href || ~e.tabIndex)
                    },
                    enabled: ge(!1),
                    disabled: ge(!0),
                    checked: function (e) {
                        var t = e.nodeName.toLowerCase();
                        return "input" === t && !!e.checked || "option" === t && !!e.selected
                    },
                    selected: function (e) {
                        return e.parentNode && e.parentNode.selectedIndex, !0 === e.selected
                    },
                    empty: function (e) {
                        for (e = e.firstChild; e; e = e.nextSibling)
                            if (e.nodeType < 6) return !1;
                        return !0
                    },
                    parent: function (e) {
                        return !b.pseudos.empty(e)
                    },
                    header: function (e) {
                        return J.test(e.nodeName)
                    },
                    input: function (e) {
                        return Q.test(e.nodeName)
                    },
                    button: function (e) {
                        var t = e.nodeName.toLowerCase();
                        return "input" === t && "button" === e.type || "button" === t
                    },
                    text: function (e) {
                        var t;
                        return "input" === e.nodeName.toLowerCase() && "text" === e.type && (null == (t = e.getAttribute("type")) || "text" === t.toLowerCase())
                    },
                    first: ve(function () {
                        return [0]
                    }),
                    last: ve(function (e, t) {
                        return [t - 1]
                    }),
                    eq: ve(function (e, t, n) {
                        return [n < 0 ? n + t : n]
                    }),
                    even: ve(function (e, t) {
                        for (var n = 0; n < t; n += 2) e.push(n);
                        return e
                    }),
                    odd: ve(function (e, t) {
                        for (var n = 1; n < t; n += 2) e.push(n);
                        return e
                    }),
                    lt: ve(function (e, t, n) {
                        for (var r = n < 0 ? n + t : t < n ? t : n; 0 <= --r;) e.push(r);
                        return e
                    }),
                    gt: ve(function (e, t, n) {
                        for (var r = n < 0 ? n + t : n; ++r < t;) e.push(r);
                        return e
                    })
                }
            }).pseudos.nth = b.pseudos.eq, {
                radio: !0,
                checkbox: !0,
                file: !0,
                password: !0,
                image: !0
            }) b.pseudos[e] = de(e);
        for (e in {
                submit: !0,
                reset: !0
            }) b.pseudos[e] = he(e);

        function me() {}

        function xe(e) {
            for (var t = 0, n = e.length, r = ""; t < n; t++) r += e[t].value;
            return r
        }

        function be(s, e, t) {
            var u = e.dir,
                l = e.next,
                c = l || u,
                f = t && "parentNode" === c,
                p = r++;
            return e.first ? function (e, t, n) {
                while (e = e[u])
                    if (1 === e.nodeType || f) return s(e, t, n);
                return !1
            } : function (e, t, n) {
                var r, i, o, a = [k, p];
                if (n) {
                    while (e = e[u])
                        if ((1 === e.nodeType || f) && s(e, t, n)) return !0
                } else
                    while (e = e[u])
                        if (1 === e.nodeType || f)
                            if (i = (o = e[S] || (e[S] = {}))[e.uniqueID] || (o[e.uniqueID] = {}), l && l === e.nodeName.toLowerCase()) e = e[u] || e;
                            else {
                                if ((r = i[c]) && r[0] === k && r[1] === p) return a[2] = r[2];
                                if ((i[c] = a)[2] = s(e, t, n)) return !0
                            } return !1
            }
        }

        function we(i) {
            return 1 < i.length ? function (e, t, n) {
                var r = i.length;
                while (r--)
                    if (!i[r](e, t, n)) return !1;
                return !0
            } : i[0]
        }

        function Te(e, t, n, r, i) {
            for (var o, a = [], s = 0, u = e.length, l = null != t; s < u; s++)(o = e[s]) && (n && !n(o, r, i) || (a.push(o), l && t.push(s)));
            return a
        }

        function Ce(d, h, g, v, y, e) {
            return v && !v[S] && (v = Ce(v)), y && !y[S] && (y = Ce(y, e)), le(function (e, t, n, r) {
                var i, o, a, s = [],
                    u = [],
                    l = t.length,
                    c = e || function (e, t, n) {
                        for (var r = 0, i = t.length; r < i; r++) se(e, t[r], n);
                        return n
                    }(h || "*", n.nodeType ? [n] : n, []),
                    f = !d || !e && h ? c : Te(c, s, d, n, r),
                    p = g ? y || (e ? d : l || v) ? [] : t : f;
                if (g && g(f, p, n, r), v) {
                    i = Te(p, u), v(i, [], n, r), o = i.length;
                    while (o--)(a = i[o]) && (p[u[o]] = !(f[u[o]] = a))
                }
                if (e) {
                    if (y || d) {
                        if (y) {
                            i = [], o = p.length;
                            while (o--)(a = p[o]) && i.push(f[o] = a);
                            y(null, p = [], i, r)
                        }
                        o = p.length;
                        while (o--)(a = p[o]) && -1 < (i = y ? P(e, a) : s[o]) && (e[i] = !(t[i] = a))
                    }
                } else p = Te(p === t ? p.splice(l, p.length) : p), y ? y(null, t, p, r) : H.apply(t, p)
            })
        }

        function Ee(e) {
            for (var i, t, n, r = e.length, o = b.relative[e[0].type], a = o || b.relative[" "], s = o ? 1 : 0, u = be(function (e) {
                    return e === i
                }, a, !0), l = be(function (e) {
                    return -1 < P(i, e)
                }, a, !0), c = [function (e, t, n) {
                    var r = !o && (n || t !== w) || ((i = t).nodeType ? u(e, t, n) : l(e, t, n));
                    return i = null, r
                }]; s < r; s++)
                if (t = b.relative[e[s].type]) c = [be(we(c), t)];
                else {
                    if ((t = b.filter[e[s].type].apply(null, e[s].matches))[S]) {
                        for (n = ++s; n < r; n++)
                            if (b.relative[e[n].type]) break;
                        return Ce(1 < s && we(c), 1 < s && xe(e.slice(0, s - 1).concat({
                            value: " " === e[s - 2].type ? "*" : ""
                        })).replace($, "$1"), t, s < n && Ee(e.slice(s, n)), n < r && Ee(e = e.slice(n)), n < r && xe(e))
                    }
                    c.push(t)
                } return we(c)
        }
        return me.prototype = b.filters = b.pseudos, b.setFilters = new me, h = se.tokenize = function (e, t) {
            var n, r, i, o, a, s, u, l = x[e + " "];
            if (l) return t ? 0 : l.slice(0);
            a = e, s = [], u = b.preFilter;
            while (a) {
                for (o in n && !(r = _.exec(a)) || (r && (a = a.slice(r[0].length) || a), s.push(i = [])), n = !1, (r = z.exec(a)) && (n = r.shift(), i.push({
                        value: n,
                        type: r[0].replace($, " ")
                    }), a = a.slice(n.length)), b.filter) !(r = G[o].exec(a)) || u[o] && !(r = u[o](r)) || (n = r.shift(), i.push({
                    value: n,
                    type: o,
                    matches: r
                }), a = a.slice(n.length));
                if (!n) break
            }
            return t ? a.length : a ? se.error(e) : x(e, s).slice(0)
        }, f = se.compile = function (e, t) {
            var n, v, y, m, x, r, i = [],
                o = [],
                a = A[e + " "];
            if (!a) {
                t || (t = h(e)), n = t.length;
                while (n--)(a = Ee(t[n]))[S] ? i.push(a) : o.push(a);
                (a = A(e, (v = o, m = 0 < (y = i).length, x = 0 < v.length, r = function (e, t, n, r, i) {
                    var o, a, s, u = 0,
                        l = "0",
                        c = e && [],
                        f = [],
                        p = w,
                        d = e || x && b.find.TAG("*", i),
                        h = k += null == p ? 1 : Math.random() || .1,
                        g = d.length;
                    for (i && (w = t == C || t || i); l !== g && null != (o = d[l]); l++) {
                        if (x && o) {
                            a = 0, t || o.ownerDocument == C || (T(o), n = !E);
                            while (s = v[a++])
                                if (s(o, t || C, n)) {
                                    r.push(o);
                                    break
                                } i && (k = h)
                        }
                        m && ((o = !s && o) && u--, e && c.push(o))
                    }
                    if (u += l, m && l !== u) {
                        a = 0;
                        while (s = y[a++]) s(c, f, t, n);
                        if (e) {
                            if (0 < u)
                                while (l--) c[l] || f[l] || (f[l] = q.call(r));
                            f = Te(f)
                        }
                        H.apply(r, f), i && !e && 0 < f.length && 1 < u + y.length && se.uniqueSort(r)
                    }
                    return i && (k = h, w = p), c
                }, m ? le(r) : r))).selector = e
            }
            return a
        }, g = se.select = function (e, t, n, r) {
            var i, o, a, s, u, l = "function" == typeof e && e,
                c = !r && h(e = l.selector || e);
            if (n = n || [], 1 === c.length) {
                if (2 < (o = c[0] = c[0].slice(0)).length && "ID" === (a = o[0]).type && 9 === t.nodeType && E && b.relative[o[1].type]) {
                    if (!(t = (b.find.ID(a.matches[0].replace(te, ne), t) || [])[0])) return n;
                    l && (t = t.parentNode), e = e.slice(o.shift().value.length)
                }
                i = G.needsContext.test(e) ? 0 : o.length;
                while (i--) {
                    if (a = o[i], b.relative[s = a.type]) break;
                    if ((u = b.find[s]) && (r = u(a.matches[0].replace(te, ne), ee.test(o[0].type) && ye(t.parentNode) || t))) {
                        if (o.splice(i, 1), !(e = r.length && xe(o))) return H.apply(n, r), n;
                        break
                    }
                }
            }
            return (l || f(e, c))(r, t, !E, n, !t || ee.test(e) && ye(t.parentNode) || t), n
        }, d.sortStable = S.split("").sort(D).join("") === S, d.detectDuplicates = !!l, T(), d.sortDetached = ce(function (e) {
            return 1 & e.compareDocumentPosition(C.createElement("fieldset"))
        }), ce(function (e) {
            return e.innerHTML = "<a href='#'></a>", "#" === e.firstChild.getAttribute("href")
        }) || fe("type|href|height|width", function (e, t, n) {
            if (!n) return e.getAttribute(t, "type" === t.toLowerCase() ? 1 : 2)
        }), d.attributes && ce(function (e) {
            return e.innerHTML = "<input/>", e.firstChild.setAttribute("value", ""), "" === e.firstChild.getAttribute("value")
        }) || fe("value", function (e, t, n) {
            if (!n && "input" === e.nodeName.toLowerCase()) return e.defaultValue
        }), ce(function (e) {
            return null == e.getAttribute("disabled")
        }) || fe(R, function (e, t, n) {
            var r;
            if (!n) return !0 === e[t] ? t.toLowerCase() : (r = e.getAttributeNode(t)) && r.specified ? r.value : null
        }), se
    }(C);
    S.find = d, S.expr = d.selectors, S.expr[":"] = S.expr.pseudos, S.uniqueSort = S.unique = d.uniqueSort, S.text = d.getText, S.isXMLDoc = d.isXML, S.contains = d.contains, S.escapeSelector = d.escape;
    var h = function (e, t, n) {
            var r = [],
                i = void 0 !== n;
            while ((e = e[t]) && 9 !== e.nodeType)
                if (1 === e.nodeType) {
                    if (i && S(e).is(n)) break;
                    r.push(e)
                } return r
        },
        T = function (e, t) {
            for (var n = []; e; e = e.nextSibling) 1 === e.nodeType && e !== t && n.push(e);
            return n
        },
        k = S.expr.match.needsContext;

    function A(e, t) {
        return e.nodeName && e.nodeName.toLowerCase() === t.toLowerCase()
    }
    var N = /^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;

    function D(e, n, r) {
        return m(n) ? S.grep(e, function (e, t) {
            return !!n.call(e, t, e) !== r
        }) : n.nodeType ? S.grep(e, function (e) {
            return e === n !== r
        }) : "string" != typeof n ? S.grep(e, function (e) {
            return -1 < i.call(n, e) !== r
        }) : S.filter(n, e, r)
    }
    S.filter = function (e, t, n) {
        var r = t[0];
        return n && (e = ":not(" + e + ")"), 1 === t.length && 1 === r.nodeType ? S.find.matchesSelector(r, e) ? [r] : [] : S.find.matches(e, S.grep(t, function (e) {
            return 1 === e.nodeType
        }))
    }, S.fn.extend({
        find: function (e) {
            var t, n, r = this.length,
                i = this;
            if ("string" != typeof e) return this.pushStack(S(e).filter(function () {
                for (t = 0; t < r; t++)
                    if (S.contains(i[t], this)) return !0
            }));
            for (n = this.pushStack([]), t = 0; t < r; t++) S.find(e, i[t], n);
            return 1 < r ? S.uniqueSort(n) : n
        },
        filter: function (e) {
            return this.pushStack(D(this, e || [], !1))
        },
        not: function (e) {
            return this.pushStack(D(this, e || [], !0))
        },
        is: function (e) {
            return !!D(this, "string" == typeof e && k.test(e) ? S(e) : e || [], !1).length
        }
    });
    var j, q = /^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;
    (S.fn.init = function (e, t, n) {
        var r, i;
        if (!e) return this;
        if (n = n || j, "string" == typeof e) {
            if (!(r = "<" === e[0] && ">" === e[e.length - 1] && 3 <= e.length ? [null, e, null] : q.exec(e)) || !r[1] && t) return !t || t.jquery ? (t || n).find(e) : this.constructor(t).find(e);
            if (r[1]) {
                if (t = t instanceof S ? t[0] : t, S.merge(this, S.parseHTML(r[1], t && t.nodeType ? t.ownerDocument || t : E, !0)), N.test(r[1]) && S.isPlainObject(t))
                    for (r in t) m(this[r]) ? this[r](t[r]) : this.attr(r, t[r]);
                return this
            }
            return (i = E.getElementById(r[2])) && (this[0] = i, this.length = 1), this
        }
        return e.nodeType ? (this[0] = e, this.length = 1, this) : m(e) ? void 0 !== n.ready ? n.ready(e) : e(S) : S.makeArray(e, this)
    }).prototype = S.fn, j = S(E);
    var L = /^(?:parents|prev(?:Until|All))/,
        H = {
            children: !0,
            contents: !0,
            next: !0,
            prev: !0
        };

    function O(e, t) {
        while ((e = e[t]) && 1 !== e.nodeType);
        return e
    }
    S.fn.extend({
        has: function (e) {
            var t = S(e, this),
                n = t.length;
            return this.filter(function () {
                for (var e = 0; e < n; e++)
                    if (S.contains(this, t[e])) return !0
            })
        },
        closest: function (e, t) {
            var n, r = 0,
                i = this.length,
                o = [],
                a = "string" != typeof e && S(e);
            if (!k.test(e))
                for (; r < i; r++)
                    for (n = this[r]; n && n !== t; n = n.parentNode)
                        if (n.nodeType < 11 && (a ? -1 < a.index(n) : 1 === n.nodeType && S.find.matchesSelector(n, e))) {
                            o.push(n);
                            break
                        } return this.pushStack(1 < o.length ? S.uniqueSort(o) : o)
        },
        index: function (e) {
            return e ? "string" == typeof e ? i.call(S(e), this[0]) : i.call(this, e.jquery ? e[0] : e) : this[0] && this[0].parentNode ? this.first().prevAll().length : -1
        },
        add: function (e, t) {
            return this.pushStack(S.uniqueSort(S.merge(this.get(), S(e, t))))
        },
        addBack: function (e) {
            return this.add(null == e ? this.prevObject : this.prevObject.filter(e))
        }
    }), S.each({
        parent: function (e) {
            var t = e.parentNode;
            return t && 11 !== t.nodeType ? t : null
        },
        parents: function (e) {
            return h(e, "parentNode")
        },
        parentsUntil: function (e, t, n) {
            return h(e, "parentNode", n)
        },
        next: function (e) {
            return O(e, "nextSibling")
        },
        prev: function (e) {
            return O(e, "previousSibling")
        },
        nextAll: function (e) {
            return h(e, "nextSibling")
        },
        prevAll: function (e) {
            return h(e, "previousSibling")
        },
        nextUntil: function (e, t, n) {
            return h(e, "nextSibling", n)
        },
        prevUntil: function (e, t, n) {
            return h(e, "previousSibling", n)
        },
        siblings: function (e) {
            return T((e.parentNode || {}).firstChild, e)
        },
        children: function (e) {
            return T(e.firstChild)
        },
        contents: function (e) {
            return null != e.contentDocument && r(e.contentDocument) ? e.contentDocument : (A(e, "template") && (e = e.content || e), S.merge([], e.childNodes))
        }
    }, function (r, i) {
        S.fn[r] = function (e, t) {
            var n = S.map(this, i, e);
            return "Until" !== r.slice(-5) && (t = e), t && "string" == typeof t && (n = S.filter(t, n)), 1 < this.length && (H[r] || S.uniqueSort(n), L.test(r) && n.reverse()), this.pushStack(n)
        }
    });
    var P = /[^\x20\t\r\n\f]+/g;

    function R(e) {
        return e
    }

    function M(e) {
        throw e
    }

    function I(e, t, n, r) {
        var i;
        try {
            e && m(i = e.promise) ? i.call(e).done(t).fail(n) : e && m(i = e.then) ? i.call(e, t, n) : t.apply(void 0, [e].slice(r))
        } catch (e) {
            n.apply(void 0, [e])
        }
    }
    S.Callbacks = function (r) {
        var e, n;
        r = "string" == typeof r ? (e = r, n = {}, S.each(e.match(P) || [], function (e, t) {
            n[t] = !0
        }), n) : S.extend({}, r);
        var i, t, o, a, s = [],
            u = [],
            l = -1,
            c = function () {
                for (a = a || r.once, o = i = !0; u.length; l = -1) {
                    t = u.shift();
                    while (++l < s.length) !1 === s[l].apply(t[0], t[1]) && r.stopOnFalse && (l = s.length, t = !1)
                }
                r.memory || (t = !1), i = !1, a && (s = t ? [] : "")
            },
            f = {
                add: function () {
                    return s && (t && !i && (l = s.length - 1, u.push(t)), function n(e) {
                        S.each(e, function (e, t) {
                            m(t) ? r.unique && f.has(t) || s.push(t) : t && t.length && "string" !== w(t) && n(t)
                        })
                    }(arguments), t && !i && c()), this
                },
                remove: function () {
                    return S.each(arguments, function (e, t) {
                        var n;
                        while (-1 < (n = S.inArray(t, s, n))) s.splice(n, 1), n <= l && l--
                    }), this
                },
                has: function (e) {
                    return e ? -1 < S.inArray(e, s) : 0 < s.length
                },
                empty: function () {
                    return s && (s = []), this
                },
                disable: function () {
                    return a = u = [], s = t = "", this
                },
                disabled: function () {
                    return !s
                },
                lock: function () {
                    return a = u = [], t || i || (s = t = ""), this
                },
                locked: function () {
                    return !!a
                },
                fireWith: function (e, t) {
                    return a || (t = [e, (t = t || []).slice ? t.slice() : t], u.push(t), i || c()), this
                },
                fire: function () {
                    return f.fireWith(this, arguments), this
                },
                fired: function () {
                    return !!o
                }
            };
        return f
    }, S.extend({
        Deferred: function (e) {
            var o = [
                    ["notify", "progress", S.Callbacks("memory"), S.Callbacks("memory"), 2],
                    ["resolve", "done", S.Callbacks("once memory"), S.Callbacks("once memory"), 0, "resolved"],
                    ["reject", "fail", S.Callbacks("once memory"), S.Callbacks("once memory"), 1, "rejected"]
                ],
                i = "pending",
                a = {
                    state: function () {
                        return i
                    },
                    always: function () {
                        return s.done(arguments).fail(arguments), this
                    },
                    "catch": function (e) {
                        return a.then(null, e)
                    },
                    pipe: function () {
                        var i = arguments;
                        return S.Deferred(function (r) {
                            S.each(o, function (e, t) {
                                var n = m(i[t[4]]) && i[t[4]];
                                s[t[1]](function () {
                                    var e = n && n.apply(this, arguments);
                                    e && m(e.promise) ? e.promise().progress(r.notify).done(r.resolve).fail(r.reject) : r[t[0] + "With"](this, n ? [e] : arguments)
                                })
                            }), i = null
                        }).promise()
                    },
                    then: function (t, n, r) {
                        var u = 0;

                        function l(i, o, a, s) {
                            return function () {
                                var n = this,
                                    r = arguments,
                                    e = function () {
                                        var e, t;
                                        if (!(i < u)) {
                                            if ((e = a.apply(n, r)) === o.promise()) throw new TypeError("Thenable self-resolution");
                                            t = e && ("object" == typeof e || "function" == typeof e) && e.then, m(t) ? s ? t.call(e, l(u, o, R, s), l(u, o, M, s)) : (u++, t.call(e, l(u, o, R, s), l(u, o, M, s), l(u, o, R, o.notifyWith))) : (a !== R && (n = void 0, r = [e]), (s || o.resolveWith)(n, r))
                                        }
                                    },
                                    t = s ? e : function () {
                                        try {
                                            e()
                                        } catch (e) {
                                            S.Deferred.exceptionHook && S.Deferred.exceptionHook(e, t.stackTrace), u <= i + 1 && (a !== M && (n = void 0, r = [e]), o.rejectWith(n, r))
                                        }
                                    };
                                i ? t() : (S.Deferred.getStackHook && (t.stackTrace = S.Deferred.getStackHook()), C.setTimeout(t))
                            }
                        }
                        return S.Deferred(function (e) {
                            o[0][3].add(l(0, e, m(r) ? r : R, e.notifyWith)), o[1][3].add(l(0, e, m(t) ? t : R)), o[2][3].add(l(0, e, m(n) ? n : M))
                        }).promise()
                    },
                    promise: function (e) {
                        return null != e ? S.extend(e, a) : a
                    }
                },
                s = {};
            return S.each(o, function (e, t) {
                var n = t[2],
                    r = t[5];
                a[t[1]] = n.add, r && n.add(function () {
                    i = r
                }, o[3 - e][2].disable, o[3 - e][3].disable, o[0][2].lock, o[0][3].lock), n.add(t[3].fire), s[t[0]] = function () {
                    return s[t[0] + "With"](this === s ? void 0 : this, arguments), this
                }, s[t[0] + "With"] = n.fireWith
            }), a.promise(s), e && e.call(s, s), s
        },
        when: function (e) {
            var n = arguments.length,
                t = n,
                r = Array(t),
                i = s.call(arguments),
                o = S.Deferred(),
                a = function (t) {
                    return function (e) {
                        r[t] = this, i[t] = 1 < arguments.length ? s.call(arguments) : e, --n || o.resolveWith(r, i)
                    }
                };
            if (n <= 1 && (I(e, o.done(a(t)).resolve, o.reject, !n), "pending" === o.state() || m(i[t] && i[t].then))) return o.then();
            while (t--) I(i[t], a(t), o.reject);
            return o.promise()
        }
    });
    var W = /^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;
    S.Deferred.exceptionHook = function (e, t) {
        C.console && C.console.warn && e && W.test(e.name) && C.console.warn("jQuery.Deferred exception: " + e.message, e.stack, t)
    }, S.readyException = function (e) {
        C.setTimeout(function () {
            throw e
        })
    };
    var F = S.Deferred();

    function B() {
        E.removeEventListener("DOMContentLoaded", B), C.removeEventListener("load", B), S.ready()
    }
    S.fn.ready = function (e) {
        return F.then(e)["catch"](function (e) {
            S.readyException(e)
        }), this
    }, S.extend({
        isReady: !1,
        readyWait: 1,
        ready: function (e) {
            (!0 === e ? --S.readyWait : S.isReady) || (S.isReady = !0) !== e && 0 < --S.readyWait || F.resolveWith(E, [S])
        }
    }), S.ready.then = F.then, "complete" === E.readyState || "loading" !== E.readyState && !E.documentElement.doScroll ? C.setTimeout(S.ready) : (E.addEventListener("DOMContentLoaded", B), C.addEventListener("load", B));
    var $ = function (e, t, n, r, i, o, a) {
            var s = 0,
                u = e.length,
                l = null == n;
            if ("object" === w(n))
                for (s in i = !0, n) $(e, t, s, n[s], !0, o, a);
            else if (void 0 !== r && (i = !0, m(r) || (a = !0), l && (a ? (t.call(e, r), t = null) : (l = t, t = function (e, t, n) {
                    return l.call(S(e), n)
                })), t))
                for (; s < u; s++) t(e[s], n, a ? r : r.call(e[s], s, t(e[s], n)));
            return i ? e : l ? t.call(e) : u ? t(e[0], n) : o
        },
        _ = /^-ms-/,
        z = /-([a-z])/g;

    function U(e, t) {
        return t.toUpperCase()
    }

    function X(e) {
        return e.replace(_, "ms-").replace(z, U)
    }
    var V = function (e) {
        return 1 === e.nodeType || 9 === e.nodeType || !+e.nodeType
    };

    function G() {
        this.expando = S.expando + G.uid++
    }
    G.uid = 1, G.prototype = {
        cache: function (e) {
            var t = e[this.expando];
            return t || (t = {}, V(e) && (e.nodeType ? e[this.expando] = t : Object.defineProperty(e, this.expando, {
                value: t,
                configurable: !0
            }))), t
        },
        set: function (e, t, n) {
            var r, i = this.cache(e);
            if ("string" == typeof t) i[X(t)] = n;
            else
                for (r in t) i[X(r)] = t[r];
            return i
        },
        get: function (e, t) {
            return void 0 === t ? this.cache(e) : e[this.expando] && e[this.expando][X(t)]
        },
        access: function (e, t, n) {
            return void 0 === t || t && "string" == typeof t && void 0 === n ? this.get(e, t) : (this.set(e, t, n), void 0 !== n ? n : t)
        },
        remove: function (e, t) {
            var n, r = e[this.expando];
            if (void 0 !== r) {
                if (void 0 !== t) {
                    n = (t = Array.isArray(t) ? t.map(X) : (t = X(t)) in r ? [t] : t.match(P) || []).length;
                    while (n--) delete r[t[n]]
                }(void 0 === t || S.isEmptyObject(r)) && (e.nodeType ? e[this.expando] = void 0 : delete e[this.expando])
            }
        },
        hasData: function (e) {
            var t = e[this.expando];
            return void 0 !== t && !S.isEmptyObject(t)
        }
    };
    var Y = new G,
        Q = new G,
        J = /^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,
        K = /[A-Z]/g;

    function Z(e, t, n) {
        var r, i;
        if (void 0 === n && 1 === e.nodeType)
            if (r = "data-" + t.replace(K, "-$&").toLowerCase(), "string" == typeof (n = e.getAttribute(r))) {
                try {
                    n = "true" === (i = n) || "false" !== i && ("null" === i ? null : i === +i + "" ? +i : J.test(i) ? JSON.parse(i) : i)
                } catch (e) {}
                Q.set(e, t, n)
            } else n = void 0;
        return n
    }
    S.extend({
        hasData: function (e) {
            return Q.hasData(e) || Y.hasData(e)
        },
        data: function (e, t, n) {
            return Q.access(e, t, n)
        },
        removeData: function (e, t) {
            Q.remove(e, t)
        },
        _data: function (e, t, n) {
            return Y.access(e, t, n)
        },
        _removeData: function (e, t) {
            Y.remove(e, t)
        }
    }), S.fn.extend({
        data: function (n, e) {
            var t, r, i, o = this[0],
                a = o && o.attributes;
            if (void 0 === n) {
                if (this.length && (i = Q.get(o), 1 === o.nodeType && !Y.get(o, "hasDataAttrs"))) {
                    t = a.length;
                    while (t--) a[t] && 0 === (r = a[t].name).indexOf("data-") && (r = X(r.slice(5)), Z(o, r, i[r]));
                    Y.set(o, "hasDataAttrs", !0)
                }
                return i
            }
            return "object" == typeof n ? this.each(function () {
                Q.set(this, n)
            }) : $(this, function (e) {
                var t;
                if (o && void 0 === e) return void 0 !== (t = Q.get(o, n)) ? t : void 0 !== (t = Z(o, n)) ? t : void 0;
                this.each(function () {
                    Q.set(this, n, e)
                })
            }, null, e, 1 < arguments.length, null, !0)
        },
        removeData: function (e) {
            return this.each(function () {
                Q.remove(this, e)
            })
        }
    }), S.extend({
        queue: function (e, t, n) {
            var r;
            if (e) return t = (t || "fx") + "queue", r = Y.get(e, t), n && (!r || Array.isArray(n) ? r = Y.access(e, t, S.makeArray(n)) : r.push(n)), r || []
        },
        dequeue: function (e, t) {
            t = t || "fx";
            var n = S.queue(e, t),
                r = n.length,
                i = n.shift(),
                o = S._queueHooks(e, t);
            "inprogress" === i && (i = n.shift(), r--), i && ("fx" === t && n.unshift("inprogress"), delete o.stop, i.call(e, function () {
                S.dequeue(e, t)
            }, o)), !r && o && o.empty.fire()
        },
        _queueHooks: function (e, t) {
            var n = t + "queueHooks";
            return Y.get(e, n) || Y.access(e, n, {
                empty: S.Callbacks("once memory").add(function () {
                    Y.remove(e, [t + "queue", n])
                })
            })
        }
    }), S.fn.extend({
        queue: function (t, n) {
            var e = 2;
            return "string" != typeof t && (n = t, t = "fx", e--), arguments.length < e ? S.queue(this[0], t) : void 0 === n ? this : this.each(function () {
                var e = S.queue(this, t, n);
                S._queueHooks(this, t), "fx" === t && "inprogress" !== e[0] && S.dequeue(this, t)
            })
        },
        dequeue: function (e) {
            return this.each(function () {
                S.dequeue(this, e)
            })
        },
        clearQueue: function (e) {
            return this.queue(e || "fx", [])
        },
        promise: function (e, t) {
            var n, r = 1,
                i = S.Deferred(),
                o = this,
                a = this.length,
                s = function () {
                    --r || i.resolveWith(o, [o])
                };
            "string" != typeof e && (t = e, e = void 0), e = e || "fx";
            while (a--)(n = Y.get(o[a], e + "queueHooks")) && n.empty && (r++, n.empty.add(s));
            return s(), i.promise(t)
        }
    });
    var ee = /[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,
        te = new RegExp("^(?:([+-])=|)(" + ee + ")([a-z%]*)$", "i"),
        ne = ["Top", "Right", "Bottom", "Left"],
        re = E.documentElement,
        ie = function (e) {
            return S.contains(e.ownerDocument, e)
        },
        oe = {
            composed: !0
        };
    re.getRootNode && (ie = function (e) {
        return S.contains(e.ownerDocument, e) || e.getRootNode(oe) === e.ownerDocument
    });
    var ae = function (e, t) {
        return "none" === (e = t || e).style.display || "" === e.style.display && ie(e) && "none" === S.css(e, "display")
    };

    function se(e, t, n, r) {
        var i, o, a = 20,
            s = r ? function () {
                return r.cur()
            } : function () {
                return S.css(e, t, "")
            },
            u = s(),
            l = n && n[3] || (S.cssNumber[t] ? "" : "px"),
            c = e.nodeType && (S.cssNumber[t] || "px" !== l && +u) && te.exec(S.css(e, t));
        if (c && c[3] !== l) {
            u /= 2, l = l || c[3], c = +u || 1;
            while (a--) S.style(e, t, c + l), (1 - o) * (1 - (o = s() / u || .5)) <= 0 && (a = 0), c /= o;
            c *= 2, S.style(e, t, c + l), n = n || []
        }
        return n && (c = +c || +u || 0, i = n[1] ? c + (n[1] + 1) * n[2] : +n[2], r && (r.unit = l, r.start = c, r.end = i)), i
    }
    var ue = {};

    function le(e, t) {
        for (var n, r, i, o, a, s, u, l = [], c = 0, f = e.length; c < f; c++)(r = e[c]).style && (n = r.style.display, t ? ("none" === n && (l[c] = Y.get(r, "display") || null, l[c] || (r.style.display = "")), "" === r.style.display && ae(r) && (l[c] = (u = a = o = void 0, a = (i = r).ownerDocument, s = i.nodeName, (u = ue[s]) || (o = a.body.appendChild(a.createElement(s)), u = S.css(o, "display"), o.parentNode.removeChild(o), "none" === u && (u = "block"), ue[s] = u)))) : "none" !== n && (l[c] = "none", Y.set(r, "display", n)));
        for (c = 0; c < f; c++) null != l[c] && (e[c].style.display = l[c]);
        return e
    }
    S.fn.extend({
        show: function () {
            return le(this, !0)
        },
        hide: function () {
            return le(this)
        },
        toggle: function (e) {
            return "boolean" == typeof e ? e ? this.show() : this.hide() : this.each(function () {
                ae(this) ? S(this).show() : S(this).hide()
            })
        }
    });
    var ce, fe, pe = /^(?:checkbox|radio)$/i,
        de = /<([a-z][^\/\0>\x20\t\r\n\f]*)/i,
        he = /^$|^module$|\/(?:java|ecma)script/i;
    ce = E.createDocumentFragment().appendChild(E.createElement("div")), (fe = E.createElement("input")).setAttribute("type", "radio"), fe.setAttribute("checked", "checked"), fe.setAttribute("name", "t"), ce.appendChild(fe), y.checkClone = ce.cloneNode(!0).cloneNode(!0).lastChild.checked, ce.innerHTML = "<textarea>x</textarea>", y.noCloneChecked = !!ce.cloneNode(!0).lastChild.defaultValue, ce.innerHTML = "<option></option>", y.option = !!ce.lastChild;
    var ge = {
        thead: [1, "<table>", "</table>"],
        col: [2, "<table><colgroup>", "</colgroup></table>"],
        tr: [2, "<table><tbody>", "</tbody></table>"],
        td: [3, "<table><tbody><tr>", "</tr></tbody></table>"],
        _default: [0, "", ""]
    };

    function ve(e, t) {
        var n;
        return n = "undefined" != typeof e.getElementsByTagName ? e.getElementsByTagName(t || "*") : "undefined" != typeof e.querySelectorAll ? e.querySelectorAll(t || "*") : [], void 0 === t || t && A(e, t) ? S.merge([e], n) : n
    }

    function ye(e, t) {
        for (var n = 0, r = e.length; n < r; n++) Y.set(e[n], "globalEval", !t || Y.get(t[n], "globalEval"))
    }
    ge.tbody = ge.tfoot = ge.colgroup = ge.caption = ge.thead, ge.th = ge.td, y.option || (ge.optgroup = ge.option = [1, "<select multiple='multiple'>", "</select>"]);
    var me = /<|&#?\w+;/;

    function xe(e, t, n, r, i) {
        for (var o, a, s, u, l, c, f = t.createDocumentFragment(), p = [], d = 0, h = e.length; d < h; d++)
            if ((o = e[d]) || 0 === o)
                if ("object" === w(o)) S.merge(p, o.nodeType ? [o] : o);
                else if (me.test(o)) {
            a = a || f.appendChild(t.createElement("div")), s = (de.exec(o) || ["", ""])[1].toLowerCase(), u = ge[s] || ge._default, a.innerHTML = u[1] + S.htmlPrefilter(o) + u[2], c = u[0];
            while (c--) a = a.lastChild;
            S.merge(p, a.childNodes), (a = f.firstChild).textContent = ""
        } else p.push(t.createTextNode(o));
        f.textContent = "", d = 0;
        while (o = p[d++])
            if (r && -1 < S.inArray(o, r)) i && i.push(o);
            else if (l = ie(o), a = ve(f.appendChild(o), "script"), l && ye(a), n) {
            c = 0;
            while (o = a[c++]) he.test(o.type || "") && n.push(o)
        }
        return f
    }
    var be = /^key/,
        we = /^(?:mouse|pointer|contextmenu|drag|drop)|click/,
        Te = /^([^.]*)(?:\.(.+)|)/;

    function Ce() {
        return !0
    }

    function Ee() {
        return !1
    }

    function Se(e, t) {
        return e === function () {
            try {
                return E.activeElement
            } catch (e) {}
        }() == ("focus" === t)
    }

    function ke(e, t, n, r, i, o) {
        var a, s;
        if ("object" == typeof t) {
            for (s in "string" != typeof n && (r = r || n, n = void 0), t) ke(e, s, n, r, t[s], o);
            return e
        }
        if (null == r && null == i ? (i = n, r = n = void 0) : null == i && ("string" == typeof n ? (i = r, r = void 0) : (i = r, r = n, n = void 0)), !1 === i) i = Ee;
        else if (!i) return e;
        return 1 === o && (a = i, (i = function (e) {
            return S().off(e), a.apply(this, arguments)
        }).guid = a.guid || (a.guid = S.guid++)), e.each(function () {
            S.event.add(this, t, i, r, n)
        })
    }

    function Ae(e, i, o) {
        o ? (Y.set(e, i, !1), S.event.add(e, i, {
            namespace: !1,
            handler: function (e) {
                var t, n, r = Y.get(this, i);
                if (1 & e.isTrigger && this[i]) {
                    if (r.length)(S.event.special[i] || {}).delegateType && e.stopPropagation();
                    else if (r = s.call(arguments), Y.set(this, i, r), t = o(this, i), this[i](), r !== (n = Y.get(this, i)) || t ? Y.set(this, i, !1) : n = {}, r !== n) return e.stopImmediatePropagation(), e.preventDefault(), n.value
                } else r.length && (Y.set(this, i, {
                    value: S.event.trigger(S.extend(r[0], S.Event.prototype), r.slice(1), this)
                }), e.stopImmediatePropagation())
            }
        })) : void 0 === Y.get(e, i) && S.event.add(e, i, Ce)
    }
    S.event = {
        global: {},
        add: function (t, e, n, r, i) {
            var o, a, s, u, l, c, f, p, d, h, g, v = Y.get(t);
            if (V(t)) {
                n.handler && (n = (o = n).handler, i = o.selector), i && S.find.matchesSelector(re, i), n.guid || (n.guid = S.guid++), (u = v.events) || (u = v.events = Object.create(null)), (a = v.handle) || (a = v.handle = function (e) {
                    return "undefined" != typeof S && S.event.triggered !== e.type ? S.event.dispatch.apply(t, arguments) : void 0
                }), l = (e = (e || "").match(P) || [""]).length;
                while (l--) d = g = (s = Te.exec(e[l]) || [])[1], h = (s[2] || "").split(".").sort(), d && (f = S.event.special[d] || {}, d = (i ? f.delegateType : f.bindType) || d, f = S.event.special[d] || {}, c = S.extend({
                    type: d,
                    origType: g,
                    data: r,
                    handler: n,
                    guid: n.guid,
                    selector: i,
                    needsContext: i && S.expr.match.needsContext.test(i),
                    namespace: h.join(".")
                }, o), (p = u[d]) || ((p = u[d] = []).delegateCount = 0, f.setup && !1 !== f.setup.call(t, r, h, a) || t.addEventListener && t.addEventListener(d, a)), f.add && (f.add.call(t, c), c.handler.guid || (c.handler.guid = n.guid)), i ? p.splice(p.delegateCount++, 0, c) : p.push(c), S.event.global[d] = !0)
            }
        },
        remove: function (e, t, n, r, i) {
            var o, a, s, u, l, c, f, p, d, h, g, v = Y.hasData(e) && Y.get(e);
            if (v && (u = v.events)) {
                l = (t = (t || "").match(P) || [""]).length;
                while (l--)
                    if (d = g = (s = Te.exec(t[l]) || [])[1], h = (s[2] || "").split(".").sort(), d) {
                        f = S.event.special[d] || {}, p = u[d = (r ? f.delegateType : f.bindType) || d] || [], s = s[2] && new RegExp("(^|\\.)" + h.join("\\.(?:.*\\.|)") + "(\\.|$)"), a = o = p.length;
                        while (o--) c = p[o], !i && g !== c.origType || n && n.guid !== c.guid || s && !s.test(c.namespace) || r && r !== c.selector && ("**" !== r || !c.selector) || (p.splice(o, 1), c.selector && p.delegateCount--, f.remove && f.remove.call(e, c));
                        a && !p.length && (f.teardown && !1 !== f.teardown.call(e, h, v.handle) || S.removeEvent(e, d, v.handle), delete u[d])
                    } else
                        for (d in u) S.event.remove(e, d + t[l], n, r, !0);
                S.isEmptyObject(u) && Y.remove(e, "handle events")
            }
        },
        dispatch: function (e) {
            var t, n, r, i, o, a, s = new Array(arguments.length),
                u = S.event.fix(e),
                l = (Y.get(this, "events") || Object.create(null))[u.type] || [],
                c = S.event.special[u.type] || {};
            for (s[0] = u, t = 1; t < arguments.length; t++) s[t] = arguments[t];
            if (u.delegateTarget = this, !c.preDispatch || !1 !== c.preDispatch.call(this, u)) {
                a = S.event.handlers.call(this, u, l), t = 0;
                while ((i = a[t++]) && !u.isPropagationStopped()) {
                    u.currentTarget = i.elem, n = 0;
                    while ((o = i.handlers[n++]) && !u.isImmediatePropagationStopped()) u.rnamespace && !1 !== o.namespace && !u.rnamespace.test(o.namespace) || (u.handleObj = o, u.data = o.data, void 0 !== (r = ((S.event.special[o.origType] || {}).handle || o.handler).apply(i.elem, s)) && !1 === (u.result = r) && (u.preventDefault(), u.stopPropagation()))
                }
                return c.postDispatch && c.postDispatch.call(this, u), u.result
            }
        },
        handlers: function (e, t) {
            var n, r, i, o, a, s = [],
                u = t.delegateCount,
                l = e.target;
            if (u && l.nodeType && !("click" === e.type && 1 <= e.button))
                for (; l !== this; l = l.parentNode || this)
                    if (1 === l.nodeType && ("click" !== e.type || !0 !== l.disabled)) {
                        for (o = [], a = {}, n = 0; n < u; n++) void 0 === a[i = (r = t[n]).selector + " "] && (a[i] = r.needsContext ? -1 < S(i, this).index(l) : S.find(i, this, null, [l]).length), a[i] && o.push(r);
                        o.length && s.push({
                            elem: l,
                            handlers: o
                        })
                    } return l = this, u < t.length && s.push({
                elem: l,
                handlers: t.slice(u)
            }), s
        },
        addProp: function (t, e) {
            Object.defineProperty(S.Event.prototype, t, {
                enumerable: !0,
                configurable: !0,
                get: m(e) ? function () {
                    if (this.originalEvent) return e(this.originalEvent)
                } : function () {
                    if (this.originalEvent) return this.originalEvent[t]
                },
                set: function (e) {
                    Object.defineProperty(this, t, {
                        enumerable: !0,
                        configurable: !0,
                        writable: !0,
                        value: e
                    })
                }
            })
        },
        fix: function (e) {
            return e[S.expando] ? e : new S.Event(e)
        },
        special: {
            load: {
                noBubble: !0
            },
            click: {
                setup: function (e) {
                    var t = this || e;
                    return pe.test(t.type) && t.click && A(t, "input") && Ae(t, "click", Ce), !1
                },
                trigger: function (e) {
                    var t = this || e;
                    return pe.test(t.type) && t.click && A(t, "input") && Ae(t, "click"), !0
                },
                _default: function (e) {
                    var t = e.target;
                    return pe.test(t.type) && t.click && A(t, "input") && Y.get(t, "click") || A(t, "a")
                }
            },
            beforeunload: {
                postDispatch: function (e) {
                    void 0 !== e.result && e.originalEvent && (e.originalEvent.returnValue = e.result)
                }
            }
        }
    }, S.removeEvent = function (e, t, n) {
        e.removeEventListener && e.removeEventListener(t, n)
    }, S.Event = function (e, t) {
        if (!(this instanceof S.Event)) return new S.Event(e, t);
        e && e.type ? (this.originalEvent = e, this.type = e.type, this.isDefaultPrevented = e.defaultPrevented || void 0 === e.defaultPrevented && !1 === e.returnValue ? Ce : Ee, this.target = e.target && 3 === e.target.nodeType ? e.target.parentNode : e.target, this.currentTarget = e.currentTarget, this.relatedTarget = e.relatedTarget) : this.type = e, t && S.extend(this, t), this.timeStamp = e && e.timeStamp || Date.now(), this[S.expando] = !0
    }, S.Event.prototype = {
        constructor: S.Event,
        isDefaultPrevented: Ee,
        isPropagationStopped: Ee,
        isImmediatePropagationStopped: Ee,
        isSimulated: !1,
        preventDefault: function () {
            var e = this.originalEvent;
            this.isDefaultPrevented = Ce, e && !this.isSimulated && e.preventDefault()
        },
        stopPropagation: function () {
            var e = this.originalEvent;
            this.isPropagationStopped = Ce, e && !this.isSimulated && e.stopPropagation()
        },
        stopImmediatePropagation: function () {
            var e = this.originalEvent;
            this.isImmediatePropagationStopped = Ce, e && !this.isSimulated && e.stopImmediatePropagation(), this.stopPropagation()
        }
    }, S.each({
        altKey: !0,
        bubbles: !0,
        cancelable: !0,
        changedTouches: !0,
        ctrlKey: !0,
        detail: !0,
        eventPhase: !0,
        metaKey: !0,
        pageX: !0,
        pageY: !0,
        shiftKey: !0,
        view: !0,
        "char": !0,
        code: !0,
        charCode: !0,
        key: !0,
        keyCode: !0,
        button: !0,
        buttons: !0,
        clientX: !0,
        clientY: !0,
        offsetX: !0,
        offsetY: !0,
        pointerId: !0,
        pointerType: !0,
        screenX: !0,
        screenY: !0,
        targetTouches: !0,
        toElement: !0,
        touches: !0,
        which: function (e) {
            var t = e.button;
            return null == e.which && be.test(e.type) ? null != e.charCode ? e.charCode : e.keyCode : !e.which && void 0 !== t && we.test(e.type) ? 1 & t ? 1 : 2 & t ? 3 : 4 & t ? 2 : 0 : e.which
        }
    }, S.event.addProp), S.each({
        focus: "focusin",
        blur: "focusout"
    }, function (e, t) {
        S.event.special[e] = {
            setup: function () {
                return Ae(this, e, Se), !1
            },
            trigger: function () {
                return Ae(this, e), !0
            },
            delegateType: t
        }
    }), S.each({
        mouseenter: "mouseover",
        mouseleave: "mouseout",
        pointerenter: "pointerover",
        pointerleave: "pointerout"
    }, function (e, i) {
        S.event.special[e] = {
            delegateType: i,
            bindType: i,
            handle: function (e) {
                var t, n = e.relatedTarget,
                    r = e.handleObj;
                return n && (n === this || S.contains(this, n)) || (e.type = r.origType, t = r.handler.apply(this, arguments), e.type = i), t
            }
        }
    }), S.fn.extend({
        on: function (e, t, n, r) {
            return ke(this, e, t, n, r)
        },
        one: function (e, t, n, r) {
            return ke(this, e, t, n, r, 1)
        },
        off: function (e, t, n) {
            var r, i;
            if (e && e.preventDefault && e.handleObj) return r = e.handleObj, S(e.delegateTarget).off(r.namespace ? r.origType + "." + r.namespace : r.origType, r.selector, r.handler), this;
            if ("object" == typeof e) {
                for (i in e) this.off(i, t, e[i]);
                return this
            }
            return !1 !== t && "function" != typeof t || (n = t, t = void 0), !1 === n && (n = Ee), this.each(function () {
                S.event.remove(this, e, n, t)
            })
        }
    });
    var Ne = /<script|<style|<link/i,
        De = /checked\s*(?:[^=]|=\s*.checked.)/i,
        je = /^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;

    function qe(e, t) {
        return A(e, "table") && A(11 !== t.nodeType ? t : t.firstChild, "tr") && S(e).children("tbody")[0] || e
    }

    function Le(e) {
        return e.type = (null !== e.getAttribute("type")) + "/" + e.type, e
    }

    function He(e) {
        return "true/" === (e.type || "").slice(0, 5) ? e.type = e.type.slice(5) : e.removeAttribute("type"), e
    }

    function Oe(e, t) {
        var n, r, i, o, a, s;
        if (1 === t.nodeType) {
            if (Y.hasData(e) && (s = Y.get(e).events))
                for (i in Y.remove(t, "handle events"), s)
                    for (n = 0, r = s[i].length; n < r; n++) S.event.add(t, i, s[i][n]);
            Q.hasData(e) && (o = Q.access(e), a = S.extend({}, o), Q.set(t, a))
        }
    }

    function Pe(n, r, i, o) {
        r = g(r);
        var e, t, a, s, u, l, c = 0,
            f = n.length,
            p = f - 1,
            d = r[0],
            h = m(d);
        if (h || 1 < f && "string" == typeof d && !y.checkClone && De.test(d)) return n.each(function (e) {
            var t = n.eq(e);
            h && (r[0] = d.call(this, e, t.html())), Pe(t, r, i, o)
        });
        if (f && (t = (e = xe(r, n[0].ownerDocument, !1, n, o)).firstChild, 1 === e.childNodes.length && (e = t), t || o)) {
            for (s = (a = S.map(ve(e, "script"), Le)).length; c < f; c++) u = e, c !== p && (u = S.clone(u, !0, !0), s && S.merge(a, ve(u, "script"))), i.call(n[c], u, c);
            if (s)
                for (l = a[a.length - 1].ownerDocument, S.map(a, He), c = 0; c < s; c++) u = a[c], he.test(u.type || "") && !Y.access(u, "globalEval") && S.contains(l, u) && (u.src && "module" !== (u.type || "").toLowerCase() ? S._evalUrl && !u.noModule && S._evalUrl(u.src, {
                    nonce: u.nonce || u.getAttribute("nonce")
                }, l) : b(u.textContent.replace(je, ""), u, l))
        }
        return n
    }

    function Re(e, t, n) {
        for (var r, i = t ? S.filter(t, e) : e, o = 0; null != (r = i[o]); o++) n || 1 !== r.nodeType || S.cleanData(ve(r)), r.parentNode && (n && ie(r) && ye(ve(r, "script")), r.parentNode.removeChild(r));
        return e
    }
    S.extend({
        htmlPrefilter: function (e) {
            return e
        },
        clone: function (e, t, n) {
            var r, i, o, a, s, u, l, c = e.cloneNode(!0),
                f = ie(e);
            if (!(y.noCloneChecked || 1 !== e.nodeType && 11 !== e.nodeType || S.isXMLDoc(e)))
                for (a = ve(c), r = 0, i = (o = ve(e)).length; r < i; r++) s = o[r], u = a[r], void 0, "input" === (l = u.nodeName.toLowerCase()) && pe.test(s.type) ? u.checked = s.checked : "input" !== l && "textarea" !== l || (u.defaultValue = s.defaultValue);
            if (t)
                if (n)
                    for (o = o || ve(e), a = a || ve(c), r = 0, i = o.length; r < i; r++) Oe(o[r], a[r]);
                else Oe(e, c);
            return 0 < (a = ve(c, "script")).length && ye(a, !f && ve(e, "script")), c
        },
        cleanData: function (e) {
            for (var t, n, r, i = S.event.special, o = 0; void 0 !== (n = e[o]); o++)
                if (V(n)) {
                    if (t = n[Y.expando]) {
                        if (t.events)
                            for (r in t.events) i[r] ? S.event.remove(n, r) : S.removeEvent(n, r, t.handle);
                        n[Y.expando] = void 0
                    }
                    n[Q.expando] && (n[Q.expando] = void 0)
                }
        }
    }), S.fn.extend({
        detach: function (e) {
            return Re(this, e, !0)
        },
        remove: function (e) {
            return Re(this, e)
        },
        text: function (e) {
            return $(this, function (e) {
                return void 0 === e ? S.text(this) : this.empty().each(function () {
                    1 !== this.nodeType && 11 !== this.nodeType && 9 !== this.nodeType || (this.textContent = e)
                })
            }, null, e, arguments.length)
        },
        append: function () {
            return Pe(this, arguments, function (e) {
                1 !== this.nodeType && 11 !== this.nodeType && 9 !== this.nodeType || qe(this, e).appendChild(e)
            })
        },
        prepend: function () {
            return Pe(this, arguments, function (e) {
                if (1 === this.nodeType || 11 === this.nodeType || 9 === this.nodeType) {
                    var t = qe(this, e);
                    t.insertBefore(e, t.firstChild)
                }
            })
        },
        before: function () {
            return Pe(this, arguments, function (e) {
                this.parentNode && this.parentNode.insertBefore(e, this)
            })
        },
        after: function () {
            return Pe(this, arguments, function (e) {
                this.parentNode && this.parentNode.insertBefore(e, this.nextSibling)
            })
        },
        empty: function () {
            for (var e, t = 0; null != (e = this[t]); t++) 1 === e.nodeType && (S.cleanData(ve(e, !1)), e.textContent = "");
            return this
        },
        clone: function (e, t) {
            return e = null != e && e, t = null == t ? e : t, this.map(function () {
                return S.clone(this, e, t)
            })
        },
        html: function (e) {
            return $(this, function (e) {
                var t = this[0] || {},
                    n = 0,
                    r = this.length;
                if (void 0 === e && 1 === t.nodeType) return t.innerHTML;
                if ("string" == typeof e && !Ne.test(e) && !ge[(de.exec(e) || ["", ""])[1].toLowerCase()]) {
                    e = S.htmlPrefilter(e);
                    try {
                        for (; n < r; n++) 1 === (t = this[n] || {}).nodeType && (S.cleanData(ve(t, !1)), t.innerHTML = e);
                        t = 0
                    } catch (e) {}
                }
                t && this.empty().append(e)
            }, null, e, arguments.length)
        },
        replaceWith: function () {
            var n = [];
            return Pe(this, arguments, function (e) {
                var t = this.parentNode;
                S.inArray(this, n) < 0 && (S.cleanData(ve(this)), t && t.replaceChild(e, this))
            }, n)
        }
    }), S.each({
        appendTo: "append",
        prependTo: "prepend",
        insertBefore: "before",
        insertAfter: "after",
        replaceAll: "replaceWith"
    }, function (e, a) {
        S.fn[e] = function (e) {
            for (var t, n = [], r = S(e), i = r.length - 1, o = 0; o <= i; o++) t = o === i ? this : this.clone(!0), S(r[o])[a](t), u.apply(n, t.get());
            return this.pushStack(n)
        }
    });
    var Me = new RegExp("^(" + ee + ")(?!px)[a-z%]+$", "i"),
        Ie = function (e) {
            var t = e.ownerDocument.defaultView;
            return t && t.opener || (t = C), t.getComputedStyle(e)
        },
        We = function (e, t, n) {
            var r, i, o = {};
            for (i in t) o[i] = e.style[i], e.style[i] = t[i];
            for (i in r = n.call(e), t) e.style[i] = o[i];
            return r
        },
        Fe = new RegExp(ne.join("|"), "i");

    function Be(e, t, n) {
        var r, i, o, a, s = e.style;
        return (n = n || Ie(e)) && ("" !== (a = n.getPropertyValue(t) || n[t]) || ie(e) || (a = S.style(e, t)), !y.pixelBoxStyles() && Me.test(a) && Fe.test(t) && (r = s.width, i = s.minWidth, o = s.maxWidth, s.minWidth = s.maxWidth = s.width = a, a = n.width, s.width = r, s.minWidth = i, s.maxWidth = o)), void 0 !== a ? a + "" : a
    }

    function $e(e, t) {
        return {
            get: function () {
                if (!e()) return (this.get = t).apply(this, arguments);
                delete this.get
            }
        }
    }! function () {
        function e() {
            if (l) {
                u.style.cssText = "position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0", l.style.cssText = "position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%", re.appendChild(u).appendChild(l);
                var e = C.getComputedStyle(l);
                n = "1%" !== e.top, s = 12 === t(e.marginLeft), l.style.right = "60%", o = 36 === t(e.right), r = 36 === t(e.width), l.style.position = "absolute", i = 12 === t(l.offsetWidth / 3), re.removeChild(u), l = null
            }
        }

        function t(e) {
            return Math.round(parseFloat(e))
        }
        var n, r, i, o, a, s, u = E.createElement("div"),
            l = E.createElement("div");
        l.style && (l.style.backgroundClip = "content-box", l.cloneNode(!0).style.backgroundClip = "", y.clearCloneStyle = "content-box" === l.style.backgroundClip, S.extend(y, {
            boxSizingReliable: function () {
                return e(), r
            },
            pixelBoxStyles: function () {
                return e(), o
            },
            pixelPosition: function () {
                return e(), n
            },
            reliableMarginLeft: function () {
                return e(), s
            },
            scrollboxSize: function () {
                return e(), i
            },
            reliableTrDimensions: function () {
                var e, t, n, r;
                return null == a && (e = E.createElement("table"), t = E.createElement("tr"), n = E.createElement("div"), e.style.cssText = "position:absolute;left:-11111px", t.style.height = "1px", n.style.height = "9px", re.appendChild(e).appendChild(t).appendChild(n), r = C.getComputedStyle(t), a = 3 < parseInt(r.height), re.removeChild(e)), a
            }
        }))
    }();
    var _e = ["Webkit", "Moz", "ms"],
        ze = E.createElement("div").style,
        Ue = {};

    function Xe(e) {
        var t = S.cssProps[e] || Ue[e];
        return t || (e in ze ? e : Ue[e] = function (e) {
            var t = e[0].toUpperCase() + e.slice(1),
                n = _e.length;
            while (n--)
                if ((e = _e[n] + t) in ze) return e
        }(e) || e)
    }
    var Ve = /^(none|table(?!-c[ea]).+)/,
        Ge = /^--/,
        Ye = {
            position: "absolute",
            visibility: "hidden",
            display: "block"
        },
        Qe = {
            letterSpacing: "0",
            fontWeight: "400"
        };

    function Je(e, t, n) {
        var r = te.exec(t);
        return r ? Math.max(0, r[2] - (n || 0)) + (r[3] || "px") : t
    }

    function Ke(e, t, n, r, i, o) {
        var a = "width" === t ? 1 : 0,
            s = 0,
            u = 0;
        if (n === (r ? "border" : "content")) return 0;
        for (; a < 4; a += 2) "margin" === n && (u += S.css(e, n + ne[a], !0, i)), r ? ("content" === n && (u -= S.css(e, "padding" + ne[a], !0, i)), "margin" !== n && (u -= S.css(e, "border" + ne[a] + "Width", !0, i))) : (u += S.css(e, "padding" + ne[a], !0, i), "padding" !== n ? u += S.css(e, "border" + ne[a] + "Width", !0, i) : s += S.css(e, "border" + ne[a] + "Width", !0, i));
        return !r && 0 <= o && (u += Math.max(0, Math.ceil(e["offset" + t[0].toUpperCase() + t.slice(1)] - o - u - s - .5)) || 0), u
    }

    function Ze(e, t, n) {
        var r = Ie(e),
            i = (!y.boxSizingReliable() || n) && "border-box" === S.css(e, "boxSizing", !1, r),
            o = i,
            a = Be(e, t, r),
            s = "offset" + t[0].toUpperCase() + t.slice(1);
        if (Me.test(a)) {
            if (!n) return a;
            a = "auto"
        }
        return (!y.boxSizingReliable() && i || !y.reliableTrDimensions() && A(e, "tr") || "auto" === a || !parseFloat(a) && "inline" === S.css(e, "display", !1, r)) && e.getClientRects().length && (i = "border-box" === S.css(e, "boxSizing", !1, r), (o = s in e) && (a = e[s])), (a = parseFloat(a) || 0) + Ke(e, t, n || (i ? "border" : "content"), o, r, a) + "px"
    }

    function et(e, t, n, r, i) {
        return new et.prototype.init(e, t, n, r, i)
    }
    S.extend({
        cssHooks: {
            opacity: {
                get: function (e, t) {
                    if (t) {
                        var n = Be(e, "opacity");
                        return "" === n ? "1" : n
                    }
                }
            }
        },
        cssNumber: {
            animationIterationCount: !0,
            columnCount: !0,
            fillOpacity: !0,
            flexGrow: !0,
            flexShrink: !0,
            fontWeight: !0,
            gridArea: !0,
            gridColumn: !0,
            gridColumnEnd: !0,
            gridColumnStart: !0,
            gridRow: !0,
            gridRowEnd: !0,
            gridRowStart: !0,
            lineHeight: !0,
            opacity: !0,
            order: !0,
            orphans: !0,
            widows: !0,
            zIndex: !0,
            zoom: !0
        },
        cssProps: {},
        style: function (e, t, n, r) {
            if (e && 3 !== e.nodeType && 8 !== e.nodeType && e.style) {
                var i, o, a, s = X(t),
                    u = Ge.test(t),
                    l = e.style;
                if (u || (t = Xe(s)), a = S.cssHooks[t] || S.cssHooks[s], void 0 === n) return a && "get" in a && void 0 !== (i = a.get(e, !1, r)) ? i : l[t];
                "string" === (o = typeof n) && (i = te.exec(n)) && i[1] && (n = se(e, t, i), o = "number"), null != n && n == n && ("number" !== o || u || (n += i && i[3] || (S.cssNumber[s] ? "" : "px")), y.clearCloneStyle || "" !== n || 0 !== t.indexOf("background") || (l[t] = "inherit"), a && "set" in a && void 0 === (n = a.set(e, n, r)) || (u ? l.setProperty(t, n) : l[t] = n))
            }
        },
        css: function (e, t, n, r) {
            var i, o, a, s = X(t);
            return Ge.test(t) || (t = Xe(s)), (a = S.cssHooks[t] || S.cssHooks[s]) && "get" in a && (i = a.get(e, !0, n)), void 0 === i && (i = Be(e, t, r)), "normal" === i && t in Qe && (i = Qe[t]), "" === n || n ? (o = parseFloat(i), !0 === n || isFinite(o) ? o || 0 : i) : i
        }
    }), S.each(["height", "width"], function (e, u) {
        S.cssHooks[u] = {
            get: function (e, t, n) {
                if (t) return !Ve.test(S.css(e, "display")) || e.getClientRects().length && e.getBoundingClientRect().width ? Ze(e, u, n) : We(e, Ye, function () {
                    return Ze(e, u, n)
                })
            },
            set: function (e, t, n) {
                var r, i = Ie(e),
                    o = !y.scrollboxSize() && "absolute" === i.position,
                    a = (o || n) && "border-box" === S.css(e, "boxSizing", !1, i),
                    s = n ? Ke(e, u, n, a, i) : 0;
                return a && o && (s -= Math.ceil(e["offset" + u[0].toUpperCase() + u.slice(1)] - parseFloat(i[u]) - Ke(e, u, "border", !1, i) - .5)), s && (r = te.exec(t)) && "px" !== (r[3] || "px") && (e.style[u] = t, t = S.css(e, u)), Je(0, t, s)
            }
        }
    }), S.cssHooks.marginLeft = $e(y.reliableMarginLeft, function (e, t) {
        if (t) return (parseFloat(Be(e, "marginLeft")) || e.getBoundingClientRect().left - We(e, {
            marginLeft: 0
        }, function () {
            return e.getBoundingClientRect().left
        })) + "px"
    }), S.each({
        margin: "",
        padding: "",
        border: "Width"
    }, function (i, o) {
        S.cssHooks[i + o] = {
            expand: function (e) {
                for (var t = 0, n = {}, r = "string" == typeof e ? e.split(" ") : [e]; t < 4; t++) n[i + ne[t] + o] = r[t] || r[t - 2] || r[0];
                return n
            }
        }, "margin" !== i && (S.cssHooks[i + o].set = Je)
    }), S.fn.extend({
        css: function (e, t) {
            return $(this, function (e, t, n) {
                var r, i, o = {},
                    a = 0;
                if (Array.isArray(t)) {
                    for (r = Ie(e), i = t.length; a < i; a++) o[t[a]] = S.css(e, t[a], !1, r);
                    return o
                }
                return void 0 !== n ? S.style(e, t, n) : S.css(e, t)
            }, e, t, 1 < arguments.length)
        }
    }), ((S.Tween = et).prototype = {
        constructor: et,
        init: function (e, t, n, r, i, o) {
            this.elem = e, this.prop = n, this.easing = i || S.easing._default, this.options = t, this.start = this.now = this.cur(), this.end = r, this.unit = o || (S.cssNumber[n] ? "" : "px")
        },
        cur: function () {
            var e = et.propHooks[this.prop];
            return e && e.get ? e.get(this) : et.propHooks._default.get(this)
        },
        run: function (e) {
            var t, n = et.propHooks[this.prop];
            return this.options.duration ? this.pos = t = S.easing[this.easing](e, this.options.duration * e, 0, 1, this.options.duration) : this.pos = t = e, this.now = (this.end - this.start) * t + this.start, this.options.step && this.options.step.call(this.elem, this.now, this), n && n.set ? n.set(this) : et.propHooks._default.set(this), this
        }
    }).init.prototype = et.prototype, (et.propHooks = {
        _default: {
            get: function (e) {
                var t;
                return 1 !== e.elem.nodeType || null != e.elem[e.prop] && null == e.elem.style[e.prop] ? e.elem[e.prop] : (t = S.css(e.elem, e.prop, "")) && "auto" !== t ? t : 0
            },
            set: function (e) {
                S.fx.step[e.prop] ? S.fx.step[e.prop](e) : 1 !== e.elem.nodeType || !S.cssHooks[e.prop] && null == e.elem.style[Xe(e.prop)] ? e.elem[e.prop] = e.now : S.style(e.elem, e.prop, e.now + e.unit)
            }
        }
    }).scrollTop = et.propHooks.scrollLeft = {
        set: function (e) {
            e.elem.nodeType && e.elem.parentNode && (e.elem[e.prop] = e.now)
        }
    }, S.easing = {
        linear: function (e) {
            return e
        },
        swing: function (e) {
            return .5 - Math.cos(e * Math.PI) / 2
        },
        _default: "swing"
    }, S.fx = et.prototype.init, S.fx.step = {};
    var tt, nt, rt, it, ot = /^(?:toggle|show|hide)$/,
        at = /queueHooks$/;

    function st() {
        nt && (!1 === E.hidden && C.requestAnimationFrame ? C.requestAnimationFrame(st) : C.setTimeout(st, S.fx.interval), S.fx.tick())
    }

    function ut() {
        return C.setTimeout(function () {
            tt = void 0
        }), tt = Date.now()
    }

    function lt(e, t) {
        var n, r = 0,
            i = {
                height: e
            };
        for (t = t ? 1 : 0; r < 4; r += 2 - t) i["margin" + (n = ne[r])] = i["padding" + n] = e;
        return t && (i.opacity = i.width = e), i
    }

    function ct(e, t, n) {
        for (var r, i = (ft.tweeners[t] || []).concat(ft.tweeners["*"]), o = 0, a = i.length; o < a; o++)
            if (r = i[o].call(n, t, e)) return r
    }

    function ft(o, e, t) {
        var n, a, r = 0,
            i = ft.prefilters.length,
            s = S.Deferred().always(function () {
                delete u.elem
            }),
            u = function () {
                if (a) return !1;
                for (var e = tt || ut(), t = Math.max(0, l.startTime + l.duration - e), n = 1 - (t / l.duration || 0), r = 0, i = l.tweens.length; r < i; r++) l.tweens[r].run(n);
                return s.notifyWith(o, [l, n, t]), n < 1 && i ? t : (i || s.notifyWith(o, [l, 1, 0]), s.resolveWith(o, [l]), !1)
            },
            l = s.promise({
                elem: o,
                props: S.extend({}, e),
                opts: S.extend(!0, {
                    specialEasing: {},
                    easing: S.easing._default
                }, t),
                originalProperties: e,
                originalOptions: t,
                startTime: tt || ut(),
                duration: t.duration,
                tweens: [],
                createTween: function (e, t) {
                    var n = S.Tween(o, l.opts, e, t, l.opts.specialEasing[e] || l.opts.easing);
                    return l.tweens.push(n), n
                },
                stop: function (e) {
                    var t = 0,
                        n = e ? l.tweens.length : 0;
                    if (a) return this;
                    for (a = !0; t < n; t++) l.tweens[t].run(1);
                    return e ? (s.notifyWith(o, [l, 1, 0]), s.resolveWith(o, [l, e])) : s.rejectWith(o, [l, e]), this
                }
            }),
            c = l.props;
        for (! function (e, t) {
                var n, r, i, o, a;
                for (n in e)
                    if (i = t[r = X(n)], o = e[n], Array.isArray(o) && (i = o[1], o = e[n] = o[0]), n !== r && (e[r] = o, delete e[n]), (a = S.cssHooks[r]) && "expand" in a)
                        for (n in o = a.expand(o), delete e[r], o) n in e || (e[n] = o[n], t[n] = i);
                    else t[r] = i
            }(c, l.opts.specialEasing); r < i; r++)
            if (n = ft.prefilters[r].call(l, o, c, l.opts)) return m(n.stop) && (S._queueHooks(l.elem, l.opts.queue).stop = n.stop.bind(n)), n;
        return S.map(c, ct, l), m(l.opts.start) && l.opts.start.call(o, l), l.progress(l.opts.progress).done(l.opts.done, l.opts.complete).fail(l.opts.fail).always(l.opts.always), S.fx.timer(S.extend(u, {
            elem: o,
            anim: l,
            queue: l.opts.queue
        })), l
    }
    S.Animation = S.extend(ft, {
        tweeners: {
            "*": [function (e, t) {
                var n = this.createTween(e, t);
                return se(n.elem, e, te.exec(t), n), n
            }]
        },
        tweener: function (e, t) {
            m(e) ? (t = e, e = ["*"]) : e = e.match(P);
            for (var n, r = 0, i = e.length; r < i; r++) n = e[r], ft.tweeners[n] = ft.tweeners[n] || [], ft.tweeners[n].unshift(t)
        },
        prefilters: [function (e, t, n) {
            var r, i, o, a, s, u, l, c, f = "width" in t || "height" in t,
                p = this,
                d = {},
                h = e.style,
                g = e.nodeType && ae(e),
                v = Y.get(e, "fxshow");
            for (r in n.queue || (null == (a = S._queueHooks(e, "fx")).unqueued && (a.unqueued = 0, s = a.empty.fire, a.empty.fire = function () {
                    a.unqueued || s()
                }), a.unqueued++, p.always(function () {
                    p.always(function () {
                        a.unqueued--, S.queue(e, "fx").length || a.empty.fire()
                    })
                })), t)
                if (i = t[r], ot.test(i)) {
                    if (delete t[r], o = o || "toggle" === i, i === (g ? "hide" : "show")) {
                        if ("show" !== i || !v || void 0 === v[r]) continue;
                        g = !0
                    }
                    d[r] = v && v[r] || S.style(e, r)
                } if ((u = !S.isEmptyObject(t)) || !S.isEmptyObject(d))
                for (r in f && 1 === e.nodeType && (n.overflow = [h.overflow, h.overflowX, h.overflowY], null == (l = v && v.display) && (l = Y.get(e, "display")), "none" === (c = S.css(e, "display")) && (l ? c = l : (le([e], !0), l = e.style.display || l, c = S.css(e, "display"), le([e]))), ("inline" === c || "inline-block" === c && null != l) && "none" === S.css(e, "float") && (u || (p.done(function () {
                        h.display = l
                    }), null == l && (c = h.display, l = "none" === c ? "" : c)), h.display = "inline-block")), n.overflow && (h.overflow = "hidden", p.always(function () {
                        h.overflow = n.overflow[0], h.overflowX = n.overflow[1], h.overflowY = n.overflow[2]
                    })), u = !1, d) u || (v ? "hidden" in v && (g = v.hidden) : v = Y.access(e, "fxshow", {
                    display: l
                }), o && (v.hidden = !g), g && le([e], !0), p.done(function () {
                    for (r in g || le([e]), Y.remove(e, "fxshow"), d) S.style(e, r, d[r])
                })), u = ct(g ? v[r] : 0, r, p), r in v || (v[r] = u.start, g && (u.end = u.start, u.start = 0))
        }],
        prefilter: function (e, t) {
            t ? ft.prefilters.unshift(e) : ft.prefilters.push(e)
        }
    }), S.speed = function (e, t, n) {
        var r = e && "object" == typeof e ? S.extend({}, e) : {
            complete: n || !n && t || m(e) && e,
            duration: e,
            easing: n && t || t && !m(t) && t
        };
        return S.fx.off ? r.duration = 0 : "number" != typeof r.duration && (r.duration in S.fx.speeds ? r.duration = S.fx.speeds[r.duration] : r.duration = S.fx.speeds._default), null != r.queue && !0 !== r.queue || (r.queue = "fx"), r.old = r.complete, r.complete = function () {
            m(r.old) && r.old.call(this), r.queue && S.dequeue(this, r.queue)
        }, r
    }, S.fn.extend({
        fadeTo: function (e, t, n, r) {
            return this.filter(ae).css("opacity", 0).show().end().animate({
                opacity: t
            }, e, n, r)
        },
        animate: function (t, e, n, r) {
            var i = S.isEmptyObject(t),
                o = S.speed(e, n, r),
                a = function () {
                    var e = ft(this, S.extend({}, t), o);
                    (i || Y.get(this, "finish")) && e.stop(!0)
                };
            return a.finish = a, i || !1 === o.queue ? this.each(a) : this.queue(o.queue, a)
        },
        stop: function (i, e, o) {
            var a = function (e) {
                var t = e.stop;
                delete e.stop, t(o)
            };
            return "string" != typeof i && (o = e, e = i, i = void 0), e && this.queue(i || "fx", []), this.each(function () {
                var e = !0,
                    t = null != i && i + "queueHooks",
                    n = S.timers,
                    r = Y.get(this);
                if (t) r[t] && r[t].stop && a(r[t]);
                else
                    for (t in r) r[t] && r[t].stop && at.test(t) && a(r[t]);
                for (t = n.length; t--;) n[t].elem !== this || null != i && n[t].queue !== i || (n[t].anim.stop(o), e = !1, n.splice(t, 1));
                !e && o || S.dequeue(this, i)
            })
        },
        finish: function (a) {
            return !1 !== a && (a = a || "fx"), this.each(function () {
                var e, t = Y.get(this),
                    n = t[a + "queue"],
                    r = t[a + "queueHooks"],
                    i = S.timers,
                    o = n ? n.length : 0;
                for (t.finish = !0, S.queue(this, a, []), r && r.stop && r.stop.call(this, !0), e = i.length; e--;) i[e].elem === this && i[e].queue === a && (i[e].anim.stop(!0), i.splice(e, 1));
                for (e = 0; e < o; e++) n[e] && n[e].finish && n[e].finish.call(this);
                delete t.finish
            })
        }
    }), S.each(["toggle", "show", "hide"], function (e, r) {
        var i = S.fn[r];
        S.fn[r] = function (e, t, n) {
            return null == e || "boolean" == typeof e ? i.apply(this, arguments) : this.animate(lt(r, !0), e, t, n)
        }
    }), S.each({
        slideDown: lt("show"),
        slideUp: lt("hide"),
        slideToggle: lt("toggle"),
        fadeIn: {
            opacity: "show"
        },
        fadeOut: {
            opacity: "hide"
        },
        fadeToggle: {
            opacity: "toggle"
        }
    }, function (e, r) {
        S.fn[e] = function (e, t, n) {
            return this.animate(r, e, t, n)
        }
    }), S.timers = [], S.fx.tick = function () {
        var e, t = 0,
            n = S.timers;
        for (tt = Date.now(); t < n.length; t++)(e = n[t])() || n[t] !== e || n.splice(t--, 1);
        n.length || S.fx.stop(), tt = void 0
    }, S.fx.timer = function (e) {
        S.timers.push(e), S.fx.start()
    }, S.fx.interval = 13, S.fx.start = function () {
        nt || (nt = !0, st())
    }, S.fx.stop = function () {
        nt = null
    }, S.fx.speeds = {
        slow: 600,
        fast: 200,
        _default: 400
    }, S.fn.delay = function (r, e) {
        return r = S.fx && S.fx.speeds[r] || r, e = e || "fx", this.queue(e, function (e, t) {
            var n = C.setTimeout(e, r);
            t.stop = function () {
                C.clearTimeout(n)
            }
        })
    }, rt = E.createElement("input"), it = E.createElement("select").appendChild(E.createElement("option")), rt.type = "checkbox", y.checkOn = "" !== rt.value, y.optSelected = it.selected, (rt = E.createElement("input")).value = "t", rt.type = "radio", y.radioValue = "t" === rt.value;
    var pt, dt = S.expr.attrHandle;
    S.fn.extend({
        attr: function (e, t) {
            return $(this, S.attr, e, t, 1 < arguments.length)
        },
        removeAttr: function (e) {
            return this.each(function () {
                S.removeAttr(this, e)
            })
        }
    }), S.extend({
        attr: function (e, t, n) {
            var r, i, o = e.nodeType;
            if (3 !== o && 8 !== o && 2 !== o) return "undefined" == typeof e.getAttribute ? S.prop(e, t, n) : (1 === o && S.isXMLDoc(e) || (i = S.attrHooks[t.toLowerCase()] || (S.expr.match.bool.test(t) ? pt : void 0)), void 0 !== n ? null === n ? void S.removeAttr(e, t) : i && "set" in i && void 0 !== (r = i.set(e, n, t)) ? r : (e.setAttribute(t, n + ""), n) : i && "get" in i && null !== (r = i.get(e, t)) ? r : null == (r = S.find.attr(e, t)) ? void 0 : r)
        },
        attrHooks: {
            type: {
                set: function (e, t) {
                    if (!y.radioValue && "radio" === t && A(e, "input")) {
                        var n = e.value;
                        return e.setAttribute("type", t), n && (e.value = n), t
                    }
                }
            }
        },
        removeAttr: function (e, t) {
            var n, r = 0,
                i = t && t.match(P);
            if (i && 1 === e.nodeType)
                while (n = i[r++]) e.removeAttribute(n)
        }
    }), pt = {
        set: function (e, t, n) {
            return !1 === t ? S.removeAttr(e, n) : e.setAttribute(n, n), n
        }
    }, S.each(S.expr.match.bool.source.match(/\w+/g), function (e, t) {
        var a = dt[t] || S.find.attr;
        dt[t] = function (e, t, n) {
            var r, i, o = t.toLowerCase();
            return n || (i = dt[o], dt[o] = r, r = null != a(e, t, n) ? o : null, dt[o] = i), r
        }
    });
    var ht = /^(?:input|select|textarea|button)$/i,
        gt = /^(?:a|area)$/i;

    function vt(e) {
        return (e.match(P) || []).join(" ")
    }

    function yt(e) {
        return e.getAttribute && e.getAttribute("class") || ""
    }

    function mt(e) {
        return Array.isArray(e) ? e : "string" == typeof e && e.match(P) || []
    }
    S.fn.extend({
        prop: function (e, t) {
            return $(this, S.prop, e, t, 1 < arguments.length)
        },
        removeProp: function (e) {
            return this.each(function () {
                delete this[S.propFix[e] || e]
            })
        }
    }), S.extend({
        prop: function (e, t, n) {
            var r, i, o = e.nodeType;
            if (3 !== o && 8 !== o && 2 !== o) return 1 === o && S.isXMLDoc(e) || (t = S.propFix[t] || t, i = S.propHooks[t]), void 0 !== n ? i && "set" in i && void 0 !== (r = i.set(e, n, t)) ? r : e[t] = n : i && "get" in i && null !== (r = i.get(e, t)) ? r : e[t]
        },
        propHooks: {
            tabIndex: {
                get: function (e) {
                    var t = S.find.attr(e, "tabindex");
                    return t ? parseInt(t, 10) : ht.test(e.nodeName) || gt.test(e.nodeName) && e.href ? 0 : -1
                }
            }
        },
        propFix: {
            "for": "htmlFor",
            "class": "className"
        }
    }), y.optSelected || (S.propHooks.selected = {
        get: function (e) {
            var t = e.parentNode;
            return t && t.parentNode && t.parentNode.selectedIndex, null
        },
        set: function (e) {
            var t = e.parentNode;
            t && (t.selectedIndex, t.parentNode && t.parentNode.selectedIndex)
        }
    }), S.each(["tabIndex", "readOnly", "maxLength", "cellSpacing", "cellPadding", "rowSpan", "colSpan", "useMap", "frameBorder", "contentEditable"], function () {
        S.propFix[this.toLowerCase()] = this
    }), S.fn.extend({
        addClass: function (t) {
            var e, n, r, i, o, a, s, u = 0;
            if (m(t)) return this.each(function (e) {
                S(this).addClass(t.call(this, e, yt(this)))
            });
            if ((e = mt(t)).length)
                while (n = this[u++])
                    if (i = yt(n), r = 1 === n.nodeType && " " + vt(i) + " ") {
                        a = 0;
                        while (o = e[a++]) r.indexOf(" " + o + " ") < 0 && (r += o + " ");
                        i !== (s = vt(r)) && n.setAttribute("class", s)
                    } return this
        },
        removeClass: function (t) {
            var e, n, r, i, o, a, s, u = 0;
            if (m(t)) return this.each(function (e) {
                S(this).removeClass(t.call(this, e, yt(this)))
            });
            if (!arguments.length) return this.attr("class", "");
            if ((e = mt(t)).length)
                while (n = this[u++])
                    if (i = yt(n), r = 1 === n.nodeType && " " + vt(i) + " ") {
                        a = 0;
                        while (o = e[a++])
                            while (-1 < r.indexOf(" " + o + " ")) r = r.replace(" " + o + " ", " ");
                        i !== (s = vt(r)) && n.setAttribute("class", s)
                    } return this
        },
        toggleClass: function (i, t) {
            var o = typeof i,
                a = "string" === o || Array.isArray(i);
            return "boolean" == typeof t && a ? t ? this.addClass(i) : this.removeClass(i) : m(i) ? this.each(function (e) {
                S(this).toggleClass(i.call(this, e, yt(this), t), t)
            }) : this.each(function () {
                var e, t, n, r;
                if (a) {
                    t = 0, n = S(this), r = mt(i);
                    while (e = r[t++]) n.hasClass(e) ? n.removeClass(e) : n.addClass(e)
                } else void 0 !== i && "boolean" !== o || ((e = yt(this)) && Y.set(this, "__className__", e), this.setAttribute && this.setAttribute("class", e || !1 === i ? "" : Y.get(this, "__className__") || ""))
            })
        },
        hasClass: function (e) {
            var t, n, r = 0;
            t = " " + e + " ";
            while (n = this[r++])
                if (1 === n.nodeType && -1 < (" " + vt(yt(n)) + " ").indexOf(t)) return !0;
            return !1
        }
    });
    var xt = /\r/g;
    S.fn.extend({
        val: function (n) {
            var r, e, i, t = this[0];
            return arguments.length ? (i = m(n), this.each(function (e) {
                var t;
                1 === this.nodeType && (null == (t = i ? n.call(this, e, S(this).val()) : n) ? t = "" : "number" == typeof t ? t += "" : Array.isArray(t) && (t = S.map(t, function (e) {
                    return null == e ? "" : e + ""
                })), (r = S.valHooks[this.type] || S.valHooks[this.nodeName.toLowerCase()]) && "set" in r && void 0 !== r.set(this, t, "value") || (this.value = t))
            })) : t ? (r = S.valHooks[t.type] || S.valHooks[t.nodeName.toLowerCase()]) && "get" in r && void 0 !== (e = r.get(t, "value")) ? e : "string" == typeof (e = t.value) ? e.replace(xt, "") : null == e ? "" : e : void 0
        }
    }), S.extend({
        valHooks: {
            option: {
                get: function (e) {
                    var t = S.find.attr(e, "value");
                    return null != t ? t : vt(S.text(e))
                }
            },
            select: {
                get: function (e) {
                    var t, n, r, i = e.options,
                        o = e.selectedIndex,
                        a = "select-one" === e.type,
                        s = a ? null : [],
                        u = a ? o + 1 : i.length;
                    for (r = o < 0 ? u : a ? o : 0; r < u; r++)
                        if (((n = i[r]).selected || r === o) && !n.disabled && (!n.parentNode.disabled || !A(n.parentNode, "optgroup"))) {
                            if (t = S(n).val(), a) return t;
                            s.push(t)
                        } return s
                },
                set: function (e, t) {
                    var n, r, i = e.options,
                        o = S.makeArray(t),
                        a = i.length;
                    while (a--)((r = i[a]).selected = -1 < S.inArray(S.valHooks.option.get(r), o)) && (n = !0);
                    return n || (e.selectedIndex = -1), o
                }
            }
        }
    }), S.each(["radio", "checkbox"], function () {
        S.valHooks[this] = {
            set: function (e, t) {
                if (Array.isArray(t)) return e.checked = -1 < S.inArray(S(e).val(), t)
            }
        }, y.checkOn || (S.valHooks[this].get = function (e) {
            return null === e.getAttribute("value") ? "on" : e.value
        })
    }), y.focusin = "onfocusin" in C;
    var bt = /^(?:focusinfocus|focusoutblur)$/,
        wt = function (e) {
            e.stopPropagation()
        };
    S.extend(S.event, {
        trigger: function (e, t, n, r) {
            var i, o, a, s, u, l, c, f, p = [n || E],
                d = v.call(e, "type") ? e.type : e,
                h = v.call(e, "namespace") ? e.namespace.split(".") : [];
            if (o = f = a = n = n || E, 3 !== n.nodeType && 8 !== n.nodeType && !bt.test(d + S.event.triggered) && (-1 < d.indexOf(".") && (d = (h = d.split(".")).shift(), h.sort()), u = d.indexOf(":") < 0 && "on" + d, (e = e[S.expando] ? e : new S.Event(d, "object" == typeof e && e)).isTrigger = r ? 2 : 3, e.namespace = h.join("."), e.rnamespace = e.namespace ? new RegExp("(^|\\.)" + h.join("\\.(?:.*\\.|)") + "(\\.|$)") : null, e.result = void 0, e.target || (e.target = n), t = null == t ? [e] : S.makeArray(t, [e]), c = S.event.special[d] || {}, r || !c.trigger || !1 !== c.trigger.apply(n, t))) {
                if (!r && !c.noBubble && !x(n)) {
                    for (s = c.delegateType || d, bt.test(s + d) || (o = o.parentNode); o; o = o.parentNode) p.push(o), a = o;
                    a === (n.ownerDocument || E) && p.push(a.defaultView || a.parentWindow || C)
                }
                i = 0;
                while ((o = p[i++]) && !e.isPropagationStopped()) f = o, e.type = 1 < i ? s : c.bindType || d, (l = (Y.get(o, "events") || Object.create(null))[e.type] && Y.get(o, "handle")) && l.apply(o, t), (l = u && o[u]) && l.apply && V(o) && (e.result = l.apply(o, t), !1 === e.result && e.preventDefault());
                return e.type = d, r || e.isDefaultPrevented() || c._default && !1 !== c._default.apply(p.pop(), t) || !V(n) || u && m(n[d]) && !x(n) && ((a = n[u]) && (n[u] = null), S.event.triggered = d, e.isPropagationStopped() && f.addEventListener(d, wt), n[d](), e.isPropagationStopped() && f.removeEventListener(d, wt), S.event.triggered = void 0, a && (n[u] = a)), e.result
            }
        },
        simulate: function (e, t, n) {
            var r = S.extend(new S.Event, n, {
                type: e,
                isSimulated: !0
            });
            S.event.trigger(r, null, t)
        }
    }), S.fn.extend({
        trigger: function (e, t) {
            return this.each(function () {
                S.event.trigger(e, t, this)
            })
        },
        triggerHandler: function (e, t) {
            var n = this[0];
            if (n) return S.event.trigger(e, t, n, !0)
        }
    }), y.focusin || S.each({
        focus: "focusin",
        blur: "focusout"
    }, function (n, r) {
        var i = function (e) {
            S.event.simulate(r, e.target, S.event.fix(e))
        };
        S.event.special[r] = {
            setup: function () {
                var e = this.ownerDocument || this.document || this,
                    t = Y.access(e, r);
                t || e.addEventListener(n, i, !0), Y.access(e, r, (t || 0) + 1)
            },
            teardown: function () {
                var e = this.ownerDocument || this.document || this,
                    t = Y.access(e, r) - 1;
                t ? Y.access(e, r, t) : (e.removeEventListener(n, i, !0), Y.remove(e, r))
            }
        }
    });
    var Tt = C.location,
        Ct = {
            guid: Date.now()
        },
        Et = /\?/;
    S.parseXML = function (e) {
        var t;
        if (!e || "string" != typeof e) return null;
        try {
            t = (new C.DOMParser).parseFromString(e, "text/xml")
        } catch (e) {
            t = void 0
        }
        return t && !t.getElementsByTagName("parsererror").length || S.error("Invalid XML: " + e), t
    };
    var St = /\[\]$/,
        kt = /\r?\n/g,
        At = /^(?:submit|button|image|reset|file)$/i,
        Nt = /^(?:input|select|textarea|keygen)/i;

    function Dt(n, e, r, i) {
        var t;
        if (Array.isArray(e)) S.each(e, function (e, t) {
            r || St.test(n) ? i(n, t) : Dt(n + "[" + ("object" == typeof t && null != t ? e : "") + "]", t, r, i)
        });
        else if (r || "object" !== w(e)) i(n, e);
        else
            for (t in e) Dt(n + "[" + t + "]", e[t], r, i)
    }
    S.param = function (e, t) {
        var n, r = [],
            i = function (e, t) {
                var n = m(t) ? t() : t;
                r[r.length] = encodeURIComponent(e) + "=" + encodeURIComponent(null == n ? "" : n)
            };
        if (null == e) return "";
        if (Array.isArray(e) || e.jquery && !S.isPlainObject(e)) S.each(e, function () {
            i(this.name, this.value)
        });
        else
            for (n in e) Dt(n, e[n], t, i);
        return r.join("&")
    }, S.fn.extend({
        serialize: function () {
            return S.param(this.serializeArray())
        },
        serializeArray: function () {
            return this.map(function () {
                var e = S.prop(this, "elements");
                return e ? S.makeArray(e) : this
            }).filter(function () {
                var e = this.type;
                return this.name && !S(this).is(":disabled") && Nt.test(this.nodeName) && !At.test(e) && (this.checked || !pe.test(e))
            }).map(function (e, t) {
                var n = S(this).val();
                return null == n ? null : Array.isArray(n) ? S.map(n, function (e) {
                    return {
                        name: t.name,
                        value: e.replace(kt, "\r\n")
                    }
                }) : {
                    name: t.name,
                    value: n.replace(kt, "\r\n")
                }
            }).get()
        }
    });
    var jt = /%20/g,
        qt = /#.*$/,
        Lt = /([?&])_=[^&]*/,
        Ht = /^(.*?):[ \t]*([^\r\n]*)$/gm,
        Ot = /^(?:GET|HEAD)$/,
        Pt = /^\/\//,
        Rt = {},
        Mt = {},
        It = "*/".concat("*"),
        Wt = E.createElement("a");

    function Ft(o) {
        return function (e, t) {
            "string" != typeof e && (t = e, e = "*");
            var n, r = 0,
                i = e.toLowerCase().match(P) || [];
            if (m(t))
                while (n = i[r++]) "+" === n[0] ? (n = n.slice(1) || "*", (o[n] = o[n] || []).unshift(t)) : (o[n] = o[n] || []).push(t)
        }
    }

    function Bt(t, i, o, a) {
        var s = {},
            u = t === Mt;

        function l(e) {
            var r;
            return s[e] = !0, S.each(t[e] || [], function (e, t) {
                var n = t(i, o, a);
                return "string" != typeof n || u || s[n] ? u ? !(r = n) : void 0 : (i.dataTypes.unshift(n), l(n), !1)
            }), r
        }
        return l(i.dataTypes[0]) || !s["*"] && l("*")
    }

    function $t(e, t) {
        var n, r, i = S.ajaxSettings.flatOptions || {};
        for (n in t) void 0 !== t[n] && ((i[n] ? e : r || (r = {}))[n] = t[n]);
        return r && S.extend(!0, e, r), e
    }
    Wt.href = Tt.href, S.extend({
        active: 0,
        lastModified: {},
        etag: {},
        ajaxSettings: {
            url: Tt.href,
            type: "GET",
            isLocal: /^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Tt.protocol),
            global: !0,
            processData: !0,
            async: !0,
            contentType: "application/x-www-form-urlencoded; charset=UTF-8",
            accepts: {
                "*": It,
                text: "text/plain",
                html: "text/html",
                xml: "application/xml, text/xml",
                json: "application/json, text/javascript"
            },
            contents: {
                xml: /\bxml\b/,
                html: /\bhtml/,
                json: /\bjson\b/
            },
            responseFields: {
                xml: "responseXML",
                text: "responseText",
                json: "responseJSON"
            },
            converters: {
                "* text": String,
                "text html": !0,
                "text json": JSON.parse,
                "text xml": S.parseXML
            },
            flatOptions: {
                url: !0,
                context: !0
            }
        },
        ajaxSetup: function (e, t) {
            return t ? $t($t(e, S.ajaxSettings), t) : $t(S.ajaxSettings, e)
        },
        ajaxPrefilter: Ft(Rt),
        ajaxTransport: Ft(Mt),
        ajax: function (e, t) {
            "object" == typeof e && (t = e, e = void 0), t = t || {};
            var c, f, p, n, d, r, h, g, i, o, v = S.ajaxSetup({}, t),
                y = v.context || v,
                m = v.context && (y.nodeType || y.jquery) ? S(y) : S.event,
                x = S.Deferred(),
                b = S.Callbacks("once memory"),
                w = v.statusCode || {},
                a = {},
                s = {},
                u = "canceled",
                T = {
                    readyState: 0,
                    getResponseHeader: function (e) {
                        var t;
                        if (h) {
                            if (!n) {
                                n = {};
                                while (t = Ht.exec(p)) n[t[1].toLowerCase() + " "] = (n[t[1].toLowerCase() + " "] || []).concat(t[2])
                            }
                            t = n[e.toLowerCase() + " "]
                        }
                        return null == t ? null : t.join(", ")
                    },
                    getAllResponseHeaders: function () {
                        return h ? p : null
                    },
                    setRequestHeader: function (e, t) {
                        return null == h && (e = s[e.toLowerCase()] = s[e.toLowerCase()] || e, a[e] = t), this
                    },
                    overrideMimeType: function (e) {
                        return null == h && (v.mimeType = e), this
                    },
                    statusCode: function (e) {
                        var t;
                        if (e)
                            if (h) T.always(e[T.status]);
                            else
                                for (t in e) w[t] = [w[t], e[t]];
                        return this
                    },
                    abort: function (e) {
                        var t = e || u;
                        return c && c.abort(t), l(0, t), this
                    }
                };
            if (x.promise(T), v.url = ((e || v.url || Tt.href) + "").replace(Pt, Tt.protocol + "//"), v.type = t.method || t.type || v.method || v.type, v.dataTypes = (v.dataType || "*").toLowerCase().match(P) || [""], null == v.crossDomain) {
                r = E.createElement("a");
                try {
                    r.href = v.url, r.href = r.href, v.crossDomain = Wt.protocol + "//" + Wt.host != r.protocol + "//" + r.host
                } catch (e) {
                    v.crossDomain = !0
                }
            }
            if (v.data && v.processData && "string" != typeof v.data && (v.data = S.param(v.data, v.traditional)), Bt(Rt, v, t, T), h) return T;
            for (i in (g = S.event && v.global) && 0 == S.active++ && S.event.trigger("ajaxStart"), v.type = v.type.toUpperCase(), v.hasContent = !Ot.test(v.type), f = v.url.replace(qt, ""), v.hasContent ? v.data && v.processData && 0 === (v.contentType || "").indexOf("application/x-www-form-urlencoded") && (v.data = v.data.replace(jt, "+")) : (o = v.url.slice(f.length), v.data && (v.processData || "string" == typeof v.data) && (f += (Et.test(f) ? "&" : "?") + v.data, delete v.data), !1 === v.cache && (f = f.replace(Lt, "$1"), o = (Et.test(f) ? "&" : "?") + "_=" + Ct.guid++ + o), v.url = f + o), v.ifModified && (S.lastModified[f] && T.setRequestHeader("If-Modified-Since", S.lastModified[f]), S.etag[f] && T.setRequestHeader("If-None-Match", S.etag[f])), (v.data && v.hasContent && !1 !== v.contentType || t.contentType) && T.setRequestHeader("Content-Type", v.contentType), T.setRequestHeader("Accept", v.dataTypes[0] && v.accepts[v.dataTypes[0]] ? v.accepts[v.dataTypes[0]] + ("*" !== v.dataTypes[0] ? ", " + It + "; q=0.01" : "") : v.accepts["*"]), v.headers) T.setRequestHeader(i, v.headers[i]);
            if (v.beforeSend && (!1 === v.beforeSend.call(y, T, v) || h)) return T.abort();
            if (u = "abort", b.add(v.complete), T.done(v.success), T.fail(v.error), c = Bt(Mt, v, t, T)) {
                if (T.readyState = 1, g && m.trigger("ajaxSend", [T, v]), h) return T;
                v.async && 0 < v.timeout && (d = C.setTimeout(function () {
                    T.abort("timeout")
                }, v.timeout));
                try {
                    h = !1, c.send(a, l)
                } catch (e) {
                    if (h) throw e;
                    l(-1, e)
                }
            } else l(-1, "No Transport");

            function l(e, t, n, r) {
                var i, o, a, s, u, l = t;
                h || (h = !0, d && C.clearTimeout(d), c = void 0, p = r || "", T.readyState = 0 < e ? 4 : 0, i = 200 <= e && e < 300 || 304 === e, n && (s = function (e, t, n) {
                    var r, i, o, a, s = e.contents,
                        u = e.dataTypes;
                    while ("*" === u[0]) u.shift(), void 0 === r && (r = e.mimeType || t.getResponseHeader("Content-Type"));
                    if (r)
                        for (i in s)
                            if (s[i] && s[i].test(r)) {
                                u.unshift(i);
                                break
                            } if (u[0] in n) o = u[0];
                    else {
                        for (i in n) {
                            if (!u[0] || e.converters[i + " " + u[0]]) {
                                o = i;
                                break
                            }
                            a || (a = i)
                        }
                        o = o || a
                    }
                    if (o) return o !== u[0] && u.unshift(o), n[o]
                }(v, T, n)), !i && -1 < S.inArray("script", v.dataTypes) && (v.converters["text script"] = function () {}), s = function (e, t, n, r) {
                    var i, o, a, s, u, l = {},
                        c = e.dataTypes.slice();
                    if (c[1])
                        for (a in e.converters) l[a.toLowerCase()] = e.converters[a];
                    o = c.shift();
                    while (o)
                        if (e.responseFields[o] && (n[e.responseFields[o]] = t), !u && r && e.dataFilter && (t = e.dataFilter(t, e.dataType)), u = o, o = c.shift())
                            if ("*" === o) o = u;
                            else if ("*" !== u && u !== o) {
                        if (!(a = l[u + " " + o] || l["* " + o]))
                            for (i in l)
                                if ((s = i.split(" "))[1] === o && (a = l[u + " " + s[0]] || l["* " + s[0]])) {
                                    !0 === a ? a = l[i] : !0 !== l[i] && (o = s[0], c.unshift(s[1]));
                                    break
                                } if (!0 !== a)
                            if (a && e["throws"]) t = a(t);
                            else try {
                                t = a(t)
                            } catch (e) {
                                return {
                                    state: "parsererror",
                                    error: a ? e : "No conversion from " + u + " to " + o
                                }
                            }
                    }
                    return {
                        state: "success",
                        data: t
                    }
                }(v, s, T, i), i ? (v.ifModified && ((u = T.getResponseHeader("Last-Modified")) && (S.lastModified[f] = u), (u = T.getResponseHeader("etag")) && (S.etag[f] = u)), 204 === e || "HEAD" === v.type ? l = "nocontent" : 304 === e ? l = "notmodified" : (l = s.state, o = s.data, i = !(a = s.error))) : (a = l, !e && l || (l = "error", e < 0 && (e = 0))), T.status = e, T.statusText = (t || l) + "", i ? x.resolveWith(y, [o, l, T]) : x.rejectWith(y, [T, l, a]), T.statusCode(w), w = void 0, g && m.trigger(i ? "ajaxSuccess" : "ajaxError", [T, v, i ? o : a]), b.fireWith(y, [T, l]), g && (m.trigger("ajaxComplete", [T, v]), --S.active || S.event.trigger("ajaxStop")))
            }
            return T
        },
        getJSON: function (e, t, n) {
            return S.get(e, t, n, "json")
        },
        getScript: function (e, t) {
            return S.get(e, void 0, t, "script")
        }
    }), S.each(["get", "post"], function (e, i) {
        S[i] = function (e, t, n, r) {
            return m(t) && (r = r || n, n = t, t = void 0), S.ajax(S.extend({
                url: e,
                type: i,
                dataType: r,
                data: t,
                success: n
            }, S.isPlainObject(e) && e))
        }
    }), S.ajaxPrefilter(function (e) {
        var t;
        for (t in e.headers) "content-type" === t.toLowerCase() && (e.contentType = e.headers[t] || "")
    }), S._evalUrl = function (e, t, n) {
        return S.ajax({
            url: e,
            type: "GET",
            dataType: "script",
            cache: !0,
            async: !1,
            global: !1,
            converters: {
                "text script": function () {}
            },
            dataFilter: function (e) {
                S.globalEval(e, t, n)
            }
        })
    }, S.fn.extend({
        wrapAll: function (e) {
            var t;
            return this[0] && (m(e) && (e = e.call(this[0])), t = S(e, this[0].ownerDocument).eq(0).clone(!0), this[0].parentNode && t.insertBefore(this[0]), t.map(function () {
                var e = this;
                while (e.firstElementChild) e = e.firstElementChild;
                return e
            }).append(this)), this
        },
        wrapInner: function (n) {
            return m(n) ? this.each(function (e) {
                S(this).wrapInner(n.call(this, e))
            }) : this.each(function () {
                var e = S(this),
                    t = e.contents();
                t.length ? t.wrapAll(n) : e.append(n)
            })
        },
        wrap: function (t) {
            var n = m(t);
            return this.each(function (e) {
                S(this).wrapAll(n ? t.call(this, e) : t)
            })
        },
        unwrap: function (e) {
            return this.parent(e).not("body").each(function () {
                S(this).replaceWith(this.childNodes)
            }), this
        }
    }), S.expr.pseudos.hidden = function (e) {
        return !S.expr.pseudos.visible(e)
    }, S.expr.pseudos.visible = function (e) {
        return !!(e.offsetWidth || e.offsetHeight || e.getClientRects().length)
    }, S.ajaxSettings.xhr = function () {
        try {
            return new C.XMLHttpRequest
        } catch (e) {}
    };
    var _t = {
            0: 200,
            1223: 204
        },
        zt = S.ajaxSettings.xhr();
    y.cors = !!zt && "withCredentials" in zt, y.ajax = zt = !!zt, S.ajaxTransport(function (i) {
        var o, a;
        if (y.cors || zt && !i.crossDomain) return {
            send: function (e, t) {
                var n, r = i.xhr();
                if (r.open(i.type, i.url, i.async, i.username, i.password), i.xhrFields)
                    for (n in i.xhrFields) r[n] = i.xhrFields[n];
                for (n in i.mimeType && r.overrideMimeType && r.overrideMimeType(i.mimeType), i.crossDomain || e["X-Requested-With"] || (e["X-Requested-With"] = "XMLHttpRequest"), e) r.setRequestHeader(n, e[n]);
                o = function (e) {
                    return function () {
                        o && (o = a = r.onload = r.onerror = r.onabort = r.ontimeout = r.onreadystatechange = null, "abort" === e ? r.abort() : "error" === e ? "number" != typeof r.status ? t(0, "error") : t(r.status, r.statusText) : t(_t[r.status] || r.status, r.statusText, "text" !== (r.responseType || "text") || "string" != typeof r.responseText ? {
                            binary: r.response
                        } : {
                            text: r.responseText
                        }, r.getAllResponseHeaders()))
                    }
                }, r.onload = o(), a = r.onerror = r.ontimeout = o("error"), void 0 !== r.onabort ? r.onabort = a : r.onreadystatechange = function () {
                    4 === r.readyState && C.setTimeout(function () {
                        o && a()
                    })
                }, o = o("abort");
                try {
                    r.send(i.hasContent && i.data || null)
                } catch (e) {
                    if (o) throw e
                }
            },
            abort: function () {
                o && o()
            }
        }
    }), S.ajaxPrefilter(function (e) {
        e.crossDomain && (e.contents.script = !1)
    }), S.ajaxSetup({
        accepts: {
            script: "text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"
        },
        contents: {
            script: /\b(?:java|ecma)script\b/
        },
        converters: {
            "text script": function (e) {
                return S.globalEval(e), e
            }
        }
    }), S.ajaxPrefilter("script", function (e) {
        void 0 === e.cache && (e.cache = !1), e.crossDomain && (e.type = "GET")
    }), S.ajaxTransport("script", function (n) {
        var r, i;
        if (n.crossDomain || n.scriptAttrs) return {
            send: function (e, t) {
                r = S("<script>").attr(n.scriptAttrs || {}).prop({
                    charset: n.scriptCharset,
                    src: n.url
                }).on("load error", i = function (e) {
                    r.remove(), i = null, e && t("error" === e.type ? 404 : 200, e.type)
                }), E.head.appendChild(r[0])
            },
            abort: function () {
                i && i()
            }
        }
    });
    var Ut, Xt = [],
        Vt = /(=)\?(?=&|$)|\?\?/;
    S.ajaxSetup({
        jsonp: "callback",
        jsonpCallback: function () {
            var e = Xt.pop() || S.expando + "_" + Ct.guid++;
            return this[e] = !0, e
        }
    }), S.ajaxPrefilter("json jsonp", function (e, t, n) {
        var r, i, o, a = !1 !== e.jsonp && (Vt.test(e.url) ? "url" : "string" == typeof e.data && 0 === (e.contentType || "").indexOf("application/x-www-form-urlencoded") && Vt.test(e.data) && "data");
        if (a || "jsonp" === e.dataTypes[0]) return r = e.jsonpCallback = m(e.jsonpCallback) ? e.jsonpCallback() : e.jsonpCallback, a ? e[a] = e[a].replace(Vt, "$1" + r) : !1 !== e.jsonp && (e.url += (Et.test(e.url) ? "&" : "?") + e.jsonp + "=" + r), e.converters["script json"] = function () {
            return o || S.error(r + " was not called"), o[0]
        }, e.dataTypes[0] = "json", i = C[r], C[r] = function () {
            o = arguments
        }, n.always(function () {
            void 0 === i ? S(C).removeProp(r) : C[r] = i, e[r] && (e.jsonpCallback = t.jsonpCallback, Xt.push(r)), o && m(i) && i(o[0]), o = i = void 0
        }), "script"
    }), y.createHTMLDocument = ((Ut = E.implementation.createHTMLDocument("").body).innerHTML = "<form></form><form></form>", 2 === Ut.childNodes.length), S.parseHTML = function (e, t, n) {
        return "string" != typeof e ? [] : ("boolean" == typeof t && (n = t, t = !1), t || (y.createHTMLDocument ? ((r = (t = E.implementation.createHTMLDocument("")).createElement("base")).href = E.location.href, t.head.appendChild(r)) : t = E), o = !n && [], (i = N.exec(e)) ? [t.createElement(i[1])] : (i = xe([e], t, o), o && o.length && S(o).remove(), S.merge([], i.childNodes)));
        var r, i, o
    }, S.fn.load = function (e, t, n) {
        var r, i, o, a = this,
            s = e.indexOf(" ");
        return -1 < s && (r = vt(e.slice(s)), e = e.slice(0, s)), m(t) ? (n = t, t = void 0) : t && "object" == typeof t && (i = "POST"), 0 < a.length && S.ajax({
            url: e,
            type: i || "GET",
            dataType: "html",
            data: t
        }).done(function (e) {
            o = arguments, a.html(r ? S("<div>").append(S.parseHTML(e)).find(r) : e)
        }).always(n && function (e, t) {
            a.each(function () {
                n.apply(this, o || [e.responseText, t, e])
            })
        }), this
    }, S.expr.pseudos.animated = function (t) {
        return S.grep(S.timers, function (e) {
            return t === e.elem
        }).length
    }, S.offset = {
        setOffset: function (e, t, n) {
            var r, i, o, a, s, u, l = S.css(e, "position"),
                c = S(e),
                f = {};
            "static" === l && (e.style.position = "relative"), s = c.offset(), o = S.css(e, "top"), u = S.css(e, "left"), ("absolute" === l || "fixed" === l) && -1 < (o + u).indexOf("auto") ? (a = (r = c.position()).top, i = r.left) : (a = parseFloat(o) || 0, i = parseFloat(u) || 0), m(t) && (t = t.call(e, n, S.extend({}, s))), null != t.top && (f.top = t.top - s.top + a), null != t.left && (f.left = t.left - s.left + i), "using" in t ? t.using.call(e, f) : ("number" == typeof f.top && (f.top += "px"), "number" == typeof f.left && (f.left += "px"), c.css(f))
        }
    }, S.fn.extend({
        offset: function (t) {
            if (arguments.length) return void 0 === t ? this : this.each(function (e) {
                S.offset.setOffset(this, t, e)
            });
            var e, n, r = this[0];
            return r ? r.getClientRects().length ? (e = r.getBoundingClientRect(), n = r.ownerDocument.defaultView, {
                top: e.top + n.pageYOffset,
                left: e.left + n.pageXOffset
            }) : {
                top: 0,
                left: 0
            } : void 0
        },
        position: function () {
            if (this[0]) {
                var e, t, n, r = this[0],
                    i = {
                        top: 0,
                        left: 0
                    };
                if ("fixed" === S.css(r, "position")) t = r.getBoundingClientRect();
                else {
                    t = this.offset(), n = r.ownerDocument, e = r.offsetParent || n.documentElement;
                    while (e && (e === n.body || e === n.documentElement) && "static" === S.css(e, "position")) e = e.parentNode;
                    e && e !== r && 1 === e.nodeType && ((i = S(e).offset()).top += S.css(e, "borderTopWidth", !0), i.left += S.css(e, "borderLeftWidth", !0))
                }
                return {
                    top: t.top - i.top - S.css(r, "marginTop", !0),
                    left: t.left - i.left - S.css(r, "marginLeft", !0)
                }
            }
        },
        offsetParent: function () {
            return this.map(function () {
                var e = this.offsetParent;
                while (e && "static" === S.css(e, "position")) e = e.offsetParent;
                return e || re
            })
        }
    }), S.each({
        scrollLeft: "pageXOffset",
        scrollTop: "pageYOffset"
    }, function (t, i) {
        var o = "pageYOffset" === i;
        S.fn[t] = function (e) {
            return $(this, function (e, t, n) {
                var r;
                if (x(e) ? r = e : 9 === e.nodeType && (r = e.defaultView), void 0 === n) return r ? r[i] : e[t];
                r ? r.scrollTo(o ? r.pageXOffset : n, o ? n : r.pageYOffset) : e[t] = n
            }, t, e, arguments.length)
        }
    }), S.each(["top", "left"], function (e, n) {
        S.cssHooks[n] = $e(y.pixelPosition, function (e, t) {
            if (t) return t = Be(e, n), Me.test(t) ? S(e).position()[n] + "px" : t
        })
    }), S.each({
        Height: "height",
        Width: "width"
    }, function (a, s) {
        S.each({
            padding: "inner" + a,
            content: s,
            "": "outer" + a
        }, function (r, o) {
            S.fn[o] = function (e, t) {
                var n = arguments.length && (r || "boolean" != typeof e),
                    i = r || (!0 === e || !0 === t ? "margin" : "border");
                return $(this, function (e, t, n) {
                    var r;
                    return x(e) ? 0 === o.indexOf("outer") ? e["inner" + a] : e.document.documentElement["client" + a] : 9 === e.nodeType ? (r = e.documentElement, Math.max(e.body["scroll" + a], r["scroll" + a], e.body["offset" + a], r["offset" + a], r["client" + a])) : void 0 === n ? S.css(e, t, i) : S.style(e, t, n, i)
                }, s, n ? e : void 0, n)
            }
        })
    }), S.each(["ajaxStart", "ajaxStop", "ajaxComplete", "ajaxError", "ajaxSuccess", "ajaxSend"], function (e, t) {
        S.fn[t] = function (e) {
            return this.on(t, e)
        }
    }), S.fn.extend({
        bind: function (e, t, n) {
            return this.on(e, null, t, n)
        },
        unbind: function (e, t) {
            return this.off(e, null, t)
        },
        delegate: function (e, t, n, r) {
            return this.on(t, e, n, r)
        },
        undelegate: function (e, t, n) {
            return 1 === arguments.length ? this.off(e, "**") : this.off(t, e || "**", n)
        },
        hover: function (e, t) {
            return this.mouseenter(e).mouseleave(t || e)
        }
    }), S.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "), function (e, n) {
        S.fn[n] = function (e, t) {
            return 0 < arguments.length ? this.on(n, null, e, t) : this.trigger(n)
        }
    });
    var Gt = /^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;
    S.proxy = function (e, t) {
        var n, r, i;
        if ("string" == typeof t && (n = e[t], t = e, e = n), m(e)) return r = s.call(arguments, 2), (i = function () {
            return e.apply(t || this, r.concat(s.call(arguments)))
        }).guid = e.guid = e.guid || S.guid++, i
    }, S.holdReady = function (e) {
        e ? S.readyWait++ : S.ready(!0)
    }, S.isArray = Array.isArray, S.parseJSON = JSON.parse, S.nodeName = A, S.isFunction = m, S.isWindow = x, S.camelCase = X, S.type = w, S.now = Date.now, S.isNumeric = function (e) {
        var t = S.type(e);
        return ("number" === t || "string" === t) && !isNaN(e - parseFloat(e))
    }, S.trim = function (e) {
        return null == e ? "" : (e + "").replace(Gt, "")
    }, "function" == typeof define && define.amd && define("jquery", [], function () {
        return S
    });
    var Yt = C.jQuery,
        Qt = C.$;
    return S.noConflict = function (e) {
        return C.$ === S && (C.$ = Qt), e && C.jQuery === S && (C.jQuery = Yt), S
    }, "undefined" == typeof e && (C.jQuery = C.$ = S), S
});



! function() {
    var $, Annotator, Delegator, LinkParser, Range, Util, base64Decode, base64UrlDecode, createDateFromISO8601, findChild, fn, functions, g, getNodeName, getNodePosition, gettext, parseToken, simpleXPathJQuery, simpleXPathPure, _Annotator, _gettext, _i, _j, _len, _len1, _ref, _ref1, _ref2, _ref3, _ref4, _t, __slice = [].slice,
        __hasProp = {}.hasOwnProperty,
        __extends = function(child, parent) {
            for (var key in parent) {
                if (__hasProp.call(parent, key)) child[key] = parent[key]
            }

            function ctor() {
                this.constructor = child
            }
            ctor.prototype = parent.prototype;
            child.prototype = new ctor;
            child.__super__ = parent.prototype;
            return child
        },
        __bind = function(fn, me) {
            return function() {
                return fn.apply(me, arguments)
            }
        },
        __indexOf = [].indexOf || function(item) {
            for (var i = 0, l = this.length; i < l; i++) {
                if (i in this && this[i] === item) return i
            }
            return -1
        };
    simpleXPathJQuery = function(relativeRoot) {
        var jq;
        jq = this.map(function() {
            var elem, idx, path, tagName;
            path = "";
            elem = this;
            while ((elem != null ? elem.nodeType : void 0) === Node.ELEMENT_NODE && elem !== relativeRoot) {
                tagName = elem.tagName.replace(":", "\\:");
                idx = $(elem.parentNode).children(tagName).index(elem) + 1;
                idx = "[" + idx + "]";
                path = "/" + elem.tagName.toLowerCase() + idx + path;
                elem = elem.parentNode
            }
            return path
        });
        return jq.get()
    };
    simpleXPathPure = function(relativeRoot) {
        var getPathSegment, getPathTo, jq, rootNode;
        getPathSegment = function(node) {
            var name, pos;
            name = getNodeName(node);
            pos = getNodePosition(node);
            return "" + name + "[" + pos + "]"
        };
        rootNode = relativeRoot;
        getPathTo = function(node) {
            var xpath;
            xpath = "";
            while (node !== rootNode) {
                if (node == null) {
                    throw new Error("Called getPathTo on a node which was not a descendant of @rootNode. " + rootNode)
                }
                xpath = getPathSegment(node) + "/" + xpath;
                node = node.parentNode
            }
            xpath = "/" + xpath;
            xpath = xpath.replace(/\/$/, "");
            return xpath
        };
        jq = this.map(function() {
            var path;
            path = getPathTo(this);
            return path
        });
        return jq.get()
    };
    findChild = function(node, type, index) {
        var child, children, found, name, _i, _len;
        if (!node.hasChildNodes()) {
            throw new Error("XPath error: node has no children!")
        }
        children = node.childNodes;
        found = 0;
        for (_i = 0, _len = children.length; _i < _len; _i++) {
            child = children[_i];
            name = getNodeName(child);
            if (name === type) {
                found += 1;
                if (found === index) {
                    return child
                }
            }
        }
        throw new Error("XPath error: wanted child not found.")
    };
    getNodeName = function(node) {
        var nodeName;
        nodeName = node.nodeName.toLowerCase();
        switch (nodeName) {
            case "#text":
                return "text()";
            case "#comment":
                return "comment()";
            case "#cdata-section":
                return "cdata-section()";
            default:
                return nodeName
        }
    };
    getNodePosition = function(node) {
        var pos, tmp;
        pos = 0;
        tmp = node;
        while (tmp) {
            if (tmp.nodeName === node.nodeName) {
                pos++
            }
            tmp = tmp.previousSibling
        }
        return pos
    };
    gettext = null;
    if (typeof Gettext !== "undefined" && Gettext !== null) {
        _gettext = new Gettext({
            domain: "annotator"
        });
        gettext = function(msgid) {
            return _gettext.gettext(msgid)
        }
    } else {
        gettext = function(msgid) {
            return msgid
        }
    }
    _t = function(msgid) {
        return gettext(msgid)
    };
    if (!(typeof jQuery !== "undefined" && jQuery !== null ? (_ref = jQuery.fn) != null ? _ref.jquery : void 0 : void 0)) {
        console.error(_t("Annotator requires jQuery: have you included lib/vendor/jquery.js?"))
    }
    if (!(JSON && JSON.parse && JSON.stringify)) {
        console.error(_t("Annotator requires a JSON implementation: have you included lib/vendor/json2.js?"))
    }
    $ = jQuery;
    Util = {};
    Util.flatten = function(array) {
        var flatten;
        flatten = function(ary) {
            var el, flat, _i, _len;
            flat = [];
            for (_i = 0, _len = ary.length; _i < _len; _i++) {
                el = ary[_i];
                flat = flat.concat(el && $.isArray(el) ? flatten(el) : el)
            }
            return flat
        };
        return flatten(array)
    };
    Util.contains = function(parent, child) {
        var node;
        node = child;
        while (node != null) {
            if (node === parent) {
                return !0
            }
            node = node.parentNode
        }
        return !1
    };
    Util.getTextNodes = function(jq) {
        var getTextNodes;
        getTextNodes = function(node) {
            var nodes;
            if (node && node.nodeType !== Node.TEXT_NODE) {
                nodes = [];
                if (node.nodeType !== Node.COMMENT_NODE) {
                    node = node.lastChild;
                    while (node) {
                        nodes.push(getTextNodes(node));
                        node = node.previousSibling
                    }
                }
                return nodes.reverse()
            } else {
                return node
            }
        };
        return jq.map(function() {
            return Util.flatten(getTextNodes(this))
        })
    };
    Util.getLastTextNodeUpTo = function(n) {
        var result;
        switch (n.nodeType) {
            case Node.TEXT_NODE:
                return n;
            case Node.ELEMENT_NODE:
                if (n.lastChild != null) {
                    result = Util.getLastTextNodeUpTo(n.lastChild);
                    if (result != null) {
                        return result
                    }
                }
                break
        }
        n = n.previousSibling;
        if (n != null) {
            return Util.getLastTextNodeUpTo(n)
        } else {
            return null
        }
    };
    Util.getFirstTextNodeNotBefore = function(n) {
        var result;
        switch (n.nodeType) {
            case Node.TEXT_NODE:
                return n;
            case Node.ELEMENT_NODE:
                if (n.firstChild != null) {
                    result = Util.getFirstTextNodeNotBefore(n.firstChild);
                    if (result != null) {
                        return result
                    }
                }
                break
        }
        n = n.nextSibling;
        if (n != null) {
            return Util.getFirstTextNodeNotBefore(n)
        } else {
            return null
        }
    };
    Util.readRangeViaSelection = function(range) {
        var sel;
        sel = Util.getGlobal().getSelection();
        sel.removeAllRanges();
        sel.addRange(range.toRange());
        return sel.toString()
    };
    Util.xpathFromNode = function(el, relativeRoot) {
        var exception, result;
        try {
            result = simpleXPathJQuery.call(el, relativeRoot)
        } catch (_error) {
            exception = _error;
            console.log("jQuery-based XPath construction failed! Falling back to manual.");
            result = simpleXPathPure.call(el, relativeRoot)
        }
        return result
    };
    Util.nodeFromXPath = function(xp, root) {
        var idx, name, node, step, steps, _i, _len, _ref1;
        steps = xp.substring(1).split("/");
        node = root;
        for (_i = 0, _len = steps.length; _i < _len; _i++) {
            step = steps[_i];
            _ref1 = step.split("["), name = _ref1[0], idx = _ref1[1];
            idx = idx != null ? parseInt((idx != null ? idx.split("]") : void 0)[0]) : 1;
            node = findChild(node, name.toLowerCase(), idx)
        }
        return node
    };
    Util.escape = function(html) {
        return html.replace(/&(?!\w+;)/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;")
    };
    Util.uuid = function() {
        var counter;
        counter = 0;
        return function() {
            return counter++
        }
    }();
    Util.getGlobal = function() {
        return function() {
            return this
        }()
    };
    Util.maxZIndex = function($elements) {
        var all, el;
        all = function() {
            var _i, _len, _results;
            _results = [];
            for (_i = 0, _len = $elements.length; _i < _len; _i++) {
                el = $elements[_i];
                if ($(el).css("position") === "static") {
                    _results.push(-1)
                } else {
                    _results.push(parseFloat($(el).css("z-index")) || -1)
                }
            }
            return _results
        }();
        return Math.max.apply(Math, all)
    };
    Util.mousePosition = function(e, offsetEl) {
        var offset, _ref1;
        if ((_ref1 = $(offsetEl).css("position")) !== "absolute" && _ref1 !== "fixed" && _ref1 !== "relative") {
            offsetEl = $(offsetEl).offsetParent()[0]
        }
        offset = $(offsetEl).offset();
        if(e.pageX > 460){
            e.pageX=e.pageX - 250;
        }
        return {
            top: e.pageY - offset.top,
            left: e.pageX - offset.left
        }
    };
    Util.preventEventDefault = function(event) {
        return event != null ? typeof event.preventDefault === "function" ? event.preventDefault() : void 0 : void 0
    };
    functions = ["log", "debug", "info", "warn", "exception", "assert", "dir", "dirxml", "trace", "group", "groupEnd", "groupCollapsed", "time", "timeEnd", "profile", "profileEnd", "count", "clear", "table", "error", "notifyFirebug", "firebug", "userObjects"];
    if (typeof console !== "undefined" && console !== null) {
        if (console.group == null) {
            console.group = function(name) {
                return console.log("GROUP: ", name)
            }
        }
        if (console.groupCollapsed == null) {
            console.groupCollapsed = console.group
        }
        for (_i = 0, _len = functions.length; _i < _len; _i++) {
            fn = functions[_i];
            if (console[fn] == null) {
                console[fn] = function() {
                    return console.log(_t("Not implemented:") + (" console." + name))
                }
            }
        }
    } else {
        this.console = {};
        for (_j = 0, _len1 = functions.length; _j < _len1; _j++) {
            fn = functions[_j];
            this.console[fn] = function() {}
        }
        this.console.error = function() {
            var args;
            args = 1 <= arguments.length ? __slice.call(arguments, 0) : [];
            return alert("ERROR: " + args.join(", "))
        };
        this.console.warn = function() {
            var args;
            args = 1 <= arguments.length ? __slice.call(arguments, 0) : [];
            return alert("WARNING: " + args.join(", "))
        }
    }
    Delegator = function() {
        Delegator.prototype.events = {};
        Delegator.prototype.options = {};
        Delegator.prototype.element = null;

        function Delegator(element, options) {
            this.options = $.extend(!0, {}, this.options, options);
            this.element = $(element);
            this._closures = {};
            this.on = this.subscribe;
            this.addEvents()
        }
        Delegator.prototype.destroy = function() {
            return this.removeEvents()
        };
        Delegator.prototype.addEvents = function() {
            var event, _k, _len2, _ref1, _results;
            _ref1 = Delegator._parseEvents(this.events);
            _results = [];
            for (_k = 0, _len2 = _ref1.length; _k < _len2; _k++) {
                event = _ref1[_k];
                _results.push(this._addEvent(event.selector, event.event, event.functionName))
            }
            return _results
        };
        Delegator.prototype.removeEvents = function() {
            var event, _k, _len2, _ref1, _results;
            _ref1 = Delegator._parseEvents(this.events);
            _results = [];
            for (_k = 0, _len2 = _ref1.length; _k < _len2; _k++) {
                event = _ref1[_k];
                _results.push(this._removeEvent(event.selector, event.event, event.functionName))
            }
            return _results
        };
        Delegator.prototype._addEvent = function(selector, event, functionName) {
            var closure, _this = this;
            closure = function() {
                return _this[functionName].apply(_this, arguments)
            };
            if (selector === "" && Delegator._isCustomEvent(event)) {
                this.subscribe(event, closure)
            } else {
                this.element.delegate(selector, event, closure)
            }
            this._closures["" + selector + "/" + event + "/" + functionName] = closure;
            return this
        };
        Delegator.prototype._removeEvent = function(selector, event, functionName) {
            var closure;
            closure = this._closures["" + selector + "/" + event + "/" + functionName];
            if (selector === "" && Delegator._isCustomEvent(event)) {
                this.unsubscribe(event, closure)
            } else {
                this.element.undelegate(selector, event, closure)
            }
            delete this._closures["" + selector + "/" + event + "/" + functionName];
            return this
        };
        Delegator.prototype.publish = function() {
            this.element.triggerHandler.apply(this.element, arguments);
            return this
        };
        Delegator.prototype.subscribe = function(event, callback) {
            var closure;
            closure = function() {
                return callback.apply(this, [].slice.call(arguments, 1))
            };
            closure.guid = callback.guid = $.guid += 1;
            this.element.bind(event, closure);
            return this
        };
        Delegator.prototype.unsubscribe = function() {
            this.element.unbind.apply(this.element, arguments);
            return this
        };
        return Delegator
    }();
    Delegator._parseEvents = function(eventsObj) {
        var event, events, functionName, sel, selector, _k, _ref1;
        events = [];
        for (sel in eventsObj) {
            functionName = eventsObj[sel];
            _ref1 = sel.split(" "), selector = 2 <= _ref1.length ? __slice.call(_ref1, 0, _k = _ref1.length - 1) : (_k = 0, []), event = _ref1[_k++];
            events.push({
                selector: selector.join(" "),
                event: event,
                functionName: functionName
            })
        }
        return events
    };
    Delegator.natives = function() {
        var key, specials, val;
        specials = function() {
            var _ref1, _results;
            _ref1 = jQuery.event.special;
            _results = [];
            for (key in _ref1) {
                if (!__hasProp.call(_ref1, key)) continue;
                val = _ref1[key];
                _results.push(key)
            }
            return _results
        }();
        return "blur focus focusin focusout load resize scroll unload click dblclick\nmousedown mouseup mousemove mouseover mouseout mouseenter mouseleave\nchange select submit keydown keypress keyup error".split(/[^a-z]+/).concat(specials)
    }();
    Delegator._isCustomEvent = function(event) {
        event = event.split(".")[0];
        return $.inArray(event, Delegator.natives) === -1
    };
    Range = {};
    Range.sniff = function(r) {
        if (r.commonAncestorContainer != null) {
            return new Range.BrowserRange(r)
        } else if (typeof r.start === "string") {
            return new Range.SerializedRange(r)
        } else if (r.start && typeof r.start === "object") {
            return new Range.NormalizedRange(r)
        } else {
            console.error(_t("Could not sniff range type"));
            return !1
        }
    };
    Range.nodeFromXPath = function(xpath, root) {
        var customResolver, evaluateXPath, namespace, node, segment;
        if (root == null) {
            root = document
        }
        evaluateXPath = function(xp, nsResolver) {
            var exception;
            if (nsResolver == null) {
                nsResolver = null
            }
            try {
                return document.evaluate("." + xp, root, nsResolver, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue
            } catch (_error) {
                exception = _error;
                console.log("XPath evaluation failed.");
                console.log("Trying fallback...");
                return Util.nodeFromXPath(xp, root)
            }
        };
        if (!$.isXMLDoc(document.documentElement)) {
            return evaluateXPath(xpath)
        } else {
            customResolver = document.createNSResolver(document.ownerDocument === null ? document.documentElement : document.ownerDocument.documentElement);
            node = evaluateXPath(xpath, customResolver);
            if (!node) {
                xpath = function() {
                    var _k, _len2, _ref1, _results;
                    _ref1 = xpath.split("/");
                    _results = [];
                    for (_k = 0, _len2 = _ref1.length; _k < _len2; _k++) {
                        segment = _ref1[_k];
                        if (segment && segment.indexOf(":") === -1) {
                            _results.push(segment.replace(/^([a-z]+)/, "xhtml:$1"))
                        } else {
                            _results.push(segment)
                        }
                    }
                    return _results
                }().join("/");
                namespace = document.lookupNamespaceURI(null);
                customResolver = function(ns) {
                    if (ns === "xhtml") {
                        return namespace
                    } else {
                        return document.documentElement.getAttribute("xmlns:" + ns)
                    }
                };
                node = evaluateXPath(xpath, customResolver)
            }
            return node
        }
    };
    Range.RangeError = function(_super) {
        __extends(RangeError, _super);

        function RangeError(type, message, parent) {
            this.type = type;
            this.message = message;
            this.parent = parent != null ? parent : null;
            RangeError.__super__.constructor.call(this, this.message)
        }
        return RangeError
    }(Error);
    Range.BrowserRange = function() {
        function BrowserRange(obj) {
            this.commonAncestorContainer = obj.commonAncestorContainer;
            this.startContainer = obj.startContainer;
            this.startOffset = obj.startOffset;
            this.endContainer = obj.endContainer;
            this.endOffset = obj.endOffset
        }
        BrowserRange.prototype.normalize = function(root) {
            var n, node, nr, r;
            if (this.tainted) {
                console.error(_t("You may only call normalize() once on a BrowserRange!"));
                return !1
            } else {
                this.tainted = !0
            }
            r = {};
            if (this.startContainer.nodeType === Node.ELEMENT_NODE) {
                r.start = Util.getFirstTextNodeNotBefore(this.startContainer.childNodes[this.startOffset]);
                r.startOffset = 0
            } else {
                r.start = this.startContainer;
                r.startOffset = this.startOffset
            }
            if (this.endContainer.nodeType === Node.ELEMENT_NODE) {
                node = this.endContainer.childNodes[this.endOffset];
                if (node != null) {
                    n = node;
                    while (n != null && n.nodeType !== Node.TEXT_NODE) {
                        n = n.firstChild
                    }
                    if (n != null) {
                        r.end = n;
                        r.endOffset = 0
                    }
                }
                if (r.end == null) {
                    node = this.endContainer.childNodes[this.endOffset - 1];
                    r.end = Util.getLastTextNodeUpTo(node);
                    r.endOffset = r.end.nodeValue.length
                }
            } else {
                r.end = this.endContainer;
                r.endOffset = this.endOffset
            }
            nr = {};
            if (r.startOffset > 0) {
                if (r.start.nodeValue.length > r.startOffset) {
                    nr.start = r.start.splitText(r.startOffset)
                } else {
                    nr.start = r.start.nextSibling
                }
            } else {
                nr.start = r.start
            }
            if (r.start === r.end) {
                if (nr.start.nodeValue.length > r.endOffset - r.startOffset) {
                    nr.start.splitText(r.endOffset - r.startOffset)
                }
                nr.end = nr.start
            } else {
                if (r.end.nodeValue.length > r.endOffset) {
                    r.end.splitText(r.endOffset)
                }
                nr.end = r.end
            }
            nr.commonAncestor = this.commonAncestorContainer;
            while (nr.commonAncestor.nodeType !== Node.ELEMENT_NODE) {
                nr.commonAncestor = nr.commonAncestor.parentNode
            }
            return new Range.NormalizedRange(nr)
        };
        BrowserRange.prototype.serialize = function(root, ignoreSelector) {
            return this.normalize(root).serialize(root, ignoreSelector)
        };
        return BrowserRange
    }();
    Range.NormalizedRange = function() {
        function NormalizedRange(obj) {
            this.commonAncestor = obj.commonAncestor;
            this.start = obj.start;
            this.end = obj.end
        }
        NormalizedRange.prototype.normalize = function(root) {
            return this
        };
        NormalizedRange.prototype.limit = function(bounds) {
            var nodes, parent, startParents, _k, _len2, _ref1;
            nodes = $.grep(this.textNodes(), function(node) {
                return node.parentNode === bounds || $.contains(bounds, node.parentNode)
            });
            if (!nodes.length) {
                return null
            }
            this.start = nodes[0];
            this.end = nodes[nodes.length - 1];
            startParents = $(this.start).parents();
            _ref1 = $(this.end).parents();
            for (_k = 0, _len2 = _ref1.length; _k < _len2; _k++) {
                parent = _ref1[_k];
                if (startParents.index(parent) !== -1) {
                    this.commonAncestor = parent;
                    break
                }
            }
            return this
        };
        NormalizedRange.prototype.serialize = function(root, ignoreSelector) {
            var end, serialization, start;
            serialization = function(node, isEnd) {
                var n, nodes, offset, origParent, textNodes, xpath, _k, _len2;
                if (ignoreSelector) {
                    origParent = $(node).parents(":not(" + ignoreSelector + ")").eq(0)
                } else {
                    origParent = $(node).parent()
                }
                xpath = Util.xpathFromNode(origParent, root)[0];
                textNodes = Util.getTextNodes(origParent);
                nodes = textNodes.slice(0, textNodes.index(node));
                offset = 0;
                for (_k = 0, _len2 = nodes.length; _k < _len2; _k++) {
                    n = nodes[_k];
                    offset += n.nodeValue.length
                }
                if (isEnd) {
                    return [xpath, offset + node.nodeValue.length]
                } else {
                    return [xpath, offset]
                }
            };
            start = serialization(this.start);
            end = serialization(this.end, !0);
            return new Range.SerializedRange({
                start: start[0],
                end: end[0],
                startOffset: start[1],
                endOffset: end[1]
            })
        };
        NormalizedRange.prototype.text = function() {
            var node;
            return function() {
                var _k, _len2, _ref1, _results;
                _ref1 = this.textNodes();
                _results = [];
                for (_k = 0, _len2 = _ref1.length; _k < _len2; _k++) {
                    node = _ref1[_k];
                    _results.push(node.nodeValue)
                }
                return _results
            }.call(this).join("")
        };
        NormalizedRange.prototype.textNodes = function() {
            var end, start, textNodes, _ref1;
            textNodes = Util.getTextNodes($(this.commonAncestor));
            _ref1 = [textNodes.index(this.start), textNodes.index(this.end)], start = _ref1[0], end = _ref1[1];
            return $.makeArray(textNodes.slice(start, +end + 1 || 9e9))
        };
        NormalizedRange.prototype.toRange = function() {
            var range;
            range = document.createRange();
            range.setStartBefore(this.start);
            range.setEndAfter(this.end);
            return range
        };
        return NormalizedRange
    }();
    Range.SerializedRange = function() {
        function SerializedRange(obj) {
            this.start = obj.start;
            this.startOffset = obj.startOffset;
            this.end = obj.end;
            this.endOffset = obj.endOffset
        }
        SerializedRange.prototype.normalize = function(root) {
            var contains, e, length, node, p, range, targetOffset, tn, _k, _l, _len2, _len3, _ref1, _ref2;
            range = {};
            _ref1 = ["start", "end"];
            for (_k = 0, _len2 = _ref1.length; _k < _len2; _k++) {
                p = _ref1[_k];
                try {
                    node = Range.nodeFromXPath(this[p], root)
                } catch (_error) {
                    e = _error;
                    throw new Range.RangeError(p, "Error while finding " + p + " node: " + this[p] + ": " + e, e)
                }
                if (!node) {
                    throw new Range.RangeError(p, "Couldn't find " + p + " node: " + this[p])
                }
                length = 0;
                targetOffset = this[p + "Offset"];
                if (p === "end") {
                    targetOffset--
                }
                _ref2 = Util.getTextNodes($(node));
                for (_l = 0, _len3 = _ref2.length; _l < _len3; _l++) {
                    tn = _ref2[_l];
                    if (length + tn.nodeValue.length > targetOffset) {
                        range[p + "Container"] = tn;
                        range[p + "Offset"] = this[p + "Offset"] - length;
                        break
                    } else {
                        length += tn.nodeValue.length
                    }
                }
                if (range[p + "Offset"] == null) {
                    throw new Range.RangeError("" + p + "offset", "Couldn't find offset " + this[p + "Offset"] + " in element " + this[p])
                }
            }
            contains = document.compareDocumentPosition == null ? function(a, b) {
                return a.contains(b)
            } : function(a, b) {
                return a.compareDocumentPosition(b) & 16
            };
            $(range.startContainer).parents().each(function() {
                if (contains(this, range.endContainer)) {
                    range.commonAncestorContainer = this;
                    return !1
                }
            });
            return new Range.BrowserRange(range).normalize(root)
        };
        SerializedRange.prototype.serialize = function(root, ignoreSelector) {
            return this.normalize(root).serialize(root, ignoreSelector)
        };
        SerializedRange.prototype.toObject = function() {
            return {
                start: this.start,
                startOffset: this.startOffset,
                end: this.end,
                endOffset: this.endOffset
            }
        };
        return SerializedRange
    }();
    _Annotator = this.Annotator;
    Annotator = function(_super) {
        __extends(Annotator, _super);
        Annotator.prototype.events = {
            ".add-btn click": "onAdderClick",
            ".add-btn mousedown": "onAdderMousedown",
            ".highlight-btn click": "onHighlighterClick",
            ".google-search-btn click": "onGoogleSearchClicked",
            ".annotator-hl mouseover": "onHighlightMouseover",
            ".annotator-hl mouseout": "startViewerHideTimer",
            ".annotator-hlh mouseover": "onHighlightMouseover",
            ".annotator-hlh mouseout": "startViewerHideTimer"
        };
        Annotator.prototype.html = {
            adder: '<div class="annotator-adder"><button class="annotate-btn highlight-btn">' + _t("Highlight") + "</button>" + "<button class='annotate-btn add-btn note-highlighting'>" + _t("Note") +"</button>"
                // +"<button class='annotate-btn'>" + _t("Translate") + "</button>"
                // +"<a  target='_blank'>Web Search</a>" +"</div>",
                + "<button class='annotate-btn google-search-btn' style=\"border-left: 1px solid rgba(68, 68, 68, 0.24);\" >" +_t("Ask Doubt") + "</button></div>",
            wrapper: '<div class="annotator-wrapper"></div>'
        };
        Annotator.prototype.options = {
            readOnly: !1
        };
        Annotator.prototype.plugins = {};
        Annotator.prototype.editor = null;
        Annotator.prototype.viewer = null;
        Annotator.prototype.selectedRanges = null;
        Annotator.prototype.mouseIsDown = !1;
        Annotator.prototype.ignoreMouseup = !1;
        Annotator.prototype.viewerHideTimer = null;

        function Annotator(element, options) {
            this.onDeleteAnnotation = __bind(this.onDeleteAnnotation, this);
            this.onEditAnnotation = __bind(this.onEditAnnotation, this);
            this.onAdderClick = __bind(this.onAdderClick, this);
            this.onHighlighterClick = __bind(this.onHighlighterClick, this);
            this.onGoogleSearchClicked = __bind(this.onGoogleSearchClicked, this);
            this.onAdderMousedown = __bind(this.onAdderMousedown, this);
            this.onHighlightMouseover = __bind(this.onHighlightMouseover, this);
            this.checkForEndSelection = __bind(this.checkForEndSelection, this);
            this.checkForStartSelection = __bind(this.checkForStartSelection, this);
            this.clearViewerHideTimer = __bind(this.clearViewerHideTimer, this);
            this.startViewerHideTimer = __bind(this.startViewerHideTimer, this);
            this.showViewer = __bind(this.showViewer, this);
            this.onEditorSubmit = __bind(this.onEditorSubmit, this);
            this.onEditorHide = __bind(this.onEditorHide, this);
            this.showEditor = __bind(this.showEditor, this);
            Annotator.__super__.constructor.apply(this, arguments);
            this.plugins = {};
            if (!Annotator.supported()) {
                return this
            }
            if (!this.options.readOnly) {
                this._setupDocumentEvents()
            }
            this._setupWrapper()._setupViewer()._setupEditor();
            this._setupDynamicStyle();
            this.adder = $(this.html.adder).appendTo(this.wrapper).hide();
            this.highlighter = $(this.html.adder).appendTo(this.wrapper).hide();
            Annotator._instances.push(this)
        }



        Annotator.prototype._setupWrapper = function() {
            this.wrapper = $(this.html.wrapper);
            this.element.find("script").remove();
            this.element.wrapInner(this.wrapper);
            this.wrapper = this.element.find(".annotator-wrapper");
            return this
        };
        Annotator.prototype._setupViewer = function() {
            var _this = this;
            this.viewer = new Annotator.Viewer({
                readOnly: this.options.readOnly
            });
            this.viewer.hide().on("edit", this.onEditAnnotation).on("delete", this.onDeleteAnnotation).addField({
                load: function(field, annotation) {
                    if (annotation.text) {
                        $(field).html(Util.escape(annotation.text))
                    } else {
                        $(field).html("<i>" + _t("No Comment") + "</i>")
                    }
                    return _this.publish("annotationViewerTextField", [field, annotation])
                }
            }).element.appendTo(this.wrapper).bind({
                mouseover: this.clearViewerHideTimer,
                mouseout: this.startViewerHideTimer
            });
            return this
        };
        Annotator.prototype._setupEditor = function() {
            this.editor = new Annotator.Editor;
            this.editor.hide().on("hide", this.onEditorHide).on("save", this.onEditorSubmit).addField({
                type: "textarea",
                label: _t("Add your notes here") + "...",
                load: function(field, annotation) {
                    return $(field).find("textarea").val(annotation.text || "")
                },
                submit: function(field, annotation) {
                    return annotation.text = $(field).find("textarea").val()
                }
            });
            this.editor.element.appendTo(this.wrapper);
            return this
        };
        Annotator.prototype._setupDocumentEvents = function() {
            $(document).bind({
                mouseup: this.checkForEndSelection,
                mousedown: this.checkForStartSelection
            });
            return this
        };
        Annotator.prototype._setupDynamicStyle = function() {
            var max, sel, style, x;
            style = $("#annotator-dynamic-style");
            if (!style.length) {
                style = $('<style id="annotator-dynamic-style"></style>').appendTo(document.head)
            }
            sel = "*" + function() {
                var _k, _len2, _ref1, _results;
                _ref1 = ["adder", "outer", "notice", "filter"];
                _results = [];
                for (_k = 0, _len2 = _ref1.length; _k < _len2; _k++) {
                    x = _ref1[_k];
                    _results.push(":not(.annotator-" + x + ")")
                }
                return _results
            }().join("");
            max = Util.maxZIndex($(document.body).find(sel));
            max = Math.max(max, 1e3);
            style.text([".annotator-adder, .annotator-outer, .annotator-notice {", "  z-index: " + (max + 20) + ";", "}", ".annotator-filter {", "  z-index: " + (max + 10) + ";", "}"].join("\n"));
            return this
        };
        Annotator.prototype.destroy = function() {
            var idx, name, plugin, _base, _ref1;
            Annotator.__super__.destroy.apply(this, arguments);
            $(document).unbind({
                mouseup: this.checkForEndSelection,
                mousedown: this.checkForStartSelection
            });
            $("#annotator-dynamic-style").remove();
            this.adder.remove();
            this.viewer.destroy();
            this.editor.destroy();
            this.wrapper.find(".annotator-hl").each(function() {
                $(this).contents().insertBefore(this);
                return $(this).remove()
            });
            this.wrapper.find(".annotator-hlh").each(function() {
                $(this).contents().insertBefore(this);
                return $(this).remove()
            });
            this.wrapper.contents().insertBefore(this.wrapper);
            this.wrapper.remove();
            this.element.data("annotator", null);
            _ref1 = this.plugins;
            for (name in _ref1) {
                plugin = _ref1[name];
                if (typeof(_base = this.plugins[name]).destroy === "function") {
                    _base.destroy()
                }
            }
            idx = Annotator._instances.indexOf(this);
            if (idx !== -1) {
                return Annotator._instances.splice(idx, 1)
            }
        };
        Annotator.prototype.getSelectedRanges = function() {
            var browserRange, i, normedRange, r, ranges, rangesToIgnore, selection, _k, _len2;
            selection = Util.getGlobal().getSelection();
            ranges = [];
            rangesToIgnore = [];
            if (!selection.isCollapsed) {
                ranges = function() {
                    var _k, _ref1, _results;
                    _results = [];
                    for (i = _k = 0, _ref1 = selection.rangeCount; 0 <= _ref1 ? _k < _ref1 : _k > _ref1; i = 0 <= _ref1 ? ++_k : --_k) {
                        r = selection.getRangeAt(i);
                        browserRange = new Range.BrowserRange(r);
                        normedRange = browserRange.normalize().limit(this.wrapper[0]);
                        if (normedRange === null) {
                            rangesToIgnore.push(r)
                        }
                        _results.push(normedRange)
                    }
                    return _results
                }.call(this);
                selection.removeAllRanges()
            }
            for (_k = 0, _len2 = rangesToIgnore.length; _k < _len2; _k++) {
                r = rangesToIgnore[_k];
                selection.addRange(r)
            }
            return $.grep(ranges, function(range) {
                if (range) {
                    selection.addRange(range.toRange())
                }
                return range
            })
        };
        Annotator.prototype.createAnnotation = function() {
            var annotation;
            annotation = {};
            this.publish("beforeAnnotationCreated", [annotation]);
            return annotation
        };
        var createdAnnotatedWithClassNames = [];
        Annotator.prototype.setupAnnotation = function(annotation) {
            var _lQuote = annotation.quote;
            if(_lQuote == "[object Object]") _lQuote = undefined;
            var e, normed, normedRanges, r, root, _k, _l, _len2, _len3, _ref1;
            root = this.wrapper[0];
            annotation.ranges || (annotation.ranges = this.selectedRanges);
            normedRanges = [];
            _ref1 = annotation.ranges;
            for (_k = 0, _len2 = _ref1.length; _k < _len2; _k++) {
                r = _ref1[_k];
                try {
                    normedRanges.push(Range.sniff(r).normalize(root))
                } catch (_error) {
                    e = _error;
                    if (e instanceof Range.RangeError) {
                        this.publish("rangeNormalizeFail", [annotation, r, e])
                    } else {
                        throw e
                    }
                }
            }
            var _currentPageTextForOriginalTextRange = [];
            var _currentNormed, _currentl, _currentlen3;
            for (_currentl = 0, _currentlen3 = normedRanges.length; _currentl < _currentlen3 && _lQuote != undefined; _currentl++) {
                _currentNormed = normedRanges[_currentl];
                _currentPageTextForOriginalTextRange.push($.trim(_currentNormed.text()));
            }
            annotation.quote = [];
            annotation.ranges = [];
            annotation.highlights = [];
            for (_l = 0, _len3 = normedRanges.length; _l < _len3 && (_lQuote == _currentPageTextForOriginalTextRange.join(" / ") || _lQuote == undefined); _l++) {
                normed = normedRanges[_l];
                annotation.quote.push($.trim(normed.text()));
                annotation.ranges.push(normed.serialize(this.wrapper[0], ".annotator-hl"));
                $.merge(annotation.highlights, this.highlightRange(normed,(annotation.text==null || typeof annotation.text === "undefined"?"annotator-hlh":"annotator-hl")))
            }
            if(_lQuote == annotation.quote.join(" / ") || _lQuote == undefined){
                annotation.quote = annotation.quote.join(" / ");
            $(annotation.highlights).data("annotation", annotation);
            $(annotation.highlights).attr("data-annotation-id", annotation.id);
            // setTimeout(function(){ $(annotation.highlights)[0].scrollIntoView()}, 3000);
            //console.log(annotation);
            createdAnnotatedWithClassNames.push(annotation);
            return annotation
            }else{
               var _lTempObj = {} ;
               return _lTempObj
            }
        };
        Annotator.prototype.updateAnnotation = function(annotation) {
            this.publish("beforeAnnotationUpdated", [annotation]);
            $(annotation.highlights).attr("data-annotation-id", annotation.id);
            this.publish("annotationUpdated", [annotation]);
            return annotation
        };
        Annotator.prototype.deleteAnnotation = function(annotation) {
            var child, h, _k, _len2, _ref1;
            if (annotation.highlights != null) {
                _ref1 = annotation.highlights;
                for (_k = 0, _len2 = _ref1.length; _k < _len2; _k++) {
                    h = _ref1[_k];
                    if (!(h.parentNode != null)) {
                        continue
                    }
                    child = h.childNodes[0];
                    $(h).replaceWith(h.childNodes)
                }
            }
            this.publish("annotationDeleted", [annotation]);
            return annotation
        };
        Annotator.prototype.loadAnnotations = function(annotations) {
            createdAnnotatedWithClassNames =[];
            var clone, loader, _this = this;
            if (annotations == null) {
                annotations = []
            }
            loader = function(annList) {
                var n, now, _k, _len2;
                if (annList == null) {
                    annList = []
                }
                now = annList.splice(0, 10);
                for (_k = 0, _len2 = now.length; _k < _len2; _k++) {
                    n = now[_k];
                    _this.setupAnnotation(n)
                }
                if (annList.length > 0) {
                    return setTimeout(function() {
                        return loader(annList)
                    }, 10)
                } else {
                    renderCreatedNotes(createdAnnotatedWithClassNames);
//                    var myCustomData = { foo: 'bar' };
//                    var event = new CustomEvent('annotationSearch', { detail: createdAnnotatedWithClassNames });
//                    window.parent.document.dispatchEvent(event);
                    return _this.publish("annotationsLoaded", [clone])
                }
            };
            clone = annotations.slice();
            loader(annotations);
            return this
        };
        Annotator.prototype.dumpAnnotations = function() {
            if (this.plugins.Store) {
                return this.plugins.Store.dumpAnnotations()
            } else {
                console.warn(_t("Can't dump annotations without Store plugin."));
                return !1
            }
        };
        Annotator.prototype.highlightRange = function(normedRange, cssClass) {
            var hl, node, white, _k, _len2, _ref1, _results;
            if (cssClass == null) {
                cssClass = "annotator-hl"
            }
            white = /^\s*$/;
            hl = $("<span class='" + cssClass + "'></span>");
            _ref1 = normedRange.textNodes();
            _results = [];
            for (_k = 0, _len2 = _ref1.length; _k < _len2; _k++) {
                node = _ref1[_k];
                if (!white.test(node.nodeValue)) {
                    _results.push($(node).wrapAll(hl).parent().show()[0])
                }
            }
            return _results
        };
        Annotator.prototype.highlightRanges = function(normedRanges, cssClass) {
            var highlights, r, _k, _len2;
            if (cssClass == null) {
                cssClass = "annotator-hl"
            }
            highlights = [];
            for (_k = 0, _len2 = normedRanges.length; _k < _len2; _k++) {
                r = normedRanges[_k];
                $.merge(highlights, this.highlightRange(r, cssClass))
            }
            return highlights
        };
        Annotator.prototype.addPlugin = function(name, options) {
            var klass, _base;
            if (this.plugins[name]) {
                console.error(_t("You cannot have more than one instance of any plugin."))
            } else {
                klass = Annotator.Plugin[name];
                if (typeof klass === "function") {
                    this.plugins[name] = new klass(this.element[0], options);
                    this.plugins[name].annotator = this;
                    if (typeof(_base = this.plugins[name]).pluginInit === "function") {
                        _base.pluginInit()
                    }
                } else {
                    console.error(_t("Could not load ") + name + _t(" plugin. Have you included the appropriate <script> tag?"))
                }
            }
            return this
        };
        Annotator.prototype.showEditor = function(annotation, location) {
            this.editor.element.css(location);
            this.editor.load(annotation);
            this.publish("annotationEditorShown", [this.editor, annotation]);
            return this
        };
        Annotator.prototype.onEditorHide = function() {
            this.publish("annotationEditorHidden", [this.editor]);
            return this.ignoreMouseup = !1
        };
        Annotator.prototype.onEditorSubmit = function(annotation) {
            return this.publish("annotationEditorSubmit", [this.editor, annotation])
        };
        Annotator.prototype.showViewer = function(annotations, location, hlClass) {
            this.viewer.element.css(location);
            this.viewer.load(annotations,hlClass);
            return this.publish("annotationViewerShown", [this.viewer, annotations])
        };
        Annotator.prototype.startViewerHideTimer = function() {
            if (!this.viewerHideTimer) {
                return this.viewerHideTimer = setTimeout(this.viewer.hide, 250)
            }
        };
        Annotator.prototype.clearViewerHideTimer = function() {
            clearTimeout(this.viewerHideTimer);
            return this.viewerHideTimer = !1
        };
        Annotator.prototype.checkForStartSelection = function(event) {

            if (!(event && this.isAnnotator(event.target))) {
                this.startViewerHideTimer()
            }
            return this.mouseIsDown = !0
        };
        Annotator.prototype.checkForEndSelection = function(event) {

            var container, range, _k, _len2, _ref1;
            this.mouseIsDown = !1;
            if (this.ignoreMouseup) {
                return
            }
            this.selectedRanges = this.getSelectedRanges();
            _ref1 = this.selectedRanges;
            for (_k = 0, _len2 = _ref1.length; _k < _len2; _k++) {
                range = _ref1[_k];
                container = range.commonAncestor;
                if (this.isAnnotator(container)) {
                    return
                }
            }
            if (event && this.selectedRanges.length) {
                return [
                    this.adder.css(Util.mousePosition(event, this.wrapper[0])),
                    this.adder.css({'display':'flex','align-items':'center'})
                ]
            } else {
                return this.adder.hide()
            }

        };
        Annotator.prototype.isAnnotator = function(element) {
            return !!$(element).parents().addBack().filter("[class^=annotator-]").not("[class=annotator-hl]").not(this.wrapper).length
        };
        Annotator.prototype.onHighlightMouseover = function(event) {
            //console.log(event.target.getAttribute('class'));
            var annotations;
            this.clearViewerHideTimer();
            if (this.mouseIsDown) {
                return !1
            }
            if (this.viewer.isShown()) {
                this.viewer.hide()
            }

            annotations = $(event.target).parents(event.target.getAttribute('class')=="annotator-hl"?".annotator-hl":".annotator-hlh").addBack().map(function() {
                return $(this).data("annotation")
            }).toArray();
            return this.showViewer(annotations, Util.mousePosition(event, this.wrapper[0]), event.target.getAttribute('class'))
        };
        Annotator.prototype.onAdderMousedown = function(event) {
            if (event != null) {
                event.preventDefault()
            }
            return this.ignoreMouseup = !0
        };
        Annotator.prototype.onAdderClick = function(event) {
            var annotation, cancel, cleanup, position, save, _this = this;
            if (event != null) {
                event.preventDefault()
            }
            position = this.adder.position();
            this.adder.hide();
            annotation = this.setupAnnotation(this.createAnnotation());
            $(annotation.highlights).removeClass("annotator-hlh");
            $(annotation.highlights).addClass("annotator-hl-temporary");
            save = function() {
                cleanup();
                $(annotation.highlights).removeClass("annotator-hl-temporary");
                $(annotation.highlights).addClass("annotator-hl");
                return _this.publish("annotationCreated", [annotation])
            };
            cancel = function() {
                cleanup();
                return _this.deleteAnnotation(annotation)
            };
            cleanup = function() {
                _this.unsubscribe("annotationEditorHidden", cancel);
                return _this.unsubscribe("annotationEditorSubmit", save)
            };
            this.subscribe("annotationEditorHidden", cancel);
            this.subscribe("annotationEditorSubmit", save);
            this.deleteAnnotation(annotation);
            /*var sel = Util.getGlobal().getSelection();
            sel.removeAllRanges();*/
            var data = annotation;
            delete data.highlights;
            var myCustomData = { action: 'notesNew',data: {"quote":data.quote,"ranges":data.ranges}};
            var event = new CustomEvent('annotationAction', { detail: myCustomData });
            window.parent.document.dispatchEvent(event);
            //return this.showEditor(annotation, position)
        };
        Annotator.prototype.onHighlighterClick = function(event) {
            var annotation, position, _this = this;
            if (event != null) {
                event.preventDefault()
            }
            position = this.adder.position();
            this.adder.hide();
            annotation = this.setupAnnotation(this.createAnnotation());
            var sel = Util.getGlobal().getSelection();
            sel.removeAllRanges();
            $(annotation.highlights).addClass("annotator-hlh");
            // console.log("111222333");
            // console.log(annotation);




            return _this.publish("annotationCreated", [annotation])
        };
        Annotator.prototype.onGoogleSearchClicked = function(event) {

            var annotation, position, _this = this;
            if (event != null) {
                event.preventDefault()
            }
            position = this.adder.position();
            this.adder.hide();
            annotation = this.setupAnnotation(this.createAnnotation());
            var sel = Util.getGlobal().getSelection();
            sel.removeAllRanges();
            $(annotation.highlights).addClass("annotator-gl");
            // if(annotation.quote != english)
            // annotation.text = translated text
            // else delete the annotation
            annotation.text = "Translated Text";
            $('#originalText').text(annotation.quote);
            // $.ajax({
            //     type: "POST",
            //     url: "https://translation.googleapis.com/language/translate/v2/languages\n" +
            //         "?key=AIzaSyBCrQXrkQEcERPCB2QS8BGShqztBa-DL-w",
            //     data:{
            //         "target":"de"
            //     },
            //     success: function(data, status){
            //         var strHTML = "";
            //         for(var ws=0; ws< data.data.languages.length; ws++){
            //             strHTML = strHTML + "<option value=\" "+data.data.languages[ws].language+"\">" + data.data.languages[ws].name + "</option>";
            //         }
            //         $('#languageList').html(strHTML);
            //         $('#removePhone').modal('show');
            //     }
            // });
            //var searchString = " https://www.google.com/search?q=" + annotation.quote;

            //window.open(searchString);
            this.deleteAnnotation(annotation);

            ReadJSInterface.onWebSearchTap(annotation.quote);
        }
        Annotator.prototype.manualSave = function() {
            var annotation, _this = this;

            $(annotation.highlights).removeClass("annotator-hl-temporary");
            return _this.publish("annotationCreated", [annotation])
        };
        Annotator.prototype.onEditAnnotation = function(annotation) {
            var cleanup, offset, update, _this = this;
            offset = this.viewer.element.position();
            update = function() {
                cleanup();
                return _this.updateAnnotation(annotation)
            };
            cleanup = function() {
                _this.unsubscribe("annotationEditorHidden", cleanup);
                return _this.unsubscribe("annotationEditorSubmit", update)
            };
            this.subscribe("annotationEditorHidden", cleanup);
            this.subscribe("annotationEditorSubmit", update);
            this.viewer.hide();
            var data = annotation;
            delete data.highlights;
            var myCustomData = { action: 'notesEdit',data: {"id":data.id,"quote":data.quote,"ranges":data.ranges,"text":data.text}};
            var event = new CustomEvent('annotationAction', { detail: myCustomData });
            window.parent.document.dispatchEvent(event);
            //return this.showEditor(annotation, offset)
        };
        Annotator.prototype.onDeleteAnnotation = function(annotation) {
            this.viewer.hide();
            return this.deleteAnnotation(annotation)
        };
        return Annotator
    }(Delegator);
    Annotator.Plugin = function(_super) {
        __extends(Plugin, _super);

        function Plugin(element, options) {
            Plugin.__super__.constructor.apply(this, arguments)
        }
        Plugin.prototype.pluginInit = function() {};
        return Plugin
    }(Delegator);
    g = Util.getGlobal();
    if (((_ref1 = g.document) != null ? _ref1.evaluate : void 0) == null) {
        $.getScript("http://assets.annotateit.org/vendor/xpath.min.js")
    }
    if (g.getSelection == null) {
        $.getScript("http://assets.annotateit.org/vendor/ierange.min.js")
    }
    if (g.JSON == null) {
        $.getScript("http://assets.annotateit.org/vendor/json2.min.js")
    }
    if (g.Node == null) {
        g.Node = {
            ELEMENT_NODE: 1,
            ATTRIBUTE_NODE: 2,
            TEXT_NODE: 3,
            CDATA_SECTION_NODE: 4,
            ENTITY_REFERENCE_NODE: 5,
            ENTITY_NODE: 6,
            PROCESSING_INSTRUCTION_NODE: 7,
            COMMENT_NODE: 8,
            DOCUMENT_NODE: 9,
            DOCUMENT_TYPE_NODE: 10,
            DOCUMENT_FRAGMENT_NODE: 11,
            NOTATION_NODE: 12
        }
    }
    Annotator.$ = $;
    Annotator.Delegator = Delegator;
    Annotator.Range = Range;
    Annotator.Util = Util;
    Annotator._instances = [];
    Annotator._t = _t;
    Annotator.supported = function() {
        return function() {
            return !!this.getSelection
        }()
    };
    Annotator.noConflict = function() {
        Util.getGlobal().Annotator = _Annotator;
        return this
    };
    $.fn.annotator = function(options) {
        var args;
        args = Array.prototype.slice.call(arguments, 1);
        return this.each(function() {
            var instance;
            instance = $.data(this, "annotator");
            if (options === "destroy") {
                $.removeData(this, "annotator");
                return instance != null ? instance.destroy(args) : void 0
            } else if (instance) {
                return options && instance[options].apply(instance, args)
            } else {
                instance = new Annotator(this, options);
                return $.data(this, "annotator", instance)
            }
        })
    };
    this.Annotator = Annotator;
    Annotator.Widget = function(_super) {
        __extends(Widget, _super);
        Widget.prototype.classes = {
            hide: "annotator-hide",
            invert: {
                x: "annotator-invert-x",
                y: "annotator-invert-y"
            }
        };

        function Widget(element, options) {
            Widget.__super__.constructor.apply(this, arguments);
            this.classes = $.extend({}, Annotator.Widget.prototype.classes, this.classes)
        }
        Widget.prototype.destroy = function() {
            this.removeEvents();
            return this.element.remove()
        };
        Widget.prototype.checkOrientation = function() {
            var current, offset, viewport, widget, window;
            this.resetOrientation();
            window = $(Annotator.Util.getGlobal());
            widget = this.element.children(":first");
            offset = widget.offset();
            viewport = {
                top: window.scrollTop(),
                right: window.width() + window.scrollLeft()
            };
            current = {
                top: offset.top,
                right: offset.left + widget.width()
            };
            if (current.top - viewport.top < 0) {
                this.invertY()
            }
            if (current.right - viewport.right > 0) {
                this.invertX()
            }
            return this
        };
        Widget.prototype.resetOrientation = function() {
            this.element.removeClass(this.classes.invert.x).removeClass(this.classes.invert.y);
            return this
        };
        Widget.prototype.invertX = function() {
            this.element.addClass(this.classes.invert.x);
            return this
        };
        Widget.prototype.invertY = function() {
            this.element.addClass(this.classes.invert.y);
            return this
        };
        Widget.prototype.isInvertedY = function() {
            return this.element.hasClass(this.classes.invert.y)
        };
        Widget.prototype.isInvertedX = function() {
            return this.element.hasClass(this.classes.invert.x)
        };
        return Widget
    }(Delegator);
    Annotator.Editor = function(_super) {
        __extends(Editor, _super);
        Editor.prototype.events = {
            "form submit": "submit",
            ".annotator-save click": "submit",
            ".annotator-cancel click": "hide",
            ".annotator-cancel mouseover": "onCancelButtonMouseover",
            "textarea keydown": "processKeypress"
        };
        Editor.prototype.classes = {
            hide: "annotator-hide",
            focus: "annotator-focus"
        };
        Editor.prototype.html = '<div class="annotator-outer annotator-editor">\n  <form class="annotator-widget">\n    <ul class="annotator-listing"></ul>\n    <div class="annotator-controls">\n      <a href="#cancel" class="annotator-cancel" onclick="cancelMetatagIos();">' + _t("Cancel") + '</a>\n<a href="#save" class="annotator-save annotator-focus" onclick="saveMetatagIos();" >' + _t("Save") + "</a>\n    </div>\n  </form>\n</div>";
        Editor.prototype.options = {};

        function Editor(options) {
            this.onCancelButtonMouseover = __bind(this.onCancelButtonMouseover, this);
            this.processKeypress = __bind(this.processKeypress, this);
            this.submit = __bind(this.submit, this);
            this.load = __bind(this.load, this);
            this.hide = __bind(this.hide, this);
            this.show = __bind(this.show, this);
            Editor.__super__.constructor.call(this, $(this.html)[0], options);
            this.fields = [];
            this.annotation = {}
        }
        Editor.prototype.show = function(event) {
            Annotator.Util.preventEventDefault(event);
            this.element.removeClass(this.classes.hide);
            this.element.find(".annotator-save").addClass(this.classes.focus);
            this.checkOrientation();
            this.element.find(":input:first").focus();
            this.setupDraggables();
            return this.publish("show")
        };
        Editor.prototype.hide = function(event) {
            Annotator.Util.preventEventDefault(event);
            this.element.addClass(this.classes.hide);
            return this.publish("hide")
        };
        Editor.prototype.load = function(annotation) {
            var field, _k, _len2, _ref2;
            this.annotation = annotation;
            this.publish("load", [this.annotation]);
            _ref2 = this.fields;
            for (_k = 0, _len2 = _ref2.length; _k < _len2; _k++) {
                field = _ref2[_k];
                field.load(field.element, this.annotation)
            }
            return this.show()
        };
        Editor.prototype.submit = function(event) {
            var field, _k, _len2, _ref2;
            Annotator.Util.preventEventDefault(event);
            _ref2 = this.fields;
            for (_k = 0, _len2 = _ref2.length; _k < _len2; _k++) {
                field = _ref2[_k];
                field.submit(field.element, this.annotation)
            }
            this.publish("save", [this.annotation]);
            return this.hide()
        };
        Editor.prototype.addField = function(options) {
            var element, field, input;
            field = $.extend({
                id: "annotator-field-" + Annotator.Util.uuid(),
                type: "input",
                label: "",
                load: function() {},
                submit: function() {}
            }, options);
            input = null;
            element = $('<li class="annotator-item" />');
            field.element = element[0];
            switch (field.type) {
                case "textarea":
                    input = $("<textarea />");
                    break;
                case "input":
                case "checkbox":
                    input = $("<input />");
                    break;
                case "select":
                    input = $("<select />")
            }
            element.append(input);
            input.attr({
                id: field.id,
                placeholder: field.label
            });
            if (field.type === "checkbox") {
                input[0].type = "checkbox";
                element.addClass("annotator-checkbox");
                element.append($("<label />", {
                    "for": field.id,
                    html: field.label
                }))
            }
            this.element.find("ul:first").append(element);
            this.fields.push(field);
            return field.element
        };
        Editor.prototype.checkOrientation = function() {
            var controls, list;
            Editor.__super__.checkOrientation.apply(this, arguments);
            list = this.element.find("ul");
            controls = this.element.find(".annotator-controls");
            if (this.element.hasClass(this.classes.invert.y)) {
                controls.insertBefore(list)
            } else if (controls.is(":first-child")) {
                controls.insertAfter(list)
            }
            return this
        };
        Editor.prototype.processKeypress = function(event) {
            if (event.keyCode === 27) {
                return this.hide()
            } else if (event.keyCode === 13 && !event.shiftKey) {
                return this.submit()
            }
        };
        Editor.prototype.onCancelButtonMouseover = function() {
            return this.element.find("." + this.classes.focus).removeClass(this.classes.focus)
        };
        Editor.prototype.setupDraggables = function() {
            var classes, controls, cornerItem, editor, mousedown, onMousedown, onMousemove, onMouseup, resize, textarea, throttle, _this = this;
            this.element.find(".annotator-resize").remove();
            if (this.element.hasClass(this.classes.invert.y)) {
                cornerItem = this.element.find(".annotator-item:last")
            } else {
                cornerItem = this.element.find(".annotator-item:first")
            }
            if (cornerItem) {
                $('<span class="annotator-resize"></span>').appendTo(cornerItem)
            }
            mousedown = null;
            classes = this.classes;
            editor = this.element;
            textarea = null;
            resize = editor.find(".annotator-resize");
            controls = editor.find(".annotator-controls");
            throttle = !1;
            onMousedown = function(event) {
                if (event.target === this) {
                    mousedown = {
                        element: this,
                        top: event.pageY,
                        left: event.pageX
                    };
                    textarea = editor.find("textarea:first");
                    $(window).bind({
                        "mouseup.annotator-editor-resize": onMouseup,
                        "mousemove.annotator-editor-resize": onMousemove
                    });
                    return event.preventDefault()
                }
            };
            onMouseup = function() {
                mousedown = null;
                return $(window).unbind(".annotator-editor-resize")
            };
            onMousemove = function(event) {
                var diff, directionX, directionY, height, width;
                if (mousedown && throttle === !1) {
                    diff = {
                        top: event.pageY - mousedown.top,
                        left: event.pageX - mousedown.left
                    };
                    if (mousedown.element === resize[0]) {
                        height = textarea.outerHeight();
                        width = textarea.outerWidth();
                        directionX = editor.hasClass(classes.invert.x) ? -1 : 1;
                        directionY = editor.hasClass(classes.invert.y) ? 1 : -1;
                        textarea.height(height + diff.top * directionY);
                        textarea.width(width + diff.left * directionX);
                        if (textarea.outerHeight() !== height) {
                            mousedown.top = event.pageY
                        }
                        if (textarea.outerWidth() !== width) {
                            mousedown.left = event.pageX
                        }
                    } else if (mousedown.element === controls[0]) {
                        editor.css({
                            top: parseInt(editor.css("top"), 10) + diff.top,
                            left: parseInt(editor.css("left"), 10) + diff.left
                        });
                        mousedown.top = event.pageY;
                        mousedown.left = event.pageX
                    }
                    throttle = !0;
                    return setTimeout(function() {
                        return throttle = !1
                    }, 1e3 / 60)
                }
            };
            resize.bind("mousedown", onMousedown);
            return controls.bind("mousedown", onMousedown)
        };
        return Editor
    }(Annotator.Widget);
    Annotator.Viewer = function(_super) {
        __extends(Viewer, _super);
        Viewer.prototype.events = {
            ".annotator-edit click": "onEditClick",
            ".annotator-delete click": "onDeleteClick"
        };
        Viewer.prototype.classes = {
            hide: "annotator-hide",
            showControls: "annotator-visible"
        };
        Viewer.prototype.html = {
            element: '<div class="annotator-outer annotator-viewer">\n  <ul class="annotator-widget annotator-listing"></ul>\n</div>',
            item: '<li class="annotator-annotation annotator-item">\n  <span class="annotator-controls">\n    <a href="#" title="View as webpage" class="annotator-link">View as webpage</a>\n    <button title="Edit" class="annotator-edit">Edit</button>\n    <button title="Delete" class="annotator-delete">Delete</button>\n  </span>\n</li>'
        };
        Viewer.prototype.options = {
            readOnly: !1
        };

        function Viewer(options) {
            this.onDeleteClick = __bind(this.onDeleteClick, this);
            this.onEditClick = __bind(this.onEditClick, this);
            this.load = __bind(this.load, this);
            this.hide = __bind(this.hide, this);
            this.show = __bind(this.show, this);
            Viewer.__super__.constructor.call(this, $(this.html.element)[0], options);
            this.item = $(this.html.item)[0];
            this.fields = [];
            this.annotations = []
        }
        Viewer.prototype.show = function(event) {
            var controls, _this = this;
            Annotator.Util.preventEventDefault(event);
            controls = this.element.find(".annotator-controls").addClass(this.classes.showControls);
            setTimeout(function() {
                return controls.removeClass(_this.classes.showControls)
            }, 500);
            this.element.removeClass(this.classes.hide);
            return this.checkOrientation().publish("show")
        };
        Viewer.prototype.isShown = function() {
            return !this.element.hasClass(this.classes.hide)
        };
        Viewer.prototype.hide = function(event) {
            Annotator.Util.preventEventDefault(event);
            this.element.addClass(this.classes.hide);
            return this.publish("hide")
        };
        Viewer.prototype.load = function(annotations, hlClass) {
            var annotation, controller, controls, del, edit, element, field, item, link, links, list, _k, _l, _len2, _len3, _ref2, _ref3;
            this.annotations = annotations || [];
            list = this.element.find("ul:first").empty();
            _ref2 = this.annotations;
            for (_k = 0, _len2 = _ref2.length; _k < _len2; _k++) {
                annotation = _ref2[_k];
                item = $(this.item).clone().appendTo(list).data("annotation", annotation);
                controls = item.find(".annotator-controls");
                link = controls.find(".annotator-link");
                edit = controls.find(".annotator-edit");
                del = controls.find(".annotator-delete");
                links = new LinkParser(annotation.links || []).get("alternate", {
                    type: "text/html"
                });
                if (links.length === 0 || links[0].href == null) {
                    link.remove()
                } else {
                    link.attr("href", links[0].href)
                }
                if (this.options.readOnly) {
                    edit.remove();
                    del.remove()
                } else {
                    if (hlClass=="annotator-hlh") {
                        edit.remove()
                    }

                    controller = {
                        showEdit: function() {
                            return edit.removeAttr("disabled")
                        },
                        hideEdit: function() {
                            return edit.attr("disabled", "disabled")
                        },
                        showDelete: function() {
                            return del.removeAttr("disabled")
                        },
                        hideDelete: function() {
                            return del.attr("disabled", "disabled")
                        }
                    }
                }
                _ref3 = this.fields;
                for (_l = 0, _len3 = _ref3.length; _l < _len3; _l++) {
                    field = _ref3[_l];
                    element = $(field.element).clone().appendTo(item)[0];
                    field.load(element, annotation, controller)
                }
            }
            this.publish("load", [this.annotations]);
            return this.show()
        };
        Viewer.prototype.addField = function(options) {
            var field;
            field = $.extend({
                load: function() {}
            }, options);
            field.element = $("<div />")[0];
            this.fields.push(field);
            field.element;
            return this
        };
        Viewer.prototype.onEditClick = function(event) {
            return this.onButtonClick(event, "edit")
        };
        Viewer.prototype.onDeleteClick = function(event) {
            return this.onButtonClick(event, "delete")
        };
        Viewer.prototype.onButtonClick = function(event, type) {
            var item;
            item = $(event.target).parents(".annotator-annotation");
            return this.publish(type, [item.data("annotation")])
        };
        return Viewer
    }(Annotator.Widget);
    LinkParser = function() {
        function LinkParser(data) {
            this.data = data
        }
        LinkParser.prototype.get = function(rel, cond) {
            var d, k, keys, match, v, _k, _len2, _ref2, _results;
            if (cond == null) {
                cond = {}
            }
            cond = $.extend({}, cond, {
                rel: rel
            });
            keys = function() {
                var _results;
                _results = [];
                for (k in cond) {
                    if (!__hasProp.call(cond, k)) continue;
                    v = cond[k];
                    _results.push(k)
                }
                return _results
            }();
            _ref2 = this.data;
            _results = [];
            for (_k = 0, _len2 = _ref2.length; _k < _len2; _k++) {
                d = _ref2[_k];
                match = keys.reduce(function(m, k) {
                    return m && d[k] === cond[k]
                }, !0);
                if (match) {
                    _results.push(d)
                } else {
                    continue
                }
            }
            return _results
        };
        return LinkParser
    }();
    Annotator = Annotator || {};
    Annotator.Notification = function(_super) {
        __extends(Notification, _super);
        Notification.prototype.events = {
            click: "hide"
        };
        Notification.prototype.options = {
            html: "<div class='annotator-notice'></div>",
            classes: {
                show: "annotator-notice-show",
                info: "annotator-notice-info",
                success: "annotator-notice-success",
                error: "annotator-notice-error"
            }
        };

        function Notification(options) {
            this.hide = __bind(this.hide, this);
            this.show = __bind(this.show, this);
            Notification.__super__.constructor.call(this, $(this.options.html).appendTo(document.body)[0], options)
        }
        Notification.prototype.show = function(message, status) {
            if (status == null) {
                status = Annotator.Notification.INFO
            }
            this.currentStatus = status;
            $(this.element).addClass(this.options.classes.show).addClass(this.options.classes[this.currentStatus]).html(Util.escape(message || ""));
            setTimeout(this.hide, 5e3);
            return this
        };
        Notification.prototype.hide = function() {
            if (this.currentStatus == null) {
                this.currentStatus = Annotator.Notification.INFO
            }
            $(this.element).removeClass(this.options.classes.show).removeClass(this.options.classes[this.currentStatus]);
            return this
        };
        return Notification
    }(Delegator);
    Annotator.Notification.INFO = "info";
    Annotator.Notification.SUCCESS = "success";
    Annotator.Notification.ERROR = "error";
    $(function() {
        var notification;
        notification = new Annotator.Notification;
        Annotator.showNotification = notification.show;
        return Annotator.hideNotification = notification.hide
    });
    Annotator.Plugin.Unsupported = function(_super) {
        __extends(Unsupported, _super);

        function Unsupported() {
            _ref2 = Unsupported.__super__.constructor.apply(this, arguments);
            return _ref2
        }
        Unsupported.prototype.options = {
            message: Annotator._t("Sorry your current browser does not support the Annotator")
        };
        Unsupported.prototype.pluginInit = function() {
            var _this = this;
            if (!Annotator.supported()) {
                return $(function() {
                    Annotator.showNotification(_this.options.message);
                    if (window.XMLHttpRequest === void 0 && ActiveXObject !== void 0) {
                        return $("html").addClass("ie6")
                    }
                })
            }
        };
        return Unsupported
    }(Annotator.Plugin);
    createDateFromISO8601 = function(string) {
        var d, date, offset, regexp, time, _ref3;
        regexp = "([0-9]{4})(-([0-9]{2})(-([0-9]{2})" + "(T([0-9]{2}):([0-9]{2})(:([0-9]{2})(\\.([0-9]+))?)?" + "(Z|(([-+])([0-9]{2}):([0-9]{2})))?)?)?)?";
        d = string.match(new RegExp(regexp));
        offset = 0;
        date = new Date(d[1], 0, 1);
        if (d[3]) {
            date.setMonth(d[3] - 1)
        }
        if (d[5]) {
            date.setDate(d[5])
        }
        if (d[7]) {
            date.setHours(d[7])
        }
        if (d[8]) {
            date.setMinutes(d[8])
        }
        if (d[10]) {
            date.setSeconds(d[10])
        }
        if (d[12]) {
            date.setMilliseconds(Number("0." + d[12]) * 1e3)
        }
        if (d[14]) {
            offset = Number(d[16]) * 60 + Number(d[17]);
            offset *= (_ref3 = d[15] === "-") != null ? _ref3 : {
                1: -1
            }
        }
        offset -= date.getTimezoneOffset();
        time = Number(date) + offset * 60 * 1e3;
        date.setTime(Number(time));
        return date
    };
    base64Decode = function(data) {
        var ac, b64, bits, dec, h1, h2, h3, h4, i, o1, o2, o3, tmp_arr;
        if (typeof atob !== "undefined" && atob !== null) {
            return atob(data)
        } else {
            b64 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
            i = 0;
            ac = 0;
            dec = "";
            tmp_arr = [];
            if (!data) {
                return data
            }
            data += "";
            while (i < data.length) {
                h1 = b64.indexOf(data.charAt(i++));
                h2 = b64.indexOf(data.charAt(i++));
                h3 = b64.indexOf(data.charAt(i++));
                h4 = b64.indexOf(data.charAt(i++));
                bits = h1 << 18 | h2 << 12 | h3 << 6 | h4;
                o1 = bits >> 16 & 255;
                o2 = bits >> 8 & 255;
                o3 = bits & 255;
                if (h3 === 64) {
                    tmp_arr[ac++] = String.fromCharCode(o1)
                } else if (h4 === 64) {
                    tmp_arr[ac++] = String.fromCharCode(o1, o2)
                } else {
                    tmp_arr[ac++] = String.fromCharCode(o1, o2, o3)
                }
            }
            return tmp_arr.join("")
        }
    };
    base64UrlDecode = function(data) {
        var i, m, _k, _ref3;
        m = data.length % 4;
        if (m !== 0) {
            for (i = _k = 0, _ref3 = 4 - m; 0 <= _ref3 ? _k < _ref3 : _k > _ref3; i = 0 <= _ref3 ? ++_k : --_k) {
                data += "="
            }
        }
        data = data.replace(/-/g, "+");
        data = data.replace(/_/g, "/");
        return base64Decode(data)
    };
    parseToken = function(token) {
        var head, payload, sig, _ref3;
        _ref3 = token.split("."), head = _ref3[0], payload = _ref3[1], sig = _ref3[2];
        return JSON.parse(base64UrlDecode(payload))
    };
    Annotator.Plugin.Auth = function(_super) {
        __extends(Auth, _super);
        Auth.prototype.options = {
            token: null,
            tokenUrl: "/auth/token",
            autoFetch: !0
        };

        function Auth(element, options) {
            Auth.__super__.constructor.apply(this, arguments);
            this.waitingForToken = [];
            if (this.options.token) {
                this.setToken(this.options.token)
            } else {
                this.requestToken()
            }
        }
        Auth.prototype.requestToken = function() {
            var _this = this;
            this.requestInProgress = !0;
            return $.ajax({
                url: this.options.tokenUrl,
                dataType: "text",
                xhrFields: {
                    withCredentials: !0
                }
            }).done(function(data, status, xhr) {
                return _this.setToken(data)
            }).fail(function(xhr, status, err) {
                var msg;
                msg = Annotator._t("Couldn't get auth token:");
                console.error("" + msg + " " + err, xhr);
                return Annotator.showNotification("" + msg + " " + xhr.responseText, Annotator.Notification.ERROR)
            }).always(function() {
                return _this.requestInProgress = !1
            })
        };
        Auth.prototype.setToken = function(token) {
            var _results, _this = this;
            this.token = token;
            this._unsafeToken = parseToken(token);
            if (this.haveValidToken()) {
                if (this.options.autoFetch) {
                    this.refreshTimeout = setTimeout(function() {
                        return _this.requestToken()
                    }, (this.timeToExpiry() - 2) * 1e3)
                }
                this.updateHeaders();
                _results = [];
                while (this.waitingForToken.length > 0) {
                    _results.push(this.waitingForToken.pop()(this._unsafeToken))
                }
                return _results
            } else {
                console.warn(Annotator._t("Didn't get a valid token."));
                if (this.options.autoFetch) {
                    console.warn(Annotator._t("Getting a new token in 10s."));
                    return setTimeout(function() {
                        return _this.requestToken()
                    }, 10 * 1e3)
                }
            }
        };
        Auth.prototype.haveValidToken = function() {
            var allFields;
            allFields = this._unsafeToken && this._unsafeToken.issuedAt && this._unsafeToken.ttl && this._unsafeToken.consumerKey;
            if (allFields && this.timeToExpiry() > 0) {
                return !0
            } else {
                return !1
            }
        };
        Auth.prototype.timeToExpiry = function() {
            var expiry, issue, now, timeToExpiry;
            now = (new Date).getTime() / 1e3;
            issue = createDateFromISO8601(this._unsafeToken.issuedAt).getTime() / 1e3;
            expiry = issue + this._unsafeToken.ttl;
            timeToExpiry = expiry - now;
            if (timeToExpiry > 0) {
                return timeToExpiry
            } else {
                return 0
            }
        };
        Auth.prototype.updateHeaders = function() {
            var current;
            current = this.element.data("annotator:headers");
            return this.element.data("annotator:headers", $.extend(current, {
                "x-annotator-auth-token": this.token
            }))
        };
        Auth.prototype.withToken = function(callback) {
            if (callback == null) {
                return
            }
            if (this.haveValidToken()) {
                return callback(this._unsafeToken)
            } else {
                this.waitingForToken.push(callback);
                if (!this.requestInProgress) {
                    return this.requestToken()
                }
            }
        };
        return Auth
    }(Annotator.Plugin);
    Annotator.Plugin.Store = function(_super) {
        __extends(Store, _super);
        Store.prototype.events = {
            annotationCreated: "annotationCreated",
            annotationDeleted: "annotationDeleted",
            annotationUpdated: "annotationUpdated"
        };
        Store.prototype.options = {
            annotationData: {},
            emulateHTTP: !1,
            loadFromSearch: !1,
            prefix: "/store",
            urls: {
                create: "/annotations",
                read: "/annotations/:id",
                update: "/annotations/:id",
                destroy: "/annotations/:id",
                search: "/search"
            }
        };

        function Store(element, options) {
            this._onError = __bind(this._onError, this);
            this._onLoadAnnotationsFromSearch = __bind(this._onLoadAnnotationsFromSearch, this);
            this._onLoadAnnotations = __bind(this._onLoadAnnotations, this);
            this._getAnnotations = __bind(this._getAnnotations, this);
            Store.__super__.constructor.apply(this, arguments);
            this.annotations = []
        }
        Store.prototype.pluginInit = function() {
            if (!Annotator.supported()) {
                return
            }
            if (this.annotator.plugins.Auth) {
                return this.annotator.plugins.Auth.withToken(this._getAnnotations)
            } else {
                return this._getAnnotations()
            }
        };
        Store.prototype._getAnnotations = function() {
            if (this.options.loadFromSearch) {
                return this.loadAnnotationsFromSearch(this.options.loadFromSearch)
            } else {
                return this.loadAnnotations()
            }
        };
        Store.prototype.annotationCreated = function(annotation) {
            var _this = this;
            if (__indexOf.call(this.annotations, annotation) < 0) {
                this.registerAnnotation(annotation);
                return this._apiRequest("create", annotation, function(data) {
                    if (data.id == null) {
                        console.warn(Annotator._t("Warning: No ID returned from server for annotation "), annotation)
                    }
                    return _this.updateAnnotation(annotation, data)
                })
            } else {
                return this.updateAnnotation(annotation, {})
            }
        };
        Store.prototype.annotationUpdated = function(annotation) {
            var _this = this;
            if (__indexOf.call(this.annotations, annotation) >= 0) {
                return this._apiRequest("update", annotation, function(data) {
                    return _this.updateAnnotation(annotation, data)
                })
            }
        };
        Store.prototype.annotationDeleted = function(annotation) {
            var _this = this;
            if (__indexOf.call(this.annotations, annotation) >= 0) {
                return this._apiRequest("destroy", annotation, function() {
                    return _this.unregisterAnnotation(annotation)
                })
            }
        };
        Store.prototype.registerAnnotation = function(annotation) {
            return this.annotations.push(annotation)
        };
        Store.prototype.unregisterAnnotation = function(annotation) {
            $("#"+annotation.id).remove();
            return this.annotations.splice(this.annotations.indexOf(annotation), 1)
        };
        Store.prototype.updateAnnotation = function(annotation, data) {
            // console.log("updateAnnotation=====");
            // console.log(annotation);
            // console.log("data=====");
            // console.log(data);
            if (__indexOf.call(this.annotations, annotation) < 0) {
                console.error(Annotator._t("Trying to update unregistered annotation!"))
            } else {
                $.extend(annotation, data)
            }


            var highlightsMetaData = annotation.highlights;
            var optionJsonData = annotation;
            var quoteAndClassNamesArr =[];
            for(var i =0; i < highlightsMetaData.length; i++){
                quoteAndClassNamesArr.push({"quote":highlightsMetaData[i].innerText,"className":highlightsMetaData[i].offsetParent.className.replace('t',' ')});
            }
            optionJsonData['quoteAndClassNamesArr'] = quoteAndClassNamesArr;
            renderedNewHighlightedText(optionJsonData);
            return $(annotation.highlights).data("annotation", annotation)
        };
        Store.prototype.loadAnnotations = function() {
            return this._apiRequest("read", null, this._onLoadAnnotations)
        };
        Store.prototype._onLoadAnnotations = function(data) {
            for(var _annotatorIndex = 0; _annotatorIndex< data.length; _annotatorIndex++){
                // console.log(data[_annotatorIndex]);
            }
            var a, annotation, annotationMap, newData, _k, _l, _len2, _len3, _ref3;
            if (data == null) {
                data = []
            }
            annotationMap = {};
            _ref3 = this.annotations;
            for (_k = 0, _len2 = _ref3.length; _k < _len2; _k++) {
                a = _ref3[_k];
                annotationMap[a.id] = a
            }
            newData = [];
            for (_l = 0, _len3 = data.length; _l < _len3; _l++) {
                a = data[_l];
                if (annotationMap[a.id]) {
                    annotation = annotationMap[a.id];
                    this.updateAnnotation(annotation, a)
                } else {
                    newData.push(a)
                }
            }
            this.annotations = this.annotations.concat(newData);
            return this.annotator.loadAnnotations(newData.slice())
        };
        Store.prototype.loadAnnotationsFromSearch = function(searchOptions) {
            this._onLoadAnnotationsFromSearch([]);
            //return this._apiRequest("search", searchOptions, this._onLoadAnnotationsFromSearch)
        };
        Store.prototype._onLoadAnnotationsFromSearch = function(data) {
            data = notes;
            console.log("notes=====");
            console.log(notes);
            if (data == null) {
                data = {}
            }
            return this._onLoadAnnotations(data.rows || [])
        };
        Store.prototype.dumpAnnotations = function() {
            var ann, _k, _len2, _ref3, _results;
            _ref3 = this.annotations;
            _results = [];
            for (_k = 0, _len2 = _ref3.length; _k < _len2; _k++) {
                ann = _ref3[_k];
                _results.push(JSON.parse(this._dataFor(ann)))
            }
            return _results
        };
        Store.prototype._apiRequest = function(action, obj, onSuccess) {
            /* Ajax call to send highlighted data to server */
            var id, options, request, url;
            /* variable highlightsMetaData stores the metadata related to the highlighted text */
            id = obj && obj.id;
            url = this._urlFor(action, id);
            options = this._apiRequestOptions(action, obj, onSuccess);

            /* Code to get the classnames of the selected text */
            if(action != 'search' && action != "destroy"){
                if( bookLang != 'English' && bookLang != null && bookLang != 'null'){var highlightsMetaData = obj.highlights;
                    var optionJsonData = JSON.parse(options.data);
                    var quoteAndClassNamesArr =[];
                    for(var i =0; i < highlightsMetaData.length; i++){
                        quoteAndClassNamesArr.push({"quote":highlightsMetaData[i].innerText,"className":highlightsMetaData[i].offsetParent.className.replace('t',' ')});
                    }
                    optionJsonData['quoteAndClassNamesArr'] = quoteAndClassNamesArr;
                    // renderedNewHighlightedText(optionJsonData);
                    options['data'] = JSON.stringify(optionJsonData);
                    // }
                }
            }
            // request = $.ajax('google.com', options);
//            request = $.ajax(url, options);
//            request._id = id;
//            request._action = action;
        var myCustomData = { action: action,data: JSON.parse(options['data'])};
        var event = new CustomEvent('annotationAction', { detail: myCustomData });
            window.parent.document.dispatchEvent(event);
            request = {}
            return request
        };
        Store.prototype._apiRequestOptions = function(action, obj, onSuccess) {
            var data, method, opts;
            method = this._methodFor(action);
            opts = {
                type: method,
                headers: this.element.data("annotator:headers"),
                dataType: "json",
                success: onSuccess || function() {},
                error: this._onError
            };
            if (this.options.emulateHTTP && (method === "PUT" || method === "DELETE")) {
                opts.headers = $.extend(opts.headers, {
                    "X-HTTP-Method-Override": method
                });
                opts.type = "POST"
            }
            if (action === "search") {
                opts = $.extend(opts, {
                    data: obj
                });
                return opts
            }
            data = obj && this._dataFor(obj);
            if (this.options.emulateJSON) {
                opts.data = {
                    json: data
                };
                if (this.options.emulateHTTP) {
                    opts.data._method = method
                }
                return opts
            }
            opts = $.extend(opts, {
                data: data,
                contentType: "application/json; charset=utf-8"
            });
            return opts
        };
        Store.prototype._urlFor = function(action, id) {
            var url;
            url = this.options.prefix != null ? this.options.prefix : "";
            url += this.options.urls[action];
            url = url.replace(/\/:id/, id != null ? "/" + id : "");
            url = url.replace(/:id/, id != null ? id : "");
            return url
        };
        Store.prototype._methodFor = function(action) {
            var table;
            table = {
                create: "POST",
                read: "GET",
                update: "PUT",
                destroy: "DELETE",
                search: "GET"
            };
            return table[action]
        };
        Store.prototype._dataFor = function(annotation) {
            var data, highlights;
            highlights = annotation.highlights;
            delete annotation.highlights;
            $.extend(annotation, this.options.annotationData);
            data = JSON.stringify(annotation);
            if (highlights) {
                annotation.highlights = highlights
            }
            return data
        };
        Store.prototype._onError = function(xhr) {
            var action, message;
            action = xhr._action;
            message = Annotator._t("Sorry we could not ") + action + Annotator._t(" this annotation");
            if (xhr._action === "search") {
                message = Annotator._t("Sorry we could not search the store for annotations")
            } else if (xhr._action === "read" && !xhr._id) {
                message = Annotator._t("Sorry we could not ") + action + Annotator._t(" the annotations from the store")
            }
            switch (xhr.status) {
                case 401:
                    message = Annotator._t("Sorry you are not allowed to ") + action + Annotator._t(" this annotation");
                    break;
                case 404:
                    message = Annotator._t("Sorry we could not connect to the annotations store");
                    break;
                case 500:
                    message = Annotator._t("Sorry something went wrong with the annotation store")
            }
            Annotator.showNotification(message, Annotator.Notification.ERROR);
            return console.error(Annotator._t("API request failed:") + (" '" + xhr.status + "'"))
        };
        return Store
    }(Annotator.Plugin);
    Annotator.Plugin.Permissions = function(_super) {
        __extends(Permissions, _super);
        Permissions.prototype.events = {
            beforeAnnotationCreated: "addFieldsToAnnotation"
        };
        Permissions.prototype.options = {
            showViewPermissionsCheckbox: !0,
            showEditPermissionsCheckbox: !0,
            userId: function(user) {
                return user
            },
            userString: function(user) {
                return user
            },
            userAuthorize: function(action, annotation, user) {
                var token, tokens, _k, _len2;
                if (annotation.permissions) {
                    tokens = annotation.permissions[action] || [];
                    if (tokens.length === 0) {
                        return !0
                    }
                    for (_k = 0, _len2 = tokens.length; _k < _len2; _k++) {
                        token = tokens[_k];
                        if (this.userId(user) === token) {
                            return !0
                        }
                    }
                    return !1
                } else if (annotation.user) {
                    if (user) {
                        return this.userId(user) === this.userId(annotation.user)
                    } else {
                        return !1
                    }
                }
                return !0
            },
            user: "",
            permissions: {
                read: [],
                update: [],
                "delete": [],
                admin: []
            }
        };

        function Permissions(element, options) {
            this._setAuthFromToken = __bind(this._setAuthFromToken, this);
            this.updateViewer = __bind(this.updateViewer, this);
            this.updateAnnotationPermissions = __bind(this.updateAnnotationPermissions, this);
            this.updatePermissionsField = __bind(this.updatePermissionsField, this);
            this.addFieldsToAnnotation = __bind(this.addFieldsToAnnotation, this);
            Permissions.__super__.constructor.apply(this, arguments);
            if (this.options.user) {
                this.setUser(this.options.user);
                delete this.options.user
            }
        }
        Permissions.prototype.pluginInit = function() {
            var createCallback, self, _this = this;
            if (!Annotator.supported()) {
                return
            }
            self = this;
            createCallback = function(method, type) {
                return function(field, annotation) {
                    return self[method].call(self, type, field, annotation)
                }
            };
            if (!this.user && this.annotator.plugins.Auth) {
                this.annotator.plugins.Auth.withToken(this._setAuthFromToken)
            }
            if (this.options.showViewPermissionsCheckbox === !0) {
                this.annotator.editor.addField({
                    type: "checkbox",
                    label: Annotator._t("Allow anyone to <strong>view</strong> this annotation"),
                    load: createCallback("updatePermissionsField", "read"),
                    submit: createCallback("updateAnnotationPermissions", "read")
                })
            }
            if (this.options.showEditPermissionsCheckbox === !0) {
                this.annotator.editor.addField({
                    type: "checkbox",
                    label: Annotator._t("Allow anyone to <strong>edit</strong> this annotation"),
                    load: createCallback("updatePermissionsField", "update"),
                    submit: createCallback("updateAnnotationPermissions", "update")
                })
            }
            this.annotator.viewer.addField({
                load: this.updateViewer
            });
            if (this.annotator.plugins.Filter) {
                return this.annotator.plugins.Filter.addFilter({
                    label: Annotator._t("User"),
                    property: "user",
                    isFiltered: function(input, user) {
                        var keyword, _k, _len2, _ref3;
                        user = _this.options.userString(user);
                        if (!(input && user)) {
                            return !1
                        }
                        _ref3 = input.split(/\s*/);
                        for (_k = 0, _len2 = _ref3.length; _k < _len2; _k++) {
                            keyword = _ref3[_k];
                            if (user.indexOf(keyword) === -1) {
                                return !1
                            }
                        }
                        return !0
                    }
                })
            }
        };
        Permissions.prototype.setUser = function(user) {
            return this.user = user
        };
        Permissions.prototype.addFieldsToAnnotation = function(annotation) {
            if (annotation) {
                annotation.permissions = $.extend(!0, {}, this.options.permissions);
                if (this.user) {
                    return annotation.user = this.user
                }
            }
        };
        Permissions.prototype.authorize = function(action, annotation, user) {
            if (user === void 0) {
                user = this.user
            }
            if (this.options.userAuthorize) {
                return this.options.userAuthorize.call(this.options, action, annotation, user)
            } else {
                return !0
            }
        };
        Permissions.prototype.updatePermissionsField = function(action, field, annotation) {
            var input;
            field = $(field).show();
            input = field.find("input").removeAttr("disabled");
            if (!this.authorize("admin", annotation)) {
                field.hide()
            }
            if (this.authorize(action, annotation || {}, null)) {
                return input.attr("checked", "checked")
            } else {
                return input.removeAttr("checked")
            }
        };
        Permissions.prototype.updateAnnotationPermissions = function(type, field, annotation) {
            var dataKey;
            if (!annotation.permissions) {
                annotation.permissions = $.extend(!0, {}, this.options.permissions)
            }
            dataKey = type + "-permissions";
            if ($(field).find("input").is(":checked")) {
                return annotation.permissions[type] = []
            } else {
                return annotation.permissions[type] = [this.options.userId(this.user)]
            }
        };
        Permissions.prototype.updateViewer = function(field, annotation, controls) {
            var user, username;
            field = $(field);
            username = this.options.userString(annotation.user);
            if (annotation.user && username && typeof username === "string") {
                user = Annotator.Util.escape(this.options.userString(annotation.user));
                field.html(user).addClass("annotator-user")
            } else {
                field.remove()
            }
            if (controls) {
                if (!this.authorize("update", annotation)) {
                    controls.hideEdit()
                }
                if (!this.authorize("delete", annotation)) {
                    return controls.hideDelete()
                }
            }
        };
        Permissions.prototype._setAuthFromToken = function(token) {
            return this.setUser(token.userId)
        };
        return Permissions
    }(Annotator.Plugin);
    Annotator.Plugin.AnnotateItPermissions = function(_super) {
        __extends(AnnotateItPermissions, _super);

        function AnnotateItPermissions() {
            this._setAuthFromToken = __bind(this._setAuthFromToken, this);
            this.updateAnnotationPermissions = __bind(this.updateAnnotationPermissions, this);
            this.updatePermissionsField = __bind(this.updatePermissionsField, this);
            this.addFieldsToAnnotation = __bind(this.addFieldsToAnnotation, this);
            _ref3 = AnnotateItPermissions.__super__.constructor.apply(this, arguments);
            return _ref3
        }
        AnnotateItPermissions.prototype.options = {
            showViewPermissionsCheckbox: !0,
            showEditPermissionsCheckbox: !0,
            groups: {
                world: "group:__world__",
                authenticated: "group:__authenticated__",
                consumer: "group:__consumer__"
            },
            userId: function(user) {
                return user.userId
            },
            userString: function(user) {
                return user.userId
            },
            userAuthorize: function(action, annotation, user) {
                var action_field, permissions, _ref4, _ref5, _ref6, _ref7;
                permissions = annotation.permissions || {};
                action_field = permissions[action] || [];
                if (_ref4 = this.groups.world, __indexOf.call(action_field, _ref4) >= 0) {
                    return !0
                } else if (user != null && user.userId != null && user.consumerKey != null) {
                    if (user.userId === annotation.user && user.consumerKey === annotation.consumer) {
                        return !0
                    } else if (_ref5 = this.groups.authenticated, __indexOf.call(action_field, _ref5) >= 0) {
                        return !0
                    } else if (user.consumerKey === annotation.consumer && (_ref6 = this.groups.consumer, __indexOf.call(action_field, _ref6) >= 0)) {
                        return !0
                    } else if (user.consumerKey === annotation.consumer && (_ref7 = user.userId, __indexOf.call(action_field, _ref7) >= 0)) {
                        return !0
                    } else if (user.consumerKey === annotation.consumer && user.admin) {
                        return !0
                    } else {
                        return !1
                    }
                } else {
                    return !1
                }
            },
            permissions: {
                read: ["group:__world__"],
                update: [],
                "delete": [],
                admin: []
            }
        };
        AnnotateItPermissions.prototype.addFieldsToAnnotation = function(annotation) {
            if (annotation) {
                annotation.permissions = this.options.permissions;
                if (this.user) {
                    annotation.user = this.user.userId;
                    return annotation.consumer = this.user.consumerKey
                }
            }
        };
        AnnotateItPermissions.prototype.updatePermissionsField = function(action, field, annotation) {
            var input;
            field = $(field).show();
            input = field.find("input").removeAttr("disabled");
            if (!this.authorize("admin", annotation)) {
                field.hide()
            }
            if (this.user && this.authorize(action, annotation || {}, {
                userId: "__nonexistentuser__",
                consumerKey: this.user.consumerKey
            })) {
                return input.attr("checked", "checked")
            } else {
                return input.removeAttr("checked")
            }
        };
        AnnotateItPermissions.prototype.updateAnnotationPermissions = function(type, field, annotation) {
            var dataKey;
            if (!annotation.permissions) {
                annotation.permissions = this.options.permissions
            }
            dataKey = type + "-permissions";
            if ($(field).find("input").is(":checked")) {
                return annotation.permissions[type] = [type === "read" ? this.options.groups.world : this.options.groups.consumer]
            } else {
                return annotation.permissions[type] = []
            }
        };
        AnnotateItPermissions.prototype._setAuthFromToken = function(token) {
            return this.setUser(token)
        };
        return AnnotateItPermissions
    }(Annotator.Plugin.Permissions);
    Annotator.Plugin.Filter = function(_super) {
        __extends(Filter, _super);
        Filter.prototype.events = {
            ".annotator-filter-property input focus": "_onFilterFocus",
            ".annotator-filter-property input blur": "_onFilterBlur",
            ".annotator-filter-property input keyup": "_onFilterKeyup",
            ".annotator-filter-previous click": "_onPreviousClick",
            ".annotator-filter-next click": "_onNextClick",
            ".annotator-filter-clear click": "_onClearClick"
        };
        Filter.prototype.classes = {
            active: "annotator-filter-active",
            hl: {
                hide: "annotator-hl-filtered",
                active: "annotator-hl-active"
            }
        };
        Filter.prototype.html = {
            element: '<div class="annotator-filter">\n  <strong>' + Annotator._t("Navigate:") + '</strong>\n<span class="annotator-filter-navigation">\n  <button class="annotator-filter-previous">' + Annotator._t("Previous") + '</button>\n<button class="annotator-filter-next">' + Annotator._t("Next") + "</button>\n</span>\n<strong>" + Annotator._t("Filter by:") + "</strong>\n</div>",
            filter: '<span class="annotator-filter-property">\n  <label></label>\n  <input/>\n  <button class="annotator-filter-clear">' + Annotator._t("Clear") + "</button>\n</span>"
        };
        Filter.prototype.options = {
            appendTo: "body",
            filters: [],
            addAnnotationFilter: !0,
            isFiltered: function(input, property) {
                var keyword, _k, _len2, _ref4;
                if (!(input && property)) {
                    return !1
                }
                _ref4 = input.split(/\s+/);
                for (_k = 0, _len2 = _ref4.length; _k < _len2; _k++) {
                    keyword = _ref4[_k];
                    if (property.indexOf(keyword) === -1) {
                        return !1
                    }
                }
                return !0
            }
        };

        function Filter(element, options) {
            this._onPreviousClick = __bind(this._onPreviousClick, this);
            this._onNextClick = __bind(this._onNextClick, this);
            this._onFilterKeyup = __bind(this._onFilterKeyup, this);
            this._onFilterBlur = __bind(this._onFilterBlur, this);
            this._onFilterFocus = __bind(this._onFilterFocus, this);
            this.updateHighlights = __bind(this.updateHighlights, this);
            var _base;
            element = $(this.html.element).appendTo((options != null ? options.appendTo : void 0) || this.options.appendTo);
            Filter.__super__.constructor.call(this, element, options);
            (_base = this.options).filters || (_base.filters = []);
            this.filter = $(this.html.filter);
            this.filters = [];
            this.current = 0
        }
        Filter.prototype.pluginInit = function() {
            var filter, _k, _len2, _ref4;
            _ref4 = this.options.filters;
            for (_k = 0, _len2 = _ref4.length; _k < _len2; _k++) {
                filter = _ref4[_k];
                this.addFilter(filter)
            }
            this.updateHighlights();
            this._setupListeners()._insertSpacer();
            if (this.options.addAnnotationFilter === !0) {
                return this.addFilter({
                    label: Annotator._t("Annotation"),
                    property: "text"
                })
            }
        };
        Filter.prototype.destroy = function() {
            var currentMargin, html;
            Filter.__super__.destroy.apply(this, arguments);
            html = $("html");
            currentMargin = parseInt(html.css("padding-top"), 10) || 0;
            html.css("padding-top", currentMargin - this.element.outerHeight());
            return this.element.remove()
        };
        Filter.prototype._insertSpacer = function() {
            var currentMargin, html;
            html = $("html");
            currentMargin = parseInt(html.css("padding-top"), 10) || 0;
            html.css("padding-top", currentMargin + this.element.outerHeight());
            return this
        };
        Filter.prototype._setupListeners = function() {
            var event, events, _k, _len2;
            events = ["annotationsLoaded", "annotationCreated", "annotationUpdated", "annotationDeleted"];
            for (_k = 0, _len2 = events.length; _k < _len2; _k++) {
                event = events[_k];
                this.annotator.subscribe(event, this.updateHighlights)
            }
            return this
        };
        Filter.prototype.addFilter = function(options) {
            var f, filter;
            filter = $.extend({
                label: "",
                property: "",
                isFiltered: this.options.isFiltered
            }, options);
            if (! function() {
                var _k, _len2, _ref4, _results;
                _ref4 = this.filters;
                _results = [];
                for (_k = 0, _len2 = _ref4.length; _k < _len2; _k++) {
                    f = _ref4[_k];
                    if (f.property === filter.property) {
                        _results.push(f)
                    }
                }
                return _results
            }.call(this).length) {
                filter.id = "annotator-filter-" + filter.property;
                filter.annotations = [];
                filter.element = this.filter.clone().appendTo(this.element);
                filter.element.find("label").html(filter.label).attr("for", filter.id);
                filter.element.find("input").attr({
                    id: filter.id,
                    placeholder: Annotator._t("Filter by ") + filter.label + "…"
                });
                filter.element.find("button").hide();
                filter.element.data("filter", filter);
                this.filters.push(filter)
            }
            return this
        };
        Filter.prototype.updateFilter = function(filter) {
            var annotation, annotations, input, property, _k, _len2, _ref4;
            filter.annotations = [];
            this.updateHighlights();
            this.resetHighlights();
            input = $.trim(filter.element.find("input").val());
            if (input) {
                annotations = this.highlights.map(function() {
                    return $(this).data("annotation")
                });
                _ref4 = $.makeArray(annotations);
                for (_k = 0, _len2 = _ref4.length; _k < _len2; _k++) {
                    annotation = _ref4[_k];
                    property = annotation[filter.property];
                    if (filter.isFiltered(input, property)) {
                        filter.annotations.push(annotation)
                    }
                }
                return this.filterHighlights()
            }
        };
        Filter.prototype.updateHighlights = function() {
            this.highlights = this.annotator.element.find(".annotator-hl:visible");
            return this.filtered = this.highlights.not(this.classes.hl.hide)
        };
        Filter.prototype.filterHighlights = function() {
            var activeFilters, annotation, annotations, filtered, highlights, index, uniques, _k, _len2, _ref4;
            activeFilters = $.grep(this.filters, function(filter) {
                return !!filter.annotations.length
            });
            filtered = ((_ref4 = activeFilters[0]) != null ? _ref4.annotations : void 0) || [];
            if (activeFilters.length > 1) {
                annotations = [];
                $.each(activeFilters, function() {
                    return $.merge(annotations, this.annotations)
                });
                uniques = [];
                filtered = [];
                $.each(annotations, function() {
                    if ($.inArray(this, uniques) === -1) {
                        return uniques.push(this)
                    } else {
                        return filtered.push(this)
                    }
                })
            }
            highlights = this.highlights;
            for (index = _k = 0, _len2 = filtered.length; _k < _len2; index = ++_k) {
                annotation = filtered[index];
                highlights = highlights.not(annotation.highlights)
            }
            highlights.addClass(this.classes.hl.hide);
            this.filtered = this.highlights.not(this.classes.hl.hide);
            return this
        };
        Filter.prototype.resetHighlights = function() {
            this.highlights.removeClass(this.classes.hl.hide);
            this.filtered = this.highlights;
            return this
        };
        Filter.prototype._onFilterFocus = function(event) {
            var input;
            input = $(event.target);
            input.parent().addClass(this.classes.active);
            return input.next("button").show()
        };
        Filter.prototype._onFilterBlur = function(event) {
            var input;
            if (!event.target.value) {
                input = $(event.target);
                input.parent().removeClass(this.classes.active);
                return input.next("button").hide()
            }
        };
        Filter.prototype._onFilterKeyup = function(event) {
            var filter;
            filter = $(event.target).parent().data("filter");
            if (filter) {
                return this.updateFilter(filter)
            }
        };
        Filter.prototype._findNextHighlight = function(previous) {
            var active, annotation, current, index, next, offset, operator, resetOffset;
            if (!this.highlights.length) {
                return this
            }
            offset = previous ? 0 : -1;
            resetOffset = previous ? -1 : 0;
            operator = previous ? "lt" : "gt";
            active = this.highlights.not("." + this.classes.hl.hide);
            current = active.filter("." + this.classes.hl.active);
            if (!current.length) {
                current = active.eq(offset)
            }
            annotation = current.data("annotation");
            index = active.index(current[0]);
            next = active.filter(":" + operator + "(" + index + ")").not(annotation.highlights).eq(resetOffset);
            if (!next.length) {
                next = active.eq(resetOffset)
            }
            return this._scrollToHighlight(next.data("annotation").highlights)
        };
        Filter.prototype._onNextClick = function(event) {
            return this._findNextHighlight()
        };
        Filter.prototype._onPreviousClick = function(event) {
            return this._findNextHighlight(!0)
        };
        Filter.prototype._scrollToHighlight = function(highlight) {
            highlight = $(highlight);
            this.highlights.removeClass(this.classes.hl.active);
            highlight.addClass(this.classes.hl.active);
            return $("html, body").animate({
                scrollTop: highlight.offset().top - (this.element.height() + 20)
            }, 150)
        };
        Filter.prototype._onClearClick = function(event) {
            return $(event.target).prev("input").val("").keyup().blur()
        };
        return Filter
    }(Annotator.Plugin);
    Annotator.Plugin.Markdown = function(_super) {
        __extends(Markdown, _super);
        Markdown.prototype.events = {
            annotationViewerTextField: "updateTextField"
        };

        function Markdown(element, options) {
            this.updateTextField = __bind(this.updateTextField, this);
            if ((typeof Showdown !== "undefined" && Showdown !== null ? Showdown.converter : void 0) != null) {
                Markdown.__super__.constructor.apply(this, arguments);
                this.converter = new Showdown.converter
            } else {
                console.error(Annotator._t("To use the Markdown plugin, you must include Showdown into the page first."))
            }
        }
        Markdown.prototype.updateTextField = function(field, annotation) {
            var text;
            text = Annotator.Util.escape(annotation.text || "");
            return $(field).html(this.convert(text))
        };
        Markdown.prototype.convert = function(text) {
            return this.converter.makeHtml(text)
        };
        return Markdown
    }(Annotator.Plugin);
    Annotator.Plugin.Tags = function(_super) {
        __extends(Tags, _super);

        function Tags() {
            this.setAnnotationTags = __bind(this.setAnnotationTags, this);
            this.updateField = __bind(this.updateField, this);
            _ref4 = Tags.__super__.constructor.apply(this, arguments);
            return _ref4
        }
        Tags.prototype.options = {
            parseTags: function(string) {
                var tags;
                string = $.trim(string);
                tags = [];
                if (string) {
                    tags = string.split(/\s+/)
                }
                return tags
            },
            stringifyTags: function(array) {
                return array.join(" ")
            }
        };
        Tags.prototype.field = null;
        Tags.prototype.input = null;
        Tags.prototype.pluginInit = function() {
            if (!Annotator.supported()) {
                return
            }
            this.field = this.annotator.editor.addField({
                label: Annotator._t("Add some tags here") + "…",
                load: this.updateField,
                submit: this.setAnnotationTags
            });
            this.annotator.viewer.addField({
                load: this.updateViewer
            });
            if (this.annotator.plugins.Filter) {
                this.annotator.plugins.Filter.addFilter({
                    label: Annotator._t("Tag"),
                    property: "tags",
                    isFiltered: Annotator.Plugin.Tags.filterCallback
                })
            }
            return this.input = $(this.field).find(":input")
        };
        Tags.prototype.parseTags = function(string) {
            return this.options.parseTags(string)
        };
        Tags.prototype.stringifyTags = function(array) {
            return this.options.stringifyTags(array)
        };
        Tags.prototype.updateField = function(field, annotation) {
            var value;
            value = "";
            if (annotation.tags) {
                value = this.stringifyTags(annotation.tags)
            }
            return this.input.val(value)
        };
        Tags.prototype.setAnnotationTags = function(field, annotation) {
            return annotation.tags = this.parseTags(this.input.val())
        };
        Tags.prototype.updateViewer = function(field, annotation) {
            field = $(field);
            if (annotation.tags && $.isArray(annotation.tags) && annotation.tags.length) {
                return field.addClass("annotator-tags").html(function() {
                    var string;
                    return string = $.map(annotation.tags, function(tag) {
                        return '<span class="annotator-tag">' + Annotator.Util.escape(tag) + "</span>"
                    }).join(" ")
                })
            } else {
                return field.remove()
            }
        };
        return Tags
    }(Annotator.Plugin);
    Annotator.Plugin.Tags.filterCallback = function(input, tags) {
        var keyword, keywords, matches, tag, _k, _l, _len2, _len3;
        if (tags == null) {
            tags = []
        }
        matches = 0;
        keywords = [];
        if (input) {
            keywords = input.split(/\s+/g);
            for (_k = 0, _len2 = keywords.length; _k < _len2; _k++) {
                keyword = keywords[_k];
                if (tags.length) {
                    for (_l = 0, _len3 = tags.length; _l < _len3; _l++) {
                        tag = tags[_l];
                        if (tag.indexOf(keyword) !== -1) {
                            matches += 1
                        }
                    }
                }
            }
        }
        return matches === keywords.length
    };
    Annotator.prototype.setupPlugins = function(config, options) {
        var name, opts, pluginConfig, plugins, uri, win, _k, _len2, _results;
        if (config == null) {
            config = {}
        }
        if (options == null) {
            options = {}
        }
        win = Annotator.Util.getGlobal();
        plugins = ["Unsupported", "Auth", "Tags", "Filter", "Store", "AnnotateItPermissions"];
        if (win.Showdown) {
            plugins.push("Markdown")
        }
        uri = win.location.href.split(/#|\?/).shift() || "";
        pluginConfig = {
            Tags: {},
            Filter: {
                filters: [{
                    label: Annotator._t("User"),
                    property: "user"
                }, {
                    label: Annotator._t("Tags"),
                    property: "tags"
                }]
            },
            Auth: {
                tokenUrl: config.tokenUrl || "http://annotateit.org/api/token"
            },
            Store: {
                prefix: config.storeUrl || "http://annotateit.org/api",
                annotationData: {
                    uri: uri
                },
                loadFromSearch: {
                    uri: uri
                }
            }
        };
        for (name in options) {
            if (!__hasProp.call(options, name)) continue;
            opts = options[name];
            if (__indexOf.call(plugins, name) < 0) {
                plugins.push(name)
            }
        }
        $.extend(!0, pluginConfig, options);
        _results = [];
        for (_k = 0, _len2 = plugins.length; _k < _len2; _k++) {
            name = plugins[_k];
            if (!(name in pluginConfig) || pluginConfig[name]) {
                _results.push(this.addPlugin(name, pluginConfig[name]))
            } else {
                _results.push(void 0)
            }
        }
        return _results
    }
}.call(this);
function cancelMetatagIos(){
    $('head').prepend('<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=1" />');
}
function saveMetatagIos() {
    $('head').prepend('<meta name="viewport"/>');
}

/*  Annotator Touch Plugin - v1.1.1
 *  Copyright 2012-2015, Compendio <www.compendio.ch>
 *  Released under the MIT license
 *  More Information: https://github.com/aron/annotator.touch.js
 */
(function() {
    var __bind = function(fn, me) {
            return function() {
                return fn.apply(me, arguments)
            }
        },
        __hasProp = {}.hasOwnProperty,
        __extends = function(child, parent) {
            for (var key in parent) {
                if (__hasProp.call(parent, key)) child[key] = parent[key]
            }

            function ctor() {
                this.constructor = child
            }
            ctor.prototype = parent.prototype;
            child.prototype = new ctor;
            child.__super__ = parent.prototype;
            return child
        };
    Annotator.Plugin.Touch = function(_super) {
        var jQuery, _t;
        __extends(Touch, _super);
        _t = Annotator._t;
        jQuery = Annotator.$;
        Touch.states = {
            ON: "on",
            OFF: "off"
        };
        Touch.prototype.template = '<div class="annotator-touch-widget annotator-touch-controls annotator-touch-hide">\n  <div class="d-flex annotator-touch-widget-inner">\n <a class="annotator-button annotator-focus highlight-btn">' + _t("Highlight") + '</a>\n<a class="annotator-button annotator-add annotator-focus">' + _t("Note") + '</a>' +
            // '<a class="annotator-button  annotator-focus">' + _t("Translate") + '</a>' +
            '<a class="annotator-button google-search-btn annotator-focus">' + _t("Ask Doubt") + '</a></div>\n</div>';
        Touch.prototype.classes = {
            hide: "annotator-touch-hide"
        };
        Touch.prototype.options = {
            force: false,
            useHighlighter: false
        };

        function Touch(element, options) {
            this._onDocumentTap = __bind(this._onDocumentTap, this);
            this._onHighlightTap = __bind(this._onHighlightTap, this);
            this._onAdderTap = __bind(this._onAdderTap, this);
            this._onHighlighterTap = __bind(this._onHighlighterTap, this);
            this._onWebSearchTap = __bind(this._onWebSearchTap, this);
            this._onToggleTap = __bind(this._onToggleTap, this);
            this._onSelection = __bind(this._onSelection, this);
            this._watchForSelection = __bind(this._watchForSelection, this);
            Touch.__super__.constructor.apply(this, arguments);
            this.utils = Annotator.Plugin.Touch.utils;
            this.selection = null;
            this.document = jQuery(document)
        }
        Touch.prototype.pluginInit = function() {
            if (!(Annotator.supported() && (this.options.force || Touch.isTouchDevice()))) {
                return
            }
            this._setupControls();
            if (this.options.useHighlighter) {
                this.showControls();
                this.highlighter = new Highlighter({
                    root: this.element[0],
                    prefix: "annotator-selection",
                    enable: false,
                    highlightStyles: true
                })
            }
            this.document.delegate(".annotator-hl,.annotator-hlh", "tap", {
                preventDefault: false
            }, this._onHighlightTap);
            this.subscribe("selection", this._onSelection);
            this._unbindAnnotatorEvents();
            this._setupAnnotatorEvents();
            return this._watchForSelection()
        };
        Touch.prototype.pluginDestroy = function() {
            if (this.controls) {
                this.controls.remove()
            }
            if (this.highlighter) {
                this.highlighter.disable()
            }
            if (this.annotator) {
                return this.annotator.editor.unsubscribe("hide", this._watchForSelection)
            }
        };
        Touch.prototype.startAnnotating = function() {
            if (this.highlighter) {
                this.highlighter.enable()
            }
            this.toggle.attr("data-state", Touch.states.ON);
            this.toggle.html("Stop Annotating");
            return this
        };
        Touch.prototype.stopAnnotating = function() {
            if (this.highlighter) {
                this.highlighter.disable()
            }
            this.toggle.attr("data-state", Touch.states.OFF);
            this.toggle.html("Start Annotating");
            return this
        };
        Touch.prototype.isAnnotating = function() {
            var usingHighlighter;
            usingHighlighter = this.options.useHighlighter;
            return !usingHighlighter || this.toggle.attr("data-state") === Touch.states.ON
        };
        Touch.prototype.showEditor = function(annotation) {
            this.annotator.showEditor(annotation, {});
            this.hideControls();
            return this
        };
        Touch.prototype.showControls = function() {
            this.controls.removeClass(this.classes.hide);
            return this
        };
        Touch.prototype.hideControls = function() {
            if (!this.options.useHighlighter) {
                this.controls.addClass(this.classes.hide)
            }
            return this
        };
        Touch.prototype._setupControls = function() {
            this.annotator.adder.remove();

            this.controls = jQuery(this.template).appendTo("body");
            this.adder = this.controls.find(".annotator-add");
            this.adder.bind("tap", {
                onTapDown: function(event) {
                    return event.stopPropagation()
                }
            }, this._onAdderTap);
            this.highlighter1 = this.controls.find(".highlight-btn");
            this.highlighter1.bind("tap", {
                onTapDown: function(event) {
                    return event.stopPropagation()
                }
            }, this._onHighlighterTap);
            this.webSearch = this.controls.find(".google-search-btn");
            this.webSearch.bind("tap", {
                onTapDown: function(event) {
                    return event.stopPropagation()
                }
            }, this._onWebSearchTap);
            this.toggle = this.controls.find(".annotator-touch-toggle");
            this.toggle.bind({
                tap: this._onToggleTap
            });
            if (!this.options.useHighlighter) {
                return this.toggle.hide()
            }
        };
        Touch.prototype._setupAnnotatorEvents = function() {
            this.editor = new Touch.Editor(this.annotator.editor);
            this.viewer = new Touch.Viewer(this.annotator.viewer);
            this.annotator.editor.on("show", function(_this) {
                return function() {
                    _this._clearWatchForSelection();
                    _this.annotator.onAdderMousedown();
                    if (_this.highlighter) {
                        return _this.highlighter.disable()
                    }
                }
            }(this));
            this.annotator.viewer.on("show", function(_this) {
                return function() {
                    if (_this.highlighter) {
                        return _this.highlighter.disable()
                    }
                }
            }(this));
            this.annotator.editor.on("hide", function(_this) {
                return function() {
                    return _this.utils.nextTick(function() {
                        if (_this.highlighter) {
                            _this.highlighter.enable().deselect()
                        }
                        return _this._watchForSelection()
                    })
                }
            }(this));
            return this.annotator.viewer.on("hide", function(_this) {
                return function() {
                    return _this.utils.nextTick(function() {
                        if (_this.highlighter) {
                            return _this.highlighter.enable().deselect()
                        }
                    })
                }
            }(this))
        };
        Touch.prototype._unbindAnnotatorEvents = function() {
            this.document.unbind({
                mouseup: this.annotator.checkForEndSelection,
                mousedown: this.annotator.checkForStartSelection
            });
            return this.element.unbind("click mousedown mouseover mouseout")
        };
        Touch.prototype._watchForSelection = function() {
            var interval, start, step;
            if (this.timer) {
                return
            }
            interval = Touch.isAndroid() ? 300 : 1e3 / 60;
            start = (new Date).getTime();
            step = function(_this) {
                return function() {
                    var progress;
                    progress = (new Date).getTime() - start;
                    if (progress > interval) {
                        start = (new Date).getTime();
                        _this._checkSelection()
                    }
                    return _this.timer = _this.utils.requestAnimationFrame.call(window, step)
                }
            }(this);
            return step()
        };
        Touch.prototype._clearWatchForSelection = function() {
            this.utils.cancelAnimationFrame.call(window, this.timer);
            return this.timer = null
        };
        Touch.prototype._checkSelection = function() {
            var previous, selection, string;
            selection = window.getSelection();
            previous = this.selectionString;
            string = jQuery.trim(selection + "");
            if (selection.rangeCount && string !== this.selectionString) {
                this.range = selection.getRangeAt(0);
                this.selectionString = string
            }
            if (selection.rangeCount === 0 || this.range && this.range.collapsed) {
                this.range = null;
                this.selectionString = ""
            }
            if (this.selectionString !== previous) {
                return this.publish("selection", [this.range, this])
            }
        };
        Touch.prototype._onSelection = function(event) {
            if(event !=null && event !=undefined) {
                var br = event.getBoundingClientRect();
                console.log(br);
                console.log('top',br.top);
                console.log(br.left);
                $('.annotator-touch-controls').css({
                    top:(br.top)-50+'px'
                });
                if($(window).width()>767) {
                    if ((br.left) > 400) {
                        $('.annotator-touch-controls,.annotator-touch-widget').css({
                            left: (br.left) - 340 + 'px'
                        });
                    }
                    if ((br.left) < 400) {
                        $('.annotator-touch-controls,.annotator-touch-widget').css({
                            left: (br.left) + 'px'
                        });
                    }
                }
                // if($(window).width()>767 || $(window).width()<992){
                //     $('.annotator-touch-controls').css({
                //         left:(br.left) - 300 +'px'
                //     });
                // }
            }
            if (this.isAnnotating() && this.range && this._isValidSelection(this.range)) {
                this.adder.removeAttr("disabled");
                return this.showControls()
            } else {
                this.adder.attr("disabled", "");
                return this.hideControls()
            }
            if (this.isAnnotating() && this.range && this._isValidSelection(this.range)) {
                this.highlighter1.removeAttr("disabled");
                return this.showControls()
            } else {
                this.highlighter1.attr("disabled", "");
                return this.hideControls()
            }
            if (this.isAnnotating() && this.range && this._isValidSelection(this.range)) {
                this.webSearch.removeAttr("disabled");
                return this.showControls()
            } else {
                this.webSearch.attr("disabled", "");
                return this.hideControls()
            }
        };
        Touch.prototype._isValidSelection = function(range) {
            var inElement, isStartOffsetValid, isValidEnd, isValidStart;
            inElement = function(node) {
                return jQuery(node).parents(".annotator-wrapper").length
            };
            isStartOffsetValid = range.startOffset < range.startContainer.length;
            isValidStart = isStartOffsetValid && inElement(range.startContainer);
            isValidEnd = range.endOffset > 0 && inElement(range.endContainer);
            return isValidStart || isValidEnd
        };
        Touch.prototype._onToggleTap = function(event) {
            event.preventDefault();
            if (this.isAnnotating()) {
                return this.stopAnnotating()
            } else {
                return this.startAnnotating()
            }
        };
        Touch.prototype._onAdderTap = function(event) {
            var browserRange, onAnnotationCreated, range;
            event.preventDefault();
            if (this.range) {
                browserRange = new Annotator.Range.BrowserRange(this.range);
                range = browserRange.normalize().limit(this.element[0]);
                if (range && !this.annotator.isAnnotator(range.commonAncestor)) {
                    onAnnotationCreated = function(_this) {
                        return function(annotation) {
                            _this.annotator.unsubscribe("beforeAnnotationCreated", onAnnotationCreated);
                            annotation.quote = range.toString();
                            return annotation.ranges = [range]
                        }
                    }(this);
                    this.annotator.subscribe("beforeAnnotationCreated", onAnnotationCreated);
                    return this.annotator.onAdderClick(event)
                }
            }
        };
        Touch.prototype._onHighlighterTap = function(event) {
            var browserRange, onAnnotationCreated, range;
            event.preventDefault();
            if (this.range) {
                browserRange = new Annotator.Range.BrowserRange(this.range);
                range = browserRange.normalize().limit(this.element[0]);
                if (range && !this.annotator.isAnnotator(range.commonAncestor)) {
                    onAnnotationCreated = function(_this) {
                        return function(annotation) {
                            _this.annotator.unsubscribe("beforeAnnotationCreated", onAnnotationCreated);
                            annotation.quote = range.toString();
                            return annotation.ranges = [range]
                        }
                    }(this);
                    this.annotator.subscribe("beforeAnnotationCreated", onAnnotationCreated);
                    return this.annotator.onHighlighterClick(event)
                }
            }
        };
        Touch.prototype._onWebSearchTap = function(event) {
            var browserRange, onAnnotationCreated, range;
            event.preventDefault();
            if (this.range) {
                browserRange = new Annotator.Range.BrowserRange(this.range);
                range = browserRange.normalize().limit(this.element[0]);
                if (range && !this.annotator.isAnnotator(range.commonAncestor)) {
                    onAnnotationCreated = function(_this) {
                        return function(annotation) {
                            _this.annotator.unsubscribe("beforeAnnotationCreated", onAnnotationCreated);
                            annotation.quote = range.toString();
                            return annotation.ranges = [range]
                        }
                    }(this);
                    this.annotator.subscribe("beforeAnnotationCreated", onAnnotationCreated);
                    return this.annotator.onGoogleSearchClicked(event);
                }
            }
        };
        Touch.prototype._onHighlightTap = function(event) {
            var clickable, original;
            clickable = jQuery(event.currentTarget).parents().filter(function() {
                return jQuery(this).is("a, [data-annotator-clickable]")
            });
            if (clickable.length) {
                return
            }
            if (jQuery.contains(this.element[0], event.currentTarget)) {
                original = event.originalEvent;
                if (original && original.touches) {
                    event.pageX = original.touches[0].pageX;
                    event.pageY = original.touches[0].pageY
                }
                if (this.annotator.viewer.isShown()) {
                    this.annotator.viewer.hide()
                }
                this.annotator.onHighlightMouseover(event);
                this.document.unbind("tap", this._onDocumentTap);
                return this.document.bind("tap", {
                    preventDefault: false
                }, this._onDocumentTap)
            }
        };
        Touch.prototype._onDocumentTap = function(event) {
            if (!this.annotator.isAnnotator(event.target)) {
                this.annotator.viewer.hide()
            }
            if (!this.annotator.viewer.isShown()) {
                return this.document.unbind("tap", this._onDocumentTap)
            }
        };
        Touch.isTouchDevice = function() {
            return "ontouchstart" in window || window.DocumentTouch && document instanceof DocumentTouch
        };
        Touch.isAndroid = function() {
            return /Android/i.test(window.navigator.userAgent)
        };
        return Touch
    }(Annotator.Plugin);
    Annotator.Plugin.Touch.Editor = function(_super) {
        var Touch, jQuery, _t;
        __extends(Editor, _super);
        _t = Annotator._t;
        jQuery = Annotator.$;
        Touch = Annotator.Plugin.Touch;
        Editor.prototype.events = {
            click: "_onOverlayTap",
            ".annotator-save tap": "_onSubmit",
            ".annotator-cancel tap": "_onCancel",
            ".annotator-quote-toggle tap": "_onExpandTap"
        };
        Editor.prototype.classes = {
            expand: "annotator-touch-expand"
        };
        Editor.prototype.templates = {
            quote: '<button class="annotator-quote-toggle">' + _t("expand") + '</button>\n<span class="quote"></span>'
        };

        function Editor(editor, options) {
            this.editor = editor;
            this._onOverlayTap = __bind(this._onOverlayTap, this);
            this._onCancel = __bind(this._onCancel, this);
            this._onSubmit = __bind(this._onSubmit, this);
            this._onExpandTap = __bind(this._onExpandTap, this);
            this._triggerAndroidRedraw = __bind(this._triggerAndroidRedraw, this);
            Editor.__super__.constructor.call(this, this.editor.element[0], options);
            this.element.addClass("annotator-touch-editor");
            this.element.wrapInner('<div class="annotator-touch-widget" />');
            this.element.find("form").addClass("annotator-touch-widget-inner");
            this.element.find(".annotator-controls a").addClass("annotator-button");
            this.element.undelegate("textarea", "keydown");
            this.on("hide", function(_this) {
                return function() {
                    return _this.element.find(":focus").blur()
                }
            }(this));
            this._setupQuoteField();
            this._setupAndroidRedrawHack()
        }
        Editor.prototype.showQuote = function() {
            this.quote.addClass(this.classes.expand);
            this.quote.find("button").text(_t("Collapse"));
            return this
        };
        Editor.prototype.hideQuote = function() {
            this.quote.removeClass(this.classes.expand);
            this.quote.find("button").text(_t("Expand"));
            return this
        };
        Editor.prototype.isQuoteHidden = function() {
            return !this.quote.hasClass(this.classes.expand)
        };
        Editor.prototype._setupQuoteField = function() {
            this.quote = jQuery(this.editor.addField({
                id: "quote",
                load: function(_this) {
                    return function(field, annotation) {
                        _this.hideQuote();
                        _this.quote.find("span").html(Annotator.Util.escape(annotation.quote || ""));
                        return _this.quote.find("button").toggle(_this._isTruncated())
                    }
                }(this)
            }));
            this.quote.empty().addClass("annotator-item-quote");
            return this.quote.append(this.templates.quote)
        };
        Editor.prototype._setupAndroidRedrawHack = function() {
            var check, timer;
            if (Touch.isAndroid()) {
                timer = null;
                check = function(_this) {
                    return function() {
                        timer = null;
                        return _this._triggerAndroidRedraw()
                    }
                }(this);
                return jQuery(window).bind("scroll", function() {
                    if (!timer) {
                        return timer = setTimeout(check, 100)
                    }
                })
            }
        };
        Editor.prototype._triggerAndroidRedraw = function() {
            if (!this._input) {
                this._input = this.element.find(":input:first")
            }
            if (!this._default) {
                this._default = parseFloat(this._input.css("padding-top"))
            }
            this._multiplier = (this._multiplier || 1) * -1;
            this._input[0].style.paddingTop = this._default + this._multiplier + "px";
            return this._input[0].style.paddingTop = this._default - this._multiplier + "px"
        };
        Editor.prototype._isTruncated = function() {
            var expandedHeight, isHidden, truncatedHeight;
            isHidden = this.isQuoteHidden();
            if (!isHidden) {
                this.hideQuote()
            }
            truncatedHeight = this.quote.height();
            this.showQuote();
            expandedHeight = this.quote.height();
            if (isHidden) {
                this.hideQuote()
            } else {
                this.showQuote()
            }
            return expandedHeight > truncatedHeight
        };
        Editor.prototype._onExpandTap = function(event) {
            event.preventDefault();
            event.stopPropagation();
            if (this.isQuoteHidden()) {
                return this.showQuote()
            } else {
                return this.hideQuote()
            }
        };
        Editor.prototype._onSubmit = function(event) {
            event.preventDefault();
            return this.editor.submit()
        };
        Editor.prototype._onCancel = function(event) {
            event.preventDefault();
            return this.editor.hide()
        };
        Editor.prototype._onOverlayTap = function(event) {
            if (event.target === this.element[0]) {
                return this.editor.hide()
            }
        };
        return Editor
    }(Annotator.Delegator);
    jQuery.event.special.tap = {
        add: function(eventHandler) {
            var context, data, onTapEnd, onTapStart;
            data = eventHandler.data = eventHandler.data || {};
            context = this;
            onTapStart = function(event) {
                if (data.preventDefault !== false) {
                    event.preventDefault()
                }
                if (data.onTapDown) {
                    data.onTapDown.apply(this, arguments)
                }
                data.event = event;
                data.touched = setTimeout(function() {
                    return data.touched = null
                }, data.timeout || 300);
                return jQuery(document).bind({
                    touchend: onTapEnd,
                    mouseup: onTapEnd
                })
            };
            onTapEnd = function(event) {
                var handler;
                if (data.touched != null) {
                    clearTimeout(data.touched);
                    if (event.target === context || jQuery.contains(context, event.target)) {
                        handler = eventHandler.origHandler || eventHandler.handler;
                        handler.call(this, data.event)
                    }
                    data.touched = null
                }
                if (data.onTapUp) {
                    data.onTapUp.apply(this, arguments)
                }
                return jQuery(document).unbind({
                    touchstart: onTapEnd,
                    mousedown: onTapEnd
                })
            };
            data.tapHandlers = {
                touchstart: onTapStart,
                mousedown: onTapStart
            };
            if (eventHandler.selector) {
                return jQuery(context).delegate(eventHandler.selector, data.tapHandlers)
            } else {
                return jQuery(context).bind(data.tapHandlers)
            }
        },
        remove: function(eventHandler) {
            return jQuery(this).unbind(eventHandler.data.tapHandlers)
        }
    };
    Annotator.Delegator.natives.push("touchstart", "touchmove", "touchend", "tap");
    Annotator.Plugin.Touch.utils = function() {
        var cancelAnimationFrame, lastTime, prefix, requestAnimationFrame, vendors, _i, _len;
        vendors = ["ms", "moz", "webkit", "o"];
        requestAnimationFrame = window.requestAnimationFrame;
        cancelAnimationFrame = window.cancelAnimationFrame;
        for (_i = 0, _len = vendors.length; _i < _len; _i++) {
            prefix = vendors[_i];
            if (!!requestAnimationFrame) {
                continue
            }
            requestAnimationFrame = window["" + prefix + "RequestAnimationFrame"];
            cancelAnimationFrame = window["" + prefix + "CancelAnimationFrame"] || window["" + prefix + "CancelRequestAnimationFrame"]
        }
        if (!requestAnimationFrame) {
            lastTime = 0;
            requestAnimationFrame = function(callback, element) {
                var currTime, timeToCall;
                currTime = (new Date).getTime();
                timeToCall = Math.max(0, 16 - (currTime - lastTime));
                lastTime = currTime + timeToCall;
                return window.setTimeout(function() {
                    return callback(currTime + timeToCall)
                }, timeToCall)
            }
        }
        if (!cancelAnimationFrame) {
            cancelAnimationFrame = function(id) {
                return clearTimeout(id)
            }
        }
        return {
            requestAnimationFrame: requestAnimationFrame,
            cancelAnimationFrame: cancelAnimationFrame,
            nextTick: function(fn) {
                return setTimeout(fn, 0)
            }
        }
    }();
    Annotator.Plugin.Touch.Viewer = function(_super) {
        var jQuery;
        __extends(Viewer, _super);
        jQuery = Annotator.$;
        Viewer.prototype.events = {
            ".annotator-item tap": "_onTap",
            ".annotator-edit tap": "_onEdit",
            ".annotator-delete tap": "_onDelete"
        };

        function Viewer(viewer, options) {
            this.viewer = viewer;
            this._onLoad = __bind(this._onLoad, this);
            Viewer.__super__.constructor.call(this, this.viewer.element[0], options);
            this.element.unbind("click");
            this.element.addClass("annotator-touch-widget annotator-touch-viewer");
            this.on("load", this._onLoad)
        }
        Viewer.prototype.hideAllControls = function() {
            this.element.find(".annotator-item").removeClass(this.viewer.classes.showControls);
            return this
        };
        Viewer.prototype._onLoad = function() {
            var controls;
            controls = this.element.find(".annotator-controls");
            controls.toggleClass("annotator-controls annotator-touch-controls");
            return controls.find("button").addClass("annotator-button")
        };
        Viewer.prototype._onTap = function(event) {
            var isVisible, target;
            target = jQuery(event.currentTarget);
            isVisible = target.hasClass(this.viewer.classes.showControls);
            this.hideAllControls();
            if (!isVisible) {
                return target.addClass(this.viewer.classes.showControls)
            }
        };
        Viewer.prototype._onEdit = function(event) {
            event.preventDefault();
            return this.viewer.onEditClick(event)
        };
        Viewer.prototype._onDelete = function(event) {
            event.preventDefault();
            return this.viewer.onDeleteClick(event)
        };
        return Viewer
    }(Annotator.Delegator)
}).call(this);


// <style>
// .annotator-gl {
//     background-color: rgba(195, 183, 215, 0.83);
// }
// #htmlContent .annotator-gl {
//     background-color: rgba(195, 183, 215, 0.83);
// }
// </style>
// <%  if(!("sage".equals(session['entryController']))) { %>
// <style>
// .modal-dialog-centered {
//         display: -ms-flexbox;
//         display: flex;
//         -ms-flex-align: center;
//         align-items: center;
//         justify-content: center;
//     }
// .modal.show .modal-dialog {
//         -webkit-transform: none;
//         transform: none;
//     }
// .modal.fade .modal-dialog {
//         transition: -webkit-transform .3s ease-out;
//         transition: transform .3s ease-out;
//         transition: transform .3s ease-out,-webkit-transform .3s ease-out;
//         -webkit-transform: translate(0,-50px);
//         transform: translate(0,-50px);
//     }
// .modal-content{
//         width: 100%;
//     }
// @media (min-width: 576px) {
//     .modal-dialog-centered {
//             min-height: calc(100% - 3.5rem);
//         }
//     }
// @media (min-width: 576px) {
//     .modal-dialog {
//             /*max-width: 500px;*/
//             margin: 1.75rem auto;
//         }
//     }
// </style>
//     <%}%>
    function googleTranslateElementInit() {
        new google.translate.TranslateElement({pageLanguage: 'en'}, 'google_translate_element');
    }

    // <script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>

//     <div class="modal" id="removePhone">
//     <div class="modal-dialog modal-dialog-centered modal-sm">
//     <div class="modal-content">
//
//     <!-- Modal Header -->
// <div class="modal-header">
//     <h4 class="modal-title">Translate</h4>
//     <button type="button" class="close" data-dismiss="modal" style='margin-top: -26px;'>&times;</button>
// </div>
//
// <!-- Modal body -->
// <div class="modal-body text-center">
//     <div >Original Text :<h4 id="originalText"></h4></div><br>
// <form>
// <div class="form-group">
//     <label for="languageList" style="display: block;">Translate To</label>
// <select onchange='languageSelected(event)' class="form-control" id="languageList">
//
//     </select>
//     </div>
//     </form>
//     <div >Translated Text :<h4 id="translatedText"></h4></div>
// </div>
//
// <!-- Modal footer -->
// <div class="modal-footer">
//
//     </div>
//
//     </div>
//     </div>
//     </div>

    // <script type="text/javascript">
    function openModal(username){
        oldUsername = username
        $('#removePhone').modal('show');

    }
var annotation;
var resIdVal;

Annotator.Plugin.StoreLogger = function (element) {
    return {
        pluginInit: function () {
            this.annotator
                .subscribe("annotationCreated", function (annotation) {
                    setTimeout(function () {
                        // %{--<g:remoteFunction controller="wonderpublish" action="annotateSearch"  onSuccess='renderCreatedNotes(data);' params="'uri='+resIdVal+'&bookId=${params.bookId}'" />--}%
                    }, 100);
                })
                .subscribe("annotationUpdated", function (annotation) {
                    setTimeout(function () {
                        // %{--<g:remoteFunction controller="wonderpublish" action="annotateSearch"  onSuccess='renderCreatedNotes(data);' params="'uri='+resIdVal+'&bookId=${params.bookId}'" />--}%
                    }, 100);
                })
                .subscribe("annotationDeleted", function (annotation) {
                    setTimeout(function () {
                        // %{--<g:remoteFunction controller="wonderpublish" action="annotateSearch"  onSuccess='renderCreatedNotes(data);' params="'uri='+resIdVal+'&bookId=${params.bookId}'" />--}%
                    }, 100);
                });
        }
    }
};

function loadAnnotator(resId,_highlightData) {
    notes = _highlightData;
    if (annotation !== undefined) {
        annotation.annotator('destroy');
    }

    annotation = $('body').annotator();
    resIdVal = resId;

    annotation.annotator('addPlugin', 'Store', {
        // The endpoint of the store on your server.
        prefix: 'http://localhost:8080' + '/wonderpublish',

        // Attach the uri of the current page to all annotations to allow search.
        annotationData: {
            'uri': resId,
            'bookId': "${params.bookId}"
        },

        urls: {
            // These are the default URLs.
            create: '/annotateSave',
            update: '/annotateUpdate/:id',
            destroy: '/annotateDestroy/:id',
            search: '/annotateSearch'
        },

        // This will perform a "search" action when the plugin loads. Will
        // request the last 20 annotations for the current url.
        // eg. /store/endpoint/search?limit=20&uri=http://this/document/only
        loadFromSearch: {
            'limit': 100,
            'all_fields': 1,
            'uri': resId,
            'bookId': "${params.bookId}"
        },

        showViewPermissionsCheckbox: true,
        showEditPermissionsCheckbox: true
    });

    annotation.annotator('addPlugin', 'Tags');
    annotation.annotator('addPlugin', 'StoreLogger');
    annotation.annotator("addPlugin", "Touch");
//    console.log(annotation.annotator().Store);
}

function findPos(obj) {
    var curtop = 0;
    if (obj.offsetParent) {
        do {
            curtop += obj.offsetTop;
        } while (obj = obj.offsetParent);
        return [curtop];
    }
}

function onclickRenderedNotes(index) {

    // $('#overlay').addClass('d-none');
    // $('.export-notes').addClass('d-none');
    // $('#notesMenu').removeClass('active');

    $(notes[index].highlights)[notes[index].highlights.length - 1].setAttribute('id', 'content' + notes[index].id);
    // $('html, body').animate({
    //     scrollTop: $("#"+"content"+notes[index].id).offset().top
    // }, 2000);
    // location.href="#";
    // location.href="#"+'content'+notes[index].id;
    document.getElementById('content' + notes[index].id).scrollIntoView(false);
    // alert(notes[index].highlights[notes[index].highlights.length-1].innerText);
    // window.find(notes[index].highlights[notes[index].highlights.length-1].innerText);
    // window.scroll(0,findPos(document.getElementById('content'+notes[index].id)));
}
var notes;
var notesToExport;

function renderCreatedNotes(data) {
    notes = data;
//     console.log("renderCreatedNotes========")
//     console.log(notes)
    var htmlStr = '';
    var userSelection = '';
    var userComment = '';
    if (data.length > 0) {
        for (var i = 0; i < notes.length; i++) {
            var highlightsMetaData = notes[i].highlights;
            var quoteAndClassNamesArr = [];
            for (var q = 0; q < highlightsMetaData.length; q++) {
                if (highlightsMetaData[q].offsetParent != null && highlightsMetaData[q].offsetParent != undefined) {
                    quoteAndClassNamesArr.push({
                        "quote": highlightsMetaData[q].innerText,
                        "className": highlightsMetaData[q].offsetParent.className.replace('t', ' ')
                    });
                } else {
                    quoteAndClassNamesArr.push({
                        "quote": highlightsMetaData[q].innerText,
                        "className": ""
                    });
                }
            }
            userSelection = notes[i].quote;
            userComment = notes[i].text;
            // console.log(notes[i]);
            // var scrollElement = JSON.stringify(notes[i]);
            if (userComment != null) {
                htmlStr += "<li onclick='onclickRenderedNotes(" + i + ")' class='notes-list-item' id='" + notes[i].id + "'>" +
                    "<div class='notes-list-item-indicator'></div>" +
                    "<div class='individual-note-selection' style='display: none;'>" +
                    "<label class='not-active'>" +
                    "<input type='checkbox' name='chapter' value='Chapter'  id='notescheckbox" + i + "'>" +
                    " <span class='checkmark'></span>\n" +
                    "</label>" +
                    "</div>" +
                    "<div class='notes-created-by-user'>" +
                    " <div >";
                for (var k = 0; k < quoteAndClassNamesArr.length; k++) {
                    htmlStr += "<span style='font-size: 16px; padding-right: 1px' class='note-of-user " + quoteAndClassNamesArr[k].className + "' id='notes" + i + "'>" + quoteAndClassNamesArr[k].quote + "</span>";
                }
                htmlStr += "<p class='comment-by-user'>" + userComment + "</p>" +
                    "</div>" +
                    "</div>" +
                    "</li>";
            } else {
                htmlStr += "<li onclick='onclickRenderedNotes(" + i + ")' class='notes-list-item' id='" + notes[i].id + "'>" +
                    "<div class='notes-list-item-indicator'></div>" +
                    "<div class='individual-note-selection' style='display: none;'>" +
                    "<label class='not-active'>" +
                    "<input type='checkbox' name='chapter' value='Chapter'  id='notescheckbox" + i + "'>" +
                    " <span class='checkmark'></span>\n" +
                    "</label>" +
                    "</div>" +
                    "<div class='notes-created-by-user'>" +
                    " <div >";
                for (var k = 0; k < quoteAndClassNamesArr.length; k++) {
                    htmlStr += "<span  style='font-size: 16px; padding-right: 1px' class='note-of-user " + quoteAndClassNamesArr[k].className + "' id='notes" + i + "'>" + quoteAndClassNamesArr[k].quote + "</span >";
                }
                htmlStr += "</div> " +
                    "</div>" +
                    "</li>";
            }
        }
        htmlStr += "<div class='export-btn-wrapper' style='display: none;'>" +
            "<a href='javascript:exportToStudySet();' class='export-notes-btn waves-effect'>" + "Export" + "</a>" +
            "</div>";
    } else {
        htmlStr += "<div class='no-notes-created' id='no-notes-created'>" + "No notes created yet." + "</div>";
    }
    if (document.getElementById('user-notes') != null && document.getElementById('user-notes') != null) document.getElementById('user-notes').innerHTML = htmlStr;
}

function renderedNewHighlightedText(data) {
//    console.log("renderedNewHighlightedText====");
//    console.log(data);
    notes.push(data);
    var index = notes.length;
    index = index - 1;
    var htmlStr = "";
    var userSelection = data.quote;
    var userComment = data.text;
    if (userComment != null) {
        htmlStr += "<li onclick='onclickRenderedNotes(" + index + ")' class='notes-list-item' id='" + data.id + "'>" +
            "<div class='notes-list-item-indicator'></div>" +
            "<div class='individual-note-selection' style='display: none;'>" +
            "<label class='not-active'>" +
            "<input type='checkbox' name='chapter' value='Chapter'  id='notescheckbox" + index + "'>" +
            " <span class='checkmark'></span>\n" +
            "</label>" +
            "</div>" +
            "<div class='notes-created-by-user'>" +
            " <div >";
        for (var k = 0; k < data.quoteAndClassNamesArr.length; k++) {
            htmlStr += "<span  style='font-size: 16px; padding-right: 1px' class='note-of-user " + data.quoteAndClassNamesArr[k].className + "' id='notes" + index + k + "'>" + data.quoteAndClassNamesArr[k].quote + "</span >";
        }
        htmlStr += "<p class='comment-by-user'>" + userComment + "</p>" +
            "</div>" +
            "</div>" +
            "</li>";
    } else {
        htmlStr += "<li onclick='onclickRenderedNotes(" + index + ")' class='notes-list-item' id='" + data.id + "'>" +
            "<div class='notes-list-item-indicator'></div>" +
            "<div class='individual-note-selection' style='display: none;'>" +
            "<label class='not-active'>" +
            "<input type='checkbox' name='chapter' value='Chapter'  id='notescheckbox" + index + "'>" +
            " <span class='checkmark'></span>\n" +
            "</label>" +
            "</div>" +
            "<div class='notes-created-by-user'>" +
            " <div >";
        for (var k = 0; k < data.quoteAndClassNamesArr.length; k++) {
            htmlStr += "<span  style='font-size: 16px; padding-right: 1px' class='note-of-user " + data.quoteAndClassNamesArr[k].className + "' id='notes" + index + k + "'>" + data.quoteAndClassNamesArr[k].quote + "</span >";
        }
        htmlStr += "</div> " +
            "</div>" +
            "</li>";
    }
    if (document.getElementById('no-notes-created') != null && document.getElementById('no-notes-created') != undefined && document.getElementById('no-notes-created') != '') {
        $('#no-notes-created').html('');
        document.getElementById('user-notes').innerHTML = "<li class='notes-list-item'></li>" + "<div class='export-btn-wrapper' style='display: none;'>" +
            "<a href='javascript:exportToStudySet();' class='export-notes-btn waves-effect'>" + "Export" + "</a>" +
            "</div>";

    }
    if (document.getElementById(data.id) == null || document.getElementById(data.id) == undefined || document.getElementById(data.id) == '') {
        $(htmlStr).insertAfter($('.notes-list-item').last());
    } else {
        htmlStr = htmlStr.replace("<li class='notes-list-item' id='" + data.id + "'>", "");
        htmlStr = htmlStr.replace("</li>", "");
        document.getElementById(data.id).innerHTML = htmlStr;
    }

}

function exportToStudySet() {
    closeNotes();
    var cStr = "<div class=\"row justify-content-center\" id=\"revisionTitleInput\">\n" +
        "        <div class=\"study-set-item\">\n" +
        "<span class=\"input-studyset\">\n" +
        "                <textarea class=\"study-set-textarea\" placeholder=\"Revision set name\" id=\"revisionTitle\"></textarea>\n" +
        "                            <label class=\"input-label input-label-login input-label-login-color-1\" for=\"revisionTitle\">\n" +
        "                                <span class=\"input-label-content input-label-content-login\">Revision Set Name</span>\n" +
        "                            </label>\n" +
        "                        </span>" +
        "        </div>\n" +
        "    </div>\n";
    var checkedItemsPresent = false;
    var noOfItemsExported = 0;
    for (var i = 0; i < notes.length; i++) {
        if (document.getElementById("notescheckbox" + i) != null && document.getElementById("notescheckbox" + i) != undefined) {
            if (document.getElementById("notescheckbox" + i).checked) {
                noOfItemsExported++;
                checkedItemsPresent = true;
                cStr += "<div class=\"study-set-main\" id=\"noteskeyvalueholder_" + noOfItemsExported + "\">\n" +
                    "            <span class=\"term-counter\"></span>\n" +
                    "<div class='d-flex justify-content-center bg-revCard flex-wrap'>" +
                    "            <div class=\"study-set-item\">\n" +
                    "<span class=\"input-studyset termDimen\">\n" +
                    "                    <textarea class=\"study-set-textarea\" placeholder=\"Enter Term\" id=\"notesterm" + noOfItemsExported + "\" ></textarea>\n" +
                    "                            <label class=\"input-label input-label-login input-label-login-color-1\" for=\"notesterm" + noOfItemsExported + "\">\n" +
                    "                                <span class=\"input-label-content input-label-content-login\">Term</span>\n" +
                    "                            </label>\n" +
                    "                        </span>" +
                    "            </div>\n" +
                    "            <div class=\"study-set-item\">\n" +
                    "<span class=\"input-studyset\">\n" +
                    "                    <textarea class=\"study-set-textarea\" placeholder=\"Enter Definition\" id=\"notesdefinition" + noOfItemsExported + "\">" + notes[i].quote + "</textarea>\n" +
                    "                            <label class=\"input-label input-label-login input-label-login-color-1\" for=\"notesdefinition" + noOfItemsExported + "\">\n" +
                    "                            </label>\n" +
                    "                        </span>" +
                    "            </div>\n" +
                    "        </div>" +
                    "</div>";
            }
        }
    }
    if (checkedItemsPresent) {
        cStr += " <div class=\"add-study-card-btn-wrapper add-card-btn-wrapper-save\">\n" +
            "            <a href=\"javascript:saveKeyValues(" + noOfItemsExported + ");\" class=\"add-study-card-btn\">\n" +
            "                <span>Save</span>\n" +
            "            </a>\n" +
            "        </div>";
        $("#study-set-wrapper").hide();
        document.getElementById("study-set-wrapper").innerHTML = "";
        document.getElementById("study-set-from-notes").innerHTML = cStr;
        $("#study-set-from-notes").show();

        $('#chapter-details-tabs a[href="#studySets"]').tab('show');
        $("#content-data-studyset-nosets").hide();
        $("#content-data-studyset").hide();
        $("#study-set-wrapper-container").show();
        newStudySet = true;
        studySetResId = -1;

    } else {
        alert("Select atleast one item");
    }

}



function displaySelectionCheckbox() {
    if ($('.export-study-set').html() == 'Export to Revision') {
        $('.export-study-set').html('Cancel').addClass('cancel-export-study-set');
        $('.comment-by-user').hide();
        $('.notes-list-item-indicator').hide();
        $('.individual-note-selection').show();
        $('.export-btn-wrapper').show();
    } else {
        $('.export-study-set').html('Export to Revision').removeClass('cancel-export-study-set');
        $('.comment-by-user').show();
        $('.notes-list-item-indicator').show();
        $('.individual-note-selection').hide();
        $('.export-btn-wrapper').hide();
    }
}

function languageSelected(event) {
    $.ajax({
        type: "POST",
        headers: {
            'Authorization': 'Bearer ************************************************************************************************************************************************************************************************************************************'
        },
        url: "https://translation.googleapis.com/language/translate/v2\n" +
            "?key=AIzaSyBCrQXrkQEcERPCB2QS8BGShqztBa-DL-w",
        data: {
            "q": $('#originalText').text(),
            "target": event.target.value.trim()
        },
        success: function (data, status) {
            $('#translatedText').text(data.data.translations[0].translatedText);
            console.log(data);
        }
    });
    console.log(event.target.value);
}

var bookLang = $("body").attr("annotator-data").split("__")[1];

var _annotatorData = JSON.parse($("body").attr("annotator-data").split("__")[2]);

loadAnnotator($("body").attr("annotator-data").split("__")[0],_annotatorData);

    function loadAnnotationsFromJavaData(resId,data){
        loadAnnotator(resId,JSON.parse(data));
    }


// </script>