# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

-keepnames class * implements android.os.Parcelable {
   *;
}

-keepnames class * implements java.io.Serializable {
   *;
}

-keep class com.wang.avi.**{*;}

-keep public class * extends java.lang.Exception

-keepclassmembers class ReadJSInterface {
   public *;
}

# Keep StringConcatFactory for R8
-dontwarn java.lang.invoke.StringConcatFactory
-keep class java.lang.invoke.StringConcatFactory { *; }

# Keep all data model classes
-keep class com.ws.resources.data.models.** { *; }

# Keep toString methods for data classes
-keepclassmembers class * {
    public java.lang.String toString();
}