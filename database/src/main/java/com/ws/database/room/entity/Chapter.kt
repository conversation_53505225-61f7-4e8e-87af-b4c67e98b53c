package com.ws.database.room.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import java.io.Serializable

@Entity
data class Chapter (
    @ColumnInfo(name = "bookId") var bookId: String,
    @ColumnInfo(name = "name") var name: String,
    @ColumnInfo(name = "previewChapter") var previewChapter: Boolean,
    @ColumnInfo(name = "desc") var desc: String?,
    @PrimaryKey @ColumnInfo(name = "chapterId") var chapterId: String,
    @ColumnInfo(name = "preview") var preview: Boolean
) : Serializable
