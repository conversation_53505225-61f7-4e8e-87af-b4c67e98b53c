package com.ws.database.room.doa

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.ws.database.room.entity.Annotations

@Dao
interface AnnotationsDao {

    @Query("SELECT * FROM annotations")
    suspend fun getAllAnnotations(): List<Annotations>

    @Query("SELECT * FROM annotations WHERE resId = :resId")
    suspend fun getAllAnnotationsByResId(resId: String): List<Annotations>

    @Query("SELECT annotationJson FROM annotations WHERE resId = :resId")
    suspend fun getAllAnnotationsJsonByResId(resId: String): List<String>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun addAnnotation(annotations: Annotations)

    @Delete
    suspend fun deleteAnnotation(annotations: Annotations)

    @Query("DELETE FROM annotations")
    suspend fun deleteAllAnnotation() {}

    @Query("DELETE FROM annotations WHERE resId = :resId")
    suspend fun deleteAnnotation(resId: String)

}