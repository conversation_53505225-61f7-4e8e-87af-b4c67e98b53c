package com.ws.database.room.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity
data class ReadingMaterial(
    @PrimaryKey(autoGenerate = true) val id: Int,
    @ColumnInfo(name = "id_reading") val id_reading : Int,
    @ColumnInfo(name = "title") val title: String,
    @ColumnInfo(name = "description") val description: String,
    @ColumnInfo(name = "resourceType") val resourceType: String,
    @ColumnInfo(name = "showFullDetails") val showFullDetails: String,
    @ColumnInfo(name = "referenceLink") val referenceLink: String,
    @ColumnInfo(name = "answer") val answer: String,
    @ColumnInfo(name = "showAnswer") val showAnswer: String,
    @ColumnInfo(name = "videoLink") val videoLink: String,
    @ColumnInfo(name = "dateCreated") val dateCreated: String,
    @ColumnInfo(name = "tag") val tag: String,
    @ColumnInfo(name = "image_url") val image_url: String,
    @ColumnInfo(name = "plainDescription") val plainDescription: String
)