package com.ws.database.room.doa

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.ws.database.room.entity.ReadData

@Dao
interface ReadDataDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun cacheReadData(readData: ReadData)

    @Query("SELECT * FROM readdata WHERE resId = :resId")
    suspend fun getCachedReadData(resId: String): List<ReadData>

    @Query("SELECT * FROM readdata WHERE bookId = :bookId")
    suspend fun getCachedReadDataByBookId(bookId: String): List<ReadData>

    @Query("DELETE FROM readdata WHERE resId = :resId")
    suspend fun removeCachedReadData(resId: String)

    @Query("DELETE FROM readdata WHERE bookId = :bookId")
    suspend fun removeCachedReadDataByBookId(bookId: String)

    @Query("DELETE FROM readdata")
    suspend fun deleteAllCachedReadData()
}