package com.ws.database.room.converters

import androidx.room.ProvidedTypeConverter
import androidx.room.TypeConverter
import com.wonderslate.commons.interfaces.JSONParser
import com.ws.database.room.entity.LibraryBook
import org.json.JSONArray
import java.lang.Exception

@ProvidedTypeConverter
class BookListTypeConverter(
    private val jsonParser: JSONParser
) {

    @TypeConverter
    fun libraryBooksToString(books: List<LibraryBook>?): String {
        return "[]"
    }

    @TypeConverter
    fun stringToLibraryBooks(jsonString: String): List<LibraryBook> {
        return try {
            val array = JSONArray(jsonString)
            jsonParser.parseList(array, LibraryBook::class.java)
        } catch (e: Exception) {
            listOf()
        }
    }

}