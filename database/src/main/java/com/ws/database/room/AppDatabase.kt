package com.ws.database.room

import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.ws.database.room.converters.BookListTypeConverter
import com.ws.database.room.doa.*
import com.ws.database.room.entity.*

@Database(entities = [ReadingMaterial::class, LibraryBook::class, Chapter::class, Resource::class, Quiz::class, ReadData::class,
                     Annotations::class, AnnotationAction::class,Preference::class], version = 4)
@TypeConverters(BookListTypeConverter::class)
abstract class AppDatabase : RoomDatabase() {
    abstract fun readingMaterialDao(): ReadingMaterialDao
    abstract fun libraryBooksDao(): LibraryBooksDao
    abstract fun chaptersDao(): ChaptersDao
    abstract fun resourcesDao(): ResourcesDao
    abstract fun quizDao(): QuizDao
    abstract fun readDataDao(): ReadDataDao
    abstract fun annotationsDao(): AnnotationsDao
    abstract fun annotationActionsDao(): AnnotationActionsDao
    abstract fun prefrenceDao(): PreferenceDao

}