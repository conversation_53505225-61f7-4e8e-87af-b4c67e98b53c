package com.ws.database.room

import android.content.Context
import androidx.room.Room

internal class DatabaseBuilder(
    private val context: Context,
    private val converters: ConvertersProvider
) {

    private var appDatabase: AppDatabase? = null

    fun getInstance(): AppDatabase {
        if (appDatabase == null) {
            synchronized(AppDatabase::class) {
                appDatabase = buildRoomDB()
            }
        }

        return appDatabase!!
    }

    private fun buildRoomDB(): AppDatabase {
        return Room
            .databaseBuilder(
                context.applicationContext,
                AppDatabase::class.java,
                "prep_joy_database"
            )
            .fallbackToDestructiveMigration()
            .addTypeConverter(converters.getBookListTypeConverter())
            .build()
    }
}
