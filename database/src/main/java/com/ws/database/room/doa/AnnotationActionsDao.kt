package com.ws.database.room.doa

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.ws.database.room.entity.AnnotationAction

@Dao
interface AnnotationActionsDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun addAction(annotationAction: AnnotationAction)

    @Query("SELECT * FROM annotationaction")
    suspend fun getAllActions(): List<AnnotationAction>

    @Query("SELECT * FROM annotationaction WHERE resId = :resId")
    suspend fun getAllActionsByResId(resId: String): List<AnnotationAction>

    @Delete
    suspend fun deleteAction(annotationAction: AnnotationAction)
}