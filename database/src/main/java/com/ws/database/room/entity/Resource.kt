package com.ws.database.room.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.io.Serializable

@Entity
data class Resource(
    val allowComments: String = "",
    val chapterDesc: String = "",
    val dateCreated: String = "",
    val displayComments: String = "",
    val downloadlink1: String = "",
    val downloadlink2: String = "",
    val downloadlink3: String = "",
    val eBook: String = "",
    val ebupChapterLink: String? = null,
    val fileSize: String = "",
    val fileType: String = "",
    val filename: String = "",
    @PrimaryKey val id: String = "",
    var resLink: String = "",
    var resName: String = "",
    val resType: String = "",
    val subType: String? = null,
    val testEndDate: String = "",
    val testResultDate: String = "",
    val testStartDate: String = "",
    val topicId: String = "",
    val videoPlayer: String = "no",
    var bookType: String = ""
): Serializable {
    fun isPdfType(): Boolean {
        return fileType.equals("pdf", true)
    }

    fun isEbook(): Boolean {
        return eBook == "true"
    }
}
