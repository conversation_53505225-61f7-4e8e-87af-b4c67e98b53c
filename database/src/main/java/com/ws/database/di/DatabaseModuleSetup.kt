package com.ws.database.di
import com.ws.database.room.ConvertersProvider
import com.ws.database.room.DaoProvider
import com.ws.database.room.DatabaseBuilder
import org.koin.core.context.loadKoinModules

import org.koin.dsl.module

object DatabaseModuleSetup {
    private fun databaseModule() = module {
        single { ConvertersProvider(get()) }
        single { DatabaseBuilder(get(), get()).getInstance() }
        single { DaoProvider(get()) }
    }

    fun inject() {
        loadKoinModules(databaseModule())
    }
}