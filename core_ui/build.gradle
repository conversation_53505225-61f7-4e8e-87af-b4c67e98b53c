plugins {
    id 'com.android.library'
    id 'kotlin-android'
}

android {
    namespace 'com.ws.core_ui'
    compileSdk 34

    defaultConfig {
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    buildFeatures {
        viewBinding true
        dataBinding true
        buildConfig true
    }

    lint {
        abortOnError false
    }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation "androidx.core:core-ktx:$core_ktx_version"
    implementation "androidx.appcompat:appcompat:$app_compat_version"
    implementation 'com.google.android.material:material:1.11.0'
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle_version"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:$coroutine_version"
    implementation "io.insert-koin:koin-android:$koin_version"
    implementation "com.github.bumptech.glide:glide:$glide_version"
    implementation "com.airbnb.android:lottie:$lottie_version"

    // Joda Time for date/time handling
    implementation 'joda-time:joda-time:2.12.5'
    implementation 'net.danlew:android.joda:2.12.5'

    // Base64 encoding
    implementation 'commons-codec:commons-codec:1.16.0'

    api project(path: ':core_common')
    api project(path: ':commons')
    
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}
