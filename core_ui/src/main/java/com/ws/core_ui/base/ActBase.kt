package com.ws.core_ui.base

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.viewbinding.ViewBinding
import com.ws.core_ui.utils.Utils
import kotlinx.coroutines.*

abstract class ActBase<T>(private val isSecure: Boolean = false) : AppCompatActivity() {

    var binding: T? = null

    protected val actScope: CoroutineScope by lazy {
        CoroutineScope(Dispatchers.Main + SupervisorJob())
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = bindView()
        setContentView((binding as ViewBinding?)?.root)
        Utils().disableScreenShot(this)
        init()
    }

    /**
     * To bind the layout to activity.
     *
     * Example: LayoutNameBinding.inflate(getLayoutInflater());
     */
    abstract fun bindView(): T

    abstract fun init()

    override fun onDestroy() {
        super.onDestroy()
        binding = null
        if(actScope.isActive) {
            actScope.cancel("Activity is destroyed")
        }
    }
}