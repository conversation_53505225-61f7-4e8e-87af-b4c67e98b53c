package com.ws.core_ui.utils

import android.app.Activity
import android.view.WindowManager
import com.ws.core_ui.BuildConfig

class Utils {

    fun disableScreenShot(context: Activity) {
        try {
            if (!BuildConfig.DEBUG) {
                context.window.setFlags(
                    WindowManager.LayoutParams.FLAG_SECURE,
                    WindowManager.LayoutParams.FLAG_SECURE
                )
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}