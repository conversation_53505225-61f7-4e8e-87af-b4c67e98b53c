package com.ws.core_ui.utils

import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*

object DateTimeUtils {

    fun getCurrentDeviceTime(format: String): String {
        val kolkataZone = ZoneId.of("Asia/Kolkata")
        val currentTime = LocalDateTime.now(kolkataZone)
        val formatter = DateTimeFormatter.ofPattern(format)
        return currentTime.format(formatter)
    }

}