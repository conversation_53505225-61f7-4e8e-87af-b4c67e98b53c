package com.ws.core_ui.extensions

import android.util.Log
import java.time.LocalDate
import java.time.temporal.ChronoUnit

private const val TAG = "LocalDateExts"

fun LocalDate?.isExpired(): Boolean {
    return try {
        if(this != null) {
            val leftDays = ChronoUnit.DAYS.between(LocalDate.now(), this)
            leftDays <= 0
        } else {
            false
        }
    } catch (e: Exception) {
        Log.e(TAG, "isBookExpired: ", e)
        false
    }
}