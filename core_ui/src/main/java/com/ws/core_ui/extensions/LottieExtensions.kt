package com.ws.core_ui.extensions

import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import androidx.core.content.ContextCompat
import com.airbnb.lottie.LottieAnimationView
import com.airbnb.lottie.LottieProperty
import com.airbnb.lottie.model.KeyPath
import com.ws.core_ui.R

fun LottieAnimationView.setAccentColorForLockAnim() {

    /*
    Layers of lock icon which can be used as keys for changing color
    -----------------------
    lower half -> body
    upper half -> Layer 3 Outlines
    key hole -> Layer 4 Outlines
    handle -> Layer 2 Outlines
    Dots -> 1, 2, 3
     */

    //To change color of lower half of lock
    addValueCallback(
        KeyPath("body", "**"),
        LottieProperty.COLOR_FILTER,
        {
            PorterDuffColorFilter(
                ContextCompat.getColor(context, R.color.colorAccent),
                PorterDuff.Mode.SRC_ATOP
            )
        }
    )
}