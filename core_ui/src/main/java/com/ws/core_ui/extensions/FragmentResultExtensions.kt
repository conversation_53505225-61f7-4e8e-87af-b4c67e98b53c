package com.ws.core_ui.extensions

import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentResultListener

fun Fragment.sendResult(
    key: String,
    result: Bundle
) {
    this.parentFragmentManager.setFragmentResult(key, result)
}

fun Fragment.receiveResultFromChildFragment(
    key: String,
    listener: FragmentResultListener
) {
    this.childFragmentManager.setFragmentResultListener(
        key,
        this,
        listener
    )
}

fun Fragment.receiveResultFromFragment(
    key: String,
    listener: FragmentResultListener
) {
    this.parentFragmentManager.setFragmentResultListener(
        key,
        this,
        listener
    )
}