package com.ws.core_ui.extensions

import android.graphics.*
import android.graphics.drawable.Drawable
import android.widget.ImageView
import androidx.annotation.DrawableRes
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestBuilder
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.ws.core_ui.R

fun ImageView.loadImage(
    url: String?,
    @DrawableRes placeholder: Int = R.drawable.book_cover_placeholder,
    @DrawableRes errorDrawable: Int = R.drawable.book_cover_placeholder,
    diskCacheStrategy: DiskCacheStrategy = DiskCacheStrategy.AUTOMATIC,
    animate: Boolean = false,
    configure: (RequestBuilder<Drawable>.() -> Unit)? = null
) {
    val urlOrImage: Any? = if(url.isNullOrBlank()) context.compatDrawable(errorDrawable) else url
    val request = Glide.with(context)
        .load(urlOrImage)
        .thumbnail(Glide.with(context).load(placeholder))
        .error(context.compatDrawable(errorDrawable))
        .diskCacheStrategy(diskCacheStrategy)

    if(animate) {
        request.transition(DrawableTransitionOptions.withCrossFade())
    }

    if (configure != null) {
        request.configure()
    }
    request.into(this)
}

fun ImageView.loadImageAsBitmap(
    url: String,
    @DrawableRes placeholder: Int = R.drawable.book_cover_placeholder,
    @DrawableRes errorDrawable: Int = R.drawable.book_cover_placeholder,
    diskCacheStrategy: DiskCacheStrategy = DiskCacheStrategy.AUTOMATIC,
    imageWidth: Int = -1,
    imageHeight: Int = -1,
    cornerRadius: Int = -1,
) {
    val height = if(imageHeight == -1) context.convertToDpi(80) else imageHeight
    val width = if(imageWidth == -1) context.convertToDpi(64) else imageWidth
    val radius = if(cornerRadius == -1) context.convertToDpi(6) else cornerRadius

    Glide.with(context)
        .asBitmap()
        .load(url)
        .placeholder(placeholder)
        .error(errorDrawable)
        .diskCacheStrategy(diskCacheStrategy)
        .into(object: CustomTarget<Bitmap>(width, height) {
            override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                if(<EMAIL> != null) {
                    val imageRounded = Bitmap.createBitmap(resource.width, resource.height, resource.config)
                    val canvas = Canvas(imageRounded)
                    val paint = Paint().also {
                        it.isAntiAlias = true
                        it.shader = BitmapShader(resource, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP)
                    }
                    canvas.drawRoundRect(RectF(0F, 0F, resource.width.toFloat(), resource.height.toFloat()), radius.toFloat(), radius.toFloat(), paint)
                    <EMAIL>(imageRounded)
                }
            }

            override fun onLoadCleared(placeholder: Drawable?) {
                //Auto generated method
            }
        });
}