package com.ws.resources.ui

import android.annotation.SuppressLint
import android.app.Activity
import android.net.Uri
import android.util.Log
import android.view.View
import android.webkit.*
import androidx.annotation.ColorInt
import java.io.File

@SuppressLint("ClickableViewAccessibility")
fun WebView.configure(
    enableZoomControls: Boolean = false,
    isDebugMode: Boolean = false,
    onProgressChanged: ((Int) -> Unit)? = null
) {

    setLayerType(View.LAYER_TYPE_HARDWARE, null)
    settings.apply {
        javaScriptEnabled = true
        builtInZoomControls = enableZoomControls
        displayZoomControls = false

        allowFileAccessFromFileURLs = true
        loadsImagesAutomatically = true
        allowUniversalAccessFromFileURLs = true

        defaultFontSize = 14
        displayZoomControls = false
    }

    isVerticalScrollBarEnabled = true
    isHorizontalScrollBarEnabled = true
    isClickable = false

    WebView.setWebContentsDebuggingEnabled(isDebugMode)

    webChromeClient = object : WebChromeClient() {
        override fun onConsoleMessage(cm: ConsoleMessage): Boolean {
            if (cm.sourceId().isNotEmpty()) {
                Log.e(
                    "CONFIGURE",
                    cm.message() + " -- From line "
                        + cm.lineNumber() + " of "
                        + cm.sourceId().substring(10)
                )
            } else {
                Log.e(
                    "CONFIGURE",
                    cm.message() + " -- From line "
                        + cm.lineNumber()
                )
            }
            return true
        }

        override fun onProgressChanged(view: WebView?, newProgress: Int) {
            onProgressChanged?.invoke(newProgress)
        }
    }
}

fun WebView.configureForCaching(
    activity: Activity,
    isConnected: Boolean,
) {
    this.settings.apply {
        //Save image cache
        domStorageEnabled = true
        mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
        // setAppCacheEnabled(true) // Deprecated in API 33
        allowFileAccess = true

        val imageCacheDir: File = activity.cacheDir
        if (!imageCacheDir.exists()) {
            imageCacheDir.mkdir()
        }
        // setAppCachePath(imageCacheDir.path) // Deprecated in API 33
        cacheMode = if (isConnected) {
            WebSettings.LOAD_DEFAULT
        } else {
            // No Internet Available; Get Images From Cache
            WebSettings.LOAD_CACHE_ELSE_NETWORK
        }
    }
}

fun WebView.configureForSecurePdf(onProgressChanged: ((Int) -> Unit)? = null) {
    val webSettings = settings
    webSettings.javaScriptEnabled = true
    webSettings.domStorageEnabled = true
    webSettings.loadsImagesAutomatically = true
    webSettings.cacheMode = WebSettings.LOAD_CACHE_ELSE_NETWORK
    webSettings.builtInZoomControls = true
    webSettings.displayZoomControls = false
    webSettings.allowFileAccessFromFileURLs = true
    webSettings.allowUniversalAccessFromFileURLs = true
    webSettings.javaScriptCanOpenWindowsAutomatically = true
    // webSettings.setAppCachePath(context.cacheDir.absolutePath) // Deprecated in API 33
    webSettings.databaseEnabled = true
    webSettings.loadWithOverviewMode = true
    webSettings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW

    webChromeClient = object : WebChromeClient() {
        override fun onConsoleMessage(cm: ConsoleMessage): Boolean {
            if (cm.sourceId().isNotEmpty()) {
                Log.e(
                    "CONFIGURE",
                    cm.message() + " -- From line "
                        + cm.lineNumber() + " of "
                        + cm.sourceId().substring(10)
                )
            } else {
                Log.e(
                    "CONFIGURE",
                    cm.message() + " -- From line "
                        + cm.lineNumber()
                )
            }
            return true
        }

        override fun onProgressChanged(view: WebView?, newProgress: Int) {
            onProgressChanged?.invoke(newProgress)
        }
    }
}

fun WebView.changeColor(
    @ColorInt color: Int,
) {
    //Change background color
    if (color == 0) {
        evaluateJavascript(
            "document.body.style.backgroundColor=\"white\";document.body.style.color=\"black\";",
            null
        )
        evaluateJavascript("document.body.classList.add('blackTheme');", null)
        evaluateJavascript("document.body.classList.remove('whiteTheme');", null)
    } else {
        evaluateJavascript(
            "document.body.style.backgroundColor=\"black\";document.body.style.color=\"white\";",
            null
        )
        evaluateJavascript("document.body.classList.add('whiteTheme');", null)
        evaluateJavascript("document.body.classList.remove('blackTheme');", null)
    }
}

fun WebView.handleUrlLoading() {
    webViewClient = object : WebViewClient() {
        override fun shouldOverrideUrlLoading(webView: WebView?, url: String): Boolean {
            return shouldOverrideUrlLoading(url)
        }

        override fun shouldOverrideUrlLoading(
            webView: WebView?,
            request: WebResourceRequest
        ): Boolean {
            val uri: Uri = request.url
            return shouldOverrideUrlLoading(uri.toString())
        }

        private fun shouldOverrideUrlLoading(url: String): Boolean {
            loadUrl(url)
            // Returning True means that application wants to leave the current WebView and handle the url itself, otherwise return false.
            return false
        }
    }
}