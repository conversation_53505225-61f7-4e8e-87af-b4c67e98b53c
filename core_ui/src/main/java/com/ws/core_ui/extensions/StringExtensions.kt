package com.ws.core_ui.extensions

import android.util.Log
import android.webkit.MimeTypeMap
import android.webkit.URLUtil
import com.ws.core_ui.R
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.io.UnsupportedEncodingException
import java.lang.IllegalArgumentException
import java.net.URLDecoder

private const val TAG = "StringExtensions"

fun String?.toLocalDateOrNull(): LocalDate? {
    return try {
        if(this != null && isNotEmpty()) {
            val expiryDateValues = this.split(" ")
            val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
            LocalDate.parse(expiryDateValues[0], formatter)
        } else {
            null
        }
    } catch (e: Exception) {
        Log.e(TAG, "toLocalDateOrNull: ", e)
        null
    }
}

fun String?.isExpired(): Boolean {
    return this.toLocalDateOrNull().isExpired()
}

fun String?.getFormattedResType() = when(this) {
    "Notes" -> "Reading materials"
    "Reference Web Links" -> "Solutions"
    "Multiple Choice Questions" -> "Quizzes"
    "Reference Videos" -> "Videos"
    "KeyValues" -> "Flashcard"
    else -> this ?: ""
}

fun String?.getResTypeIcon() = when(this) {
    "Notes" -> R.drawable.ic_note_24
    "Reference Web Links" -> R.drawable.ic_link_24
    "Multiple Choice Questions" -> R.drawable.ic_quiz_24
    "Reference Videos" -> R.drawable.ic_videocam_24
    "KeyValues" -> R.drawable.ic_fc_book
    else -> R.drawable.ic_notes_res
}

fun String?.getFormattedResTypeAndIcon(): Pair<String, Int> = when(this) {
    "Notes" -> Pair("Reading materials", R.drawable.ic_note_24)
    "Reference Web Links" -> Pair("Solutions", R.drawable.ic_link_24)
    "Multiple Choice Questions" -> Pair("MCQ's", R.drawable.ic_quiz_24)
    "Reference Videos" -> Pair("Videos", R.drawable.ic_videocam_24)
    "KeyValues" -> Pair("Flashcard", R.drawable.ic_fc_book)
    else -> Pair(this ?: "", R.drawable.ic_note_24)
}

fun String?.guessExtension(fallback: String = ""): String {
    val ext = this?.let {
        MimeTypeMap.getFileExtensionFromUrl(it)
    } ?: fallback

    return ext.ifBlank { fallback }
}

fun String?.guessFileName(fallbackExtension: String = ""): String {
    val guessedExt = this.guessExtension()
    val name = URLUtil.guessFileName(
        this,
        null,
        null
    )

    return if(guessedExt.isBlank() && name.contains(".bin"))
        name.replace(".bin", fallbackExtension)
    else
        name
}


fun String.toDecode(): String {
   return try {
       URLDecoder.decode(this, Charsets.UTF_8.name())
   } catch (e: IllegalArgumentException) {
       this
   } catch (u: UnsupportedEncodingException) {
       this
   }
}