<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="2dp"
                android:left="2dp"
                android:right="2dp"
                android:top="2dp" />

            <solid android:color="#00FFFFFF" />

            <corners android:radius="25dp" />

        </shape>
    </item>

    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="2dp"
                android:left="2dp"
                android:right="2dp"
                android:top="2dp" />

            <solid android:color="#00FFFFFF" />

            <corners android:radius="25dp" />
        </shape>
    </item>

    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="2dp"
                android:left="2dp"
                android:right="2dp"
                android:top="2dp" />

            <solid android:color="#30CCCCCC" />

            <corners android:radius="25dp" />
        </shape>
    </item>

    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="2dp"
                android:left="2dp"
                android:right="2dp"
                android:top="2dp" />

            <corners android:radius="25dp" />

            <gradient
                android:angle="180"
                android:centerColor="#21CCCCCC"
                android:endColor="#21CCCCCC"
                android:startColor="#21CCCCCC"
                android:type="linear" />

        </shape>
    </item>

    <item
        android:bottom="1dp"
        android:left="1dp"
        android:right="1dp"
        android:top="1dp">
        <shape android:shape="rectangle">
            <corners android:radius="25dp" />
            <gradient
                android:angle="360"
                android:endColor="@color/colorAccentDark"
                android:startColor="@color/colorAccent"
                android:type="linear" />
        </shape>
    </item>
    <item
        android:gravity="center_vertical|right"
        android:left="5dp"
        android:right="5dp">
        <layer-list>
            <item>
                <bitmap
                    android:gravity="center_vertical|right"
                    android:src="@drawable/ic_arrow_down"
                    android:tint="@color/white" />
            </item>
        </layer-list>
    </item>
</layer-list>