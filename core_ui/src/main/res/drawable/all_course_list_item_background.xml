<?xml version="1.0" encoding="utf-8"?><!--
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle">

    <solid
        android:color="@color/white"/>
    <stroke
        android:color="#FFD601"
        android:width="1dp"/>
    <corners android:radius="6dp" />

</shape>-->
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_checked="true" android:state_pressed="true">

        <shape android:shape="rectangle">
            <solid android:color="@color/white" />
            <corners android:radius="6dp" />
            <stroke android:color="@color/colorAccent" android:width="4dp" />
        </shape>

    </item>

    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/white" />
            <corners android:radius="6dp" />
            <stroke android:color="@color/colorAccent" android:width="4dp" />
        </shape>
    </item>
    <item android:state_checked="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/white" />
            <corners android:radius="6dp" />
            <stroke android:color="@color/colorAccent" android:width="4dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">

            <solid android:color="@color/white" />
            <stroke android:color="@color/colorAccent" android:width="1dp" />
            <corners android:radius="6dp" />
        </shape>
    </item>
</selector>