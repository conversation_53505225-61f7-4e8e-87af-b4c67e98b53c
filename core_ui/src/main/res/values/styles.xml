<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="ModuleTheme" parent="Theme.AppCompat.Light">
        <!-- Customize your theme here. -->
        <!--<item name="colorPrimary">@color/colorPrimary</item>-->
        <item name="colorPrimary">@color/white</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="actionMenuTextColor">@color/white</item>
    </style>

    <style name="ModuleTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="ButtonLoadingProgress">
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="MyProgressBarTwo" parent="@style/Widget.AppCompat.ProgressBar.Horizontal">
        <item name="colorButtonNormal">@color/colorAccent</item>
        <item name="colorControlActivated">@color/colorAccent</item>
        <item name="colorControlHighlight">@color/light_grey</item>
        <item name="android:progressDrawable">@drawable/drawable_circularprogressbar</item>
        <item name="android:minWidth">200dp</item>
    </style>

    <style name="CustomSpinner" parent="Widget.AppCompat.Spinner">
        <item name="android:background">@drawable/spinner_background_colored</item>
        <item name="android:fontFamily">@font/poppins_medium</item>
    </style>

    <style name="CustomBottomSheetDialogTheme" parent="Theme.Design.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/CustomBottomSheetStyle</item>
        <item name="colorPrimary">@color/colorBottomSheetPrimary</item>
        <item name="colorPrimaryDark">@color/colorBottomSheetPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="CustomBottomSheetStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@drawable/bg_bottom_sheet</item>
        <item name="colorPrimary">@color/colorBottomSheetPrimary</item>
        <item name="colorPrimaryDark">@color/colorBottomSheetPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>
</resources>