package com.ws.shop.ui

import android.app.Activity
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.AdapterView
import android.widget.ArrayAdapter
import androidx.core.os.bundleOf
import androidx.core.widget.doOnTextChanged
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.ws.commons.Status
import com.ws.commons.enums.EventType
import com.ws.commons.extensions.joinList
import com.ws.commons.models.Event
import com.ws.commons.models.ShopBookData
import com.ws.core_ui.base.BaseFragmentWithListener
import com.ws.core_ui.extensions.*
import com.ws.shop.R
import com.ws.shop.data.models.QueryBooksListRequest
import com.ws.shop.data.models.ShopBooksFragConfig
import com.ws.shop.data.models.ShopBooksRequest
import com.ws.shop.data.models.SuggestionsRequest
import com.ws.shop.databinding.LayoutShopBooksBinding
import com.ws.shop.ui.ShopBooksFrag.Companion.newInstance
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * For displaying shop books list
 *
 * Use [newInstance] for creating instance of ShopBooksFrag.kt
 */
class ShopBooksFrag : BaseFragmentWithListener<LayoutShopBooksBinding, ShopBooksFrag.ShopBooksFragmentListener>() {

    private val viewModel by viewModel<ShopBooksViewModel>()

    private val latestBooksAdapter by inject<LatestBooksAdapter>()

    private val shopRequest = ShopBooksRequest()
    private val initialShopRequest = ShopBooksRequest()

    private var shopBooksFragConfig = ShopBooksFragConfig()
    private var wonderPubSharedPrefs: WonderPubSharedPrefs = WonderPubSharedPrefs.getInstance(context)

    private var endlessRecyclerOnScrollListener: EndlessRecyclerOnScrollListener? = null

    private var levelFilterAdapt: ArrayAdapter<String>? = null
    private var subjectFilterAdapt: ArrayAdapter<String>? = null
    private var gradeFilterAdapt: ArrayAdapter<String>? = null
    private var syllabusFilterAdapt: ArrayAdapter<String>? = null
    private var selectedLevelPre = ""
    private var selectedSyllabusPre = ""
    private var selectedGradePre = ""
    private var selectedSchoolPre = ""
    private var resetFilter = false

    private var isFirstTime = true

    override fun inflateViewBinding(inflater: LayoutInflater, container: ViewGroup?): LayoutShopBooksBinding {
        return LayoutShopBooksBinding.inflate(inflater, container, false)
    }

    override fun initArguments(bundle: Bundle?) {
        shopBooksFragConfig = (bundle?.getSerializable(PARAM_SHOP_BOOKS_FRAG_CONFIG) as? ShopBooksFragConfig?)
                ?: ShopBooksFragConfig()
        if (shopBooksFragConfig.level.isNotBlank()) {
            selectedLevelPre = shopBooksFragConfig.level
            shopBooksFragConfig.level = shopBooksFragConfig.level
        }
        if (shopBooksFragConfig.syllabus.isNotBlank()) {
            selectedSyllabusPre = shopBooksFragConfig.syllabus
            shopBooksFragConfig.syllabus = shopBooksFragConfig.syllabus
        }
        if (shopBooksFragConfig.grade.isNotBlank()) {
            selectedGradePre = shopBooksFragConfig.grade
            shopBooksFragConfig.grade = shopBooksFragConfig.grade
        }
        if (shopBooksFragConfig.subject.isNotBlank()) {
            selectedSchoolPre = shopBooksFragConfig.subject
            shopBooksFragConfig.subject = shopBooksFragConfig.subject
        }
    }

    override fun initView() {
        wonderPubSharedPrefs = WonderPubSharedPrefs.getInstance(activity)
        binding?.shimmerBooks?.apply {
            showView()
            startShimmer()
        }

        viewModel.useLocalLevelFilters = shopBooksFragConfig.useLocalLevelFilters

        initLatestBooksRecycler()

        binding?.moreFilters?.setImageResource(
            if(shopBooksFragConfig.replaceFilterBtnWithClearBtn) R.drawable.ic_close_24 else R.drawable.ic_filter_accent_color
        )

        initClicks()

        initSearchBox()

        initObservers()

    }

    override fun load() {
        if (isFirstTime) {
            requestInitialBooks()
            showPreSelectedLevelFilter()
            fetchFilters(2)
        }
        isFirstTime = false
    }

    private fun fetchFilters(sequence: Int, observe: Boolean = true) {
        viewModel.getFilters(shopRequest, sequence)
        if (!observe) {
            viewModel.filters.removeObservers(viewLifecycleOwner)
        }
    }

    private fun booksAndFilters(resetPageCount: Boolean,
                                sequence: Int,
                                observe: Boolean,
                                emitVal: Boolean?) {
        requestBooks(resetPageCount, emitVal)
        fetchFilters(sequence, observe)
    }


    private fun resetBooks(ref: Int) {

        if (ref == 0) {
            requestBooks(resetPageCount = true, emitVal = false)
        } else {
            resetSyllabusFilterUI()
            resetGradeFilterUI()
            resetSubjectFilterUI()
            binding?.apply {
                spinnerGrades.isEnabled = false
                spinnerSubjects.isEnabled = false
            }
            binding?.acSearchBox?.apply {
                setText("")
                clearFocus()
            }
            resetValues()
            resetLevelFilterUI()
            binding?.apply {
                spinnerSyllabus.isEnabled = false
                linearAdditionFilters.hideView()
            }
            resetFilter = true
            initClicks()
            //requestBooks(resetPageCount = true)
            //showPreSelectedSyllabusFilter()
            clearFilters()
        }
    }

    private fun resetValues()
    {
        wonderPubSharedPrefs.sharedPrefsLastFilterLevelSearch = shopBooksFragConfig.preSelectLevel
        selectedLevelPre = shopBooksFragConfig.preSelectLevel

        wonderPubSharedPrefs.sharedPrefsLastFilterSyllabusSearch = shopBooksFragConfig.preSelectSyllabus
        selectedSyllabusPre = ""

        wonderPubSharedPrefs.sharedPrefsLastFilterGradeSearch = ""
        selectedGradePre = ""

        wonderPubSharedPrefs.sharedPrefsLastFilterSubjectSearch = ""
        selectedSchoolPre = ""

       // showPreSelectedSyllabusFilter()
    }

    private fun showPreSelectedLevelFilter() {
        shopRequest.level = selectedLevelPre

        // Change level selection and background color
        val indexOfLevel = levelFilterAdapt?.getPosition(shopRequest.level) ?: -1
        if(indexOfLevel != -1 && binding?.spinnerLevel?.selectedItemPosition != indexOfLevel) {
            binding?.spinnerLevel?.setSelection(indexOfLevel)
            changeFilterBackground(binding?.spinnerLevel)
        }

        // Show more/clear filter button when at least one filter is selected
        binding?.moreFilters?.showView()
    }

    private fun syllabusVal(): String {
        val syllabus = wonderPubSharedPrefs.sharedPrefsUserSyllabusPref.joinList()
        var spinnerValue = ""
        if (syllabus.isEmpty() && selectedSyllabusPre.isNotEmpty()) {
            spinnerValue = selectedSyllabusPre
        } else if (syllabus.isNotEmpty()) {
            spinnerValue = syllabus
        }
        if (selectedSyllabusPre.isEmpty()) {
            selectedSyllabusPre = spinnerValue
        }
        shopRequest.syllabus = spinnerValue
        return spinnerValue
    }

    private fun showPreSelectedSyllabusFilter() {
        val spinnerValue = if (selectedSyllabusPre.isEmpty()) {
            ""
        } else { syllabusVal() }
        if (spinnerValue.isNotEmpty() && !spinnerValue.contains(",")) {
            binding?.spinnerSyllabus?.visibility(true)
            val indexOfLevel = syllabusFilterAdapt?.getPosition(spinnerValue) ?: -1
            if(indexOfLevel != -1 && binding?.spinnerSyllabus?.selectedItemPosition != indexOfLevel) {
                binding?.spinnerSyllabus?.setSelection(indexOfLevel)
                changeFilterBackground(binding?.spinnerSyllabus)
            }
        } else if (spinnerValue.contains(",")) {
            binding?.spinnerSyllabus?.visibility(false)
        } else {
            binding?.spinnerSyllabus?.visibility(true)
            resetGradeFilterUI()
            resetSubjectFilterUI()
        }

        // Show more/clear filter button when at least one filter is selected
        binding?.moreFilters?.showView()

        selectedSyllabusPre.let {
            if (it.isNotEmpty()) {
                resetGradeFilterUI()
                resetSubjectFilterUI()
                viewModel.logEvent(
                    Event(
                        type = EventType.SYLLABUS_FILTER_CHANGED,
                        value = Pair("filter", it)
                    )
                )
            }
        }

    }

    private fun gradeVal(): String {
        val grade = wonderPubSharedPrefs.sharedPrefsUserGradePref.joinList()
        var spinnerValue = ""
        if (grade.isEmpty() && selectedGradePre.isNotEmpty()) {
            spinnerValue = selectedGradePre
        } else if (grade.isNotEmpty()) {
            spinnerValue = grade
        }
        if (selectedGradePre.isEmpty()) {
            selectedGradePre = spinnerValue
        }
        shopRequest.grade = spinnerValue
        return spinnerValue
    }


    private fun showPreSelectedGradeFilter() {
        val spinnerValue = if (selectedGradePre.isEmpty()) {
            ""
        } else { gradeVal() }
        // Change level selection and background color
        if (spinnerValue.isNotEmpty() && !spinnerValue.contains(",")) {
            binding?.containerGrades?.visibility(true)
            val indexOfLevel = gradeFilterAdapt?.getPosition(spinnerValue) ?: -1
            if(indexOfLevel != -1 && binding?.spinnerGrades?.selectedItemPosition != indexOfLevel) {
                binding?.spinnerGrades?.setSelection(indexOfLevel)
                changeFilterBackground(binding?.spinnerGrades)
            }
        } else if (spinnerValue.contains(",")) {
            binding?.containerGrades?.visibility(false)
        } else {
            binding?.containerGrades?.visibility(true)
            resetSubjectFilterUI()
        }
        // Show more/clear filter button when at least one filter is selected
        binding?.moreFilters?.showView()
        selectedGradePre.let {
            if (it.isNotEmpty()) {
                resetSubjectFilterUI()
                if (null != gradeFilterAdapt &&
                    gradeFilterAdapt?.getPosition(it) == -1 &&
                    wonderPubSharedPrefs.sharedPrefsUserGradePref.joinList().isEmpty()) {
                    selectedGradePre = ""
                    gradeVal()
                    booksAndFilters(false, 4, observe = false, emitVal = false)
                }
                viewModel.logEvent(
                    Event(
                        type = EventType.GRADE_FILTER_CHANGED,
                        value = Pair("filter", spinnerValue)
                    )
                )
            }
        }
        binding?.apply {
            spinnerSyllabus.isEnabled = true
            if (selectedGradePre.isNotEmpty()) {
                spinnerGrades.isEnabled = true
            }
            linearAdditionFilters.showView()
        }

    }

    private fun subjectVal(): String {
        val subjects = wonderPubSharedPrefs.sharedPrefsUserSubjectPref.joinList()
        var spinnerValue = ""
        if (subjects.isEmpty() && selectedSchoolPre.isNotEmpty()) {
            spinnerValue = selectedSchoolPre
        } else if (subjects.isNotEmpty()) {
            spinnerValue = subjects
        }
        if (selectedSchoolPre.isEmpty()) {
            selectedSchoolPre = spinnerValue
        }
        shopRequest.subject = spinnerValue
        return spinnerValue
    }

    private fun showPreSelectedSchoolFilter() {
        val spinnerValue = if (selectedSchoolPre.isEmpty()) {
            ""
        } else { subjectVal() }
        if (spinnerValue.isNotEmpty() && !spinnerValue.contains(",")) {
            // Change level selection and background color
            binding?.spinnerSubjects?.visibility(true)
            val indexOfLevel = subjectFilterAdapt?.getPosition(shopRequest.subject) ?: -1
            if(indexOfLevel != -1 && binding?.spinnerSubjects?.selectedItemPosition != indexOfLevel) {
                binding?.spinnerSubjects?.setSelection(indexOfLevel)
                changeFilterBackground(binding?.spinnerSubjects)
            }
        } else if (spinnerValue.contains(",")) {
            binding?.spinnerSubjects?.visibility(false)
        } else {
            binding?.spinnerSubjects?.visibility(true)
        }
        // Show more/clear filter button when at least one filter is selected
        binding?.moreFilters?.showView()
        selectedSchoolPre.let {
            if (it.isNotEmpty()) {
                if (null != gradeFilterAdapt &&
                    gradeFilterAdapt?.getPosition(it) == -1 &&
                    wonderPubSharedPrefs.sharedPrefsUserSubjectPref.joinList().isEmpty()) {
                    selectedSchoolPre = ""
                    subjectVal()
                    booksAndFilters(false, 4, observe = false, emitVal = false)
                }
                viewModel.logEvent(
                    Event(
                        type = EventType.GRADE_FILTER_CHANGED,
                        value = Pair("filter", spinnerValue)
                    )
                )
            }
        }

        binding?.apply {
            spinnerSyllabus.isEnabled = true
            spinnerGrades.isEnabled = selectedSyllabusPre.isNotEmpty()
            spinnerSubjects.isEnabled = true
            linearAdditionFilters.showView()
        }

    }

    private fun requestInitialBooks() {
        val wonderSharePrefs = WonderPubSharedPrefs.getInstance(context)
        endlessRecyclerOnScrollListener?.resetState()

        shopRequest.pageNo = 0
        shopRequest.publisherId = ""
        shopRequest.subject = ""
        shopRequest.syllabus = ""
        shopRequest.grade = ""
        shopRequest.mcqBook = false

        initialShopRequest.pageNo = 0
        binding?.recyclerLatestBooks?.scrollToPosition(0)
        if (wonderSharePrefs.sharedPrefsUserLevelPref.isNotEmpty()) {
            shopRequest.level = wonderSharePrefs.sharedPrefsUserLevelPref
            initialShopRequest.level = wonderSharePrefs.sharedPrefsUserLevelPref
            initialShopRequest.publisherId = ""
            if (wonderSharePrefs.sharedPrefsUserGradePref.isNotEmpty()) {
                initialShopRequest.grade = wonderSharePrefs.sharedPrefsUserGradePref[0]
            }
            else {
                initialShopRequest.grade = ""
            }
            if (wonderSharePrefs.sharedPrefsUserSyllabusPref.isNotEmpty()) {
                initialShopRequest.syllabus = wonderSharePrefs.sharedPrefsUserSyllabusPref[0]
            }
            else {
                initialShopRequest.syllabus = ""
            }
            initialShopRequest.subject = ""
            initialShopRequest.mcqBook = false
        }
        else {
            initialShopRequest.level = ""
            shopRequest.level = ""
        }
        viewModel.getShopBooks(initialShopRequest, true)
    }
    
    private fun requestBooks(resetPageCount: Boolean = false,
                             emitVal: Boolean? = true) {
        if(resetPageCount) {
            endlessRecyclerOnScrollListener?.resetState()
            shopRequest.pageNo = 0
            binding?.recyclerLatestBooks?.scrollToPosition(0)
        }
        val wonderSharePrefs = WonderPubSharedPrefs.getInstance(context)
        if (wonderSharePrefs.quizFilterLevelSearch.isNotEmpty()) {
            shopRequest.level = wonderSharePrefs.quizFilterLevelSearch
            shopRequest.publisherId = ""
            shopRequest.grade = ""
            shopRequest.syllabus = ""
            shopRequest.subject = ""
            shopRequest.mcqBook = true
        }
        else {
            shopRequest.mcqBook = false
        }
        viewModel.getShopBooks(shopRequest, emitVal)
    }

    private fun clearFilters(emitVal: Boolean? = true) {
        val wonderSharePrefs = WonderPubSharedPrefs.getInstance(context)
        endlessRecyclerOnScrollListener?.resetState()
        shopRequest.pageNo = 0
        binding?.recyclerLatestBooks?.scrollToPosition(0)
        shopRequest.level = ""
        shopRequest.publisherId = ""
        shopRequest.grade = ""
        shopRequest.syllabus = ""
        shopRequest.subject = ""
        shopRequest.mcqBook = false
        viewModel.getShopBooks(shopRequest, emitVal)
    }
    
    private fun initLatestBooksRecycler() {
        binding?.recyclerLatestBooks?.apply {
            showView()
            
            //Show 3 books in row if app is running in a tablet else 2 books
            val gridLayoutManager = GridLayoutManager(requireContext(), if(isTablet()) 3 else 2)
            layoutManager = gridLayoutManager
            adapter = latestBooksAdapter
            
            //For pagination EndlessRecyclerOnScrollListener is required
            endlessRecyclerOnScrollListener = object : EndlessRecyclerOnScrollListener(gridLayoutManager) {
                override fun onLoadMore(page: Int, totalItemsCount: Int, view: RecyclerView?) {
                    shopRequest.pageNo = page
                    requestBooks()
                }
            }.also {
                //Configure scroll listener
                it.setInitialPage(0)
                it.setLoadItemsFrequency(20)
                it.setVisibleThreshold(4)
            }
            //Finally add scroll listener in Recycler view
            addOnScrollListener(endlessRecyclerOnScrollListener!!)
        }
    }

    private fun initClicks() {
        binding?.spinnerLevel?.onItemSelectedByUser = this::handleLevelFilterClick

        binding?.spinnerSyllabus?.onItemSelectedByUser = this::handleSyllabusFilterClick

        binding?.spinnerGrades?.onItemSelectedByUser = this::handleGradesFilterClick

        binding?.spinnerSubjects?.onItemSelectedByUser = this::handleSubjectsFilterClick

        latestBooksAdapter.onBookClickListener = {
            listener?.onBookItemClicked(it, "Shop books list")
        }
        binding?.moreFilters?.setOnClickListener {
            if(shopBooksFragConfig.replaceFilterBtnWithClearBtn) {
                val wonderSharePrefs = WonderPubSharedPrefs.getInstance(context)
                wonderSharePrefs.quizFilterLevelSearch = ""
                resetBooks(1)
            }
            else
                binding?.linearAdditionFilters?.toggleViewVisibility()
        }
        binding?.swipeToRefresh?.setOnRefreshListener {
            binding?.swipeToRefresh?.isRefreshing = false
            resetBooks(0)
        }
    }

    private fun changeFilterBackground(view: AdapterView<*>?) {
        view?.setBackgroundResource(
            if(view.selectedItemPosition == 0 || view.selectedItemPosition == -1)
                R.drawable.custom_spinner_selected_bg_lite
            else
                R.drawable.custom_spinner_selected_bg
        )
    }
    
    private fun initSearchBox() {
        binding?.ebookSearchLayout?.visibility(shopBooksFragConfig.showSearchBoxByDefault)

        binding?.acSearchBox?.setOnFocusChangeListener { v, hasFocus ->
            if (!hasFocus) {
                context?.let {
                    val inputMethodManager = it.getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
                    inputMethodManager.hideSoftInputFromWindow(v.windowToken, 0)
                }
            }
        }

        binding?.acSearchBox?.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                if (!binding?.acSearchBox?.text.isNullOrBlank()) {
                    binding?.acSearchBox?.clearFocus()
                    resetAllFilters()
                    shopRequest.pageNo = 0
                    viewModel.getSearchedBooks(QueryBooksListRequest(binding?.acSearchBox?.text?.trim().toString()))
                    viewModel.logEvent(
                        Event(
                            type = EventType.SHOP_SEARCH,
                            value = Pair("Term", binding?.acSearchBox?.text?.trim().toString())
                        )
                    )
                }
            }
            true
        }

        binding?.acSearchBox?.doOnTextChanged { text, _, before, count ->
            // Check text is not blank and text is entered by user (i.e. don't request suggestion after a suggestion is selected)
            // Below workaround for distinguishing user input or from suggestions check
            //  will fail if user paste text into the search box, so need to find a proper solution
            if(!text.isNullOrBlank() &&
                (before.minus(count) == 1 || before.minus(count) == -1)) {
                viewModel.getSuggestions(SuggestionsRequest(text.toString()))
            }
        }

        binding?.acSearchBox?.setOnItemClickListener { adapterView, _, i, _ ->
            val selectedSearchTerm = adapterView.getItemAtPosition(i) as String
            binding?.acSearchBox?.clearFocus()
            resetAllFilters()
            shopRequest.pageNo = 0
            viewModel.getSearchedBooks(QueryBooksListRequest(selectedSearchTerm))
            viewModel.logEvent(
                Event(
                    type = EventType.SHOP_SEARCH,
                    value = Pair("Term", selectedSearchTerm)
                )
            )
        }
    }

    private fun initObservers() {
        viewModel.levelFilters.observe(requireActivity(), this::setupLevelFilters)
        viewModel.boardFilters.observe(requireActivity(), this::setupSyllabusFilters)
        viewModel.gradeFilters.observe(requireActivity(), this::setupGradeFilters)
        viewModel.subjectFilters.observe(requireActivity(), this::setupSubjectFilters)
        viewModel.suggestions.collectLatestWithLifecycle(this) {
            if(it.responseType == Status.SUCCESSFUL && !binding?.acSearchBox?.text.isNullOrBlank()) {
                val list = it.data ?: listOf()
                val suggestionAdapt = ArrayAdapter(requireContext(), android.R.layout.simple_list_item_1, list)
                binding?.acSearchBox?.apply {
                    setAdapter(suggestionAdapt)
                    if(list.isEmpty()) {
                        showToast(getString(R.string.no_result))
                        dismissDropDown()
                    } else {
                        showDropDown()
                    }
                }
            }
        }
        viewModel.filters.observe(viewLifecycleOwner) {
            when(it) {
                2 -> {
                    syllabusVal()
                    fetchFilters(3)
                }
                3 -> {
                    gradeVal()
                    fetchFilters(4)
                }
                4 -> {
                    subjectVal()
                    fetchFilters(5)
                }
                5 -> {
                    // For enabling filters click
                    binding?.filterOverlay?.hideView()
                    //requestBooks(true, emitVal = false)
                }
                else -> {}
            }
        }
        viewModel.latestBooks.collectLatestWithLifecycle(this) { data ->
            when(data.responseType) {
                Status.LOADING -> {
                    binding?.lvCenter?.showView()
                    binding?.noDatalayout?.hideView()
                    binding?.linearNoData?.hideView()

                    // For disabling filters click
                    binding?.filterOverlay?.showView()
                }
        
                Status.SUCCESSFUL -> {
                    binding?.layoutFilterTop?.showView()
                    binding?.shimmerBooks?.hideView()
                    binding?.lvCenter?.hideView()
                    binding?.swipeToRefresh?.isRefreshing = false

                    if(shopRequest.pageNo == 0)
                        binding?.recyclerLatestBooks?.scrollToPosition(0)

                    val books = data.data ?: arrayListOf()
                    latestBooksAdapter.update(books, pageNo = shopRequest.pageNo)
                    latestBooksAdapter.toggleSeeMoreOverlay(false)
                    binding?.linearNoData?.visibility(books.isEmpty())
                    fragScope?.launch(Dispatchers.IO) {
                        listener?.onShopBooksListUpdated(books)
                    }
                    binding?.filterOverlay?.hideView()
                }
        
                Status.ERROR, Status.HTTP_UNAVAILABLE -> {
                    binding?.shimmerBooks?.hideView()
                    binding?.lvCenter?.hideView()
                    binding?.swipeToRefresh?.isRefreshing = false
                    //Need to inform scroll listener that last request has been failed
                    endlessRecyclerOnScrollListener?.lastRequestFailed()
            
                    // Show error message if adapter is empty
                    if(latestBooksAdapter.itemCount == 0) {
                        if(data.responseType == Status.HTTP_UNAVAILABLE) {
                            binding?.noDatalayout?.showView()
                        } else {
                            binding?.linearNoData?.showView()
                        }
                    } else
                        showToast("Problem while getting books. Please try again.")
                }
                else -> {}
            }
        }
    }

    private fun setupLevelFilters(levels: List<String>) {
        val indexOfLevel = levels.indexOf(shopRequest.level)
        if(levelFilterAdapt == null || binding?.spinnerLevel?.adapter == null) {
            levelFilterAdapt = ArrayAdapter(requireContext(), R.layout.filter_spinner_item, levels.toMutableList())
            levelFilterAdapt?.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            binding?.spinnerLevel?.adapter = levelFilterAdapt
        } else {
            levelFilterAdapt?.update(levels.toMutableList())
        }

        if(indexOfLevel != -1 && binding?.spinnerLevel?.selectedItemPosition != indexOfLevel) {
            binding?.spinnerLevel?.setSelection(indexOfLevel)
            changeFilterBackground(binding?.spinnerLevel)
        }
    }

    private fun setupSyllabusFilters(boards: List<String>) {
        if(syllabusFilterAdapt == null || binding?.spinnerSyllabus?.adapter == null) {
            syllabusFilterAdapt = ArrayAdapter(requireContext(), R.layout.filter_spinner_item, boards.toMutableList())
            syllabusFilterAdapt?.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            binding?.spinnerSyllabus?.adapter = syllabusFilterAdapt
            showPreSelectedSyllabusFilter()
        } else {
            syllabusFilterAdapt?.update(boards.toMutableList())
            showPreSelectedSyllabusFilter()
        }
        binding?.spinnerSyllabus?.apply {
            isEnabled = boards.size != 1
        }
    }

    private fun setupGradeFilters(grades: List<String>) {
        if(gradeFilterAdapt == null || binding?.spinnerGrades?.adapter == null) {
            gradeFilterAdapt = ArrayAdapter(requireContext(), R.layout.filter_spinner_item, grades.toMutableList())
            gradeFilterAdapt?.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            binding?.spinnerGrades?.adapter = gradeFilterAdapt
            showPreSelectedGradeFilter()
        } else {
            gradeFilterAdapt?.update(grades.toMutableList())
            showPreSelectedGradeFilter()
        }
        val isGradesAvailable = grades.size > 1
        binding?.spinnerGrades?.apply {
            isEnabled = isGradesAvailable
        }
        binding?.linearAdditionFilters?.visibility(isGradesAvailable)
    }

    private fun setupSubjectFilters(subjects: List<String>) {
        if(subjectFilterAdapt == null || binding?.spinnerSubjects?.adapter == null) {
            subjectFilterAdapt = ArrayAdapter(requireContext(), R.layout.filter_spinner_item, subjects.toMutableList())
            subjectFilterAdapt?.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            binding?.spinnerSubjects?.adapter = subjectFilterAdapt
            showPreSelectedSchoolFilter()
        } else {
            subjectFilterAdapt?.update(subjects.toMutableList())
            showPreSelectedSchoolFilter()
            resetFilter = false
        }
        binding?.spinnerSubjects?.apply {
            isEnabled = subjects.size != 1
        }
    }

    private fun handleLevelFilterClick(view: AdapterView<*>?, pos: Int) {
        val wonderSharePrefs = WonderPubSharedPrefs.getInstance(context)
        wonderSharePrefs.quizFilterLevelSearch = ""
        if(pos == 0) {
            wonderPubSharedPrefs.sharedPrefsLastFilterLevelSearch = ""
            selectedLevelPre = ""
            wonderPubSharedPrefs.sharedPrefsLastFilterSyllabusSearch = ""
            selectedSyllabusPre = ""
            wonderPubSharedPrefs.sharedPrefsLastFilterGradeSearch = ""
            selectedGradePre = ""
            wonderPubSharedPrefs.sharedPrefsLastFilterSubjectSearch = ""
            selectedSchoolPre = ""
            resetAllFilters()
            booksAndFilters(true, 2, observe = false, emitVal = false)
            changeFilterBackground(view)

        } else {
            val selectedLevel = binding?.spinnerLevel?.adapter?.getItem(pos) as? String
            selectedLevel?.let {
                wonderPubSharedPrefs.sharedPrefsLastFilterSyllabusSearch = ""
                selectedSyllabusPre = ""
                wonderPubSharedPrefs.sharedPrefsLastFilterGradeSearch = ""
                selectedGradePre = ""

                wonderPubSharedPrefs.sharedPrefsLastFilterSubjectSearch = ""
                selectedSchoolPre = ""

                shopRequest.level = it
                resetSyllabusFilterUI()
                resetGradeFilterUI()
                resetSubjectFilterUI()
                booksAndFilters(true, 2, observe = false, emitVal = true)
                changeFilterBackground(view)

                // Show more/clear filter button when at least one filter is selected
                binding?.moreFilters?.showView()

                viewModel.logEvent(
                    Event(
                        type = EventType.LEVEL_FILTER_CHANGED,
                        value = Pair("filter", it)
                    )
                )
            }
            wonderPubSharedPrefs.sharedPrefsLastFilterLevelSearch = selectedLevel
            if (selectedLevel != null) {
                selectedLevelPre = selectedLevel
            }

        }
      //  resetValues()
    }

    private fun handleSyllabusFilterClick(view: AdapterView<*>?, pos: Int) {
        val wonderSharePrefs = WonderPubSharedPrefs.getInstance(context)
        wonderSharePrefs.quizFilterLevelSearch = ""
        if(pos == 0) {
            wonderPubSharedPrefs.sharedPrefsLastFilterSyllabusSearch = ""
            selectedSyllabusPre = ""
            wonderPubSharedPrefs.sharedPrefsLastFilterGradeSearch = ""
            selectedGradePre = ""

            wonderPubSharedPrefs.sharedPrefsLastFilterSubjectSearch = ""
            selectedSchoolPre = ""
            resetSyllabusFilterUI()
            resetGradeFilterUI()
            resetSubjectFilterUI()
            booksAndFilters(true, 3, observe = false, emitVal = false)
            changeFilterBackground(view)

        } else {
            val selectedSyllabus = binding?.spinnerSyllabus?.adapter?.getItem(pos) as? String
            selectedSyllabus?.let {
                wonderPubSharedPrefs.sharedPrefsLastFilterGradeSearch = ""
                selectedGradePre = ""

                wonderPubSharedPrefs.sharedPrefsLastFilterSubjectSearch = ""
                selectedSchoolPre = ""
                shopRequest.syllabus = it
                resetGradeFilterUI()
                resetSubjectFilterUI()
                booksAndFilters(true, 3, observe = false, emitVal = false)
                changeFilterBackground(view)

                viewModel.logEvent(
                    Event(
                        type = EventType.SYLLABUS_FILTER_CHANGED,
                        value = Pair("filter", it)
                    )
                )
            }
            wonderPubSharedPrefs.sharedPrefsLastFilterSyllabusSearch = selectedSyllabus
            if (selectedSyllabus != null) {
                selectedSyllabusPre = selectedSyllabus
            }

        }
    }

    private fun handleGradesFilterClick(view: AdapterView<*>?, pos: Int) {
        val wonderSharePrefs = WonderPubSharedPrefs.getInstance(context)
        wonderSharePrefs.quizFilterLevelSearch = ""
        if(pos == 0) {
            resetGradeFilterUI()
            resetSubjectFilterUI()
            booksAndFilters(true, 4, observe = false, emitVal = false)
            changeFilterBackground(view)
            wonderPubSharedPrefs.sharedPrefsLastFilterGradeSearch = ""
            selectedGradePre = ""

            wonderPubSharedPrefs.sharedPrefsLastFilterSubjectSearch = ""
            selectedSchoolPre = ""
        } else {
            val selectedGrade = binding?.spinnerGrades?.adapter?.getItem(pos) as? String
            selectedGrade?.let {

                wonderPubSharedPrefs.sharedPrefsLastFilterSubjectSearch = ""
                selectedSchoolPre = ""
                shopRequest.grade = it
                resetSubjectFilterUI()
                booksAndFilters(true, 4, observe = false, emitVal = false)
                changeFilterBackground(view)

                viewModel.logEvent(
                    Event(
                        type = EventType.GRADE_FILTER_CHANGED,
                        value = Pair("filter", it)
                    )
                )
            }
        }
    }


    private fun handleSubjectsFilterClick(view: AdapterView<*>?, pos: Int) {
        val wonderSharePrefs = WonderPubSharedPrefs.getInstance(context)
        wonderSharePrefs.quizFilterLevelSearch = ""
        if(pos == 0) {
            resetSubjectFilterUI()
            requestBooks(true, emitVal = false)
            changeFilterBackground(view)
            wonderPubSharedPrefs.sharedPrefsLastFilterSubjectSearch = ""
            selectedSchoolPre = ""
        } else {
            val selectedSubject = binding?.spinnerSubjects?.adapter?.getItem(pos) as? String

            selectedSubject?.let {
                shopRequest.subject = it
                requestBooks(true, emitVal = false)
                changeFilterBackground(view)
            }

            wonderPubSharedPrefs.sharedPrefsLastFilterSubjectSearch = selectedSubject
            if (selectedSubject != null) {
                selectedSchoolPre = selectedSubject
            }
        }
    }
    
    private fun resetAllFilters() {
        WonderPubSharedPrefs.getInstance(context).quizFilterLevelSearch = ""
        resetLevelFilterUI()
        resetSyllabusFilterUI()
        resetGradeFilterUI()
        resetSubjectFilterUI()
        binding?.apply {
            spinnerSyllabus.isEnabled = false
            spinnerGrades.isEnabled = false
            spinnerSubjects.isEnabled = false
            linearAdditionFilters.hideView()
        }
    }
    
    private fun resetLevelFilterUI() {
        shopRequest.level = ""
        binding?.spinnerLevel?.setSelection(0, true)
        changeFilterBackground(binding?.spinnerLevel)

        // Hide more/clear filter button when none filter is selected
        binding?.moreFilters?.hideView()
    }
    
    private fun resetSyllabusFilterUI() {
        shopRequest.syllabus = ""
        binding?.spinnerSyllabus?.setSelection(0, true)
        changeFilterBackground(binding?.spinnerSyllabus)
    }
    
    private fun resetGradeFilterUI() {
        shopRequest.grade = ""
        binding?.spinnerGrades?.setSelection(0, true)
        changeFilterBackground(binding?.spinnerGrades)
    }
    
    private fun resetSubjectFilterUI() {
        shopRequest.subject = ""
        binding?.spinnerSubjects?.setSelection(0, true)
        changeFilterBackground(binding?.spinnerSubjects)
    }

    private fun validateFilters() {
        var changed = false
        val selectedSyllabus = wonderPubSharedPrefs.sharedPrefsLastFilterSyllabusSearch
        val selectedGrade = wonderPubSharedPrefs.sharedPrefsLastFilterGradeSearch
        val selectedSubject = wonderPubSharedPrefs.sharedPrefsLastFilterSubjectSearch
        if (selectedSyllabus != selectedSyllabusPre)
        {
            selectedSyllabusPre = selectedSyllabus
            changed = true
        }
        if (selectedGrade != selectedGradePre)
        {
            selectedGradePre = selectedGrade
            changed = true
        }
        if (selectedSubject != selectedSchoolPre)
        {
            selectedSchoolPre = selectedSubject
            changed = true
        }
        if (changed) {
            load()
        }
    }

    override fun whenResumed() {
        validateFilters()
    }

    companion object {

        private const val PARAM_SHOP_BOOKS_FRAG_CONFIG = "shopBooksFragConfig"

        @JvmStatic
        fun newInstance(shopBooksFragConfig: ShopBooksFragConfig) = ShopBooksFrag().also {
            it.arguments = bundleOf(PARAM_SHOP_BOOKS_FRAG_CONFIG to shopBooksFragConfig)
        }
    }

    interface ShopBooksFragmentListener {
        fun onBookItemClicked(bookData: ShopBookData, from: String)
        fun onShopBooksListUpdated(books: List<ShopBookData>) {}
    }

}