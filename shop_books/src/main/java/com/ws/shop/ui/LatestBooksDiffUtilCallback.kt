package com.ws.shop.ui

import androidx.recyclerview.widget.DiffUtil
import com.ws.commons.models.ShopBookData

class LatestBooksDiffUtilCallback(
    private val oldItems: List<ShopBookData>,
    private val newItems: List<ShopBookData>
): DiffUtil.Callback() {
    
    override fun getOldListSize(): Int {
        return oldItems.size
    }
    
    override fun getNewListSize(): Int {
        return newItems.size
    }
    
    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return oldItems[oldItemPosition].id == newItems[newItemPosition].id
    }
    
    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return oldItems[oldItemPosition] == newItems[newItemPosition]
    }
}