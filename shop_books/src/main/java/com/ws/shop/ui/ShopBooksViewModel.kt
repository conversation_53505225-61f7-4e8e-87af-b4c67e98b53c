package com.ws.shop.ui

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ws.commons.Data
import com.ws.commons.Result
import com.ws.commons.Status
import com.ws.commons.enums.Flavor
import com.ws.commons.exceptions.NoInternetException
import com.ws.commons.extensions.isNullOrBlankOrContains
import com.ws.commons.extensions.sortByIntOrString
import com.ws.commons.interfaces.EventLogger
import com.ws.commons.interfaces.FlavorConfig
import com.ws.commons.models.Event
import com.ws.commons.models.ShopBookData
import com.ws.shop.data.models.*
import com.ws.shop.data.repositories.ShopBooksRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class ShopBooksViewModel(
    private val shopBooksRepository: ShopBooksRepository,
    private val eventLogger: EventLogger
) : ViewModel() {

    private val _latestBooks = MutableStateFlow<Data<List<ShopBookData>>>(Data(responseType = Status.LOADING))
    val latestBooks = _latestBooks.asStateFlow()

    private val _levelFilters = MutableLiveData<List<String>>()
    val levelFilters: LiveData<List<String>> get() = _levelFilters

    private val _boardFilters = MutableLiveData<List<String>>()
    val boardFilters: LiveData<List<String>> get() = _boardFilters

    private val _gradeFilters = MutableLiveData<List<String>>()
    val gradeFilters: LiveData<List<String>> get() = _gradeFilters

    private val _subjectFilters = MutableLiveData<List<String>>()
    val subjectFilters: LiveData<List<String>> get() = _subjectFilters
    
    private val _suggestions = MutableSharedFlow<Data<List<String>>>()
    val suggestions = _suggestions.asSharedFlow()

    private val _filters = MutableLiveData<Int>()
    val filters: LiveData<Int> get() = _filters

    var useLocalLevelFilters = false

    private var fetchSuggestionsJob: Job? = null

    private val localLevelFilters = arrayListOf(
        "College", "Competitive Exams", "Engineering Entrances", "General", "Government Recruitments", "ITI & Polytechnic",
        "Magazine", "Medical Entrances", "School"
    )

    private val localLevelFiltersKICX = arrayListOf(
        "Competitive Exams", "Concept through MCQs", "General", "Government Recruitments", "NCERT Foundation",
        "School"
    )

    /**
     * Returns the list of books, tags and other information based on params set in [booksRequest]
     * Emits the value to [_latestBooks] which is observed by [latestBooks]
     * Also invokes the function [updateFilters] based on the param [emitVal]
     * @param emitVal if true [updateFilters] is invoked
     */
    fun getShopBooks(booksRequest: ShopBooksRequest,
                     emitVal: Boolean? = true) = viewModelScope.launch(Dispatchers.IO) {
        _latestBooks.emit(
            Data(
                responseType = Status.LOADING,
                data = _latestBooks.value.data
            )
        )
        when(val result = shopBooksRepository.getLatestBooksList(booksRequest)) {
            is Result.Success -> {
                //Update books list
                val books = if(booksRequest.pageNo == 0) {
                    arrayListOf()
                } else {
                    _latestBooks.value.data?.toMutableList() ?: arrayListOf()
                }
                books.addAll(result.data.books)
                _latestBooks.emit(
                    Data(
                        responseType = Status.SUCCESSFUL,
                        data = books
                    )
                )
                if (emitVal == true) {
                    //Update filters
                    updateFilters(result.data)
                }
            }

            is Result.Failure -> {
                _latestBooks.emit(
                    Data(
                        responseType = if(result.exception is NoInternetException) Status.HTTP_UNAVAILABLE else Status.ERROR,
                        data = _latestBooks.value.data,
                        error = result.exception
                    )
                )
            }
        }
    }

    /**
     * Returns [LatestBooksResponse] and filters the values for [_levelFilters],[_boardFilters]
     * [_gradeFilters],[_subjectFilters]
     * @param sequence updates respective filters
     * 2 -> [_boardFilters]
     * 3 -> [_gradeFilters]
     * 4 -> [_subjectFilters]
     */
    fun getFilters(booksRequest: ShopBooksRequest, sequence: Int) = viewModelScope.launch {
        when(val result = shopBooksRepository.getLatestBooksList(booksRequest)) {
            is Result.Success -> {
                _filters.value = sequence
                when(sequence) {
                    2 -> updateSyllabus(result.data)
                    3 -> updateGrades(result.data)
                    4 -> updateSubjects(result.data)
                    else -> {}
                }
            }
            is Result.Failure -> {
                _filters.value = sequence
            }
        }
    }

    private fun clearFilters() {
        val boards = mutableSetOf("Select Board")
        val grades = mutableSetOf("Select Grade")
        val subjects = mutableSetOf("All Subjects")
        _boardFilters.value = boards.toList()
        _gradeFilters.value = grades.toList()
        _subjectFilters.value = subjects.toList()
    }

    private suspend fun updateSyllabus(response: LatestBooksResponse) = withContext(Dispatchers.IO) {
        val boards = mutableSetOf<String>()
        val levels = mutableSetOf<String>()
        val isLevelNotSelected = response.level.isNullOrBlankOrContains("null")
        val isSyllabusNotSelected = response.syllabus.isNullOrBlankOrContains("null")
        if(useLocalLevelFilters) {
            if (Flavor.KIRAN.siteId.equals("71")) {
                levels.addAll(localLevelFiltersKICX)
            }
            else {
                levels.addAll(localLevelFilters)
            }
        }
        response.bookTags.forEach { data ->
            //No need to show other filters unless level filter is selected
            if(!isLevelNotSelected) {
                data.syllabus?.let {
                    boards.add(it)
                }
            }
        }

        withContext(Dispatchers.Main) {
            //Update levels list only if it is not selected
            if (isLevelNotSelected || useLocalLevelFilters) {
                val sortedLevels = levels.sortByIntOrString()
                sortedLevels.add(0, "Select Level")
                _levelFilters.value = sortedLevels
            }
            //Update syllabus list only if it is not selected
            if (isSyllabusNotSelected || _boardFilters.value == null) {
                val sortedBoards = boards.sortByIntOrString()
                sortedBoards.add(0, "Select Board")
                _boardFilters.value = sortedBoards
            }
        }
    }

    private suspend fun updateGrades(response: LatestBooksResponse) = withContext(Dispatchers.IO) {
        val grades = mutableSetOf<String>()
        val isSyllabusNotSelected = response.syllabus.isNullOrBlankOrContains("null")
        val isGradeNotSelected = response.grade.isNullOrBlankOrContains("null")
        response.bookTags.forEach { data ->
            if(!isSyllabusNotSelected) {
                data.grade?.let {
                    grades.add(it)
                }
            }
        }
        withContext(Dispatchers.Main) {
            //Update grades list only if it is not selected
            if (isGradeNotSelected || _gradeFilters.value == null || grades.isNotEmpty()) {
                val sortedGrades = grades.sortByIntOrString()
                sortedGrades.add(0, "Select Grade")
                _gradeFilters.value = sortedGrades
            }
        }
    }

    private suspend fun updateSubjects(response: LatestBooksResponse) = withContext(Dispatchers.IO) {
        val subjects = mutableSetOf<String>()
        val isGradeNotSelected = response.grade.isNullOrBlankOrContains("null")
        val isSubjectNotSelected = response.subject.isNullOrBlankOrContains("null")
        response.bookTags.forEach { data ->
            if(!isGradeNotSelected) {
                data.subject?.let {
                    subjects.add(it)
                }
            }
        }
        withContext(Dispatchers.Main) {
            //Update subjects list only if it is not selected
            if (isSubjectNotSelected || _subjectFilters.value == null || subjects.isNotEmpty()) {
                val sortedSubjects = subjects.sortByIntOrString()
                sortedSubjects.add(0, "All Subjects")
                _subjectFilters.value = sortedSubjects
            }
        }
    }
    
    private suspend fun updateFilters(response: LatestBooksResponse) = withContext(Dispatchers.IO) {
        val levels = mutableSetOf<String>()
        val boards = mutableSetOf<String>()
        val grades = mutableSetOf<String>()
        val subjects = mutableSetOf<String>()

        if(useLocalLevelFilters) {
            if (Flavor.KIRAN.siteId.equals("71")) {
                levels.addAll(localLevelFiltersKICX)
            }
            else {
                levels.addAll(localLevelFilters)
            }
        }
        
        val isLevelNotSelected = response.level.isNullOrBlankOrContains("null")
        val isSyllabusNotSelected = response.syllabus.isNullOrBlankOrContains("null")
        val isGradeNotSelected = response.grade.isNullOrBlankOrContains("null")
        val isSubjectNotSelected = response.subject.isNullOrBlankOrContains("null")
        response.bookTags.forEach { data ->
            data.level?.let {
                levels.add(it)
            }

            //No need to show other filters unless level filter is selected
            if(!isLevelNotSelected) {
                data.syllabus?.let {
                    boards.add(it)
                }

                //No need to show other filters unless level & syllabus filter is selected
                if(!isSyllabusNotSelected) {
                    data.grade?.let {
                        grades.add(it)
                    }

                    //No need to show other filters unless level, syllabus & grade filter is selected
                    if(!isGradeNotSelected) {
                        data.subject?.let {
                            subjects.add(it)
                        }
                    }
                }

            }
        }
        withContext(Dispatchers.Main) {
            //Update levels list only if it is not selected
            if (isLevelNotSelected || useLocalLevelFilters) {
                val sortedLevels = levels.sortByIntOrString()
                sortedLevels.add(0, "Select Level")
                _levelFilters.value = sortedLevels
            }

            //Update syllabus list only if it is not selected
            if (isSyllabusNotSelected || _boardFilters.value == null) {
                val sortedBoards = boards.sortByIntOrString()
                sortedBoards.add(0, "Select Board")
                _boardFilters.value = sortedBoards
            }

            //Update grades list only if it is not selected
            if (isGradeNotSelected || _gradeFilters.value == null) {
                val sortedGrades = grades.sortByIntOrString()
                sortedGrades.add(0, "Select Grade")
                _gradeFilters.value = sortedGrades
            }

            //Update subjects list only if it is not selected
            if (isSubjectNotSelected || _subjectFilters.value == null) {
                val sortedSubjects = subjects.sortByIntOrString()
                sortedSubjects.add(0, "All Subjects")
                _subjectFilters.value = sortedSubjects
            }
        }
    }
    
    fun getSuggestions(suggestionsRequest: SuggestionsRequest) {
        fetchSuggestionsJob?.cancel()
        fetchSuggestionsJob = viewModelScope.launch {
            _suggestions.emit(
                Data(
                    responseType = Status.LOADING,
                    data = listOf()
                )
            )
            when(val result = shopBooksRepository.getSuggestions(suggestionsRequest)) {
                is Result.Success -> {
                    _suggestions.emit(
                        Data(
                            responseType = Status.SUCCESSFUL,
                            data = result.data.searchList
                        )
                    )
                }

                is Result.Failure -> {
                    _suggestions.emit(
                        Data(
                            responseType = if(result.exception is NoInternetException) Status.HTTP_UNAVAILABLE else Status.ERROR,
                            error = result.exception
                        )
                    )
                }
            }
        }
    }

    fun getSearchedBooks(queryBooksListRequest: QueryBooksListRequest) = viewModelScope.launch {
        _latestBooks.emit(
            Data(
                responseType = Status.LOADING,
                data = _latestBooks.value.data
            )
        )
        when(val result = shopBooksRepository.getSearchedBooksList(queryBooksListRequest)) {
            is Result.Success -> {
                //Reset filters
                clearFilters()

                //Update books
                _latestBooks.emit(
                    Data(
                        responseType = Status.SUCCESSFUL,
                        data = result.data.books
                    )
                )
            }

            is Result.Failure -> {
                _latestBooks.emit(
                    Data(
                        responseType = if(result.exception is NoInternetException) Status.HTTP_UNAVAILABLE else Status.ERROR,
                        data = listOf(),
                        error = result.exception
                    )
                )
            }
        }
    }

    fun logEvent(event: Event) {
        eventLogger.logEvent(event)
    }

}