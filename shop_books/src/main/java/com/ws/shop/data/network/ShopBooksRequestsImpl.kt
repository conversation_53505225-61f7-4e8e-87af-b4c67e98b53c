package com.ws.shop.data.network

import com.ws.commons.interfaces.TokenProvider
import com.ws.networking.NetworkRequest
import com.ws.commons.models.NetworkConfig
import com.ws.networking.utils.ApiParams
import com.ws.shop.data.models.QueryBooksListRequest
import com.ws.shop.data.models.SuggestionsRequest
import com.ws.shop.data.models.ShopBooksRequest
import com.ws.shop.data.network.ShopBooksRequests.ShopBooksListEndpoints

internal class ShopBooksRequestsImpl(
    private val tokenProvider: TokenProvider,
    private val networkConfig: NetworkConfig,
): ShopBooksRequests {

    override fun getBooksListRequest(shopBooksRequest: ShopBooksRequest) = NetworkRequest(
        baseUrl = networkConfig.service1,
        apiEndPoint = ShopBooksListEndpoints.LATEST_BOOKS_LIST_ENDPOINT,
        siteId = networkConfig.siteId,
        token = tokenProvider.getToken(),
        params = hashMapOf(
            ApiParams.PARAM_FILTER_LEVEL to shopBooksRequest.level,
            ApiParams.PARAM_FILTER_GRADE to shopBooksRequest.grade,
            ApiParams.PARAM_FILTER_SUBJECT to shopBooksRequest.subject,
            ApiParams.PARAM_FILTER_SYLLABUS to shopBooksRequest.syllabus,
            ApiParams.PARAM_PAGE_NUMBER to (shopBooksRequest.pageNo.toString()),
            ApiParams.PARAM_PUBLISHER_ID to shopBooksRequest.publisherId,
                ApiParams.PARAM_MCQBooks to shopBooksRequest.mcqBook.toString()
        )
    )

    override fun getSearchSuggestionsRequest(suggestionsRequest: SuggestionsRequest) = NetworkRequest(
        baseUrl = networkConfig.service1,
        apiEndPoint = ShopBooksListEndpoints.GET_SHOP_SEARCH_SUGGESTIONS,
        siteId = networkConfig.siteId,
        token = tokenProvider.getToken(),
        params = hashMapOf(
            ApiParams.PARAM_SEARCH_QUERY to suggestionsRequest.query
        )
    )

    override fun getSearchedBooksListRequest(queryBooksListRequest: QueryBooksListRequest) = NetworkRequest (
        baseUrl = networkConfig.service1,
        apiEndPoint = ShopBooksListEndpoints.GET_SHOP_SEARCHED_BOOKS,
        siteId = networkConfig.siteId,
        token = tokenProvider.getToken(),
        params = hashMapOf(
            ApiParams.PARAM_SEARCH_STRING to queryBooksListRequest.searchString
        )
    )

    override fun getSiteLevelBooks() = NetworkRequest (
        baseUrl = networkConfig.service1,
        apiEndPoint = ShopBooksListEndpoints.GET_SITE_LEVEL_BOOKS,
        siteId = networkConfig.siteId,
        token = tokenProvider.getToken()
    )

}