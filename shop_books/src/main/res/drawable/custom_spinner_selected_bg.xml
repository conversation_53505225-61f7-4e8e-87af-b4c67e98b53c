<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="2dp"
                android:left="2dp"
                android:right="2dp"
                android:top="2dp" />

            <solid android:color="#00FBE174" />

            <corners android:radius="8dp" />

        </shape>
    </item>

    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="2dp"
                android:left="2dp"
                android:right="2dp"
                android:top="2dp" />

            <solid android:color="#00FBE174" />

            <corners android:radius="8dp" />
        </shape>
    </item>

    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="2dp"
                android:left="2dp"
                android:right="2dp"
                android:top="2dp" />

            <solid android:color="@color/colorAccent" />

            <corners android:radius="8dp" />

            <gradient
                android:angle="180"
                android:centerColor="@color/yellow"
                android:endColor="@color/yellow"
                android:startColor="@color/yellow"
                android:type="linear" />

        </shape>
    </item>

    <item
        android:bottom="1dp"
        android:left="1dp"
        android:right="1dp"
        android:top="1dp">
        <shape android:shape="rectangle">
            <solid android:color="@color/colorAccent" />
            <corners android:radius="3dp" />
            <gradient
                android:angle="180"
                android:centerColor="@color/yellow"
                android:endColor="@color/yellow"
                android:startColor="@color/yellow"
                android:type="linear" />
        </shape>
    </item>
    <item android:gravity="center_vertical|right">
        <layer-list>
            <item>
                <bitmap
                    android:gravity="center_vertical|right"
                    android:src="@drawable/ic_arrow_drop_down_white" />

            </item>
        </layer-list>
    </item>
</layer-list>