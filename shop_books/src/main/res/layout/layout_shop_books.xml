<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorShopBg">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorShopBg">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior"
            android:animateLayoutChanges="true"
            app:layout_collapseMode="pin">

            <LinearLayout
                android:id="@+id/ebook_search_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"
                android:layout_marginTop="8dp"
                android:layout_marginHorizontal="4dp"
                android:orientation="vertical">

                <AutoCompleteTextView
                    android:id="@+id/acSearchBox"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginLeft="12dp"
                    android:layout_marginRight="12dp"
                    android:background="@drawable/all_course_list_item_background"
                    android:hint="Search title, subject, author, ISBN, language etc."
                    android:maxLines="1"
                    android:layout_marginBottom="7dp"
                    android:dropDownHeight="300dp"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"
                    android:singleLine="true"
                    android:textSize="12sp"
                    android:textColor="@color/colorShopSearchText"
                    android:textColorHint="@color/colorShopSearchHint"
                    android:imeOptions="actionSearch"
                    android:inputType="text"
                    android:visibility="visible" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/searchHistoryRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="15dp"
                    android:layout_marginRight="15dp"
                    android:background="@drawable/filter_dialog_background"
                    android:nestedScrollingEnabled="false"
                    android:padding="3dp"
                    android:scrollbars="none"
                    android:visibility="gone" />

                <ImageView
                    android:id="@+id/imageView_close_history"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:background="@color/white"
                    android:padding="7dp"
                    android:src="@drawable/ic_close_red"
                    android:visibility="gone" />
            </LinearLayout>

            <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                android:id="@+id/swipeToRefresh"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@id/ebook_search_layout">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <!--<androidx.core.widget.NestedScrollView
                        android:id="@+id/nsvGlobal"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">-->

                        <RelativeLayout
                            android:id="@+id/nsvGlobal"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent">

                            <RelativeLayout
                                android:id="@+id/layout_top"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginTop="3dp"
                                android:animateLayoutChanges="true"
                                android:clickable="true"
                                android:focusable="true"
                                android:focusableInTouchMode="true"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:layout_margin="8dp"
                                    android:text="or"
                                    android:textSize="14dp"
                                    android:textStyle="bold"
                                    android:visibility="gone" />

                                <LinearLayout
                                    android:id="@+id/layout_filter_top"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginHorizontal="4dp"
                                    android:layout_marginTop="4dp"
                                    android:animateLayoutChanges="true"
                                    android:orientation="horizontal"
                                    android:visibility="gone"
                                    tools:visibility="visible">

                                    <com.ws.core_ui.custom_views.CustomSpinner
                                        android:id="@+id/spinnerLevel"
                                        style="@style/CustomSpinner"
                                        android:layout_width="0dp"
                                        android:layout_height="40dp"
                                        android:layout_marginLeft="10dp"
                                        android:layout_marginRight="8dp"
                                        android:layout_weight="1"
                                        android:background="@drawable/custom_spinner_selected_bg_lite"
                                        android:overlapAnchor="false" />

                                    <com.ws.core_ui.custom_views.CustomSpinner
                                        android:id="@+id/spinnerSyllabus"
                                        style="@style/CustomSpinner"
                                        android:layout_width="0dp"
                                        android:layout_height="40dp"
                                        android:layout_marginLeft="8dp"
                                        android:layout_marginRight="10dp"
                                        android:layout_weight="1"
                                        android:background="@drawable/custom_spinner_selected_bg_lite"
                                        android:overlapAnchor="false" />

                                    <ImageView
                                        android:id="@+id/moreFilters"
                                        android:layout_width="35dp"
                                        android:layout_height="35dp"
                                        android:layout_gravity="center|right"
                                        android:layout_marginRight="10dp"
                                        android:foreground="?selectableItemBackground"
                                        android:src="@drawable/ic_filter_accent_color"
                                        android:visibility="gone" />
                                </LinearLayout>

                                <LinearLayout
                                    android:id="@+id/linearAdditionFilters"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_below="@+id/layout_filter_top"
                                    android:layout_marginHorizontal="4dp"
                                    android:orientation="vertical"
                                    android:visibility="gone"
                                    tools:visibility="visible">

                                    <LinearLayout
                                        android:id="@+id/layout_filter_down"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:orientation="horizontal">

                                        <androidx.cardview.widget.CardView
                                            android:id="@+id/containerGrades"
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:layout_weight="1"
                                            android:backgroundTint="@color/colorShopBg"
                                            android:elevation="0dp"
                                            app:cardElevation="0dp"
                                            app:contentPadding="8dp">

                                            <com.ws.core_ui.custom_views.CustomSpinner
                                                android:id="@+id/spinner_grades"
                                                style="@style/CustomSpinner"
                                                android:layout_width="match_parent"
                                                android:layout_height="40dp"
                                                android:layout_marginLeft="3dp"
                                                android:background="@drawable/custom_spinner_selected_bg_lite"
                                                android:overlapAnchor="false" />

                                        </androidx.cardview.widget.CardView>

                                        <androidx.cardview.widget.CardView
                                            android:id="@+id/containerSubjects"
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:layout_weight="1"
                                            android:backgroundTint="@color/colorShopBg"
                                            android:elevation="0dp"
                                            app:cardElevation="0dp"
                                            app:contentPadding="8dp">

                                            <com.ws.core_ui.custom_views.CustomSpinner
                                                android:id="@+id/spinnerSubjects"
                                                style="@style/CustomSpinner"
                                                android:layout_width="match_parent"
                                                android:layout_height="40dp"
                                                android:layout_marginEnd="3dp"
                                                android:background="@drawable/custom_spinner_selected_bg_lite"
                                                android:overlapAnchor="false" />

                                        </androidx.cardview.widget.CardView>

                                    </LinearLayout>

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"
                                        android:text="or"
                                        android:textSize="14sp"
                                        android:textStyle="bold"
                                        android:visibility="gone" />

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="3dp"
                                        android:layout_marginEnd="3dp"
                                        android:visibility="gone">

                                        <androidx.cardview.widget.CardView
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:elevation="0dp"
                                            app:cardElevation="0dp"
                                            app:contentPaddingLeft="8dp"
                                            app:contentPaddingRight="8dp">

                                            <androidx.appcompat.widget.AppCompatSpinner
                                                android:id="@+id/publisherSpinner"
                                                style="@style/CustomSpinner"
                                                android:layout_width="match_parent"
                                                android:layout_height="40dp"
                                                android:background="@drawable/custom_spinner_selected_bg_lite"
                                                android:overlapAnchor="false" />

                                        </androidx.cardview.widget.CardView>
                                    </LinearLayout>

                                </LinearLayout>

                                <View
                                    android:id="@+id/filterOverlay"
                                    android:layout_width="match_parent"
                                    android:layout_height="0dp"
                                    android:layout_above="@+id/recyclerLatestBooks"
                                    android:layout_alignParentTop="true"
                                    android:background="@color/transparent"
                                    android:clickable="true"
                                    android:focusable="true"
                                    android:visibility="gone" />

                                <com.ws.core_ui.custom_views.WSTextView
                                    android:id="@+id/textLatestRelease"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_below="@+id/linearAdditionFilters"
                                    android:layout_marginStart="3dp"
                                    android:layout_marginTop="15dp"
                                    android:background="#F4F4F4"
                                    android:ellipsize="end"
                                    android:gravity="center|left"
                                    android:maxWidth="220dp"
                                    android:maxLines="1"
                                    android:paddingStart="12dp"
                                    android:paddingEnd="12dp"
                                    android:textSize="18sp"
                                    android:textStyle="bold"
                                    android:visibility="gone"
                                    app:autoSizeMaxTextSize="20sp"
                                    app:autoSizeMinTextSize="12sp"
                                    app:autoSizeTextType="uniform"
                                    app:enableGradient="true"
                                    app:ws_font_weight="bold"
                                    tools:text="Latest Releases" />

                                <androidx.recyclerview.widget.RecyclerView
                                    android:id="@+id/recyclerLatestBooks"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_below="@+id/textLatestRelease"
                                    android:layout_marginTop="10dp"
                                    android:background="@color/colorShopBg"
                                    android:clipToPadding="false"
                                    android:paddingStart="12dp"
                                    android:paddingEnd="12dp"
                                    android:paddingBottom="70dp"
                                    tools:listitem="@layout/item_ebook_info_new" />

                                <ProgressBar
                                    android:id="@+id/idPBLoading"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_below="@+id/recyclerLatestBooks"
                                    android:visibility="gone" />

                                <com.facebook.shimmer.ShimmerFrameLayout
                                    android:id="@+id/shimmerBooks"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_below="@+id/textLatestRelease"
                                    android:padding="4dp"
                                    android:visibility="visible"
                                    tools:visibility="gone">

                                    <include
                                        layout="@layout/shimmer_empty_book_layout"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content" />

                                    <!--<LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginLeft="15dp"
                                        android:orientation="vertical">

                                        <LinearLayout
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:orientation="horizontal">

                                            <include
                                                layout="@layout/shimmer_empty_layout"

                                                android:layout_width="match_parent"
                                                android:layout_height="match_parent"
                                                android:layout_weight="1" />

                                            <include
                                                layout="@layout/shimmer_empty_layout"
                                                android:layout_width="match_parent"
                                                android:layout_height="match_parent"
                                                android:layout_weight="1" />
                                        </LinearLayout>

                                        <LinearLayout
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:orientation="horizontal">

                                            <include
                                                layout="@layout/shimmer_empty_layout"
                                                android:layout_width="match_parent"
                                                android:layout_height="match_parent"
                                                android:layout_weight="1" />

                                            <include
                                                layout="@layout/shimmer_empty_layout"
                                                android:layout_width="match_parent"
                                                android:layout_height="match_parent"
                                                android:layout_weight="1" />
                                        </LinearLayout>
                                    </LinearLayout>-->
                                </com.facebook.shimmer.ShimmerFrameLayout>

                            </RelativeLayout>
                            </RelativeLayout>
                    <!--</androidx.core.widget.NestedScrollView>-->
                </RelativeLayout>
            </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

            <LinearLayout
                android:id="@+id/noDatalayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <com.airbnb.lottie.LottieAnimationView
                    android:id="@+id/lottieEmpty"
                    android:layout_width="150dp"
                    android:layout_height="150dp"
                    android:layout_centerHorizontal="true"
                    app:lottie_autoPlay="true"
                    app:lottie_loop="true"
                    app:lottie_rawRes="@raw/empty_lottie" />

                <com.ws.core_ui.custom_views.WSTextView
                    android:id="@+id/emptytextview"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:padding="3dp"
                    android:text="Books not available now,\nPlease check your internet connection."
                    android:textColor="@color/colorShopErrorText"
                    android:textSize="12sp"
                    android:typeface="normal" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linearNoData"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <com.airbnb.lottie.LottieAnimationView
                    android:layout_width="150dp"
                    android:layout_height="150dp"
                    android:layout_centerHorizontal="true"
                    app:lottie_autoPlay="true"
                    app:lottie_loop="true"
                    app:lottie_rawRes="@raw/empty_lottie" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="No books found."
                    android:textColor="@color/colorShopErrorText"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="We can't find any books matching your search."
                    android:textColor="@color/colorShopErrorTextSecondary"
                    android:textSize="12sp" />
            </LinearLayout>

            <!--<include
                layout="@layout/no_internet_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true" />-->

            <ProgressBar
                android:id="@+id/lvCenter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:visibility="gone"
                android:indeterminateTint="@color/colorAccent" />
        </RelativeLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</FrameLayout>