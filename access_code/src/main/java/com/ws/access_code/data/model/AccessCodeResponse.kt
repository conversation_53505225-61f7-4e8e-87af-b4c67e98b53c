package com.ws.access_code.data.model

import com.ws.access_code.ui.AccessCodeStatus
import java.io.Serializable

data class AccessCodeResponse(
    val status: String = "",
    val instituteId: String = ""
): Serializable {
    fun getStatus(isForInstitute: Boolean = false): AccessCodeStatus {
        return if(isForInstitute) {
            when {
                "ok".equals(status, true) && instituteId.isNotEmpty() -> AccessCodeStatus.ALLOCATED
                else -> {
                    AccessCodeStatus.INVALID.also {
                        it.msg = status
                    }
                }
            }
        } else {
            when {
                "allocated".equals(status, true) -> AccessCodeStatus.ALLOCATED
                "invalid".equals(status, true) -> AccessCodeStatus.INVALID
                else -> AccessCodeStatus.ERROR
            }
        }
    }
}
