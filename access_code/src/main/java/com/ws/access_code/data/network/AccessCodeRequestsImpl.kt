package com.ws.access_code.data.network

import com.ws.access_code.data.model.AccessCodeRequest
import com.ws.access_code.data.network.AccessCodeRequests.AccessCodeEndpoints
import com.ws.commons.interfaces.TokenProvider
import com.ws.commons.models.NetworkConfig
import com.ws.networking.NetworkRequest
import com.ws.networking.utils.ApiParams

internal class AccessCodeRequestsImpl(
    private val tokenProvider: TokenProvider,
    private val networkConfig: NetworkConfig
): AccessCodeRequests {
    override fun getValidateAccessCodeRequest(request: AccessCodeRequest): NetworkRequest {
        val params = hashMapOf<String, String?>(ApiParams.PARAM_BOOK_CODE to request.accessCode)
        if(request.bookId.isNotBlank()) {
            params[ApiParams.PARAM_BOOK_ID] = request.bookId
            params[ApiParams.PARAM_BOOK_LEVEL] = "true"
        }
        return NetworkRequest(
            baseUrl = networkConfig.service,
            apiEndPoint = AccessCodeEndpoints.VALIDATE_ACCESS_CODE_ENDPOINT,
            siteId = networkConfig.siteId,
            token = tokenProvider.getToken(),
            params = params
        )
    }
}