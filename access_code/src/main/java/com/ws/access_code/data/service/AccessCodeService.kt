package com.ws.access_code.data.service

import com.ws.access_code.data.model.AccessCodeRequest
import com.ws.commons.Result

interface AccessCodeService {
    
    /**
     * Validates and adds requested book or Institute into user's account
     *
     * @param request
     *  - access code (can be of a book or institute) is required
     *  - bookId is optional (required only if given access code for a specific book)
     */
    suspend fun validateAccessCode(request: AccessCodeRequest): Result<String>
}