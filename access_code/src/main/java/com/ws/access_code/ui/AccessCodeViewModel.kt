package com.ws.access_code.ui

import androidx.lifecycle.ViewModel
import com.ws.access_code.data.model.AccessCodeRequest
import com.ws.access_code.data.model.AccessCodeResponse
import com.ws.access_code.data.repositories.AccessCodeRepository
import com.ws.commons.Result

class AccessCodeViewModel(
    private val accessCodeRepository: AccessCodeRepository
): ViewModel() {
    
    suspend fun validateAccessCode(accessCodeRequest: AccessCodeRequest): Result<AccessCodeResponse> {
        return accessCodeRepository.validateAccessCode(accessCodeRequest)
    }
    
}