apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'ca.cutterslade.analyze'

repositories {
    mavenCentral()
    google()
}

android {
    namespace 'com.wonderslate.prepjoy'
    compileSdk 34
    buildToolsVersion '34.0.0'

    defaultConfig {
        configurations.all {
            resolutionStrategy { force 'androidx.core:core-ktx:1.12.0' }
        }
        applicationId "com.wonderslate.prepjoy"
        minSdk 21
        targetSdk 34
        versionCode 32
        versionName "1.3.9"
        multiDexEnabled true
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        buildConfigField "String", "SITE_ID", "\"27\""
        buildConfigField "String", "SMS_APP_CODE_DEBUG", "\"GVxHolP/AFd\""
        buildConfigField "String", "SMS_APP_CODE_LIVE", "\"GVxHolP/AFd\""
        buildConfigField "String", "RAZOR_PAY_ID", WSRazorPay_Live
        vectorDrawables.useSupportLibrary = true
    }

    buildTypes {
        release {
            shrinkResources false
            minifyEnabled false
            debuggable false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            shrinkResources false
            minifyEnabled false
            ext.enableCrashlytics = false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    
    kotlinOptions {
        jvmTarget = '17'
    }
    
    packagingOptions {
        resources {
            excludes += ['META-INF/ASL2.0', 'META-INF/DEPENDENCIES.txt', 'META-INF/LICENSE.txt', 
                        'META-INF/NOTICE.txt', 'META-INF/NOTICE', 'META-INF/LICENSE', 
                        'META-INF/DEPENDENCIES', 'META-INF/notice.txt', 'META-INF/license.txt', 
                        'META-INF/dependencies.txt', 'META-INF/LGPL2.1', 
                        'META-INF/services/javax.annotation.processing.Processor']
        }
    }
    
    buildFeatures {
        viewBinding true
        dataBinding true
        buildConfig true
    }
    
    flavorDimensions = ["prepjoy"]
    productFlavors {
        current_affairs {
            dimension "prepjoy"
            applicationId 'com.wonderslate.prepjoy'
            manifestPlaceholders = [dynamicLinkHost : "prepjoy.page.link"]
            versionCode 65
            versionName "2.2.5"
            targetSdk 34
            buildConfigField "String", "SITE_ID", "\"27\""
            buildConfigField "String", "SMS_APP_CODE_DEBUG", "\"GVxHolP/AFd\""
            buildConfigField "String", "SMS_APP_CODE_LIVE", "\"GVxHolP/AFd\""
        }
        yct {
            dimension "prepjoy"
            manifestPlaceholders = [dynamicLinkHost : "yctbooks.page.link"]
            versionCode 7
            versionName "1.0.6"
            targetSdk 34
            applicationIdSuffix ".yct"
            buildConfigField "String", "SITE_ID", "\"52\""
            buildConfigField "String", "SMS_APP_CODE_DEBUG", "\"GVxHolP/AFd\""
            buildConfigField "String", "SMS_APP_CODE_LIVE", "\"GVxHolP/AFd\""
        }
        prabhat {
            dimension "prepjoy"
            manifestPlaceholders = [dynamicLinkHost : "prabhatexams.page.link"]
            versionCode 2
            versionName "1.0.1"
            targetSdk 34
            applicationIdSuffix ".prabhat"
            buildConfigField "String", "SITE_ID", "\"57\""
            buildConfigField "String", "SMS_APP_CODE_DEBUG", "\"GVxHolP/AFd\""
            buildConfigField "String", "SMS_APP_CODE_LIVE", "\"GVxHolP/AFd\""
        }
        kiran {
            dimension "prepjoy"
            manifestPlaceholders = [dynamicLinkHost : "prabhatexams.page.link"]
            versionCode 10
            versionName "1.1.0"
            targetSdk 34
            applicationIdSuffix ".kicx"
            buildConfigField "String", "SITE_ID", "\"71\""
            buildConfigField "String", "SMS_APP_CODE_DEBUG", "\"GVxHolP/AFd\""
            buildConfigField "String", "SMS_APP_CODE_LIVE", "\"GVxHolP/AFd\""
        }
        spardha {
            dimension "prepjoy"
            manifestPlaceholders = [dynamicLinkHost : "prabhatexams.page.link"]
            versionCode 2
            versionName "1.0.1"
            targetSdk 34
            applicationIdSuffix ".spardha"
            buildConfigField "String", "SITE_ID", "\"75\""
            buildConfigField "String", "SMS_APP_CODE_DEBUG", "\"GVxHolP/AFd\""
            buildConfigField "String", "SMS_APP_CODE_LIVE", "\"GVxHolP/AFd\""
        }
        karnataka {
            dimension "prepjoy"
            manifestPlaceholders = [dynamicLinkHost : "prpjykarnataka.page.link"]
            versionCode 31
            versionName "1.4.0"
            targetSdk 34
            applicationIdSuffix ".karnataka"
            buildConfigField "String", "SITE_ID", "\"30\""
            buildConfigField "String", "SMS_APP_CODE_DEBUG", "\"GVxHolP/AFd\""
            buildConfigField "String", "SMS_APP_CODE_LIVE", "\"GVxHolP/AFd\""
        }
        engineering {
            dimension "prepjoy"
            manifestPlaceholders = [dynamicLinkHost : "prpjyeee.page.link"]
            versionCode 13
            versionName "1.1.0"
            targetSdk 34
            applicationIdSuffix ".engineering"
            buildConfigField "String", "SITE_ID", "\"31\""
            buildConfigField "String", "SMS_APP_CODE_DEBUG", "\"GVxHolP/AFd\""
            buildConfigField "String", "SMS_APP_CODE_LIVE", "\"GVxHolP/AFd\""
        }
        neet {
            dimension "prepjoy"
            manifestPlaceholders = [dynamicLinkHost : "prpjyneet.page.link"]
            versionCode 13
            versionName "1.0.3"
            targetSdk 34
            applicationIdSuffix ".neet"
            buildConfigField "String", "SITE_ID", "\"32\""
            buildConfigField "String", "SMS_APP_CODE_DEBUG", "\"GVxHolP/AFd\""
            buildConfigField "String", "SMS_APP_CODE_LIVE", "\"GVxHolP/AFd\""
        }
        ctet {
            dimension "prepjoy"
            manifestPlaceholders = [dynamicLinkHost : "prepjoyteaching.page.link"]
            versionCode 12
            versionName "1.1.0"
            targetSdk 34
            applicationIdSuffix ".ctet"
            buildConfigField "String", "SITE_ID", "\"33\""
            buildConfigField "String", "SMS_APP_CODE_DEBUG", "\"GVxHolP/AFd\""
            buildConfigField "String", "SMS_APP_CODE_LIVE", "\"GVxHolP/AFd\""
        }
        ca {
            dimension "prepjoy"
            manifestPlaceholders = [dynamicLinkHost : "prepjoyca.page.link"]
            versionCode 12
            versionName "1.1.0"
            targetSdk 34
            applicationIdSuffix ".ca"
            buildConfigField "String", "SITE_ID", "\"35\""
            buildConfigField "String", "SMS_APP_CODE_DEBUG", "\"GVxHolP/AFd\""
            buildConfigField "String", "SMS_APP_CODE_LIVE", "\"GVxHolP/AFd\""
        }
    }
    
    useLibrary 'org.apache.http.legacy'
}

configurations {
    all {
        exclude group: 'commons-logging', module: 'commons-logging'
        exclude group: 'org.apache.httpcomponents', module: 'httpclient'
        exclude group: 'org.apache.httpcomponents', module: 'httpcore'
        // Exclude the no-tzdb variant of joda-time to prevent duplicates
        exclude group: 'joda-time', module: 'joda-time-no-tzdb'
        resolutionStrategy {
            force 'joda-time:joda-time:2.12.5'
        }
    }
}

dependencies {
    implementation "androidx.appcompat:appcompat:$app_compat_version"
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation 'com.google.firebase:firebase-inappmessaging-display:20.4.0'
    implementation 'androidx.test.ext:junit:1.1.5'
    testImplementation 'junit:junit:4.13.2'
    implementation 'de.hdodenhof:circleimageview:3.1.0'
    implementation(name:'infinitecycleviewpager-1.0.2', ext:'aar')
    implementation(name:'meow-bottom-navigation-java-1.2.0', ext:'aar')
    implementation(name:'prdownloader-0.6.0', ext:'aar')
    implementation(name:'library-2.1.3', ext:'aar')
    implementation 'com.github.varunest:TheGlowingLoader:1.0.7'

    //Koin - Dependency injection
    implementation "io.insert-koin:koin-android:$koin_version"
    implementation "com.android.volley:volley:$volley_version"

    //commons layer
    api project(path: ':commons')
    api project(path: ':domain')
    api project(path: ':data')

    implementation project(path: ':core_ui')
    implementation project(path: ':core_common')
    implementation project(path: ':shop_books')
    implementation project(path: ':book_details')
    implementation project(path: ':access_code')
    implementation project(path: ':purchase')
    implementation project(path: ':library_books')
    implementation project(path: ':chapters_list')
    implementation project(path: ':resources')
    implementation project(path: ':newYoutubeExtractor')
    implementation project(path: ':rssmanager')

    //Razorpay - Payment handling
    implementation "com.razorpay:checkout:$razor_pay_version"

    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle_version"
    implementation 'javax.inject:javax.inject:1'

    //Kotlin
    implementation "androidx.core:core-ktx:$core_ktx_version"

    //Coroutines
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:$coroutine_version"

    // Testing Navigation
    androidTestImplementation("androidx.navigation:navigation-testing:2.7.6")

    // picasso to download image and set in image view
    implementation 'com.squareup.picasso:picasso:2.8'

    // lib for SMS verification (Phone Auth)
    implementation 'com.google.android.gms:play-services-auth:20.7.0'
    implementation 'com.google.android.gms:play-services-auth-api-phone:18.0.1'
    implementation "com.google.code.gson:gson:$gson_version"
    implementation "com.github.bumptech.glide:glide:$glide_version"

    implementation 'androidx.media3:media3-exoplayer:1.2.1'
    implementation 'androidx.media3:media3-ui:1.2.1'
    implementation 'androidx.media3:media3-exoplayer-hls:1.2.1'

    implementation('net.danlew:android.joda:2.12.5') {
        exclude group: 'joda-time', module: 'joda-time-no-tzdb'
    }
    implementation 'org.jsoup:jsoup:1.17.2'
    implementation 'com.facebook.network.connectionclass:connectionclass:1.0.1'

    // SDP & SSP - size consistency
    implementation "com.intuit.sdp:sdp-android:$sdp_ssp_version"
    implementation "com.intuit.ssp:ssp-android:$sdp_ssp_version"

    // lottie for animation loading
    implementation "com.airbnb.android:lottie:$lottie_version"
    implementation 'com.soundcloud.android:android-crop:1.0.1@aar'

    implementation ('org.apache.httpcomponents:httpclient-android:4.3.5.1') {
        exclude group: 'org.apache.http'
    }
    implementation('org.apache.httpcomponents:httpmime:4.3.6') {
        exclude group: 'org.apache.http'
        exclude module: 'httpclient'
    }

    implementation 'net.yslibrary.keyboardvisibilityevent:keyboardvisibilityevent:3.0.0-RC3'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'com.google.android.play:review:2.0.1'
    implementation 'com.google.android.play:app-update:2.1.0'

    //Shimmer layout
    implementation "com.facebook.shimmer:shimmer:$shimmer_version"

    // analytics - Import the BoM for the Firebase platform
    implementation platform('com.google.firebase:firebase-bom:32.7.1')
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.firebase:firebase-config-ktx'
    implementation 'com.google.firebase:firebase-core:21.1.1'
    implementation 'com.google.firebase:firebase-crashlytics-ktx'
    implementation 'com.google.firebase:firebase-dynamic-links'
    implementation 'com.google.firebase:firebase-messaging-ktx'

    implementation 'com.jakewharton:butterknife:10.2.3'
    androidTestImplementation 'junit:junit:4.13.2'
    annotationProcessor 'com.jakewharton:butterknife-compiler:10.2.3'

    //For New feed
    implementation 'com.github.KwabenBerko:News-API-Java:1.0.2'

    configurations {
        all*.exclude group: 'com.google.guava', module: 'guava-jdk5'
    }

    implementation 'com.github.fornewid:neumorphism:0.3.2'
    implementation "com.android.installreferrer:installreferrer:2.2"
    implementation 'com.nostra13.universalimageloader:universal-image-loader:1.9.5'
    implementation "androidx.browser:browser:1.7.0"
    implementation 'com.google.androidbrowserhelper:androidbrowserhelper:2.5.0'
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'
    implementation 'com.github.douglasjunior:android-simple-tooltip:1.1.0'
    implementation 'androidx.work:work-runtime-ktx:2.9.0'
    implementation 'com.jakewharton.threetenabp:threetenabp:1.4.7'
    implementation 'androidx.work:work-runtime:2.9.0'
    implementation "com.github.skydoves:powermenu:2.2.3"

    implementation("io.noties.markwon:core:4.6.2") {
        exclude group: 'org.jetbrains', module: 'annotations-java5'
    }
    implementation("io.noties.markwon:ext-latex:4.6.2") {
        exclude group: 'org.jetbrains', module: 'annotations-java5'
    }
    implementation("io.noties.markwon:ext-strikethrough:4.6.2") {
        exclude group: 'org.jetbrains', module: 'annotations-java5'
    }
    implementation("io.noties.markwon:ext-tables:4.6.2") {
        exclude group: 'org.jetbrains', module: 'annotations-java5'
    }
    implementation("io.noties.markwon:ext-tasklist:4.6.2") {
        exclude group: 'org.jetbrains', module: 'annotations-java5'
    }
    implementation("io.noties.markwon:html:4.6.2") {
        exclude group: 'org.jetbrains', module: 'annotations-java5'
    }
    implementation("io.noties.markwon:image:4.6.2") {
        exclude group: 'org.jetbrains', module: 'annotations-java5'
    }
    implementation("io.noties.markwon:image-coil:4.6.2") {
        exclude group: 'org.jetbrains', module: 'annotations-java5'
    }
    implementation("io.noties.markwon:linkify:4.6.2") {
        exclude group: 'org.jetbrains', module: 'annotations-java5'
    }
    implementation("io.noties.markwon:syntax-highlight:4.6.2") {
        exclude group: 'org.jetbrains', module: 'annotations-java5'
    }

    implementation 'io.noties.markwon:image-glide:4.6.2'

    implementation ('com.vanniktech:android-image-cropper:4.5.0') {
        exclude group: "org.jetbrains.kotlin", module: "kotlin-stdlib"
    }

    implementation 'io.noties.markwon:image-coil:4.6.2'
    implementation 'io.coil-kt:coil:2.5.0'
    implementation 'de.hdodenhof:circleimageview:3.1.0'
    implementation 'com.itextpdf:itext7-core:8.0.2'
    implementation "com.github.skydoves:powerspinner:1.2.7"
    implementation 'com.caverock:androidsvg-aar:1.4'
}

apply plugin: 'com.google.gms.google-services'
