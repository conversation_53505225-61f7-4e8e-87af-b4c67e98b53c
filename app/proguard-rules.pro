# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in C:\Android\sdk/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:
-ignorewarnings
-keep class com.wonderslate.prepjoy.ui.** {
   *;
}

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
-keepclassmembers class JSInterface {
   public *;
}

-keepclassmembers class com.wonderslate.prepjoy.service.PrepJoyFirebaseMessagingService {
   public *;
}

-keepclassmembers class ReadJSInterface {
   public *;
}

-keepnames class * implements android.os.Parcelable {
   *;
}

-keepnames class * implements java.io.Serializable {
   *;
}

-keep class com.wang.avi.**{*;}

-keep public class * extends java.lang.Exception

##---------------Begin: proguard configuration for <PERSON>son  ----------
# <PERSON><PERSON> uses generic type information stored in a class file when working with fields. Proguard
# removes such information by default, so configure it to keep all of it.

# For using GSON @Expose annotation
-keepattributes *Annotation*

# Gson specific classes
-dontwarn sun.misc.**
#-keep class com.google.gson.stream.** { *; }
-keep class com.naveed.ytextractor.** {
   *;
}
# Application classes that will be serialized/deserialized over Gson
-keep class com.google.gson.examples.android.model.** { *; }

# Prevent proguard from stripping interface information from TypeAdapterFactory,
# JsonSerializer, JsonDeserializer instances (so they can be used in @JsonAdapter)
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

##---------------End: proguard configuration for Gson  ----------



##---------------Begin: Razorpay ---------------------

-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

-keepattributes JavascriptInterface

-dontwarn com.razorpay.**
-keep class com.razorpay.** {*;}

-optimizations !method/inlining/*

-keepclasseswithmembers class * {
  public void onPayment*(...);
}
##---------------End: proguard configuration for Razorpay  ----------

-keep class com.crashlytics.** { *; }
-dontwarn com.crashlytics.**
-keepattributes SourceFile,LineNumberTable
-keepattributes EnclosingMethod
-keepattributes InnerClasses

# Retrofit 2.X
## https://square.github.io/retrofit/ ##

-dontwarn retrofit2.**
-keep class retrofit2.** { *; }
-keepattributes Signature
-keepattributes Exceptions

-keepclasseswithmembers class * {
    @retrofit2.http.* <methods>;
}

-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public class * extends com.bumptech.glide.module.AppGlideModule
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}
# Removed Joda-Time ProGuard rules - using Android native date/time libraries
-keep class org.json.** { *; }
-keep class kotlinx.coroutines.**
-dontwarn kotlinx.atomicfu.**
-keep class * extends androidx.room.RoomDatabase
-dontwarn androidx.room.paging.**


-keep class org.apache.** { *; }
-dontwarn javax.xml.stream.**

-keep public class org.simpleframework.** { *; }
-keep class org.simpleframework.xml.** { *; }
-keep class org.simpleframework.xml.core.** { *; }
-keep class org.simpleframework.xml.util.** { *; }

-keepattributes ElementList, Root, Annotation

-keepclassmembers class * {
    @org.simpleframework.xml.* *;
}

-dontwarn okio.**
-dontwarn javax.annotation.Nullable
-dontwarn javax.annotation.ParametersAreNonnullByDefault

# Platform calls Class.forName on types which do not exist on Android to determine platform.
-dontnote retrofit2.Platform
# Platform used when running on Java 8 VMs. Will not be used at runtime.
-dontwarn retrofit2.Platform$Java8
# Retain generic type information for use by reflection by converters and adapters.
-keepattributes Signature
# Retain declared checked exceptions for use by a Proxy instance.
-keepattributes Exceptions

-keepattributes Signature
# For using GSON @Expose annotation
-keepattributes *Annotation*
# Gson specific classes
-keep class sun.misc.Unsafe { *; }
#-keep class com.google.gson.stream.** { *; }

# rxjava
-keep class rx.schedulers.Schedulers {
    public static <methods>;
}
-keep class rx.schedulers.ImmediateScheduler {
    public <methods>;
}
-keep class rx.schedulers.TestScheduler {
    public <methods>;
}
-keep class rx.schedulers.Schedulers {
    public static ** test();
}
-keepclassmembers class rx.internal.util.unsafe.*ArrayQueue*Field* {
    long producerIndex;
    long consumerIndex;
}
-keepclassmembers class rx.internal.util.unsafe.BaseLinkedQueueProducerNodeRef {
    long producerNode;
    long consumerNode;
}

-keep class rx.internal.util.unsafe.** {
    *;
 }

 -keep class com.ws.commons
 -keep class * extends **.commons

 # Keep the GPT classes and its members
  -keep class com.wonderslate.data.models.GPTPrompts {
      *;
  }

  -keep class com.wonderslate.data.models.ChatExtra {
      *;
  }

  -keep class com.wonderslate.data.models.ChatMessage {
      *;
  }

  -keep class com.wonderslate.data.models.GptLog {
      *;
  }

  -keep class com.wonderslate.data.models.GptLogResponse {
      *;
  }

  -keep class com.wonderslate.data.models.GptLogsResponse {
      *;
  }

  -keep class com.wonderslate.data.models.RechargeOption {
      *;
  }
