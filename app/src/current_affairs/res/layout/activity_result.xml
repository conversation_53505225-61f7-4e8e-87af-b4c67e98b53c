<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/parent_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_bg_dark"
    tools:context=".ui.quiz.ResultActivity">

    <LinearLayout
        android:id="@+id/webViewParent"
        android:layout_width="match_parent"
        android:background="@color/primary_bg_dark"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <com.wonderslate.prepjoy.ui.quiz.NestedWebView
            android:id="@+id/webView"
            android:layout_width="match_parent"
            android:background="@color/primary_bg_red"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:focusable="true"
            android:isScrollContainer="false"
            android:visibility="visible"/>
    </LinearLayout>


    <com.wang.avi.AVLoadingIndicatorView
        android:id="@+id/progressLoader"
        style="@style/AVLoadingIndicatorView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:gravity="center"
        app:indicatorColor="@color/primary_bg_red"
        app:indicatorName="LineScalePulseOutRapidIndicator"
        android:elevation="10dp"
        android:visibility="visible"/>

    <include
        layout="@layout/no_internet_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_alignParentBottom="true"
        android:layout_above="@id/relativeLayout"
        android:layout_marginBottom="5dp" />

</androidx.constraintlayout.widget.ConstraintLayout>