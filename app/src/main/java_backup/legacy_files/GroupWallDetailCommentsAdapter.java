package com.wonderslate.prepjoy.ui.groupwall;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions;
import com.bumptech.glide.signature.ObjectKey;
import com.wonderslate.data.models.Comments;
import com.wonderslate.data.network.WSAPIManager;
import com.wonderslate.prepjoy.R;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class GroupWallDetailCommentsAdapter extends RecyclerView.Adapter<GroupWallDetailCommentsAdapter.ViewHolder> {

    private final LayoutInflater mInflater;
    private int clickCount = 0, commendClickCount = 0;
    private List<Comments> mData = new ArrayList<>();
    private ObjectKey userImageGlideKey;
    private final GroupWallCommentAndReplayActivity mContext;
    private final List<String> replayList = new ArrayList<>();
    public String HTTP_IMAGE_FILENAME = "fileName";
    private final String HTTP_OBJECT_ID = "id";

    public GroupWallDetailCommentsAdapter(GroupWallCommentAndReplayActivity context, List<Comments> data) {
        this.mContext = context;
        this.mInflater = LayoutInflater.from(context);
        this.mData = data;
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = mInflater.inflate(R.layout.item_study_group_commends, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        try {
            Comments data = mData.get(position);
            holder.textViewCommendText.setText(data.getDescription());
            holder.textviewUser.setText(data.getName());
            holder.textviewDateTime.setText("" + data.getDateCreated());

            if (userImageGlideKey == null)
                userImageGlideKey = new ObjectKey(System.currentTimeMillis());
            String image = WSAPIManager.URL_SELECTED_USER_IMAGE_API + "&" + HTTP_IMAGE_FILENAME + "="
                    + data.getProfilepic() + "&" + HTTP_OBJECT_ID + "=" + data.getUserId();
            Glide.with(mContext)
                    .load(image)
                    .placeholder(R.drawable.ic_sample_member)
                    .error(R.drawable.ic_sample_member)
                    .transition(DrawableTransitionOptions.withCrossFade())
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .signature(userImageGlideKey)
                    .into(holder.imageViewAvatar);
            userImageGlideKey = null;

            holder.recyclerViewReplays.setVisibility(View.GONE);
            if (data.getRepliedDetailsByCommentsId().size() != 0) {
                StudyGroupReplayAdapter studyGroupReplayAdapter = new StudyGroupReplayAdapter(mContext, data.getRepliedDetailsByCommentsId());
                holder.recyclerViewReplays.setAdapter(studyGroupReplayAdapter);
                holder.textViewShowCReplays.setVisibility(View.VISIBLE);
                holder.textViewShowCReplays.setText("View " + data.getRepliedCount() + " Reply");
            } else {
                holder.textViewShowCReplays.setVisibility(View.GONE);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public int getItemCount() {
        return mData.size();
    }

    @Override
    public int getItemViewType(int position) {
        return position;
    }


    public class ViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {
        @BindView(R.id.imageView_avatar)
        ImageView imageViewAvatar;
        @BindView(R.id.relativeLayout_avatar)
        RelativeLayout relativeLayoutAvatar;
        @BindView(R.id.textview_user)
        TextView textviewUser;
        @BindView(R.id.textview_date_time)
        TextView textviewDateTime;
        @BindView(R.id.layout_user)
        LinearLayout layoutUser;
        @BindView(R.id.textView_commend_text)
        TextView textViewCommendText;
        @BindView(R.id.textView_replay)
        TextView textViewReplay;
        @BindView(R.id.editText_replay)
        EditText editTextReplay;
        @BindView(R.id.layout_replay)
        RelativeLayout layoutReplay;
        @BindView(R.id.recyclerView_groups_replays)
        RecyclerView recyclerViewReplays;
        @BindView(R.id.imageView_send_replay)
        ImageView imageViewSendReplay;
        @BindView(R.id.textView_show_replay)
        TextView textViewShowCReplays;

        ViewHolder(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
            recyclerViewReplays.setLayoutManager(new LinearLayoutManager(mContext));
            recyclerViewReplays.setHasFixedSize(false);
            textViewReplay.setOnClickListener(this);
            imageViewSendReplay.setOnClickListener(this);
            textViewShowCReplays.setOnClickListener(this);
            itemView.setOnClickListener(this);
        }

        @Override
        public void onClick(View view) {
            try {
                switch (view.getId()) {
                    case R.id.textView_replay:
                        if (clickCount == 0) {
                            clickCount = 1;
                            layoutReplay.setVisibility(View.VISIBLE);
                            imageViewSendReplay.setVisibility(View.VISIBLE);
                            mContext.scrollNestedScrollToBottom();
                        } else {
                            clickCount = 0;
                            layoutReplay.setVisibility(View.GONE);
                            imageViewSendReplay.setVisibility(View.GONE);
                            mContext.hideKeyboard(mContext);
                        }
                        break;
                    case R.id.imageView_send_replay:
                        String comment = editTextReplay.getText().toString().trim();
                        if (!comment.isEmpty()) {
                            mContext.replayToComment(mData.get(getAdapterPosition()).getPostId(), mData.get(getAdapterPosition()).getId(), comment);
                            editTextReplay.setText("");
                            layoutReplay.setVisibility(View.GONE);
                            imageViewSendReplay.setVisibility(View.GONE);
                            mContext.hideKeyboard(mContext);
                        } else {
                            editTextReplay.setHint("Please enter Reply");
                            editTextReplay.setHintTextColor(ContextCompat.getColor(mContext, R.color.primary_bg_red));
                        }
                        break;
                    case R.id.textView_show_replay:
                        if (commendClickCount == 0) {
                            commendClickCount = 1;
                            recyclerViewReplays.setVisibility(View.VISIBLE);
                            textViewShowCReplays.setText("Hide Reply");
                        } else {
                            commendClickCount = 0;
                            recyclerViewReplays.setVisibility(View.GONE);
                            textViewShowCReplays.setText("View " + mData.get(getAdapterPosition()).getRepliedCount() + " Reply");
                        }
                        break;
                    default:
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public interface ItemClickListener {
        void onItemClick(View view, int position);
    }
}
