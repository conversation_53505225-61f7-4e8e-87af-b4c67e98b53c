package com.wonderslate.prepjoy.ui.groupwall;

import static android.app.Activity.RESULT_OK;
import static com.wonderslate.data.helper.ConstantsHelper.DOWNLOADED_FILES;
import static com.wonderslate.data.helper.ConstantsHelper.HTTP_IMAGE_FILENAME;
import static com.wonderslate.data.helper.ConstantsHelper.HTTP_OBJECT_ID;
import static com.wonderslate.data.helper.ConstantsHelper.UNZIPPED_EPUB_FILES;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.DownloadManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.Resources;
import android.database.Cursor;
import android.database.sqlite.SQLiteException;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.PorterDuff;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.StatFs;
import android.provider.MediaStore;
import android.provider.OpenableColumns;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.ImageSpan;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.inputmethod.InputMethodManager;
import android.webkit.ConsoleMessage;
import android.webkit.MimeTypeMap;
import android.webkit.URLUtil;
import android.webkit.WebChromeClient;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.AutoCompleteTextView;
import android.widget.Button;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AlertDialogLayout;
import androidx.appcompat.widget.AppCompatSpinner;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;
import androidx.core.widget.NestedScrollView;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.airbnb.lottie.LottieAnimationView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.target.Target;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.wang.avi.AVLoadingIndicatorView;
import com.wonderslate.data.interfaces.WSCallback;
import com.wonderslate.data.models.Comments;
import com.wonderslate.data.models.GroupPostDataModel;
import com.wonderslate.data.models.GroupPostsDataModel;
import com.wonderslate.data.models.ReplayListDate;
import com.wonderslate.data.network.WSAPIManager;
import com.wonderslate.data.network.WSStudyGroup;
import com.wonderslate.data.network.Wonderslate;
import com.wonderslate.data.preferences.WonderPubSharedPrefs;
import com.wonderslate.prepjoy.BuildConfig;
import com.wonderslate.prepjoy.R;
import com.wonderslate.prepjoy.Utils.FirebaseAnalyticsUtils;
import com.wonderslate.prepjoy.Utils.InternetConnectionChecker;
import com.wonderslate.prepjoy.Utils.Utils;
import com.wonderslate.prepjoy.Views.ServerResponseProcessingEngine;
import com.wonderslate.prepjoy.ui.dashboard.DashBoardActivity;

import org.apache.http.HttpEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.ByteArrayBody;
import org.apache.http.entity.mime.content.StringBody;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.ProtocolException;
import java.net.URL;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

public class GroupWallFragment extends Fragment {

    @BindView(R.id.btnBack)
    ImageView btnBack;
    @BindView(R.id.textview_header_back)
    TextView textviewHeaderBack;
    @BindView(R.id.button_notification)
    Button buttonNotification;
    @BindView(R.id.lltNotification)
    LinearLayout lltNotification;
    @BindView(R.id.notification_count)
    TextView notificationCount;
    @BindView(R.id.frameLayout_notification)
    FrameLayout frameLayoutNotification;
    @BindView(R.id.imageView_eye)
    ImageView imageViewEye;
    @BindView(R.id.button_report_group_header)
    Button buttonReportGroup;
    @BindView(R.id.layout_report_group)
    RelativeLayout layoutReportGroup;
    @BindView(R.id.textview_group_name_header)
    TextView textviewGroupNameHeader;
    @BindView(R.id.imageView_invite)
    ImageView imageViewInvite;
    @BindView(R.id.layout_invite_group)
    RelativeLayout layoutInviteGroup;
    @BindView(R.id.button_inview)
    Button buttonInvite;
    @BindView(R.id.imageView_join)
    ImageView imageViewJoin;
    @BindView(R.id.layout_join_group)
    RelativeLayout layoutJoinGroup;
    @BindView(R.id.layout_top_header)
    LinearLayout layoutTopHeader;
    @BindView(R.id.layout_top)
    RelativeLayout layoutTop;
    @BindView(R.id.autocomplete_textView_search_group_detail)
    AutoCompleteTextView autocompleteTextViewSearchGroupDetail;
    @BindView(R.id.shimmerLoadingLayout)
    ShimmerFrameLayout shimmerLoadingLayout;
    @BindView(R.id.recyclerView_groups_details)
    RecyclerView recyclerViewGroupsDetails;
    @BindView(R.id.recycler_view_pages)
    RecyclerView recyclerViewPages;
    @BindView(R.id.layout_page)
    RelativeLayout layoutPagenation;
    @BindView(R.id.groupLoader_details)
    AVLoadingIndicatorView groupLoaderDetails;
    @BindView(R.id.textView_private_msg)
    LinearLayout textViewPrivateMsg;
    @BindView(R.id.layout_settings)
    RelativeLayout layoutSettings;
    @BindView(R.id.button_join)
    Button buttonJoin;
    @BindView(R.id.editText_post)
    EditText editTextPost;
    @BindView(R.id.image_attach_post)
    ImageView imageAttachPost;
    @BindView(R.id.button_post)
    Button buttonPost;
    @BindView(R.id.button_post_shape)
    Button buttonPostShape;
    @BindView(R.id.layout_post)
    RelativeLayout layoutPost;
    @BindView(R.id.layout_attach_image)
    RelativeLayout layoutAttachImage;
    @BindView(R.id.layout_attach_file)
    RelativeLayout layoutAttachFile;
    @BindView(R.id.layout_attach_post_opt)
    LinearLayout layoutAttachPostOpt;
    @BindView(R.id.empty_layout_post)
    LinearLayout layoutEmptyPost;
    @BindView(R.id.private_layout)
    LinearLayout layoutPrivate;
    @BindView(R.id.imageView_attach)
    ImageView imageViewAttach;
    @BindView(R.id.imageView_attached)
    ImageView imageViewAttached;
    @BindView(R.id.button_attach_image)
    Button buttonAttachImage;
    @BindView(R.id.imageView_attach_clear)
    ImageView imageViewAttachClear;
    @BindView(R.id.imageView_attached_file)
    ImageView imageViewFileAttached;
    @BindView(R.id.button_attach_file)
    Button buttonAttachFile;
    @BindView(R.id.imageView_attach_file)
    ImageView imageViewAttachFile;
    @BindView(R.id.imageView_file_attach_clear)
    ImageView imageViewFileAttachClear;
    @BindView(R.id.layout_action)
    RelativeLayout layoutAction;
    @BindView(R.id.animationView_actions)
    LottieAnimationView lottieAnimationViewCommon;
    @BindView(R.id.textView_action)
    TextView textViewAction;
    @BindView(R.id.disable_view)
    FrameLayout disableView;
    @BindView(R.id.disable_view_top)
    FrameLayout disableViewTop;
    @BindView(R.id.imageView_close_edit)
    ImageView imageViewCloseEditPost;
    @BindView(R.id.swipeToRefresh)
    SwipeRefreshLayout swipeRefreshLayout;
    @BindView(R.id.downloadMsgTxt)
    TextView downloadMsgTxt;
    @BindView(R.id.progressDialogBar)
    ProgressBar dialogProgressBar;
    @BindView(R.id.downloadPercent)
    TextView dialogProgressPercent;
    @BindView(R.id.alertDialogLayout)
    AlertDialogLayout dialogLayout;
    @BindView(R.id.nested_seroll_view_post)
    NestedScrollView nestedScrollViewPost;
    @BindView(R.id.idPBLoading)
    ProgressBar progressBar;
    @BindView(R.id.webView_group_res_open)
    WebView webViewGroupResOpen;
    @BindView(R.id.bottom_sheet)
    RelativeLayout bottomSheet;
    @BindView(R.id.back_webView)
    ImageView backWebView;
    @BindView(R.id.nohistorylayout)
    LinearLayout webPageErrorLayout;
    @BindView(R.id.imageView_avatar_webView)
    ImageView imageViewAvatarWebView;
    @BindView(R.id.textview_user_webView)
    TextView textviewUserWebView;
    @BindView(R.id.spinner_exit_report)
    AppCompatSpinner spinnerExitReport;
    @BindView(R.id.layout_exit_report_spinner)
    RelativeLayout layoutExitReportSpinner;
    @BindView(R.id.animationViewInvite)
    LottieAnimationView lottieAnimationViewShare;
    @BindView(R.id.animationView)
    LottieAnimationView lottieAnimationViewPrivate;
    @BindView(R.id.imageView_full_view)
    ImageView imageViewFullView;
    @BindView(R.id.imageView_full_view_close)
    ImageView imageViewFullViewClose;
    @BindView(R.id.layout_image_full_view)
    RelativeLayout layoutImageFullView;

    @BindView(R.id.progressImageFullView)
    AVLoadingIndicatorView progressImageFullView;

    private Context mContext;
    private WSStudyGroup studyGroup;
    private InternetConnectionChecker internetConnectionChecker;
    private BroadcastReceiver notificationReceiver, notificationDismissReceiver;
    private Animation slideIn, sideUp, animationShake, slideAnimation;
    private boolean isGroupWall = false;
    private int loadPageNo = 0;
    private final List<GroupPostsDataModel> postDataListMain = new ArrayList<>();
    private final List<Integer> pagesList = new ArrayList<>();
    private boolean isLoadMoreBook = true;
    private final List<GroupPostsDataModel> postDataList = new ArrayList<>();
    private String groupId = "", groupName = "", privacyType = "", groupVisibility = "", userType = "", coverImage = "", postImage = "", postFile = "", postId = "", postIdEdit = "", postIdComment = "", groupColorCode = "", userStatus = "";
    //    private final List<GroupPostsDataModel> postDataList = new ArrayList<>();
    private List<Comments> commendList;
    private List<ReplayListDate> replayList;
    //    private boolean isLoadMoreBook = true;
    private boolean isNexPostListLoaded = false;
    private GroupWallDetailsAdapter adapter;
    private boolean isAdmin = false;
    private boolean isFromMyGroupList = true;
    private boolean isPaginationOn = false;
    private String deeplinkInvite = "";
    private String fileName = "";
    private File fileUnzipped, fileZipped;
    private long myDownloadReference;
    private DownloadManager downloadManager;
    private Thread progressThread;
    private boolean downloading = false, isCheckUserExit = true, isFromDeepLink = true, isImageFullViewVisible = false;
    private final String TAG = "TAGGroupWallDetails";
    private boolean isPublicGroup = false;
    private boolean isEditPost = false;
    private BottomSheetBehavior bottomSheetBehavior;
    private boolean isPostImageAttached = false, isPostFileAttached = false;
    public static final int LOAD_IMAGE_FROM_GALLERY = 4, LOAD_PDF_FILE = 5;
    private Bitmap imageBitMap = null;
    private File attachmentOutputFile;
    private byte[] dataFile;
    private String attachmentPicturePath = "", attachedFileName = "";
    private final String attachmentFilePath = "";
    private boolean isGroupInfoEdited = false;
    public static String resourceToShareInGroup = "";
    private View rootView;
    private Uri path;
    private FirebaseAnalyticsUtils analyticsUtils;
    private static final int MAX_BITMAP_SIZE = 100 * 1024 * 1024; // 100 MB
    private boolean allowPagination = false, isListBottom = false;
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        rootView = inflater.inflate(R.layout.activity_group_wall, container, false);
        ButterKnife.bind(this, rootView);
        mContext = getContext();
        analyticsUtils = new FirebaseAnalyticsUtils(mContext);
        init();
        return rootView;
    }

    /*@Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        try {
            if (isVisibleToUser) {
                fetchGroupWallDetails();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }*/


    @Override
    public void onResume() {
        super.onResume();
        //slideAnimation = AnimationUtils.loadAnimation(mContext, R.anim.side_slide);
        //textviewGroupNameHeader.startAnimation(slideAnimation);
        //GroupWallActivity activity = (GroupWallActivity) getActivity();
      //  activity.showOrHideUserPreference(false);

    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
    }

    @Override
    public void onDetach() {
        super.onDetach();
    }

    private void init() {
        try {
            analyticsUtils.logEvent("Cafe Home", "Cafe");
            studyGroup = new WSStudyGroup();
            //registerNotificationReceivers();
            animationShake = AnimationUtils.loadAnimation(mContext, R.anim.shake);
            internetConnectionChecker = new InternetConnectionChecker();
            shimmerLoadingLayout.setVisibility(View.VISIBLE);
            shimmerLoadingLayout.startShimmerAnimation();
            recyclerViewGroupsDetails.setLayoutManager(new LinearLayoutManager(mContext));
            recyclerViewGroupsDetails.setHasFixedSize(false);

            RecyclerView.LayoutManager manager = new LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false);
            recyclerViewPages.setLayoutManager(manager);

            isGroupWall = true;

            //swipe to refresh
            //swipeRefreshLayout.setRefreshing(false);
            swipeRefreshLayout.setOnRefreshListener(() -> {
                /*if (!buttonJoin.getText().toString().equalsIgnoreCase("Requested")) {
                    try {
                        loadPageNo = 0;
                        postDataListMain.clear();
                        pagesList.clear();
                        isLoadMoreBook = true;
                        layoutTop.setVisibility(View.GONE);
                        hideKeyboard(getActivity());
                        editTextPost.setText("");
                        editTextPost.clearFocus();
                        //imageViewCloseEditPost.startAnimation(sideUp);
                        imageViewCloseEditPost.setVisibility(View.GONE);
                        disableView.setVisibility(View.GONE);
                        disableViewTop.setVisibility(View.GONE);
                        getGroupPostsDetailsList();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else {
                    swipeRefreshLayout.setRefreshing(false);
                    Toast.makeText(mContext, "Your join request is in progress.", Toast.LENGTH_SHORT).show();
                }*/

                loadPageNo = 0;
                allowPagination = false;
                postDataListMain.clear();
                pagesList.clear();
                isLoadMoreBook = true;
                layoutTop.setVisibility(View.GONE);
                hideKeyboard(getActivity());
                editTextPost.setText("");
                editTextPost.clearFocus();
                imageViewCloseEditPost.setVisibility(View.GONE);
                disableView.setVisibility(View.GONE);
                disableViewTop.setVisibility(View.GONE);
                //getGroupPostsDetailsList();
                fetchGroupWallDetails();
            });

            //API call to fetch GroupWall Details
            fetchGroupWallDetails();
            nestedScrollViewPost.setOnScrollChangeListener(new NestedScrollView.OnScrollChangeListener() {
                @Override
                public void onScrollChange(NestedScrollView v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
                    try {
                        if (scrollY == v.getChildAt(0).getMeasuredHeight() - v.getMeasuredHeight()) {
                            if (isLoadMoreBook) {
                                if (isNexPostListLoaded) {
                                    isNexPostListLoaded = false;
                                    isListBottom = true;
                                    allowPagination = true;
                                    progressBar.setVisibility(View.VISIBLE);
                                    getGroupPostsDetailsList();
                                }
                            } else {
                                Toast.makeText(getActivity(), "No more Posts.", Toast.LENGTH_SHORT).show();
                            }
                        }/*else if (scrollY == 0) {
                                if (loadPageNo != 0) {
                                    if (isNexPostListLoaded) {
                                        isNexPostListLoaded = false;
                                        loadPageNo--;
                                        getGroupPostsDetailsList();
                                    }
                                }
                            }*/
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });

            //For shared resource open from post
            bottomSheetBehavior = BottomSheetBehavior.from(bottomSheet);
            bottomSheetBehavior.setBottomSheetCallback(new BottomSheetBehavior.BottomSheetCallback() {
                @Override
                public void onStateChanged(@NonNull View bottomSheet, int newState) {
                    switch (newState) {
                        case BottomSheetBehavior.STATE_HIDDEN:
                            break;

                        case BottomSheetBehavior.STATE_DRAGGING:
                            bottomSheetBehavior.setState(BottomSheetBehavior.STATE_EXPANDED);
                            break;
                        case BottomSheetBehavior.STATE_EXPANDED: {
                        }
                        break;
                        case BottomSheetBehavior.STATE_COLLAPSED:
                            break;
                        case BottomSheetBehavior.STATE_SETTLING:
                            break;
                    }
                }

                @Override
                public void onSlide(@NonNull View bottomSheet, float slideOffset) {

                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @OnClick({R.id.btnBack, R.id.textview_header_back, R.id.frameLayout_notification,
            R.id.image_attach_post, R.id.button_attach_image, R.id.layout_attach_file, R.id.imageView_attach_clear, R.id.imageView_file_attach_clear, R.id.button_post, R.id.button_post_shape,
            R.id.imageView_close_edit, R.id.back_webView, R.id.textView_close_webview, R.id.imageView_full_view_close,R.id.button_attach_file})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.btnBack:
                //onBackPressed();
                break;
            case R.id.textview_header_back:
                //onBackPressed();
                break;
            case R.id.frameLayout_notification:
                break;

            case R.id.image_attach_post:
                if (layoutAttachPostOpt.getVisibility() == View.VISIBLE) {
                    layoutAttachPostOpt.setVisibility(View.GONE);
                    imageAttachPost.setColorFilter(ContextCompat.getColor(mContext, R.color.video_title_color_land), PorterDuff.Mode.MULTIPLY);
                } else {
                    layoutAttachPostOpt.setVisibility(View.VISIBLE);
                    imageAttachPost.setColorFilter(ContextCompat.getColor(mContext, R.color.primary_bg_red), PorterDuff.Mode.MULTIPLY);
                }
                break;
            case R.id.button_attach_image:
                if (buttonAttachImage.getText().toString().equalsIgnoreCase("Attach a Image")) {
                    if (buttonAttachFile.getText().toString().equalsIgnoreCase("Attached")) {
                        isPostFileAttached = false;
                        imageViewFileAttachClear.setVisibility(View.GONE);
                        imageViewFileAttached.setVisibility(View.GONE);
                        imageViewAttachFile.setVisibility(View.VISIBLE);
                        buttonAttachFile.setText("Upload a File");

                        Intent getIntent = new Intent(Intent.ACTION_GET_CONTENT);
                        getIntent.setType("image/*");

                        Intent pickIntent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
                        pickIntent.setType("image/*");

                        Intent chooserIntent = Intent.createChooser(getIntent, "Select Image");
                        chooserIntent.putExtra(Intent.EXTRA_INITIAL_INTENTS, new Intent[]{pickIntent});

                        //startActivityForResult(chooserIntent, LOAD_IMAGE_FROM_GALLERY);
                        loadImageFormGallery.launch(chooserIntent);
                    } else {
                        Intent getIntent = new Intent(Intent.ACTION_GET_CONTENT);
                        getIntent.setType("image/*");

                        Intent pickIntent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
                        pickIntent.setType("image/*");

                        Intent chooserIntent = Intent.createChooser(getIntent, "Select Image");
                        chooserIntent.putExtra(Intent.EXTRA_INITIAL_INTENTS, new Intent[]{pickIntent});

                        //startActivityForResult(chooserIntent, LOAD_IMAGE_FROM_GALLERY);
                        loadImageFormGallery.launch(chooserIntent);
                    }
                } else {
                    Toast.makeText(getActivity(), "Image already attached.", Toast.LENGTH_SHORT).show();
                }
                break;
            case R.id.button_attach_file:
                if (buttonAttachFile.getText().toString().equalsIgnoreCase("Upload a File")) {
                    if (buttonAttachImage.getText().toString().equalsIgnoreCase("Attached")) {
                        isPostImageAttached = false;
                        imageViewAttachClear.setVisibility(View.GONE);
                        imageViewAttached.setVisibility(View.GONE);
                        imageViewAttach.setVisibility(View.VISIBLE);
                        buttonAttachImage.setText("Attach a Image");

                        //For PDF upload
                        Intent intent = new Intent();
                        intent.setType("application/pdf");
                        intent.setAction(Intent.ACTION_GET_CONTENT);
                        loadPdfFile.launch(Intent.createChooser(intent, "Select PDF"));
                        //startActivityForResult(Intent.createChooser(intent, "Select PDF"), LOAD_PDF_FILE);
                    } else {
                        //For PDF upload
                        Intent intent = new Intent();
                        intent.setType("application/pdf");
                        intent.setAction(Intent.ACTION_GET_CONTENT);
                        loadPdfFile.launch(Intent.createChooser(intent, "Select PDF"));
                        //startActivityForResult(Intent.createChooser(intent, "Select PDF"), LOAD_PDF_FILE);
                    }
                }
                break;
            case R.id.imageView_attach_clear:
                isPostImageAttached = false;
                imageViewAttachClear.setVisibility(View.GONE);
                imageViewAttached.setVisibility(View.GONE);
                imageViewAttach.setVisibility(View.VISIBLE);
                buttonAttachImage.setText("Attach a Image");
                break;
            case R.id.imageView_file_attach_clear:
                isPostFileAttached = false;
                imageViewFileAttachClear.setVisibility(View.GONE);
                imageViewFileAttached.setVisibility(View.GONE);
                imageViewAttachFile.setVisibility(View.VISIBLE);
                buttonAttachFile.setText("Upload a File");
                break;
            case R.id.button_post:
                String post = editTextPost.getText().toString().trim();
                if (post.isEmpty()) {
                    if (!isPostImageAttached && !isPostFileAttached) {
                        Toast.makeText(mContext, "Please enter content.", Toast.LENGTH_SHORT).show();
                    } else {
                        if (isPostImageAttached) {
                            uploadPostWithImage(post);
                            isPostImageAttached = false;
                            imageViewAttachClear.setVisibility(View.GONE);
                            imageViewAttached.setVisibility(View.GONE);
                            imageViewAttach.setVisibility(View.VISIBLE);
                            buttonAttachImage.setText("Attach a Image");
                            layoutAttachPostOpt.setVisibility(View.GONE);
                        } else if (isPostFileAttached) {
                            uploadPostWithFile(post);
                            isPostFileAttached = false;
                            imageViewFileAttachClear.setVisibility(View.GONE);
                            imageViewFileAttached.setVisibility(View.GONE);
                            imageViewAttachFile.setVisibility(View.VISIBLE);
                            buttonAttachFile.setText("Upload a File");
                            layoutAttachPostOpt.setVisibility(View.GONE);
                        } else {
                            Toast.makeText(mContext, "Post can not be empty.", Toast.LENGTH_SHORT).show();
                        }
                    }
                } else {
                    if (!isEditPost) {
                        if (isPaginationOn) {
                            loadPageNo = 0;
                            postDataListMain.clear();
                            pagesList.clear();
                            isLoadMoreBook = true;
                            isPaginationOn = false;
                            createPost(post);
                        } else {
                            createPost(post);
                        }
                    } else {
                        disableView.setVisibility(View.GONE);
                        disableViewTop.setVisibility(View.GONE);
                        imageViewCloseEditPost.startAnimation(sideUp);
                        imageViewCloseEditPost.setVisibility(View.GONE);
                        editGroupPost(post, postIdEdit);
                    }
                }
                break;
            case R.id.button_post_shape:
                String postShape = editTextPost.getText().toString().trim();
                if (postShape.isEmpty()) {
                    Toast.makeText(mContext, "Please enter content.", Toast.LENGTH_SHORT).show();
                } else {
                    if (!isEditPost) {
                        if (isPaginationOn) {
                            loadPageNo = 0;
                            postDataListMain.clear();
                            pagesList.clear();
                            isLoadMoreBook = true;
                            isPaginationOn = false;
                            createPost(postShape);
                        } else {
                            createPost(postShape);
                        }
                    } else {
                        disableView.setVisibility(View.GONE);
                        disableViewTop.setVisibility(View.GONE);
                        imageViewCloseEditPost.startAnimation(sideUp);
                        imageViewCloseEditPost.setVisibility(View.GONE);
                        editGroupPost(postShape, postIdEdit);
                    }
                }
                break;
            case R.id.imageView_close_edit:
                hideKeyboard(getActivity());
                editTextPost.setText("");
                editTextPost.clearFocus();
                imageViewCloseEditPost.startAnimation(sideUp);
                imageViewCloseEditPost.setVisibility(View.GONE);
                disableView.setVisibility(View.GONE);
                disableViewTop.setVisibility(View.GONE);
                break;
            case R.id.textView_close_webview:
            case R.id.back_webView:
                bottomSheetBehavior.setState(BottomSheetBehavior.STATE_COLLAPSED);
                break;
            case R.id.imageView_full_view_close:
                try {
                    layoutImageFullView.setVisibility(View.GONE);
                    isImageFullViewVisible = false;
                    disableView.setVisibility(View.GONE);
                    disableViewTop.setVisibility(View.GONE);
                    swipeRefreshLayout.setEnabled(true);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;
        }
    }

    private ActivityResultLauncher<Intent> loadPdfFile = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            new ActivityResultCallback<ActivityResult>() {
                @Override
                public void onActivityResult(ActivityResult result) {
                    try {

                        if (result.getResultCode() == RESULT_OK) {
                            Uri fileuri = result.getData().getData();
                            isPostFileAttached = true;
                            isPostImageAttached = false;
                            imageViewFileAttachClear.setVisibility(View.VISIBLE);
                            buttonAttachFile.setText("Attached");
                            imageViewFileAttached.setVisibility(View.VISIBLE);
                            imageViewAttachFile.setVisibility(View.INVISIBLE);
                            path = fileuri;

                            String displayName = "sample.pdf";
                            attachedFileName = displayName;
                            try {
                                String uriString = fileuri.toString();
                                File myFile = new File(uriString);
                                String path = myFile.getAbsolutePath();
                                if (uriString.startsWith("content://")) {
                                    Cursor cursor = null;
                                    try {
                                        cursor = getActivity().getContentResolver().query(fileuri, null, null, null, null);
                                        if (cursor != null && cursor.moveToFirst()) {
                                            displayName = cursor.getString(cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME));
                                        }
                                    } finally {
                                        cursor.close();
                                    }
                                } else if (uriString.startsWith("file://")) {
                                    displayName = myFile.getName();
                                }
                            } catch (Exception e) {
                                displayName = "sample.pdf";
                                e.printStackTrace();
                            }

                            attachedFileName = displayName;

                            InputStream inputStream = null;
                            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                            try {
                                inputStream = getActivity().getContentResolver().openInputStream(fileuri);

                                byte[] buffer = new byte[1024];
                                byteArrayOutputStream = new ByteArrayOutputStream();

                                int bytesRead;
                                while ((bytesRead = inputStream.read(buffer)) != -1) {
                                    byteArrayOutputStream.write(buffer, 0, bytesRead);
                                }
                            } catch (IOException e) {
                                e.printStackTrace();
                            } finally {
                                if (inputStream != null) {
                                    try {
                                        inputStream.close();
                                    } catch (IOException e) {
                                        e.printStackTrace();
                                    }
                                }
                            }

                            byte[] pdfByteArray = byteArrayOutputStream.toByteArray();
                            dataFile = pdfByteArray;
                        }

                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });


    private ActivityResultLauncher<Intent> loadImageFormGallery = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            new ActivityResultCallback<ActivityResult>() {
                @Override
                public void onActivityResult(ActivityResult result) {
                    try {
                        if (RESULT_OK == result.getResultCode() && result.getData() != null && result.getData().getData() != null) {
                            Uri selectedImage = result.getData().getData();
                            Bitmap bitmap = null;
                            try {
                                bitmap = MediaStore.Images.Media.getBitmap(getActivity().getContentResolver(), selectedImage);
                                int bitmapSize = bitmap.getByteCount();
                                if (bitmapSize > MAX_BITMAP_SIZE) {
                                    Toast.makeText(getActivity(), "Too large image, Please try with small in size.", Toast.LENGTH_SHORT).show();
                                } else {
                                    imageBitMap = bitmap;
                                    ByteArrayOutputStream bytes = new ByteArrayOutputStream();
                                    bitmap.compress(Bitmap.CompressFormat.JPEG, 100, bytes);
                                    Drawable drawable = new BitmapDrawable(getResources(), bitmap);
                                    SpannableString ss = new SpannableString("abc");
                                    Drawable d = drawable;
                                    d.setBounds(0, 0, 800, 300);
                                    ImageSpan span = new ImageSpan(d, ImageSpan.ALIGN_BASELINE);
                                    ss.setSpan(span, 0, 3, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                                    Toast.makeText(getActivity(), "Image attached successfully.", Toast.LENGTH_SHORT).show();

                                    isPostImageAttached = true;
                                    isPostFileAttached = false;
                                    imageViewAttachClear.setVisibility(View.VISIBLE);
                                    buttonAttachImage.setText("Attached");
                                    imageViewAttached.setVisibility(View.VISIBLE);
                                    imageViewAttach.setVisibility(View.INVISIBLE);

                                    String[] filePathColumn = {MediaStore.Images.Media.DATA};
                                    Cursor cursor = getActivity().getContentResolver().query(selectedImage, filePathColumn, null, null, null);
                                    if (cursor != null) {
                                        cursor.moveToFirst();
                                        int columnIndex = cursor.getColumnIndex(filePathColumn[0]);
                                        if (columnIndex > -1) {
                                            attachmentPicturePath = cursor.getString(columnIndex);
                                            cursor.close();
                                            ContextWrapper wrapper = new ContextWrapper(getActivity());
                                            File directory = wrapper.getDir("profileImages", Context.MODE_PRIVATE);
                                            attachmentOutputFile = new File(directory, "cropped.png");
                                        }
                                    }
                                }
                            } catch (FileNotFoundException e) {
                                e.printStackTrace();
                            } catch (IOException e) {
                                e.printStackTrace();
                            } catch (IllegalStateException e) {
                                e.printStackTrace();
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });


    private ActivityResultLauncher<Intent> updateComments = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            new ActivityResultCallback<ActivityResult>() {
                @Override
                public void onActivityResult(ActivityResult result) {
                    try {
                        if (result.getResultCode() == RESULT_OK) {
                            try {
                                //For update comments
                                int postId = result.getData().getIntExtra("POST_ID", 0);
                                int postPositionBack = result.getData().getIntExtra("POSITION", -1);
                                boolean isCommentOrReplayPosted = result.getData().getBooleanExtra("POSTED_COMMENT", false);
                                if (isCommentOrReplayPosted) {
                                    postIdComment = String.valueOf(postId);
                                    getPostComments(postPositionBack);
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                }
            });

   /* @Override
    public void onActivityResult(int req, int result, @Nullable Intent data) {
        super.onActivityResult(req, result, data);
        try {
            if (result == RESULT_OK && req == LOAD_PDF_FILE) {
                Uri fileuri = data.getData();
                isPostFileAttached = true;
                isPostImageAttached = false;
                imageViewFileAttachClear.setVisibility(View.VISIBLE);
                buttonAttachFile.setText("Attached");
                imageViewFileAttached.setVisibility(View.VISIBLE);
                imageViewAttachFile.setVisibility(View.INVISIBLE);
                path = fileuri;

                String displayName = "sample.pdf";
                attachedFileName = displayName;
                try {
                    String uriString = fileuri.toString();
                    File myFile = new File(uriString);
                    String path = myFile.getAbsolutePath();
                    if (uriString.startsWith("content://")) {
                        Cursor cursor = null;
                        try {
                            cursor = getActivity().getContentResolver().query(fileuri, null, null, null, null);
                            if (cursor != null && cursor.moveToFirst()) {
                                displayName = cursor.getString(cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME));
                            }
                        } finally {
                            cursor.close();
                        }
                    } else if (uriString.startsWith("file://")) {
                        displayName = myFile.getName();
                    }
                } catch (Exception e) {
                    displayName = "sample.pdf";
                    e.printStackTrace();
                }

                attachedFileName = displayName;

                InputStream inputStream = null;
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                try {
                    inputStream = getActivity().getContentResolver().openInputStream(fileuri);

                    byte[] buffer = new byte[1024];
                    byteArrayOutputStream = new ByteArrayOutputStream();

                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        byteArrayOutputStream.write(buffer, 0, bytesRead);
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                } finally {
                    if (inputStream != null) {
                        try {
                            inputStream.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                }

                byte[] pdfByteArray = byteArrayOutputStream.toByteArray();
                dataFile = pdfByteArray;
            } else if (req == LOAD_IMAGE_FROM_GALLERY && result == RESULT_OK && data != null && data.getData() != null) {
                Uri selectedImage = data.getData();
                Bitmap bitmap = null;
                try {
                    bitmap = MediaStore.Images.Media.getBitmap(getActivity().getContentResolver(), selectedImage);
                    int bitmapSize = bitmap.getByteCount();
                    if (bitmapSize > MAX_BITMAP_SIZE) {
                        Toast.makeText(getActivity(), "Too large image, Please try with small in size.", Toast.LENGTH_SHORT).show();
                    } else {
                        imageBitMap = bitmap;
                        ByteArrayOutputStream bytes = new ByteArrayOutputStream();
                        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, bytes);
                        Drawable drawable = new BitmapDrawable(getResources(), bitmap);
                        SpannableString ss = new SpannableString("abc");
                        Drawable d = drawable;
                        d.setBounds(0, 0, 800, 300);
                        ImageSpan span = new ImageSpan(d, ImageSpan.ALIGN_BASELINE);
                        ss.setSpan(span, 0, 3, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                        Toast.makeText(getActivity(), "Image attached successfully.", Toast.LENGTH_SHORT).show();

                        isPostImageAttached = true;
                        isPostFileAttached = false;
                        imageViewAttachClear.setVisibility(View.VISIBLE);
                        buttonAttachImage.setText("Attached");
                        imageViewAttached.setVisibility(View.VISIBLE);
                        imageViewAttach.setVisibility(View.INVISIBLE);

                        String[] filePathColumn = {MediaStore.Images.Media.DATA};
                        Cursor cursor = getActivity().getContentResolver().query(selectedImage, filePathColumn, null, null, null);
                        if (cursor != null) {
                            cursor.moveToFirst();
                            int columnIndex = cursor.getColumnIndex(filePathColumn[0]);
                            if (columnIndex > -1) {
                                attachmentPicturePath = cursor.getString(columnIndex);
                                cursor.close();
                                ContextWrapper wrapper = new ContextWrapper(getActivity());
                                File directory = wrapper.getDir("profileImages", Context.MODE_PRIVATE);
                                attachmentOutputFile = new File(directory, "cropped.png");
                            }
                        }
                    }
                } catch (FileNotFoundException e) {
                    e.printStackTrace();
                } catch (IOException e) {
                    e.printStackTrace();
                } catch (IllegalStateException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else if (result == RESULT_OK && req == 100) {
                try {
                    //For update comments
                    int postId = data.getIntExtra("POST_ID", 0);
                    int postPositionBack = data.getIntExtra("POSITION", -1);
                    boolean isCommentOrReplayPosted = data.getBooleanExtra("POSTED_COMMENT", false);
                    if (isCommentOrReplayPosted) {
                        postIdComment = String.valueOf(postId);
                        getPostComments(postPositionBack);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }*/

    private void fetchGroupWallDetails() {
        try {
            studyGroup.getGroupWallDetails(new WSCallback() {
                @Override
                public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                    groupLoaderDetails.hide();
                    groupLoaderDetails.setVisibility(View.GONE);
                    if (jsonObject != null && !jsonObject.toString().equals("[]")) {
                        layoutEmptyPost.setVisibility(View.GONE);
                        groupId = jsonObject.optString("id");
                        groupName = jsonObject.optString("groupName");
                        //privacyType = jsonObject.optString("privacyType");
                        privacyType = "private";
                        //coverImage = jsonObject.optString("image");
                        userType = jsonObject.optString("userType");
                        Wonderslate.getInstance().getSharedPrefs().setUserType(userType);
                        //userType = "admin";
                        //groupVisibility = jsonObject.optString("visibility");
                        userStatus = jsonObject.optString("userExist");
                        groupColorCode = jsonObject.optString("colorCode");
                        isFromMyGroupList = true;
                        isAdmin = !userType.isEmpty() && userType.equalsIgnoreCase("admin");

                        if (privacyType.equalsIgnoreCase("public") && textViewPrivateMsg.getVisibility() == View.VISIBLE) {
                            textViewPrivateMsg.setVisibility(View.GONE);
                        } else if (!privacyType.equalsIgnoreCase("public") && textViewPrivateMsg.getVisibility() != View.VISIBLE) {
                            textViewPrivateMsg.setVisibility(View.VISIBLE);
                        }

                        textviewGroupNameHeader.setText(groupName);//once api done for prepjoy uncommend this


                        if (isFromDeepLink && !privacyType.equalsIgnoreCase("public")) {
                            //deeplink private group
                            isPublicGroup = false;
                            isCheckUserExit = true;
                            textViewPrivateMsg.setVisibility(View.VISIBLE);
                            shimmerLoadingLayout.stopShimmerAnimation();
                            shimmerLoadingLayout.setVisibility(View.GONE);
                            //isFromDeepLink = false;
                            textViewPrivateMsg.setVisibility(View.VISIBLE);
                            layoutPrivate.setVisibility(View.GONE);
                            if (userStatus.equalsIgnoreCase("yes")) {
                                if (isAdmin) {
                                    layoutSettings.setVisibility(View.VISIBLE);
                                    layoutReportGroup.setVisibility(View.INVISIBLE);
                                    layoutInviteGroup.setVisibility(View.VISIBLE);
                                    layoutJoinGroup.setVisibility(View.VISIBLE);
                                    layoutExitReportSpinner.setVisibility(View.GONE);
                                    layoutPost.setVisibility(View.VISIBLE);
                                    buttonJoin.setText("You're Admin");
                                    buttonJoin.setTextColor(ContextCompat.getColor(mContext, R.color.white));
                                    layoutJoinGroup.setBackgroundResource(R.drawable.shadow_curved_layout_primeary_bg);
                                    imageViewJoin.setVisibility(View.GONE);
                                    buttonJoin.setEnabled(false);
                                    layoutJoinGroup.setEnabled(false);
                                } else {
                                    layoutSettings.setVisibility(View.GONE);
                                    layoutReportGroup.setVisibility(View.INVISIBLE);
                                    layoutInviteGroup.setVisibility(View.VISIBLE);
                                    layoutJoinGroup.setVisibility(View.VISIBLE);
                                    layoutPost.setVisibility(View.VISIBLE);
                                    buttonJoin.setText("Exit Group");
                                    layoutJoinGroup.setVisibility(View.GONE);
                                    layoutExitReportSpinner.setVisibility(View.VISIBLE);
                                    imageViewJoin.setVisibility(View.GONE);
                                }

                                loadPageNo = 0;
                                pagesList.clear();
                                getGroupPostsDetailsList();
                            } else {
                                layoutSettings.setVisibility(View.GONE);
                                layoutReportGroup.setVisibility(View.INVISIBLE);
                                layoutInviteGroup.setVisibility(View.GONE);
                                layoutJoinGroup.setVisibility(View.VISIBLE);
                                layoutExitReportSpinner.setVisibility(View.GONE);
                                layoutPost.setVisibility(View.GONE);
                                buttonJoin.setText("Request to Join");
                                imageViewJoin.setVisibility(View.VISIBLE);
                                shimmerLoadingLayout.stopShimmerAnimation();
                                shimmerLoadingLayout.setVisibility(View.GONE);
                                //                            checkJoinRequestStatus();
                            }
                        } else if (isFromDeepLink && privacyType.equalsIgnoreCase("public")) {
                            //deeplink public group
                            isPublicGroup = true;
                            isCheckUserExit = true;
                            textViewPrivateMsg.setVisibility(View.GONE);
                            layoutPrivate.setVisibility(View.GONE);
                            if (userStatus.equalsIgnoreCase("yes")) {
                                //Joined already
                                if (isAdmin) {
                                    layoutSettings.setVisibility(View.VISIBLE);
                                    layoutReportGroup.setVisibility(View.INVISIBLE);
                                    layoutInviteGroup.setVisibility(View.VISIBLE);
                                    layoutJoinGroup.setVisibility(View.VISIBLE);
                                    layoutExitReportSpinner.setVisibility(View.GONE);
                                    layoutPost.setVisibility(View.VISIBLE);
                                    buttonJoin.setText("You're Admin");
                                    buttonJoin.setTextColor(ContextCompat.getColor(mContext, R.color.white));
                                    layoutJoinGroup.setBackgroundResource(R.drawable.shadow_curved_layout_primeary_bg);
                                    imageViewJoin.setVisibility(View.GONE);
                                    buttonJoin.setEnabled(false);
                                    layoutJoinGroup.setEnabled(false);
                                } else {
                                    layoutSettings.setVisibility(View.GONE);
                                    layoutReportGroup.setVisibility(View.INVISIBLE);
                                    layoutInviteGroup.setVisibility(View.VISIBLE);
                                    layoutJoinGroup.setVisibility(View.VISIBLE);
                                    layoutPost.setVisibility(View.VISIBLE);
                                    buttonJoin.setText("Exit Group");
                                    layoutJoinGroup.setVisibility(View.GONE);
                                    layoutExitReportSpinner.setVisibility(View.VISIBLE);
                                    imageViewJoin.setVisibility(View.GONE);
                                }
                            } else {
                                //Not Joined yet
                                layoutSettings.setVisibility(View.GONE);
                                layoutReportGroup.setVisibility(View.INVISIBLE);
                                layoutInviteGroup.setVisibility(View.GONE);
                                layoutJoinGroup.setVisibility(View.VISIBLE);
                                layoutExitReportSpinner.setVisibility(View.GONE);
                                layoutPost.setVisibility(View.GONE);
                                buttonJoin.setText("Join Group");
                                imageViewJoin.setVisibility(View.VISIBLE);
                            }

                            loadPageNo = 0;
                            pagesList.clear();
                            getGroupPostsDetailsList();
                        } else {
                            if (userStatus.equalsIgnoreCase("yes")) {
                                //Joined already
                                if (isAdmin) {
                                    layoutSettings.setVisibility(View.GONE);
                                    layoutReportGroup.setVisibility(View.GONE);
                                    layoutInviteGroup.setVisibility(View.GONE);
                                    layoutJoinGroup.setVisibility(View.GONE);
                                    layoutExitReportSpinner.setVisibility(View.GONE);
                                    layoutPost.setVisibility(View.VISIBLE);
                                    buttonJoin.setText("You're Admin");
                                    buttonJoin.setTextColor(ContextCompat.getColor(mContext, R.color.white));
                                    layoutJoinGroup.setBackgroundResource(R.drawable.shadow_curved_layout_primeary_bg);
                                    imageViewJoin.setVisibility(View.GONE);
                                    buttonJoin.setEnabled(false);
                                    layoutJoinGroup.setEnabled(false);
                                } else {
                                    layoutSettings.setVisibility(View.GONE);
                                    layoutReportGroup.setVisibility(View.INVISIBLE);
                                    layoutInviteGroup.setVisibility(View.GONE);
                                    layoutJoinGroup.setVisibility(View.GONE);
                                    layoutPost.setVisibility(View.GONE);
                                    //                                buttonJoin.setText("Exit Group");
                                    layoutJoinGroup.setVisibility(View.GONE);
                                    layoutExitReportSpinner.setVisibility(View.GONE);
                                    imageViewJoin.setVisibility(View.GONE);
                                }
                            } else {
                                //Not Joined yet
                                layoutSettings.setVisibility(View.GONE);
                                layoutReportGroup.setVisibility(View.INVISIBLE);
                                layoutInviteGroup.setVisibility(View.GONE);
                                layoutJoinGroup.setVisibility(View.VISIBLE);
                                layoutExitReportSpinner.setVisibility(View.GONE);
                                layoutPost.setVisibility(View.GONE);
                                buttonJoin.setText("Join Group");
                                imageViewJoin.setVisibility(View.VISIBLE);
                            }
                        }
                        //fetch group posts/feed
                        getGroupPostsDetailsList();

                    } else {
                        layoutEmptyPost.setVisibility(View.VISIBLE);
                    }

                }

                @Override
                public void onWSResultFailed(String resString, int responseCode) {
                    groupLoaderDetails.setVisibility(View.GONE);
                    progressBar.setVisibility(View.GONE);
                    swipeRefreshLayout.setRefreshing(false);
                    groupLoaderDetails.setVisibility(View.GONE);
                    recyclerViewGroupsDetails.setVisibility(View.GONE);
                    layoutEmptyPost.setVisibility(View.VISIBLE);
                    if (responseCode == 401) {
                        Toast.makeText(mContext, "Can not valiadate user, Please check login once.", Toast.LENGTH_SHORT).show();
                    } else if (responseCode == 404) {
                        Toast.makeText(mContext, "Server error! please try after some time.", Toast.LENGTH_SHORT).show();
                    } else {
                        Utils.showErrorToast(mContext, responseCode);
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void getGroupPostsDetailsList() {
        try {
            if(allowPagination) {
                loadPageNo++;
            }
            if (groupId != null && !groupId.isEmpty()) {
                if (!groupLoaderDetails.isShown())
                    groupLoaderDetails.setVisibility(View.VISIBLE);
                groupLoaderDetails.show();
                studyGroup.getGroupPostsDetails(groupId, String.valueOf(loadPageNo), new WSCallback() {
                    @Override
                    public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                        try {
                            shimmerLoadingLayout.stopShimmerAnimation();
                            shimmerLoadingLayout.setVisibility(View.GONE);
                            swipeRefreshLayout.setRefreshing(false);
                            progressBar.setVisibility(View.GONE);
                            String currentUserLogin = BuildConfig.SITE_ID + "_" + WonderPubSharedPrefs.getInstance(mContext).getUsermobile();
                            if (jsonObject != null) {
                                if (jsonObject.has("groupPostDetails") && !jsonObject.optString("groupPostDetails").equalsIgnoreCase("[]")) {
                                    layoutEmptyPost.setVisibility(View.GONE);
                                    JSONArray jsonArray = ServerResponseProcessingEngine.getJsonArray(jsonObject, "groupPostDetails");
                                    if (jsonArray != null) {
                                        postDataList.clear();
                                        for (int i = 0; i < jsonArray.length(); i++) {
                                            JSONObject object = jsonArray.getJSONObject(i);
                                            GroupPostsDataModel groupPostsDataModel = new GroupPostsDataModel();
                                            groupPostsDataModel.setId(object.optInt("id"));
                                            groupPostsDataModel.setDescription(object.optString("description"));
                                            groupPostsDataModel.setCreatedBy(object.optString("createdBy"));
                                            groupPostsDataModel.setDateCreated(object.optString("dateCreated"));
                                            groupPostsDataModel.setPostImage(object.optString("postImage"));
                                            groupPostsDataModel.setFileName(object.optString("fileName"));
                                            groupPostsDataModel.setFilePath(object.optString("filePath"));
                                            groupPostsDataModel.setName(object.optString("name"));
                                            groupPostsDataModel.setUserId(object.optInt("userId"));
                                            groupPostsDataModel.setProfilepic(object.optString("profilepic"));
                                            groupPostsDataModel.setUsername(object.optString("username"));
                                            groupPostsDataModel.setUserType(object.optString("userType"));
                                            JSONArray jsonCommentsArray = ServerResponseProcessingEngine.getJsonArray(object, "comments");
                                            if (jsonCommentsArray != null && !jsonCommentsArray.equals("[]")) {
                                                commendList = new ArrayList<>();
                                                for (int j = 0; j < jsonCommentsArray.length(); j++) {
                                                    JSONObject objectComments = jsonCommentsArray.getJSONObject(j);
                                                    Comments comments = new Comments();
                                                    comments.setId(objectComments.getInt("id"));
                                                    comments.setDateCreated(objectComments.optString("dateCreated"));
                                                    comments.setCreatedBy(objectComments.optString("createdBy"));
                                                    comments.setDescription(objectComments.optString("description"));
                                                    comments.setName(objectComments.optString("name"));
                                                    comments.setUserId(objectComments.optInt("userId"));
                                                    comments.setProfilepic(objectComments.optString("profilepic"));
                                                    comments.setUsername(objectComments.optString("username"));
                                                    JSONArray jsonReplayArray = ServerResponseProcessingEngine.getJsonArray(objectComments, "repliedDetailsByCommentsId");
                                                    if (jsonReplayArray != null && !jsonReplayArray.equals("[]")) {
                                                        replayList = new ArrayList<>();
                                                        for (int k = 0; k < jsonReplayArray.length(); k++) {
                                                            JSONObject objectReplay = jsonReplayArray.getJSONObject(k);
                                                            ReplayListDate replayListDate = new ReplayListDate();
                                                            replayListDate.setId(objectReplay.getInt("id"));
                                                            replayListDate.setDateCreated(objectReplay.optString("dateCreated"));
                                                            replayListDate.setCreatedBy(objectReplay.optString("createdBy"));
                                                            replayListDate.setDescription(objectReplay.optString("description"));
                                                            replayListDate.setName(objectReplay.optString("name"));
                                                            replayListDate.setUserId(objectReplay.optInt("userId"));
                                                            replayListDate.setProfilepic(objectReplay.optString("profilepic"));
                                                            replayListDate.setUsername(objectReplay.optString("username"));
                                                            replayListDate.setPostId(object.optInt("id"));
                                                            replayList.add(replayListDate);
                                                        }
                                                    }
                                                    comments.setRepliedDetailsByCommentsId(replayList);
                                                    comments.setRepliedCount(objectComments.optString("repliedCount"));
                                                    comments.setPostId(object.optInt("id"));
                                                    comments.setAdapterPosition(i);
                                                    commendList.add(comments);
                                                }
                                            }
                                            groupPostsDataModel.setComments(commendList);
                                            groupPostsDataModel.setCommentsCount(object.optInt("commentsCount"));
                                            groupPostsDataModel.setPostLikesCount(object.optInt("postLikesCount"));
                                            groupPostsDataModel.setLikedPost(object.optString("likedPost"));
                                            if (currentUserLogin.equalsIgnoreCase(String.valueOf(object.optString("userType")))) {
                                                groupPostsDataModel.setIsMyGroup(true);
                                            } else {
                                                groupPostsDataModel.setIsMyGroup(false);
                                            }
                                            postDataList.add(groupPostsDataModel);
                                        }
                                        layoutPrivate.setVisibility(View.GONE);
                                        recyclerViewGroupsDetails.setVisibility(View.VISIBLE);
                                        isNexPostListLoaded = true;
                                        setPostDateToAdapter(postDataList);
                                        //for pagination view
                                        if (!pagesList.contains(loadPageNo)) {
                                            pagesList.add(loadPageNo);
                                        }
                                        if (pagesList.size() > 1) {
                                            layoutPagenation.setVisibility(View.VISIBLE);
                                            isPaginationOn = true;
                                            setPostPageToAdapter(pagesList, loadPageNo);
                                        } else {
                                            layoutPagenation.setVisibility(View.GONE);
                                            isPaginationOn = false;
                                        }
                                    }
                                } else {
                                    if (loadPageNo == 0) {
                                        isLoadMoreBook = false;
                                        recyclerViewGroupsDetails.setVisibility(View.GONE);
                                        layoutEmptyPost.setVisibility(View.VISIBLE);
                                    } else {
                                        loadPageNo--;
                                        isLoadMoreBook = false;
                                    }
                                }
                            }
                            new Handler().postDelayed(() -> {
                                    groupLoaderDetails.hide();
                                    groupLoaderDetails.setVisibility(View.GONE);
                                }, 3000);
                        } catch (Exception e) {
                            progressBar.setVisibility(View.GONE);
                            swipeRefreshLayout.setRefreshing(false);
                            groupLoaderDetails.setVisibility(View.GONE);
                            recyclerViewGroupsDetails.setVisibility(View.GONE);
                            layoutEmptyPost.setVisibility(View.VISIBLE);
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void onWSResultFailed(String resString, int responseCode) {
                        progressBar.setVisibility(View.GONE);
                        swipeRefreshLayout.setRefreshing(false);
                        groupLoaderDetails.setVisibility(View.GONE);
                        recyclerViewGroupsDetails.setVisibility(View.GONE);
                        layoutEmptyPost.setVisibility(View.VISIBLE);
                        if (responseCode == 401) {
                            Toast.makeText(mContext, "Please check your login once.", Toast.LENGTH_SHORT).show();
                        } else if (responseCode == 404) {
                            Toast.makeText(mContext, "Server error! please try after some time.", Toast.LENGTH_SHORT).show();
                        } else {
                            Utils.showErrorToast(mContext, responseCode);
                        }
                    }
                });
            } else {
                Toast.makeText(mContext, "Cound not featch posts now, Please try after some time.", Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void uploadPostWithImage(String description) {
        try {
            if (internetConnectionChecker.isNetworkConnected(mContext)) {
                new UpdatePostImage(description).execute(attachmentPicturePath);
            } else {
                Toast.makeText(mContext, "Please check your Internet connection.", Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /* api call - update post image */
    private class UpdatePostImage extends AsyncTask<String, Void, Boolean> {
        String description;
        public UpdatePostImage(String description) {
            this.description = description;
        }

        @Override
        protected void onPreExecute() {
            if (!groupLoaderDetails.isShown())
                groupLoaderDetails.show();
        }

        @Override
        protected Boolean doInBackground(String... params) {
            Boolean result = Boolean.FALSE;
            try {
                String boundary = "*****";
                URL url = new URL(WSAPIManager.SERVICE3 + "groups/createPostImgFileForGroup");
                HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                conn.setRequestMethod("POST");
                conn.setRequestProperty("Connection", "Keep-Alive");
                conn.setRequestProperty("ENCTYPE", "multipart/form-data");
                conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);
                conn.setRequestProperty("X-Auth-Token", Wonderslate.getInstance().getSharedPrefs().getAccessToken());
                conn.setRequestProperty("Accept", "application/json");
                conn.setRequestProperty("Accept-Charset", "UTF-8");
                conn.setReadTimeout(30000);
                conn.setConnectTimeout(16000);

                MultipartEntityBuilder entityBuilder = MultipartEntityBuilder.create();

                entityBuilder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);

                if (attachmentOutputFile != null) {
                    ByteArrayOutputStream bos = new ByteArrayOutputStream();
                    Bitmap bitmap = imageBitMap == null ? BitmapFactory.decodeFile(String.valueOf(attachmentOutputFile)) : imageBitMap;
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 70, bos);
                    byte[] data = bos.toByteArray();

                    ByteArrayBody bab = new ByteArrayBody(data, "postImage.jpg");
                    entityBuilder.addPart("file", bab);
                    entityBuilder.addPart("type", new StringBody("image", ContentType.TEXT_PLAIN));
                }
                entityBuilder.addPart("groupId", new StringBody(groupId, ContentType.TEXT_PLAIN));
                if (postId != null && !postId.isEmpty() && postId.equalsIgnoreCase("null")) {
                    entityBuilder.addPart("postId", new StringBody(postId, ContentType.TEXT_PLAIN));
                }
                entityBuilder.addPart("description", new StringBody(description, ContentType.TEXT_PLAIN));
                entityBuilder.addPart("siteId", new StringBody(Wonderslate.getInstance().getSiteID(), ContentType.TEXT_PLAIN));

                HttpEntity entity = entityBuilder.build();

                conn.addRequestProperty("Content-length", entity.getContentLength() + "");
                conn.addRequestProperty(entity.getContentType().getName(), entity.getContentType().getValue());

                OutputStream os = conn.getOutputStream();
                entity.writeTo(conn.getOutputStream());
                os.close();
                conn.connect();

                if (conn.getResponseCode() == HttpURLConnection.HTTP_OK || conn.getResponseCode() == HttpURLConnection.HTTP_MOVED_TEMP) {
                    InputStream in = new BufferedInputStream(conn.getInputStream());
                    JSONObject obj = new JSONObject(readStream(in));
                    if (obj.has("status") && obj.optString("status").equalsIgnoreCase("success")) {
                        postImage = "postImage.jpg";
                        result = Boolean.TRUE;
                    } else {
                        result = Boolean.FALSE;
                    }
                } else {
                    result = Boolean.FALSE;
                }


            } catch (Exception e) {
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP && e instanceof ProtocolException)
                    result = Boolean.TRUE;
                return result;
            }

            return result;
        }

        private String readStream(InputStream in) {
            BufferedReader reader = null;
            StringBuilder builder = new StringBuilder();
            try {
                reader = new BufferedReader(new InputStreamReader(in));
                String line = "";
                while ((line = reader.readLine()) != null) {
                    builder.append(line);
                }
            } catch (IOException e) {
                Log.e(TAG, e.getMessage());
            } finally {
                if (reader != null) {
                    try {
                        reader.close();
                    } catch (IOException e) {
                        Log.e(TAG, e.getMessage());
                    }
                }
            }
            return builder.toString();
        }

        @Override
        protected void onPostExecute(Boolean uploaded) {
            groupLoaderDetails.hide();
            if (uploaded == null || !uploaded) {
                Toast.makeText(mContext, "Couldn't upload post! Please try after some time.", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(mContext, "Posted successfully.", Toast.LENGTH_SHORT).show();
                loadPageNo = 0;
                pagesList.clear();
                attachmentOutputFile = null;
                allowPagination = false;
                getGroupPostsDetailsList();
            }
        }
    }

    private class UpdatePostFile extends AsyncTask<String, Void, Boolean> {

        private Context c;
        String description;
        public UpdatePostFile(String description) {
            this.description = description;
        }

        @Override
        protected void onPreExecute() {
            if (!groupLoaderDetails.isShown())
                groupLoaderDetails.show();
        }

        @Override
        protected Boolean doInBackground(String... params) {
            Boolean result = Boolean.FALSE;
            try {
                String boundary = "*****";
                URL url = new URL(WSAPIManager.SERVICE3 + "groups/createPostImgFileForGroup");
                HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                conn.setRequestMethod("POST");
                conn.setRequestProperty("Connection", "Keep-Alive");
                conn.setRequestProperty("ENCTYPE", "multipart/form-data");
                conn.setRequestProperty("Content-Type", "multipart/form-data;boundary=" + boundary);
                conn.setRequestProperty("X-Auth-Token", Wonderslate.getInstance().getSharedPrefs().getAccessToken());
                conn.setRequestProperty("Accept", "application/json");
                conn.setRequestProperty("Accept-Charset", "UTF-8");
                conn.setReadTimeout(30000);
                conn.setConnectTimeout(16000);

                MultipartEntityBuilder entityBuilder = MultipartEntityBuilder.create();
                entityBuilder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);

                ByteArrayBody bab = new ByteArrayBody(dataFile, attachedFileName);
                entityBuilder.addPart("file", bab);
                entityBuilder.addPart("type", new StringBody("file", ContentType.TEXT_PLAIN));
                entityBuilder.addPart("groupId", new StringBody(groupId, ContentType.TEXT_PLAIN));
                if (postId != null && !postId.isEmpty() && postId.equalsIgnoreCase("null")) {
                    entityBuilder.addPart("postId", new StringBody(postId, ContentType.TEXT_PLAIN));
                }
                entityBuilder.addPart("description", new StringBody(description, ContentType.TEXT_PLAIN));
                entityBuilder.addPart("siteId", new StringBody(Wonderslate.getInstance().getSiteID(), ContentType.TEXT_PLAIN));

                HttpEntity entity = entityBuilder.build();

                conn.addRequestProperty("Content-length", entity.getContentLength() + "");
                conn.addRequestProperty(entity.getContentType().getName(), entity.getContentType().getValue());

                OutputStream os = conn.getOutputStream();
                entity.writeTo(conn.getOutputStream());
                os.close();
                conn.connect();

                if (conn.getResponseCode() == HttpURLConnection.HTTP_OK || conn.getResponseCode() == HttpURLConnection.HTTP_MOVED_TEMP) {
                    InputStream in = new BufferedInputStream(conn.getInputStream());
                    JSONObject obj = new JSONObject(readStream(in));
                    if (obj.has("status") && obj.optString("status").equalsIgnoreCase("success")) {
                        postFile = attachedFileName;
                        result = Boolean.TRUE;
                    } else {
                        result = Boolean.FALSE;
                    }
                } else {
                    result = Boolean.FALSE;
                }
            } catch (Exception e) {
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP && e instanceof ProtocolException)
                    result = Boolean.TRUE;
                return result;
            }

            return result;
        }

        private String readStream(InputStream in) {
            BufferedReader reader = null;
            StringBuilder builder = new StringBuilder();
            try {
                reader = new BufferedReader(new InputStreamReader(in));
                String line = "";
                while ((line = reader.readLine()) != null) {
                    builder.append(line);
                }
            } catch (IOException e) {
                Log.e(TAG, e.getMessage());
            } finally {
                if (reader != null) {
                    try {
                        reader.close();
                    } catch (IOException e) {
                        Log.e(TAG, e.getMessage());
                    }
                }
            }
            return builder.toString();
        }

        @Override
        protected void onPostExecute(Boolean uploaded) {
            groupLoaderDetails.hide();
            if (uploaded == null || !uploaded) {
                Toast.makeText(mContext, "Couldn't upload File! Please try after some time.", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(mContext, "Updated File successfully.", Toast.LENGTH_SHORT).show();
                loadPageNo = 0;
                pagesList.clear();
                getGroupPostsDetailsList();
            }
        }

    }

    private void uploadPostWithFile(String description) {
        try {
            if (internetConnectionChecker.isNetworkConnected(mContext)) {
                new UpdatePostFile(description).execute(attachmentFilePath);
            } else {
                Toast.makeText(mContext, "Please check your Internet connection.", Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void createPost(String description) {
        try {
            if (!groupLoaderDetails.isShown())
                groupLoaderDetails.setVisibility(View.VISIBLE);
            groupLoaderDetails.show();
            if (isPostImageAttached || isPostFileAttached) {
                if (isPostImageAttached) {
                    uploadPostWithImage(description);
                    editTextPost.setText("");
                    resourceToShareInGroup = "";
                    hideKeyboard(getActivity());
                    isPostImageAttached = false;
                    imageViewAttachClear.setVisibility(View.GONE);
                    imageViewAttached.setVisibility(View.GONE);
                    imageViewAttach.setVisibility(View.VISIBLE);
                    buttonAttachImage.setText("Attach a Image");
                    layoutAttachPostOpt.setVisibility(View.GONE);
                } else if (isPostFileAttached) {
                    uploadPostWithFile(description);
                    editTextPost.setText("");
                    isPostFileAttached = false;
                    imageViewFileAttachClear.setVisibility(View.GONE);
                    imageViewFileAttached.setVisibility(View.GONE);
                    imageViewAttachFile.setVisibility(View.VISIBLE);
                    buttonAttachFile.setText("Upload a File");
                    layoutAttachPostOpt.setVisibility(View.GONE);
                }
            }
            else {
                uploadPostWithImage(description);
                editTextPost.setText("");
                resourceToShareInGroup = "";
                hideKeyboard(getActivity());
                isPostImageAttached = false;
                imageViewAttachClear.setVisibility(View.GONE);
                imageViewAttached.setVisibility(View.GONE);
                imageViewAttach.setVisibility(View.VISIBLE);
                buttonAttachImage.setText("Attach a Image");
                layoutAttachPostOpt.setVisibility(View.GONE);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void editGroupPost(String description, String postID) {
        try {
            if (!groupLoaderDetails.isShown())
                groupLoaderDetails.setVisibility(View.VISIBLE);
            groupLoaderDetails.show();
            postId = postID;
            studyGroup.editPostForGroup(groupId, description, postID, new WSCallback() {
                @Override
                public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                    try {
                        if (jsonObject != null) {
                            if (jsonObject.has("status") && jsonObject.optString("status").equalsIgnoreCase("success")) {
                                GroupPostDataModel groupPostDataModel = new GroupPostDataModel();
                                groupPostDataModel.setGroupId(jsonObject.optString("groupId"));
                                groupPostDataModel.setPostId(jsonObject.optString("postId"));
                                postId = jsonObject.optString("postId");
                                editTextPost.setText("");
                                resourceToShareInGroup = "";
                                isEditPost = false;
                                hideKeyboard(getActivity());
                                if (isPostImageAttached || isPostFileAttached) {
                                    if (isPostImageAttached) {
                                        uploadPostWithImage(description);
                                    } else if (isPostFileAttached) {
                                        uploadPostWithFile(description);
                                    }
                                } else {
                                    getGroupPostsDetailsList();
                                }
                            } else {
                                Toast.makeText(mContext, "You are Fail.", Toast.LENGTH_SHORT).show();
                            }
                        }
                    } catch (Exception e) {
                        groupLoaderDetails.setVisibility(View.GONE);
                        e.printStackTrace();
                    }
                }

                @Override
                public void onWSResultFailed(String resString, int responseCode) {
                    groupLoaderDetails.setVisibility(View.GONE);
                    if (responseCode == 401) {
                        Toast.makeText(mContext, "Please check your login once.", Toast.LENGTH_SHORT).show();
                    } else if (responseCode == 404) {
                        Toast.makeText(mContext, "Server error! please try after some time.", Toast.LENGTH_SHORT).show();
                    } else {
                        Utils.showErrorToast(mContext, responseCode);
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private void setPostDateToAdapter(List<GroupPostsDataModel> postList) {
        try {
            adapter = new GroupWallDetailsAdapter(GroupWallFragment.this, getActivity(), getContext());
            adapter.setEntries(postList, isAdmin);
            recyclerViewGroupsDetails.setAdapter(adapter);
            if (isListBottom) {
                isListBottom = false;
                nestedScrollViewPost.scrollTo(0,0);
            }
            shimmerLoadingLayout.stopShimmerAnimation();
            shimmerLoadingLayout.setVisibility(View.GONE);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void hideProgressLoader() {
        groupLoaderDetails.hide();
        groupLoaderDetails.setVisibility(View.GONE);
    }
    private void setPostPageToAdapter(List<Integer> postPage, int currentPosition) {
        try {
            GroupWallPostPaginationAdapter postPaginationAdapter = new GroupWallPostPaginationAdapter(GroupWallFragment.this, getActivity());
            postPaginationAdapter.setEntries(postPage, currentPosition);
            recyclerViewPages.setAdapter(postPaginationAdapter);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void paginationClicked(int pageNo) {
        try {
            loadPageNo = pageNo;
            postDataListMain.clear();
            isLoadMoreBook = true;
            allowPagination = false;
            getGroupPostsDetailsList();
            //setPostPageToAdapter(pagesList, pageNo);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void shareGroupItem() {

    }

    public void reportUser(Integer postId, Integer userId, String userName) {
        try {
            lottieAnimationViewCommon.setAnimation(R.raw.lottie_report_user);
            layoutAction.setVisibility(View.VISIBLE);
            textViewAction.setText("Reporting...");

            studyGroup.reportGroupUser(groupId, "report user", userName, new WSCallback() {
                @Override
                public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                    try {
                        groupLoaderDetails.hide();
                        groupLoaderDetails.setVisibility(View.GONE);
                        if (jsonObject != null) {
                            if (jsonObject.has("status") && jsonObject.optString("status").equalsIgnoreCase("OK")) {
                                lottieAnimationViewCommon.setAnimation(R.raw.lottie_success);
                                textViewAction.setText("Successful.");
                                new Handler().postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        layoutAction.setVisibility(View.GONE);
                                    }
                                }, 2000);
                            } else {
                                Toast.makeText(mContext, "Can not Report now, Please try again.", Toast.LENGTH_SHORT).show();
                            }
                        }
                    } catch (Exception e) {
                        layoutAction.setVisibility(View.GONE);
                        e.printStackTrace();
                    }
                }

                @Override
                public void onWSResultFailed(String resString, int responseCode) {
                    layoutAction.setVisibility(View.GONE);
                    if (responseCode == 401) {
                        Toast.makeText(mContext, "Finding usert failed, Please check login once.", Toast.LENGTH_SHORT).show();
                    } else if (responseCode == 404) {
                        Toast.makeText(mContext, "Server error! please try after some time.", Toast.LENGTH_SHORT).show();
                    } else {
                        Utils.showErrorToast(mContext, responseCode);
                    }
                }
            });
        } catch (Resources.NotFoundException e) {
            e.printStackTrace();
        }
    }

    public void reportPost(Integer postId) {
        try {
            lottieAnimationViewCommon.setAnimation(R.raw.lottie_report);
            layoutAction.setVisibility(View.VISIBLE);
            textViewAction.setText("Reporting...");
            String userName = "mobile";
            studyGroup.reportPost(groupId, String.valueOf(postId), "Report Post", userName, new WSCallback() {
                @Override
                public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                    try {
                        groupLoaderDetails.hide();
                        groupLoaderDetails.setVisibility(View.GONE);
                        if (jsonObject != null) {
                            if (jsonObject.has("status") && jsonObject.optString("status").equalsIgnoreCase("OK")) {
                                lottieAnimationViewCommon.setAnimation(R.raw.lottie_success);
                                textViewAction.setText("Successful.");
                                new Handler().postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        layoutAction.setVisibility(View.GONE);
                                    }
                                }, 2000);
                            } else {
                                Toast.makeText(mContext, "Can not Report now, Please try again.", Toast.LENGTH_SHORT).show();
                            }
                        }
                    } catch (Exception e) {
                        layoutAction.setVisibility(View.GONE);
                        e.printStackTrace();
                    }
                }

                @Override
                public void onWSResultFailed(String resString, int responseCode) {
                    layoutAction.setVisibility(View.GONE);
                    if (responseCode == 401) {
                        Toast.makeText(mContext, "Finding usert failed, Please check login once.", Toast.LENGTH_SHORT).show();
                    } else if (responseCode == 404) {
                        Toast.makeText(mContext, "Server error! please try after some time.", Toast.LENGTH_SHORT).show();
                    } else {
                        Utils.showErrorToast(mContext, responseCode);
                    }
                }
            });
        } catch (Resources.NotFoundException e) {
            e.printStackTrace();
        }
    }

    public void deletePost(Integer postId, int position) {
        try {
            lottieAnimationViewCommon.setAnimation(R.raw.lottie_delete_post);
            layoutAction.setVisibility(View.VISIBLE);
            textViewAction.setText("Deleting.....");
            String userName = WonderPubSharedPrefs.getInstance(mContext).getUsername();
            studyGroup.deletePost(groupId, String.valueOf(postId), new WSCallback() {
                @Override
                public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                    try {
                        if (jsonObject != null) {
                            if (jsonObject.has("status") && jsonObject.optString("status").equalsIgnoreCase("OK")) {
                                lottieAnimationViewCommon.setAnimation(R.raw.lottie_success);
                                textViewAction.setText("Successful.");
                                if (position > 9) {
                                    int load = position / 10;
                                    loadPageNo = load;
                                    postDataListMain.clear();
                                } else {
                                    if (loadPageNo == 0) {
                                        loadPageNo = 0;
                                        postDataListMain.clear();
                                        pagesList.clear();
                                    } else {
                                        loadPageNo = loadPageNo;
                                        postDataListMain.clear();
                                    }
                                }
                                new Handler().postDelayed(() -> {
                                    groupLoaderDetails.hide();
                                    groupLoaderDetails.setVisibility(View.GONE);
                                    getGroupPostsDetailsList();
                                }, 1500);
                                new Handler().postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        layoutAction.setVisibility(View.GONE);
                                    }
                                }, 2000);
                            } else {
                                Toast.makeText(mContext, "Can not delete now, Please try after some time.", Toast.LENGTH_SHORT).show();
                            }
                        }
                    } catch (Exception e) {
                        layoutAction.setVisibility(View.GONE);
                        e.printStackTrace();
                    }
                }

                @Override
                public void onWSResultFailed(String resString, int responseCode) {
                    layoutAction.setVisibility(View.GONE);
                    if (responseCode == 401) {
                        Toast.makeText(mContext, "Finding usert failed, Please check login once.", Toast.LENGTH_SHORT).show();
                    } else if (responseCode == 404) {
                        Toast.makeText(mContext, "Can not delete now, Please try after some time.", Toast.LENGTH_SHORT).show();
                    } else {
                        Toast.makeText(mContext, "Can not delete now, Please try after some time.", Toast.LENGTH_SHORT).show();
                    }
                }
            });
        } catch (Resources.NotFoundException e) {
            e.printStackTrace();
        }
    }

    public void likePost(Integer postId, int position) {
        try {
            if (!groupLoaderDetails.isShown())
                groupLoaderDetails.setVisibility(View.VISIBLE);
            groupLoaderDetails.show();
            analyticsUtils.logEvent("Cafe Like", "Cafe");
            studyGroup.likePost(groupId, String.valueOf(postId), new WSCallback() {
                @Override
                public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                    try {
                        if (jsonObject != null) {
                            if (jsonObject.has("status") && jsonObject.optString("status").equalsIgnoreCase("OK")) {
                                if (position > 9) {
                                    int load = position / 10;
                                    loadPageNo = load;
                                    postDataListMain.clear();
                                } else {
                                    if (loadPageNo == 0) {
                                        loadPageNo = 0;
                                        postDataListMain.clear();
                                        pagesList.clear();
                                    } else {
                                        loadPageNo = loadPageNo;
                                        postDataListMain.clear();
                                    }
                                }
                                allowPagination = false;
                                getGroupPostsDetailsList();

                            } else {
                                dislikePost(postId, position);
                            }
                        }
                    } catch (Exception e) {
                        groupLoaderDetails.hide();
                        groupLoaderDetails.setVisibility(View.GONE);
                        e.printStackTrace();
                    }
                }

                @Override
                public void onWSResultFailed(String resString, int responseCode) {
                    groupLoaderDetails.hide();
                    groupLoaderDetails.setVisibility(View.GONE);
                    if (responseCode == 401) {
                        Toast.makeText(mContext, "Finding usert failed, Please check login once.", Toast.LENGTH_SHORT).show();
                    } else if (responseCode == 404) {
                        Toast.makeText(mContext, "Server error! please try after some time.", Toast.LENGTH_SHORT).show();
                    } else {
                        Utils.showErrorToast(mContext, responseCode);
                    }
                }
            });
        } catch (Resources.NotFoundException e) {
            e.printStackTrace();
        }
    }

    public void dislikePost(Integer postId, int position) {
        try {
            if (!groupLoaderDetails.isShown())
                groupLoaderDetails.setVisibility(View.VISIBLE);
            groupLoaderDetails.show();
            String userName = WonderPubSharedPrefs.getInstance(mContext).getUsername();
            studyGroup.dislikePost(groupId, String.valueOf(postId), new WSCallback() {
                @Override
                public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                    try {
                        if (jsonObject != null) {
                            if (jsonObject.has("status") && jsonObject.optString("status").equalsIgnoreCase("OK")) {
                                if (position > 9) {
                                    int load = position / 10;
                                    loadPageNo = load;
                                    postDataListMain.clear();
                                } else {
                                    if (loadPageNo == 0) {
                                        loadPageNo = 0;
                                        postDataListMain.clear();
                                        pagesList.clear();
                                    } else {
                                        loadPageNo = loadPageNo;
                                        postDataListMain.clear();
                                    }
                                }
                                getGroupPostsDetailsList();
                            } else {
                                Toast.makeText(mContext, "Can not perform this now, Please try again.", Toast.LENGTH_SHORT).show();
                            }
                        }
                    } catch (Exception e) {
                        groupLoaderDetails.hide();
                        groupLoaderDetails.setVisibility(View.GONE);
                        e.printStackTrace();
                    }
                }

                @Override
                public void onWSResultFailed(String resString, int responseCode) {
                    groupLoaderDetails.hide();
                    groupLoaderDetails.setVisibility(View.GONE);
                    if (responseCode == 401) {
                        Toast.makeText(mContext, "Finding usert failed, Please check login once.", Toast.LENGTH_SHORT).show();
                    } else if (responseCode == 404) {
                        Toast.makeText(mContext, "Server error! please try after some time.", Toast.LENGTH_SHORT).show();
                    } else {
                        Utils.showErrorToast(mContext, responseCode);
                    }
                }
            });
        } catch (Resources.NotFoundException e) {
            e.printStackTrace();
        }
    }

    public void sharePost() {

    }

    public void openCommenAndReplayView(GroupPostsDataModel groupPostsDataModel, int position) {
        try {
            Intent navigateToComments = new Intent(getActivity(), GroupWallCommentAndReplayActivity.class);
            navigateToComments.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
            navigateToComments.putExtra("POST_DATE", groupPostsDataModel);
            navigateToComments.putExtra("POST_POSITION", position);
            navigateToComments.putExtra("POST_GROUP_ID", groupId);
            updateComments.launch(navigateToComments);
            //startActivityForResult(navigateToComments, 100);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void commentPost(Integer postId, String comment, int position) {
        try {
            lottieAnimationViewCommon.setAnimation(R.raw.lottie_comment);
            layoutAction.setVisibility(View.VISIBLE);
            textViewAction.setText("Posting your comment.");
            analyticsUtils.logEvent("Cafe comment", "Cafe");
            studyGroup.commentPost(groupId, String.valueOf(postId), comment, new WSCallback() {
                @Override
                public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                    try {
                        if (jsonObject != null) {
                            if (jsonObject.has("status") && jsonObject.optString("status").equalsIgnoreCase("OK")) {
                                hideKeyboard(getActivity());
                                //getGroupPostsDetailsList();
                                postIdComment = String.valueOf(postId);
                                getPostComments(position);
                                lottieAnimationViewCommon.setAnimation(R.raw.lottie_success);
                                textViewAction.setText("Successful.");
                                new Handler().postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        layoutAction.setVisibility(View.GONE);
                                    }
                                }, 2000);
                            } else {
                                Toast.makeText(mContext, "Can not Comment now, Please try after some time.", Toast.LENGTH_SHORT).show();
                            }
                        }
                    } catch (Exception e) {
                        layoutAction.setVisibility(View.GONE);
                        e.printStackTrace();
                    }
                }

                @Override
                public void onWSResultFailed(String resString, int responseCode) {
                    layoutAction.setVisibility(View.GONE);
                    if (responseCode == 401) {
                        Toast.makeText(mContext, "Finding usert failed, Please check login once.", Toast.LENGTH_SHORT).show();
                    } else if (responseCode == 404) {
                        Toast.makeText(mContext, "Can not Comment now, Please try after some time.", Toast.LENGTH_SHORT).show();
                    } else {
                        Toast.makeText(mContext, "Can not Comment now, Please try after some time.", Toast.LENGTH_SHORT).show();
                    }
                }
            });
        } catch (Resources.NotFoundException e) {
            e.printStackTrace();
        }
    }

    public void replayToComment(Integer postId, Integer commentId, String replay, int position, int postPosition) {
        try {
            lottieAnimationViewCommon.setAnimation(R.raw.lottie_replay);
            layoutAction.setVisibility(View.VISIBLE);
            textViewAction.setText("Sending your reply..");
            String userName = "mobile";
            studyGroup.replayToCommentPost(groupId, String.valueOf(postId), String.valueOf(commentId), replay, new WSCallback() {
                @Override
                public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                    try {
                        if (jsonObject != null) {
                            if (jsonObject.has("status") && jsonObject.optString("status").equalsIgnoreCase("OK")) {
                                hideKeyboard(getActivity());
                                //getGroupPostsDetailsList();
                                postIdComment = String.valueOf(postId);
                                getPostComments(postPosition);
                                lottieAnimationViewCommon.setAnimation(R.raw.lottie_success);
                                new Handler().postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        layoutAction.setVisibility(View.GONE);
                                        textViewAction.setText("Successful.");
                                    }
                                }, 2000);
                            } else {
                                Toast.makeText(mContext, "Can not Like now, Please try after some time.", Toast.LENGTH_SHORT).show();
                            }
                        }
                    } catch (Exception e) {
                        layoutAction.setVisibility(View.GONE);
                        e.printStackTrace();
                    }
                }

                @Override
                public void onWSResultFailed(String resString, int responseCode) {
                    layoutAction.setVisibility(View.GONE);
                    if (responseCode == 401) {
                        Toast.makeText(mContext, "Finding usert failed, Please check login once.", Toast.LENGTH_SHORT).show();
                    } else if (responseCode == 404) {
                        Toast.makeText(mContext, "Can not Like now, Please try after some time.", Toast.LENGTH_SHORT).show();
                    } else {
                        Toast.makeText(mContext, "Can not Like now, Please try after some time.", Toast.LENGTH_SHORT).show();
                    }
                }
            });
        } catch (Resources.NotFoundException e) {
            e.printStackTrace();
        }
    }

    private void getPostComments(int position) {
        try {
            if (!groupLoaderDetails.isShown())
                groupLoaderDetails.setVisibility(View.VISIBLE);
            groupLoaderDetails.show();
            studyGroup.getCommentsList(postIdComment, "0", new WSCallback() {
                @Override
                public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                    try {
                        groupLoaderDetails.hide();
                        groupLoaderDetails.setVisibility(View.GONE);
                        if (jsonObject != null) {
                            if (jsonObject.has("commentsList") && !jsonObject.optString("commentsList").equalsIgnoreCase("[]")) {
                                JSONArray jsonCommentsArray = ServerResponseProcessingEngine.getJsonArray(jsonObject, "commentsList");
                                if (jsonCommentsArray != null && !jsonCommentsArray.equals("[]")) {
                                    commendList = new ArrayList<>();
                                    for (int j = 0; j < jsonCommentsArray.length(); j++) {
                                        JSONObject objectComments = jsonCommentsArray.getJSONObject(j);
                                        Comments comments = new Comments();
                                        comments.setId(objectComments.getInt("id"));
                                        comments.setDateCreated(objectComments.optString("dateCreated"));
                                        comments.setCreatedBy(objectComments.optString("createdBy"));
                                        comments.setDescription(objectComments.optString("description"));
                                        comments.setName(objectComments.optString("name"));
                                        comments.setUserId(objectComments.optInt("userId"));
                                        comments.setProfilepic(objectComments.optString("profilepic"));
                                        comments.setUsername(objectComments.optString("username"));
                                        JSONArray jsonReplayArray = ServerResponseProcessingEngine.getJsonArray(objectComments, "repliedDetailsByCommentsId");
                                        if (jsonReplayArray != null && !jsonReplayArray.equals("[]")) {
                                            replayList = new ArrayList<>();
                                            for (int k = 0; k < jsonReplayArray.length(); k++) {
                                                JSONObject objectReplay = jsonReplayArray.getJSONObject(k);
                                                ReplayListDate replayListDate = new ReplayListDate();
                                                replayListDate.setId(objectReplay.getInt("id"));
                                                replayListDate.setDateCreated(objectReplay.optString("dateCreated"));
                                                replayListDate.setCreatedBy(objectReplay.optString("createdBy"));
                                                replayListDate.setDescription(objectReplay.optString("description"));
                                                replayListDate.setName(objectReplay.optString("name"));
                                                replayListDate.setUserId(objectReplay.optInt("userId"));
                                                replayListDate.setProfilepic(objectReplay.optString("profilepic"));
                                                replayListDate.setUsername(objectReplay.optString("username"));
                                                replayListDate.setPostId(jsonObject.optInt("id"));
                                                replayList.add(replayListDate);
                                            }
                                        }
                                        comments.setRepliedDetailsByCommentsId(replayList);
                                        comments.setRepliedCount(objectComments.optString("repliedCount"));
                                        comments.setPostId(Integer.valueOf(postIdComment));
                                        comments.setAdapterPosition(position);
                                        commendList.add(comments);
                                    }
                                }
                                if (adapter == null)
                                    adapter = new GroupWallDetailsAdapter(GroupWallFragment.this, getActivity(), getContext());
                                adapter.updateCommentsList(commendList, position);
                                recyclerViewGroupsDetails.smoothScrollToPosition(position);
                            }
                        }
                    } catch (Exception e) {
                        groupLoaderDetails.setVisibility(View.GONE);
                        e.printStackTrace();
                    }
                }

                @Override
                public void onWSResultFailed(String resString, int responseCode) {
                    groupLoaderDetails.setVisibility(View.GONE);
                    if (responseCode == 401) {
                        Toast.makeText(mContext, "Finding usert failed, Please check login once.", Toast.LENGTH_SHORT).show();
                    } else if (responseCode == 404) {
                        Toast.makeText(mContext, "Server error! please try after some time.", Toast.LENGTH_SHORT).show();
                    } else {
                        Utils.showErrorToast(mContext, responseCode);
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //Download pdf file
    public void downloadPostFile(String postId, String fileNamepdf, String filePath) {
        try {
            if (!groupLoaderDetails.isShown())
                groupLoaderDetails.setVisibility(View.VISIBLE);
            groupLoaderDetails.show();

            fileName = fileNamepdf;
            String downloadFileUrl = WSAPIManager.URL_DOWNLOAD_POST + "?id=" + postId;
            Uri uri = Uri.parse(downloadFileUrl);

            //our logic
            downloadManager = (DownloadManager) mContext.getSystemService(Context.DOWNLOAD_SERVICE);
            DownloadManager.Request request = new DownloadManager.Request(uri);
            fileName = URLUtil.guessFileName(fileNamepdf, null, MimeTypeMap.getFileExtensionFromUrl(fileNamepdf));
            fileName = postId + "_" + fileNamepdf;
            fileZipped = new File(mContext.getExternalFilesDir(null) + DOWNLOADED_FILES + fileName);
            fileUnzipped = new File(mContext.getExternalFilesDir(null) + UNZIPPED_EPUB_FILES + fileName);

            if (fileZipped.exists()) {
                handleDownloadComplete();
            } else if (fileUnzipped.exists()) {
                handleDownloadComplete();
            } else {
                String token = WonderPubSharedPrefs.getInstance(mContext).getAccessToken();
                Log.d(TAG, "token: " + token);
                if (!token.matches("nil")) {
                    request.addRequestHeader("X-Auth-Token", token);
                }
                File file = new File(mContext.getExternalFilesDir(null) + DOWNLOADED_FILES + fileName);
                long availableSize = getAvailableInternalMemorySize();
                request.setDestinationUri(Uri.fromFile(file));
                request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_HIDDEN);
                myDownloadReference = downloadManager.enqueue(request);

                //downloadMsgTxt.setText(String.format("Downloading %s", fileName));
                downloadMsgTxt.setText(String.format("Downloading", " your file."));
                dialogLayout.setVisibility(View.VISIBLE);
                dialogProgressBar.setVisibility(View.VISIBLE);

                getActivity().getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE, WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
                final DownloadManager.Query q = new DownloadManager.Query();
                q.setFilterById(myDownloadReference);
                progressThread = new Thread() {
                    @Override
                    public void run() {
                        try {
                            downloading = true;
                            while (true) {
                                if (!Thread.currentThread().isInterrupted())
                                    sleep(500);
                                while (downloading) {
                                    try {
                                        int progress = 0;
                                        Cursor cursor = downloadManager.query(q);
                                        if (!cursor.moveToFirst())
                                            return;

                                        int bytesDownloaded = cursor.getInt(cursor
                                                .getColumnIndex(DownloadManager.COLUMN_BYTES_DOWNLOADED_SO_FAR));
                                        int bytesTotal = cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_TOTAL_SIZE_BYTES));

                                        if (availableSize < bytesTotal) {
                                            lowMemoryAlert(String.valueOf(bytesTotal));
                                            downloading = false;
                                            updateProgress(100);
                                            if (!Thread.currentThread().isInterrupted())
                                                Thread.currentThread().interrupt();
                                        } else if (cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_STATUS)) == DownloadManager.STATUS_SUCCESSFUL) {
                                            downloading = false;
                                            if (!Thread.currentThread().isInterrupted())
                                                Thread.currentThread().interrupt();
                                        } else if (cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_STATUS)) == DownloadManager.STATUS_FAILED || bytesTotal == 22) {
                                            downloadManager.remove(myDownloadReference);
                                            handleFailedDownload(bytesTotal == 22);
                                            downloading = false;
                                            updateProgress(100);
                                            if (!Thread.currentThread().isInterrupted())
                                                Thread.currentThread().interrupt();
                                        }
                                        if (bytesDownloaded < 0)
                                            bytesDownloaded = 0;

                                        try {
                                            progress = (int) ((bytesDownloaded * 100l) / bytesTotal);
                                        } catch (ArithmeticException e) {
                                            updateProgress(100);
                                            if (!Thread.currentThread().isInterrupted())
                                                Thread.currentThread().interrupt();
                                            cursor.close();
                                            Log.e(TAG, "Arithmetic Exception while downloading zip", e);
                                        }
                                        updateProgress(progress);

                                        cursor.close();

                                        if (progress == 100 && !Thread.currentThread().isInterrupted()) {
                                            Thread.currentThread().interrupt();
                                        }

                                    } catch (SQLiteException exception) {
                                        updateProgress(100);
                                        if (!Thread.currentThread().isInterrupted())
                                            Thread.currentThread().interrupt();
                                        Log.e("SQLite Exception", exception.getMessage());
                                    }

                                }
                            }
                        } catch (InterruptedException e) {
                            groupLoaderDetails.hide();
                            groupLoaderDetails.setVisibility(View.GONE);
                            updateProgress(100);
                            if (!Thread.currentThread().isInterrupted())
                                Thread.currentThread().interrupt();
                            Log.e(TAG, "Exception in Downloading ", e);
                        }
                    }
                };
                progressThread.start();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void lowMemoryAlert(String size) {
        groupLoaderDetails.hide();
        groupLoaderDetails.setVisibility(View.GONE);
        AlertDialog.Builder builder = new AlertDialog.Builder(mContext);
        builder.setTitle("Not enough storage space!");
        builder.setMessage("Require" + fileSize(size) + " free.");
        builder.setPositiveButton("OK", null);
        builder.setCancelable(false);
        final AlertDialog dialog = builder.create();
        Button okBtn;
        dialog.show();
        okBtn = dialog.getButton(DialogInterface.BUTTON_POSITIVE);
        okBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.setCancelable(true);
                dialog.dismiss();
            }
        });
    }

    private String fileSize(String longValue) {
        DecimalFormat format = new DecimalFormat("#.##");
        double length = Double.valueOf(longValue);
        String convertedSizeStr = "";
        if (length > 1024 * 1024) {
            convertedSizeStr = format.format(length / (1024 * 1024)) + " MB";
        } else if (length > 1024) {
            convertedSizeStr = format.format(length / 1024) + " KB";
        } else {
            convertedSizeStr = format.format(length) + " B";
        }
        return convertedSizeStr;
    }

    private void handleFailedDownload(boolean isBytes) {
        try {
            groupLoaderDetails.hide();
            groupLoaderDetails.setVisibility(View.GONE);
            File file = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).getPath() + "/" + fileZipped.getName());
            if (file.exists())
                deleteChildRecursive(file);
            getActivity().runOnUiThread(() -> {
                String message = isBytes ? "This file is not available." : "Download failed, please check your connection";
                Toast.makeText(mContext, "" + message, Toast.LENGTH_SHORT).show();
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void updateProgress(final int progress) {
        try {
            getActivity().runOnUiThread(() -> {
                dialogProgressBar.setProgress(progress);
                dialogProgressPercent.setText(progress + "%");

                if (progress == 100) {
                    groupLoaderDetails.hide();
                    groupLoaderDetails.setVisibility(View.GONE);
                    File file = new File(mContext.getExternalFilesDir(null).getPath() + "/" + "storage");
                    if (file.exists())
                        deleteChildRecursive(file);
                    dialogProgressBar.setVisibility(View.GONE);
                    dialogProgressBar.setProgress(0);
                    dialogProgressPercent.setText("Processing...");
                    downloading = false;
                    if (dialogLayout != null)
                        dialogLayout.setVisibility(View.GONE);

                    handleDownloadComplete();
                }
            });
        } catch (Exception e) {
            groupLoaderDetails.hide();
            groupLoaderDetails.setVisibility(View.GONE);
            e.printStackTrace();
        }
    }

    private void deleteChildRecursive(File file) {
        if (file.isDirectory())
            for (File child : file.listFiles()) {
                child.delete();
                deleteChildRecursive(child);
            }
        file.delete();
    }

    private void handleDownloadComplete() {
        try {
            groupLoaderDetails.hide();
            groupLoaderDetails.setVisibility(View.GONE);
            getActivity().getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
            MimeTypeMap map = MimeTypeMap.getSingleton();
            String ext = MimeTypeMap.getFileExtensionFromUrl(fileUnzipped.getName());
            String type = map.getMimeTypeFromExtension(ext);
            Intent intent = new Intent();
            intent.setAction(Intent.ACTION_VIEW);
            File file = new File(mContext.getExternalFilesDir(null) + DOWNLOADED_FILES + fileName);
            Uri apkURI = FileProvider.getUriForFile(
                    mContext,
                    getContext().getApplicationContext()
                            .getPackageName() + ".provider", file);
            intent.setDataAndType(apkURI, type);
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            getActivity().startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private long getAvailableInternalMemorySize() {
        File path = mContext.getExternalFilesDir(null);
        StatFs stat = new StatFs(path.getPath());
        long blockSize, availableBlocks;
        blockSize = stat.getBlockSizeLong();
        availableBlocks = stat.getAvailableBlocksLong();

        return availableBlocks * blockSize;
    }

    public void editPost(GroupPostsDataModel groupPostsDataModel) {
        try {
            slideIn = AnimationUtils.loadAnimation(mContext, R.anim.slide_top_in);
            sideUp = AnimationUtils.loadAnimation(mContext, R.anim.slide_up_animation);
            disableView.setVisibility(View.VISIBLE);
            disableViewTop.setVisibility(View.VISIBLE);
            imageViewCloseEditPost.setVisibility(View.VISIBLE);
            imageViewCloseEditPost.startAnimation(slideIn);
            isEditPost = true;
            postIdEdit = String.valueOf(groupPostsDataModel.getId());
            editTextPost.setText("" + groupPostsDataModel.getDescription());
            editTextPost.requestFocus();
            editTextPost.setSelection(editTextPost.getText().length());
            InputMethodManager imm = (InputMethodManager) mContext.getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.toggleSoftInput(InputMethodManager.SHOW_FORCED, InputMethodManager.HIDE_IMPLICIT_ONLY);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void openResource(String resLink, String profilePic, String name, String userId) {
        try {
            if (bottomSheetBehavior.getState() != BottomSheetBehavior.STATE_EXPANDED) {
                bottomSheetBehavior.setState(BottomSheetBehavior.STATE_EXPANDED);
            }
            groupLoaderDetails.setVisibility(View.VISIBLE);
            groupLoaderDetails.show();
            webViewGroupResOpen.getSettings().setJavaScriptEnabled(true);
            webViewGroupResOpen.getSettings().setSupportZoom(false);
            webViewGroupResOpen.getSettings().setBuiltInZoomControls(false);
            webViewGroupResOpen.setVerticalScrollBarEnabled(true);
            webViewGroupResOpen.setHorizontalScrollBarEnabled(true);
            webViewGroupResOpen.getSettings().setDisplayZoomControls(false);
            webViewGroupResOpen.getSettings().setLoadWithOverviewMode(true);
            webViewGroupResOpen.getSettings().setUseWideViewPort(true);

            webViewGroupResOpen.getSettings().setLoadsImagesAutomatically(true);
            webViewGroupResOpen.getSettings().setJavaScriptCanOpenWindowsAutomatically(true);

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                WebView.setWebContentsDebuggingEnabled(true);
            }

            webViewGroupResOpen.setWebViewClient(new WebViewClient() {
                public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                    try {
                        webViewGroupResOpen.loadUrl("about:blank");
                        webPageErrorLayout.setVisibility(View.VISIBLE);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public boolean shouldOverrideUrlLoading(WebView view, String url) {
                    groupLoaderDetails.show();
                    view.loadUrl(url);
                    return true;
                }

                @Override
                public void onPageFinished(WebView view, final String url) {
                    groupLoaderDetails.show();
                }
            });

            webViewGroupResOpen.setWebChromeClient(new WebChromeClient() {
                public boolean onConsoleMessage(ConsoleMessage cm) {
                    if (cm != null && cm.sourceId().length() > 0) {
                        Log.e(TAG, cm.message() + " -- From line "
                                + cm.lineNumber() + " of "
                                + cm.sourceId().substring(10));
                    } else if (cm != null) {
                        Log.e(TAG, cm.message() + " -- From line "
                                + cm.lineNumber());
                    }
                    return true;
                }

                @Override
                public void onProgressChanged(WebView view, int newProgress) {
                    super.onProgressChanged(view, newProgress);
                    if (newProgress == 100) {
                        groupLoaderDetails.smoothToHide();
                    }
                }
            });

            webPageErrorLayout.setVisibility(View.GONE);
            webViewGroupResOpen.loadUrl(resLink);

            textviewUserWebView.setText(name);
            if (profilePic != null && !profilePic.isEmpty() && !profilePic.equalsIgnoreCase("null")) {
                String image = WSAPIManager.URL_SELECTED_USER_IMAGE_API + "&" + HTTP_IMAGE_FILENAME + "="
                        + profilePic + "&" + HTTP_OBJECT_ID + "=" + userId;
                Glide.with(mContext)
                        .load(image)
                        .placeholder(R.drawable.profile_icon)
                        .into(imageViewAvatarWebView);
            } else if (profilePic == null || profilePic.equalsIgnoreCase("null")) {
                String image = WSAPIManager.URL_SELECTED_USER_IMAGE_API + "&" + HTTP_IMAGE_FILENAME + "="
                        + "profileImage.jpg" + "&" + HTTP_OBJECT_ID + "=" + userId;
                Glide.with(mContext)
                        .load(image)
                        .placeholder(R.drawable.profile_icon)
                        .into(imageViewAvatarWebView);
            } else {
                Glide.with(mContext)
                        .load(R.drawable.profile_icon)
                        .into(imageViewAvatarWebView);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void showFullImageView(String imagePath) {
        try {
            progressImageFullView.setVisibility(View.VISIBLE);
            layoutImageFullView.setVisibility(View.VISIBLE);
            disableView.setVisibility(View.VISIBLE);
            disableViewTop.setVisibility(View.VISIBLE);
            swipeRefreshLayout.setEnabled(false);
            Glide.with(mContext)
                    .load(imagePath)
                    //.placeholder(R.drawable.ic_people_bg_green)
                    .listener(new RequestListener<Drawable>() {
                        @Override
                        public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Drawable> target, boolean isFirstResource) {
                            progressImageFullView.setVisibility(View.GONE);
                            return false;
                        }

                        @Override
                        public boolean onResourceReady(Drawable resource, Object model, Target<Drawable> target, DataSource dataSource, boolean isFirstResource) {
                            progressImageFullView.setVisibility(View.GONE);
                            return false;
                        }
                    })
                    .into(imageViewFullView);
            isImageFullViewVisible = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void hideKeyboard(Activity activity) {
        try {
            InputMethodManager imm = (InputMethodManager) activity.getSystemService(Activity.INPUT_METHOD_SERVICE);
            View view = activity.getCurrentFocus();
            if (view == null) {
                view = new View(activity);
            }
            imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public void updateCafe()
    {
        try {
            loadPageNo = 0;
            postDataListMain.clear();
            pagesList.clear();
            isLoadMoreBook = true;
            layoutTop.setVisibility(View.GONE);
            hideKeyboard(getActivity());
            editTextPost.setText("");
            editTextPost.clearFocus();
            imageViewCloseEditPost.setVisibility(View.GONE);
            disableView.setVisibility(View.GONE);
            disableViewTop.setVisibility(View.GONE);
            //getGroupPostsDetailsList();
            fetchGroupWallDetails();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }
}