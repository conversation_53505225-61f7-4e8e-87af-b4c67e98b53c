package com.wonderslate.prepjoy.ui.groupwall;

import static com.wonderslate.prepjoy.Utils.Utils.disableScreenShot;

import android.app.Activity;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.ToggleButton;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.cardview.widget.CardView;
import androidx.core.content.ContextCompat;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.airbnb.lottie.LottieAnimationView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.CustomTarget;
import com.bumptech.glide.request.transition.Transition;
import com.bumptech.glide.signature.ObjectKey;
import com.wang.avi.AVLoadingIndicatorView;
import com.wonderslate.data.interfaces.WSCallback;
import com.wonderslate.data.models.Comments;
import com.wonderslate.data.models.GroupPostsDataModel;
import com.wonderslate.data.models.ReplayListDate;
import com.wonderslate.data.network.WSAPIManager;
import com.wonderslate.data.network.WSStudyGroup;
import com.wonderslate.data.preferences.WonderPubSharedPrefs;
import com.wonderslate.prepjoy.BuildConfig;
import com.wonderslate.prepjoy.R;
import com.wonderslate.prepjoy.Utils.Utils;
import com.wonderslate.prepjoy.Views.ServerResponseProcessingEngine;
import com.wonderslate.prepjoy.ui.BaseActivity;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import butterknife.BindView;
import butterknife.ButterKnife;

public class GroupWallCommentAndReplayActivity extends BaseActivity {

    @BindView(R.id.imgBack)
    AppCompatImageView imgBack;
    @BindView(R.id.txtBack)
    TextView txtBack;
    @BindView(R.id.rrltBack)
    RelativeLayout rrltBack;
    @BindView(R.id.toggleLanguage)
    ToggleButton toggleLanguage;
    @BindView(R.id.imageView_avatar)
    ImageView imageViewAvatar;
    @BindView(R.id.relativeLayout_avatar)
    RelativeLayout relativeLayoutAvatar;
    @BindView(R.id.textview_user)
    TextView textviewUser;
    @BindView(R.id.textview_date_time)
    TextView textviewDateTime;
    @BindView(R.id.layout_user)
    LinearLayout layoutUser;
    @BindView(R.id.textView_group_details_item)
    TextView textViewGroupDetailsItem;
    @BindView(R.id.imageView_post_content)
    ImageView imageViewPostContent;
    @BindView(R.id.post_image_card)
    CardView postImageCard;
    @BindView(R.id.imageView_post_file)
    ImageView imageViewPostFile;
    @BindView(R.id.textView_file_name)
    TextView textViewFileName;
    @BindView(R.id.layout_attachment)
    RelativeLayout layoutAttachment;
    @BindView(R.id.textView_comments)
    TextView textViewComments;
    @BindView(R.id.recyclerView_groups_comments_and_replay)
    RecyclerView recyclerViewGroupsCommentsAndReplay;
    @BindView(R.id.editText_comment)
    EditText editTextComment;
    @BindView(R.id.imageView_send_comment)
    ImageView imageViewSendComment;
    @BindView(R.id.layout_comment)
    RelativeLayout layoutComment;
    @BindView(R.id.layout_action)
    RelativeLayout layoutAction;
    @BindView(R.id.animationView_actions)
    LottieAnimationView lottieAnimationViewCommon;
    @BindView(R.id.textView_action)
    TextView textViewAction;
    @BindView(R.id.groupLoader_details)
    AVLoadingIndicatorView groupLoaderDetails;
    @BindView(R.id.group_wall_comment_reply_nested_scroll)
    NestedScrollView groupWallCommentReplyNestedScroll;

    private String HTTP_OBJECT_ID = "id";
    private String HTTP_IMAGE_FILENAME = "fileName";
    private WSStudyGroup studyGroup;
    private ObjectKey userImageGlideKey;
    private GroupPostsDataModel groupPostsData;
    private Integer postPosition;
    private String groupId;
    private String postId;
    private boolean isFromNotification;
    private List<Comments> commendList, finalCommentList;
    private List<ReplayListDate> replayList;
    private String postIdComment;
    private GroupWallDetailCommentsAdapter studyGroupCommendsAdapter;
    private boolean isCommentOrReplayPosted = false;
    private final List<GroupPostsDataModel> postDataList = new ArrayList<>();
    private int pageNo = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ButterKnife.bind(this);
        try {
            getSupportActionBar().hide();
            toggleLanguage.setVisibility(View.GONE);
            txtBack.setText("Discuss");
            init(getIntent());
            disableScreenShot(this);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected int getLayoutResource() {
        return R.layout.activity_group_wall_comment_and_replay;
    }

    /**
     * {@inheritDoc}
     * <p>
     * Handle onNewIntent() to inform the fragment manager that the
     * state is not saved.  If you are handling new intents and may be
     * making changes to the fragment state, you want to be sure to call
     * through to the super-class here first.  Otherwise, if your state
     * is saved but the activity is not stopped, you could get an
     * onNewIntent() call which happens before onResume() and trying to
     * perform fragment operations at that point will throw IllegalStateException
     * because the fragment manager thinks the state is still saved.
     *
     * @param intent
     */
    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        init(intent);
    }

    private void init(Intent intent) {
        try {
            studyGroup = new WSStudyGroup();
            finalCommentList = new ArrayList<>();
            if (intent != null && intent.hasExtra("POST_DATE")) {
                groupPostsData = (GroupPostsDataModel) intent.getSerializableExtra("POST_DATE");
                groupId = intent.getStringExtra("POST_GROUP_ID");
                postPosition = intent.getIntExtra("POST_POSITION", -1);

                setUpView();
            }

            if (intent != null && intent.hasExtra("POST_NOTIFICATION")) {
                if (intent.getBooleanExtra("POST_NOTIFICATION", false)) {
                    isFromNotification = intent.getBooleanExtra("POST_NOTIFICATION", false);
                    postPosition = 0;
                    groupId = intent.getStringExtra("groupId");
                    postId = intent.getStringExtra("postId");
                    getGroupPostsDetailsList();
                }
            }

            rrltBack.setOnClickListener(view -> {
                onBackPressed();
            });

            imageViewSendComment.setOnClickListener(view -> {
                imageViewSendComment.setEnabled(false);
                String comment = editTextComment.getText().toString().trim();
                if (!comment.isEmpty()) {
                    commentPost(groupPostsData.getId(), comment, postPosition);
                } else {
                    editTextComment.setHint("Please enter comment");
                    editTextComment.setHintTextColor(ContextCompat.getColor(this, R.color.primary_bg_red));
                    imageViewSendComment.setEnabled(true);
                }
            });

            textViewFileName.setOnClickListener(view -> {
                //Download file logic
                Toast.makeText(GroupWallCommentAndReplayActivity.this, "Please download this from Discuss.", Toast.LENGTH_SHORT).show();
            });
            if (!isFromNotification) {
                getPostComments();
            }

            groupWallCommentReplyNestedScroll.setOnScrollChangeListener(new NestedScrollView.OnScrollChangeListener() {
                @Override
                public void onScrollChange(NestedScrollView v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
                    try {
                        if (scrollY == v.getChildAt(0).getMeasuredHeight() - v.getMeasuredHeight()) {
                            pageNo = pageNo + 1;
                            getPostComments();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onBackPressed() {
        if (isFromNotification) {
            isFromNotification = false;
            Intent intent = new Intent(GroupWallCommentAndReplayActivity.this, GroupWallActivity.class);
            intent.putExtra("comingFrom", "notification");
            startActivity(intent);
            finish();
            super.onBackPressed();
        }
        else {
            try {
                Intent returnIntent = new Intent();
                returnIntent.putExtra("POST_ID", groupPostsData.getId());
                returnIntent.putExtra("POSITION", postPosition);
                returnIntent.putExtra("POSTED_COMMENT", isCommentOrReplayPosted);
                setResult(Activity.RESULT_OK, returnIntent);
                finish();
                super.onBackPressed();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void setUpView() {
        try {
            recyclerViewGroupsCommentsAndReplay.setLayoutManager(new LinearLayoutManager(this));
            recyclerViewGroupsCommentsAndReplay.setHasFixedSize(true);
            //recyclerViewGroupsCommentsAndReplay.setNestedScrollingEnabled(true);

            if (groupPostsData.getUserType().equalsIgnoreCase("admin")) {
                textviewUser.setText("admin");
            }
            else {
                textviewUser.setText(groupPostsData.getName());
            }
            if (groupPostsData.getDescription() == null || groupPostsData.getDescription().equalsIgnoreCase("null") || groupPostsData.getDescription().isEmpty()) {
                textViewGroupDetailsItem.setVisibility(View.GONE);
            } else {
                textViewGroupDetailsItem.setVisibility(View.VISIBLE);
                textViewGroupDetailsItem.setText(groupPostsData.getDescription());
            }

            if (userImageGlideKey == null)
                userImageGlideKey = new ObjectKey(System.currentTimeMillis());
            if (groupPostsData.getProfilepic() != null && !groupPostsData.getProfilepic().isEmpty() && !groupPostsData.getProfilepic().equalsIgnoreCase("null")) {
                String image = WSAPIManager.URL_SELECTED_USER_IMAGE_API + "&" + HTTP_IMAGE_FILENAME + "="
                        + groupPostsData.getProfilepic() + "&" + HTTP_OBJECT_ID + "=" + groupPostsData.getUserId();
                Glide.with(this)
                        .load(image)
                        .signature(userImageGlideKey)
                        .placeholder(R.drawable.app_icon)
                        .into(imageViewAvatar);
            } else if (groupPostsData.getProfilepic() == null || groupPostsData.getProfilepic().equalsIgnoreCase("null")) {
                String image = WSAPIManager.URL_SELECTED_USER_IMAGE_API + "&" + HTTP_IMAGE_FILENAME + "="
                        + "profileImage.jpg" + "&" + HTTP_OBJECT_ID + "=" + groupPostsData.getUserId();
                Glide.with(this)
                        .load(image)
                        .signature(userImageGlideKey)
                        .placeholder(R.drawable.app_icon)
                        .into(imageViewAvatar);
            } else {
                Glide.with(this)
                        .load(R.drawable.app_icon)
                        .signature(userImageGlideKey)
                        .into(imageViewAvatar);
            }

            textviewDateTime.setText("" + groupPostsData.getDateCreated());

            //For Commends and Replays
            recyclerViewGroupsCommentsAndReplay.setVisibility(View.GONE);
            if (groupPostsData.getComments().size() != 0) {
                Collections.reverse(groupPostsData.getComments());
                studyGroupCommendsAdapter = new GroupWallDetailCommentsAdapter(GroupWallCommentAndReplayActivity.this, groupPostsData.getComments());
                recyclerViewGroupsCommentsAndReplay.setAdapter(studyGroupCommendsAdapter);
                textViewComments.setVisibility(View.GONE);
                recyclerViewGroupsCommentsAndReplay.setVisibility(View.VISIBLE);
            } else {
                textViewComments.setVisibility(View.GONE);
            }

            //For post file and image attachment
            if (groupPostsData.getPostImage() != null && !groupPostsData.getPostImage().equals("null")) {
                imageViewPostContent.setVisibility(View.VISIBLE);
                postImageCard.setVisibility(View.VISIBLE);
                imageViewPostFile.setVisibility(View.GONE);
                textViewFileName.setVisibility(View.GONE);
                String imagePost = WSAPIManager.SERVICE3 + "groups/showGroupPostImage?id=" + groupPostsData.getId() + "&fileName=" + groupPostsData.getPostImage();
                Glide.with(this)
                        .asBitmap()
                        .load(imagePost)
                        .into(new CustomTarget<Bitmap>() {
                            @Override
                            public void onResourceReady(@NonNull Bitmap resource, @Nullable Transition<? super Bitmap> transition) {
                                imageViewPostContent.setImageBitmap(resource);
                            }

                            @Override
                            public void onLoadCleared(@Nullable Drawable placeholder) {

                            }
                        });
            } else {
                imageViewPostContent.setVisibility(View.GONE);
                postImageCard.setVisibility(View.GONE);
            }

            if (groupPostsData.getFileName() != null && !groupPostsData.getFileName().equals("null")) {
                imageViewPostFile.setVisibility(View.VISIBLE);
                textViewFileName.setVisibility(View.VISIBLE);
                textViewFileName.setText("" + groupPostsData.getFileName());
                //textViewFileName.setText("" + "attachment_file.pdf");
                imageViewPostContent.setVisibility(View.GONE);
                postImageCard.setVisibility(View.GONE);
            } else {
                imageViewPostFile.setVisibility(View.GONE);
                textViewFileName.setVisibility(View.GONE);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void commentPost(Integer postId, String comment, int position) {
        try {
            lottieAnimationViewCommon.setAnimation(R.raw.lottie_comment);
            layoutAction.setVisibility(View.VISIBLE);
            textViewAction.setText("Posting your comment.");
            postIdComment = String.valueOf(postId);
            studyGroup.commentPost(groupId, String.valueOf(postId), comment, new WSCallback() {
                @Override
                public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                    try {
                        if (jsonObject != null) {
                            if (jsonObject.has("status") && jsonObject.optString("status").equalsIgnoreCase("OK")) {
                                hideKeyboard(GroupWallCommentAndReplayActivity.this);
                                lottieAnimationViewCommon.setAnimation(R.raw.lottie_success);
                                textViewAction.setText("Successful.");
                                new Handler().postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        layoutAction.setVisibility(View.GONE);
                                    }
                                }, 2000);
                                isCommentOrReplayPosted = true;
                                finalCommentList.clear();
                                pageNo = 0;
                                getPostComments();
                            } else {
                                Toast.makeText(GroupWallCommentAndReplayActivity.this, "Can not Comment now, Please try after some time.", Toast.LENGTH_SHORT).show();
                            }
                        }
                    } catch (Exception e) {
                        layoutAction.setVisibility(View.GONE);
                        e.printStackTrace();
                    }
                }

                @Override
                public void onWSResultFailed(String resString, int responseCode) {
                    layoutAction.setVisibility(View.GONE);
                    if (responseCode == 401) {
                        Toast.makeText(GroupWallCommentAndReplayActivity.this, "Finding usert failed, Please check login once.", Toast.LENGTH_SHORT).show();
                    } else if (responseCode == 404) {
                        Toast.makeText(GroupWallCommentAndReplayActivity.this, "Can not Comment now, Please try after some time.", Toast.LENGTH_SHORT).show();
                    } else {
                        Toast.makeText(GroupWallCommentAndReplayActivity.this, "Can not Comment now, Please try after some time.", Toast.LENGTH_SHORT).show();
                    }
                }
            });
        } catch (Resources.NotFoundException e) {
            e.printStackTrace();
        }
    }

    public void scrollNestedScrollToBottom() {
        groupWallCommentReplyNestedScroll.fullScroll(View.FOCUS_DOWN);
    }

    public void scrollNestedScrollToTop() {

    }



    public void replayToComment(Integer postId, Integer commentId, String replay) {
        try {
            lottieAnimationViewCommon.setAnimation(R.raw.lottie_replay);
            layoutAction.setVisibility(View.VISIBLE);
            textViewAction.setText("Sending your reply..");
            studyGroup.replayToCommentPost(groupId, String.valueOf(postId), String.valueOf(commentId), replay, new WSCallback() {
                @Override
                public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                    try {
                        if (jsonObject != null) {
                            if (jsonObject.has("status") && jsonObject.optString("status").equalsIgnoreCase("OK")) {
                                hideKeyboard(GroupWallCommentAndReplayActivity.this);
                                postIdComment = String.valueOf(postId);
                                isCommentOrReplayPosted = true;
                                finalCommentList.clear();
                                pageNo = 0;
                                getPostComments();
                                lottieAnimationViewCommon.setAnimation(R.raw.lottie_success);
                                new Handler().postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        layoutAction.setVisibility(View.GONE);
                                        textViewAction.setText("Successful.");
                                    }
                                }, 2000);
                            } else {
                                Toast.makeText(GroupWallCommentAndReplayActivity.this, "Can not Like now, Please try after some time.", Toast.LENGTH_SHORT).show();
                            }
                        }
                    } catch (Exception e) {
                        layoutAction.setVisibility(View.GONE);
                        e.printStackTrace();
                    }
                }

                @Override
                public void onWSResultFailed(String resString, int responseCode) {
                    layoutAction.setVisibility(View.GONE);
                    if (responseCode == 401) {
                        Toast.makeText(GroupWallCommentAndReplayActivity.this, "Finding usert failed, Please check login once.", Toast.LENGTH_SHORT).show();
                    } else if (responseCode == 404) {
                        Toast.makeText(GroupWallCommentAndReplayActivity.this, "Can not Like now, Please try after some time.", Toast.LENGTH_SHORT).show();
                    } else {
                        Toast.makeText(GroupWallCommentAndReplayActivity.this, "Can not Like now, Please try after some time.", Toast.LENGTH_SHORT).show();
                    }
                }
            });
        } catch (Resources.NotFoundException e) {
            e.printStackTrace();
        }
    }

    private void getPostComments() {
        try {
            groupLoaderDetails.setVisibility(View.VISIBLE);
            groupLoaderDetails.show();
            studyGroup.getCommentsList(postIdComment==null? groupPostsData.getId().toString():postIdComment, String.valueOf(pageNo), new WSCallback() {
                @Override
                public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                    try {
                        if (jsonObject != null) {
                            if (jsonObject.has("commentsList") && !jsonObject.optString("commentsList").equalsIgnoreCase("[]") &&
                                    !jsonObject.optString("commentsList").equalsIgnoreCase("no records")) {
                                JSONArray jsonCommentsArray = ServerResponseProcessingEngine.getJsonArray(jsonObject, "commentsList");
                                if (jsonCommentsArray != null && !jsonCommentsArray.equals("[]")) {
                                    commendList = new ArrayList<>();
                                    for (int j = 0; j < jsonCommentsArray.length(); j++) {
                                        JSONObject objectComments = jsonCommentsArray.getJSONObject(j);
                                        Comments comments = new Comments();
                                        comments.setId(objectComments.getInt("id"));
                                        comments.setDateCreated(objectComments.optString("dateCreated"));
                                        comments.setCreatedBy(objectComments.optString("createdBy"));
                                        comments.setDescription(objectComments.optString("description"));
                                        comments.setName(objectComments.optString("name"));
                                        comments.setUserId(objectComments.optInt("userId"));
                                        comments.setProfilepic(objectComments.optString("profilepic"));
                                        comments.setUsername(objectComments.optString("username"));
                                        JSONArray jsonReplayArray = ServerResponseProcessingEngine.getJsonArray(objectComments, "repliedDetailsByCommentsId");
                                        if (jsonReplayArray != null && !jsonReplayArray.equals("[]")) {
                                            replayList = new ArrayList<>();
                                            for (int k = 0; k < jsonReplayArray.length(); k++) {
                                                JSONObject objectReplay = jsonReplayArray.getJSONObject(k);
                                                ReplayListDate replayListDate = new ReplayListDate();
                                                replayListDate.setId(objectReplay.getInt("id"));
                                                replayListDate.setDateCreated(objectReplay.optString("dateCreated"));
                                                replayListDate.setCreatedBy(objectReplay.optString("createdBy"));
                                                replayListDate.setDescription(objectReplay.optString("description"));
                                                replayListDate.setName(objectReplay.optString("name"));
                                                replayListDate.setUserId(objectReplay.optInt("userId"));
                                                replayListDate.setProfilepic(objectReplay.optString("profilepic"));
                                                replayListDate.setUsername(objectReplay.optString("username"));
                                                replayListDate.setPostId(jsonObject.optInt("id"));
                                                replayList.add(replayListDate);
                                            }
                                        }
                                        comments.setRepliedDetailsByCommentsId(replayList);
                                        comments.setRepliedCount(objectComments.optString("repliedCount"));
                                        comments.setPostId(Integer.valueOf(postIdComment==null? groupPostsData.getId().toString():postIdComment));
                                        commendList.add(comments);
                                    }
                                }

                                Collections.reverse(commendList);
                                finalCommentList.addAll(commendList);
                                if (!commendList.isEmpty()) {
                                    studyGroupCommendsAdapter = new GroupWallDetailCommentsAdapter(GroupWallCommentAndReplayActivity.this, finalCommentList);
                                    recyclerViewGroupsCommentsAndReplay.setAdapter(studyGroupCommendsAdapter);
                                    recyclerViewGroupsCommentsAndReplay.setVisibility(View.VISIBLE);
                                    studyGroupCommendsAdapter.notifyDataSetChanged();
                                    editTextComment.setText("");
                                    editTextComment.clearFocus();
                                    imageViewSendComment.setEnabled(true);
                                }
                            }
                            groupLoaderDetails.hide();
                            groupLoaderDetails.setVisibility(View.GONE);
                        }
                    } catch (Exception e) {
                        groupLoaderDetails.setVisibility(View.GONE);
                        imageViewSendComment.setEnabled(true);
                        e.printStackTrace();
                    }
                }

                @Override
                public void onWSResultFailed(String resString, int responseCode) {
                    groupLoaderDetails.setVisibility(View.GONE);
                    if (responseCode == 401) {
                        Toast.makeText(GroupWallCommentAndReplayActivity.this, "Finding usert failed, Please check login once.", Toast.LENGTH_SHORT).show();
                    } else if (responseCode == 404) {
                        Toast.makeText(GroupWallCommentAndReplayActivity.this, "Server error! please try after some time.", Toast.LENGTH_SHORT).show();
                    } else {
                        Utils.showErrorToast(GroupWallCommentAndReplayActivity.this, responseCode);
                    }
                    imageViewSendComment.setEnabled(true);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            imageViewSendComment.setEnabled(true);
        }
    }

    public void hideKeyboard(Activity activity) {
        try {
            InputMethodManager imm = (InputMethodManager) activity.getSystemService(Activity.INPUT_METHOD_SERVICE);
            View view = activity.getCurrentFocus();
            if (view == null) {
                view = new View(activity);
            }
            imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void getGroupPostsDetailsList() {
        try {
            if (groupId != null || !groupId.isEmpty()) {
                if (!groupLoaderDetails.isShown())
                    groupLoaderDetails.setVisibility(View.VISIBLE);
                groupLoaderDetails.show();
                studyGroup.getGroupPostsDetails(groupId, "0", new WSCallback() {
                    @Override
                    public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                        try {
                            String currentUserLogin = BuildConfig.SITE_ID + "_" + WonderPubSharedPrefs.getInstance(GroupWallCommentAndReplayActivity.this).getUsermobile();
                            if (jsonObject != null) {
                                if (jsonObject.has("groupPostDetails") && !jsonObject.optString("groupPostDetails").equalsIgnoreCase("[]")) {
                                    JSONArray jsonArray = ServerResponseProcessingEngine.getJsonArray(jsonObject, "groupPostDetails");
                                    if (jsonArray != null) {
                                        postDataList.clear();
                                        for (int i = 0; i < jsonArray.length(); i++) {
                                            JSONObject object = jsonArray.getJSONObject(i);
                                            GroupPostsDataModel groupPostsDataModel = new GroupPostsDataModel();
                                            groupPostsDataModel.setId(object.optInt("id"));
                                            groupPostsDataModel.setDescription(object.optString("description"));
                                            groupPostsDataModel.setCreatedBy(object.optString("createdBy"));
                                            groupPostsDataModel.setDateCreated(object.optString("dateCreated"));
                                            groupPostsDataModel.setPostImage(object.optString("postImage"));
                                            groupPostsDataModel.setFileName(object.optString("fileName"));
                                            groupPostsDataModel.setFilePath(object.optString("filePath"));
                                            groupPostsDataModel.setName(object.optString("name"));
                                            groupPostsDataModel.setUserId(object.optInt("userId"));
                                            groupPostsDataModel.setProfilepic(object.optString("profilepic"));
                                            groupPostsDataModel.setUsername(object.optString("username"));
                                            groupPostsDataModel.setUserType(object.optString("userType"));
                                            JSONArray jsonCommentsArray = ServerResponseProcessingEngine.getJsonArray(object, "comments");
                                            if (jsonCommentsArray != null && !jsonCommentsArray.equals("[]")) {
                                                commendList = new ArrayList<>();
                                                for (int j = 0; j < jsonCommentsArray.length(); j++) {
                                                    JSONObject objectComments = jsonCommentsArray.getJSONObject(j);
                                                    Comments comments = new Comments();
                                                    comments.setId(objectComments.getInt("id"));
                                                    comments.setDateCreated(objectComments.optString("dateCreated"));
                                                    comments.setCreatedBy(objectComments.optString("createdBy"));
                                                    comments.setDescription(objectComments.optString("description"));
                                                    comments.setName(objectComments.optString("name"));
                                                    comments.setUserId(objectComments.optInt("userId"));
                                                    comments.setProfilepic(objectComments.optString("profilepic"));
                                                    comments.setUsername(objectComments.optString("username"));
                                                    JSONArray jsonReplayArray = ServerResponseProcessingEngine.getJsonArray(objectComments, "repliedDetailsByCommentsId");
                                                    if (jsonReplayArray != null && !jsonReplayArray.equals("[]")) {
                                                        replayList = new ArrayList<>();
                                                        for (int k = 0; k < jsonReplayArray.length(); k++) {
                                                            JSONObject objectReplay = jsonReplayArray.getJSONObject(k);
                                                            ReplayListDate replayListDate = new ReplayListDate();
                                                            replayListDate.setId(objectReplay.getInt("id"));
                                                            replayListDate.setDateCreated(objectReplay.optString("dateCreated"));
                                                            replayListDate.setCreatedBy(objectReplay.optString("createdBy"));
                                                            replayListDate.setDescription(objectReplay.optString("description"));
                                                            replayListDate.setName(objectReplay.optString("name"));
                                                            replayListDate.setUserId(objectReplay.optInt("userId"));
                                                            replayListDate.setProfilepic(objectReplay.optString("profilepic"));
                                                            replayListDate.setUsername(objectReplay.optString("username"));
                                                            replayListDate.setPostId(object.optInt("id"));
                                                            replayList.add(replayListDate);
                                                        }
                                                    }
                                                    comments.setRepliedDetailsByCommentsId(replayList);
                                                    comments.setRepliedCount(objectComments.optString("repliedCount"));
                                                    comments.setPostId(object.optInt("id"));
                                                    comments.setAdapterPosition(i);
                                                    commendList.add(comments);
                                                }
                                            }
                                            groupPostsDataModel.setComments(commendList);
                                            groupPostsDataModel.setCommentsCount(object.optInt("commentsCount"));
                                            groupPostsDataModel.setPostLikesCount(object.optInt("postLikesCount"));
                                            groupPostsDataModel.setLikedPost(object.optString("likedPost"));
                                            if (currentUserLogin.equalsIgnoreCase(String.valueOf(object.optString("userType")))) {
                                                groupPostsDataModel.setIsMyGroup(true);
                                            } else {
                                                groupPostsDataModel.setIsMyGroup(false);
                                            }
                                            postDataList.add(groupPostsDataModel);
                                            for (GroupPostsDataModel groupPostModel:postDataList) {
                                                if (Objects.equals(groupPostModel.getId(), Integer.valueOf(postId))) {
                                                    groupPostsData = groupPostModel;
                                                    setUpView();
                                                    getPostComments();
                                                    return;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            new Handler().postDelayed(() -> {
                                groupLoaderDetails.hide();
                                groupLoaderDetails.setVisibility(View.GONE);
                            }, 3000);
                        } catch (Exception e) {
                            groupLoaderDetails.setVisibility(View.GONE);
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void onWSResultFailed(String resString, int responseCode) {
                        groupLoaderDetails.setVisibility(View.GONE);
                        if (responseCode == 401) {
                            Toast.makeText(GroupWallCommentAndReplayActivity.this, "Please check your login once.", Toast.LENGTH_SHORT).show();
                        } else if (responseCode == 404) {
                            Toast.makeText(GroupWallCommentAndReplayActivity.this, "Server error! please try after some time.", Toast.LENGTH_SHORT).show();
                        } else {
                            Utils.showErrorToast(GroupWallCommentAndReplayActivity.this, responseCode);
                        }
                    }
                });
            } else {
                Toast.makeText(GroupWallCommentAndReplayActivity.this, "Could not fetch posts now, Please try after some time.", Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}