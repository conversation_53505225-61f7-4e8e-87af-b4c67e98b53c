package com.wonderslate.prepjoy.ui.groupwall;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.wonderslate.prepjoy.R;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class GroupWallPostPaginationAdapter extends RecyclerView.Adapter<GroupWallPostPaginationAdapter.ViewHolder> {

    private List<Integer> mData;
    private final LayoutInflater mInflater;
    private final GroupWallFragment context;
    private int selectPosition = 0;
    private Activity mContext;

    public GroupWallPostPaginationAdapter(GroupWallFragment contexts, Activity activity) {
        this.context = contexts;
        this.mContext = activity;
        this.mInflater = LayoutInflater.from(mContext);
    }

    public void setEntries(List<Integer> list, int currentPosition) {
        this.mData = list;
        this.selectPosition = currentPosition;
        notifyDataSetChanged();
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = mInflater.inflate(R.layout.item_post_pagination, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        try {
            Integer data = mData.get(position) + 1;
            holder.textviewPageNumber.setText("" + data);

            if (selectPosition == position) {
                holder.relativeLayoutContainer.setBackgroundResource(R.drawable.button_background_black_rounded);
                holder.textviewPageNumber.setTextColor(ContextCompat.getColor(mContext, R.color.white));
            } else {
                holder.relativeLayoutContainer.setBackgroundResource(R.drawable.button_background_light_rounded);
                holder.textviewPageNumber.setTextColor(ContextCompat.getColor(mContext, R.color.black));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public int getItemCount() {
        return mData.size();
    }

    @Override
    public int getItemViewType(int position) {
        return position;
    }


    public class ViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {
        @BindView(R.id.layout_container)
        RelativeLayout relativeLayoutContainer;
        @BindView(R.id.textView_page_number)
        TextView textviewPageNumber;

        ViewHolder(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
            relativeLayoutContainer.setOnClickListener(this);
            itemView.setOnClickListener(this);
        }

        @Override
        public void onClick(View view) {
            try {
                switch (view.getId()) {
                    case R.id.layout_container:
                        int page = mData.get(getAdapterPosition());
                        context.paginationClicked(mData.get(page));
                        break;
                    default:
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public interface ItemClickListener {
        void onItemClick(View view, int position);
    }
}
