package com.wonderslate.prepjoy.news;

import android.content.Intent;
import android.os.Bundle;

import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import com.google.android.material.snackbar.Snackbar;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.wang.avi.AVLoadingIndicatorView;
import com.wonderslate.data.interfaces.WSCallback;
import com.wonderslate.data.preferences.WonderPubSharedPrefs;
import com.wonderslate.prepjoy.R;
import com.wonderslate.prepjoy.Utils.Utils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

import butterknife.OnClick;

/**
 * A simple {@link Fragment} subclass.
 * Use the {@link NewsPreferenceFragment#newInstance} factory method to
 * create an instance of this fragment.
 */
public class NewsPreferenceFragment extends Fragment implements NewsSourcePrefAdapter.ItemClickListener, LanguagePrefAdapter.LangItemClickListener, View.OnClickListener {
    private static final String ARG_PARAM1 = "param1";
    private static final String ARG_PARAM2 = "param2";

    // TODO: Rename and change types of parameters
    private String mParam1;
    private String mParam2;
    private View view;
    private List<NewsLanguage> langPrefsList;
    private List<NewsLanguage> templangPrefsList;
    private List<NewsSource> newsSourceList;
    List<NewsSource> refinedSourceList;
    UserPrefModel userPrefModel;
    private static RecyclerView langPrefsRecyclerView;
    private static RecyclerView newsSourceRecyclerView;
    private TextView headerTitle, headerSubTitle;
    //private FloatingActionButton userPrefsFab;
    private boolean isNewsSourcePage;
    private int newsSourceSelectedPosition;
    Button userPrefsFab,buttonBack,buttonClear;
    LanguagePrefAdapter languagePrefAdapter;
    NewsSourcePrefAdapter newsSourcePrefAdapter;
    boolean isLangChecked, isNewsSourceChecked;
    AVLoadingIndicatorView loader;
    String navMode =  "";

    public NewsPreferenceFragment() {
        // Required empty public constructor
    }

    /**
     * Use this factory method to create a new instance of
     * this fragment using the provided parameters.
     *
     * @param param1 Parameter 1.
     * @param param2 Parameter 2.
     * @return A new instance of fragment NewsPreferenceFragment.
     */
    // TODO: Rename and change types and number of parameters
    public static NewsPreferenceFragment newInstance(String param1, String param2) {
        NewsPreferenceFragment fragment = new NewsPreferenceFragment();
        Bundle args = new Bundle();
        args.putString(ARG_PARAM1, param1);
        args.putString(ARG_PARAM2, param2);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            mParam1 = getArguments().getString(ARG_PARAM1);
            mParam2 = getArguments().getString(ARG_PARAM2);
        }
        userPrefModel = new UserPrefModel();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState)
    {
        try {
            view = inflater.inflate(R.layout.fragment_news_preference, container, false);
            langPrefsRecyclerView = view.findViewById(R.id.langPrefRecyclerView);
            newsSourceRecyclerView = view.findViewById(R.id.newsSourcePrefRecyclerView);
            headerTitle = view.findViewById(R.id.header_title);
            headerSubTitle = view.findViewById(R.id.header_sub_title);
            userPrefsFab = view.findViewById(R.id.user_pref_next_fab);
            buttonBack = view.findViewById(R.id.button_back);
            buttonClear = view.findViewById(R.id.button_clear);
            loader = view.findViewById(R.id.loaderMain);
            userPrefsFab.setEnabled(false);
            userPrefsFab.setBackgroundResource(R.drawable.button_shape_disabled);
            userPrefsFab.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    if (langPrefsRecyclerView.getVisibility() == View.VISIBLE) {
                        Gson gson = new Gson();
                        String newsLangJSON = gson.toJson(langPrefsList);
                        WonderPubSharedPrefs.getInstance(getContext()).setNewsLang(newsLangJSON);
                        showNewsSource(langPrefsList);
                        if (navMode.equalsIgnoreCase("change")) {
                            userPrefsFab.setEnabled(true);
                        }
                        userPrefsFab.setText("Done");

                    } else if(newsSourceRecyclerView.getVisibility() == View.VISIBLE) {
                        userPrefModel.setUserNewsLanguage(langPrefsList);
                        userPrefModel.setUserNewsSource(newsSourceList);
                        TemporaryDataHolder.setUserPrefModel(userPrefModel);
                        Gson gson = new Gson();
                        String newsSourceJSON = gson.toJson(newsSourceList);
                        WonderPubSharedPrefs.getInstance(getContext()).setNewsSource(newsSourceJSON);
                        Snackbar.make(view, "Fetching your feeds. Please wait.", Snackbar.LENGTH_LONG).show();
                        updateUserPreferenceRemote(newsSourceList);
                        Intent feedDetailsIntent = new Intent(getContext(), NewsListActivity.class);
                        startActivity(feedDetailsIntent);
                        getActivity().finish();
                    }
                }
            });

            RecyclerView.LayoutManager layoutManagerLanguage = new GridLayoutManager(getContext(), 2);
            RecyclerView.LayoutManager layoutManagerSource = new GridLayoutManager(getContext(), 2);
            langPrefsRecyclerView.setLayoutManager(layoutManagerLanguage);
            newsSourceRecyclerView.setLayoutManager(layoutManagerSource);
            languagePrefAdapter = new LanguagePrefAdapter(getActivity());
            getNewsPrefs();
            buttonBack.setOnClickListener(this);
            buttonClear.setOnClickListener(this);
           if( getActivity().getIntent().hasExtra("Mode"))
                navMode = getActivity().getIntent().getStringExtra("Mode");
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return view;
    }

    public void updateUserPreferenceRemote(List<NewsSource> selectedSources) {
        String selectedLanguage = "";
        String selectedSource = "";
        for (NewsSource newsSource : selectedSources) {
            if (newsSource.isChecked()) {
                if (selectedLanguage.isEmpty()) {
                    selectedLanguage = selectedLanguage + newsSource.getLanguage();
                }
                else {
                    selectedLanguage = selectedLanguage + "," + newsSource.getLanguage();
                }
                if (selectedSource.isEmpty()) {
                    selectedSource = selectedSource + newsSource.getId();
                }
                else {
                    selectedSource = selectedSource + "," + newsSource.getId();
                }
            }
        }

        new NewsService().updateUserNewsPrefs(selectedLanguage, selectedSource, new WSCallback() {
            @Override
            public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                Log.d("Preference", "Success: " + jsonObject.toString());
            }

            @Override
            public void onWSResultFailed(String resString, int responseCode) {

            }
        });
    }

    public void setData(List<NewsLanguage> newsLanguages, List<NewsSource> newsSources)
    {
        try {
            //Show UI according to the data received
            langPrefsList = new ArrayList<>();
            newsSourceList = new ArrayList<>();
            langPrefsList = newsLanguages;
            newsSourceList = newsSources;
            if (langPrefsList != null && !langPrefsList.isEmpty()) {
                headerTitle.setText("Choose your language");
                headerSubTitle.setText("You can change it anytime");
                langPrefsRecyclerView.setVisibility(View.VISIBLE);
                newsSourceRecyclerView.setVisibility(View.GONE);
                languagePrefAdapter.updateData(langPrefsList);
                languagePrefAdapter.setClickListener(this);
                langPrefsRecyclerView.setAdapter(languagePrefAdapter);

                if (loader != null) {
                    loader.smoothToHide();
                }

                if (isLangChecked) {
                    userPrefsFab.setBackgroundResource(R.drawable.button_shape_default);
                    userPrefsFab.setVisibility(View.VISIBLE);
                    userPrefsFab.setEnabled(true);
                }
            } else {
                Toast.makeText(getActivity(), "Problem while getting news languages", Toast.LENGTH_SHORT).show();
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    public void showNewsSource(List<NewsLanguage> languages)
    {
        try {
            userPrefsFab.setVisibility(View.VISIBLE);
            userPrefsFab.setBackgroundResource(R.drawable.button_shape_disabled);
            userPrefsFab.setEnabled(false);
            headerTitle.setText("Choose your news source");
            headerSubTitle.setText("You can change it anytime");
            langPrefsRecyclerView.setVisibility(View.GONE);

            if (newsSourceList != null && !newsSourceList.isEmpty() && languages != null && !languages.isEmpty()) {
                refinedSourceList = new ArrayList<>();
                for (int i = 0; i < newsSourceList.size(); i++) {
                    for (int j = 0; j < languages.size(); j++) {
                        if (newsSourceList.get(i).getLanguage().equalsIgnoreCase(languages.get(j).getLanguage()) && languages.get(j).isChecked()) {
                            if (!refinedSourceList.contains(newsSourceList.get(i))) {
                                refinedSourceList.add(newsSourceList.get(i));
                            }
                        }
                    }
                }
                newsSourcePrefAdapter = new NewsSourcePrefAdapter(getActivity(), refinedSourceList);
                newsSourcePrefAdapter.setClickListener(this);
                newsSourceRecyclerView.setAdapter(newsSourcePrefAdapter);
                newsSourceRecyclerView.setVisibility(View.VISIBLE);

                if (isNewsSourceChecked) {
                    userPrefsFab.setBackgroundResource(R.drawable.button_shape_default);
                    userPrefsFab.setVisibility(View.VISIBLE);
                    userPrefsFab.setEnabled(true);
                }
                isNewsSourcePage = true;
            } else {
                Toast.makeText(getActivity(), "Problem while getting news sources", Toast.LENGTH_SHORT).show();
                isNewsSourcePage = true;
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    private void getNewsPrefs()
    {
        loader.smoothToShow();
        List<NewsLanguage> newsLanguages = new ArrayList<>();
        List<NewsSource> newsSources = new ArrayList<>();
        NewsService newsService = new NewsService();
        newsService.getNewsPreferenceList(new WSCallback(){
            @Override
            public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                try {
                    String newsLangPref = WonderPubSharedPrefs.getInstance(getActivity()).getNewsLangPref();
                    Gson gsonLangPref = new Gson();
                    Type typeLangPref = new TypeToken<List<NewsLanguage>>(){}.getType();
                    List<NewsLanguage> chosenNewsLangList;
                    chosenNewsLangList = gsonLangPref.fromJson(newsLangPref, typeLangPref);

                    String newsSourcePref = WonderPubSharedPrefs.getInstance(getActivity()).getNewsSource();
                    Gson gsonSourcePref = new Gson();
                    Type typeSourcePref = new TypeToken<List<NewsSource>>(){}.getType();
                    List<NewsSource> chosenNewsSourceList;
                    chosenNewsSourceList = gsonSourcePref.fromJson(newsSourcePref, typeSourcePref);

                    JSONArray languageArray = new JSONArray(jsonObject.optString("newsLanguages"));
                    JSONArray newsSource = new JSONArray(jsonObject.optString("newsSource"));

                    for (int i = 0;i<languageArray.length();i++) {
                        JSONObject langObject = languageArray.optJSONObject(i);

                        NewsLanguage newsLanguage = new NewsLanguage();
                        newsLanguage.setLanguage(langObject.optString("language"));
                        newsLanguage.setDisplayLanguage(langObject.optString("language_display"));
                        newsLanguages.add(newsLanguage);
                    }

                    if (chosenNewsLangList != null && !chosenNewsLangList.isEmpty()) {
                        for (int i=0;i<chosenNewsLangList.size();i++) {
                            for (int j=0;j<newsLanguages.size();j++) {
                                if (chosenNewsLangList.get(i).getLanguage().equalsIgnoreCase(newsLanguages.get(j).getLanguage())
                                        && chosenNewsLangList.get(i).isChecked()) {
                                    newsLanguages.get(j).setChecked(true);
                                    isLangChecked = true;
                                }
                            }
                        }
                    }

                    for (int m=0;m<newsSource.length();m++) {
                        JSONObject sourceObject = newsSource.optJSONObject(m);
                        NewsSource newsSourceObject = new NewsSource();
                        newsSourceObject.setId(sourceObject.optString("id"));
                        newsSourceObject.setName(sourceObject.optString("name"));
                        newsSourceObject.setRssFeed(sourceObject.optString("rss_feed"));
                        newsSourceObject.setLanguage(sourceObject.optString("language"));
                        newsSources.add(newsSourceObject);
                    }

                    if (chosenNewsSourceList != null && !chosenNewsSourceList.isEmpty()) {
                        for (int j=0;j<chosenNewsSourceList.size();j++) {
                            for (int k=0;k<newsSources.size();k++) {
                                if (chosenNewsSourceList.get(j).getRssFeed().equalsIgnoreCase(newsSources.get(k).getRssFeed())
                                        && chosenNewsSourceList.get(j).isChecked()) {
                                    newsSources.get(k).setChecked(true);
                                    isNewsSourceChecked = true;
                                }
                            }
                        }
                    }

                    setData(newsLanguages, newsSources);

                } catch (JSONException e) {
                    loader.smoothToHide();
                    Toast.makeText(requireActivity(), "Problem while getting your news preference. " +
                            "Please try again later.", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onWSResultFailed(String resString, int responseCode) {
                loader.smoothToHide();
                if (responseCode == 401) {
                    Toast.makeText(requireActivity(), "Problem while getting your news preference. " +
                            "Please try again later.", Toast.LENGTH_SHORT).show();
                } else if (responseCode == 404) {
                    Toast.makeText(requireActivity(), "Server error! please try after some time.", Toast.LENGTH_SHORT).show();
                } else {
                    Utils.showErrorToast(requireActivity(), responseCode);
                }
            }
        });

    }

    @Override
    public void onItemClick(View view, int position)
    {
        try {
            boolean isItemChecked = false;
            refinedSourceList.get(position).setChecked(!refinedSourceList.get(position).isChecked());

            for (int i = 0; i < refinedSourceList.size(); i++) {
                if (refinedSourceList.get(i).isChecked()) {
                    isItemChecked = true;
                    break;
                } else {
                    isItemChecked = false;
                }
            }

            if (isItemChecked) {
                userPrefsFab.setBackgroundResource(R.drawable.button_shape_default);
                userPrefsFab.setEnabled(true);
            } else {
                userPrefsFab.setBackgroundResource(R.drawable.button_shape_disabled);
                userPrefsFab.setEnabled(false);
            }
            userPrefsFab.setVisibility(View.VISIBLE);
            isNewsSourcePage = true;
            newsSourceSelectedPosition = position;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    @Override
    public void onLangItemClick(View view, int position)
    {
        try {
            boolean isItemChecked = false;
            String selectedLang = langPrefsList.get(position).getLanguage();
            langPrefsList.get(position).setChecked(!langPrefsList.get(position).isChecked());
            if (newsSourceList != null && !newsSourceList.isEmpty()) {
                for (NewsSource newsSource : newsSourceList) {
                    if (selectedLang.equalsIgnoreCase(newsSource.getLanguage()) && newsSource.isChecked()) {
                        newsSource.setChecked(false);
                    }
                }
            }

            for (int i = 0; i < langPrefsList.size(); i++) {
                if (langPrefsList.get(i).isChecked) {
                    isItemChecked = true;
                    break;
                } else {
                    isItemChecked = false;
                }
            }

            if (isItemChecked) {
                userPrefsFab.setBackgroundResource(R.drawable.button_shape_default);
                userPrefsFab.setEnabled(true);
            } else {
                userPrefsFab.setBackgroundResource(R.drawable.button_shape_disabled);
                userPrefsFab.setEnabled(false);
            }
            userPrefsFab.setVisibility(View.VISIBLE);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    // @OnClick({R.id.button_back, R.id.button_clear})
    public void onClick(View view)
    {
        try {
            switch (view.getId()) {
                case R.id.button_back:
                    if (langPrefsRecyclerView.getVisibility() == View.VISIBLE) {
                        if (navMode.equalsIgnoreCase("change")) {
                            Intent feedDetailsIntent = new Intent(getContext(), NewsListActivity.class);
                            startActivity(feedDetailsIntent);
                        }
                        getActivity().finish();
                    } else if (newsSourceRecyclerView.getVisibility() == View.VISIBLE) {
                        newsSourceRecyclerView.setVisibility(View.GONE);
                        getNewsPrefs();
                        userPrefsFab.setText("Next");
                    }
                    break;
                case R.id.button_clear:
                    try {
                        if (langPrefsRecyclerView.getVisibility() == View.VISIBLE && languagePrefAdapter != null) {
                            langPrefsList.clear();
                            newsSourceList.clear();
                            WonderPubSharedPrefs.getInstance(getActivity()).setNewsLang("");
                            WonderPubSharedPrefs.getInstance(getActivity()).setNewsSource("");
                            TemporaryDataHolder.setRssItemList(new ArrayList<>());
                            TemporaryDataHolder.setMergedItemList(new ArrayList<>());
                            TemporaryDataHolder.setUserPrefModel(null);
                            userPrefsFab.setBackgroundResource(R.drawable.button_shape_disabled);
                            userPrefsFab.setVisibility(View.VISIBLE);
                            userPrefsFab.setEnabled(false);
                            languagePrefAdapter.notifyDataSetChanged();
                            isLangChecked = false;
                            isNewsSourceChecked = false;
                            getNewsPrefs();
                        } else if (newsSourceRecyclerView.getVisibility() == View.VISIBLE && newsSourcePrefAdapter != null) {
                            List<NewsSource> tempNewsSourceList = new ArrayList<>();
                            for (NewsSource newsSource : refinedSourceList) {
                                if (newsSource.isChecked()) {
                                    newsSource.setChecked(false);
                                }
                                tempNewsSourceList.add(newsSource);
                            }
                            userPrefsFab.setBackgroundResource(R.drawable.button_shape_disabled);
                            userPrefsFab.setVisibility(View.VISIBLE);
                            userPrefsFab.setEnabled(false);
                            refinedSourceList.clear();
                            WonderPubSharedPrefs.getInstance(getActivity()).setNewsSource("");
                            TemporaryDataHolder.setMergedItemList(new ArrayList<>());
                            TemporaryDataHolder.setRssItemList(new ArrayList<>());
                            userPrefModel.setUserNewsSource(new ArrayList<>());
                            TemporaryDataHolder.setUserPrefModel(userPrefModel);
                            newsSourcePrefAdapter.notifyDataSetChanged();
                            refinedSourceList.addAll(tempNewsSourceList);
                            newsSourcePrefAdapter.notifyDataSetChanged();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    break;
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }
}