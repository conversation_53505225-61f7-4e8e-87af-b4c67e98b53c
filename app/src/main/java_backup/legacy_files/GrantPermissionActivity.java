package com.wonderslate.prepjoy.ui.dashboard;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.RequiresApi;
import androidx.core.content.ContextCompat;

import com.wonderslate.data.preferences.WonderPubSharedPrefs;
import com.wonderslate.prepjoy.R;
import com.wonderslate.prepjoy.ui.BaseActivity;

public class GrantPermissionActivity extends BaseActivity implements View.OnClickListener{

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        init();
    }

    private void init() {
        TextView okayBtn = findViewById(R.id.settingsbtn);
        TextView cancelBtn = findViewById(R.id.backbtn);

        okayBtn.setOnClickListener(this);
        cancelBtn.setOnClickListener(this);
    }

    @SuppressLint("NonConstantResourceId")
    @Override
    public void onClick(View view) {
        int id = view.getId();
        switch (id) {
            case R.id.settingsbtn:
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    checkForPermissions();
                }
                else {
                    advanceToMain();
                }
                break;
            case R.id.backbtn:
                advanceToMain();
                break;
            default:
                break;
        }
    }

    private void advanceToMain() {
        WonderPubSharedPrefs.getInstance(GrantPermissionActivity.this).setFirstTimeLaunch(false);
        finish();
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
    }
    @Override
    protected int getLayoutResource() {
        return R.layout.grant_permission_layout;
    }

    private final ActivityResultLauncher<String> requestPermissionLauncher =
            registerForActivityResult(new ActivityResultContracts.RequestPermission(), isGranted -> advanceToMain());


    @RequiresApi(api = Build.VERSION_CODES.TIRAMISU)
    private void checkForPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(
                    GrantPermissionActivity.this, Manifest.permission.POST_NOTIFICATIONS) !=
                    PackageManager.PERMISSION_GRANTED) {
                // You can use the API that requires the permission.
                requestPermissionLauncher.launch(
                        Manifest.permission.POST_NOTIFICATIONS);
            }
            else {
                advanceToMain();
            }
        }
    }

}
