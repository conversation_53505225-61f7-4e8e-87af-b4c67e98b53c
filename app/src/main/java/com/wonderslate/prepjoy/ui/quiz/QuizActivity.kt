package com.wonderslate.prepjoy.ui.quiz

import android.annotation.SuppressLint
import android.app.Activity
import android.app.AlertDialog
import android.content.*
import android.media.AudioManager
import android.media.MediaPlayer
import android.net.ConnectivityManager
import android.os.Build
import android.os.Bundle
import android.util.DisplayMetrics
import android.util.Log
import android.view.View
import android.webkit.*
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import android.widget.Toast
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.widget.NestedScrollView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.asLiveData
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import android.widget.ProgressBar
import com.wonderslate.commons.Data
import com.wonderslate.commons.Status
import com.wonderslate.data.network.WSAPIManager
import com.wonderslate.data.network.Wonderslate
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.domain.entities.ResourceLogData
import com.wonderslate.domain.entities.multiquiz.CFMultiQuizInput
import com.wonderslate.domain.entities.multiquiz.QuizResponse
import com.wonderslate.domain.entities.tests.DTQuizInput
import com.wonderslate.prepjoy.BuildConfig
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.Utils.*
import com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
import com.wonderslate.prepjoy.Utils.Utils.disableScreenShot
import com.wonderslate.prepjoy.ui.BaseActivity
import com.wonderslate.prepjoy.ui.dashboard.DashBoardActivity
import com.wonderslate.prepjoy.ui.home.HomeViewModel
import com.wonderslate.prepjoy.ui.login.LoginActivity
import com.wonderslate.prepjoy.ui.new_book_details.BookDetailsAct
import com.wonderslate.prepjoy.ui.quiz_instruction.QuizInstructions
import com.wonderslate.prepjoy.ui.tests.DailyTestViewModel
import com.ws.book_details.data.models.BookDetails
import com.ws.book_details.data.models.BookDetailsRequest
import com.ws.book_details.data.models.BookDetailsResponse
import com.ws.book_details.ui.BookDetailsFragViewModel
import com.ws.commons.enums.BotPlay
import com.ws.commons.enums.EventType
import com.ws.commons.enums.JSAudio
import com.ws.commons.extensions.isFreeBook
import com.ws.commons.interfaces.ImageUrlProvider
import com.ws.commons.models.BookCoverUrlRequest
import com.ws.commons.models.Event
import com.ws.commons.models.ShopBookData
import com.ws.core_ui.extensions.*
import com.ws.shop.data.models.ShopBooksRequest
import com.ws.shop.ui.LatestBooksAdapter
// import kotlinx.android.synthetic.main.activity_dash_board.*
// import kotlinx.android.synthetic.main.activity_quiz.*
// import kotlinx.android.synthetic.main.fragment_home.*
import kotlinx.coroutines.InternalCoroutinesApi
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

@OptIn(InternalCoroutinesApi::class)
class QuizActivity : BaseActivity(), QuizInterface, BaseActivity.weeklyContestListener, ForegroundBackgroundListener {
    val viewModel by viewModel<HomeViewModel>()
    private val dailyViewModel by viewModel<DailyTestViewModel>()
    private val bookDetailsViewModel by viewModel<BookDetailsFragViewModel>()
    var mContext: Context? = null
    private var webview: NestedWebView? = null
    private var webViewParent: LinearLayout? = null
    private var quizScrollParent: NestedScrollView? = null
    var moreTestsRecyclerView1: RecyclerView? = null
    var moreTestsRecyclerView2: RecyclerView? = null
    var moreTestsParent: LinearLayout? = null
    var buyBookParent: LinearLayout? = null
    var moreTests1Parent: LinearLayout? = null
    var moreTests2Parent: LinearLayout? = null
    var parentView: CoordinatorLayout? = null
    var progressLoader: ProgressBar? = null
    var buyBookProgressLoader: ProgressBar? = null
    var ivBookCover: ImageView? = null
    var soundenabled: Boolean = false
    var JsonquizData = JSONObject()
    var quizid: String? = null
    var language1: String? = null
    var language2: String? = null
    var mediaplayer: MediaPlayer = MediaPlayer()
    private var mediaList = ArrayList<MediaPlayer>()
    private var selectedDate: String? = null
    var previosVal: String? = "yes"
    var nextVal: String? = "yes"
    var quizType: String? = null
    var challengerName: String? = null
    private var realDailyTestDtlId : String? = null
    var challengerPlace: String? = null
    var noOfDays: String? = null
    var noOfQuestions: String? = null
    var selectedWeek: String? = null
    var selectedMonth: String? = null
    var selectedTestDate: String? = null
    var selectedQuizMode: String? = "play"
    var summaryAnswers: String? = ""
    var summaryQuestion: String? = ""
    private var isQuizSubmitted = false
    private lateinit var shareFab: FloatingActionButton
    private var isFromShop: Boolean? = false
    private var isFromDeepLink: Boolean? = false
    private var bookId: String = ""
    private var bookTitle: String = ""
    private var bookPublisher: String = ""
    private var bookPublisherId: String = ""
    private  var pTestMode = ""
    private var isStatsShown = false
    private var isChallengeMode = false
    private var isChallengePrevious = false
    private var isChallengeNext = false
    private var isJoinMode = false
    private var connectionStatus = true
    private var isOnlineRematch = false
    private var isBattleAudioStarted = false
    private var isQuestionAudioStarted = false
    private val magazineAdapter by inject<LatestBooksAdapter>()
    private val compExamBooksAdapter by inject<LatestBooksAdapter>()
    private var isSummaryClicked: Boolean? = false
    private var isCurrentAffairs: Boolean? = false
    private var selectedQuizName: String = ""
    private var bookDetails: BookDetails? = null
    private val imageUrlProvider by inject<ImageUrlProvider>()
    private var fullCoverUrl = ""
    private var buyBookBtn: WSTextView? = null
    private var bookTitleTxt: TextView? = null
    private var bookPublisherTxt: TextView? = null
    private var bookDetailsParent: RelativeLayout? = null
    private val foregroundBackgroundObserver = ForegroundBackgroundObserver(this)
    private var isGPTQuiz: Boolean = false
    private var lvCenter: ProgressBar? = null
    private var noDatalayout: View? = null
    private var linearNoData: View? = null

    private val declineReceiver = object : BroadcastReceiver() {
        override fun onReceive(p0: Context?, p1: Intent?) {
            if (null != p1) {
                if (p1.hasExtra("userName")) {
                    val user = p1.getStringExtra("userName")
                    val dialogView: View =
                        layoutInflater.inflate(R.layout.dialog_language_selection, null)
                    val alertDialog = AlertDialog.Builder(mContext)
                    alertDialog.setView(dialogView)
                    val title = dialogView.findViewById<TextView>(R.id.title)
                    title.text = "$user has declined the challenge"
                    dialogView.findViewById<LinearLayout>(R.id.actionContainer).visibility = View.GONE
                    val btnAccept: Button = dialogView.findViewById<View>(R.id.btnOk) as Button
                    val btnCancel: Button = dialogView.findViewById<View>(R.id.btnCacle) as Button
                    btnCancel.visibility = View.INVISIBLE
                    btnAccept.text = "OK"
                    btnAccept.visibility = View.VISIBLE
                    btnAccept.setOnClickListener { exitScreen() }
                    alertDialog.setCancelable(false)
                    runOnUiThread {
                        alertDialog.create().show()
                    }
                }
            }
        }
    }


    override fun onStart() {
        super.onStart()
        Wonderslate.APP_STATE = Lifecycle.Event.ON_START
        LocalBroadcastManager.getInstance(this).registerReceiver(declineReceiver,
            IntentFilter("decline_game"))
    }

    override fun onResume() {
        super.onResume()
        Wonderslate.APP_STATE = Lifecycle.Event.ON_RESUME
        isSummaryClicked = false
    }

    override fun onStop() {
        super.onStop()
        Wonderslate.APP_STATE = Lifecycle.Event.ON_STOP
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        lifecycle.addObserver(foregroundBackgroundObserver)
        disableTicker()
        Wonderslate.aClass = javaClass
        if (supportActionBar != null) {
            supportActionBar!!.hide()
        }
        mContext = this
        quizid = intent.getStringExtra("quizId")
        takeIf { intent.hasExtra("quizId") }
        selectedQuizName = intent.getStringExtra("quizName").toString()
        takeIf { intent.hasExtra("quizName") }
        initObserver()
        quizScrollParent = findViewById(R.id.quiz_scroll_parent)
        webViewParent = findViewById(R.id.webViewParent)
        moreTestsParent = findViewById(R.id.more_test_parent)
        buyBookParent = findViewById(R.id.buy_book_parent)
        ivBookCover = findViewById(R.id.ivBookCover)
        bookDetailsParent = findViewById(R.id.book_details_parent)
        buyBookBtn = findViewById(R.id.buyBookBtn)
        bookTitleTxt = findViewById(R.id.bookTitleTxt)
        bookPublisherTxt = findViewById(R.id.bookPublisherTxt)
        buyBookBtn?.visibility = View.GONE
        moreTests1Parent = findViewById(R.id.more_test1_parent)
        moreTests2Parent = findViewById(R.id.more_test2_parent)
        moreTestsRecyclerView1 = findViewById(R.id.more_test_recyclerview1)
        moreTestsRecyclerView2 = findViewById(R.id.more_test_recyclerview2)
        moreTestsParent?.visibility = View.GONE
        buyBookParent?.visibility = View.GONE
        moreTests1Parent?.visibility = View.GONE
        moreTests2Parent?.visibility = View.GONE
        webview = findViewById(R.id.webView)
        webview?.visibility = View.GONE
        progressLoader = findViewById(R.id.progressLoader)
        buyBookProgressLoader = findViewById(R.id.buyBookProgressLoader)
        shareFab = findViewById(R.id.fab_share)
        parentView = findViewById(R.id.parent_view)
        lvCenter = findViewById(R.id.lvCenter)
        noDatalayout = findViewById(R.id.noDatalayout)
        linearNoData = findViewById(R.id.linearNoData)
        progressLoader?.visibility = View.VISIBLE
        isGPTQuiz = intent.getBooleanExtra("isGPTQuiz", false);
        if (!isGPTQuiz) {
            quizid?.let { viewModel.getQuizQuestionsAnswers(it) }
        }
        soundenabled = WonderPubSharedPrefs.getInstance(applicationContext).sharedPrefsGameSound
        quizid = intent.getStringExtra("quizId")
        isChallengeMode = intent.getBooleanExtra("challenge", false)
        isJoinMode = intent.getBooleanExtra("joinGame", false)
        language1 = intent.getStringExtra("language1")
        takeIf { intent.hasExtra("language1") }
        language2 = intent.getStringExtra("language2")
        takeIf { intent.hasExtra("language2") }
        isFromShop = intent.getBooleanExtra("isFromShop", false).takeIf {
            intent.hasExtra("isFromShop")
        }
        isFromDeepLink = intent.getBooleanExtra("isFromDeepLink", false).takeIf {
            intent.hasExtra("isFromDeepLink")
        }
        bookId = intent.getStringExtra("bookId").takeIf {
            intent.hasExtra("bookId")
        }.toString()
        bookPublisher = intent.getStringExtra("publisher").takeIf {
            intent.hasExtra("publisher")
        }.toString()
        bookPublisherId = intent.getStringExtra("publisherId").takeIf {
            intent.hasExtra("publisherId")
        }.toString()
        bookTitle = intent.getStringExtra("bookTitle").takeIf {
            intent.hasExtra("bookTitle")
        }.toString()
        selectedDate = intent.getStringExtra("selectedDate")
        takeIf { intent.hasExtra("selectedDate") }
        selectedQuizMode = intent.getStringExtra("selectedFormat")
        takeIf { intent.hasExtra("selectedFormat") }
        quizType = intent.getStringExtra("quizType")?.takeIf { intent.hasExtra("quizType") }
        challengerName = intent.getStringExtra("challengerName").takeIf {
            intent.hasExtra("challengerName")
        }

        realDailyTestDtlId = intent.getStringExtra("realDailyTestDtlId").takeIf {
            intent.hasExtra("realDailyTestDtlId")
        }
        challengerPlace = intent.getStringExtra("challengerPlace").takeIf {
            intent.hasExtra("challengerPlace")
        }
        noOfDays = intent.getStringExtra("noOfDays").takeIf { intent.hasExtra("noOfDays") }
        noOfQuestions = intent.getStringExtra("noOfQuestions").takeIf { intent.hasExtra("noOfQuestions") }
        selectedWeek = intent.getStringExtra("selectedWeek").takeIf {
            intent.hasExtra("selectedWeek")
        }
        selectedMonth = intent.getStringExtra("selectedMonth").takeIf {
            intent.hasExtra("selectedMonth")
        }
        selectedTestDate = intent.getStringExtra("testDate").takeIf {
            intent.hasExtra("testDate")
        }
        previosVal = intent.getStringExtra("previous")
        nextVal = intent.getStringExtra("next")
        shareFab.setOnClickListener { shareClickOperation() }
        shareFab.hide()
        if (intent.hasExtra("testId") && intent.hasExtra("quizData")) {
            val jsonObject = JSONObject()
            jsonObject.put("results", JSONArray(intent.getStringExtra("quizData")))
            var testId = ""
            var testMode= ""
            intent.getStringExtra("testId")?.let { testId = it }
            intent.getStringExtra("testMode")?.let { testMode = it }
            pTestMode = testMode
            prepareDailyTestJson(jsonObject, testId, testMode)
            if (isChallengeMode) {
                initHostGame()
            } else {
                loadWebView()
            }
        } else if (intent.hasExtra("quizData") && !intent.hasExtra("testId")) {
            val jsonObject = JSONObject()
            jsonObject.put("results", JSONArray(intent.getStringExtra("quizData")))
            prepareMultiQuizJson(jsonObject)
            if (isChallengeMode) {
                initHostGame()
            } else {
                loadWebView()
            }
        }
        if (intent.getBooleanExtra("stats", false)) {
            isStatsShown = true
            val wonderPubSharedPrefs = WonderPubSharedPrefs.getInstance(mContext)
           // val quizData = wonderPubSharedPrefs.userLastQuizData
            val quizResults = wonderPubSharedPrefs.userLastQuizResults
            JsonquizData = JSONObject(quizResults)
           /* val resultsObject = JSONObject(quizResults)
            resultsObject.keys().forEach { key ->
                val value = resultsObject.get(key)
                JsonquizData.put(key, value)
            }*/
            loadWebView(true)
        }
        if (isJoinMode) {
            val gameCode = intent.getStringExtra("data")
            if (null != gameCode) {
                initJoinGame(gameCode)
            }
        }
        disableScreenShot(this)

        viewModel.logResourceOpen(
            ResourceLogData(
                "",
                BuildConfig.SITE_ID,
                "android",
                "",
                ""
            )
        )
    }

    private fun initObserver() {
        viewModel.quizQuestionAnswers.observe(this, ::quizQuestionAnswersResponse)
        viewModel.quizResponse.observe(this, ::quizResponse)
        viewModel.multiQuizResponse.observe(this, ::multiQuizResponse)
        dailyViewModel.testQuizResponse.observe(this, ::dailyTestResponse)
        dailyViewModel.reportIssue.observe(this, ::observeIssue)
        viewModel.accessCode.observe(this, ::observeAccessCode)
        viewModel.resLogResponse.observe(this, ::logResourceResponse)
        bookDetailsViewModel.bookDetails.asLiveData().observe(this) { data ->
            when (data.responseType) {
                com.ws.commons.Status.LOADING -> {
                }

                com.ws.commons.Status.SUCCESSFUL -> {
                    data.data?.let { response ->
                        handleBookDetailsResponse(response)
                    }
                }

                com.ws.commons.Status.ERROR, com.ws.commons.Status.HTTP_UNAVAILABLE -> {
                }

                else -> {}
            }
        }
    }

    private fun requestBookDetails() {
        bookDetailsViewModel.getBooksDetails(BookDetailsRequest(bookId))
    }

    private fun handleBookDetailsResponse(response: BookDetailsResponse) {

        bookDetails = BookDetails(
            response.bookId,
            response.title,
            response.coverImage,
            false,
            -1,
            response.publisherName,
            response.price.toString(),
            response.bookDesc ?: "",
            false,
            response.listPrice?.toString() ?: "",
            response.testsListprice?.toString() ?: "",
            "",
            "",
            response.isbn ?: "",
            language = response.bookLangauge ?: ""
        )

        fullCoverUrl = imageUrlProvider.getBookCoverUrl(
            BookCoverUrlRequest(
                fileName = response.coverImage ?: "",
                bookId = response.bookId.toString()
            ))
        ivBookCover?.loadImage(fullCoverUrl) {
            transition(DrawableTransitionOptions.withCrossFade())
        }

        if (response.price.toString().isNotEmpty()) {
            if (response.price.isFreeBook()) {
                buyBookBtn?.text = "Open Book"
            }
            else {
                buyBookBtn?.text = "Buy Book"
            }
        }
        else {
            buyBookBtn?.text = "Open Book"
        }

        buyBookProgressLoader?.visibility = View.GONE
        buyBookProgressLoader?.visibility = View.GONE

        bookDetailsParent?.visibility = View.VISIBLE

        buyBookBtn?.visibility = View.VISIBLE
    }

    private fun logResourceResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
                // do nothing
            }
            Status.SUCCESSFUL -> {
                // do nothing
            }

            Status.HTTP_UNAVAILABLE -> {
                //do nothing
            }

            Status.ERROR -> {
                //do nothing
            }
        }
    }

    private fun initMagazineBooks() {
        viewModel.getMagazines(buildMagazineRequest())
    }

    private fun initCompExamBooks() {
        viewModel.getCompExamBooks(buildCompExamRequest())
    }

    private fun buildMagazineRequest(): ShopBooksRequest {
        val level = getString(R.string.level_more_test_magazine)
        val syllabus = ""
        val grade = ""
        return ShopBooksRequest(level, syllabus, grade, mcqBook = true)
    }

    private fun buildCompExamRequest(): ShopBooksRequest {
        val level = getString(R.string.level_more_test_competitive)
        val syllabus = ""
        val grade = ""
        return ShopBooksRequest(level, syllabus, grade, mcqBook = true)
    }

    private fun getMagazines()
    {
        viewModel.magazines.collectLatestWithLifecycle(this) { data ->
            when(data.responseType) {
                com.ws.commons.Status.LOADING -> {
                    lvCenter?.visibility = View.VISIBLE
                    noDatalayout?.hideView()
                    linearNoData?.hideView()
                }

                com.ws.commons.Status.SUCCESSFUL -> {
                    try {
                        lvCenter?.visibility = View.GONE

                        val books = data.data ?: arrayListOf()
                        if (books.size > 0) {
                            val magazines = java.util.ArrayList<ShopBookData>()
                            for (i in 0..5) {
                                magazines.add(books[i])
                            }
                            magazineAdapter.update(magazines,true)
                            magazineAdapter.toggleSeeMoreOverlay(true)
                        }
                        linearNoData?.visibility(books.isEmpty())
                    }
                    catch (e:Exception)
                    {
                        e.printStackTrace()
                    }
                }

                com.ws.commons.Status.ERROR, com.ws.commons.Status.HTTP_UNAVAILABLE -> {
                    lvCenter?.visibility = View.GONE

                    // Show error message if adapter is empty
                    if(magazineAdapter.itemCount == 0) {
                        if(data.responseType == com.ws.commons.Status.HTTP_UNAVAILABLE) {
                            noDatalayout?.showView()
                        } else {
                            linearNoData?.showView()
                        }
                    } else
                        showToast("Problem while getting books. Please try again.")
                }
                else -> {}
            }
        }
    }

    private fun getCompExamBooks()
    {
        viewModel.compExamBooks.collectLatestWithLifecycle(this) { data ->
            when(data.responseType) {
                com.ws.commons.Status.LOADING -> {
                    lvCenter?.visibility = View.VISIBLE
                    noDatalayout?.hideView()
                    linearNoData?.hideView()
                }

                com.ws.commons.Status.SUCCESSFUL -> {
                    try {
                        lvCenter?.visibility = View.GONE

                        val books = data.data ?: arrayListOf()
                        val compExamBooks = java.util.ArrayList<ShopBookData>()
                        for (i in 0..5) {
                            compExamBooks.add(books[i])
                        }
                        compExamBooksAdapter.update(compExamBooks,true)
                        compExamBooksAdapter.toggleSeeMoreOverlay(true)
                        linearNoData?.visibility(books.isEmpty())
                    }
                    catch (e:Exception)
                    {
                        e.printStackTrace()
                    }
                }

                com.ws.commons.Status.ERROR, com.ws.commons.Status.HTTP_UNAVAILABLE -> {
                    lvCenter?.visibility = View.GONE

                    // Show error message if adapter is empty
                    if(compExamBooksAdapter.itemCount == 0) {
                        if(data.responseType == com.ws.commons.Status.HTTP_UNAVAILABLE) {
                            noDatalayout?.showView()
                        } else {
                            linearNoData?.showView()
                        }
                    } else
                        showToast("Problem while getting books. Please try again.")
                }
                else -> {}
            }
        }
    }

    private fun initMagazineRecycler() {
        moreTestsRecyclerView1?.apply {
            //Show 3 books in row if app is running in a tablet else 2 books

            val gridLayoutManager = GridLayoutManager(this@QuizActivity, if(isTablet()) 3 else 2)
            layoutManager = gridLayoutManager
            adapter = if (isCurrentAffairs == true) {
                magazineAdapter
            } else {
                compExamBooksAdapter
            }
        }

        magazineAdapter.onBookClickListener = {
            onBookItemClicked(it, "Quiz Summary")
        }

        magazineAdapter.onSeeMoreClickListener = {
            onSeeMoreClicked(it, "Quiz Summary")
        }
    }

    private fun initCompExamBooksRecycler() {
        moreTestsRecyclerView2?.apply {
            //Show 3 books in row if app is running in a tablet else 2 books

            val gridLayoutManager = GridLayoutManager(this@QuizActivity, if(isTablet()) 3 else 2)
            layoutManager = gridLayoutManager
            adapter = if (isCurrentAffairs == true) {
                compExamBooksAdapter
            } else {
                magazineAdapter
            }
        }

        compExamBooksAdapter.onBookClickListener = {
            onBookItemClicked(it, "Quiz Summary")
        }

        compExamBooksAdapter.onSeeMoreClickListener = {
            onSeeMoreClicked(it, "Quiz Summary")
        }
    }

    private fun onBookItemClicked(bookData: ShopBookData, from: String) {
        viewModel.logEvent(
            Event(
                type = EventType.BOOK_DETAILS_PAGE_OPENED,
                currentScreen = from,
                value = Pair("BookId", bookData.id.toString())
            )
        )
        val bookDetails = BookDetails(
            bookData.id,
            bookData.title,
            bookData.coverImage,
            false,
            bookData.publisherId,
            bookData.publisher
        )
        startActivityWithAnim(BookDetailsAct.createIntent(this, bookDetails))
        finishWithAnim()
    }

    private fun onSeeMoreClicked(bookData: ShopBookData, from: String) {
        viewModel.logEvent(
            Event(
                type = EventType.SHOP_BOOKS_PAGE_OPENED,
                currentScreen = from,
                value = Pair("Level", bookData.level.toString())
            )
        )

        val navigateToStore = Intent(this@QuizActivity, DashBoardActivity::class.java)
        navigateToStore.putExtra("level", bookData.level)
        navigateToStore.putExtra("context", "navigate_to_store")
        navigateToStore.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivityWithAnim(navigateToStore)
        finishWithAnim()
    }

    override fun getLayoutResource(): Int {
        return R.layout.activity_quiz
    }

    private fun initWebView(nextChallenger: String, nextchallengerPlace: String) {
        try {
            stopMediaPlayer()
            JsonquizData.remove("nextChallenger")
            JsonquizData.remove("nextchallengerPlace")
            JsonquizData.put("nextChallenger", nextChallenger)
            val place = Utils.challengerPlace(applicationContext)
            if (place.isNotEmpty()) {
                JsonquizData.put("nextchallengerPlace", place)
            } else {
                JsonquizData.put("nextchallengerPlace", nextchallengerPlace)
            }
            if (selectedQuizMode != "") {
                JsonquizData.put("quizType", "")
            }
            runOnUiThread {
                webview?.loadUrl("about:blank")
                loadWebView()
            }
        } catch (e: Exception) {
            Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), this)
            e.printStackTrace()
        }
    }

    fun restart(mode: String) {
        stopMediaPlayer()
        JsonquizData.put("quizType", mode)
        runOnUiThread {
            webview?.loadUrl("about:blank")
            loadWebView()
        }
    }

    //region Share
    fun toggleShare(boolean: Boolean) {
        runOnUiThread {
            when (boolean) {
                true -> shareFab.show()
                else -> shareFab.hide()
            }
        }
    }

    private fun shareGameCode(gameCode: String?) {
        runOnUiThread {
            val appName = getString(R.string.app_name)
            val user = WonderPubSharedPrefs.getInstance(applicationContext).username
            val playStoreLink = getString(R.string.playstorelink)
            val subject = "$user has requested you to play an online quiz. " +
                    "Join the game by entering $gameCode in your $appName app!\nClick here $playStoreLink to download."
            val intent = Intent(Intent.ACTION_SEND)
            intent.type = "text/plain"
            intent.putExtra(Intent.EXTRA_SUBJECT, appName)
            intent.putExtra(Intent.EXTRA_TEXT, subject)
            startActivity(Intent.createChooser(intent, "Share Game Code via"))
        }
    }

    private fun shareClickOperation() {
        runOnUiThread {
            val screenShot = Utils.getScreenShot(quizScrollParent)
            val path = Utils.screenShotPath(screenShot, applicationContext)
            val intent = Utils.shareIntent(path, applicationContext,
                getString(R.string.result_share_content))
            startActivity(intent)
        }
    }
    //endregion
    //region Web View Settings
    fun setupWebView() {
        setWebViewLayoutParams(false)
        webview?.getSettings()?.setJavaScriptEnabled(true)
        webview?.getSettings()?.setSupportZoom(false)
        webview?.getSettings()?.setBuiltInZoomControls(false)
        webview?.setVerticalScrollBarEnabled(true)
        webview?.setHorizontalScrollBarEnabled(true)
        webview?.settings?.domStorageEnabled = true
        webview?.getSettings()?.setDisplayZoomControls(false)
        webview?.setOnLongClickListener(View.OnLongClickListener { view: View? -> true })
        webview?.getSettings()?.setLoadsImagesAutomatically(true)
        webview?.getSettings()?.setJavaScriptCanOpenWindowsAutomatically(true)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            webview?.getSettings()?.setAllowUniversalAccessFromFileURLs(true);
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            WebView.setWebContentsDebuggingEnabled(true)
        }
        webview?.clearCache(true)
        webview?.let { progressLoader?.let { it1 ->
            QuizJSInterface(this) }
        }?.let { webview?.addJavascriptInterface(it, "JSInterface") }
    }

    fun loadUrl(url: String = "", gameCode: String ?= null) {
        webview?.webViewClient = object : WebViewClient() {
            override fun onReceivedError(
                view: WebView?,
                request: WebResourceRequest?,
                error: WebResourceError?
            ) {
                super.onReceivedError(view, request, error)
//                webView.visibility = View.VISIBLE
//                webView.loadUrl("about:blank")
            }

            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                progressLoader?.visibility = View.GONE
                webview?.visibility = View.VISIBLE
                if (null != gameCode) {
                    val data = JSONObject(gameCode)
                    initializeJoinGame(data)
                } else {
                    setupChallenge(view as NestedWebView?)
                }
            }
        }
        webview?.loadUrl(url)
    }

    private fun toggleMoreTestVisibility(value: Boolean = false) {
        if (value) {
            if (isFromDeepLink == true) {
                requestBookDetails()
                buyBookParent?.visibility = View.VISIBLE
                buyBookProgressLoader?.visibility = View.VISIBLE
                bookTitleTxt?.text = bookTitle
                bookPublisherTxt?.text = bookPublisher

                buyBookBtn?.setOnClickListener {
                    viewModel.logEvent(
                        Event(
                            type = EventType.BOOK_DETAILS_PAGE_OPENED,
                            currentScreen = "Quiz Summary",
                            value = Pair("BookId", bookId)
                        )
                    )
                    val bookDetails = BookDetails(
                        bookId.toInt(),
                        bookTitle,
                        "",
                        false,
                        bookPublisherId.toInt(),
                        bookPublisher
                    )
                    val bookDetailsActIntent = BookDetailsAct.createIntent(this, bookDetails)
                    startActivityWithAnim(bookDetailsActIntent)
                    finish()
                }
            }
            else {
                moreTestsParent?.visibility = View.VISIBLE
                moreTests1Parent?.visibility = View.VISIBLE
                moreTests2Parent?.visibility = View.VISIBLE
            }
        }
        else {
            buyBookParent?.visibility = View.GONE
            moreTestsParent?.visibility = View.GONE
            moreTests1Parent?.visibility = View.GONE
            moreTests2Parent?.visibility = View.GONE
        }
    }

    private fun setWebViewLayoutParams(resize: Boolean) {
        runOnUiThread {
            if (resize) {
                val layoutParams = LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT)
                webview?.layoutParams = layoutParams
            }
            else {
                val pixels = getDeviceScreenHeight()
                val layoutParams = LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, pixels)
                webview?.layoutParams = layoutParams
            }

            webview?.requestLayout()
        }
    }

    private fun setupChallenge(view: NestedWebView? = null,
                               quizAvailable: Boolean = true) {
        setWebViewLayoutParams(false)
        toggleMoreTestVisibility(false)
        val wonderPubSharedPrefs = WonderPubSharedPrefs.getInstance(applicationContext)
        val userId = wonderPubSharedPrefs.usermobile
        val userName = wonderPubSharedPrefs.username
        val siteId = BuildConfig.SITE_ID
        val json = JSONObject()
        json.put("userId", userId)
        json.put("userName", userName)
        json.put("siteId", siteId)
        if (isChallengeMode) {
            JsonquizData.put("onlineChallenge", intent.getBooleanExtra("onlineChallenge", false))
            JsonquizData.put("userCreds", json)
            JsonquizData.put("quizAvailable", quizAvailable)
            if (null != view) {
                JsonquizData.put("reMatch", isOnlineRematch)
                view.loadUrl("javascript:initialize($JsonquizData)")
            } else {
                JsonquizData.put("reMatch", true)
                reMatchByHost()
            }

            isCurrentAffairs = JsonquizData.optJSONObject("quizData")?.optString("resourceName")
                ?.contains("Current Affairs") == true || JsonquizData.optString("weekly").equals("yes")
                    || JsonquizData.optString("monthly").equals("yes") || selectedQuizName.contains("Current Affairs")

            initializeMoreTestBooks()
        }
    }

    private fun reMatchByHost() {
        setWebViewLayoutParams(false)
        toggleMoreTestVisibility(false)
        webview?.webViewClient = object : WebViewClient() {
            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                webview?.loadUrl("javascript:initialize($JsonquizData)")
            }
        }
        webview?.loadUrl("file:///android_asset/quiz/assets/roomScreen.html")

        isCurrentAffairs = JsonquizData.optJSONObject("quizData")?.optString("resourceName")
            ?.contains("Current Affairs") == true || JsonquizData.optString("weekly").equals("yes")
                || JsonquizData.optString("monthly").equals("yes") || selectedQuizName.contains("Current Affairs")

        initializeMoreTestBooks()
    }

    private fun initializeJoinGame(connectionData: JSONObject) {
        if (connectionData.has("connectionCode")) {
            val connectionCode = connectionData.optString("connectionCode")
            val wonderPubSharedPrefs = WonderPubSharedPrefs.getInstance(applicationContext)
            val userId = wonderPubSharedPrefs.usermobile
            val userName = wonderPubSharedPrefs.username
            val siteId = BuildConfig.SITE_ID
            val json = JSONObject()
            json.put("userId", userId)
            json.put("userName", userName)
            json.put("siteId", siteId)
            json.put("connectionCode", connectionCode)
            json.put("userImage", wonderPubSharedPrefs.userImage)
            json.put("hasAccess", connectionData.optBoolean("hasAccess"))
            webview?.loadUrl("javascript:initialize($json)")
            //webview?.loadUrl("javascript:getUserChallengeId($connectionData)")
        }
    }

    private fun initJoinGame(data: String?) {
        webview?.loadUrl("about:blank")
        setupWebView()
        loadUrl("file:///android_asset/quiz/assets/joinRoom.html", data)
    }

    private fun initHostGame(quizAvailable: Boolean = true) {
        setupWebView()
        if (isChallengePrevious || isChallengeNext) {
            setupChallenge(webview, quizAvailable)
        } else {
            loadUrl("file:///android_asset/quiz/assets/roomScreen.html")
        }
    }

    private fun loadAccessCode(data: JSONObject? = null, challengerData: JSONObject? = null) {
          if (null != data) {
                if (isChallengeMode) {
                    webview?.loadUrl("javascript:gameCode($data)")
                }
        }
    }
    //endregion
    fun loadWebView(stats: Boolean = false) {
        setupWebView()
        try {
            webview?.setWebViewClient(object : WebViewClient() {
                override fun onReceivedError(view: WebView, errorCode: Int, description: String, failingUrl: String) {
                    try {
                        Log.e("data error", ":")
                        webview?.visibility = View.VISIBLE
                        webview?.loadUrl("about:blank")
                    } catch (e: java.lang.Exception) {
                        e.printStackTrace()
                    }
                }

                override fun onPageFinished(view: WebView?, url: String?) {
                    try {
                        progressLoader?.visibility = View.GONE
                        webview?.visibility = View.VISIBLE

                        isCurrentAffairs = JsonquizData.optJSONObject("quizData")?.optString("resourceName")
                            ?.contains("Current Affairs") == true || JsonquizData.optString("weekly").equals("yes")
                                || JsonquizData.optString("monthly").equals("yes") || selectedQuizName.contains("Current Affairs")

                        if (JsonquizData.optJSONObject("quizData")?.optString("resourceName")?.isEmpty() == true) {
                            JsonquizData.put("quizData", JsonquizData.optJSONObject("quizData").put("resourceName", intent.getStringExtra("resourceName")))
                        }

                        if (isGPTQuiz && !intent.getStringExtra("selectedFormat")?.isEmpty()!!) {
                            JsonquizData.put("quizData", JsonquizData.optJSONObject("quizData").put("resourceName", intent.getStringExtra("resourceName")))
                            JsonquizData.put("realDailyTestDtlId", intent.getStringExtra("realDailyTestDtlId"))
                        }

                        if (isGPTQuiz) {
                            JsonquizData.put("timeValue", "15")
                            JsonquizData.put("resId", intent.getStringExtra("resId"))
                            JsonquizData.put("bookgpt", true)
                            JsonquizData.put("gptTestSeries", true)
                            JsonquizData.put("quizId", intent.getStringExtra("resId"))
                        }

                        if (!stats) {
                            Log.e("Datasent", ":" + "testing" + JsonquizData)
                            if(isFromShop == true || intent.getBooleanExtra("history", false))
                            {
                                JsonquizData.put("isFromShop","disabled" )
                            }
                            webview?.loadUrl("javascript:receiveData($JsonquizData)")
                        } else {
                            webview?.loadUrl("javascript:getHistory($JsonquizData)")
                        }

                        initializeMoreTestBooks()
                    } catch (e: Exception) {
                        Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), mContext as Activity?)
                        e.printStackTrace()
                    }

                }
            })
            webview?.setWebChromeClient(object : WebChromeClient() {
                override fun onConsoleMessage(cm: ConsoleMessage): Boolean {

                    return true
                }

                override fun onProgressChanged(view: WebView, newProgress: Int) {
                    super.onProgressChanged(view, newProgress)

                }
            })
            webview?.loadUrl("file:///android_asset/quiz/assets/prepJoyGame.html")

        } catch (e: Exception) {
            Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), this)
            e.printStackTrace()
        }

    }

    private fun initializeMoreTestBooks() {
        runOnUiThread {
            initMagazineRecycler()
            initCompExamBooksRecycler()
            initMagazineBooks()
            initCompExamBooks()
            getMagazines()
            getCompExamBooks()
        }
    }

    @SuppressLint("MissingPermission")
    private fun isNetworkConnected(): Boolean {
        val cm: ConnectivityManager = mContext?.getSystemService(CONNECTIVITY_SERVICE) as ConnectivityManager
        return cm.getActiveNetworkInfo() != null && cm.getActiveNetworkInfo()!!.isConnected()
    }

    private fun startAudio(resId: Int, looping: Boolean) {
        try {
            mediaplayer = MediaPlayer.create(mContext, resId)
            if (null != mediaplayer && soundenabled) {
                mediaList.add(mediaplayer)
                if (looping) {
                    mediaplayer.isLooping = true
                }
                mediaplayer.setOnPreparedListener {
                    mediaplayer.start()
                }
            }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }
    }

    private fun quizQuestionAnswersResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
            }
            Status.SUCCESSFUL -> {
                data.data?.let {
                    try {
                        prepareDailyQuizJson(it)
                        if (isChallengeMode && selectedQuizMode?.isEmpty() == true) {
                           initHostGame()
                        } else {
                            loadWebView()
                        }

                    } catch (e: JSONException) {
                        e.printStackTrace()
                    }
                }
            }
            Status.HTTP_UNAVAILABLE -> {

                try {
                    progressLoader?.visibility = View.GONE
                } catch (e: Exception) {
                    Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), this)
                    e.printStackTrace()
                }
            }

            Status.ERROR -> {
                try {
                    progressLoader?.visibility = View.GONE
                    if (data.error.toString().contains("401")) {
                        WonderPubSharedPrefs.getInstance(applicationContext).clearAllSharePref()
                        val intent = Intent(this, LoginActivity::class.java)
                        startActivity(intent)
                        finish()
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

    private fun getDeviceScreenHeight(): Int {
        val displayMetrics = DisplayMetrics()
        windowManager.defaultDisplay.getMetrics(displayMetrics)

        val height = displayMetrics.heightPixels

        return height
    }

    private fun getDeviceScreenWidth(): Int {
        val displayMetrics = DisplayMetrics()
        windowManager.defaultDisplay.getMetrics(displayMetrics)

        val width = displayMetrics.widthPixels

        return width
    }

    //region JSON details
    private fun basicDetailsJson() {
        val prefs = WonderPubSharedPrefs.getInstance(this)
        val userid: String = BuildConfig.SITE_ID + "_" + prefs.usermobile
        JsonquizData.put("userId", userid)
        JsonquizData.put("userName", prefs.username)
        JsonquizData.put("city", prefs.userDistrict)
        JsonquizData.put("state", prefs.userState)
        val url: String = prefs.userImage
        val check: Boolean = "null" in url
        if (check) {
            JsonquizData.put("userImage", "")

        } else {
            JsonquizData.put("userImage", prefs.userImage)
        }
        // JsonquizData.put("userImage", WonderPubSharedPrefs.getInstance(this).userImage )
        JsonquizData.put("xauth", prefs.accessToken)
        JsonquizData.put("baseUrl", WSAPIManager.SERVICE3.toString() + "api/quizSubmit")
        JsonquizData.put("serviceURL", WSAPIManager.SERVICE3.toString())
        JsonquizData.put("siteId", BuildConfig.SITE_ID)
        JsonquizData.put("source", "android")
        Log.e("dsdsdsds", ":" + prefs.sharedPrefsContentLanguagePref)
        // JsonquizData.put("language", "")//WonderPubSharedPrefs.getInstance(mContext). sharedPrefsContentLanguagePref.lowercase())
        if ((!language1?.isEmpty()!! && !language1.equals("null")) && (!language2?.isEmpty()!! && !language2.equals("null"))) {
            JsonquizData.put("language", prefs.sharedPrefsContentLanguagePref.lowercase())
        } else {
            JsonquizData.put("language", "")
        }
        JsonquizData.put("sound", if (prefs.sharedPrefsGameSound) "on" else "off")
        JsonquizData.put("nextChallenger", "")
        val place = Utils.challengerPlace(applicationContext)
        JsonquizData.put("nextchallengerPlace", place)
        JsonquizData.put("quizType", selectedQuizMode)
        JsonquizData.put("resourceDate", "")
        if (isGPTQuiz) {
            JsonquizData.put("timeValue", intent.getStringExtra("timeValue"))
        }
        else {
            JsonquizData.put("timeValue", prefs.sharedPrefsQuizDurationPref)
        }
        JsonquizData.put("gameEbook", isFromShop)
    }

    private fun prepareDailyQuizJson(jsonObject: JSONObject) {
        basicDetailsJson()
        JsonquizData.put("weekly", "no")
        JsonquizData.put("monthly", "no")
        JsonquizData.put("quizData", jsonObject)
        JsonquizData.put("resId", quizid)
        JsonquizData.put("previous", previosVal)
        JsonquizData.put("next", nextVal)
        if (selectedQuizMode == null || selectedQuizMode == "play") {
            JsonquizData.put("quizType", "")
        }
    }

    private fun prepareDailyTestJson(jsonObject: JSONObject,
                                     testId: String,
                                     testMode: String) {
        basicDetailsJson()
        jsonObject.put("challengerName", challengerName)
        jsonObject.put("challengerPlace", challengerPlace)
        jsonObject.put("realDailyTestDtlId", realDailyTestDtlId)

        JsonquizData.put("quizData", jsonObject)
        JsonquizData.put("testId", testId)
        JsonquizData.put("quizType", testMode)
        JsonquizData.put("previous", previosVal)
        JsonquizData.put("next", nextVal)
        selectedTestDate?.let {
            JsonquizData.put("resourceDate", it)
        }
    }

    private fun prepareMultiQuizJson(jsonObject: JSONObject) {
        basicDetailsJson()
        quizType?.let { type ->
            when (type) {
                "weekly" -> {
                    JsonquizData.put("weekly", "yes")
                    JsonquizData.put("monthly", "no")
                    selectedWeek?.let { JsonquizData.put("resourceDate", it) }
                }
                "monthly" -> {
                    JsonquizData.put("monthly", "yes")
                    JsonquizData.put("weekly", "no")
                    selectedMonth?.let { JsonquizData.put("resourceDate", it) }
                }
                else -> {
                }
            }
        }
        jsonObject.put("challengerName", challengerName)
        jsonObject.put("challengerPlace", challengerPlace)
        jsonObject.put("realDailyTestDtlId", realDailyTestDtlId)
        if (language1 != null && language1!!.isNotEmpty()) {
            jsonObject.put("language1", language1)
        }
        if (language2 != null && language2!!.isNotEmpty()) {
            jsonObject.put("language2", language2)
        }
        JsonquizData.put("quizData", jsonObject)
        JsonquizData.put("previous", previosVal)
        JsonquizData.put("next", nextVal)
        if (selectedQuizMode == null || selectedQuizMode == "play") {
            JsonquizData.put("quizType", "")
        }
    }
    //endregion

    override fun onBackPressed() {
        try {
            showCloseDialog()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        lifecycle.removeObserver(foregroundBackgroundObserver)
        onSaveQuizData("")
        LocalBroadcastManager.getInstance(this).unregisterReceiver(declineReceiver)
    }



    private fun loadBlank() {
        runOnUiThread {
            webview?.webViewClient = WebViewClient()
            webview?.loadUrl("about:blank")
        }
    }

    private fun exitScreen() {
        stopMediaPlayer()
        stopAllMediaPlayer()
        loadBlank()
        onSaveQuizData("")
        if (isFromDeepLink == true) {
            val dashboard = Intent(
                this@QuizActivity,
                DashBoardActivity::class.java
            )
            startActivity(dashboard)
        }
        else {
            finish()
        }
        //finish()
    }

    fun showCloseDialog() {
        if (!isStatsShown && !isQuizSubmitted) {
            val dialogClickListener = DialogInterface.OnClickListener { dialog, which ->
                when (which) {
                    DialogInterface.BUTTON_POSITIVE -> {
                        try {
                            exitScreen()
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                    DialogInterface.BUTTON_NEGATIVE ->
                        dialog.dismiss()
                }
            }
            runOnUiThread {
                val builder = AlertDialog.Builder(this)
                builder.setTitle("Exit?")
                builder.setMessage("You will loose your progress if you exit.").
                setPositiveButton("EXIT", dialogClickListener).
                setNegativeButton("NO", dialogClickListener).show()
            }
        } else {
           exitScreen()
        }
    }

    override fun networkStatusChecker(state: Boolean) {
        super.networkStatusChecker(state)
        if (!isQuizSubmitted && (!isChallengeMode || !isJoinMode)) {
            webview!!.loadUrl("javascript:submitFromAndroid()")
            Log.e("status", ":" + state)
        }
        if (!connectionStatus && isJoinMode && state) {
                connectionStatus = true
                webview?.loadUrl("javascript:connectionBack()")
        }
    }

    fun quizsubmit() {
        try {
            runOnUiThread {
                webview!!.loadUrl("javascript:submitFromAndroid()")
                isQuizSubmitted = true
            }
        } catch (e: Exception) {
            Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), this)
            e.printStackTrace()
        }

    }

    fun reportQuizIssue(json: JSONObject) {
        runOnUiThread {
            if (json.has("issuetext") && json.has("questionIssueId") &&
                json.has("selectedIssue")) {
                val issue = json.getString("selectedIssue")
                val issueId = json.getString("questionIssueId")
                if (!issue.isEmpty() && !issueId.isEmpty()) {
                    dailyViewModel.reportIssue(json)
                    progressLoader?.visibility = View.VISIBLE
                } else {
                    Toast.makeText(applicationContext,
                        "Nothing to report", Toast.LENGTH_SHORT).show()
                }
            }

        }
    }

    private fun stopMediaPlayer() {
        try {
            (getSystemService(
                    AUDIO_SERVICE
            ) as AudioManager).requestAudioFocus(
                { }, AudioManager.STREAM_MUSIC,
                    AudioManager.AUDIOFOCUS_GAIN_TRANSIENT
            )

        } catch (e: Exception) {
            Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), this)
            e.printStackTrace()
        }
    }


    override fun onPause() {
        Wonderslate.APP_STATE = Lifecycle.Event.ON_PAUSE
        try {
            super.onPause()
            if (!isSummaryClicked!!) {
                var exit = if (isStatsShown || isChallengeMode || isJoinMode) {
                    false
                } else pTestMode.isBlank()
                exit = false
                if(exit)
                {
                    //releaseWebView()
                    stopMediaPlayer()
                    stopAllMediaPlayer()
                    runOnUiThread {
                        webview?.loadUrl("about:blank")
                    }
                    finish()
                } else if (isChallengeMode || isJoinMode) {
                    webview?.postDelayed({
                        if (Wonderslate.APP_STATE == Lifecycle.Event.ON_STOP) {
                            runOnUiThread {  stopAllMediaPlayer()
                                webview?.loadUrl("javascript:appMinimized()") }
                        }
                    }, 5000)
                }
            }
        } catch (e: Exception) {
           // Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), this)
            e.printStackTrace()
        }
    }

    private fun stopAllMediaPlayer() {
        try {
            for (i in 0 until mediaList.size) {
                try {
                    mediaList[i].stop()
                    mediaList[i].release()
                } catch (e: Exception) {

                }
            }
            isBattleAudioStarted = false
        } catch (e: Exception) {
            Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), this)
            e.printStackTrace()
        }
    }

    private fun multiQuizResponse(data: Data<QuizResponse>) {
        when (data.responseType) {
            Status.LOADING -> {

            }
            Status.SUCCESSFUL -> {
                progressLoader?.visibility = View.GONE
                data.data?.let { loadMultiQuiz(it) }
            }
            Status.ERROR -> {
                serverError()
            }
            Status.HTTP_UNAVAILABLE -> {
                networkError()
            }
        }
    }

    private fun observeIssue(data: Data<JSONObject>) {
        when(data.responseType) {
            Status.LOADING -> {

            }
            Status.SUCCESSFUL -> {
                progressLoader?.visibility = View.GONE
                data.data?.let {
                    if (null != webview) {
                        progressLoader?.visibility = View.GONE
                        webview?.loadUrl("javascript:clearModal()")
                        Toast.makeText(applicationContext,
                            "Thanks for your valuable feedback", Toast.LENGTH_SHORT).show()
                    }
                }
            }
            Status.ERROR -> {
                serverError()
            }
            Status.HTTP_UNAVAILABLE -> {
                networkError()
            }
        }
    }

    private fun dailyTestResponse(data: Data<QuizResponse>) {
        when (data.responseType) {
            Status.LOADING -> {

            }
            Status.SUCCESSFUL -> {
                progressLoader?.visibility = View.GONE
                data.data?.let { loadDailyTest(it) }
            }
            Status.ERROR -> {
                serverError()
            }
            Status.HTTP_UNAVAILABLE -> {
                networkError()
            }
        }
    }

    private fun quizResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
                //progressLoader.visibility = View.GONE
            }
            Status.SUCCESSFUL -> {
                progressLoader?.visibility = View.GONE
                data.data?.let {
                    Log.e("Quizadadassd", "" + data)
                    data.data?.let {
                        loadQuizData(it)
                    }
                }
            }
            Status.HTTP_UNAVAILABLE -> {
                networkError()
            }

            Status.ERROR -> {
                serverError()
            }
        }
    }

    private fun networkError() {
        progressLoader?.visibility = View.GONE
        if (Utils.isNetworkAvailable(this)) {
            Utils.showTopSnackBar(resources.getString(R.string.server_under_maintenance), this)
        } else {
            Utils.showTopSnackBar(resources.getString(R.string.internet_connection_offline_text), this)
        }
    }

    private fun serverError() {
        progressLoader?.visibility = View.GONE
        if (Utils.isNetworkAvailable(this)) {
            Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), this)
        } else {
            Utils.showTopSnackBar(resources.getString(R.string.internet_connection_offline_text), this)
        }
    }

    private fun onlineGameRematch() {
        runOnUiThread { setupChallenge() }
    }

    private fun onGameCode(connectionCode: String,
                           quizId: String ?= null, quizMode: String ?= null) {
        val lastUser = if (intent.hasExtra("lastUser")) {
            intent.getStringExtra("lastUser")!!
        } else {
            ""
        }
        val bookId = if (intent.hasExtra("bookId")) {
            intent.getStringExtra("bookId")!!
        } else {
            ""
        }
        val challenger = WonderPubSharedPrefs.getInstance(applicationContext).username
        if (quizId != null && quizMode != null) {
            runOnUiThread {
                viewModel.accessCode(connectionCode, quizId, quizMode, lastUser, challenger, bookId)
                progressLoader?.visibility = View.VISIBLE
            }
        } else {
            var id: String? = null
            if (null != quizid) {
                id = quizid
            }
            runOnUiThread {
                val type = if (isFromShop == true) {
                    "regular"
                } else {
                    "currentAffairs"
                }
                if (realDailyTestDtlId.isNullOrEmpty()) {
                    viewModel.accessCode(connectionCode, id, type, lastUser, challenger, bookId)
                    progressLoader?.visibility = View.VISIBLE
                }
            }
            realDailyTestDtlId?.let {
                runOnUiThread {
                    if (it.isNotEmpty()) {
                        viewModel.accessCode(connectionCode, it, "dailyTests", lastUser, challenger)
                        progressLoader?.visibility = View.VISIBLE
                    }
                }
            }
        }
    }

    private fun onConnectionLost() {
       connectionStatus = false
    }

    private fun observeAccessCode(data: Data<JSONObject>) {
        progressLoader?.visibility = View.GONE
        when(data.responseType) {
            Status.SUCCESSFUL -> {
                data.data?.let {
                    loadAccessCode(it)
                }
            }
            Status.ERROR, Status.LOADING, Status.HTTP_UNAVAILABLE -> {
                // Handle other cases
            }
        }
    }

    private fun loadDailyTest(output: QuizResponse) {
        val testResponse = WonderPubSharedPrefs.getInstance(applicationContext).testDatesResponse
        selectedTestDate?.let {
            val result = GenerateDates.checkifDateExist(it, testResponse)
            previosVal = result.split("-")[0]
            nextVal = result.split("-")[1]
        }
        val jsonObject = JSONObject()
        jsonObject.put("results", JSONArray(output.result))
        jsonObject.put("challengerName", output.challengerName)
        jsonObject.put("challengerPlace", output.challengerPlace)
        jsonObject.put("realDailyTestDtlId",realDailyTestDtlId)
        JsonquizData.put("quizData", jsonObject)
        JsonquizData.put("language1", output.language1)
        JsonquizData.put("language2", output.language2)
        JsonquizData.put("previous", previosVal)
        JsonquizData.put("next", nextVal)
        selectedQuizMode?.let {
            JsonquizData.put("quizType", it)
        }
        runOnUiThread {
//            webview?.loadUrl("about:blank")
            if (isChallengeMode && selectedQuizMode.toString().isEmpty()) {
                initHostGame()
            } else {
                loadWebView()
            }
        }
    }

    private fun loadMultiQuiz(output: QuizResponse) {
        setupMultiQuiz(output)
        if (selectedQuizMode == "") {
            if (isChallengeMode) {
                toggleShare(false)
                runOnUiThread {
                    val isAvailable = output.result.isNotBlank() && output.result != "[]"
                    initHostGame(isAvailable)
                }
            } else {
                navigateMultiQuiz(output)
            }
        } else {
            runOnUiThread {
                webview?.loadUrl("about:blank")
                loadWebView()
            }
        }
    }

    private fun setupMultiQuiz(output: QuizResponse) {
        val datesResponse = WonderPubSharedPrefs.getInstance(applicationContext).datesResponse
        val jsonObject = JSONObject()
        jsonObject.put("results", JSONArray(output.result))
        jsonObject.put("challengerName", output.challengerName)
        jsonObject.put("challengerPlace", output.challengerPlace)
        jsonObject.put("realDailyTestDtlId",output.realDailyTestDtlId)
//        language1 = output.language1
//        language2 = output.language2
        selectedWeek?.let {
            val result = GenerateDates.validateWeek(it, datesResponse)
            previosVal = result.split("-")[0]
            nextVal = result.split("-")[1]
        }
        selectedMonth?.let {
            val result = GenerateDates.validateMonth(it, datesResponse)
            previosVal = result.split("-")[0]
            nextVal = result.split("-")[1]
        }
        prepareMultiQuizJson(jsonObject)
    }

    private fun navigateMultiQuiz(data: QuizResponse) {
        if (data.result.isBlank()) {
            Utils.showBottomSnackBar(resources.getString(R.string.no_quiz_data_available_week),
                this)
        } else {
            val intent = Intent(this, QuizInstructions::class.java)
            val quizType: String
            val intentKey: String
            var selection: String = ""
            if (noOfDays == "7") {
                quizType = "weekly"
                intentKey = "selectedWeek"
                selectedWeek?.let { selection = it }
            } else {
                quizType = "monthly"
                intentKey = "selectedMonth"
                selectedMonth?.let { selection = it }
            }
            intent.putExtra(intentKey, selection)
            intent.putExtra("quizType", quizType)
            intent.putExtra("noOfDays", noOfDays)
            intent.putExtra("noOfQuestions", noOfQuestions)
            intent.putExtra("quizData", data.result)
            intent.putExtra("language1", data.language1)
            intent.putExtra("language2", data.language2)
            intent.putExtra("challengerName", data.challengerName)
            intent.putExtra("challengerPlace", data.challengerPlace)
            intent.putExtra("realDailyTestDtlId",data.realDailyTestDtlId)
            intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
            this.startActivity(intent)
        }
    }

    private fun loadQuizData(jsonObject: JSONObject) {
        val jObj = JSONObject(jsonObject.toString())
        if (selectedQuizMode != "") {
            if (jObj.getString("resId").equals("-1")) {
                Utils.showBottomSnackBar(resources.getString(R.string.no_quiz_data_available), this)
                runOnUiThread { setupChallenge(webview, false) }
            } else {
                triggerQA(jObj)
            }
        } else {
            when {
                jObj.getString("resId").equals("-1") -> {
                    Utils.showBottomSnackBar(resources.getString(R.string.no_quiz_data_available), this)
                    runOnUiThread { setupChallenge(webview, false) }
                }
                isChallengeMode -> {
                    triggerQA(jObj)
                }
                else -> {
                    val intent = Intent(this, QuizInstructions::class.java)
                    intent.putExtra("quizId", jObj.getString("resId"))
                    intent.putExtra("language1", jObj.getString("language1"))
                    intent.putExtra("language2", jObj.getString("language2"))
                    intent.putExtra("selectedDate", selectedDate)
                    intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
                    intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
                    this.startActivity(intent)
                }
            }
        }

    }

    private fun triggerQA(jObj: JSONObject) {
        quizid = jObj.getString("resId")
        language1 = jObj.getString("language1")
        language2 = jObj.getString("language2")
        selectedDate?.let {
            val datesResponse = WonderPubSharedPrefs.getInstance(applicationContext).datesResponse
            val result = GenerateDates.checkifDateExist(it, datesResponse)
            previosVal = result.split("-")[0]
            nextVal = result.split("-")[1]
        }
        viewModel.getQuizQuestionsAnswers(jObj.getString("resId"))
    }

    private fun getQuiz(value: Int, rematch: Boolean = false) {
        isOnlineRematch = rematch
        if (isChallengeMode) {
            when(value) {
                1 ->  {
                    isChallengePrevious = true
                    isChallengeNext = false
                }
                2 -> {
                    isChallengePrevious = false
                    isChallengeNext = true
                }
            }
        }
        runOnUiThread {
            selectedDate?.let {
                selectedQuizMode = ""
                nextDailyQuiz(value)
            }
            selectedWeek?.let {
                selectedQuizMode = ""
                nextWeeklyQuiz(value, it)
            }
            selectedMonth?.let {
                selectedQuizMode = ""
                nextMonthlyQuiz(value, it)
            }
            if (selectedDate == null && selectedWeek == null && selectedMonth == null)  {
                executeQuiz("", value)
            }
        }
    }

    private fun executeQuiz(mode: String, value: Int) {
        selectedQuizMode = mode
        if (JsonquizData.has("testId")) {
            selectedTestDate?.let {
                nextDailyTest(value, it, JsonquizData.getString("testId"))
            }
        } else {
            runOnUiThread {
                selectedDate?.let {
                    nextDailyQuiz(value)
                }
                selectedWeek?.let {
                    nextWeeklyQuiz(value, it)
                }
                selectedMonth?.let {
                    nextMonthlyQuiz(value, it)
                }
            }
        }
    }

    private fun nextDailyTest(value: Int, currentDate: String, testId: String) {
        val nextDate = GenerateDates.findDay(currentDate, value)
        val input = DTQuizInput(testId, nextDate)
        selectedTestDate = nextDate
        dailyViewModel.retrieveTest(input)
        runOnUiThread {
            progressLoader?.visibility = View.VISIBLE
        }
    }

    private fun nextWeeklyQuiz(value: Int, currentWeek: String) {
        val nextWeek = if (value == 1) {
            GenerateDates.findPreviousWeek(currentWeek)
        } else {
            GenerateDates.findNextWeek(currentWeek)
        }
        selectedWeek = nextWeek
        val input = CFMultiQuizInput(nextWeek, noOfQuestions!!, noOfDays!!)
        viewModel.fetchWeeklyQuiz(input)
    }

    private fun nextMonthlyQuiz(value: Int, currentMonth: String) {
        val nextMonth = if (value == 1) {
            GenerateDates.findPreviousMonth(currentMonth)
        } else {
            GenerateDates.findNextMonth(currentMonth)
        }
        selectedMonth = nextMonth
        val input = CFMultiQuizInput(nextMonth, noOfQuestions!!, noOfDays!!)
        viewModel.fetchWeeklyQuiz(input)
    }

    private fun nextDailyQuiz(value: Int) {
        //1-previous 2- next
        selectedDate?.let {
            selectedDate = GenerateDates.findDay(it, value)
        }
        if (Utils.isNetworkAvailable(this)) {
            progressLoader?.visibility = View.VISIBLE
            selectedDate?.let { viewModel.getQuiz(it) }
        } else {
            Utils.showTopSnackBar("No internet connection found.", this)
        }
    }

    //region Quiz JS Interface
    override fun onAudio(type: JSAudio, looping: Boolean) {
        val id = when(type) {
            JSAudio.WIN -> R.raw.won
            JSAudio.LOSE -> R.raw.lose
            JSAudio.TIE -> R.raw.tie
            JSAudio.BATTLE -> R.raw.battle
            JSAudio.TIMER -> R.raw.timeup
            JSAudio.BOT_CORRECT -> R.raw.botcorrect
            JSAudio.BOT_INCORRECT -> R.raw.botincorrect
            JSAudio.USER -> R.raw.playstart
            JSAudio.SLIDE_BOT -> R.raw.opponentscroll
            JSAudio.SHOW_BOT -> R.raw.opponentfound
            JSAudio.STOP -> -1
            JSAudio.CORRECT -> R.raw.correct
            JSAudio.INCORRECT -> R.raw.incorrect
            JSAudio.QUESTION -> R.raw.questiondisplay
        }
        if (id == -1) {
            stopAllMediaPlayer()
        } else {
            when(type) {
                JSAudio.BATTLE -> {
                    if (!isBattleAudioStarted && soundenabled) {
                        startAudio(R.raw.battle, true)
                        isBattleAudioStarted = true
                    }
                }
                JSAudio.QUESTION -> {
                    if (!isQuestionAudioStarted && soundenabled) {
                        startAudio(R.raw.questiondisplay, false)
                        isQuestionAudioStarted = true
                    }
                }
                JSAudio.WIN -> toggleShare(true)
                else -> startAudio(id, looping)
            }
        }
    }

    private fun onEBooksClicked(from: String) {
        viewModel.logEvent(
            Event(
                type = EventType.SHOP_BOOKS_PAGE_OPENED,
                currentScreen = from
            )
        )
        //selectedTab = 2
        // bottomNavigation.show(2, true)
        // viewPager?.currentItem = 1

        // Select "Ebooks" tab
        //getEbooksFragment()?.selectTab(0)

        //shareApp.hide()

    }

    override fun onAudioChange(isSet: Boolean) {
        if (isSet) {
            soundenabled = true
            stopAllMediaPlayer()
            startAudio(R.raw.battle, true)
            isBattleAudioStarted = true
        } else {
            soundenabled = false
            stopAllMediaPlayer()
        }
    }

    override fun onBotRestart(nextChallenger: String, nextchallengerPlace: String) {
        val firebaseAnalytics: FirebaseAnalytics = Firebase.analytics
        val bundle = Bundle()
        bundle.putString(FirebaseAnalyticsUtils.ACTIVITY_NAME, "Quiz Result")
        bundle.putString(FirebaseAnalyticsUtils.LOCATION, "Quiz Rematch")
        firebaseAnalytics.logEvent(FirebaseAnalyticsUtils.ACTION, bundle)
        toggleShare(false)
        initWebView(nextChallenger, nextchallengerPlace)
    }

    override fun onBotPlay(mode: BotPlay) {
        toggleShare(false)
        setWebViewLayoutParams(false)
        toggleMoreTestVisibility(false)
        when(mode) {
            BotPlay.PREVIOUS -> getQuiz(1)
            BotPlay.NEXT -> getQuiz(2)
            BotPlay.PRE_PRACTICE -> executeQuiz("practice", 1)
            BotPlay.PRE_TEST -> executeQuiz("testSeries", 1)
            BotPlay.RE_PRACTICE -> restart("practice")
            BotPlay.RE_TEST -> restart("testSeries")
            BotPlay.NEXT_TEST -> executeQuiz("testSeries", 2)
            BotPlay.NEXT_PRACTICE -> executeQuiz("practice", 2)
        }
    }

    override fun onOnlineGame(mode: String) {
        toggleShare(false)
        when (mode) {
//            "rematch" -> {
//                onlineGameRematch()
//            }
            "previous" -> {
                getQuiz(1, true)
            }
            "next" -> {
                getQuiz(2, true)
            }
        }
    }

    override fun onStoringLastOpponent(opponentName: String, id: String) {
        val user = "$opponentName*$id"
        WonderPubSharedPrefs.getInstance(applicationContext).storeLastUserOnline(user)
    }

    override fun onPeerAccessCode(
        quizId: String?,
        resId: String?,
        quizMode: String,
        connectionCode: String
    ) {
        onGameCode(connectionCode, quizId, quizMode)
    }

    override fun onGameCodeGen(connectionCode: String) {
        onGameCode(connectionCode)
    }

    override fun onQuizRecordId(userDetails: String) {
    }

    override fun onShareGameCode(gameCode: String?) {
        shareGameCode(gameCode)
    }

    override fun onQuizQuestionIssueReported(reports: String) {
        reportQuizIssue(JSONObject(reports))
    }

    override fun onQuizExit() {
        disableTicker()
        stopAllMediaPlayer()
        showCloseDialog()
    }

    fun forceCloseQuiz() {
        disableTicker()
        finish()
    }

    override fun onInternetConnectionLost() {
        onConnectionLost()
    }

    override fun onQuizSubmit() {
        if (isNetworkConnected())
            quizsubmit()
        else
            Utils.showTopSnackBar("No internet connection found.", this)
    }

    override fun onSocketConnectionLost() {

        try {
            exitScreen()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onSummaryClick(userAnswers: String?, questions: String?) {
        runOnUiThread {
            isSummaryClicked = true
            summaryAnswers = userAnswers
            summaryQuestion = questions
            val quizSummaryIntent = Intent(
                this@QuizActivity,
                ResultActivity::class.java
            )
            quizSummaryIntent.putExtra("answers", summaryAnswers)
            quizSummaryIntent.putExtra("questions", summaryQuestion)
            startActivityWithAnim(quizSummaryIntent)
        }
    }

    override fun resizeWebview(resize: Boolean) {
        runOnUiThread {
            setWebViewLayoutParams(resize)

            if (resize) {
                toggleMoreTestVisibility(true)
                initializeContestListener(this)
                enableTicker()
            }
            else {
                toggleMoreTestVisibility(false)
                disableTicker()
            }
        }
    }

    override fun summaryBack() {}
    override fun loadMoreTests(resName: String) {
        isCurrentAffairs = (resName.contains("Current Affairs") || resName.contains("Weekly Current Affairs")
                || resName.contains("Monthly Current Affairs") || selectedQuizName.contains("Current Affairs"))
        initializeMoreTestBooks()
    }

    override fun onScrollContainer(value: Boolean) {
        webview?.contentHeight?.let { webview?.scrollTo(0, it) }
    }

    override fun onSaveQuizData(quizData: String) {
        val prefs = WonderPubSharedPrefs.getInstance(applicationContext)
        prefs.sharedPrefsQuizData = quizData
    }

    fun getQuizData(): String {
        return WonderPubSharedPrefs.getInstance(applicationContext).sharedPrefsQuizData
    }

    override fun redirectToLeaderBoard(quizMode: String?) {

    }

    override fun onEnterForeground() {
        //Send the saved quiz data to js code
        webview?.loadUrl("javascript:resumeTest()")
    }

    override fun onEnterBackground() {
        //Notify the js code that the app is in background
        webview?.loadUrl("javascript:pauseAndSaveQuiz()")
    }
    //endregion
}