package com.wonderslate.prepjoy.ui.videos;

import android.app.ActivityManager;
import android.content.Context;
import android.net.ConnectivityManager;
import android.telephony.TelephonyManager;
import android.text.Html;
import android.text.Spanned;
import android.util.Log;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Created by root on 12/16/16.
 */

public final class WSUtils {
    private static final String LOG_TAG = "WSUtils";
    private static Random random;

    /**
     * Code reference from method java.util.Collections.shuffle();
     * Usage:
     * int[] array = {1, 2, 3};
     * Util.shuffle(array);
     */
    public static void shuffle(int[] array) {
        if (random == null) random = new Random();
        int count = array.length;
        for (int i = count; i > 1; i--) {
            swap(array, i - 1, random.nextInt(i));
        }
    }

    private static void swap(int[] array, int i, int j) {
        int temp = array[i];
        array[i] = array[j];
        array[j] = temp;
    }

    /**
     * array initializer between given numbers
     * @param min
     * @param max
     * @return int[]
     */
    public static int [] range(int min, int max) {
        if (max == 0 || max <= min)
            return null;

        int[] array = new int[max - min];
        for(int k = 0; k < array.length; k++)
            array[k] = min + k;
        return array;
    }

    /**
     * array initializer between given numbers in random order
     * @param min
     * @param max
     * @return int[]
     */
    public static int [] rangeRandom(int min, int max) {
        int[] rangeArr = range(min, max);
        if (rangeArr == null) {
            return null;
        }
        shuffle(rangeArr);

        return rangeArr;
    }

    public static String stripHtml(String html) {
        Spanned spanned;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            spanned = Html.fromHtml(html, Html.FROM_HTML_MODE_LEGACY);
        } else {
            spanned = Html.fromHtml(html);
        }
        return spanned.toString();
    }

    public static String[] extractArrayFromJson(JSONObject json, String keyPrefix, int count) {
        String[] options = new String[count];
        String value;
        String valueStripTags;
        for (int j = 0; j < count; j++) {
            value = json.optString(keyPrefix + String.valueOf(j + 1));
            options[j] = value;
            //valueStripTags = Utils.stripHtml(value);
            //options[j] = Utils.stripHtml(valueStripTags);
        }
        return options;
    }

    public static boolean isConnectionFast(int type, int subType) {
        if(type== ConnectivityManager.TYPE_WIFI){
            return true;
        } else if (type==ConnectivityManager.TYPE_MOBILE) {
            switch(subType){
                case TelephonyManager.NETWORK_TYPE_1xRTT:
                    return false; // ~ 50-100 kbps
                case TelephonyManager.NETWORK_TYPE_CDMA:
                    return false; // ~ 14-64 kbps
                case TelephonyManager.NETWORK_TYPE_EDGE:
                    return false; // ~ 50-100 kbps
                case TelephonyManager.NETWORK_TYPE_EVDO_0:
                    return true; // ~ 400-1000 kbps
                case TelephonyManager.NETWORK_TYPE_EVDO_A:
                    return true; // ~ 600-1400 kbps
                case TelephonyManager.NETWORK_TYPE_GPRS:
                    return false; // ~ 100 kbps
                case TelephonyManager.NETWORK_TYPE_HSDPA:
                    return true; // ~ 2-14 Mbps
                case TelephonyManager.NETWORK_TYPE_HSPA:
                    return true; // ~ 700-1700 kbps
                case TelephonyManager.NETWORK_TYPE_HSUPA:
                    return true; // ~ 1-23 Mbps
                case TelephonyManager.NETWORK_TYPE_UMTS:
                    return true; // ~ 400-7000 kbps
                /*
                 * Above API level 7, make sure to set android:targetSdkVersion
                 * to appropriate level to use these
                 */
                case TelephonyManager.NETWORK_TYPE_EHRPD: // API level 11
                    return true; // ~ 1-2 Mbps
                case TelephonyManager.NETWORK_TYPE_EVDO_B: // API level 9
                    return true; // ~ 5 Mbps
                case TelephonyManager.NETWORK_TYPE_HSPAP: // API level 13
                    return true; // ~ 10-20 Mbps
                case TelephonyManager.NETWORK_TYPE_IDEN: // API level 8
                    return false; // ~25 kbps
                case TelephonyManager.NETWORK_TYPE_LTE: // API level 11
                    return true; // ~ 10+ Mbps
                // Unknown
                case TelephonyManager.NETWORK_TYPE_UNKNOWN:
                default:
                    return false;
            }
        } else {
            return false;
        }
    }


    public static List<String> extractListFromJson(JSONObject json, String keyPrefix, int count) {
        List<String> options = new ArrayList<String>(count);
        String value;
        for (int j = 0; j < count; j++) {
            value = json.optString(keyPrefix + String.valueOf(j + 1));
            //value = Utils.stripHtml(value);
            options.add(value);
        }
        return options;
    }

    public static Boolean addArrayToJson(JSONObject json, String keyPrefix, String[] array) {
        Boolean allDone = true;
        if (array.length == 0) {
            return allDone;
        }
        for (int j = 0; j < array.length; j++) {
            try {
                json.put(keyPrefix + String.valueOf(j + 1), array[j]);
            } catch (JSONException e) {
                Log.e(LOG_TAG,"JSONException",e);
                allDone = false;
            }
        }
        return allDone;
    }

    public static Boolean addListToJson(JSONObject json, String keyPrefix, List<String> bookHistory) {
        Boolean allDone = true;
        if (bookHistory.size() == 0) {
            return allDone;
        }
        for (int j = 0; j < bookHistory.size(); j++) {
            try {
                json.put(keyPrefix + String.valueOf(j + 1), bookHistory.get(j));
            } catch (JSONException e) {
                Log.e(LOG_TAG,"JSONException",e);
                allDone = false;
            }
        }
        return allDone;
    }

    public static String listToPrintString(List<String> strList) {
        StringBuilder sb = new StringBuilder();
        sb.append("{ ");
        int i;
        for (i = 0; i < strList.size(); i++) {
            if (i != 0) { sb.append(", "); }
            sb.append(strList.get(i));
        }
        sb.append(" }");
        return sb.toString();
    }

    public static boolean isMyServiceRunning(Context context, Class<?> serviceClass) {
        try {
            ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            for (ActivityManager.RunningServiceInfo service : manager.getRunningServices(Integer.MAX_VALUE)) {
                if (serviceClass.getName().equals(service.service.getClassName())) {
                    return true;
                }
            }
        }catch (NullPointerException e){
            Log.e(LOG_TAG, "isMyServiceRunning: Context is null");
        }
        return false;
    }

    public static LocalDateTime stringToDate(String dateStr){
        if (dateStr != null && !dateStr.isEmpty()) {
            dateStr = dateStr.replaceAll("#", ":");
            try {
                DateTimeFormatter sdf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                return LocalDateTime.parse(dateStr, sdf);
            }
            catch (Exception exception){
                try {
                    DateTimeFormatter sdf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
                    return LocalDateTime.parse(dateStr, sdf);
                }
                catch (Exception ex){
                    try {
                        dateStr = dateStr.replace("T", " ").replace(".000", "").replace("Z", "");
                        DateTimeFormatter sdf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        return LocalDateTime.parse(dateStr, sdf);
                    }
                    catch (Exception exep){
                        return LocalDateTime.now();
                    }
                }
            }
        } else {
            return LocalDateTime.now();
        }
    }

    public static String newStringToDate(String dateStr){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MMM-yyyy HH:mm:ss");
        return stringToDate(dateStr).format(formatter);
    }

    public static String newStringToDate(String dateStr, String pattern){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return stringToDate(dateStr).format(formatter);
    }

}
