package com.wonderslate.prepjoy.ui.dashboard;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;


import com.wonderslate.prepjoy.R;

import java.util.ArrayList;
import java.util.Collections;

public class UserPrefSelectLevelAdapter extends RecyclerView.Adapter<UserPrefSelectLevelAdapter.ViewHolder> {

    private final Context mContext;
    private final UserPreferanceSelectionActivity activity;
    private ArrayList<String> levelsList = new ArrayList<>();
    private String levelName;

    public UserPrefSelectLevelAdapter(Context mcontext, UserPreferanceSelectionActivity selectionActivity) {
        this.mContext = mcontext;
        this.activity = selectionActivity;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        // Inflate Layout
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_user_pref_selection, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        try {
            String level = levelsList.get(position);
            holder.textViewName.setText(level);

            holder.imageSelected.setVisibility(View.GONE);
            if (levelName != null && levelName.equalsIgnoreCase(level)) {
                holder.textViewName.setTextColor(ContextCompat.getColor(mContext, R.color.primary_bg_red));
                holder.imageSelected.setVisibility(View.VISIBLE);
            } else {
                holder.textViewName.setTextColor(ContextCompat.getColor(mContext, R.color.white));
            }

            holder.layoutContainer.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    activity.onLevelSelected(holder.textViewName.getText().toString().trim());
                }
            });

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public int getItemCount() {
        return levelsList.size();
    }

    @Override
    public int getItemViewType(int position) {
        return position;
    }

    public class ViewHolder extends RecyclerView.ViewHolder {

        private final TextView textViewName;
        private LinearLayout layoutContainer;
        private ImageView imageSelected;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            textViewName = itemView.findViewById(R.id.textView_name);
            layoutContainer = itemView.findViewById(R.id.container);
            imageSelected = itemView.findViewById(R.id.image_selected);
        }
    }

    public void setLevelDala(ArrayList<String> lavels, String levelNamePos) {
        this.levelsList = lavels;
        this.levelName = levelNamePos;
        notifyDataSetChanged();
    }
}