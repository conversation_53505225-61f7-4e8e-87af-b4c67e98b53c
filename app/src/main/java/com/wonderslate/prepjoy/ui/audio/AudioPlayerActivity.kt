package com.wonderslate.prepjoy.ui.audio

import android.annotation.SuppressLint
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Intent
import android.graphics.Color
import android.media.AudioAttributes
import android.media.MediaPlayer
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.support.v4.media.session.PlaybackStateCompat
import android.view.View
import android.widget.Button
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.SeekBar
import android.widget.TextView
import androidx.appcompat.widget.ListPopupWindow
import androidx.cardview.widget.CardView
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import androidx.media.app.NotificationCompat
import com.wang.avi.AVLoadingIndicatorView
import com.wonderslate.commons.Data
import com.wonderslate.commons.Status
import com.wonderslate.domain.entities.AudioData
import com.wonderslate.domain.entities.ResourceLogData
import com.wonderslate.prepjoy.BuildConfig
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.ui.BaseActivity
import org.json.JSONArray
import org.json.JSONObject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.text.DateFormat
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

class AudioPlayerActivity : BaseActivity() {
    private val model by viewModel<AudioViewModel>()
    private var chosenDate = ""
    private var viewedFrom = ""

    private var thumbnailImageView: ImageView? = null
    private var playPauseButton: ImageButton? = null
    private var rewindButton: ImageButton? = null
    private var forwardButton: ImageButton? = null
    private var previousButton: ImageButton? = null
    private var nextButton: ImageButton? = null
    private var mediaPlayer: MediaPlayer? = null
    private var isPlaying = false
    private var seekBar: SeekBar? = null
    private var cvBack: CardView? = null
    private var tvResourceName: TextView? = null
    private var tvTitle: TextView? = null
    private var avProgressLoader: AVLoadingIndicatorView? = null
    private var tvTimePlayed: TextView? = null
    private var tvTotalTime: TextView? = null
    private var rlAudioParent: RelativeLayout? = null
    private var rlNoDataLayout: RelativeLayout? = null
    private var btnSpeed: Button? = null

    private var audioTitle: String = ""
    private var audioPageTitle: String = "Playing "
    private val audioStreamURLPrefix = "http://docs.google.com/uc?export=open&id="
    // Boolean flag to track if the app is in the background
    private var isInBackground = false
    private var audioSpeed: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.hide()
        initObserver()

        intent.getStringExtra("inputDate")
                ?.let {
                    chosenDate = changeDateFormat(it)
                    model.getAudio(chosenDate)
                }

        intent.getStringExtra("viewFrom")?.let {
            viewedFrom = it
        }

        init()
        audioPageTitle += getString(R.string.audio_page_title)
    }

    private fun init() {
        // Initialize UI elements
        cvBack = findViewById(R.id.btnBack)
        tvResourceName = findViewById(R.id.tvResourceName)
        tvTitle = findViewById(R.id.tvResourceTitle)
        thumbnailImageView = findViewById(R.id.thumbnailImageView)
        playPauseButton = findViewById(R.id.playPauseButton)
        rewindButton = findViewById(R.id.rewindButton)
        forwardButton = findViewById(R.id.forwardButton)
        previousButton = findViewById(R.id.previousButton)
        nextButton = findViewById(R.id.nextButton)
        seekBar = findViewById(R.id.seekBar)
        tvTimePlayed = findViewById(R.id.tvTimePlayed)
        tvTotalTime = findViewById(R.id.tvTotalTime)
        avProgressLoader = findViewById(R.id.progressLoader)
        avProgressLoader?.smoothToShow()
        rlAudioParent = findViewById(R.id.rlAudioParent)
        rlNoDataLayout = findViewById(R.id.rlNoDataLayout)
        btnSpeed = findViewById(R.id.speedControlButton)

        setupToolbar()
    }

    private fun logAudioOpen(id: String) {
        model.logResourceOpen(
                ResourceLogData(
                        id,
                        BuildConfig.SITE_ID,
                        "android",
                        viewedFrom,
                        "view"
                )
        )
    }

    private fun initObserver() {
        model.audioResponse.observe(this, ::audioResponse)
        model.resLogResponse.observe(this, ::logResourceResponse)
    }

    private fun audioResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {}

            Status.SUCCESSFUL -> {
                data.data?.let {
                    loadAudioData(it)
                }
            }

            Status.HTTP_UNAVAILABLE -> {
                avProgressLoader?.smoothToHide()
                rlAudioParent?.visibility = View.GONE
                rlNoDataLayout?.visibility = View.VISIBLE
            }

            Status.ERROR -> {
                avProgressLoader?.smoothToHide()
                rlAudioParent?.visibility = View.GONE
                rlNoDataLayout?.visibility = View.VISIBLE
            }
        }
    }

    private fun prepareMediaAndPlay(audioURL: String, id: Int) {
        // Initialize MediaPlayer
        mediaPlayer = MediaPlayer()
        logAudioOpen(id.toString())
        // Set the media source
        try {
            mediaPlayer!!.setDataSource(audioURL)
            mediaPlayer!!.setAudioAttributes(AudioAttributes.Builder()
                    .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                    .build())
            mediaPlayer!!.prepareAsync()
            mediaPlayer!!.setOnPreparedListener { mediaPlayer: MediaPlayer ->
                avProgressLoader?.smoothToHide()
                mediaPlayer.start()
                // Set SeekBar max value to the media duration
                seekBar?.progress = 0
                seekBar?.max = mediaPlayer.duration
                // Update the total time TextView with the media duration
                val totalDuration = mediaPlayer.duration
                val totalDurationText = formatDuration(totalDuration)
                tvTotalTime?.text = totalDurationText

                // Start updating the time played
                updateSeekBar()
                playPauseButton?.setImageResource(R.drawable.ic_media_pause)
                playPauseButton?.setColorFilter(ContextCompat.getColor(applicationContext, R.color.white))
                isPlaying = true
                setPlaybackSpeed(1.0f)
            }
        } catch (e: Exception) {
            playPauseButton?.setImageResource(R.drawable.ic_media_play)
            playPauseButton?.setColorFilter(ContextCompat.getColor(applicationContext, R.color.white))
            e.printStackTrace()
        }

        // Update SeekBar progress as the media plays
        mediaPlayer!!.setOnBufferingUpdateListener { _: MediaPlayer?, percent: Int -> seekBar?.secondaryProgress = percent }

        // SeekBar change listener to seek the media
        seekBar?.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    mediaPlayer!!.seekTo(progress)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar) {
                // Pause the media while seeking
                if (mediaPlayer!!.isPlaying) {
                    mediaPlayer!!.pause()
                    isPlaying = false
                    playPauseButton?.setImageResource(R.drawable.ic_media_play)
                    playPauseButton?.setColorFilter(ContextCompat.getColor(applicationContext, R.color.white))
                }
            }

            override fun onStopTrackingTouch(seekBar: SeekBar) {
                // Resume playback after seeking
                if (!mediaPlayer!!.isPlaying) {
                    mediaPlayer!!.start()
                    isPlaying = true
                    playPauseButton?.setImageResource(R.drawable.ic_media_pause)
                    playPauseButton?.setColorFilter(ContextCompat.getColor(applicationContext, R.color.white))
                }
            }
        })

        // Play/Pause button click listener
        playPauseButton?.setOnClickListener {
            if (isPlaying) {
                mediaPlayer!!.pause()
                playPauseButton?.setImageResource(R.drawable.ic_media_play)
                playPauseButton?.setColorFilter(ContextCompat.getColor(applicationContext, R.color.white))
            } else {
                mediaPlayer!!.start()
                playPauseButton?.setImageResource(R.drawable.ic_media_pause)
                playPauseButton?.setColorFilter(ContextCompat.getColor(applicationContext, R.color.white))
            }
            isPlaying = !isPlaying

            // Update the notification state
            if (isAppInBackground()) {
                updateNotification(if (isPlaying) PlaybackStateCompat.STATE_PLAYING else PlaybackStateCompat.STATE_PAUSED)
            }
        }

        // Rewind button click listener
        rewindButton?.setOnClickListener {
            val currentPosition = mediaPlayer!!.currentPosition
            mediaPlayer!!.seekTo((currentPosition - 10000).coerceAtLeast(0))
        }

        // Forward button click listener
        forwardButton?.setOnClickListener {
            val currentPosition = mediaPlayer!!.currentPosition
            mediaPlayer!!.seekTo((currentPosition + 10000).coerceAtMost(mediaPlayer!!.duration))
        }

        // Previous button click listener
        previousButton?.setOnClickListener {
            avProgressLoader?.smoothToShow()
            if (mediaPlayer != null && mediaPlayer!!.isPlaying) {
                playPauseButton?.setImageResource(R.drawable.ic_media_play)
                playPauseButton?.setColorFilter(ContextCompat.getColor(applicationContext, R.color.white))
                mediaPlayer!!.release()
            }
            chosenDate = getPreviousDate(chosenDate)
            model.getAudio(chosenDate)
        }

        // Next button click listener
        nextButton?.setOnClickListener {
            avProgressLoader?.smoothToShow()
            if (mediaPlayer != null && mediaPlayer!!.isPlaying) {
                playPauseButton?.setImageResource(R.drawable.ic_media_play)
                playPauseButton?.setColorFilter(ContextCompat.getColor(applicationContext, R.color.white))
                mediaPlayer!!.release()
            }
            chosenDate = getNextDate(chosenDate)
            model.getAudio(chosenDate)
        }

        //Speed Control button click listener
        btnSpeed?.setOnClickListener {
            showSpeedOptions(it)
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channelId = "media_player_channel"
            val channelName: CharSequence = "Media Player Channel"
            val importance = NotificationManager.IMPORTANCE_LOW
            val channel = NotificationChannel(channelId, channelName, importance)
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun formatDuration(durationInMillis: Int): String {
        val minutes = durationInMillis / 60000
        val seconds = (durationInMillis % 60000) / 1000
        return String.format(Locale.getDefault(), "%02d:%02d", minutes, (seconds + 1))
    }

    override fun onBackPressed() {
        if (mediaPlayer != null && mediaPlayer!!.isPlaying) {
            mediaPlayer!!.stop()
            hideNotification()
        }
        super.onBackPressed()
    }

    private fun showSpeedOptions(view: View) {
        val listPopupWindow = ListPopupWindow(this)
        listPopupWindow.anchorView = view
        val data = listOf("0.5", "0.75", "Normal", "1.25", "1.5")
        var selectedPosition = 2

        for ((index, item) in data.withIndex()) {
            if (item == audioSpeed) {
                selectedPosition = index
            }
        }

        // Create an adapter for the list items
        val adapter = AudioSpeedArrayAdapter(this, R.layout.audio_speed_list_item, R.id.text1, data, selectedPosition, Color.RED)
        listPopupWindow.setAdapter(adapter)

        // Set a listener for list item clicks
        listPopupWindow.setOnItemClickListener { _, _, position, _ ->
            // Handle item click
            val selectedItem = data[position]

            // Close the list popup
            listPopupWindow.dismiss()
            when (selectedItem) {
                "Normal" -> setPlaybackSpeed(1.0f) // Normal (1x)
                "0.5" -> setPlaybackSpeed(0.5f) // Slow (0.5x)
                "0.75" -> setPlaybackSpeed(0.75f) // Slightly Slower (0.75x)
                "1.25" -> setPlaybackSpeed(1.25f) // Slightly Faster (1.25x)
                "1.5" -> setPlaybackSpeed(1.5f) // Faster (1.5x)
            }
        }

        // Show the list popup
        listPopupWindow.show()
    }


    @SuppressLint("SetTextI18n")
    private fun setPlaybackSpeed(speed: Float) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            mediaPlayer?.playbackParams = mediaPlayer?.playbackParams?.setSpeed(speed)!!
            btnSpeed?.text = "Speed  $speed"
            audioSpeed = if (speed == 1.0f) {
                "Normal"
            } else {
                speed.toString()
            }
        }
    }

    private fun logResourceResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {}
            Status.SUCCESSFUL -> {}
            Status.HTTP_UNAVAILABLE -> {}
            Status.ERROR -> {}
        }
    }

    private fun loadAudioData(it: JSONObject){
        val videosString = it.getString("audios")
        val videosJsonArray = JSONArray(videosString)
        if (videosJsonArray.length() > 0) {
            val audioDataList: ArrayList<AudioData> = arrayListOf()
            for (index in 0 until videosJsonArray.length()) {
                val audioObj = videosJsonArray.getJSONObject(index)
                audioDataList.add(getAudioObj(audioObj))
            }
            for (index in 0 until audioDataList.size) {
                if (audioDataList[index].videoLink.contains("drive")) {
                    val audioStreamURL = audioStreamURLPrefix + getAudioStreamId(audioDataList[index].videoLink)
                    audioTitle = audioDataList[index].title
                    tvTitle?.text = audioTitle
                    prepareMediaAndPlay(audioStreamURL, audioDataList[index].id)
                    return
                }
            }
        }
        else {
            rlAudioParent?.visibility = View.GONE
            rlNoDataLayout?.visibility = View.VISIBLE
        }
    }

    private fun setupToolbar() {
        tvResourceName?.text = getString(R.string.audio_page_title)
        cvBack?.setOnClickListener {
            onBackPressed()
        }
    }

    private fun getAudioStreamId(audioURL: String): String {
        // Find the index of "d/"
        val startIndex = audioURL.indexOf("d/") + 2

        // Find the index of "/view"
        val endIndex = audioURL.indexOf("/view")

        // Extract the content between "d/" and "/view" and return
        return audioURL.substring(startIndex, endIndex)
    }

    private fun getAudioObj(audioObj:JSONObject) : AudioData
    {
        return  AudioData(
                audioObj.getInt("id"),
                audioObj.getString("title"),
                audioObj.getString("videoLink"),
                audioObj.getString("answer"),
                audioObj.getString("showAnswer"),
                audioObj.getString("tag"),
                audioObj.getString("referenceLink"),
                audioObj.getString("deepLink"),
                audioObj.getString("showFullDetails"),
                audioObj.getString("dateCreated"),
                audioObj.getString("description"),
                audioObj.getString("resourceType")
        )
    }

    override fun onPause() {
        super.onPause()
        isInBackground = true
        if (mediaPlayer != null && mediaPlayer!!.isPlaying) {
            showNotification(if (isPlaying) PlaybackStateCompat.STATE_PLAYING else PlaybackStateCompat.STATE_PAUSED)
        }
    }

    override fun onResume() {
        super.onResume()
        isInBackground = false
        hideNotification()
    }

    // Update the SeekBar progress continuously
    private fun updateSeekBar() {
        try {
            seekBar!!.progress = mediaPlayer!!.currentPosition

            // Update the time played TextView with the current playback position
            val currentTime = mediaPlayer!!.currentPosition
            val currentTimeText = formatDuration(currentTime)
            tvTimePlayed?.text = currentTimeText

            Handler(Looper.getMainLooper()).postDelayed({
                updateSeekBar()
            }, 1000) // Update every 1 second (adjust as needed)
        } catch (ex: IllegalStateException) {
            ex.printStackTrace()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        hideNotification()
        mediaPlayer!!.release()
    }

    override fun getLayoutResource(): Int {
        return R.layout.activity_audio_player
    }

    private fun showNotification(playbackState: Int) {
        // Create a notification to control media playback
        val builder = androidx.core.app.NotificationCompat.Builder(this, "media_player_channel")
                .setSmallIcon(R.drawable.prepjoy_icon_light)
                .setContentTitle(audioTitle)
                .setContentText(audioPageTitle)
                .setStyle(NotificationCompat.MediaStyle()
                        .setShowActionsInCompactView(0, 1)) // Index of play/pause and stop buttons
                .setOngoing(true) // Not an ongoing notification
                .setAutoCancel(false) // Allow the notification to be dismissed by swiping

        // Create a PendingIntent for opening the app when the notification is tapped
        val intent = Intent(this, AudioPlayerActivity::class.java)
        intent.action = "notification_action"
        val contentIntent = PendingIntent.getActivity(this, 0, intent, PendingIntent.FLAG_IMMUTABLE)

        // Add play/pause and stop actions to the notification
        if (playbackState == PlaybackStateCompat.STATE_PLAYING) {
            builder.addAction(R.drawable.exo_icon_pause, "Pause", getPendingIntentForAction("play_pause"))
        } else {
            builder.addAction(R.drawable.exo_icon_play, "Play", getPendingIntentForAction("play_pause"))
        }
        builder.addAction(R.drawable.exo_icon_stop, "Stop", getPendingIntentForAction("stop"))

        // Set a PendingIntent to stop the media player when the notification is dismissed
        val stopIntent = Intent(this, AudioPlayerActivity::class.java)
        stopIntent.action = "stop"
        val stopPendingIntent = PendingIntent.getActivity(this, 0, stopIntent, PendingIntent.FLAG_IMMUTABLE)
        builder.setDeleteIntent(stopPendingIntent)

        builder.setContentIntent(contentIntent)

        // Show the notification
        val notificationManager = NotificationManagerCompat.from(this)
        notificationManager.notify(1, builder.build())
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        if (intent.action != null) {
            when (intent.action) {
                "stop" -> {
                    mediaPlayer!!.pause()
                    playPauseButton?.setImageResource(R.drawable.ic_media_play)
                    playPauseButton?.setColorFilter(ContextCompat.getColor(applicationContext, R.color.white))
                    updateNotification(if (isPlaying) PlaybackStateCompat.STATE_PLAYING else PlaybackStateCompat.STATE_PAUSED)
                    hideNotification()
                    isPlaying = false
                }
                "play_pause" -> {
                    isPlaying = if (mediaPlayer!!.isPlaying) {
                        mediaPlayer!!.pause()
                        playPauseButton?.setImageResource(R.drawable.ic_media_play)
                        playPauseButton?.setColorFilter(ContextCompat.getColor(applicationContext, R.color.white))
                        false
                    } else {
                        mediaPlayer!!.start()
                        playPauseButton?.setImageResource(R.drawable.ic_media_pause)
                        playPauseButton?.setColorFilter(ContextCompat.getColor(applicationContext, R.color.white))
                        true
                    }
                    updateNotification(if (isPlaying) PlaybackStateCompat.STATE_PLAYING else PlaybackStateCompat.STATE_PAUSED)
                }
                "notification_action" -> {}
            }
        }
    }

    private fun updateNotification(playbackState: Int) {
        val builder = androidx.core.app.NotificationCompat.Builder(this, "media_player_channel")
                .setSmallIcon(R.drawable.prepjoy_icon_light)
                .setContentTitle(audioTitle)
                .setContentText(audioPageTitle)
                .setStyle(NotificationCompat.MediaStyle()
                        .setShowActionsInCompactView(0, 1)) // Index of play/pause and stop buttons
                .setOngoing(true) // Not an ongoing notification
                .setAutoCancel(false) // Allow the notification to be dismissed by swiping

        val intent = Intent(this, AudioPlayerActivity::class.java)
        intent.action = "notification_action"
        val contentIntent = PendingIntent.getActivity(this, 0, intent, PendingIntent.FLAG_IMMUTABLE)

        if (playbackState == PlaybackStateCompat.STATE_PLAYING) {
            builder.addAction(R.drawable.exo_icon_pause, "Pause", getPendingIntentForAction("play_pause"))
        } else {
            builder.addAction(R.drawable.exo_icon_play, "Play", getPendingIntentForAction("play_pause"))
        }

        builder.addAction(R.drawable.exo_icon_stop, "Stop", getPendingIntentForAction("stop"))

        val stopIntent = Intent(this, AudioPlayerActivity::class.java)
        stopIntent.action = "stop"
        val stopPendingIntent = PendingIntent.getActivity(this, 0, stopIntent, PendingIntent.FLAG_IMMUTABLE)
        builder.setDeleteIntent(stopPendingIntent)

        builder.setContentIntent(contentIntent)

        val notificationManager = NotificationManagerCompat.from(this)
        notificationManager.notify(1, builder.build())
    }

    private fun hideNotification() {
        // Hide the notification when the app is in the foreground
        val notificationManager = NotificationManagerCompat.from(this)
        notificationManager.cancel(1)
    }

    private fun getPendingIntentForAction(action: String): PendingIntent {
        val intent = Intent(this, AudioPlayerActivity::class.java)
        intent.action = action
        return PendingIntent.getActivity(this, 0, intent, PendingIntent.FLAG_IMMUTABLE)
    }

    private fun changeDateFormat(dateString1: String): String {
        try {
            val df1: DateFormat = SimpleDateFormat("dd-MMM-yyyy", Locale.ENGLISH)
            var date1: Date? = null
            try {
                date1 = df1.parse(dateString1)
            } catch (e: ParseException) {
                e.printStackTrace()
            }
            val cal1: Calendar = Calendar.getInstance()
            if (date1 != null) {
                cal1.time = date1
            }

            val dateFormatter = SimpleDateFormat("dd-MM-yyyy", Locale.ENGLISH)

            return dateFormatter.format(cal1.time)
        } catch (e: Exception) {
            return dateString1
        }
    }

    private fun getPreviousDate(dateString: String, format: String = "dd-MM-yyyy"): String {
        val dateFormat = SimpleDateFormat(format, Locale.ENGLISH)
        val date = dateFormat.parse(dateString)

        val calendar = Calendar.getInstance()
        calendar.time = date!!
        calendar.add(Calendar.DAY_OF_YEAR, -1) // Subtract one day to get the previous date

        return dateFormat.format(calendar.time)
    }

    private fun getNextDate(dateString: String, format: String = "dd-MM-yyyy"): String {
        val dateFormat = SimpleDateFormat(format, Locale.ENGLISH)
        val date = dateFormat.parse(dateString)

        val calendar = Calendar.getInstance()
        calendar.time = date!!
        calendar.add(Calendar.DAY_OF_YEAR, 1) // Add one day to get the next date

        return dateFormat.format(calendar.time)
    }

    // Add a method to check if the app is in the background
    private fun isAppInBackground(): Boolean {
        return isInBackground
    }
}