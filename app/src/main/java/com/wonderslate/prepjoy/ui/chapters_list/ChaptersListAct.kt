package com.wonderslate.prepjoy.ui.chapters_list

import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.media3.common.util.Util
import com.wonderslate.data.network.Wonderslate
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.prepjoy.databinding.ActivityChaptersListBinding
import com.wonderslate.prepjoy.ui.dashboard.DashBoardActivity
import com.wonderslate.prepjoy.ui.new_book_details.BookDetailsAct
import com.wonderslate.prepjoy.ui.quiz_instruction.QuizInstructions
import com.wonderslate.prepjoy.ui.resources.reading.ReadingActivity
// import com.wonderslate.prepjoy.ui.videos.AudioPlayerService
// import com.wonderslate.prepjoy.ui.videos.CustomMediaPlayer
import com.ws.book_details.data.models.BookDetails
import com.ws.chapter_list.R
import com.ws.chapter_list.data.models.ActionType
import com.ws.chapter_list.data.models.ChapterResources
import com.ws.chapter_list.data.models.ChaptersListConfig
import com.ws.chapter_list.ui.CHAPTER_LIST_CONFIG
import com.ws.chapter_list.ui.ChaptersListFrag
import com.ws.chapter_list.ui.action_sheet.ResourceActionSheet
import com.ws.chapter_list.ui.action_sheet.UpgradeActionSheet
import com.ws.core_ui.base.ActBase
import com.ws.core_ui.extensions.finishWithAnim
import com.ws.core_ui.extensions.replaceFragment
import com.ws.core_ui.extensions.showToast
import com.ws.core_ui.extensions.startActivityWithAnim
import com.ws.core_ui.utils.ResTypes
import com.ws.database.room.entity.Resource
import com.ws.purchase.data.models.PaymentDetails
import com.ws.purchase.ui.fragment.PaymentFrag
import com.ws.resources.data.models.ReadingFragConfig
import com.ws.resources.ui.weblink.WeblinkAct

class ChaptersListAct : ActBase<ActivityChaptersListBinding>(true),
        ChaptersListFrag.OnChaptersListFragInteraction,
        ResourceActionSheet.OnResourceActionInteractionListener,
        UpgradeActionSheet.OnUpgradeInteractionListener{

    private lateinit var chapterConfig: ChaptersListConfig

    private var paymentFrag: PaymentFrag? = null
    override fun bindView(): ActivityChaptersListBinding {
        return ActivityChaptersListBinding.inflate(layoutInflater)
    }

    override fun init() {
        supportActionBar?.hide()
        chapterConfig = intent.getSerializableExtra(CHAPTER_LIST_CONFIG) as ChaptersListConfig
        replaceFragment(
                R.id.fragmentContainer,
                ChaptersListFrag.newInstance(chapterConfig)
        )
    }

    override fun onBackButtonClicked() {
        onBackPressed()
    }

    override fun onReadClicked(resource: Resource) {
        startActivityWithAnim(ReadingActivity.createIntent(this, ReadingFragConfig(resource.id,
            isPreviewMode = chapterConfig.isPreview, bookType = resource.bookType)))
    }

    override fun onGenericReadClicked(chapterList: List<ChapterResources>) {
        startActivityWithAnim(ReadingActivity.createIntentWithChapterList(
            this, ReadingFragConfig(
                chapterList[0].resources[0].id,
                isPreviewMode = chapterConfig.isPreview, bookType = chapterConfig.bookType
            ),
            chapterList = chapterList
        ))
        finish()
    }

    override fun onWebLinkClicked(resource: Resource) {
        startActivityWithAnim(WeblinkAct.createIntent(this, resource))
    }

    override fun onFlashCardClicked(resource: Resource) {
        startActivityWithAnim(WeblinkAct.createIntent(this, resource))
    }

    override fun onQuizAction(action: ActionType, resource: Resource, bookId: String?) {
        val intent = Intent(this, QuizInstructions::class.java)
        intent.putExtra("quizId", resource.id)
        intent.putExtra("isFromShop", true)
        intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
        if (null != bookId) {
            intent.putExtra("bookId", bookId)
        }
        when (action) {
            ActionType.PRACTICE -> {
                intent.putExtra("selectedFormat", "practice")
                startActivity(intent)
            }
            ActionType.PLAY_QUIZ -> {
                intent.putExtra("selectedFormat", "")
                startActivity(intent)
            }
            ActionType.TAKE_TEST -> {
                intent.putExtra("selectedFormat", "testSeries")
                startActivity(intent)
            }
            ActionType.HISTORY_QUIZ -> {
                val historyIntent = Intent(this, HistoryForQuiz::class.java)
                historyIntent.putExtra("quizId", resource.id)
                historyIntent.putExtra("resName", resource.resName)
                historyIntent.putExtra("quizType", "regular")
                historyIntent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
                historyIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
                startActivity(historyIntent)
            }

            else -> {
                showToast("Problem while opening quiz. Please try again")
                Log.e(TAG, "handleQuiz: ", IllegalArgumentException(ResTypes.UNDEFINED.value))
            }
        }
    }

    override fun upgradeEbook() {
        paymentFrag = PaymentFrag.newInstance(
            PaymentDetails(
                bookName = "",
                bookId = "216742",
                coverImage = "",
                author = "",
                amount = "50",
                organization = getString(com.wonderslate.prepjoy.R.string.app_name),
                logo = com.wonderslate.prepjoy.R.drawable.app_icon,
                description = "",
                themeColor = "#F05A2A",
                bookType = "upgrade"
            )
        )
        replaceFragment(
            com.wonderslate.prepjoy.R.id.fragmentContainer,
            paymentFrag!!
        )
    }

    override fun onVideoAction(action: ActionType, resource: Resource, resSheet: ResourceActionSheet) {
        when (action) {
            ActionType.WATCH -> {
                stopAudio()
                resSheet.dismiss()
                // CustomMediaPlayer functionality temporarily disabled for demo
                // val videoPlayerIntent = Intent(this, CustomMediaPlayer::class.java)
                val pResLink: String = if (resource.resLink.isNotEmpty() && (resource.resLink.contains("jwplayer") || resource.resLink.contains("jwplatform")))
                    resource.resLink
                else
                    "http://www.youtube.com/watch?v=" + resource.resLink
                // videoPlayerIntent.putExtra("resLink", pResLink)
                // videoPlayerIntent.putExtra("videoResName", resource.resName)
                // startActivity(videoPlayerIntent)
                showToast("Video player temporarily disabled for demo")

            }
            ActionType.LISTEN -> {
                // AudioPlayerService functionality temporarily disabled for demo
                if (false) { // if (AudioPlayerService.isAudioPlaying && (AudioPlayerService.resId == resource.id)) {
                    val intent = Intent()
                    intent.action = "stop_audio"
                    LocalBroadcastManager.getInstance(this).sendBroadcast(intent)
                    stopAudio()
                    return
                }
                stopAudio()
                showToast("Preparing Audio Channel. Please wait..")
                Wonderslate.getInstance().sharedPrefs.currentAudioTrack = resource.resLink
                // AudioPlayerService functionality temporarily disabled for demo
                // val openRelatedRefsIntent = Intent(this, AudioPlayerService::class.java)
                // openRelatedRefsIntent.putExtra("videoUrl", "http://www.youtube.com/watch?v=" + resource.resLink)
                // openRelatedRefsIntent.putExtra("shopView", false)
                // openRelatedRefsIntent.putExtra("intent", "videos")
                // openRelatedRefsIntent.putExtra("resLink", resource.resLink)
                // openRelatedRefsIntent.putExtra("audioTitle", resource.resName)
                // openRelatedRefsIntent.putExtra("hasPreview", chapterConfig.isPreview)
                // openRelatedRefsIntent.putExtra("bookId", chapterConfig.bookId)
                // openRelatedRefsIntent.putExtra("pResID", resource.id)
                // openRelatedRefsIntent.action = "Start_Service"
                // Util.startForegroundService(this, openRelatedRefsIntent)
                showToast("Audio player temporarily disabled for demo")

            }
            else -> {
                showToast("Problem while loading video. Please try again")
                Log.e(TAG, "handleQuiz: ", IllegalArgumentException(ResTypes.UNDEFINED.value))
            }
        }
    }

    private fun stopAudio() {
        // AudioPlayerService functionality temporarily disabled for demo
        if (false) { // if (AudioPlayerService.isAudioPlaying) {
            // val audioPlayerIntent = Intent(this, AudioPlayerService::class.java)
            // audioPlayerIntent.action = "Stop_Service"
            // Util.startForegroundService(this, audioPlayerIntent)
            Wonderslate.getInstance().sharedPrefs.currentAudioTrack = ""
        }
    }


    override fun onBookDetails(chapterListConfig: ChaptersListConfig) {
        if (chapterListConfig.isFromShop) {
            onBackPressed()
        } else {
            val bookDetails = BookDetails(
                    bookId = chapterListConfig.bookId,
                    bookName = chapterListConfig.bookName,
                    coverImage = chapterListConfig.bookCoverImage
            )
            startActivityWithAnim(BookDetailsAct.createIntent(this, bookDetails))
        }
    }

    override fun onStopAudioClick() {
        stopAudio()
    }

    override fun onBackPressed() {
        WonderPubSharedPrefs.getInstance(baseContext).isBookInLibrary = false
        if (chapterConfig.isFromNotification) {
            val login = Intent(this@ChaptersListAct, DashBoardActivity::class.java)
            startActivity(login)
            finish()
        } else
            finishWithAnim()
    }

    companion object {

        private const val TAG = "DemoChaptersListAct"

        @JvmStatic
        fun createIntent(context: Context, chaptersListConfig: ChaptersListConfig) = Intent(context, ChaptersListAct::class.java).also {
            it.putExtra(CHAPTER_LIST_CONFIG, chaptersListConfig)
            it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
        }
    }

    override fun onUpgradeAction(upgradePrice: String, chapterListConfig: ChaptersListConfig) {
        val bookDetails = BookDetails(
            bookId = chapterListConfig.bookId,
            bookName = chapterListConfig.bookName,
            coverImage = chapterListConfig.bookCoverImage,
            bookType = "upgrade",
            testsPrice = Wonderslate.getInstance().sharedPrefs.bookUpgradePrice
        )
        startActivityWithAnim(BookDetailsAct.createIntent(this, bookDetails))
    }
}