package com.wonderslate.prepjoy.ui.resources.annotations

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ws.commons.extensions.clearAndAddAll
import com.ws.core_ui.extensions.hideView
import com.ws.core_ui.extensions.newLayoutInflater
import com.ws.resources.data.models.AnnotationNote
import com.ws.resources.databinding.NotesListItemBinding

class NotesAdapter(
    private val notes: ArrayList<AnnotationNote>,
    private val isEnglishBook: Boolean = true,
): RecyclerView.Adapter<NotesAdapter.NoteViewHolder>() {

    fun update(notes: List<AnnotationNote>) {
        this.notes.clearAndAddAll(notes)
        notifyDataSetChanged()
    }

    inner class NoteViewHolder(val binding: NotesListItemBinding): RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): NoteViewHolder {
        return NoteViewHolder(NotesListItemBinding.inflate(parent.context.newLayoutInflater(), parent, false))
    }

    override fun onBindViewHolder(holder: NoteViewHolder, position: Int) {
        val note = notes[position]
        if (isEnglishBook) {
            holder.binding.notesTxt.text = "\"" + note.quote + "\""
        } else {
            holder.binding.notesTxt.hideView()
        }

        if (note.text.isNotEmpty() && !note.text.equals("null", true))
            holder.binding.annotationTxt.text = note.text
        else
            holder.binding.annotationTxt.hideView()
    }

    override fun getItemCount(): Int {
        return notes.size
    }
}