package com.wonderslate.prepjoy.ui.shoppingcart

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.google.firebase.analytics.FirebaseAnalytics
import com.razorpay.Checkout
import com.razorpay.PaymentResultListener
import com.wonderslate.data.helper.ConstantsHelper
import com.wonderslate.data.interfaces.WSCallback
import com.wonderslate.data.network.Wonderslate
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.data.repository.WSBuyBookOrChapters
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.Utils.Utils
import com.wonderslate.prepjoy.ui.dashboard.DashBoardActivity
import com.ws.commons.interfaces.FlavorConfig
import com.ws.commons.interfaces.UserDetailsProvider
import com.ws.core_ui.extensions.replaceFragment
import com.ws.core_ui.extensions.showToast
import com.ws.purchase.ui.fragment.PaymentFrag
import org.json.JSONObject
import org.koin.android.ext.android.inject

class ActShoppingCart : AppCompatActivity(), FragShoppingCart.OnFragShoppingCartListener,
    PaymentResultListener {

    private var shoppingCartId: String? = null
    private var totalPayableAmount: Double? = null

    private var fragment: FragShoppingCart? = null

    private val userDetailsProvider by inject<UserDetailsProvider>()
    private val flavorConfig by inject<FlavorConfig>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.act_shopping_cart)

        setupToolbar()
        fragment = FragShoppingCart.newInstance(FragShoppingCart.Config(showHeader = true))
        replaceFragment(R.id.fragmentContainer, fragment!!)
    }

    private fun setupToolbar() {
        supportActionBar?.hide()
        try {
            val window = this.window
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.navigationBarColor = ContextCompat.getColor(this, R.color.gradientColorEnd)
            getWindow().statusBarColor = ContextCompat.getColor(this, R.color.gradientColorEnd)
            val decorView = getWindow().decorView
            decorView.systemUiVisibility =
                decorView.systemUiVisibility and View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv() //set status text  light
        } catch (e: Exception) {
            Log.e(TAG, "setupToolbar: ", e)
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        overridePendingTransition(android.R.anim.slide_in_left, android.R.anim.slide_out_right)
    }

    override fun onUpdateCartCount(count: Int) {
        WonderPubSharedPrefs.getInstance(this).cartCount = count
    }

    override fun onShoppingCartBackClicked() {
        onBackPressed()
    }

    override fun onGotoShopClicked() {
        startShopScreen()
    }

    private fun startShopScreen() {
        val startLibraryIntent = Intent(this, DashBoardActivity::class.java)
        startLibraryIntent.addFlags(
            Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK or
                    Intent.FLAG_ACTIVITY_REORDER_TO_FRONT or Intent.FLAG_ACTIVITY_SINGLE_TOP
        )
        startLibraryIntent.putExtra("context", "navigate_to_shop")
        startActivity(startLibraryIntent)
        overridePendingTransition(android.R.anim.slide_in_left, android.R.anim.slide_out_right)
    }

    override fun startPayment(desc: String, totalPayableAmount: Double, cartId: String) {
        try {
            shoppingCartId = cartId
            this.totalPayableAmount = totalPayableAmount

            logPaymentStartEvent()

            val co = Checkout()
            try {
                co.setImage(R.mipmap.ic_launcher)
                val options = JSONObject().apply {
                    put("name", resources.getString(R.string.app_name))
                    put("description", desc)
                    put("currency", "INR")
                    val pDiscountPrice: Double = totalPayableAmount * 100
                    val bookPriceConverted = String.format("%.2f", pDiscountPrice)
                    put("amount", bookPriceConverted + "")
                }

                val prefill = JSONObject().apply {
                    put("name", userDetailsProvider.getUserName())
                    put("email", userDetailsProvider.getUserEmail())
                    put("contact", userDetailsProvider.getUserMobile())
                }
                val readOnly = JSONObject().apply {
                    put("email", "true")
                    put("contact", "true")
                }

                val theme = JSONObject().apply {
                    put("color", "#F05A2A")
                }

                val notes = JSONObject().apply {
                    put("username",userDetailsProvider.getSiteId() +"_"+ userDetailsProvider.getUserMobile())
                    put(ConstantsHelper.DATA_CART_ID, cartId)
                }

                options.put("prefill", prefill)
                options.put("theme", theme)
                //options.put("readonly", readOnly)
                options.put("notes", notes)

                co.setKeyID(flavorConfig.razorPayKeyId)

                co.open(this, options)
            } catch (e: Exception) {
                Utils.showErrorToast(this, -3)
                Log.e(TAG, "Error in payment process", e)
                onPaymentError(400, e.message)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun logPaymentStartEvent() {
        val bundle = Bundle()
        bundle.putString(FirebaseAnalytics.Param.ITEM_NAME, "Begin_Checkout")
        bundle.putString(FirebaseAnalytics.Param.CONTENT_TYPE, "buy_test")
        FirebaseAnalytics.getInstance(this).logEvent("Buy_Book_Checkout_Start", bundle)
    }

    private fun logPaymentSuccessEvent(paymentId: String?) {
        val bundle = Bundle()
        bundle.putString(FirebaseAnalytics.Param.ITEM_NAME, "PaymentId: $paymentId")
        bundle.putString(FirebaseAnalytics.Param.CONTENT_TYPE, "buy_chapters")
        FirebaseAnalytics.getInstance(this).logEvent("Unlock_Chapters_Completed", bundle)
    }

    override fun onPaymentSuccess(paymentId: String?) {
        Toast.makeText(applicationContext, "Payment success: $paymentId", Toast.LENGTH_SHORT).show()
        logPaymentSuccessEvent(paymentId)
        validatePayment(paymentId)
    }

    override fun onPaymentError(errorCode: Int, message: String?) {
        try {
            fragment?.hideLoader()
            val msg = if (message.equals(
                    "net::ERR_INTERNET_DISCONNECTED",
                    ignoreCase = true
                )
            ) getString(R.string.internet_connection_offline_text) else "Payment Failed"
            Toast.makeText(applicationContext, msg, Toast.LENGTH_SHORT).show()
            Log.d(TAG, "Payment Failure: $message code: $errorCode")
        } catch (e: Exception) {
            Log.e(TAG, "onPaymentError: ", e)
        }
    }

    private fun validatePayment(paymentId: String?) {
        Toast.makeText(applicationContext, "Validating payment. Please wait...", Toast.LENGTH_SHORT).show()
        val buyBookApi = WSBuyBookOrChapters()
        val bookType = "book"
        val currency = "INR"
        val paymentFrom = "mobile"
        val pDiscountPrice: Double = totalPayableAmount ?: (0.0 * 100)
        val bookPriceConverted = String.format("%.2f", pDiscountPrice)
        buyBookApi.buyBookOrChapters(
            shoppingCartId,
            "",
            "",
            bookPriceConverted,
            currency,
            paymentId,
            bookType,
            paymentFrom,
            "",
            object : WSCallback {
                override fun onWSResultSuccess(jsonObject: JSONObject, responseCode: Int) {
                    <EMAIL>("Purchase successful", Toast.LENGTH_LONG)
                    /*Wonderslate.getInstance().sharedPrefs.testSeriesPurchaseStatus =
                        paymentDetails.bookType.equals("upgrade", true) || paymentDetails.bookType.equals("testSeries", true)*/
                    fragment?.onPurchaseSuccess(paymentId, totalPayableAmount)
                }

                override fun onWSResultFailed(resString: String, responseCode: Int) {
                    <EMAIL>("Problem while validating purchase", Toast.LENGTH_LONG)
                    fragment?.hideLoader()
                }
            }
        )
    }

    companion object {
        private const val TAG = "ActShoppingCart"
    }
}