package com.wonderslate.prepjoy.ui.annotations

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.recyclerview.widget.LinearLayoutManager
import com.wonderslate.prepjoy.ui.resources.annotations.NotesAdapter
import com.ws.commons.Status
import com.ws.commons.extensions.isNullOrBlankOrContains
import com.ws.core_ui.base.BaseFragmentWithListener
import com.ws.core_ui.extensions.collectLatestWithLifecycle
import com.ws.core_ui.extensions.forEachJSONObject
import com.ws.core_ui.extensions.showToast
import com.ws.core_ui.extensions.visibility
import com.ws.core_ui.utils.TempConfig
import com.ws.resources.data.models.AnnotationNote
import com.ws.resources.data.models.AnnotationsRequest
import com.ws.resources.databinding.NotesAndHighlightsFragmentBinding
import com.ws.resources.ui.annotations.NotesAndHighlightsFragViewModel
import kotlinx.coroutines.flow.filter
import org.koin.android.ext.android.inject

class NotesAndHighlightsFrag : BaseFragmentWithListener<NotesAndHighlightsFragmentBinding, NotesAndHighlightsFrag.OnNotesAndHighlightsFragInteractionListener>() {

    companion object {

        const val ARG_ANNOTATION_REQUEST = "argAnnoReq"

        fun newInstance(annotationsRequest: AnnotationsRequest) = NotesAndHighlightsFrag().also {
            it.arguments = bundleOf(ARG_ANNOTATION_REQUEST to annotationsRequest)
        }
    }

    private val viewModel: NotesAndHighlightsFragViewModel by inject()

    private val notesAdapter: NotesAdapter by lazy {
        NotesAdapter(arrayListOf(), TempConfig.isEnglishBook)
    }

    private lateinit var request: AnnotationsRequest

    override fun inflateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): NotesAndHighlightsFragmentBinding {
        return NotesAndHighlightsFragmentBinding.inflate(inflater, container, false)
    }

    override fun initArguments(bundle: Bundle?) {
        request = bundle?.getSerializable(ARG_ANNOTATION_REQUEST) as AnnotationsRequest
    }

    override fun initView() {
        if (!TempConfig.isEnglishBook) {
            binding?.tvTitle?.text = "Notes"
        }
        binding?.btnBack?.setOnClickListener {
            listener?.onBackPressed()
        }

        binding?.recNotes?.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = notesAdapter
        }
    }

    override fun load() {
        viewModel.getAnnotations(request)
        viewModel.annotations
            .collectLatestWithLifecycle(this) { data ->
            when(data.responseType) {
                Status.LOADING -> {
                    binding?.lvCenter?.visibility = View.VISIBLE
                }

                Status.SUCCESSFUL -> {
                    val rows = data.data?.optJSONArray("rows")
                    val notes = arrayListOf<AnnotationNote>()
                    rows?.forEachJSONObject {
                        val text = it?.optString("text") ?: ""
                        if(showNote(text)) {
                            notes.add(
                                AnnotationNote(
                                    quote = it?.optString("quote") ?: "",
                                    text = text,
                                    id = it?.optString("id") ?: "",
                                    resId = request.resId
                                )
                            )
                        }
                    }

                    binding?.rlEmpty?.visibility(notes.isEmpty())

                    notesAdapter.update(notes)
                    binding?.lvCenter?.visibility = View.GONE
                }

                else -> {
                    showToast("Problem while getting annotations. Please try again")
                    listener?.onBackPressed()
                }
            }

        }
    }

    private fun showNote(text: String): Boolean {
        return TempConfig.isEnglishBook || (!TempConfig.isEnglishBook && !text.isNullOrBlankOrContains("null"))
    }

    interface OnNotesAndHighlightsFragInteractionListener {
        fun onBackPressed()
    }

}