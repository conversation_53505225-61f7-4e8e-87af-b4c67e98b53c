package com.wonderslate.prepjoy.ui.reading_material

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.LinearLayout
import android.widget.Switch
import androidx.viewpager.widget.ViewPager.OnPageChangeListener
import com.wang.avi.AVLoadingIndicatorView
import com.wonderslate.commons.Data
import com.wonderslate.commons.Status
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.domain.entities.ReadingMaterialData
import com.wonderslate.domain.entities.ResourceLogData
import com.wonderslate.prepjoy.BuildConfig
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.Utils.Flavours
import com.wonderslate.prepjoy.Utils.Utils
import com.wonderslate.prepjoy.Utils.Utils.disableScreenShot
import com.wonderslate.prepjoy.ui.BaseActivity
// import kotlinx.android.synthetic.main.activity_video.progressLoader
// import kotlinx.android.synthetic.main.header_language_change.rrltBack
// import kotlinx.android.synthetic.main.header_language_change.toggleLanguage
import org.json.JSONArray
import org.json.JSONObject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.text.DateFormat
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale


class ReadingMaterialActivity : BaseActivity(), ReadingMaterialFragment.DatePickerListener {
    private var viewPager: VerticalViewPager? = null
    private var context: Context? = null
    private var chooseDate = "23-09-2021"
    private var readMode : String? = null
    private var lastReadPage: Int = 0
    private lateinit var readingMaterial: JSONObject
    private lateinit var linearNoData : LinearLayout
    private lateinit var readingMaterialAdapter: ReadingMaterialAdapter
    private var viewedFrom = ""
    private var readingMaterialJArr = JSONArray()
    private val readingMaterialDataList: ArrayList<ReadingMaterialData> = arrayListOf()
    val readingMaterialObj: ArrayList<String> = arrayListOf()

    private val model by viewModel<ReadingMaterialViewModel>()
    private lateinit var progressLoader: AVLoadingIndicatorView
    private lateinit var rrltBack: View
    private lateinit var toggleLanguage: Switch

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        context = this
        viewPager = findViewById(R.id.viewPager)
        linearNoData = findViewById(R.id.linearNoData)
        progressLoader = findViewById(R.id.progressLoader)
        rrltBack = findViewById(R.id.rrltBack)
        toggleLanguage = findViewById(R.id.toggleLanguage)
        initObserver()
        intent.getStringExtra("inputDate")?.let {
                chooseDate = changeDateFormat(it)
            }

        intent.getStringExtra("viewFrom")?.let {
            viewedFrom = it
        }

        readMode = intent.getStringExtra("readMode").takeIf {
            intent.hasExtra("readMode")
        }
        if(readMode.equals("Weekly",true))
        {
            model.getWeeklyReadingMaterial(chooseDate)
        }
        else
            model.getReadingMaterial(chooseDate)

        if (supportActionBar != null) {
            supportActionBar!!.hide()
        }
        progressLoader.smoothToShow()
        val showToggle = resources.getBoolean(R.bool.show_language_toggle)
        if (showToggle) {
            languageChange()
        } else {
            toggleLanguage.visibility = View.INVISIBLE
        }
        rrltBack.setOnClickListener {
            onBackPressed()
        }
        disableScreenShot(this)

        model.logResourceOpen(
            ResourceLogData(
                "",
                BuildConfig.SITE_ID,
                "android",
                viewedFrom,
                "view"
            )
        )
    }


    override fun getLayoutResource(): Int {
        return R.layout.activity_reading_material
    }


    private fun readingMaterialResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
                progressLoader.smoothToHide()

            }

            Status.SUCCESSFUL -> {
                data.data?.let {
                    readingMaterial = it
                    loadReadingMaterialData()
                    progressLoader.smoothToHide()
                }
            }

            Status.HTTP_UNAVAILABLE -> {
                progressLoader.smoothToHide()
                Utils.showTopSnackBar(resources.getString(R.string.server_under_maintenance), this)
            }

            Status.ERROR -> {
                progressLoader.smoothToHide()
                Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), this)
            }
        }
    }

    private fun readingMaterialWeeklyResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
                progressLoader.smoothToHide()

            }

            Status.SUCCESSFUL -> {
                data.data?.let {
                    readingMaterial = it
                    loadReadingMaterialData()
                    progressLoader.smoothToHide()
                }
            }

            Status.HTTP_UNAVAILABLE -> {
                progressLoader.smoothToHide()
                Utils.showTopSnackBar(resources.getString(R.string.server_under_maintenance), this)
            }

            Status.ERROR -> {
                progressLoader.smoothToHide()
                Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), this)
            }
        }
    }

    private fun loadReadingMaterialData(): List<ReadingMaterialData> {
        val readingMaterialString = readingMaterial.getString("readingMaterials")

        readingMaterialJArr = JSONArray(readingMaterialString)

        prepareReadingMaterial()

        if (readingMaterialObj.size == 0) {
            linearNoData.visibility = View.VISIBLE
        } else {
            linearNoData.visibility = View.GONE
        }

        viewPager!!.adapter =
            ReadingMaterialAdapter(
                supportFragmentManager,
                readingMaterialObj, this, chooseDate
            )

        viewPager!!.setCurrentItem(lastReadPage)

        viewPager!!.addOnPageChangeListener(object : OnPageChangeListener {
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
            }

            override fun onPageSelected(position: Int) {
                lastReadPage = position
            }

            override fun onPageScrollStateChanged(state: Int) {
            }
        })
        return readingMaterialDataList
    }

    private fun prepareReadingMaterial() {
        for (index in 0 until readingMaterialJArr.length()) {
            val readingMatObj = readingMaterialJArr.getJSONObject(index)

            var o: JSONObject
            var language = resources.getString(R.string.english)
            if (readingMatObj.has("tag")) {
                o = readingMatObj.getJSONObject("tag")
                language = o.getString("language")
            }
            if (BuildConfig.FLAVOR.equals(Flavours.KARNATAKA.flavour, ignoreCase = true)) {
                if (language.equals("Kannada", ignoreCase = true) || language.isEmpty())
                {
                    readingMaterialObj.add(readingMatObj.toString())
                }
            }
            else
            {
                if (language.equals(WonderPubSharedPrefs.getInstance(this).sharedPrefsContentLanguagePref, ignoreCase = true) || language.isEmpty()) {
                    readingMaterialObj.add(readingMatObj.toString())
                }
            }
        }
    }

    private fun initObserver() {
        model.readingMaterial.observe(this, ::readingMaterialResponse)
        model.readingMaterialWeekly.observe(this, ::readingMaterialWeeklyResponse)
        model.resLogResponse.observe(this, ::logResourceResponse)
    }

    private fun logResourceResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
                // do nothing
            }
            Status.SUCCESSFUL -> {
                // do nothing
            }

            Status.HTTP_UNAVAILABLE -> {
                //do nothing
            }

            Status.ERROR -> {
                //do nothing
            }
        }
    }

    override fun selectedDate(date: String?) {
        date?.let {
            chooseDate = it
          //  model.getReadingMaterial(it)

            val intent = Intent(context, ReadingMaterialActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            intent.putExtra("inputDate", it) // "08-Nov-2021"
            startActivity(intent)
            finish()
        }
    }

    private fun languageChange() {
        toggleLanguage.isChecked =
            WonderPubSharedPrefs.getInstance(this).sharedPrefsContentLanguagePref.equals(
                resources.getString(R.string.hindi)
            )

        toggleLanguage.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                WonderPubSharedPrefs.getInstance(this).sharedPrefsContentLanguagePref =
                    resources.getString(R.string.hindi)
            } else {
                WonderPubSharedPrefs.getInstance(this).sharedPrefsContentLanguagePref =
                    resources.getString(R.string.english)
            }

            if (this::readingMaterial.isInitialized) {
                readingMaterialObj.clear()
                prepareReadingMaterial()
                viewPager!!.adapter!!.notifyDataSetChanged()
                viewPager!!.setCurrentItem(lastReadPage)
            }
        }
    }

    private fun changeDateFormat(dateString1: String): String {
        try {
            val df1: DateFormat = SimpleDateFormat("dd-MMM-yyyy", Locale.ENGLISH)
            var date1: Date? = null
            try {
                date1 = df1.parse(dateString1)
            } catch (e: ParseException) {
                e.printStackTrace()
            }
            val cal1: Calendar = Calendar.getInstance()
            cal1.time = date1


            val dateFormater = SimpleDateFormat("dd-MM-yyyy", Locale.ENGLISH)
            val formattedDate = dateFormater.format(cal1.time)


            return formattedDate
        } catch (e : Exception) {
            return dateString1
        }
    }
}