package com.wonderslate.prepjoy.ui.groupwall;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.text.SpannableString;
import android.text.style.ForegroundColorSpan;
import android.text.style.UnderlineSpan;
import android.util.Log;
import android.util.Patterns;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupMenu;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.airbnb.lottie.LottieAnimationView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.CustomTarget;
import com.bumptech.glide.request.transition.Transition;
import com.bumptech.glide.signature.ObjectKey;
import com.wonderslate.data.models.Comments;
import com.wonderslate.data.models.GroupPostsDataModel;
import com.wonderslate.data.network.WSAPIManager;
import com.wonderslate.data.preferences.WonderPubSharedPrefs;
import com.wonderslate.prepjoy.BuildConfig;
import com.wonderslate.prepjoy.R;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;

import butterknife.BindView;
import butterknife.ButterKnife;

public class GroupWallDetailsAdapter extends RecyclerView.Adapter<GroupWallDetailsAdapter.ViewHolder> {

    private List<GroupPostsDataModel> mData;
    private final LayoutInflater mInflater;
    private ItemClickListener mClickListener;
    private int clickCount = 0, commendClickCount = 0, likeClickCount = 0, menuCount = 2;
    private final GroupWallFragment context;
    private List<Comments> commentsList = new ArrayList<>();
    private boolean isAdminOfGroup = false;
    private ObjectKey userImageGlideKey;
    private String HTTP_IMAGE_FILENAME = "fileName";
    private final String HTTP_OBJECT_ID = "id";
    private Activity mContext;
    private Context contextW;

    public GroupWallDetailsAdapter(GroupWallFragment contexts, Activity activity, Context context) {
        this.context = contexts;
        this.mContext = activity;
        this.contextW = context;
        this.mInflater = LayoutInflater.from(mContext);
    }

    public void setEntries(List<GroupPostsDataModel> list, boolean isAdmin) {
        this.mData = list;
        this.isAdminOfGroup = isAdmin;
        notifyDataSetChanged();
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = mInflater.inflate(R.layout.item_study_group_inside_details, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        try {
            //viewHolder = holder;
            GroupPostsDataModel data = mData.get(position);
            if (data.getDescription() == null || data.getDescription().equalsIgnoreCase("null") || data.getDescription().isEmpty()) {
                holder.textViewGroupDetailsItem.setVisibility(View.GONE);
                holder.textViewGroupUrlDetailItem.setVisibility(View.GONE);
            } else {
                holder.textViewGroupDetailsItem.setVisibility(View.VISIBLE);
                /*if (data.getDescription().contains("https://") || data.getDescription().contains("http://"))
                {
                    try
                    {
                        String resUrl = null;
                        String description =  null;
                        Matcher webMatcher = Patterns.WEB_URL.matcher(data.getDescription());
                        while (webMatcher.find()){
                            resUrl = webMatcher.group();
                        }
                        if(resUrl!=null) {
                            holder.textViewGroupUrlDetailItem.setVisibility(View.VISIBLE);
                            holder.textViewGroupUrlDetailItem.setText(resUrl);
                            description = data.getDescription().replace(resUrl, "");
                        }

                        if(description!=null)
                            holder.textViewGroupDetailsItem.setText(description);
                    }
                    catch (Exception e)
                    {
                        e.printStackTrace();
                    }
                }
                else
                {
                    holder.textViewGroupDetailsItem.setText(data.getDescription());
                }*/
                holder.textViewGroupDetailsItem.setText(data.getDescription());
            }

            if (data.getUserType().equalsIgnoreCase("admin")) {
                holder.layoutPostAuthor.setVisibility(View.GONE);
                holder.textviewUser.setText("Admin");
            } else {
                holder.textviewUser.setText(data.getName());
            }

            String userRole = WonderPubSharedPrefs.getInstance(contextW).getUserType();
            String currentUserLogin = BuildConfig.SITE_ID + "_" + WonderPubSharedPrefs.getInstance(contextW).getUsermobile();

            if (userRole.equalsIgnoreCase("admin") || currentUserLogin.equalsIgnoreCase(String.valueOf(mData.get(position).getUsername()))) {
                holder.imageViewMenu.setVisibility(View.VISIBLE);
            } else {
                holder.imageViewMenu.setVisibility(View.GONE);
            }

            holder.textviewDateTime.setText("" + data.getDateCreated());
            if (data.getPostLikesCount() != 0) {
                holder.textViewLikeCount.setVisibility(View.GONE);
                holder.textViewLikeCount.setText("" + data.getPostLikesCount());
                holder.textViewLikeCountDate.setText("" + data.getPostLikesCount());
            } else {
                holder.textViewLikeCount.setVisibility(View.GONE);
                holder.textViewLikeCountDate.setText("No Likes");
            }

            if (data.getLikedPost().equalsIgnoreCase("true")) {
                //holder.imageViewLike.setColorFilter(ContextCompat.getColor(contextW, R.color.light_gray), PorterDuff.Mode.MULTIPLY);
                holder.imageViewLike.setImageResource(R.drawable.ic_like_new_linked);
                holder.textViewGroupPublicPrivate.setVisibility(View.VISIBLE);
            } else {
                //holder.imageViewLike.setColorFilter(ContextCompat.getColor(contextW, R.color.light_gray), PorterDuff.Mode.MULTIPLY);
                holder.imageViewLike.setImageResource(R.drawable.ic_like_new);
                holder.textViewGroupPublicPrivate.setVisibility(View.VISIBLE);
                holder.textViewGroupPublicPrivate.setText(" Like");
            }
            holder.textViewCommendCount.setText("" + data.getCommentsCount());

            if (userImageGlideKey == null)
                userImageGlideKey = new ObjectKey(System.currentTimeMillis());

            if (data.getProfilepic() != null && !data.getProfilepic().isEmpty() && !data.getProfilepic().equalsIgnoreCase("null")) {
                String image = WSAPIManager.URL_SELECTED_USER_IMAGE_API + "&" + HTTP_IMAGE_FILENAME + "="
                        + data.getProfilepic() + "&" + HTTP_OBJECT_ID + "=" + data.getUserId();
                Glide.with(mContext)
                        .load(image)
                        .signature(userImageGlideKey)
                        .placeholder(R.drawable.app_icon)
                        .into(holder.imageViewAvatar);
            } else if (data.getProfilepic() == null || data.getProfilepic().equalsIgnoreCase("null")) {
                String image = WSAPIManager.URL_SELECTED_USER_IMAGE_API + "&" + HTTP_IMAGE_FILENAME + "="
                        + "profileImage.jpg" + "&" + HTTP_OBJECT_ID + "=" + data.getUserId();
                Glide.with(mContext)
                        .load(image)
                        .signature(userImageGlideKey)
                        .placeholder(R.drawable.app_icon)
                        .into(holder.imageViewAvatar);
            } else {
                Glide.with(mContext)
                        .load(R.drawable.app_icon)
                        .signature(userImageGlideKey)
                        .into(holder.imageViewAvatar);
            }

            if (data.getPostImage() != null && !data.getPostImage().equals("null")) {
                holder.imageViewPostContent.setVisibility(View.VISIBLE);
                holder.postImagecard.setVisibility(View.VISIBLE);
                holder.imageViewFile.setVisibility(View.GONE);
                holder.textViewFileName.setVisibility(View.GONE);
                String imagePost = WSAPIManager.SERVICE3 + "groups/showGroupPostImage?id=" + data.getId() + "&fileName=" + data.getPostImage();
                Glide.with(mContext)
                        .asBitmap()
                        .load(imagePost)
                        .placeholder(R.drawable.ic_people_bg_green)
                        .into(new CustomTarget<Bitmap>(){
                            @Override
                            public void onResourceReady(@NonNull Bitmap resource, @Nullable Transition<? super Bitmap> transition) {
                                holder.imageViewPostContent.setImageBitmap(resource);
                                context.hideProgressLoader();
                            }

                            @Override
                            public void onLoadCleared(@Nullable Drawable placeholder) {

                            }
                        });

            } else {
                holder.imageViewPostContent.setVisibility(View.GONE);
                holder.postImagecard.setVisibility(View.GONE);
            }

            if (data.getFileName() != null && !data.getFileName().equals("null")) {
                holder.imageViewFile.setVisibility(View.VISIBLE);
                holder.textViewFileName.setVisibility(View.VISIBLE);
                holder.textViewFileName.setText("" + data.getFileName());
                //holder.textViewFileName.setText("" + "attachment_file.pdf");
                holder.imageViewPostContent.setVisibility(View.GONE);
                holder.postImagecard.setVisibility(View.GONE);
            } else {
                holder.imageViewFile.setVisibility(View.GONE);
                holder.textViewFileName.setVisibility(View.GONE);
            }

            //For Commends and Replays
            holder.recyclerViewCommend.setVisibility(View.GONE);
            if (data.getComments().size() != 0 && commentsList != null && commentsList.size() == 0) {
                holder.textViewShowCommentsDats.setText("" + data.getCommentsCount() + " comments");
                holder.relativeLayoutlikecomment.setVisibility(View.VISIBLE);
                Comments commentsData = mData.get(position).getComments().get(0);
                holder.textViewcommentsDesc.setText(commentsData.getDescription());
                holder.textviewUsername.setText(commentsData.getName());
                holder.textViewCommentDateTime.setText("" + commentsData.getDateCreated());

                if (userImageGlideKey == null)
                    userImageGlideKey = new ObjectKey(System.currentTimeMillis());
                String image = WSAPIManager.URL_SELECTED_USER_IMAGE_API + "&" + HTTP_IMAGE_FILENAME + "="
                        + commentsData.getProfilepic() + "&" + HTTP_OBJECT_ID + "=" + commentsData.getUserId();
                Glide.with(mContext)
                        .load(image)
                        .placeholder(R.drawable.ic_sample_member)
                        .error(R.drawable.ic_sample_member)
                        .transition(DrawableTransitionOptions.withCrossFade())
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                        .signature(userImageGlideKey)
                        .into(holder.imageAvatar);
                userImageGlideKey = null;
            } else if (commentsList != null && commentsList.size() != 0) {
                holder.textViewShowCommentsDats.setText("" + commentsList.size() + " comments");

                holder.relativeLayoutlikecomment.setVisibility(View.VISIBLE);
                Comments commentsData = commentsList.get(0);
                holder.textViewcommentsDesc.setText(commentsData.getDescription());
                holder.textviewUsername.setText(commentsData.getName());
                holder.textViewCommentDateTime.setText("" + commentsData.getDateCreated());

                if (userImageGlideKey == null)
                    userImageGlideKey = new ObjectKey(System.currentTimeMillis());
                String image = WSAPIManager.URL_SELECTED_USER_IMAGE_API + "&" + HTTP_IMAGE_FILENAME + "="
                        + commentsData.getProfilepic() + "&" + HTTP_OBJECT_ID + "=" + commentsData.getUserId();
                Glide.with(mContext)
                        .load(image)
                        .placeholder(R.drawable.ic_sample_member)
                        .error(R.drawable.ic_sample_member)
                        .transition(DrawableTransitionOptions.withCrossFade())
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                        .signature(userImageGlideKey)
                        .into(holder.imageAvatar);
                userImageGlideKey = null;

            } else {
                holder.textViewShowComments.setVisibility(View.VISIBLE);
                holder.textViewShowCommentsDats.setText("No comments");
                holder.relativeLayoutlikecomment.setVisibility(View.GONE);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public int getItemCount() {
        return mData.size();
    }

    @Override
    public int getItemViewType(int position) {
        return position;
    }


    public class ViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {
        @BindView(R.id.imageView_avatar)
        ImageView imageViewAvatar;
        @BindView(R.id.relativeLayout_avatar)
        RelativeLayout relativeLayoutAvatar;
        @BindView(R.id.textview_user)
        TextView textviewUser;
        @BindView(R.id.textview_date_time)
        TextView textviewDateTime;
        @BindView(R.id.layout_user)
        LinearLayout layoutUser;
        @BindView(R.id.textView_group_details_item)
        TextView textViewGroupDetailsItem;

        @BindView(R.id.textView_group_url_details_item)
        TextView textViewGroupUrlDetailItem;
        @BindView(R.id.imageView_post_content)
        ImageView imageViewPostContent;
        @BindView(R.id.post_image_card)
        CardView postImagecard;
        @BindView(R.id.textView_like_count)
        TextView textViewLikeCount;
        @BindView(R.id.imageView_like)
        ImageView imageViewLike;
        @BindView(R.id.textView_group_public_private)
        TextView textViewGroupPublicPrivate;
        @BindView(R.id.textView_commend_count)
        TextView textViewCommendCount;
        @BindView(R.id.imageView_share_item)
        ImageView imageViewShare;
        @BindView(R.id.imageView_commend)
        ImageView imageViewCommend;
        @BindView(R.id.layout_bottom_footer)
        RelativeLayout layoutBottomFooter;
        @BindView(R.id.editText_comment)
        EditText editTextComment;
        @BindView(R.id.recyclerView_groups_comments_and_replay)
        RecyclerView recyclerViewCommend;
        @BindView(R.id.animationViewLike)
        LottieAnimationView lottieAnimationView;
        @BindView(R.id.imageView_report_menu)
        ImageView imageViewMenu;
        @BindView(R.id.imageView_send_comment)
        ImageView imageViewSendComment;
        @BindView(R.id.textView_show_comments)
        TextView textViewShowComments;
        @BindView(R.id.imageView_post_file)
        ImageView imageViewFile;
        @BindView(R.id.textView_file_name)
        TextView textViewFileName;
        @BindView(R.id.layout_comment)
        RelativeLayout layoutComment;
        @BindView(R.id.layout_post_author)
        LinearLayout layoutPostAuthor;
        @BindView(R.id.imageView_like_date)
        ImageView imageViewLikeData;
        @BindView(R.id.textView_like_count_date)
        TextView textViewLikeCountDate;
        @BindView(R.id.textView_show_comments_date)
        TextView textViewShowCommentsDats;

        @BindView(R.id.layout_like_comment)
        RelativeLayout relativeLayoutlikecomment;

        @BindView(R.id.imageView_comment_avatar)
        ImageView imageAvatar;

        @BindView(R.id.textview_comment_user)
        TextView textviewUsername;

        @BindView(R.id.textview_comment_date_time)
        TextView textViewCommentDateTime;

        @BindView(R.id.textView_commend_text)
        TextView textViewcommentsDesc;


        ViewHolder(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
            recyclerViewCommend.setLayoutManager(new LinearLayoutManager(mContext));
            recyclerViewCommend.setHasFixedSize(true);
            imageViewCommend.setOnClickListener(this);
            imageViewShare.setOnClickListener(this);
            imageViewLike.setOnClickListener(this);
            imageViewMenu.setOnClickListener(this);
            imageViewSendComment.setOnClickListener(this);
            textViewShowComments.setOnClickListener(this);
            textViewFileName.setOnClickListener(this);
            textViewGroupDetailsItem.setOnClickListener(this);
            textViewGroupUrlDetailItem.setOnClickListener(this);
            imageViewPostContent.setOnClickListener(this);
            relativeLayoutlikecomment.setOnClickListener(this);
            itemView.setOnClickListener(this);
        }

        @Override
        public void onClick(View view) {
            try {
                switch (view.getId()) {
                    case R.id.imageView_commend:
                        context.openCommenAndReplayView(mData.get(getAdapterPosition()), getAdapterPosition());
                        break;

                    case R.id.layout_like_comment:
                        context.openCommenAndReplayView(mData.get(getAdapterPosition()), getAdapterPosition());
                        break;
                    case R.id.imageView_send_comment:
                        /*String comment = editTextComment.getText().toString().trim();
                        if (!comment.isEmpty()) {
                            context.commentPost(mData.get(getAdapterPosition()).getId(), comment, getAdapterPosition());
                        } else {
                            editTextComment.setHint("Please enter comment");
                            editTextComment.setHintTextColor(ContextCompat.getColor(mContext, R.color.primary_bg_red));
                        }*/
                        break;
                    case R.id.imageView_like:
                        if (likeClickCount == 0) {
                            likeClickCount = 1;
                            lottieAnimationView.setVisibility(View.VISIBLE);
                            imageViewLike.setColorFilter(ContextCompat.getColor(mContext, R.color.light_gray), PorterDuff.Mode.MULTIPLY);
                            textViewGroupPublicPrivate.setVisibility(View.VISIBLE);
                            context.likePost(mData.get(getAdapterPosition()).getId(), getAdapterPosition());
                            new Handler().postDelayed(() -> {
                                lottieAnimationView.setVisibility(View.GONE);
                            }, 2000);
                            break;
                        } else {
                            likeClickCount = 0;
                            textViewGroupPublicPrivate.setVisibility(View.INVISIBLE);
                            imageViewLike.setColorFilter(ContextCompat.getColor(mContext, R.color.light_gray), PorterDuff.Mode.MULTIPLY);
                            context.dislikePost(mData.get(getAdapterPosition()).getId(), getAdapterPosition());
                        }
                    case R.id.imageView_share_item:
                        context.sharePost();
                        break;
                    case R.id.textView_show_comments:
                        /*if (commendClickCount == 0) {
                            commendClickCount = 1;
                            recyclerViewCommend.setVisibility(View.VISIBLE);
                            textViewShowComments.setText("Hide Comments");
                        } else {
                            commendClickCount = 0;
                            recyclerViewCommend.setVisibility(View.GONE);
                            textViewShowComments.setText("View all " + mData.get(getAdapterPosition()).getCommentsCount() + " comments");
                        }*/
                        context.openCommenAndReplayView(mData.get(getAdapterPosition()), getAdapterPosition());
                        break;
                    case R.id.textView_file_name:
                        context.downloadPostFile(String.valueOf(mData.get(getAdapterPosition()).getId()), mData.get(getAdapterPosition()).getFileName(), mData.get(getAdapterPosition()).getFilePath());
                        break;
                    case R.id.textView_group_details_item:
                        if (mData.get(getAdapterPosition()).getDescription().contains("https://") || mData.get(getAdapterPosition()).getDescription().contains("http://")) {
                            context.openResource(mData.get(getAdapterPosition()).getDescription(), mData.get(getAdapterPosition()).getProfilepic(), mData.get(getAdapterPosition()).getName(), String.valueOf(mData.get(getAdapterPosition()).getUserId()));
                        }
                        break;

                    case R.id.textView_group_url_details_item:
                        if (mData.get(getAdapterPosition()).getDescription().contains("https://") || mData.get(getAdapterPosition()).getDescription().contains("http://")) {
                            context.openResource(mData.get(getAdapterPosition()).getDescription(), mData.get(getAdapterPosition()).getProfilepic(), mData.get(getAdapterPosition()).getName(), String.valueOf(mData.get(getAdapterPosition()).getUserId()));
                        }
                        break;


                    case R.id.imageView_post_content:
                        if (mData.get(getAdapterPosition()).getPostImage() != null && !mData.get(getAdapterPosition()).getPostImage().equals("null")) {
                            String imageFile = WSAPIManager.SERVICE3 + "groups/showGroupPostImage?id=" + mData.get(getAdapterPosition()).getId() + "&fileName=" + mData.get(getAdapterPosition()).getPostImage();
                            context.showFullImageView(imageFile);
                        }
                        break;
                    case R.id.imageView_report_menu:
                        try {
                            PopupMenu popup = new PopupMenu(mContext, imageViewMenu);
                            String userRole = WonderPubSharedPrefs.getInstance(mContext).getUserType();
                            boolean isFromMyGroupPost = mData.get(getAdapterPosition()).getIsMyGroup();
                            String currentUser = WonderPubSharedPrefs.getInstance(mContext).getUserId();
                            String currentUserLogin = BuildConfig.SITE_ID + "_" + WonderPubSharedPrefs.getInstance(contextW).getUsermobile();
                            menuCount = 1;
                            String[] menuTitle = new String[]{"Delete Post"};
                            if (userRole.equalsIgnoreCase("admin") && currentUserLogin.equalsIgnoreCase(String.valueOf(mData.get(getAdapterPosition()).getUsername()))) {
                                popup.inflate(R.menu.report_group_menu_admin);
                                menuCount = 2;
                                menuTitle = new String[]{"Delete Post", "Edit Post"};
                            } else if (userRole.equalsIgnoreCase("admin")) {
                                popup.inflate(R.menu.report_group_menu_admin_group);
                                menuCount = 1;
                                menuTitle = new String[]{"Delete Post"};
                            } else if (currentUserLogin.equalsIgnoreCase(String.valueOf(mData.get(getAdapterPosition()).getUsername()))) {
                                popup.inflate(R.menu.report_group_menu_admin);
                                menuCount = 2;
                                menuTitle = new String[]{"Delete Post", "Edit Post"};
                            } else if (isAdminOfGroup) {
                                popup.inflate(R.menu.report_group_menu_admin_group);
                                menuCount = 1;
                                menuTitle = new String[]{"Delete Post"};
                            } /*else {
                                popup.inflate(R.menu.report_group_menu_admin_group);
                                menuCount = 1;
                                menuTitle = new String[]{"Delete Post"};
                            }*/ /*else if (isAdminOfGroup) {
                                popup.inflate(R.menu.report_group_menu_admin_group);
                                menuCount = 1;
                                menuTitle = new String[]{"Delete Post"};
                            } else {
                                popup.inflate(R.menu.report_group_menu);
                                menuCount = 1;
                                menuTitle = new String[]{"Delete Post"};
                            }*/

                            for (int i = 0; i < menuCount; i++) {
                                int positionOfMenuItem = i;
                                MenuItem item = popup.getMenu().getItem(positionOfMenuItem);
                                SpannableString s = new SpannableString(menuTitle[i]);
                                s.setSpan(new ForegroundColorSpan(Color.RED), 0, s.length(), 0);
                                item.setTitle(s);
                            }
                            popup.setOnMenuItemClickListener(new PopupMenu.OnMenuItemClickListener() {
                                @Override
                                public boolean onMenuItemClick(MenuItem item) {
                                    switch (item.getItemId()) {
                                        case R.id.action_report_post:
                                            context.reportPost(mData.get(getAdapterPosition()).getId());
                                            return true;
                                        case R.id.action_report_user:
                                            context.reportUser(mData.get(getAdapterPosition()).getId(), mData.get(getAdapterPosition()).getUserId(), mData.get(getAdapterPosition()).getUsername());
                                            return true;
                                        case R.id.action_delete_post:
                                            context.deletePost(mData.get(getAdapterPosition()).getId(), getAdapterPosition());
                                            return true;
                                        case R.id.action_edit_post:
                                            context.editPost(mData.get(getAdapterPosition()));
                                            return true;
                                        default:
                                            return false;
                                    }
                                }
                            });
                            popup.show();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        break;
                    default:
                }
                if (mClickListener != null) mClickListener.onItemClick(view, getAdapterPosition());
                //fragment.onItemClick(getAdapterPosition());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /*String getItem(int id) {
        return mData.get(id);
    }*/

    void setClickListener(ItemClickListener itemClickListener) {
        this.mClickListener = itemClickListener;
    }

    public interface ItemClickListener {
        void onItemClick(View view, int position);
    }

    public void updateCommentsList(List<Comments> commendList, int position) {
        try {
            if (commendList.size() != 0) {
                commentsList = commendList;
                clickCount = 1;
                notifyItemChanged(position);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
