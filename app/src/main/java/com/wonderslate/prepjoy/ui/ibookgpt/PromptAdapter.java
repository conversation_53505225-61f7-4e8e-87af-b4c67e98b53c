package com.wonderslate.prepjoy.ui.ibookgpt;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.skydoves.powerspinner.OnSpinnerItemSelectedListener;
import com.skydoves.powerspinner.PowerSpinnerInterface;
import com.skydoves.powerspinner.PowerSpinnerView;
import com.wonderslate.data.models.GPTPrompts;
import com.wonderslate.data.network.Wonderslate;
import com.wonderslate.prepjoy.R;

import java.util.List;
import java.util.Random;

public class PromptAdapter extends RecyclerView.Adapter<PromptAdapter.PromptViewHolder> implements PowerSpinnerInterface<List<GPTPrompts>> {

    private Context context;
    private List<GPTPrompts> prompts;
    private PowerSpinnerView powerSpinnerView;
    private int index;
    private OnSpinnerItemSelectedListener onSpinnerItemSelectedListener;

    @Override
    public int getIndex() {
        return 0;
    }

    @Override
    public void setIndex(int i) {
        index = i;
    }

    @NonNull
    @Override
    public PowerSpinnerView getSpinnerView() {
        return powerSpinnerView;
    }

    @Nullable
    @Override
    public OnSpinnerItemSelectedListener<List<GPTPrompts>> getOnSpinnerItemSelectedListener() {
        return null;
    }

    @Override
    public void setOnSpinnerItemSelectedListener(@Nullable OnSpinnerItemSelectedListener<List<GPTPrompts>> onSpinnerItemSelectedListener) {
        this.onSpinnerItemSelectedListener = onSpinnerItemSelectedListener;

    }

    @Override
    public void notifyItemSelected(int i) {
        int oldIndex;
        oldIndex = index;
        index = i;
        //powerSpinnerView.notifyItemSelected(index, prompts.get(index));
        this.onSpinnerItemSelectedListener.onItemSelected(oldIndex, prompts,
                index, prompts);

    }

    @Override
    public void setItems(@NonNull List<? extends List<GPTPrompts>> list) {

    }

    public PromptAdapter(Context context, List<GPTPrompts> prompts, PowerSpinnerView powerSpinnerView) {
        this.context = context;
        this.prompts = prompts;
        this.powerSpinnerView = powerSpinnerView;
    }

    @NonNull
    @Override
    public PromptViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.gpt_fab_menu_item, parent, false);
        return new PromptViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull PromptViewHolder holder, int position) {
        GPTPrompts prompt = prompts.get(position);
        holder.labelPrompt.setText(prompt.getPromptLabel().replace("&amp;", "&"));
        String iconPath = Wonderslate.SERVICE + "prompt/showPromptIcon?promptId=" + prompt.getIconId() + "&fileName=" + prompt.getIconPath();
        if (prompt.getPromptType().equalsIgnoreCase("test") || prompt.getPromptType().equalsIgnoreCase("doubts")
                || prompt.getPromptType().equalsIgnoreCase("highlights")) {
            holder.iconPrompt.setImageResource(prompt.getLocalIconPath());
        }
        else {
            Glide.with(context)
                    .load(iconPath)
                    .error(R.drawable.give_test)
                    .placeholder(R.drawable.give_test)
                    .into(holder.iconPrompt);
        }

        // Generate a random color and apply it as the tint
        int randomColor = generateRandomColor();
        holder.iconPrompt.setColorFilter(randomColor, PorterDuff.Mode.SRC_IN); // Set the tint with the random color

        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                notifyItemSelected(holder.getAdapterPosition());
            }
        });
    }

    private int generateRandomColor() {
        Random random = new Random();
        int color;

        do {
            int red = random.nextInt(256);
            int green = random.nextInt(256);
            int blue = random.nextInt(256);

            // Combine the RGB values into a single color
            color = Color.rgb(red, green, blue);
        } while (color == Color.BLACK || color == Color.WHITE); // Avoid black and white

        return color;
    }


    @Override
    public int getItemCount() {
        return prompts.size();
    }

    public static class PromptViewHolder extends RecyclerView.ViewHolder {
        ImageView iconPrompt;
        TextView labelPrompt;
        FrameLayout promptItem;

        public PromptViewHolder(@NonNull View itemView) {
            super(itemView);
            iconPrompt = itemView.findViewById(R.id.iconPrompt);
            labelPrompt = itemView.findViewById(R.id.labelPrompt);
            promptItem = itemView.findViewById(R.id.promptItem);
        }
    }
}
