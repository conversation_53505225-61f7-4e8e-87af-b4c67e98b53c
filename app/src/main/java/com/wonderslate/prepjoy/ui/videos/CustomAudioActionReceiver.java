package com.wonderslate.prepjoy.ui.videos;

import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import androidx.core.app.NotificationCompat;
import androidx.media3.common.PlaybackParameters;
import androidx.media3.common.Player;
import androidx.media3.ui.PlayerNotificationManager;

import com.wonderslate.data.network.Wonderslate;
import com.wonderslate.prepjoy.R;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CustomAudioActionReceiver implements PlayerNotificationManager.CustomActionReceiver {
    static String[] audioPlayBackSpeedList;
    static Context mContext;

    @Override
    public Map<String, NotificationCompat.Action> createCustomActions(Context context, int instanceId) {
        mContext = context;
        Intent intent = new Intent("audio_slow").setPackage(context.getPackageName());
        PendingIntent pendingIntent = PendingIntent.getBroadcast(
                context, instanceId, intent, PendingIntent.FLAG_CANCEL_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        NotificationCompat.Action action1 = new NotificationCompat.Action(
                R.drawable.ic_audio_slow,
                "audio_slow",
                pendingIntent
        );

        Intent intent2 = new Intent("audio_fast").setPackage(context.getPackageName());
        PendingIntent pendingIntent2 = PendingIntent.getBroadcast(
                context, instanceId, intent2, PendingIntent.FLAG_CANCEL_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        NotificationCompat.Action action2 = new NotificationCompat.Action(
                R.drawable.ic_audio_fast,
                "audio_fast",
                pendingIntent2
        );
        Map<String, NotificationCompat.Action> actionMap = new HashMap<>();
        actionMap.put("audio_slow", action1);
        actionMap.put("audio_fast", action2);
        return actionMap;
    }

    @Override
    public List<String> getCustomActions(Player player) {
        List<String> customActions = new ArrayList<>();
        customActions.add("audio_slow");
        customActions.add("audio_fast");
        Log.e("getCustomActions", "action: " + player.getCurrentWindowIndex());
        return customActions;
    }

    @Override
    public void onCustomAction(Player player, String action, Intent intent) {
        audioPlayBackSpeedList = mContext.getResources().getStringArray(R.array.video_playback_speed);
        Log.e("onCustomAction", "action: " + intent.getAction() + action);
        if (action.equalsIgnoreCase("audio_slow")) {
            String currentAudioSpeed = Wonderslate.getInstance().getSharedPrefs().getCurrentAudioSpeed();
            int currentAudioSpeedIndex = Arrays.asList(audioPlayBackSpeedList).indexOf(currentAudioSpeed);
            int normalSpeedIndex = 3; //Index Of NORMAL Speed IS 3
            if (!currentAudioSpeed.isEmpty()) {
                currentAudioSpeedIndex = currentAudioSpeedIndex - 1;
                if (!(currentAudioSpeedIndex < 0)) {
                    if (audioPlayBackSpeedList[currentAudioSpeedIndex].equalsIgnoreCase("NORMAL")) {
                        PlaybackParameters playbackParameters = new PlaybackParameters(1f);
                        player.setPlaybackParameters(playbackParameters);
                    } else {
                        PlaybackParameters playbackParameters = new PlaybackParameters(Float.parseFloat(audioPlayBackSpeedList[currentAudioSpeedIndex]));
                        player.setPlaybackParameters(playbackParameters);
                    }
                }
            } else {
                PlaybackParameters playbackParameters = new PlaybackParameters(1f);
                player.setPlaybackParameters(playbackParameters);
            }
            if (!(currentAudioSpeedIndex < 0)) {
                Wonderslate.getInstance().getSharedPrefs().setCurrentAudioSpeed(audioPlayBackSpeedList[currentAudioSpeedIndex]);
            }
        } else if (action.equalsIgnoreCase("audio_fast")) {
            String currentAudioSpeed = Wonderslate.getInstance().getSharedPrefs().getCurrentAudioSpeed();
            int currentAudioSpeedIndex = Arrays.asList(audioPlayBackSpeedList).indexOf(currentAudioSpeed);
            int normalSpeedIndex = 3; //Index Of NORMAL Speed IS 3
            if (!currentAudioSpeed.isEmpty()) {
                currentAudioSpeedIndex = currentAudioSpeedIndex + 1;
                if (!(currentAudioSpeedIndex == audioPlayBackSpeedList.length)) {
                    if (audioPlayBackSpeedList[currentAudioSpeedIndex].equalsIgnoreCase("NORMAL")) {
                        PlaybackParameters playbackParameters = new PlaybackParameters(1f);
                        player.setPlaybackParameters(playbackParameters);
                    } else {
                        PlaybackParameters playbackParameters = new PlaybackParameters(Float.parseFloat(audioPlayBackSpeedList[currentAudioSpeedIndex]));
                        player.setPlaybackParameters(playbackParameters);
                    }
                }
            } else {
                PlaybackParameters playbackParameters = new PlaybackParameters(1f);
                player.setPlaybackParameters(playbackParameters);
            }
            if (!(currentAudioSpeedIndex == audioPlayBackSpeedList.length)) {
                Wonderslate.getInstance().getSharedPrefs().setCurrentAudioSpeed(audioPlayBackSpeedList[currentAudioSpeedIndex]);
            }
        }
    }
}
