package com.wonderslate.prepjoy.ui.ibookgpt;

import android.os.Bundle;
import android.text.Html;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.wonderslate.data.interfaces.WSCallback;
import com.wonderslate.data.interfaces.WSResponseCallback;
import com.wonderslate.data.models.RechargeOption;
import com.wonderslate.data.preferences.WonderPubSharedPrefs;
import com.wonderslate.data.repository.BookGPT;
import com.wonderslate.data.repository.WSBookStore;
import com.wonderslate.prepjoy.R;
import com.wonderslate.prepjoy.Utils.CartDialog;
import com.wonderslate.prepjoy.ui.BaseActivity;
// import com.wonderslate.prepjoy.ui.shoppingcart.ShoppingCartIcon;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class GPTRechargeActivity extends BaseActivity {

    private RecyclerView recyclerView;
    private GPTRechargeAdapter rechargeAdapter;
    private List<RechargeOption> rechargeOptions;
    private Button purchaseRechargeBtn;
    private TextView pageTitle;
    private CardView btnBack;
    private RechargeOption selectedOption;
    // private ShoppingCartIcon shoppingCartIcon;
    private RelativeLayout rechargeLayout;
    private LinearLayout loadingLayout;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        loadingLayout = findViewById(R.id.gpt_recharge_loader_layout);
        loadingLayout.setVisibility(View.VISIBLE);
        rechargeLayout = findViewById(R.id.gpt_recharge_layout);
        rechargeLayout.setVisibility(View.GONE);
        pageTitle = findViewById(R.id.ws_gpt_recharge_title);
        pageTitle.setText("Token Recharge");
        btnBack = findViewById(R.id.btnBack);
        btnBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                onBackPressed();
            }
        });
        recyclerView = findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        purchaseRechargeBtn = findViewById(R.id.rechargePurchaseBtn);
        // shoppingCartIcon = new ShoppingCartIcon(GPTRechargeActivity.this, findViewById(R.id.flCart));

        rechargeOptions = new ArrayList<>();
        rechargeAdapter = new GPTRechargeAdapter(this, rechargeOptions, new GPTRechargeAdapter.OnRechargeOptionSelectedListener() {
            @Override
            public void onRechargeOptionSelected(RechargeOption option) {
                selectedOption = option;
            }
        });
        recyclerView.setAdapter(rechargeAdapter);

        fetchRechargeOptions();

        purchaseRechargeBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (selectedOption == null) {
                    Toast.makeText(GPTRechargeActivity.this, "Please select a recharge option", Toast.LENGTH_SHORT).show();
                    return;
                }
                else {
                    loadingLayout.setVisibility(View.VISIBLE);
                    addBookToCart(String.valueOf(selectedOption.getId()));
                }
            }
        });

        // updateShoppingCartIcon();
    }

    @Override
    protected int getLayoutResource() {
        return R.layout.gpt_recharge_activity;
    }

    private void fetchRechargeOptions() {
        new BookGPT().getGPTRechargeOptions(new WSCallback() {
            @Override
            public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                try {
                    JSONArray jsonArray = jsonObject.getJSONArray("rechargeOptions");

                    for (int i = 0; i < jsonArray.length(); i++) {
                        JSONObject optionObject = jsonArray.getJSONObject(i);

                        RechargeOption option = new RechargeOption(
                                optionObject.optInt("id"),
                                optionObject.optString("title"),
                                Html.fromHtml(optionObject.optString("description")).toString().trim(),
                                optionObject.optDouble("sell_price"),
                                optionObject.optInt("free_chat_tokens")
                        );

                        rechargeOptions.add(option);
                    }

                    rechargeAdapter.notifyDataSetChanged();
                    loadingLayout.setVisibility(View.GONE);
                    rechargeLayout.setVisibility(View.VISIBLE);

                } catch (JSONException e) {
                    loadingLayout.setVisibility(View.GONE);
                    rechargeLayout.setVisibility(View.VISIBLE);
                    e.printStackTrace();
                }
            }

            @Override
            public void onWSResultFailed(String resString, int responseCode) {
                loadingLayout.setVisibility(View.GONE);
                rechargeLayout.setVisibility(View.VISIBLE);
            }
        });
    }

    public void updateShoppingCartIcon() {
        // Shopping cart functionality temporarily disabled for demo
        // if (shoppingCartIcon != null) {
        //     shoppingCartIcon.updateCountUI();
        // }
    }

    private void addBookToCart(String bookId) {
        WSBookStore bookStore = new WSBookStore();
        bookStore.addBookToCart(bookId, "recharge", new WSResponseCallback<JSONObject>() {
            @Override
            public void onWSResultSuccess(JSONObject response, int responseCode) {
                loadingLayout.setVisibility(View.GONE);
                String status = response.optString("status");
                if ("OK".equalsIgnoreCase(status)) {
                    // fetchCartCount();
                    // shoppingCartIcon.openCart();
                    Toast.makeText(GPTRechargeActivity.this, "Added successfully!", Toast.LENGTH_SHORT).show();
                } else if ("Already exist".equalsIgnoreCase(status)) {
                    CartDialog cartDialog = new CartDialog();
                    cartDialog.showDialog(GPTRechargeActivity.this, CartDialog.ALREADY_ADDED);
                }
            }

            @Override
            public void onWSResultFailed(String resString, int responseCode) {
                loadingLayout.setVisibility(View.GONE);
                Toast.makeText(GPTRechargeActivity.this, "Something went wrong", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void fetchCartCount() {
        new WSBookStore().getCartCount(new WSResponseCallback<Integer>() {
            @Override
            public void onWSResultSuccess(Integer response, int responseCode) {
                // WonderPubSharedPrefs.getInstance(GPTRechargeActivity.this).setCartCount(response);
                // if (shoppingCartIcon != null) {
                //     shoppingCartIcon.updateCountUI();
                // }
            }

            @Override
            public void onWSResultFailed(String resString, int responseCode) {
                // WonderPubSharedPrefs.getInstance(GPTRechargeActivity.this).setCartCount(0);
                // if (shoppingCartIcon != null) {
                //     shoppingCartIcon.updateCountUI();
                // }
            }
        });
    }
}

