package com.wonderslate.prepjoy.ui.videos

import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.widget.LinearLayout
import androidx.recyclerview.widget.LinearLayoutManager
import com.wonderslate.commons.Data
import com.wonderslate.commons.Status
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.domain.entities.ResourceLogData
import com.wonderslate.domain.entities.VideosData
import com.wonderslate.prepjoy.BuildConfig
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.Utils.Utils
import com.wonderslate.prepjoy.Utils.Utils.disableScreenShot
import com.wonderslate.prepjoy.ui.BaseActivity
import com.wonderslate.prepjoy.databinding.ActivityVideoBinding
import com.wonderslate.prepjoy.databinding.HeaderLanguageChangeBinding
import org.json.JSONArray
import org.json.JSONObject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.text.DateFormat
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.*


class VideoActivity : BaseActivity() {
    private val model by viewModel<VideoViewModel>()
    val videoDataHash : HashMap<String,ArrayList<VideosData>>  = HashMap<String,ArrayList<VideosData>>()
    var videosDataList: ArrayList<VideosData> = arrayListOf()
    var pVideoAdapter : VideoAdapter? = null
    private var chooseDate = "23-09-2021"
    private lateinit var linearNoData : LinearLayout
    private var viewedFrom = ""
    private lateinit var binding: ActivityVideoBinding
    private lateinit var headerBinding: HeaderLanguageChangeBinding
    override fun getLayoutResource(): Int {
        return R.layout.activity_video
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityVideoBinding.inflate(layoutInflater)
        setContentView(binding.root)
        headerBinding = binding.header
        if (supportActionBar != null) {
            supportActionBar!!.hide()
        }
        binding.progressLoader.visibility = View.VISIBLE
        initObserver()
       // intent.getStringExtra("inputDate")?.let { model.getVideos("23-09-2021") } // "23-09-2021" data will come please put this date is static

        intent.getStringExtra("inputDate")
                ?.let {
                    chooseDate = changeDateFormat(it)
                    Log.e("Date",":"+chooseDate)
                    model.getVideos(chooseDate)
                }

        intent.getStringExtra("viewFrom")?.let {
            viewedFrom = it
        }

        val showToggle = resources.getBoolean(R.bool.show_language_toggle)
        if (showToggle) {
            headerBinding.toggleLanguage.setOnCheckedChangeListener { buttonView, isChecked ->
                if (isChecked) {
                    if (videoDataHash.containsKey("Hindi") && null != videoDataHash["Hindi"]) {
                        videosDataList = videoDataHash["Hindi"]!!
                        WonderPubSharedPrefs.getInstance(this). sharedPrefsContentLanguagePref = "Hindi"
                        if (videosDataList.size == 0) {
                            binding.linearNoData.visibility = View.VISIBLE
                        } else {
                            binding.linearNoData.visibility = View.GONE
                        }
                    } else {
                        binding.linearNoData.visibility = View.VISIBLE
                    }
                }
                else
                {
                    if (videoDataHash.containsKey("English") && null != videoDataHash["English"]) {
                        videosDataList = videoDataHash["English"]!!
                        WonderPubSharedPrefs.getInstance(this). sharedPrefsContentLanguagePref = "English"
                        if (videosDataList.size == 0) {
                            binding.linearNoData.visibility = View.VISIBLE
                        } else {
                            binding.linearNoData.visibility = View.GONE
                        }
                    } else {
                        binding.linearNoData.visibility = View.VISIBLE
                    }
                }
                pVideoAdapter = VideoAdapter(videosDataList, this, this)
                binding.recycleVideo.setLayoutManager(LinearLayoutManager(this));
                binding.recycleVideo.setAdapter(pVideoAdapter)
            }
        } else {
            headerBinding.toggleLanguage.visibility = View.INVISIBLE
        }
        headerBinding.rrltBack.setOnClickListener {
            onBackPressed()
        }
        linearNoData = binding.linearNoData
        disableScreenShot(this)
    }

    private fun initObserver() {
        model.videoResponse.observe(this, ::videoResponse)
        model.resLogResponse.observe(this, ::logResourceResponse)
    }

    private fun logResourceResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
                // do nothing
            }
            Status.SUCCESSFUL -> {
                // do nothing
            }

            Status.HTTP_UNAVAILABLE -> {
                //do nothing
            }

            Status.ERROR -> {
                //do nothing
            }
        }
    }



    private fun videoResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
                binding.progressLoader.visibility = View.GONE
            }

            Status.SUCCESSFUL -> {
                data.data?.let {
                    binding.progressLoader.visibility = View.GONE
                    loadVideoData(it)
                }
            }

            Status.HTTP_UNAVAILABLE -> {
                binding.progressLoader.visibility = View.GONE

            }

            Status.ERROR -> {
                binding.progressLoader.visibility = View.GONE

            }
        }
    }
    private fun loadVideoData(jsonObject: JSONObject) {
        val videosString = jsonObject.getString("videos")
        val videosJsonarray = JSONArray(videosString)
        val videosHindiDataList: ArrayList<VideosData> = arrayListOf()
        val videosEnglishDataList: ArrayList<VideosData> = arrayListOf()
        for (index in 0 until videosJsonarray.length()) {
            val VideosObj = videosJsonarray.getJSONObject(index)
            val tagObj = VideosObj.getJSONObject("tag")
            if(tagObj.getString("language").equals("Hindi",true))
            {
                videosHindiDataList.add(getVideoObj(VideosObj))
            }
            else
            {
                videosEnglishDataList.add(getVideoObj(VideosObj))
            }

        }
        videoDataHash["Hindi"] = videosHindiDataList
        videoDataHash["English"] = videosEnglishDataList
        loadAdapter()
    }

    fun logVideoOpen(id: String) {
        model.logResourceOpen(
            ResourceLogData(
                id,
                BuildConfig.SITE_ID,
                "android",
                viewedFrom,
                "view"
            )
        )
    }
    private fun getVideoObj(VideosObj:JSONObject) : VideosData
    {
         return  VideosData(
                    VideosObj.getInt("id"),
                    VideosObj.getString("title"),
                    VideosObj.getString("description"),
                    VideosObj.getString("resourceType"),
                    VideosObj.getString("showFullDetails"),
                    VideosObj.getString("referenceLink"),
                    VideosObj.getString("answer"),
                    VideosObj.getString("showAnswer"),
                    VideosObj.getString("videoLink"),
                    VideosObj.getString("deepLink"),
                    VideosObj.getString("dateCreated"),
                    VideosObj.getString("tag")
            )
    }
    private fun loadAdapter()
    {
        val wonderPubSharedPrefs = WonderPubSharedPrefs.getInstance(applicationContext)
        videosDataList = if(wonderPubSharedPrefs.sharedPrefsContentLanguagePref.equals("Hindi",true))
            videoDataHash["Hindi"]!!
        else
            videoDataHash["English"]!!
        if (wonderPubSharedPrefs.sharedPrefsContentLanguagePref.equals("Hindi", true))
            headerBinding.toggleLanguage.isChecked = true
        if (videosDataList.size == 0) {
            linearNoData.visibility = View.VISIBLE
        } else {
            linearNoData.visibility = View.GONE
        }

        pVideoAdapter = VideoAdapter(videosDataList, this, this)
       /* val layoutManager = GridLayoutManager(this, 2)
        binding.recycleVideo.setLayoutManager(layoutManager)
        binding.recycleVideo.setAdapter(adapter)*/
        binding.recycleVideo.setLayoutManager(LinearLayoutManager(this))
        binding.recycleVideo.setAdapter(pVideoAdapter)
    }

    private fun changeDateFormat(dateString1: String): String {
        try {
            val df1: DateFormat = SimpleDateFormat("dd-MMM-yyyy", Locale.ENGLISH)
            var date1: Date? = null
            try {
                date1 = df1.parse(dateString1)
            } catch (e: ParseException) {
                e.printStackTrace()
            }
            val cal1: Calendar = Calendar.getInstance()
            cal1.time = date1


            val dateFormater = SimpleDateFormat("dd-MM-yyyy", Locale.ENGLISH)
            val formattedDate = dateFormater.format(cal1.time)


            return formattedDate
        } catch (e : Exception) {
            return dateString1
        }
    }
}