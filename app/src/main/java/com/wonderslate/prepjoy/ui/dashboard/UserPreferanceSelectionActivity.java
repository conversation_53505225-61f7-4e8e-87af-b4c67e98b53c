package com.wonderslate.prepjoy.ui.dashboard;

import static com.wonderslate.prepjoy.Utils.Utils.disableScreenShot;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.airbnb.lottie.LottieAnimationView;
import com.google.common.reflect.TypeToken;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.firebase.ktx.Firebase;
import com.google.gson.Gson;
import com.wang.avi.AVLoadingIndicatorView;
import com.wonderslate.data.interfaces.WSCallback;
import com.wonderslate.data.models.UserStudyPreference;
import com.wonderslate.data.network.OpenUtils;
import com.wonderslate.data.network.Wonderslate;
import com.wonderslate.data.preferences.WonderPubSharedPrefs;
import com.wonderslate.prepjoy.BuildConfig;
import com.wonderslate.prepjoy.R;
import com.wonderslate.prepjoy.Utils.FirebaseAnalyticsUtils;
import com.wonderslate.prepjoy.Utils.Flavours;
import com.wonderslate.prepjoy.Utils.InternetConnectionChecker;
import com.wonderslate.prepjoy.Utils.Utils;
import com.wonderslate.prepjoy.ui.BaseActivity;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

public class UserPreferanceSelectionActivity extends BaseActivity {

    @BindView(R.id.recyclerView_level) RecyclerView recyclerViewLevel;
    @BindView(R.id.recyclerView_syllabus) RecyclerView recyclerViewSyllabus;
    @BindView(R.id.recyclerView_grade) RecyclerView recyclerViewGrade;

    @BindView(R.id.recyclerView_subject) RecyclerView recyclerViewSubject;
    @BindView(R.id.recyclerView_sub_subject) RecyclerView recyclerViewSubSubject;


    @BindView(R.id.textView_title_level) TextView textViewTitleLevel;
    @BindView(R.id.textView_title_level_stage) TextView textViewTitleLevelStage;
    @BindView(R.id.loaderMain) AVLoadingIndicatorView loaderMain;
    @BindView(R.id.button_next_level) Button buttonNextLevel;
    @BindView(R.id.button_back) Button buttonBack;
    @BindView(R.id.button_clear) Button buttonClear;
    @BindView(R.id.lottie_view) LottieAnimationView lottieView;
    @BindView(R.id.textView_server_message) TextView textViewServerMessage;
    @BindView(R.id.layout_issue) RelativeLayout layoutIssue;
    @BindView(R.id.rrltBack) RelativeLayout rrltBack;
    private List<SyllabusPrefModel> syllabusListComplete = new ArrayList<>();
    private List<GradePrefModel> gradeListComplete = new ArrayList<>();
    private List<SubjectPrefModel> subjectListComplete = new ArrayList<>();
    private List<SubSubjectPrefModel> subSubjectListComplete = new ArrayList<>();

    private Context mContext;
    private WonderPubSharedPrefs wonderPubSharedPrefs;
    private JSONArray arrayAllFilterData = null;
    private ArrayList<String> alLevelName;
    private ArrayList<String> alSyllabusName;
    private ArrayList<String> alGradeName;

    private ArrayList<String> alSubjectName;
    private ArrayList<String> alSubSubjectName;

    private String levelId = "", syllabusId = "", gradeId = "", subjectId = "", publisherId = "";
    private UserPrefSelectLevelAdapter userPrefSelectLevelAdapter;
    private UserPrefSelectSyllabusAdapter userPrefSelectSyllabusAdapter;
    private UserPrefSelectGradeAdapter userPrefSelectGradeAdapter;
    private UserPrefSelectSubjectAdapter userPrefSelectSubjectAdapter;
    private UserPrefSelectSubjectSubAdapter userPrefSelectSubSubjectAdapter;
    private String selectedLevelFinal = "", selectedSyllabusFinal = "", selectedGradeFinal = "";
    private String selectedSubjectFinal = "", selectedSubSubjectFinal = "";
    private String showFrom = "level", comingFor = "set";
    private List<String> syllabusList = new ArrayList<>();
    private List<String> gradeList = new ArrayList<>();
    private List<String> subjectList = new ArrayList<>();
    private List<String> subSubjectList = new ArrayList<>();
    private FirebaseAnalytics   firebaseAnalytics;
    private int tabPos;
    private HashMap<String,List<String>> pGradeList = new HashMap<>();

    private String prefTempLevel;
    private List<String> prefTempSyllabus;
    private List<String> preTempGrade;

    private String searchPreTempLevel;
    private String searchpreTempSyllabus;
    private String searchpreTempGrade;

    private UserStudyPreference userStudyPreference;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ButterKnife.bind(this);

        mContext = this;
        wonderPubSharedPrefs = WonderPubSharedPrefs.getInstance(UserPreferanceSelectionActivity.this);

        userStudyPreference = new UserStudyPreference();

        if (wonderPubSharedPrefs.getUserPrefs() != null && !wonderPubSharedPrefs.getUserPrefs().isEmpty()) {
            try {
                JSONObject userPrefs = new JSONObject(wonderPubSharedPrefs.getUserPrefs());
                userStudyPreference.setLevelPreference(userPrefs.optString("selectedLevel"));
                userStudyPreference.setSyllabusPreference(userPrefs.optString("selectedSyllabus"));
                userStudyPreference.setGradePreference(userPrefs.optString("selectedGrade"));
            } catch (JSONException e) {
                userStudyPreference.setLevelPreference("");
                userStudyPreference.setSyllabusPreference("");
                userStudyPreference.setGradePreference("");
            }
        }

        Window window = this.getWindow();
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        window.setNavigationBarColor(ContextCompat.getColor(this, R.color.primary_bg_dark));
        getWindow().setStatusBarColor(ContextCompat.getColor(this, R.color.primary_bg_dark));
        View decorView = getWindow().getDecorView();
        decorView.setSystemUiVisibility(decorView.getSystemUiVisibility() & ~View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING);

        if (getIntent() != null) {
            showFrom = getIntent().getStringExtra("SHOW_FROM");
            comingFor = getIntent().getStringExtra("MODE");
            comingFor = "";
            if(getIntent().hasExtra("TabPosition"))
                tabPos = getIntent().getIntExtra("TabPosition",0);

        }
        showFrom = "level";
        if (comingFor.equalsIgnoreCase("change"))
        {
            prefTempLevel = wonderPubSharedPrefs.getSharedPrefsUserLevelPref();
            prefTempSyllabus = wonderPubSharedPrefs .getSharedPrefsUserSyllabusPref();
            preTempGrade = wonderPubSharedPrefs.getSharedPrefsUserGradePref();

            searchPreTempLevel = wonderPubSharedPrefs.getSharedPrefsLastFilterLevelSearch();
            searchpreTempSyllabus= wonderPubSharedPrefs.getSharedPrefsLastFilterSyllabusSearch();
            searchpreTempGrade = wonderPubSharedPrefs.getSharedPrefsLastFilterGradeSearch();

        }

        buttonNextLevel.setEnabled(false);
        buttonNextLevel.setBackgroundResource(R.drawable.button_shape_disabled);

        setUpView();
        loaderMain.smoothToShow();
        try {
            //String jsonFileString = getAssetsData("list_dropdown_values.json");
            String jsonFileString = Wonderslate.getInstance().getSharedPrefs().getDefaultCategory();
            arrayAllFilterData = new JSONArray(jsonFileString);
            /*String cachedData =  wonderPubSharedPrefs.getPrefrenceValue();
            if(cachedData.isEmpty() && !cachedData.equals("[]")) {
                arrayAllFilterData = new JSONArray(cachedData);
            }
            else
            {
                String jsonFileString = getAssetsData("list_dropdown_values.json");
                arrayAllFilterData = new JSONArray(jsonFileString);
            }*/

        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        getAllFilterData();
        firebaseAnalytics = FirebaseAnalytics.getInstance(this);
        disableScreenShot(this);
    }

    @Override
    protected int getLayoutResource() {
        return R.layout.activity_user_preferance_selection;
    }

    @Override
    public void onBackPressed() {
        try {
            if (comingFor.equalsIgnoreCase("change")) {
                wonderPubSharedPrefs.setSharedPrefsUserLevelPref(prefTempLevel);
                wonderPubSharedPrefs.setSharedPrefsUserSyllabusPref(prefTempSyllabus);
                wonderPubSharedPrefs.setSharedPrefsUserGradePref(preTempGrade);

                wonderPubSharedPrefs.setSharedPrefsLastFilterLevelSearch(searchPreTempLevel);
                wonderPubSharedPrefs.setSharedPrefsLastFilterSyllabusSearch(searchpreTempSyllabus);
                wonderPubSharedPrefs.setSharedPrefsLastFilterGradeSearch(searchpreTempGrade);

                finish();
            } else {
               /* Intent returnIntent = new Intent();
                returnIntent.putExtra("PRE_STATUS", "notDone");
                setResult(Activity.RESULT_OK, returnIntent);*/
                finish();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        super.onBackPressed();
    }

    private void setUpView() {
        try {
            recyclerViewLevel.setLayoutManager(new GridLayoutManager(mContext, 2));
            recyclerViewLevel.setNestedScrollingEnabled(false);

            recyclerViewSyllabus.setLayoutManager(new GridLayoutManager(mContext, 2));
            recyclerViewSyllabus.setNestedScrollingEnabled(false);

            recyclerViewGrade.setLayoutManager(new GridLayoutManager(mContext, 2));
            recyclerViewGrade.setNestedScrollingEnabled(false);

            recyclerViewSubject.setLayoutManager(new GridLayoutManager(mContext, 2));
            recyclerViewSubject.setNestedScrollingEnabled(false);

            recyclerViewSubSubject.setLayoutManager(new GridLayoutManager(mContext, 2));
            recyclerViewSubSubject.setNestedScrollingEnabled(false);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void getAllFilterData() {
        try {
            if(arrayAllFilterData != null)
            {
                loaderMain.smoothToHide();
                layoutIssue.setVisibility(View.GONE);
                getAllData();
            }
            else {

                InternetConnectionChecker internetConnectionChecker = new InternetConnectionChecker();
                if (internetConnectionChecker.isNetworkConnected(this))
                {
                    loaderMain.smoothToShow();
                    OpenUtils wsBookStore = new OpenUtils();
                    wsBookStore.getLatestBooksNewFilter(levelId, syllabusId, gradeId, subjectId, publisherId, String.valueOf(1), new WSCallback() {
                        @Override
                        public void onWSResultSuccess(JSONObject suggestionJson, int responseCode) {
                            try {
                                loaderMain.smoothToHide();
                                layoutIssue.setVisibility(View.GONE);
                                if (suggestionJson.optString("books") != null && !suggestionJson.optString("books").equalsIgnoreCase("[]")) {
                                    arrayAllFilterData = new JSONArray(suggestionJson.getString("bookTags"));
                                    getAllData();
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }

                        @Override
                        public void onWSResultFailed(String resString, int responseCode) {
                            loaderMain.smoothToHide();
                            layoutIssue.setVisibility(View.VISIBLE);
                            textViewServerMessage.setText("Could not connect to server, Please try after some time.");
                            Utils.showErrorToast(mContext, responseCode);
                        }
                    });
                } else {
                    layoutIssue.setVisibility(View.VISIBLE);
                    textViewServerMessage.setText("Please check your internet connection.");
                }
            }
        } catch (Exception e) {
            loaderMain.smoothToHide();
            e.printStackTrace();
        }
    }
    private void getAllData()
    {
        try {
            if (showFrom.equalsIgnoreCase("level")) {
                if (comingFor.equalsIgnoreCase("change")) {
                    onLevelSelectedChange(wonderPubSharedPrefs.getSharedPrefsUserLevelPref());
                } else {
                    if (!wonderPubSharedPrefs.getSharedPrefsUserLevelPref().isEmpty()) {
                        onLevelSelectedChange(wonderPubSharedPrefs.getSharedPrefsUserLevelPref());
                    } else {
                        setLevelDataToGrid();
                    }
                    //setLevelDataToGrid();
                }
            } else if (showFrom.equalsIgnoreCase("syllabus")) {
                if (comingFor.equalsIgnoreCase("change")) {
                    syllabusList = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref();
                    onSyllabusSelectedChange(syllabusList);
                } else {
                    setSyllabusDataToGrid();
                }
                selectedLevelFinal = wonderPubSharedPrefs.getSharedPrefsUserLevelPref();
            } else if ((showFrom.equalsIgnoreCase("grade") && wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().size() > 0) &&
                    !(BuildConfig.FLAVOR.equalsIgnoreCase(Flavours.ENGINEERING.getFlavour() )))
            {
                if (comingFor.equalsIgnoreCase("change") && wonderPubSharedPrefs.getSharedPrefsUserGradePref() != null) {
                    gradeList = wonderPubSharedPrefs.getSharedPrefsUserGradePref();
                    selectedLevelFinal = wonderPubSharedPrefs.getSharedPrefsUserLevelPref();
                    //  selectedSyllabusFinal = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0);
                    if (wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().size() > 0) {
                        selectedSyllabusFinal = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0);
                    }
                    onGradeSelectedChange(gradeList);
                } else {
                    selectedLevelFinal = wonderPubSharedPrefs.getSharedPrefsUserLevelPref();
                    //selectedSyllabusFinal = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0);
                    if (wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().size() > 0) {
                        selectedSyllabusFinal = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0);
                    }
                    setGradeDataToGrid();
                }

            }
            else if ( BuildConfig.FLAVOR.equalsIgnoreCase(Flavours.CACSCMA.getFlavour()))
            {
                if ((showFrom.equalsIgnoreCase("subject") && wonderPubSharedPrefs.getSharedPrefsUserGradePref().size() > 0))
                {
                    if (comingFor.equalsIgnoreCase("change")) {
                        subjectList = wonderPubSharedPrefs.getSharedPrefsUserSubjectPref();
                        selectedLevelFinal = wonderPubSharedPrefs.getSharedPrefsUserLevelPref();
                        //  selectedSyllabusFinal = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0);
                        if (wonderPubSharedPrefs.getSharedPrefsUserGradePref().size() > 0) {
                            selectedGradeFinal = wonderPubSharedPrefs.getSharedPrefsUserGradePref().get(0);
                        }
                        if (wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().size() > 0) {
                            selectedSyllabusFinal = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0);
                        }
                        onSubjectSelectedChange(subjectList);
                    } else {
                        selectedLevelFinal = wonderPubSharedPrefs.getSharedPrefsUserLevelPref();
                        //selectedSyllabusFinal = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0);
                        if (wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().size() > 0) {
                            selectedSyllabusFinal = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0);
                        }
                        if (wonderPubSharedPrefs.getSharedPrefsUserGradePref().size() > 0) {
                            selectedGradeFinal = wonderPubSharedPrefs.getSharedPrefsUserGradePref().get(0);
                        }
                        setSubjectDataToGrid();
                    }
                }
               else if ((showFrom.equalsIgnoreCase("subSubject") && wonderPubSharedPrefs.getSharedPrefsUserSubjectPref().size() > 0))
                {
                    if (comingFor.equalsIgnoreCase("change")) {
                        subSubjectList = wonderPubSharedPrefs.getSharedPrefsUserSubSubjectPref();
                        selectedLevelFinal = wonderPubSharedPrefs.getSharedPrefsUserLevelPref();
                        //  selectedSyllabusFinal = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0);
                        if (wonderPubSharedPrefs.getSharedPrefsUserGradePref().size() > 0) {
                            selectedGradeFinal = wonderPubSharedPrefs.getSharedPrefsUserGradePref().get(0);
                        }
                        if (wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().size() > 0) {
                            selectedSyllabusFinal = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0);
                        }
                        if (wonderPubSharedPrefs.getSharedPrefsUserSubjectPref().size() > 0) {
                            selectedSubjectFinal = wonderPubSharedPrefs.getSharedPrefsUserSubjectPref().get(0);
                        }

                        onSubSubjectSelectedChange(subSubjectList);
                    } else {
                        selectedLevelFinal = wonderPubSharedPrefs.getSharedPrefsUserLevelPref();
                        //selectedSyllabusFinal = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0);
                        if (wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().size() > 0) {
                            selectedSyllabusFinal = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0);
                        }
                        if (wonderPubSharedPrefs.getSharedPrefsUserGradePref().size() > 0) {
                            selectedGradeFinal = wonderPubSharedPrefs.getSharedPrefsUserGradePref().get(0);
                        }
                        if (wonderPubSharedPrefs.getSharedPrefsUserSubjectPref().size() > 0) {
                            selectedSubjectFinal = wonderPubSharedPrefs.getSharedPrefsUserSubjectPref().get(0);
                        }
                        setSubSubjectDataToGrid();
                    }
                }
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    @OnClick({R.id.button_next_level, R.id.button_back, R.id.button_clear})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.button_next_level:
                try {
                    loaderMain.smoothToShow();
                    if (buttonNextLevel.getText().toString().equalsIgnoreCase("next") && textViewTitleLevelStage.getTag().toString().contains("Level")
                            && !userStudyPreference.getLevelPreference().isEmpty()) {
                        if (userStudyPreference.getSyllabusPreference() != null && userStudyPreference.getSyllabusPreference().isEmpty()) {
                            buttonNextLevel.setEnabled(false);
                            buttonNextLevel.setBackgroundResource(R.drawable.button_shape_disabled);
                        } else {
                            buttonNextLevel.setEnabled(true);
                            buttonNextLevel.setBackgroundResource(R.drawable.button_shape_default);
                        }
                        if (comingFor.equalsIgnoreCase("change")) {
                            syllabusList = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref();
                            onSyllabusSelectedChange(syllabusList);
                        } else {
                            syllabusList = new ArrayList<>();
                            if (!userStudyPreference.getSyllabusPreference().isEmpty()) {
                                syllabusList.add(userStudyPreference.getSyllabusPreference());
                            }
                            if (syllabusList != null && syllabusList.size() > 0) {
                                onSyllabusSelectedChange(syllabusList);
                            }
                            else {
                                selectedLevelFinal = userStudyPreference.getLevelPreference();
                                setSyllabusDataToGrid();
                            }
                        }
                        loaderMain.smoothToHide();
                    } else if (buttonNextLevel.getText().toString().equalsIgnoreCase("next") && textViewTitleLevelStage.getTag().toString().contains("Syllabus")
                            && !userStudyPreference.getSyllabusPreference().isEmpty()) {
                        if (userStudyPreference.getSyllabusPreference() != null && userStudyPreference.getSyllabusPreference().isEmpty()) {
                            buttonNextLevel.setEnabled(false);
                            buttonNextLevel.setBackgroundResource(R.drawable.button_shape_disabled);
                        } else {
                            buttonNextLevel.setEnabled(true);
                            buttonNextLevel.setBackgroundResource(R.drawable.button_shape_default);
                        }

                        if (comingFor.equalsIgnoreCase("change") && wonderPubSharedPrefs.getSharedPrefsUserGradePref()!= null) {
                            gradeList = wonderPubSharedPrefs.getSharedPrefsUserGradePref();
                            selectedLevelFinal = wonderPubSharedPrefs.getSharedPrefsUserLevelPref();
                            //  selectedSyllabusFinal = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0);
                            if (wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().size() > 0) {
                                selectedSyllabusFinal = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0);
                            }
                            onGradeSelectedChange(gradeList);
                        } else {
                            gradeList = new ArrayList<>();
                            gradeList.add(userStudyPreference.getGradePreference());
                            if (gradeList != null && gradeList.size() > 0) {
                                onGradeSelectedChange(gradeList);
                            }
                            else {
                                selectedLevelFinal = userStudyPreference.getLevelPreference();
                                //selectedSyllabusFinal = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0);
                                if (!userStudyPreference.getSyllabusPreference().isEmpty()) {
                                    selectedSyllabusFinal = userStudyPreference.getSyllabusPreference();
                                }
                                setGradeDataToGrid();
                            }
                        }
                        loaderMain.smoothToHide();
                    }
                    else if ((buttonNextLevel.getText().toString().equalsIgnoreCase("Done") && textViewTitleLevelStage.getTag().toString().contains("Grade")
                            && !userStudyPreference.getGradePreference().isEmpty())
                            || BuildConfig.FLAVOR.equalsIgnoreCase(Flavours.ENGINEERING.getFlavour()))
                    {
                        loaderMain.smoothToShow();
                        buttonNextLevel.setEnabled(true);
                        buttonNextLevel.setBackgroundResource(R.drawable.button_shape_default);

                        Bundle bundleLevel = new Bundle();
                        bundleLevel.putString(FirebaseAnalyticsUtils.ACTIVITY_NAME, "UserPreferenceSelectionActivity");
                        if(!userStudyPreference.getLevelPreference().isEmpty())
                        {
                            String levelVal = userStudyPreference.getLevelPreference();
                            bundleLevel.putString(FirebaseAnalyticsUtils.ACTION, "UserPreferenceSelectionActivity Level - "+levelVal);
                        }
                        firebaseAnalytics.logEvent(FirebaseAnalyticsUtils.LOCATION, bundleLevel);

                        Bundle bundleSyllbus = new Bundle();
                        bundleSyllbus.putString(FirebaseAnalyticsUtils.ACTIVITY_NAME, "UserPreferenceSelectionActivity");
                        if(!userStudyPreference.getSyllabusPreference().isEmpty())
                        {
                            List<String> syllabusList = new ArrayList<>();
                            syllabusList.add(userStudyPreference.getSyllabusPreference());
                            wonderPubSharedPrefs.setSharedPrefsLastFilterSyllabusSearch(userStudyPreference.getSyllabusPreference());
                            String syllabusVal = TextUtils.join(", ", syllabusList);
                            bundleSyllbus.putString(FirebaseAnalyticsUtils.ACTION, "UserPreferenceSelectionActivity Syllabus - "+syllabusVal);
                        }
                        firebaseAnalytics.logEvent(FirebaseAnalyticsUtils.LOCATION, bundleSyllbus);

                        Bundle bundleGrade = new Bundle();
                        bundleGrade.putString(FirebaseAnalyticsUtils.ACTIVITY_NAME, "UserPreferenceSelectionActivity");
                        if(!userStudyPreference.getGradePreference().isEmpty())
                        {
                            List<String> gradeList = new ArrayList<>();
                            gradeList.add(userStudyPreference.getGradePreference());
                            wonderPubSharedPrefs.setSharedPrefsLastFilterGradeSearch(userStudyPreference.getGradePreference());
                            String syllabusVal = TextUtils.join(", ", gradeList);
                            bundleSyllbus.putString(FirebaseAnalyticsUtils.ACTION, "UserPreferenceSelectionActivity Grade - "+syllabusVal);
                        }
                        firebaseAnalytics.logEvent(FirebaseAnalyticsUtils.LOCATION, bundleGrade);

                        outerloop:    for(String alSyllabus : syllabusList)
                        {
                            for(String pSyllabus : syllabusList)
                            {
                                if(pSyllabus.equalsIgnoreCase(alSyllabus))
                                {
                                    wonderPubSharedPrefs.setSharedPrefsLastFilterSyllabusSearch(pSyllabus);

                                    for (HashMap.Entry<String, List<String>> entry : pGradeList.entrySet())
                                    {
                                        String key = entry.getKey();

                                        if(key.equalsIgnoreCase(pSyllabus))
                                        {
                                            List<String> value = entry.getValue();
                                            for(String aString : value)
                                            {
                                               // System.out.println("key : " + key + " value : " + aString);

                                                for(String pGrade : gradeList)
                                                {
                                                    if(pGrade.equalsIgnoreCase(aString))
                                                    {
                                                        wonderPubSharedPrefs.setSharedPrefsLastFilterGradeSearch(pGrade);
                                                        break outerloop;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                      //  Log.e("Data get",":"+wonderPubSharedPrefs.getSharedPrefsLastFilterGradeSearch()+":"+wonderPubSharedPrefs.getSharedPrefsLastFilterSyllabusSearch());

                        Bundle bundle = new Bundle();
                        bundle.putString(FirebaseAnalyticsUtils.ACTIVITY_NAME, "UserPreferenceSelectionActivity");
                        bundle.putString(FirebaseAnalyticsUtils.ACTION, "UserPreferenceSelectionActivity Done");
                        firebaseAnalytics.logEvent(FirebaseAnalyticsUtils.LOCATION, bundle);

                           /* Intent returnIntent = new Intent();
                            returnIntent.putExtra("PRE_STATUS", "done");
                            setResult(Activity.RESULT_OK, returnIntent);
                            this.finish();*/

                        if (comingFor.equalsIgnoreCase("change"))
                        {
                            if (BuildConfig.FLAVOR == Flavours.CACSCMA.getFlavour() || BuildConfig.FLAVOR == Flavours.CTET.getFlavour()) {
                                if(preTempGrade != null) {
                                    preTempGrade.removeAll(wonderPubSharedPrefs.getSharedPrefsUserGradePref());
                                    if (preTempGrade.size() >= 0) {
                                        String level = getString(R.string.level);
                                        List<String> syllabus = Arrays.asList(getString(R.string.syllabus));
                                        //wonderPubSharedPrefs.setSharedPrefsUserLevelPref(level);
                                        //wonderPubSharedPrefs.setSharedPrefsLastFilterLevelSearch(level);
                                        wonderPubSharedPrefs.setSharedPrefsUserSyllabusPref(syllabus);
                                        wonderPubSharedPrefs.setSharedPrefsLastFilterSyllabusSearch(syllabus.get(0));
                                    }
                                }
                            }
                            else if (BuildConfig.FLAVOR.equalsIgnoreCase(Flavours.ENGINEERING.getFlavour())) {
                                preTempGrade.removeAll(wonderPubSharedPrefs.getSharedPrefsUserGradePref());
                                if(preTempGrade.size()>=0)
                                {
                                    wonderPubSharedPrefs.setSharedPrefsUserLevelPref("Engineering Entrances");
                                    wonderPubSharedPrefs.setSharedPrefsLastFilterLevelSearch("Engineering Entrances");
                                }
                            }
                            //String level = getString(R.string.level);
                            //wonderPubSharedPrefs.setSharedPrefsUserLevelPref(level);
                            //wonderPubSharedPrefs.setSharedPrefsLastFilterLevelSearch(level);
                            Intent returnIntent = new Intent();
                            returnIntent.putExtra("PRE_STATUS", "change");
                            returnIntent.putExtra("CurrentTab",tabPos);
                            setResult(Activity.RESULT_OK, returnIntent);
                            finish();
                            //finish();
                        } else {
                            Intent returnIntent = new Intent();
                            returnIntent.putExtra("PRE_STATUS", "done");
                            returnIntent.putExtra("userSelectedPrefsObject", userStudyPreference);
                            setResult(Activity.RESULT_OK, returnIntent);
                            finish();
                        }


                        loaderMain.smoothToHide();
                    }
                    else if ( BuildConfig.FLAVOR.equalsIgnoreCase(Flavours.CACSCMA.getFlavour()))
                    {
                       if ((buttonNextLevel.getText().toString().equalsIgnoreCase("Next") &&  textViewTitleLevelStage.getTag().toString().contains("Grade")  && wonderPubSharedPrefs.getSharedPrefsUserGradePref().size() > 0))
                        {
                            if ((wonderPubSharedPrefs.getSharedPrefsUserGradePref() != null  && wonderPubSharedPrefs.getSharedPrefsUserGradePref().size() == 0)|| wonderPubSharedPrefs.getSharedPrefsUserSubjectPref() == null) {
                                buttonNextLevel.setEnabled(false);
                                buttonNextLevel.setBackgroundResource(R.drawable.button_shape_disabled);
                            } else {
                                buttonNextLevel.setEnabled(true);
                                buttonNextLevel.setBackgroundResource(R.drawable.button_shape_default);
                            }
                            if (comingFor.equalsIgnoreCase("change") && wonderPubSharedPrefs.getSharedPrefsUserSubjectPref() != null) {
                                subjectList = wonderPubSharedPrefs.getSharedPrefsUserSubjectPref();
                                selectedLevelFinal = wonderPubSharedPrefs.getSharedPrefsUserLevelPref();
                                //  selectedSyllabusFinal = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0);
                                if (wonderPubSharedPrefs.getSharedPrefsUserGradePref().size() > 0) {
                                    selectedGradeFinal = wonderPubSharedPrefs.getSharedPrefsUserGradePref().get(0);
                                }
                                if (wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().size() > 0) {
                                    selectedSyllabusFinal = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0);
                                }
                                onSubjectSelectedChange(subjectList);
                            } else {
                                selectedLevelFinal = wonderPubSharedPrefs.getSharedPrefsUserLevelPref();
                                //selectedSyllabusFinal = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0);
                                if (wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().size() > 0) {
                                    selectedSyllabusFinal = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0);
                                }
                                if (wonderPubSharedPrefs.getSharedPrefsUserGradePref().size() > 0) {
                                    selectedGradeFinal = wonderPubSharedPrefs.getSharedPrefsUserGradePref().get(0);
                                }
                                setSubjectDataToGrid();
                            }
                            loaderMain.smoothToHide();

                        }
                       else if((buttonNextLevel.getText().toString().equalsIgnoreCase("Next") &&  textViewTitleLevelStage.getTag().toString().contains("Subject")  && wonderPubSharedPrefs.getSharedPrefsUserSubjectPref().size() > 0))
                       {
                           if (comingFor.equalsIgnoreCase("change") && wonderPubSharedPrefs.getSharedPrefsUserSubSubjectPref() != null) {
                               subSubjectList = wonderPubSharedPrefs.getSharedPrefsUserSubSubjectPref();
                               selectedLevelFinal = wonderPubSharedPrefs.getSharedPrefsUserLevelPref();
                               //  selectedSyllabusFinal = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0);
                               if (wonderPubSharedPrefs.getSharedPrefsUserGradePref().size() > 0) {
                                   selectedGradeFinal = wonderPubSharedPrefs.getSharedPrefsUserGradePref().get(0);
                               }
                               if (wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().size() > 0) {
                                   selectedSyllabusFinal = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0);
                               }
                               if (wonderPubSharedPrefs.getSharedPrefsUserSubjectPref().size() > 0) {
                                   selectedSubjectFinal = wonderPubSharedPrefs.getSharedPrefsUserSubjectPref().get(0);
                               }

                               onSubSubjectSelectedChange(subSubjectList);
                           } else {
                               if(wonderPubSharedPrefs.getSharedPrefsUserSubSubjectPref() == null) {
                                   buttonNextLevel.setEnabled(false);
                                   buttonNextLevel.setBackgroundResource(R.drawable.button_shape_disabled);
                               }
                               selectedLevelFinal = wonderPubSharedPrefs.getSharedPrefsUserLevelPref();
                               //selectedSyllabusFinal = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0);
                               if (wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().size() > 0) {
                                   selectedSyllabusFinal = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0);
                               }
                               if (wonderPubSharedPrefs.getSharedPrefsUserGradePref().size() > 0) {
                                   selectedGradeFinal = wonderPubSharedPrefs.getSharedPrefsUserGradePref().get(0);
                               }
                               if (wonderPubSharedPrefs.getSharedPrefsUserSubjectPref().size() > 0) {
                                   selectedSubjectFinal = wonderPubSharedPrefs.getSharedPrefsUserSubjectPref().get(0);
                               }
                               setSubSubjectDataToGrid();
                           }
                           loaderMain.smoothToHide();
                       }
                      else  if ((buttonNextLevel.getText().toString().equalsIgnoreCase("Done") && wonderPubSharedPrefs.getSharedPrefsUserSubjectPref().size() > 0))
                        {
                            loaderMain.smoothToShow();
                            buttonNextLevel.setEnabled(true);
                            buttonNextLevel.setBackgroundResource(R.drawable.button_shape_default);

                            Bundle bundleSyllbus = new Bundle();
                            bundleSyllbus.putString(FirebaseAnalyticsUtils.ACTIVITY_NAME, "UserPreferenceSelectionActivity");
                            if(wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().size()>0)
                            {
                                List<String> syllabusList = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref();
                                wonderPubSharedPrefs.setSharedPrefsLastFilterSyllabusSearch(wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0));
                                String syllabusVal = TextUtils.join(", ", syllabusList);
                                bundleSyllbus.putString(FirebaseAnalyticsUtils.ACTION, "UserPreferenceSelectionActivity Syllabus - "+syllabusVal);
                            }
                            firebaseAnalytics.logEvent(FirebaseAnalyticsUtils.LOCATION, bundleSyllbus);

                            Bundle bundleGrade = new Bundle();
                            bundleGrade.putString(FirebaseAnalyticsUtils.ACTIVITY_NAME, "UserPreferenceSelectionActivity");
                            if(wonderPubSharedPrefs.getSharedPrefsUserGradePref().size()>0)
                            {
                                List<String> gradeList = wonderPubSharedPrefs.getSharedPrefsUserGradePref();
                                wonderPubSharedPrefs.setSharedPrefsLastFilterGradeSearch(wonderPubSharedPrefs.getSharedPrefsUserGradePref().get(0));
                                String syllabusVal = TextUtils.join(", ", gradeList);
                                bundleSyllbus.putString(FirebaseAnalyticsUtils.ACTION, "UserPreferenceSelectionActivity Grade - "+syllabusVal);
                            }
                            firebaseAnalytics.logEvent(FirebaseAnalyticsUtils.LOCATION, bundleGrade);


                            if(wonderPubSharedPrefs.getSharedPrefsUserSubjectPref().size()>0)
                            {
                                wonderPubSharedPrefs.setSharedPrefsLastFilterSubjectSearch(wonderPubSharedPrefs.getSharedPrefsUserSubjectPref().get(wonderPubSharedPrefs.getSharedPrefsUserSubjectPref().size()-1));
                            }


                            //  Log.e("Data get",":"+wonderPubSharedPrefs.getSharedPrefsLastFilterGradeSearch()+":"+wonderPubSharedPrefs.getSharedPrefsLastFilterSyllabusSearch());

                            Bundle bundle = new Bundle();
                            bundle.putString(FirebaseAnalyticsUtils.ACTIVITY_NAME, "UserPreferenceSelectionActivity");
                            bundle.putString(FirebaseAnalyticsUtils.ACTION, "UserPreferenceSelectionActivity Done");
                            firebaseAnalytics.logEvent(FirebaseAnalyticsUtils.LOCATION, bundle);



                            if (comingFor.equalsIgnoreCase("change"))
                            {
                                if (BuildConfig.FLAVOR == Flavours.CACSCMA.getFlavour() || BuildConfig.FLAVOR == Flavours.CTET.getFlavour()) {
                                    preTempGrade.removeAll(wonderPubSharedPrefs.getSharedPrefsUserGradePref());
                                    if(preTempGrade.size()>=0)
                                    {
                                        String level = getString(R.string.level);
                                        List<String> syllabus = Arrays.asList(getString(R.string.syllabus));
                                        //wonderPubSharedPrefs.setSharedPrefsUserLevelPref(level);
                                        //wonderPubSharedPrefs.setSharedPrefsLastFilterLevelSearch(level);
                                        wonderPubSharedPrefs.setSharedPrefsUserSyllabusPref(syllabus);
                                        wonderPubSharedPrefs.setSharedPrefsLastFilterSyllabusSearch(syllabus.get(0));
                                    }
                                }
                                else if (BuildConfig.FLAVOR.equalsIgnoreCase(Flavours.ENGINEERING.getFlavour())) {
                                    preTempGrade.removeAll(wonderPubSharedPrefs.getSharedPrefsUserGradePref());
                                    if(preTempGrade.size()>=0)
                                    {
                                        wonderPubSharedPrefs.setSharedPrefsUserLevelPref("Engineering Entrances");
                                        wonderPubSharedPrefs.setSharedPrefsLastFilterLevelSearch("Engineering Entrances");
                                    }
                                }
                                Intent returnIntent = new Intent();
                                returnIntent.putExtra("PRE_STATUS", "change");
                                returnIntent.putExtra("CurrentTab",tabPos);
                                setResult(Activity.RESULT_OK, returnIntent);
                                finish();
                                //finish();
                            } else {
                                Intent returnIntent = new Intent();
                                returnIntent.putExtra("PRE_STATUS", "done");
                                setResult(Activity.RESULT_OK, returnIntent);
                                finish();
                            }
                            loaderMain.smoothToHide();
                        }
                    }
                    else
                    {
                        loaderMain.smoothToHide();
                        Toast.makeText(UserPreferanceSelectionActivity.this, "Please select your preference.", Toast.LENGTH_SHORT).show();
                    }
                } catch (Exception e) {
                    loaderMain.smoothToHide();
                    e.printStackTrace();
                }
                break;
            case R.id.button_back:
                try {
                    if (buttonBack.getTag().toString().equalsIgnoreCase("level")) {
                        if (wonderPubSharedPrefs.getSharedPrefsUserLevelPref() != null && wonderPubSharedPrefs.getSharedPrefsUserLevelPref().isEmpty()) {
                            buttonNextLevel.setEnabled(false);
                            buttonNextLevel.setBackgroundResource(R.drawable.button_shape_disabled);
                        } else {
                            buttonNextLevel.setEnabled(true);
                            buttonNextLevel.setBackgroundResource(R.drawable.button_shape_default);
                        }
                        recyclerViewLevel.setVisibility(View.VISIBLE);
                        recyclerViewSyllabus.setVisibility(View.GONE);
                        buttonBack.setVisibility(View.INVISIBLE);
                        rrltBack.setVisibility(View.INVISIBLE);
                        // buttonBack.setBackgroundResource(R.drawable.button_shape_disabled);
                        //buttonClear.setVisibility(View.INVISIBLE);
                        // textViewTitleLevel.setText("Please select your preferred ");
                        textViewTitleLevelStage.setTag(" Level.");
                        buttonNextLevel.setText("Next");
                        selectedLevelFinal = wonderPubSharedPrefs.getSharedPrefsUserLevelPref();
                        setLevelDataToGrid();
                    } else if (buttonBack.getTag().toString().equalsIgnoreCase("syllabus")) {
                        if (wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref() != null && wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().size() == 0) {
                            buttonNextLevel.setEnabled(false);
                            buttonNextLevel.setBackgroundResource(R.drawable.button_shape_disabled);
                        } else {
                            buttonNextLevel.setEnabled(true);
                            buttonNextLevel.setBackgroundResource(R.drawable.button_shape_default);
                        }
                        recyclerViewLevel.setVisibility(View.GONE);
                        recyclerViewSyllabus.setVisibility(View.VISIBLE);
                        recyclerViewGrade.setVisibility(View.GONE);
                        // textViewTitleLevel.setText("Please select your preferred ");
                        textViewTitleLevelStage.setTag(" Syllabus.");
                        buttonNextLevel.setText("Next");
                        syllabusList = wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref();
                        setSyllabusDataToGrid();
                    }
                    if ( BuildConfig.FLAVOR.equalsIgnoreCase(Flavours.CACSCMA.getFlavour()))
                    {
                        if (buttonBack.getTag().toString().equalsIgnoreCase("grade")) {
                            if (wonderPubSharedPrefs.getSharedPrefsUserGradePref() != null && wonderPubSharedPrefs.getSharedPrefsUserGradePref().size() == 0) {
                                buttonNextLevel.setEnabled(false);
                                buttonNextLevel.setBackgroundResource(R.drawable.button_shape_disabled);
                            } else {
                                buttonNextLevel.setEnabled(true);
                                buttonNextLevel.setBackgroundResource(R.drawable.button_shape_default);
                            }
                            recyclerViewLevel.setVisibility(View.GONE);
                            recyclerViewSyllabus.setVisibility(View.GONE);
                            recyclerViewGrade.setVisibility(View.VISIBLE);
                            recyclerViewSubject.setVisibility(View.GONE);
                            recyclerViewSubSubject.setVisibility(View.GONE);

                            // textViewTitleLevel.setText("Please select your preferred ");
                            textViewTitleLevelStage.setTag(" Grade.");
                            buttonNextLevel.setText("Next");
                            gradeList = wonderPubSharedPrefs.getSharedPrefsUserGradePref();
                            setGradeDataToGrid();
                        }
                        else if (buttonBack.getTag().toString().equalsIgnoreCase("subject")) {
                            if (wonderPubSharedPrefs.getSharedPrefsUserSubjectPref() != null && wonderPubSharedPrefs.getSharedPrefsUserSubjectPref().size() == 0) {
                                buttonNextLevel.setEnabled(false);
                                buttonNextLevel.setBackgroundResource(R.drawable.button_shape_disabled);
                            } else {
                                buttonNextLevel.setEnabled(true);
                                buttonNextLevel.setBackgroundResource(R.drawable.button_shape_default);
                            }
                            recyclerViewLevel.setVisibility(View.GONE);
                            recyclerViewSyllabus.setVisibility(View.GONE);
                            recyclerViewGrade.setVisibility(View.GONE);
                            recyclerViewSubject.setVisibility(View.VISIBLE);
                            recyclerViewSubSubject.setVisibility(View.GONE);
                            // textViewTitleLevel.setText("Please select your preferred ");
                            textViewTitleLevelStage.setTag(" Subject.");
                            buttonNextLevel.setText("Done");
                            subjectList = wonderPubSharedPrefs.getSharedPrefsUserSubjectPref();
                            setSubjectDataToGrid();
                        }
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;
            case R.id.button_clear:
                try {
                    buttonNextLevel.setEnabled(false);
                    buttonNextLevel.setBackgroundResource(R.drawable.button_shape_disabled);
                    if (recyclerViewLevel.getVisibility() == View.VISIBLE && userStudyPreference.getLevelPreference() != null && !userStudyPreference.getLevelPreference().isEmpty()) {
                        //wonderPubSharedPrefs.setSharedPrefsUserLevelPref("");
                        wonderPubSharedPrefs.setSharedPrefsLastFilterLevelSearch("");
                        selectedLevelFinal = "";
                        //clear syllanbus
                        syllabusList.clear();
                        syllabusListComplete.clear();
                        //wonderPubSharedPrefs.setSharedPrefsUserSyllabusPref(syllabusList);
                        //clear grade
                        gradeList.clear();
                        gradeListComplete.clear();
                        //wonderPubSharedPrefs.setSharedPrefsUserGradePref(gradeList);
                        userStudyPreference.setLevelPreference("");
                        userStudyPreference.setSyllabusPreference("");
                        userStudyPreference.setGradePreference("");
                        userPrefSelectLevelAdapter.setLevelDala(alLevelName, "");
                    }
                    else if (recyclerViewSyllabus.getVisibility() == View.VISIBLE && userStudyPreference.getSyllabusPreference() != null && !userStudyPreference.getSyllabusPreference().isEmpty())
                    {
                        syllabusList.clear();
                        syllabusListComplete.clear();
                        userStudyPreference.setSyllabusPreference("");
                        userStudyPreference.setGradePreference("");
                        setSyllabusDataToGrid();
                    }
                    else if (recyclerViewGrade.getVisibility() == View.VISIBLE && userStudyPreference.getGradePreference() != null && !userStudyPreference.getGradePreference().isEmpty() &&
                            !userStudyPreference.getSyllabusPreference().isEmpty())
                    {
                        gradeList.clear();
                        gradeListComplete.clear();
                        userStudyPreference.setGradePreference("");
                        setGradeDataToGrid();
                    }
                    else  if ( BuildConfig.FLAVOR.equalsIgnoreCase(Flavours.CACSCMA.getFlavour()))
                     {
                            if (recyclerViewSubject.getVisibility() == View.VISIBLE && wonderPubSharedPrefs.getSharedPrefsUserSubjectPref() != null && wonderPubSharedPrefs.getSharedPrefsUserSubjectPref().size() > 0 &&
                                 wonderPubSharedPrefs.getSharedPrefsUserGradePref().size() > 0)
                         {
                             subjectList.clear();
                             subjectListComplete.clear();
                             wonderPubSharedPrefs.setSharedPrefsUserSubjectPref(subjectList);
                             setSubjectDataToGrid();
                         }
                         else if (recyclerViewSubSubject.getVisibility() == View.VISIBLE && wonderPubSharedPrefs.getSharedPrefsUserSubSubjectPref() != null && wonderPubSharedPrefs.getSharedPrefsUserSubSubjectPref().size() > 0 &&
                                 wonderPubSharedPrefs.getSharedPrefsUserSubjectPref().size() > 0)
                         {
                             subSubjectList.clear();
                             subSubjectListComplete.clear();
                             wonderPubSharedPrefs.setSharedPrefsUserSubSubjectPref(subSubjectList);
                             setSubSubjectDataToGrid();
                         }
                         else {
                                Toast.makeText(UserPreferanceSelectionActivity.this, "Nothing selected.", Toast.LENGTH_SHORT).show();
                            }
                     }
                    else
                    {
                        Toast.makeText(UserPreferanceSelectionActivity.this, "Nothing selected.", Toast.LENGTH_SHORT).show();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;
        }
    }

    private void setLevelDataToGrid() {
        try {
            if (arrayAllFilterData != null) {
                alLevelName = new ArrayList<>();
                alLevelName = OpenUtils.getLevelFilterListData(arrayAllFilterData);
                userPrefSelectLevelAdapter = new UserPrefSelectLevelAdapter(mContext, this);
                textViewTitleLevelStage.setTag("Level");
                selectedLevelFinal = userStudyPreference.getLevelPreference();
                alLevelName.remove(0);
                userPrefSelectLevelAdapter.setLevelDala(alLevelName, selectedLevelFinal);
                recyclerViewLevel.setAdapter(userPrefSelectLevelAdapter);
                //onLevelSelected(selectedLevelFinal);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onLevelSelected(String selectedLevel) {
        try {
            buttonNextLevel.setEnabled(true);
            buttonNextLevel.setBackgroundResource(R.drawable.button_shape_default);
            userStudyPreference.setLevelPreference(selectedLevel);
            userStudyPreference.setSyllabusPreference("");
            userStudyPreference.setGradePreference("");
            //wonderPubSharedPrefs.setSharedPrefsUserLevelPref(selectedLevel);
            wonderPubSharedPrefs.setSharedPrefsLastFilterLevelSearch(selectedLevel);
            selectedLevelFinal = selectedLevel;
            //clear syllanbus
            syllabusList.clear();
            syllabusListComplete.clear();
            //wonderPubSharedPrefs.setSharedPrefsUserSyllabusPref(syllabusList);
            //clear grade
            gradeList.clear();
            gradeListComplete.clear();
            //wonderPubSharedPrefs.setSharedPrefsUserGradePref(gradeList);
            userPrefSelectLevelAdapter.setLevelDala(alLevelName, selectedLevel);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onLevelSelectedChange(String selectedLevel) {
        try {
            buttonNextLevel.setEnabled(true);
            buttonNextLevel.setBackgroundResource(R.drawable.button_shape_default);
            alLevelName = new ArrayList<>();
            alLevelName = OpenUtils.getLevelFilterListData(arrayAllFilterData);
            textViewTitleLevelStage.setTag("Level");
            userPrefSelectLevelAdapter = new UserPrefSelectLevelAdapter(mContext, this);
            alLevelName.remove(0);
            userPrefSelectLevelAdapter.setLevelDala(alLevelName, selectedLevel);
            recyclerViewLevel.setAdapter(userPrefSelectLevelAdapter);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void setSyllabusDataToGrid() {
        try {
            recyclerViewLevel.setVisibility(View.GONE);
            recyclerViewSyllabus.setVisibility(View.VISIBLE);
            buttonClear.setVisibility(View.GONE);
            buttonBack.setVisibility(View.VISIBLE);
            rrltBack.setVisibility(View.VISIBLE);
            // buttonBack.setBackgroundResource(R.drawable.button_shape_disabled);
            buttonBack.setTag("Level");
            // textViewTitleLevel.setText("Please select your preferred ");
            textViewTitleLevelStage.setTag(" Syllabus.");
            if(BuildConfig.FLAVOR.equalsIgnoreCase(Flavours.ENGINEERING.getFlavour() ))
            {
                buttonNextLevel.setText("Done");
            }
            else {
                buttonNextLevel.setText("Next");
            }

            alSyllabusName = new ArrayList<>();
            alSyllabusName = OpenUtils.getSyllabusFilterListData(arrayAllFilterData, userStudyPreference.getLevelPreference());
            userPrefSelectSyllabusAdapter = new UserPrefSelectSyllabusAdapter(mContext, this);
            alSyllabusName.remove(0);
            Collections.sort(alSyllabusName);

            syllabusListComplete.clear();
            for (int i = 0; i < alSyllabusName.size(); i++) {
                SyllabusPrefModel syllabusPrefModel = new SyllabusPrefModel();
                syllabusPrefModel.setSyllabusName(alSyllabusName.get(i));
                syllabusPrefModel.setSelected(false);
                syllabusListComplete.add(syllabusPrefModel);
            }

            // Collections.sort(syllabusList);
            userPrefSelectSyllabusAdapter.setSyllabusDala(alSyllabusName, syllabusList);
            recyclerViewSyllabus.setAdapter(userPrefSelectSyllabusAdapter);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onSyllabusSelected(String selectedSyllabus, int position) {
        try {
            buttonNextLevel.setEnabled(true);
            buttonNextLevel.setBackgroundResource(R.drawable.button_shape_default);
            // syllabusList.add(selectedSyllabus);
            //syllabusList.add(selectedSyllabus);
            syllabusList.clear();
            //syllabusListComplete.clear();

            for (int i=0;i<syllabusListComplete.size();i++) {
                syllabusListComplete.get(i).setSelected(false);
            }

            syllabusListComplete.get(position).setSelected(true);
            if (!syllabusList.contains(selectedSyllabus)) {
                syllabusList.add(selectedSyllabus);
            }
            //wonderPubSharedPrefs.setSharedPrefsUserSyllabusPref(syllabusList);
            userStudyPreference.setSyllabusPreference(selectedSyllabus);
            userStudyPreference.setGradePreference("");
            if (userStudyPreference.getSyllabusPreference() != null && !userStudyPreference.getSyllabusPreference().isEmpty()) {
                wonderPubSharedPrefs.setSharedPrefsLastFilterSyllabusSearch(userStudyPreference.getSyllabusPreference());
                buttonNextLevel.setEnabled(true);
                buttonNextLevel.setBackgroundResource(R.drawable.button_shape_default);
            } else {
                buttonNextLevel.setEnabled(false);
                buttonNextLevel.setBackgroundResource(R.drawable.button_shape_disabled);
            }
            gradeList.clear();
            gradeListComplete.clear();

            selectedSyllabusFinal = userStudyPreference.getSyllabusPreference();
            //To remove duplicate
            HashSet<String> hashSet = new HashSet<String>();
            hashSet.addAll(syllabusList);
            syllabusList.clear();
            syllabusList.addAll(hashSet);
            userPrefSelectSyllabusAdapter.setSyllabusDala(alSyllabusName, syllabusList);
           // recyclerViewSyllabus.setAdapter(userPrefSelectSyllabusAdapter);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onSyllabusSelectedChange(List<String> selectedSyllabus) {
        try {
            buttonNextLevel.setEnabled(true);
            buttonNextLevel.setBackgroundResource(R.drawable.button_shape_default);
            recyclerViewLevel.setVisibility(View.GONE);
            recyclerViewSyllabus.setVisibility(View.VISIBLE);
            buttonClear.setVisibility(View.GONE);
            buttonBack.setVisibility(View.INVISIBLE);
            rrltBack.setVisibility(View.INVISIBLE);
            // buttonBack.setBackgroundResource(R.drawable.button_shape_disabled);
            buttonBack.setTag("Level");
            // textViewTitleLevel.setText("Please select your preferred ");
            textViewTitleLevelStage.setTag(" Syllabus.");
            if(BuildConfig.FLAVOR.equalsIgnoreCase(Flavours.ENGINEERING.getFlavour() ))
            {
                buttonNextLevel.setText("Done");
            }
            else {
                buttonNextLevel.setText("Next");
            }
            alSyllabusName = new ArrayList<>();
            alSyllabusName = OpenUtils.getSyllabusFilterListData(arrayAllFilterData, userStudyPreference.getLevelPreference());
            userPrefSelectSyllabusAdapter = new UserPrefSelectSyllabusAdapter(mContext, this);
            alSyllabusName.remove(0);
            // Collections.sort(selectedSyllabus);
            Collections.sort(alSyllabusName);

            syllabusListComplete.clear();
            for (int i = 0; i < alSyllabusName.size(); i++) {
                SyllabusPrefModel syllabusPrefModel = new SyllabusPrefModel();
                syllabusPrefModel.setSyllabusName(alSyllabusName.get(i));
                for (int j = 0; j < selectedSyllabus.size(); j++) {
                    if (selectedSyllabus.get(j).equalsIgnoreCase(alSyllabusName.get(i))) {
                        syllabusPrefModel.setSelected(true);
                    } else {
                        syllabusPrefModel.setSelected(false);
                    }

                }
                syllabusListComplete.add(syllabusPrefModel);
            }
            userPrefSelectSyllabusAdapter.setSyllabusDala(alSyllabusName, selectedSyllabus);
            recyclerViewSyllabus.setAdapter(userPrefSelectSyllabusAdapter);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void setGradeDataToGrid() {
        try {
            recyclerViewLevel.setVisibility(View.GONE);
            recyclerViewSyllabus.setVisibility(View.GONE);
            recyclerViewGrade.setVisibility(View.VISIBLE);
            buttonClear.setVisibility(View.GONE);

            if (BuildConfig.FLAVOR == Flavours.CACSCMA.getFlavour() || BuildConfig.FLAVOR == Flavours.CTET.getFlavour()) {
                buttonBack.setVisibility(View.INVISIBLE);
                rrltBack.setVisibility(View.INVISIBLE);
            }
            else {
                buttonBack.setVisibility(View.VISIBLE);
                rrltBack.setVisibility(View.VISIBLE);
            }
              if ( BuildConfig.FLAVOR.equalsIgnoreCase(Flavours.CACSCMA.getFlavour()))
            {
                // buttonBack.setBackgroundResource(R.drawable.button_shape_default);
                buttonBack.setTag("Syllabus");
                // textViewTitleLevel.setText("Please select your preferred ");
                textViewTitleLevelStage.setTag(" Grade.");
                buttonNextLevel.setText("Next");
            }
              else
              {
                  // buttonBack.setBackgroundResource(R.drawable.button_shape_default);
                  buttonBack.setTag("Syllabus");
                  // textViewTitleLevel.setText("Please select your preferred ");
                  textViewTitleLevelStage.setTag(" Grade.");
                  buttonNextLevel.setText("Done");
              }


            alGradeName = new ArrayList<>();
            ArrayList<String> alGradeNameTemp = new ArrayList<>();
            if (userStudyPreference.getSyllabusPreference() != null && !userStudyPreference.getSyllabusPreference().isEmpty())
            {
                alGradeName = OpenUtils.getGradeFilterListData(arrayAllFilterData, selectedLevelFinal, userStudyPreference.getSyllabusPreference());
                alGradeName.remove(0);
               // pGradeList.put(wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0),alGradeName);
                pGradeList.put(userStudyPreference.getSyllabusPreference(),
                        OpenUtils.getGradeFilterListData(arrayAllFilterData, selectedLevelFinal, userStudyPreference.getSyllabusPreference()));
                Collections.sort(alGradeName);
            }
            else
            {
                for (int i = 0; i < wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().size(); i++)
                {
                    alGradeName = OpenUtils.getGradeFilterListData(arrayAllFilterData, selectedLevelFinal, wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(i));
                    alGradeName.remove(0);
                    pGradeList.put(wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(i),OpenUtils.getGradeFilterListData(arrayAllFilterData, selectedLevelFinal, wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(i)));
                   // pGradeList.put(wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(i),alGradeName);
                    alGradeNameTemp.addAll(alGradeName);
                    alGradeName.clear();
                }
                alGradeName.addAll(alGradeNameTemp);
                Collections.sort(alGradeName);
            }
            // Collections.sort(gradeList);
            userPrefSelectGradeAdapter = new UserPrefSelectGradeAdapter(mContext, this);
            gradeListComplete.clear();
            for (int i = 0; i < alGradeName.size(); i++) {
                GradePrefModel gradePrefModel = new GradePrefModel();
                gradePrefModel.setGradeName(alGradeName.get(i));
                gradePrefModel.setSelected(false);
                gradeListComplete.add(gradePrefModel);
            }

            if (userStudyPreference.getLevelPreference().equalsIgnoreCase("school")) {
                Collections.sort(alGradeName, new Comparator<String>() {
                    @Override
                    public int compare(String o1, String o2) {
                        return Integer.valueOf(o1).compareTo(Integer.valueOf(o2));
                    }
                });
            }

            userPrefSelectGradeAdapter.setGradeDala(alGradeName, gradeList);
            recyclerViewGrade.setAdapter(userPrefSelectGradeAdapter);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onGradeSelected(String selectedGrade, int position) {
        try {
            buttonNextLevel.setEnabled(true);
            buttonNextLevel.setBackgroundResource(R.drawable.button_shape_default);
            // gradeList.add(selectedGrade);
            gradeList.clear();

            for (int i=0;i<gradeListComplete.size();i++) {
                gradeListComplete.get(i).setSelected(false);
            }

            gradeListComplete.get(position).setSelected(true);
            if (!gradeList.contains(selectedGrade)) {
                gradeList.add(selectedGrade);
            }

            userStudyPreference.setGradePreference(selectedGrade);

            if (userStudyPreference.getGradePreference() != null && !userStudyPreference.getGradePreference().isEmpty()) {
                selectedGradeFinal = userStudyPreference.getGradePreference();
                buttonNextLevel.setEnabled(true);
                buttonNextLevel.setBackgroundResource(R.drawable.button_shape_default);
            } else {
                buttonNextLevel.setEnabled(false);
                buttonNextLevel.setBackgroundResource(R.drawable.button_shape_disabled);
            }

            //To remove duplicate
            HashSet<String> hashSet = new HashSet<String>();
            hashSet.addAll(gradeList);
            gradeList.clear();
            gradeList.addAll(hashSet);

            userPrefSelectGradeAdapter.setGradeDala(alGradeName, gradeList);


        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onGradeSelectedChange(List<String> selectedGrade) {
        try {
            if (!gradeList.isEmpty()) {
                buttonNextLevel.setEnabled(true);
                buttonNextLevel.setBackgroundResource(R.drawable.button_shape_default);
            }
            recyclerViewLevel.setVisibility(View.GONE);
            recyclerViewSyllabus.setVisibility(View.GONE);
            recyclerViewGrade.setVisibility(View.VISIBLE);
            buttonClear.setVisibility(View.GONE);

            if (BuildConfig.FLAVOR == Flavours.CACSCMA.getFlavour() || BuildConfig.FLAVOR == Flavours.CTET.getFlavour()) {
                buttonBack.setVisibility(View.INVISIBLE);
                rrltBack.setVisibility(View.INVISIBLE);
            }
            else {
                buttonBack.setVisibility(View.VISIBLE);
                rrltBack.setVisibility(View.VISIBLE);
            }
         /*   // buttonBack.setBackgroundResource(R.drawable.button_shape_default);
            buttonBack.setTag("Syllabus");
            // textViewTitleLevel.setText("Please select your preferred ");
            textViewTitleLevelStage.setTag(" Grade.");
            buttonNextLevel.setText("Done");*/

            if (BuildConfig.FLAVOR.equalsIgnoreCase(Flavours.CACSCMA.getFlavour())) {
                // buttonBack.setBackgroundResource(R.drawable.button_shape_default);
                buttonBack.setTag("Syllabus");
                // textViewTitleLevel.setText("Please select your preferred ");
                textViewTitleLevelStage.setTag(" Grade.");
                buttonNextLevel.setText("Next");
            } else {
                // buttonBack.setBackgroundResource(R.drawable.button_shape_default);
                buttonBack.setTag("Syllabus");
                // textViewTitleLevel.setText("Please select your preferred ");
                textViewTitleLevelStage.setTag(" Grade.");
                buttonNextLevel.setText("Done");
            }


            alGradeName = new ArrayList<>();
            ArrayList<String> alGradeNameTemp = new ArrayList<>();
            if (!userStudyPreference.getSyllabusPreference().isEmpty()) {
                selectedLevelFinal = userStudyPreference.getLevelPreference();
                alGradeName = OpenUtils.getGradeFilterListData(arrayAllFilterData, selectedLevelFinal, userStudyPreference.getSyllabusPreference());
                alGradeName.remove(0);

                if (userStudyPreference.getLevelPreference().equalsIgnoreCase("school")) {
                    Collections.sort(alGradeName, new Comparator<String>() {
                        @Override
                        public int compare(String o1, String o2) {
                            return Integer.valueOf(o1).compareTo(Integer.valueOf(o2));
                        }
                    });
                }
                //  pGradeList.put(wonderPubSharedPrefs.getSharedPrefsUserSyllabusPref().get(0),alGradeName);
                pGradeList.put(userStudyPreference.getSyllabusPreference(), OpenUtils.getGradeFilterListData(arrayAllFilterData,
                        selectedLevelFinal, userStudyPreference.getSyllabusPreference()));
            } else {
                alGradeName = OpenUtils.getGradeFilterListData(arrayAllFilterData, selectedLevelFinal, userStudyPreference.getSyllabusPreference());
                alGradeName.remove(0);
                pGradeList.put(userStudyPreference.getSyllabusPreference(), OpenUtils.getGradeFilterListData(arrayAllFilterData, selectedLevelFinal,
                        userStudyPreference.getSyllabusPreference()));
                alGradeNameTemp.addAll(alGradeName);
                alGradeName.clear();
                alGradeName.addAll(alGradeNameTemp);
                Collections.sort(alGradeName);
            }
            userPrefSelectGradeAdapter = new UserPrefSelectGradeAdapter(mContext, this);
            // Collections.sort(selectedGrade);


            gradeListComplete.clear();
            for (int i = 0; i < alGradeName.size(); i++) {
                GradePrefModel gradePrefModel = new GradePrefModel();
                gradePrefModel.setGradeName(alGradeName.get(i));
                for (int j = 0; j < selectedGrade.size(); j++) {
                    if (selectedGrade.get(j).equalsIgnoreCase(alGradeName.get(i))) {
                        gradePrefModel.setSelected(true);
                    } else {
                        gradePrefModel.setSelected(false);
                    }

                }
                gradeListComplete.add(gradePrefModel);
            }
            userPrefSelectGradeAdapter.setGradeDala(alGradeName, selectedGrade);
            recyclerViewGrade.setAdapter(userPrefSelectGradeAdapter);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void setSubjectDataToGrid() {
        try {
            recyclerViewLevel.setVisibility(View.GONE);
            recyclerViewSyllabus.setVisibility(View.GONE);
            recyclerViewGrade.setVisibility(View.GONE);
            recyclerViewSubject.setVisibility(View.VISIBLE);
            recyclerViewSubSubject.setVisibility(View.GONE);
            buttonClear.setVisibility(View.GONE);

            buttonBack.setVisibility(View.VISIBLE);
            rrltBack.setVisibility(View.VISIBLE);

            // buttonBack.setBackgroundResource(R.drawable.button_shape_default);
            buttonBack.setTag("Grade");
            // textViewTitleLevel.setText("Please select your preferred ");
            textViewTitleLevelStage.setTag(" Subject.");
            buttonNextLevel.setText("Next");
            alSubjectName = new ArrayList<>();
            ArrayList<String> alSubjectNameTemp = new ArrayList<>();
            if (wonderPubSharedPrefs.getSharedPrefsUserGradePref().size() != 0 && wonderPubSharedPrefs.getSharedPrefsUserGradePref().size() == 1) {
                alSubjectName = OpenUtils.getClassFilterListData(arrayAllFilterData, selectedLevelFinal, selectedSyllabusFinal, wonderPubSharedPrefs.getSharedPrefsUserGradePref().get(0));
                alSubjectName.remove(0);
                Collections.sort(alSubjectName);
            } else {
                for (int i = 0; i < wonderPubSharedPrefs.getSharedPrefsUserGradePref().size(); i++) {
                    alSubjectName = OpenUtils.getClassFilterListData(arrayAllFilterData, selectedLevelFinal, selectedSyllabusFinal, wonderPubSharedPrefs.getSharedPrefsUserGradePref().get(i));
                    alSubjectName.remove(0);
                    alSubjectNameTemp.addAll(alSubjectName);
                    alSubjectName.clear();
                }
                alSubjectName.addAll(alSubjectNameTemp);
                Collections.sort(alSubjectName);
            }
            // Collections.sort(gradeList);
            userPrefSelectSubjectAdapter = new UserPrefSelectSubjectAdapter(mContext, this);
            subjectListComplete.clear();
            for (int i = 0; i < alSubjectName.size(); i++) {
                SubjectPrefModel gradePrefModel = new SubjectPrefModel();
                gradePrefModel.setSubjectName(alSubjectName.get(i));
                gradePrefModel.setSelected(false);
                subjectListComplete.add(gradePrefModel);
            }
            recyclerViewSubject.setVisibility(View.VISIBLE);
            userPrefSelectSubjectAdapter.setSubjectData(alSubjectName, subjectList);
            recyclerViewSubject.setAdapter(userPrefSelectSubjectAdapter);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onSubjectSelected(String selectedGrade, int position) {
        try {
            buttonNextLevel.setEnabled(true);
            buttonNextLevel.setBackgroundResource(R.drawable.button_shape_default);
            // gradeList.add(selectedGrade);
            if (subjectListComplete.get(position).isSelected()) {
                subjectListComplete.get(position).setSelected(false);
                for (int i = 0; i < subjectList.size(); i++) {
                    if (subjectList.get(i).equalsIgnoreCase(selectedGrade)) {
                        subjectList.remove(i);
                    }
                }
            } else {
                subjectListComplete.get(position).setSelected(true);
                subjectList.add(selectedGrade);
            }

            wonderPubSharedPrefs.setSharedPrefsUserSubjectPref(subjectList);
            if (wonderPubSharedPrefs.getSharedPrefsUserSubjectPref().size() > 0) {
                selectedSubjectFinal = wonderPubSharedPrefs.getSharedPrefsUserSubjectPref().get(0);
                buttonNextLevel.setEnabled(true);
                buttonNextLevel.setBackgroundResource(R.drawable.button_shape_default);
            } else {
                buttonNextLevel.setEnabled(false);
                buttonNextLevel.setBackgroundResource(R.drawable.button_shape_disabled);
            }

            //To remove duplicate
            HashSet<String> hashSet = new HashSet<String>();
            hashSet.addAll(subjectList);
            subjectList.clear();
            subjectList.addAll(hashSet);
            userPrefSelectSubjectAdapter.setSubjectData(alSubjectName, subjectList);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onSubjectSelectedChange(List<String> selectedSubject) {
        try {
            if(selectedSubject != null) {
                if(selectedSubject.isEmpty()) {
                    buttonNextLevel.setEnabled(false);
                    buttonNextLevel.setBackgroundResource(R.drawable.button_shape_disabled);
                } else {
                    buttonNextLevel.setEnabled(true);
                    buttonNextLevel.setBackgroundResource(R.drawable.button_shape_default);
                }
            }
            recyclerViewLevel.setVisibility(View.GONE);
            recyclerViewSyllabus.setVisibility(View.GONE);
            recyclerViewGrade.setVisibility(View.GONE);
            recyclerViewSubject.setVisibility(View.VISIBLE);
            recyclerViewSubSubject.setVisibility(View.GONE);
            buttonClear.setVisibility(View.GONE);

            buttonBack.setVisibility(View.VISIBLE);
            rrltBack.setVisibility(View.VISIBLE);


            // buttonBack.setBackgroundResource(R.drawable.button_shape_default);
            buttonBack.setTag("Grade");
            // textViewTitleLevel.setText("Please select your preferred ");
            textViewTitleLevelStage.setTag(" Subject.");
            buttonNextLevel.setText("Next");
            alSubjectName = new ArrayList<>();
            ArrayList<String> alSubjectNameTemp = new ArrayList<>();
            if (wonderPubSharedPrefs.getSharedPrefsUserGradePref().size() != 0 && wonderPubSharedPrefs.getSharedPrefsUserGradePref().size() == 1) {
                alSubjectName = OpenUtils.getClassFilterListData(arrayAllFilterData, selectedLevelFinal, selectedSyllabusFinal,wonderPubSharedPrefs.getSharedPrefsUserGradePref().get(0));
                alSubjectName.remove(0);
                Collections.sort(alSubjectName);
            } else {
                for (int i = 0; i < wonderPubSharedPrefs.getSharedPrefsUserGradePref().size(); i++) {
                    alSubjectName = OpenUtils.getClassFilterListData(arrayAllFilterData, selectedLevelFinal,selectedSyllabusFinal, wonderPubSharedPrefs.getSharedPrefsUserGradePref().get(i));
                    alSubjectName.remove(0);
                    alSubjectNameTemp.addAll(alSubjectName);
                    alSubjectName.clear();
                }
                alSubjectName.addAll(alSubjectNameTemp);
                Collections.sort(alSubjectName);

            }
            userPrefSelectSubjectAdapter = new UserPrefSelectSubjectAdapter(mContext, this);
            // Collections.sort(selectedGrade);

            subjectListComplete.clear();
            for (int i = 0; i < alSubjectName.size(); i++) {
                SubjectPrefModel subjectPrefModel = new SubjectPrefModel();
                subjectPrefModel.setSubjectName(alSubjectName.get(i));
                for (int j = 0; j < selectedSubject.size(); j++) {
                    if (selectedSubject.get(j).equalsIgnoreCase(alSubjectName.get(i))) {
                        subjectPrefModel.setSelected(true);
                    } else {
                        subjectPrefModel.setSelected(false);
                    }

                }
                subjectListComplete.add(subjectPrefModel);
            }
            userPrefSelectSubjectAdapter.setSubjectData(alSubjectName, selectedSubject);
            recyclerViewSubject.setAdapter(userPrefSelectSubjectAdapter);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private void setSubSubjectDataToGrid() {
        try {
            recyclerViewLevel.setVisibility(View.GONE);
            recyclerViewSyllabus.setVisibility(View.GONE);
            recyclerViewGrade.setVisibility(View.GONE);
            recyclerViewSubject.setVisibility(View.GONE);
            recyclerViewSubSubject.setVisibility(View.VISIBLE);
            buttonClear.setVisibility(View.GONE);

            buttonBack.setVisibility(View.VISIBLE);
            rrltBack.setVisibility(View.VISIBLE);

            // buttonBack.setBackgroundResource(R.drawable.button_shape_default);
            buttonBack.setTag("Subject");
            // textViewTitleLevel.setText("Please select your preferred ");
            textViewTitleLevelStage.setTag(" SubSubject.");
            buttonNextLevel.setText("Done");
            alSubSubjectName = new ArrayList<>();
            alSubSubjectName.add("May 2022");
            alSubSubjectName.add("Nov 2022");
            // Collections.sort(gradeList);
            userPrefSelectSubSubjectAdapter = new UserPrefSelectSubjectSubAdapter(mContext, this);
           subSubjectListComplete .clear();
            for (int i = 0; i < alSubSubjectName.size(); i++) {
                SubSubjectPrefModel gradePrefModel = new SubSubjectPrefModel();
                gradePrefModel.setSubSubjectName(alSubSubjectName.get(i));
                gradePrefModel.setSelected(false);
                subSubjectListComplete.add(gradePrefModel);
            }
            userPrefSelectSubSubjectAdapter.setSubSubjectData(alSubSubjectName, subSubjectList);
            recyclerViewSubSubject.setAdapter(userPrefSelectSubSubjectAdapter);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onSubSubjectSelected(String selectedGrade, int position) {
        try {
            buttonNextLevel.setEnabled(true);
            buttonNextLevel.setBackgroundResource(R.drawable.button_shape_default);
            // gradeList.add(selectedGrade);
            if (subSubjectListComplete.get(position).isSelected()) {
                subSubjectListComplete.get(position).setSelected(false);
                for (int i = 0; i < subSubjectList.size(); i++) {
                    if (subSubjectList.get(i).equalsIgnoreCase(selectedGrade)) {
                        subSubjectList.remove(i);
                    }
                }
            } else {
                subSubjectListComplete.get(position).setSelected(true);
                subSubjectList.add(selectedGrade);
            }

            wonderPubSharedPrefs.setSharedPrefsUserSubSubjectPref(subSubjectList);
            if (wonderPubSharedPrefs.getSharedPrefsUserSubSubjectPref().size() > 0) {
                selectedSubSubjectFinal = wonderPubSharedPrefs.getSharedPrefsUserSubSubjectPref().get(0);
                buttonNextLevel.setEnabled(true);
                buttonNextLevel.setBackgroundResource(R.drawable.button_shape_default);
            } else {
                buttonNextLevel.setEnabled(false);
                buttonNextLevel.setBackgroundResource(R.drawable.button_shape_disabled);
            }

            //To remove duplicate
            HashSet<String> hashSet = new HashSet<String>();
            hashSet.addAll(subSubjectList);
            subSubjectList.clear();
            subSubjectList.addAll(hashSet);
            userPrefSelectSubSubjectAdapter.setSubSubjectData(alSubSubjectName, subSubjectList);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onSubSubjectSelectedChange(List<String> selectedSubject) {
        try {
            if(selectedSubject != null) {
                if(selectedSubject.isEmpty()) {
                    buttonNextLevel.setEnabled(false);
                    buttonNextLevel.setBackgroundResource(R.drawable.button_shape_disabled);
                } else {
                    buttonNextLevel.setEnabled(true);
                    buttonNextLevel.setBackgroundResource(R.drawable.button_shape_default);
                }
            }
            recyclerViewLevel.setVisibility(View.GONE);
            recyclerViewSyllabus.setVisibility(View.GONE);
            recyclerViewGrade.setVisibility(View.GONE);
            recyclerViewSubject.setVisibility(View.GONE);
            recyclerViewSubSubject.setVisibility(View.VISIBLE);
            buttonClear.setVisibility(View.GONE);

            buttonBack.setVisibility(View.VISIBLE);
            rrltBack.setVisibility(View.VISIBLE);

            // buttonBack.setBackgroundResource(R.drawable.button_shape_default);
            buttonBack.setTag("Subject");
            // textViewTitleLevel.setText("Please select your preferred ");
            textViewTitleLevelStage.setTag(" SubSubject.");
            buttonNextLevel.setText("Done");
            alSubSubjectName = new ArrayList<>();

            alSubSubjectName.add("May 2022");
            alSubSubjectName.add("Nov 2022");

            userPrefSelectSubSubjectAdapter = new UserPrefSelectSubjectSubAdapter(mContext, this);
            // Collections.sort(selectedGrade);

            subSubjectListComplete.clear();
            for (int i = 0; i < alSubSubjectName.size(); i++) {
                SubSubjectPrefModel subSubjectPrefModel = new SubSubjectPrefModel();
                subSubjectPrefModel.setSubSubjectName(alSubSubjectName.get(i));
                for (int j = 0; j < selectedSubject.size(); j++) {
                    if (selectedSubject.get(j).equalsIgnoreCase(alSubSubjectName.get(i))) {
                        subSubjectPrefModel.setSelected(true);
                    } else {
                        subSubjectPrefModel.setSelected(false);
                    }
                }
                subSubjectListComplete.add(subSubjectPrefModel);
            }
            userPrefSelectSubSubjectAdapter.setSubSubjectData(alSubSubjectName, selectedSubject);
            recyclerViewSubSubject.setAdapter(userPrefSelectSubSubjectAdapter);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private String getAssetsData(String fileName) {
        String jsonString;
        try {
            InputStream is = mContext.getAssets().open(fileName);
            int size = is.available();
            byte[] buffer = new byte[size];
            is.read(buffer);
            is.close();
            jsonString = new String(buffer, "UTF-8");
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
        return jsonString;
    }
}