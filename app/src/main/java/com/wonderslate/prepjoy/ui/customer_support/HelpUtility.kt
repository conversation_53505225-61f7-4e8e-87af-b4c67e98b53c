package com.wonderslate.prepjoy.ui.customer_support

import android.content.Context
import android.content.Intent
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.graphics.Typeface
import android.graphics.drawable.ColorDrawable
import android.net.Uri
import android.os.Build
import android.view.View
import androidx.core.content.ContextCompat
import com.skydoves.powermenu.CircularEffect
import com.skydoves.powermenu.MenuAnimation
import com.skydoves.powermenu.OnMenuItemClickListener
import com.skydoves.powermenu.PowerMenu
import com.skydoves.powermenu.PowerMenuItem
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.prepjoy.BuildConfig
import com.wonderslate.prepjoy.R
import java.net.URLEncoder


class HelpUtility(private val context: Context) {

    private lateinit var helpMenu: PowerMenu

    fun showHelpOptions(view: View) {
        helpMenu = PowerMenu.Builder(context)
                .addItem(PowerMenuItem(WHATSAPP, false)) // add an item.
                .addItem(PowerMenuItem(EMAIL, false)) // aad an item list.
                .setMenuRadius(10f) // sets the corner radius.
                .setMenuShadow(10f) // sets the shadow.
                .setTextColor(ContextCompat.getColor(context, R.color.black))
                .setTextTypeface(Typeface.create("sans-serif-medium", Typeface.BOLD))
                .setIconSize(20)
                .setIconPadding(5)
                .setCircularEffect(CircularEffect.BODY)
                .setAnimation(MenuAnimation.SHOWUP_TOP_RIGHT)
                .setOnMenuItemClickListener(onMenuItemClickListener)
                .setDivider(ColorDrawable(ContextCompat.getColor(context, android.R.color.darker_gray))) // sets a divider.
                .setDividerHeight(1) // sets the divider height.
                .build()
        helpMenu.showAsAnchorLeftBottom(view)
    }

    private val onMenuItemClickListener: OnMenuItemClickListener<PowerMenuItem?> = OnMenuItemClickListener<PowerMenuItem?> { _, item ->
        val message = getHelpMessage()

        helpMenu.dismiss()
        if (item?.title!! == WHATSAPP) {
            openWhatsAppChat(context.getString(R.string.support_number), message)
        } else {
            openEmail(
                    context.getString(R.string.support_email),
                    message
            )
        }
    }

    private fun getHelpMessage(): String {
        var packageName: String
        try {
            packageName = context::class.simpleName?.replace("ui.", "").toString()
        } catch (e: PackageManager.NameNotFoundException) {
            packageName = context.packageName
            e.printStackTrace()
        }
        val sb = StringBuilder()
        sb.append(SUPPORT_MESSAGE)
                .append(ANDROID_VERSION).append(Build.VERSION.RELEASE)
                .append(APP_NAME).append(context.getString(R.string.app_name))
                .append(APP_VERSION).append(BuildConfig.VERSION_NAME)
                .append(PHONE_MODEL).append(Build.MODEL)
                .append(USER_MOBILE).append(WonderPubSharedPrefs.getInstance(context).usermobile)
                .append(USER_NAME).append(WonderPubSharedPrefs.getInstance(context).username)
                .append(USER_EMAIL).append(WonderPubSharedPrefs.getInstance(context).useremail)
                .append(PAGE).append(packageName)
                .append("\n")
        return sb.toString()
    }

    private fun openEmail(address: String, body: String) {
        val intent = Intent(Intent.ACTION_SEND)
        intent.type = "plain/text"
        intent.putExtra(Intent.EXTRA_EMAIL, arrayOf(address))
        intent.putExtra(Intent.EXTRA_SUBJECT, EMAIL_SUBJECT)
        intent.putExtra(Intent.EXTRA_TEXT, body)
        context.startActivity(Intent.createChooser(intent, EMAIL_TITLE))
    }

    private fun openWhatsAppChat(toNumber: String, message: String) {
        val url = "https://api.whatsapp.com/send?phone=$toNumber&text=" + URLEncoder.encode(
                message,
                "UTF-8"
        )
        try {
            context.packageManager.getPackageInfoCompat("com.whatsapp", PackageManager.GET_ACTIVITIES)
            context.startActivity(Intent(Intent.ACTION_VIEW).apply { data = Uri.parse(url) })
        } catch (e: PackageManager.NameNotFoundException) {
            context.startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(url)))
        }
    }

    private fun PackageManager.getPackageInfoCompat(packageName: String, flags: Int = 0): PackageInfo =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                getPackageInfo(packageName, PackageManager.PackageInfoFlags.of(flags.toLong()))
            } else {
                @Suppress("DEPRECATION") getPackageInfo(packageName, flags)
            }

    companion object {
        private const val EMAIL_SUBJECT = "Support Request"
        private const val EMAIL_TITLE = "Help"
        private const val SUPPORT_MESSAGE = "Hello\nI am in need of help.\n"
        private const val ANDROID_VERSION = "\nAndroid Version: "
        private const val APP_NAME = "\nApp Name: "
        private const val APP_VERSION = "\nApp Version: v"
        private const val PHONE_MODEL = "\nPhone: "
        private const val USER_MOBILE = "\nUser Mobile: "
        private const val USER_NAME = "\nUser Name: "
        private const val USER_EMAIL = "\nUser Email: "
        private const val PAGE = "\nPage: "
        private const val WHATSAPP = "WhatsApp"
        private const val EMAIL = "Email"
    }
}
