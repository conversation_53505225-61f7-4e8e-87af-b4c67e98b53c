package com.wonderslate.prepjoy.ui.ebooks

import android.view.LayoutInflater
import android.view.ViewGroup
import com.google.android.material.tabs.TabLayoutMediator
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.prepjoy.BuildConfig
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.Utils.Flavours
import com.wonderslate.prepjoy.databinding.EbooksfragmentBinding
import com.wonderslate.prepjoy.ui.dashboard.DashBoardActivity
import com.wonderslate.prepjoy.ui.library.LibraryBooksFrag
import com.ws.commons.enums.EventType
import com.ws.commons.interfaces.EventLogger
import com.ws.commons.models.Event
import com.ws.core_ui.base.BaseFragment
import com.ws.core_ui.extensions.addOnTabSelectedListener
import com.ws.core_ui.utils.FragmentData
import com.ws.core_ui.utils.FragmentViewPager2Adapter
import com.ws.shop.data.models.ShopBooksFragConfig
import com.ws.shop.ui.ShopBooksFrag
import org.koin.android.ext.android.inject

class EbooksFragment: BaseFragment<EbooksfragmentBinding>() {

    private val fragments = arrayListOf<FragmentData>()

    private val eventLogger by inject<EventLogger>()

    private var libraryBooksFrag: LibraryBooksFrag? = null

    override fun inflateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): EbooksfragmentBinding {
        return EbooksfragmentBinding.inflate(inflater, container, false)
    }

    override fun initView() {
        prepareFragments()
        initViewPager()
        initTabs()
        if (WonderPubSharedPrefs.getInstance(requireContext()).shouldRedirectToLibrary()) {
            selectTab(1)
        }
        else {
            WonderPubSharedPrefs.getInstance(requireContext()).setRedirectToLibrary(false)
        }
    }

    override fun onResume() {
        super.onResume()
        val visibility = (!BuildConfig.FLAVOR.equals(Flavours.NEET.flavour, ignoreCase = true))
        (requireActivity() as DashBoardActivity).showOrHideUserPreference(visibility)

    }
    override fun load() {

    }

    private fun prepareFragments() {
        fragments.clear()
        val myBooksFragData = FragmentData(
            getString(R.string.my_books),
            LibraryBooksFrag.newInstance(true)
        )
        libraryBooksFrag = myBooksFragData.fragment as LibraryBooksFrag
        val wonderSharePrefs = WonderPubSharedPrefs.getInstance(context)
        val level: String
        val syllabus: String
        val grade: String
        val subject: String
        if (wonderSharePrefs.sharedPrefsUserLevelPref.isNotEmpty()) {
            level = wonderSharePrefs.sharedPrefsUserLevelPref
            syllabus = if (wonderSharePrefs.sharedPrefsUserSyllabusPref != null && wonderSharePrefs.sharedPrefsUserSyllabusPref.size > 0) {
                wonderSharePrefs.sharedPrefsUserSyllabusPref[0]
            } else {
                ""
            }
            grade = if (wonderSharePrefs.sharedPrefsUserGradePref != null && wonderSharePrefs.sharedPrefsUserGradePref.size > 0) {
                wonderSharePrefs.sharedPrefsUserGradePref[0]
            }
            else {
                ""
            }
            subject = ""

            val shopBooksFragData = FragmentData(
                getString(R.string.ebooks),
                ShopBooksFrag.newInstance(
                    ShopBooksFragConfig(
                        showSearchBoxByDefault = true,
                        replaceFilterBtnWithClearBtn = true,
                        preSelectCompetitiveExams = false,
                        useLocalLevelFilters = true,
                        level,
                        syllabus,
                        grade,subject,level,syllabus
                    )
                )
            )
            fragments.add(shopBooksFragData)
        }
        else {
            level = ""
            syllabus = wonderSharePrefs.sharedPrefsLastFilterSyllabusSearch
            grade = wonderSharePrefs.sharedPrefsLastFilterGradeSearch
            subject = wonderSharePrefs.sharedPrefsLastFilterSubjectSearch

            val shopBooksFragData = FragmentData(
                getString(R.string.ebooks),
                ShopBooksFrag.newInstance(
                    ShopBooksFragConfig(
                        showSearchBoxByDefault = true,
                        replaceFilterBtnWithClearBtn = true,
                        preSelectCompetitiveExams = true,
                        useLocalLevelFilters = true,
                        level,
                        syllabus,
                        grade,subject,level,getString(R.string.syllabus)
                    )
                )
            )
            fragments.add(shopBooksFragData)
        }
        fragments.add(myBooksFragData)
    }

    fun selectTab(position: Int) {
        binding?.vpEbooks?.currentItem = position
    }

    fun refreshTab() {
        initView()
    }

    private fun initViewPager() {
        binding?.vpEbooks?.apply {
            isUserInputEnabled = false
            adapter = FragmentViewPager2Adapter(2, childFragmentManager, lifecycle) { position ->
                return@FragmentViewPager2Adapter fragments[position].fragment
            }
        }
    }

    private fun initTabs() {
        binding?.let {
            TabLayoutMediator(it.bookDetailsTabLayout, it.vpEbooks) { tab, pos ->
                tab.text = fragments[pos].title
            }.attach()

            it.bookDetailsTabLayout.addOnTabSelectedListener { tab ->
                if(tab?.position == 1) {
                    eventLogger.logEvent(
                        Event(
                            type = EventType.MY_BOOKS_PAGE_OPENED,
                        )
                    )
                }
            }
        }
    }

}