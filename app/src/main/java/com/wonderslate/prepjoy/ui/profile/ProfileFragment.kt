package com.wonderslate.prepjoy.ui.profile

import android.app.Activity
import android.app.Activity.RESULT_OK
import android.content.Context
import android.content.ContextWrapper
import android.content.Intent
import android.database.Cursor
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.AsyncTask
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.*
import androidx.appcompat.widget.AppCompatSpinner
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.bumptech.glide.Glide
import com.bumptech.glide.signature.ObjectKey
import com.soundcloud.android.crop.Crop
import android.widget.ProgressBar
import com.wonderslate.commons.Data
import com.wonderslate.commons.Status
import com.wonderslate.data.network.WSAPIManager
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.domain.entities.UpdateUserProfileData
import com.wonderslate.prepjoy.BuildConfig
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.Utils.IndiaStatesData
import com.wonderslate.prepjoy.Utils.Utils
import com.wonderslate.prepjoy.Utils.Utils.disableScreenShot
import com.wonderslate.prepjoy.ui.BaseActivity
import com.wonderslate.prepjoy.ui.home.HomeBadge
import de.hdodenhof.circleimageview.CircleImageView
import org.apache.http.HttpEntity
import org.apache.http.entity.ContentType
import org.apache.http.entity.mime.HttpMultipartMode
import org.apache.http.entity.mime.MultipartEntityBuilder
import org.apache.http.entity.mime.content.ByteArrayBody
import org.apache.http.entity.mime.content.StringBody
import org.json.JSONObject
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import java.io.ByteArrayOutputStream
import java.io.File
import java.net.HttpURLConnection
import java.net.ProtocolException
import java.net.URL
import java.text.SimpleDateFormat
import java.util.*

class ProfileFragment  : Fragment() {
    private lateinit var rootView: View

    private lateinit var imageBadge: ImageView
    private lateinit var textBadge: TextView
    private lateinit var textLifeTimePoints : TextView
    private lateinit var textLifeTimeMedals : TextView
   // private lateinit var imageUser : ImageView
    private lateinit var editName : EditText
    private lateinit var editCity : EditText
    private lateinit var editState : EditText


    private lateinit var txtvalidateName : TextView
    private lateinit var txtvalidateState : TextView
    private lateinit var txtvalidatecity : TextView


    private var mContext: Context? = null

    private lateinit var spinnerstate : AppCompatSpinner
    private lateinit var spinnercity : AppCompatSpinner

    private lateinit var avLoadingIndicatorView: ProgressBar
    var statesData: IndiaStatesData? = null
    private var selectedState: String? = null
    private var selectedDistrict: String? = null
    private var myContext: FragmentActivity? = null
    private lateinit var btnupdateUser : Button
    private var userImageLayout: FrameLayout? = null
    private var userImageView: CircleImageView? = null
    private var modifiedDate: String = ""

   // private val sharedPrefs: WonderPubSharedPrefs? = null

    private val viewModel: ProfileViewModel by sharedViewModel<ProfileViewModel>()
    override fun onAttach(activity: Activity) {
        myContext = activity as FragmentActivity
        super.onAttach(activity)
    }


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        rootView = inflater.inflate(R.layout.fragment_profile, container, false)
        try {
            mContext = container?.getContext();
            intiViews()
            setProfile()
            editName.setImeOptions(EditorInfo.IME_ACTION_NEXT);

            disableScreenShot(activity)
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }

        viewModel.currentAffairsUserDetails.observe(viewLifecycleOwner, ::currentAffairsUserDetails)

        return rootView
    }

    private fun currentAffairsUserDetails(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
            }
            Status.SUCCESSFUL -> {

                data.data?.let {
                    updateUserPoints(data.data!!)
                }
            }
            Status.HTTP_UNAVAILABLE -> {
            }

            Status.ERROR -> {
            }
        }
    }

    private fun updateUserPoints(jsonObject: JSONObject) {
        try {
            val s = jsonObject.getString("details")

            val arr = s.split(",").toTypedArray()

            if (s.contains("totalPoints")) {
                for (i in arr.indices) {
                    if (arr[i].contains("totalPoints")) {
                        WonderPubSharedPrefs.getInstance(context).totalPoints =
                            arr[i].split(":")[1]
                        break
                    }
                }
            }

            if (s.contains("currentBadge")) {
                for (i in arr.indices) {
                    if (arr[i].contains("currentBadge")) {
                        WonderPubSharedPrefs.getInstance(context).totalBades =
                            arr[i].split(":")[1]
                        break
                    }
                }
            }

            if (s.contains("totalMedals")) {
                for (i in arr.indices) {
                    if (arr[i].contains("totalMedals")) {
                        WonderPubSharedPrefs.getInstance(context).totalMedals =
                            arr[i].split(":")[1]
                        break
                    }
                }
            }
        } catch (e : Exception) {
            e.printStackTrace()
        }

        setProfile()
    }

    override fun onResume() {
        super.onResume()
        setProfile()
        activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        viewModel.getCurrentAffairsUserDetails(BuildConfig.SITE_ID)
    }

    private fun intiViews() {
        imageBadge = rootView.findViewById(R.id.imgPcommanderIcon)
        textBadge = rootView.findViewById(R.id.textBadge)
        textLifeTimePoints = rootView.findViewById(R.id.textLifeTimePoints)
        textLifeTimeMedals = rootView.findViewById(R.id.textLifeTimeMedals)
      //  imageUser = rootView.findViewById(R.id.userImageView)
        editName = rootView.findViewById(R.id.edtName)
        spinnerstate = rootView.findViewById(R.id.spinnerstate)
        spinnercity = rootView.findViewById(R.id.spinnercity)
        btnupdateUser = rootView.findViewById(R.id.btnupdateUser)
        avLoadingIndicatorView = rootView.findViewById(R.id.avlSignUp)
        val imageLayout: RelativeLayout = rootView.findViewById(R.id.userImageContainerLayout)
        userImageLayout = rootView.findViewById(R.id.userImageLayout)

        userImageView = rootView.findViewById(R.id.userImageView)
        txtvalidateName = rootView.findViewById(R.id.txtvalidateName)
        txtvalidateState = rootView.findViewById(R.id.txtvalidateState)
        txtvalidatecity = rootView.findViewById(R.id.txtvalidatecity)
        //sharedPrefs = WonderPubSharedPrefs.getInstance(mContext)





        statesData = IndiaStatesData(mContext)
        val states: MutableList<String?> = ArrayList()
        states.addAll(statesData!!.statesList)
        states.sortWith { obj: String?, str: String? ->
            obj!!.compareTo(
                    str!!,
                    ignoreCase = true
            )
        }
        states.add(0, "Select State")


        val stateSpinnerAdapter: ArrayAdapter<*> = ArrayAdapter<Any?>(
                mContext!!,
                R.layout.spinner_row_nothing_selected,
                states as List<Any?>
        )

        stateSpinnerAdapter.setDropDownViewResource(R.layout.spinner_textview)
        spinnerstate.adapter = stateSpinnerAdapter

        val districtList: MutableList<String?> = ArrayList()
        districtList.add(0, "Select District")


        val districtAdapter: ArrayAdapter<*> = ArrayAdapter<Any?>(mContext!!, R.layout.spinner_row_nothing_selected, districtList as List<Any?>)
        districtAdapter.setDropDownViewResource(R.layout.spinner_textview)
        spinnercity.adapter = districtAdapter
        spinnercity.isEnabled = false


        spinnerstate.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(
                    parent: AdapterView<*>?,
                    view: View?,
                    position: Int,
                    id: Long
            ) {
                try {
                    if (!states[position].equals(selectedState, ignoreCase = true)) {
                        selectedState = states[position]
                        if (spinnerstate.selectedItem.toString()
                                        .equals("Select State", ignoreCase = true)
                        ) {
                            districtList.clear()
                            districtList.add(0, "Select District")
                            selectedDistrict = "Select District"
                            districtAdapter.notifyDataSetChanged()
                            spinnercity.isEnabled = false
                        } else {
                            spinnercity.isEnabled = true
                            districtAdapter.clear()
                            districtList.addAll(statesData!!.getDistricts(selectedState))
                            districtAdapter.notifyDataSetChanged()
                            selectedDistrict = districtList[0]
                            spinnercity.setSelection(0)
                        }
                        myContext?.let { hideKeyboard(it) }
                    }
                    if (txtvalidateState.isVisible) {
                        txtvalidateState.visibility = View.GONE
                    }
                }
                catch (E:Exception)
                {
                    E.printStackTrace()
                }
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }

        spinnercity.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(
                    parent: AdapterView<*>?,
                    view: View?,
                    position: Int,
                    id: Long
            ) {
                try {
                    if (position < districtList.size) selectedDistrict = districtList[position]
                    myContext?.let { hideKeyboard(it) }
                    if (txtvalidatecity.isVisible) {
                        txtvalidatecity.visibility = View.GONE
                    }
                }
                catch (e:Exception)
                {
                    e.printStackTrace()
                }
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }

        editName.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(arg0: Editable) {
            }

            override fun beforeTextChanged(
                    s: CharSequence,
                    start: Int,
                    count: Int,
                    after: Int
            ) {
            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                try{
                if (txtvalidateName.isVisible) {
                    txtvalidateName.visibility = View.GONE
                }
                }
                catch (e:Exception)
                {
                    e.printStackTrace()
                }
            }
        })

        btnupdateUser.setOnClickListener{
            startupdateProcess()
        }
        initObserver()
        editName.setText(WonderPubSharedPrefs.getInstance(mContext).username)

        if (! WonderPubSharedPrefs.getInstance(mContext).getUserState().isEmpty()) {
            if (states.contains( WonderPubSharedPrefs.getInstance(mContext).getUserState())) {
                selectedState =  WonderPubSharedPrefs.getInstance(mContext).getUserState()
                spinnerstate.setSelection(states.indexOf(selectedState))
                spinnercity.setEnabled(true)


                if (! WonderPubSharedPrefs.getInstance(mContext).getUserDistrict().isEmpty()) {
                    selectedDistrict =  WonderPubSharedPrefs.getInstance(mContext).getUserDistrict()
                    districtAdapter.clear()
                    districtList.addAll(statesData!!.getDistricts(selectedState))
                    districtAdapter.notifyDataSetChanged()
                    spinnercity.setSelection(districtList.indexOf(selectedDistrict))
                }
            }
        }


        /* if ("true".equalsIgnoreCase(sharedPrefs.getUserLoginType())) {
            profilePhoneLayout.setVisibility(View.GONE);
            profileEmailLayout.setVisibility(View.VISIBLE);
            profileEmailLayout.setEnabled(false);
//            emailInputTxt.setText(sharedPrefs.getUseremail());
        } else {
            profilePhoneLayout.setVisibility(View.VISIBLE);
            profileEmailLayout.setVisibility(View.GONE);
            profilePhoneLayout.setEnabled(false);
//            userMobileInputTxt.setText(sharedPrefs.getUsermobile());
        }*/imageLayout.layoutParams.height = calculateDPI(180)
        imageLayout.layoutParams.width = calculateDPI(180)
        userImageView?.getLayoutParams()!!.height = calculateDPI(160)
        userImageView?.getLayoutParams()!!.width = calculateDPI(160)

      /*  Glide.with(this)
                .load(WonderPubSharedPrefs.getInstance(mContext).getUserImage())
                .placeholder(R.drawable.prepjoy_full_icon)
                .signature(ObjectKey(System.currentTimeMillis()))
                .into(userImageView!!)*/


        val url : String = WonderPubSharedPrefs.getInstance(context).userImage
        val check : Boolean = "null" in url
        if(check)
        {
           /* Glide.with(this)
                    .load(R.drawable.prepjoy_full_icon)
                    //.placeholder(R.drawable.prepjoy_full_icon)
                    //.signature(ObjectKey(System.currentTimeMillis()))
                    .into(userImageView!!)*/
            userImageView!!.setImageResource(R.drawable.prepjoy_full_icon);
           // userImageView!!.setImageDrawable(resources.getDrawable(R.drawable.prepjoy_full_icon));
            // JsonquizData.put("userImage", WonderPubSharedPrefs.getInstance(context).userImage )
        }
        else{
            Glide.with(this)
                    .load(WonderPubSharedPrefs.getInstance(context).getUserImage())
                   // .placeholder(R.drawable.prepjoy_full_icon)
                    .signature(ObjectKey(System.currentTimeMillis()))
                    .into(userImageView!!)

        }


        userImageLayout!!.setOnClickListener(View.OnClickListener { v: View? ->
            val getIntent = Intent(Intent.ACTION_GET_CONTENT)
            getIntent.type = "image/*"
            val pickIntent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
            pickIntent.type = "image/*"
            val chooserIntent = Intent.createChooser(getIntent, "Select Image")
            chooserIntent.putExtra(Intent.EXTRA_INITIAL_INTENTS, arrayOf(pickIntent))
            startActivityForResult(chooserIntent, 27)
        })

    }
     var picturePath = ""
    private var outputFile: File? = null

     override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
         try{
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 27 && resultCode == RESULT_OK && data != null && data.data != null) {
            val selectedImage = data.data
            val filePathColumn = arrayOf(MediaStore.Images.Media.DATA)
            val cursor: Cursor? = activity?.applicationContext?.contentResolver?.query(selectedImage!!, filePathColumn, null, null, null)
            if (cursor != null) {
                cursor.moveToFirst()
                val columnIndex = cursor.getColumnIndex(filePathColumn[0])
                if (columnIndex > -1) {
                    if (null != cursor.getString(columnIndex)) {
                        picturePath = cursor.getString(columnIndex)
                    }
                    cursor.close()
                    val wrapper = ContextWrapper(mContext)
                    val directory = wrapper.getDir("profileImages", Context.MODE_PRIVATE)
                    outputFile = File(directory, "cropped.png")
                    Crop.of(selectedImage, Uri.fromFile(outputFile)).asSquare().start(getContext(), this);
                }
            }
        } else if (requestCode == Crop.REQUEST_CROP && resultCode == RESULT_OK) {
            val bitmap = BitmapFactory.decodeFile(outputFile.toString())

            UploadProfilePic(myContext!!,avLoadingIndicatorView,picturePath!!,userImageView,outputFile).execute()
        }
         }
         catch (e:Exception)
         {
             e.printStackTrace()
         }
    }

    class UploadProfilePic(context: Context,avLoadingIndicatorView:ProgressBar,
                           picturePath:String, var userImageView: CircleImageView? = null, private var outputFile: File? = null) :
            AsyncTask<String, Void, Boolean>() {

        val mpcon: Context? = context

        val mavLoadingIndicatorView: ProgressBar? = avLoadingIndicatorView
        val mpicturePath: String? = picturePath

        override fun onPreExecute() {
            super.onPreExecute()
            if (mavLoadingIndicatorView != null) {
                mavLoadingIndicatorView.visibility = View.VISIBLE
            }
        }

        override fun doInBackground(vararg params: String?): Boolean? {

            var result = java.lang.Boolean.FALSE
            try {
                val boundary = "*****"
                val url = URL(WSAPIManager.SERVICE3.toString() + "api/uploadProfileImage")
                val conn = url.openConnection() as HttpURLConnection
                conn.requestMethod = "POST"
                conn.setRequestProperty("Connection", "Keep-Alive")
                conn.setRequestProperty("ENCTYPE", "multipart/form-data")
                conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=$boundary")
                conn.setRequestProperty("X-Auth-Token", WonderPubSharedPrefs.getInstance(mpcon).getAccessToken())
                conn.setRequestProperty("Accept", "application/json")
                conn.setRequestProperty("Accept-Charset", "UTF-8")
                conn.readTimeout = 30000
                conn.connectTimeout = 16000
                val entityBuilder: MultipartEntityBuilder = MultipartEntityBuilder.create()
                entityBuilder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE)
                val bos = ByteArrayOutputStream()
                val bitmap = BitmapFactory.decodeFile(outputFile!!.getPath())
                bitmap.compress(Bitmap.CompressFormat.JPEG, 70, bos)
                val data = bos.toByteArray()
                val bab = ByteArrayBody(data, "profileImage.jpg")
                entityBuilder.addPart("file", bab)
                entityBuilder.addPart("type", StringBody("user", ContentType.TEXT_PLAIN))
                val entity: HttpEntity = entityBuilder.build()
                conn.addRequestProperty("Content-length", entity.contentLength.toString() + "")
                conn.addRequestProperty(entity.contentType.name, entity.contentType.value)
                val os = conn.outputStream
                entity.writeTo(conn.outputStream)
                os.close()
                conn.connect()
                result = if (conn.responseCode == HttpURLConnection.HTTP_OK || conn.responseCode == HttpURLConnection.HTTP_MOVED_TEMP) {
                    val imageUrl: String = (WSAPIManager.SERVICE3.toString() + "funlearn/showProfileImage?id=" + WonderPubSharedPrefs.getInstance(mpcon).getUserId()
                            + "&fileName=profileImage.jpg&type=user&imgType=passport")
                    WonderPubSharedPrefs.getInstance(mpcon).setUserImage(imageUrl)
                    java.lang.Boolean.TRUE
                } else {
                    java.lang.Boolean.FALSE
                }
            } catch (e: Exception) {
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP && e is ProtocolException) result = java.lang.Boolean.TRUE

                return result
            }

            return result
        }


        override fun onPostExecute(uploaded: Boolean?) {
            super.onPostExecute(uploaded)


            mavLoadingIndicatorView!!.visibility = View.GONE
            if (uploaded == null || !uploaded) {
                Utils.showTopSnackBar("Could'nt upload profile image! Please try after some time.", mpcon as Activity?)
               // Toast.makeText(this@EditUserProfileActivity, "Could'nt upload profile image! Please try after some time.", Toast.LENGTH_SHORT).show()
            } else {
                Glide.with(mpcon!!)
                        .load(WonderPubSharedPrefs.getInstance(mpcon).getUserImage())
                        .placeholder(R.drawable.prepjoy_full_icon)
                        .signature(ObjectKey(System.currentTimeMillis()))
                        .into(userImageView!!)
                Utils.showTopSnackBar("Updated Profile pic successfully.", mpcon as Activity?)
            }

        }
    }



    fun hideKeyboard(activity: Activity) {
        val imm = activity.getSystemService(BaseActivity.INPUT_METHOD_SERVICE) as InputMethodManager
        //Find the currently focused view, so we can grab the correct window token from it.
        var view = activity.currentFocus
        //If no view currently has focus, create a new one, just so we can grab a window token from it
        if (view == null) {
            view = View(activity)
        }
        imm.hideSoftInputFromWindow(view.windowToken, 0)
    }

    private fun startupdateProcess() {
        try{
        if (validateFields()) {
            avLoadingIndicatorView.visibility = View.VISIBLE
            val date = Date()
            modifiedDate = SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH).format(date)
            viewModel.updateUserData(
                    UpdateUserProfileData(
                            editName.text.toString(), WonderPubSharedPrefs.getInstance(mContext).usermobile,
                            selectedState!!,selectedDistrict!!, BuildConfig.SITE_ID
                    )
            )
        }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }
    }

    private fun initObserver() {
        viewModel.userDetailsResponse.observe(viewLifecycleOwner, ::updateResponse)
    }

    private fun updateResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {

            }

            Status.SUCCESSFUL -> {
                try {
                    avLoadingIndicatorView.visibility = View.GONE
                    avLoadingIndicatorView.visibility = View.GONE
                    Utils.showTopSnackBar("Updated Successfully.", myContext)
                    btnupdateUser.setBackgroundResource(R.drawable.button_shape_default)
                    WonderPubSharedPrefs.getInstance(requireContext()).userDistrict =
                        selectedDistrict
                    WonderPubSharedPrefs.getInstance(requireContext()).username =
                        editName.text.toString()
                    WonderPubSharedPrefs.getInstance(requireContext()).userState = selectedState
                } catch (e : Exception) {

                }
            }

            Status.HTTP_UNAVAILABLE -> {
                try {
                    Utils.showTopSnackBar(
                        resources.getString(R.string.server_under_maintenance),
                        myContext
                    )
                } catch (e : Exception) {
                    e.printStackTrace()
                }
            }

            Status.ERROR -> {
                try {
                    Utils.showTopSnackBar(
                        resources.getString(R.string.some_thing_went_wrong),
                        myContext
                    )
                } catch (e :Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

    private fun validateFields(): Boolean {
        val name = editName.text.toString()
        when {
            name.isEmpty() -> {
                txtvalidateName.visibility = View.VISIBLE
            }
            selectedState.equals(getString(R.string.select_state), ignoreCase = true) -> {
                txtvalidateState.visibility = View.VISIBLE
            }
            selectedDistrict.equals(getString(R.string.select_district), ignoreCase = true) -> {
                txtvalidatecity.visibility = View.VISIBLE
            }
            else -> {
                txtvalidateName.visibility = View.GONE
                txtvalidateState.visibility = View.GONE
                txtvalidatecity.visibility = View.GONE
                return true
            }
        }
        return false
    }

    private fun setProfile() {
        try{
      /*  Picasso.get().load(WonderPubSharedPrefs.getInstance(context).userImage)
            .into(imageUser)*/

        editName.setText(WonderPubSharedPrefs.getInstance(context).username)
      //  editCity.setText(WonderPubSharedPrefs.getInstance(context).userDistrict)
       // editState.setText(WonderPubSharedPrefs.getInstance(context).userState)


        textLifeTimePoints.text = WonderPubSharedPrefs.getInstance(context).totalPoints
        textLifeTimeMedals.text = WonderPubSharedPrefs.getInstance(context).totalMedals
        var home = HomeBadge(context, imageBadge, textBadge, WonderPubSharedPrefs.getInstance(context).totalBades)
        home.getImageIcon(context, imageBadge, textBadge, WonderPubSharedPrefs.getInstance(context).totalBades)
       // editName.setText("Satish")
       // editCity.setText("Jaipur")
       // editState.setText("KA")
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }
    }

    /*@Override
    public void handleStatus(String action, Integer status) {

    }

    @Override
    public void handleStatusIntent(Intent intent) {
        if (intent.getAction().equals(WonderComponentMessagingInterface.BROADCAST_ACTION_SERVICE_UPDATE_USER_PROFILE)) {
            mainLoader.visibility = View.GONE;
            int statusCode = intent.getIntExtra(WonderComponentMessagingInterface.BROADCAST_RESULT_STATUS,
                    WonderComponentMessagingInterface.RESULT_STATUS_SUCCESS);
            if (!Utils.checkResultSuccess(statusCode)){
                Utils.showErrorToast(this,statusCode);
                return;
            }
                sharedPrefs.setUsermobile(mobileStr);
                sharedPrefs.setUserEmail(emailStr);
                sharedPrefs.setUsername(userNameStr);

                Toast.makeText(this, "Updated Successfully.", Toast.LENGTH_SHORT).show();
                finish();

        }
    }*/
    private fun calculateDPI(calculateSizeInt: Int): Int {
        val metrics = resources.displayMetrics
        if (metrics.densityDpi >= 120 && metrics.densityDpi <= 200) {
            return calculateSizeInt
        } else if (metrics.densityDpi >= 201 && metrics.densityDpi <= 280) {
            val size = calculateSizeInt * 1.5
            return size.toInt()
        } else if (metrics.densityDpi >= 281 && metrics.densityDpi <= 400) {
            return calculateSizeInt * 2
        } else if (metrics.densityDpi >= 401 && metrics.densityDpi <= 540) {
            return calculateSizeInt * 3
        } else if (metrics.densityDpi >= 541) {
            return calculateSizeInt * 4
        }
        return calculateSizeInt
    }
}