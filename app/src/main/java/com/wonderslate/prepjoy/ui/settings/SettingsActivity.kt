package com.wonderslate.prepjoy.ui.settings

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.webkit.URLUtil
import android.widget.TextView
import android.widget.Toast
import android.widget.ToggleButton
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.prepjoy.BuildConfig
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.Utils.Utils
import com.wonderslate.prepjoy.Utils.Utils.disableScreenShot
import com.wonderslate.prepjoy.ui.BaseActivity
import com.ws.database.room.entity.Resource
import com.ws.resources.ui.weblink.WeblinkAct.Companion.createIntent
// import kotlinx.android.synthetic.main.header_language_change.*
// import kotlinx.android.synthetic.main.settings_activity.*

class SettingsActivity : BaseActivity() {

    private lateinit var rrltBack: View
    private lateinit var privacypolicytext: View
    private lateinit var termsofservicetext: View

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (supportActionBar != null) {
            supportActionBar!!.hide()
        }

        // Initialize views
        rrltBack = findViewById(R.id.rrltBack)
        privacypolicytext = findViewById(R.id.privacypolicytext)
        termsofservicetext = findViewById(R.id.termsofservicetext)

        rrltBack.setOnClickListener {
            onBackPressed()
        }
        privacypolicytext.setOnClickListener {
            //openWebPage(this, getString(R.string.privacy_url))
            val url = getString(R.string.privacy_url)+"&tokenId="+WonderPubSharedPrefs.getInstance(this).accessToken
            val resource = Resource("", "", "", "", "", "",
                    "", "", "", "", "", "", "", url, "Privacy Policy", "", "",
                    "", "", "", "", "")
            startActivity(createIntent(this, resource))
            overridePendingTransition(android.R.anim.slide_in_left, android.R.anim.slide_out_right)

        }
        termsofservicetext.setOnClickListener {
            // openWebPage(this, getString(R.string.term_condition_url))
            val url = getString(R.string.term_condition_url)+"&tokenId="+WonderPubSharedPrefs.getInstance(this).accessToken
            val resource = Resource("", "", "", "", "", "",
                    "", "", "", "", "", "", "", url, "Terms of Service", "", "",
                    "", "", "", "", "")
            startActivity(createIntent(this, resource))
            overridePendingTransition(android.R.anim.slide_in_left, android.R.anim.slide_out_right)
        }
        val toggleLanguage = findViewById<ToggleButton>(R.id.toggleLanguage)
        toggleLanguage.visibility = View.GONE
        val txtVerion = findViewById<TextView>(R.id.txtAppVerions)
        txtVerion.text = "App Version: v"+ BuildConfig.VERSION_NAME

        disableScreenShot(this)
    }

    override fun getLayoutResource(): Int {
        return R.layout.settings_activity
    }

    private fun openWebPage(context: Context, url: String?) {
        try {
            if (!URLUtil.isValidUrl(url)) {
                Toast.makeText(context, " This is not a valid link", Toast.LENGTH_LONG).show()
            } else {
                val intent = Intent(Intent.ACTION_VIEW)
                intent.setData(Uri.parse(url))
                context.startActivity(intent)
            }
        } catch (e: ActivityNotFoundException) {
            Toast.makeText(
                    context,
                    " You don't have any browser to open web page",
                    Toast.LENGTH_LONG
            ).show()
        }
    }
}