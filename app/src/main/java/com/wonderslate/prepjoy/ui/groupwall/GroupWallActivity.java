package com.wonderslate.prepjoy.ui.groupwall;

import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.RelativeLayout;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;

import com.wonderslate.prepjoy.R;
import com.wonderslate.prepjoy.Views.Activity.SplashActivity;
import com.wonderslate.prepjoy.news.NewsListActivity;
import com.wonderslate.prepjoy.news.NewsPreferenceFragment;
import com.wonderslate.prepjoy.ui.dashboard.DashBoardActivity;

public class GroupWallActivity extends AppCompatActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_group_wall_parent);
        init();
    }
    private void init() {
        // GroupWallFragment functionality temporarily disabled for demo
        // GroupWallFragment groupWallFragment = new GroupWallFragment();
        // getSupportFragmentManager().beginTransaction().replace(R.id.group_wall, groupWallFragment).commit();
        if (Build.VERSION.SDK_INT >= 23) {
            Window window = this.getWindow();
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setNavigationBarColor(ContextCompat.getColor(this, R.color.primary_bg_dark));
            getWindow().setStatusBarColor(ContextCompat.getColor(this, R.color.primary_bg_dark));
            View decorView = getWindow().getDecorView();
            decorView.setSystemUiVisibility(decorView.getSystemUiVisibility() & ~View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING);
        }

        RelativeLayout rrltBack = findViewById(R.id.rrltBack);
        rrltBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
                finish();
            }
        });


    }

    @Override
    public void onBackPressed() {
        if (getIntent().hasExtra("comingFrom")) {
            if (getIntent().getStringExtra("comingFrom").equalsIgnoreCase("notification")) {
                Intent dashboard = new Intent(GroupWallActivity.this, DashBoardActivity.class);
                startActivity(dashboard);
                finish();
            }
            else {
                super.onBackPressed();
                finish();
            }
        }
        else {
            super.onBackPressed();
            finish();
        }
    }
}