package com.wonderslate.prepjoy.ui.chapters_list

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.widget.ProgressBar
import com.wonderslate.commons.Data
import com.wonderslate.commons.Status
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.domain.entities.QuizFullDetails
import com.wonderslate.domain.entities.UserHistoryForQuizData
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.Utils.Utils
import com.wonderslate.prepjoy.ui.BaseActivity
import com.wonderslate.prepjoy.ui.analytics.AnalyticsDataModel
import com.wonderslate.prepjoy.ui.home.HomeViewModel
import com.wonderslate.prepjoy.ui.home.QuizOptionsListener
import com.wonderslate.prepjoy.ui.login.LoginActivity
import com.wonderslate.prepjoy.ui.quiz.QuizActivity
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import org.koin.androidx.viewmodel.ext.android.viewModel

class HistoryForQuiz : BaseActivity(), QuizOptionsListener {

    lateinit var recyclerViewGroupsDetails: RecyclerView
    private var mContext: Context? = null
    val viewModel: HomeViewModel by viewModel()
    var adapter: HistoryQuizAdapter? = null
    val postDataList: ArrayList<AnalyticsDataModel> = arrayListOf()
    var quizid : String? = null
    var resname :String? =  null
    var quiztype : String? = null
    lateinit var groupLoaderDetails: ProgressBar
    lateinit var btnBack :CardView
    lateinit var noDataLayout :LinearLayout
    lateinit var lltTitle : LinearLayout
    private var multiQuizData: String? = null
    var language1 : String? = null
    var language2 : String? = null
    private var challengerName: String? = null
    private var challengerPlace: String? = null
    private var realDailyTestDtlId : String? = null
    private var selectedTestId: String? = null
    private var selectedTestMode: String? = null
    private var selectedTestDate: String? = null
    private var selectedQuizName: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        try {
            mContext = this
            if (supportActionBar != null) {
                supportActionBar!!.hide()
            }
            quizid = intent.getStringExtra("quizId").takeIf { intent.hasExtra("quizId") }
            resname = intent.getStringExtra("resName").takeIf { intent.hasExtra("resName") }
            quiztype = intent.getStringExtra("quizType").takeIf { intent.hasExtra("quizType") }
            if(quiztype.equals("dailyTests"))
                 getMultiQuizData()
            initObserver()
            init()

        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }
    }

    private fun getMultiQuizData() {
        multiQuizData = intent.getStringExtra("quizData").
        takeIf { intent.hasExtra("quizData") }
        language1 = intent.getStringExtra("language1").takeIf {
            intent.hasExtra("language1")
        }
        language2 = intent.getStringExtra("language2").takeIf {
            intent.hasExtra("language2")
        }

        challengerName = intent.getStringExtra("challengerName").takeIf {
            intent.hasExtra("challengerName")
        }
        challengerPlace = intent.getStringExtra("challengerPlace").takeIf {
            intent.hasExtra("challengerPlace")
        }
        realDailyTestDtlId  = intent.getStringExtra("realDailyTestDtlId").takeIf {
            intent.hasExtra("realDailyTestDtlId")
        }

        selectedTestId = intent.getStringExtra("testId").
        takeIf { intent.hasExtra("testId") }
        selectedTestMode = intent.getStringExtra("testMode").takeIf {
            intent.hasExtra("testMode")
        }
        selectedTestDate = intent.getStringExtra("testDate").takeIf {
            intent.hasExtra("testDate")
        }
    }

    override fun onResume() {
        super.onResume()
        viewModel.getHistoryForAQuiz(UserHistoryForQuizData(quizid.toString(), quiztype!!))
    }

    override fun getLayoutResource(): Int {
        return R.layout.activity_history_for_quiz
    }


    private fun init() {
        try {
            recyclerViewGroupsDetails = findViewById(R.id.recHistory)
            recyclerViewGroupsDetails.layoutManager = LinearLayoutManager(mContext)
            recyclerViewGroupsDetails.setHasFixedSize(false)
            groupLoaderDetails = findViewById(R.id.groupLoader_details)
            lltTitle =  findViewById(R.id.lltTitle)
            groupLoaderDetails.visibility = View.VISIBLE
            btnBack =  findViewById(R.id.btnBack)
            btnBack.setOnClickListener{
                super.onBackPressed()
            }
            val textQuizName : TextView = findViewById(R.id.textQuizName)
            if (resname.equals("Daily test", true)) {
                textQuizName.text = "Online Tests"
            }
            else {
                textQuizName.text = resname
            }
            noDataLayout = findViewById(R.id.noDataLayout)
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }
    }



    private fun initObserver() {
        viewModel.historyForAQuizDetails.observe(this, ::historyForAQuizDetails)
        viewModel.getAllFullQuizDetails.observe(this, ::getAllDetailsForQuiz)
    }

    private fun historyForAQuizDetails(data: Data<JSONObject>) {
        try {
            when (data.responseType) {
                Status.LOADING -> {
                }
                Status.SUCCESSFUL -> {
                    data.data?.let {
                        lltTitle.visibility = View.VISIBLE
                        noDataLayout.visibility = View.GONE
                        groupLoaderDetails.visibility = View.GONE
                        Log.e("Data", ":" + data.data)
                        val res = it.getString("lastQuizDetails")
                        if (res.equals("no records") || res == "null") {
                            noDataLayout.visibility = View.VISIBLE
                            lltTitle.visibility = View.GONE
                            groupLoaderDetails.visibility = View.GONE


                            val bottomSheet = BottomSheetDialog(mContext!!,quizid!!,resname!!,
                                multiQuizData,
                                language1,
                                language2,
                                challengerName,
                                challengerPlace,
                                realDailyTestDtlId,
                                selectedTestId,
                                selectedTestMode,
                                selectedTestDate,
                                quiztype)
                            bottomSheet.show(supportFragmentManager, "BottomSheet")
                        } else {
                            val jsonArray = JSONArray(res)
                            postDataList.clear()
                            for (i in 0 until jsonArray.length()) {
                                val jsonObject = jsonArray.getJSONObject(i)
                                val datalist = AnalyticsDataModel()
                                datalist.setquizName(jsonObject.getString("quizName"))
                                if (jsonObject.has("matchStatus")) {
                                    if (jsonObject.getString("matchStatus") == "null") {
                                        if (jsonObject.has("quizType")) {
                                            if (jsonObject.getString("quizType")
                                                    .equals("practice")
                                            ) {
                                                datalist.setmatchStatus("Practice")
                                            } else if (jsonObject.getString("quizType")
                                                    .contains("test")
                                            ) {
                                                datalist.setmatchStatus("Test")
                                            } else {
                                                datalist.setmatchStatus("--")
                                            }
                                        } else
                                            datalist.setmatchStatus("--")
                                    } else {
                                        if (jsonObject.getString("matchStatus").equals("win"))
                                            datalist.setmatchStatus("Won")
                                        else
                                            datalist.setmatchStatus(jsonObject.getString("matchStatus"))
                                    }
                                }

                                if (jsonObject.has("points"))
                                    datalist.setpoints(jsonObject.getString("points").toInt())
                                else
                                    datalist.setpoints(0)
                                datalist.setDateCreated(jsonObject.getString("dateCreated"))

                                if (jsonObject.has("quizRecId") && !jsonObject.isNull("quizRecId")) {
                                    datalist.setquizRecId(jsonObject.getString("quizRecId").toInt())
                                    postDataList.add(datalist)
                                }
                            }
                            adapter = HistoryQuizAdapter(mContext!!, this)
                            recyclerViewGroupsDetails.adapter = adapter
                            adapter!!.updateData(postDataList)
                        }
                    }
                }
                Status.HTTP_UNAVAILABLE -> {
                    noDataLayout.visibility = View.VISIBLE
                    lltTitle.visibility = View.GONE
                    groupLoaderDetails.visibility = View.GONE
                }
                Status.ERROR -> {
                    noDataLayout.visibility = View.VISIBLE
                    groupLoaderDetails.visibility = View.GONE
                    lltTitle.visibility = View.GONE
                }
            }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }
    }

    private fun getAllDetailsForQuiz(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
            }
            Status.SUCCESSFUL -> {
                data.data?.let {
                    try {
                        // avShimmer.visibility = View.GONE
                        groupLoaderDetails.visibility = View.GONE
                        WonderPubSharedPrefs.getInstance(mContext).userLastQuizResults = data.data.toString()
                        navigateToQuizStats()

                    } catch (e: JSONException) {
                        e.printStackTrace()
                    }
                }
            }
            Status.HTTP_UNAVAILABLE -> {

                try {
                    groupLoaderDetails.visibility = View.GONE
                } catch (e: Exception) {
                    groupLoaderDetails.visibility = View.GONE
                    Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong),
                        mContext as Activity?
                    )
                    e.printStackTrace()
                }
            }

            Status.ERROR -> {
                try {
                    groupLoaderDetails.visibility = View.GONE
                    if (data.error.toString().contains("401")) {
                        WonderPubSharedPrefs.getInstance(mContext).clearAllSharePref()
                        val intent = Intent(mContext, LoginActivity::class.java)
                        startActivity(intent)
                    }
                } catch (e: Exception) {
                    groupLoaderDetails.visibility = View.GONE
                    e.printStackTrace()
                }
            }
        }
    }

    override fun onOptionSelected(option: String, quizName: String) {

        groupLoaderDetails.visibility = View.VISIBLE
        // viewModel.getQuizQuestionsAnswers(lastquizId)
        selectedQuizName = quizName
        viewModel.getFullQuizDetails(QuizFullDetails(option))
    }

    private fun navigateToQuizStats() {
        val quizIntent = Intent(mContext, QuizActivity::class.java).also {
            it.putExtra("stats", true)
            it.putExtra("quizName", selectedQuizName)
        }
        startActivity(quizIntent)
    }
}