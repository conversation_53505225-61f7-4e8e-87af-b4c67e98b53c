package com.wonderslate.prepjoy.ui.leaderboard

import android.app.Activity
import android.app.AlertDialog
import android.app.DatePickerDialog
import android.content.Intent
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.distinctUntilChanged
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import androidx.viewpager.widget.ViewPager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.bumptech.glide.signature.ObjectKey
import com.google.android.material.tabs.TabLayout
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.ktx.Firebase
import com.wonderslate.commons.Data
import com.wonderslate.commons.Status
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.domain.entities.UserRankData
import com.wonderslate.domain.entities.UserRankDataCurrent
import com.wonderslate.prepjoy.BuildConfig
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.Utils.CrashlyticsLogger
import com.wonderslate.prepjoy.Utils.CustomViews.BadgesDialog
import com.wonderslate.prepjoy.Utils.CustomViews.MedalsDialog
import com.wonderslate.prepjoy.Utils.FirebaseAnalyticsUtils
import com.wonderslate.prepjoy.Utils.GenerateDates
import com.wonderslate.prepjoy.Utils.TickerClickListener
import com.wonderslate.prepjoy.Utils.Utils
import com.wonderslate.prepjoy.ui.dashboard.DashBoardActivity
import com.wonderslate.prepjoy.ui.home.HomeBadge
import com.wonderslate.prepjoy.ui.home.HomeViewModel
import com.wonderslate.prepjoy.ui.login.LoginActivity
import com.wonderslate.prepjoy.ui.profile.ProfileActivity
import com.wang.avi.AVLoadingIndicatorView
// import kotlinx.android.synthetic.main.fragment_leaderboard.*
import org.json.JSONObject
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import java.text.SimpleDateFormat
import java.util.*


class LeaderboardFragment : Fragment(), LeaderBoardListener{

    private val viewModel: HomeViewModel by sharedViewModel<HomeViewModel>()
    var dates: MutableList<String> = ArrayList()
    var tabLayout: TabLayout? = null
    var viewPager: ViewPager? = null
    private var rootView: View? = null
    private var myContext: FragmentActivity? = null
    private lateinit var textComingSoon: TextView
    private lateinit var recyclerRankList: RecyclerView

    private lateinit var imageBadge: ImageView
    private lateinit var textBadge: TextView
    private lateinit var textLifeTimePoints: TextView
    private lateinit var textLifeTimeMedals: TextView
    private lateinit var imageProfile: ImageView
    private lateinit var textName: TextView
    private lateinit var textUserName: TextView
    private lateinit var linearLeaderBoard: LinearLayout
    private lateinit var list: ArrayList<UserRankDataCurrent>

    private lateinit var rlProfile: RelativeLayout
    private lateinit var rrltTopRanks: RelativeLayout

    private lateinit var rrltUser1Rank: RelativeLayout


    private lateinit var llProfile: LinearLayout
    private lateinit var lltPoints: LinearLayout
    private lateinit var lltMedals: LinearLayout

    private lateinit var userRankImage1: ImageView
    private lateinit var userRankImage2: ImageView
    private lateinit var userRankImage3: ImageView

    private lateinit var txtUserName1: TextView
    private lateinit var txtUserName2: TextView
    private lateinit var txtUserName3: TextView

    private lateinit var txtUserState1: TextView
    private lateinit var txtUserState2: TextView
    private lateinit var txtUserState3: TextView

    private lateinit var txtUserPoints1: TextView
    private lateinit var txtUserPoints2: TextView
    private lateinit var txtUserPoints3: TextView
    private lateinit var textNoRankRecord: TextView

    //Weekly Contest Winners
    private lateinit var pwTopRanks: RelativeLayout

    private lateinit var pwUser1Rank: RelativeLayout
    private lateinit var pwUser2Rank: RelativeLayout
    private lateinit var pwUser3Rank: RelativeLayout
    private lateinit var rrltUser2Rank: RelativeLayout
    private lateinit var rrltUser3Rank: RelativeLayout

    private lateinit var pwuserRankImage1: ImageView
    private lateinit var pwuserRankImage2: ImageView
    private lateinit var pwuserRankImage3: ImageView

    private lateinit var pwtxtUserName1: TextView
    private lateinit var pwtxtUserName2: TextView
    private lateinit var pwtxtUserName3: TextView

    private lateinit var pwtxtUserState1: TextView
    private lateinit var pwtxtUserState2: TextView
    private lateinit var pwtxtUserState3: TextView

    private lateinit var pwtxtUserPoints1: TextView
    private lateinit var pwtxtUserPoints2: TextView
    private lateinit var pwtxtUserPoints3: TextView
    private lateinit var pwtextNoRankRecord: TextView

    private lateinit var contestInfoBtn: Button
    private lateinit var weeklyWinnersParent: RelativeLayout


    private lateinit var textNoRecord: TextView
    private var modifiedDate: String = ""
    private lateinit var swipeToRefresh: SwipeRefreshLayout
    private lateinit var relativeDatePicker: RelativeLayout
    private lateinit var textDate: TextView
    private var tabPosition = 0
    private var selectedMode = "daily"

    private lateinit var firebaseAnalytics: FirebaseAnalytics
    private lateinit var progressLoader: AVLoadingIndicatorView


    override fun onAttach(activity: Activity) {
        myContext = activity as FragmentActivity
        super.onAttach(activity)
    }

    override fun onCreateView(
            inflater: LayoutInflater,
            container: ViewGroup?,
            savedInstanceState: Bundle?
    ): View? {
        rootView = inflater.inflate(R.layout.fragment_leaderboard, container, false)
        initViews()
        try {

            tabLayout = rootView!!.findViewById(R.id.tabLayout)
            viewPager = rootView!!.findViewById(R.id.viewPager)
            tabLayout!!.addTab(
                    tabLayout!!.newTab().setText(myContext!!.resources.getString(R.string.daily))
            )
            tabLayout!!.addTab(
                    tabLayout!!.newTab().setText(myContext!!.resources.getString(R.string.tab_rank_weekly))
            )
            tabLayout!!.addTab(
                    tabLayout!!.newTab().setText(myContext!!.resources.getString(R.string.tab_rank_monthly))
            )
            tabLayout!!.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab) {
                    textNoRecord.visibility = View.GONE
                    textComingSoon.visibility = View.GONE
                    rrltTopRanks.visibility = View.VISIBLE
                    when (tab.position) {
                        0 -> {
                            recyclerRankList.visibility = View.VISIBLE
                            selectedMode = "daily"
                            tabPosition = 0
                            relativeDatePicker.visibility = View.VISIBLE
                        }
                        1 -> {
                            selectedMode = "weekly"
                            tabPosition = 1
                            relativeDatePicker.visibility = View.GONE
                        }
                        2 -> {
                            selectedMode = "monthly"
                            tabPosition = 2
                            relativeDatePicker.visibility = View.GONE
                        }
                    }
                    progressLoader.smoothToShow()
                    refreshLeaderBoard()
                }

                override fun onTabUnselected(tab: TabLayout.Tab) {

                }

                override fun onTabReselected(tab: TabLayout.Tab) {

                }
            })
        } catch (e: Exception) {
            e.printStackTrace()
        }


        firebaseAnalytics = Firebase.analytics
        val bundle = Bundle()
        bundle.putString(FirebaseAnalyticsUtils.ACTIVITY_NAME, "LeaderboardFragment")
        bundle.putString(FirebaseAnalyticsUtils.LOCATION, "LeaderboardFragment onCreateView")
        firebaseAnalytics.logEvent(FirebaseAnalyticsUtils.ACTION, bundle)
        return rootView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (null == savedInstanceState) {
            setInitial()
        }
    }

    override fun onResume() {
        super.onResume()
        (requireActivity() as DashBoardActivity).showOrHideUserPreference(false)

        //setInitial()
        refreshLeaderBoard()
    }

    private fun setInitial() {
        val date = Date()
        modifiedDate = SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH).format(date)
        textDate.text = SimpleDateFormat("dd-MM-yyyy", Locale.ENGLISH).format(date)
        viewModel.userRank.observe(viewLifecycleOwner, ::userRank)
        viewModel.weeklyWinnersRank.observe(viewLifecycleOwner, ::weeklyWinnersRank)
        viewModel.getUserRank(UserRankData(modifiedDate, BuildConfig.SITE_ID, selectedMode))
        if (WonderPubSharedPrefs.getInstance(context).tickerVisibility) {
            viewModel.getWeeklyContestRank(UserRankData(GenerateDates.getPreviousSaturday(), BuildConfig.SITE_ID, "weekly"))
        }
    }

    private fun initViews() {
        imageBadge = rootView!!.findViewById(R.id.imgPcommanderIcon)
        textBadge = rootView!!.findViewById(R.id.textBadge)
        textComingSoon = rootView!!.findViewById(R.id.textComingSoon)
        recyclerRankList = rootView!!.findViewById(R.id.horizontalcardviewpager)

        textLifeTimePoints = rootView!!.findViewById(R.id.textLifeTimePoints)
        textLifeTimeMedals = rootView!!.findViewById(R.id.textLifeTimeMedals)
        imageProfile = rootView!!.findViewById(R.id.userImageView)
        textName = rootView!!.findViewById(R.id.txtUserProfilename)
        textUserName = rootView!!.findViewById(R.id.txtUsername)
        linearLeaderBoard = rootView!!.findViewById(R.id.linearList)

        rlProfile = rootView!!.findViewById(R.id.rrltUserProfile)
        llProfile = rootView!!.findViewById(R.id.lltUserPerformance)
        textNoRecord = rootView!!.findViewById(R.id.textNoRecord)
        textNoRankRecord = rootView!!.findViewById(R.id.textNoRankRecord)
        swipeToRefresh = rootView!!.findViewById(R.id.swipeToRefresh)
        relativeDatePicker = rootView!!.findViewById(R.id.rlDatePicker)
        textDate = rootView!!.findViewById(R.id.textDate)
        lltPoints = rootView!!.findViewById(R.id.lltPoints)
        lltMedals = rootView!!.findViewById(R.id.lltMedals)
        rrltTopRanks = rootView!!.findViewById(R.id.rrltTopRanks)
        rrltUser1Rank = rootView!!.findViewById(R.id.rrltUser1Rank)
        rrltUser1Rank.bringToFront()
        rrltUser2Rank = rootView!!.findViewById(R.id.rrltUser2Rank)
        rrltUser3Rank = rootView!!.findViewById(R.id.rrltUser3Rank)

        userRankImage1 = rootView!!.findViewById(R.id.userRankImage1)
        userRankImage2 = rootView!!.findViewById(R.id.userRankImage2)
        userRankImage3 = rootView!!.findViewById(R.id.userRankImage3)

        txtUserName1 = rootView!!.findViewById(R.id.txtUserName1)
        txtUserName2 = rootView!!.findViewById(R.id.txtUserName2)
        txtUserName3 = rootView!!.findViewById(R.id.txtUserName3)

        txtUserPoints1 = rootView!!.findViewById(R.id.txtUserPoints1)
        txtUserPoints2 = rootView!!.findViewById(R.id.txtUserPoints2)
        txtUserPoints3 = rootView!!.findViewById(R.id.txtUserPoints3)

        txtUserState1 = rootView!!.findViewById(R.id.txtUserState1)
        txtUserState2 = rootView!!.findViewById(R.id.txtUserState2)
        txtUserState3 = rootView!!.findViewById(R.id.txtUserState3)

        //Weekly Contest Winners
        pwTopRanks = rootView!!.findViewById(R.id.previousWeekTopRanks)
        pwUser1Rank = rootView!!.findViewById(R.id.pwUser1Rank)
        pwUser1Rank.bringToFront()
        pwUser2Rank = rootView!!.findViewById(R.id.pwUser2Rank)
        pwUser3Rank = rootView!!.findViewById(R.id.pwUser3Rank)

        pwuserRankImage1 = rootView!!.findViewById(R.id.pwuserRankImage1)
        pwuserRankImage2 = rootView!!.findViewById(R.id.pwuserRankImage2)
        pwuserRankImage3 = rootView!!.findViewById(R.id.pwuserRankImage3)

        pwtxtUserName1 = rootView!!.findViewById(R.id.pwtxtUserName1)
        pwtxtUserName2 = rootView!!.findViewById(R.id.pwtxtUserName2)
        pwtxtUserName3 = rootView!!.findViewById(R.id.pwtxtUserName3)

        pwtxtUserPoints1 = rootView!!.findViewById(R.id.pwtxtUserPoints1)
        pwtxtUserPoints2 = rootView!!.findViewById(R.id.pwtxtUserPoints2)
        pwtxtUserPoints3 = rootView!!.findViewById(R.id.pwtxtUserPoints3)

        pwtxtUserState1 = rootView!!.findViewById(R.id.pwtxtUserState1)
        pwtxtUserState2 = rootView!!.findViewById(R.id.pwtxtUserState2)
        pwtxtUserState3 = rootView!!.findViewById(R.id.pwtxtUserState3)

        weeklyWinnersParent = rootView!!.findViewById(R.id.weekly_winner_parent)
        weeklyWinnersParent.visibility = View.GONE

        contestInfoBtn = rootView!!.findViewById(R.id.info_contest)
        progressLoader = rootView!!.findViewById(R.id.progressLoader)

        contestInfoBtn.setOnClickListener {
            val dialogView: View =
                layoutInflater.inflate(R.layout.contest_info_layout, null)
            val alertDialog = AlertDialog.Builder(context)
            alertDialog.setView(dialogView)
            alertDialog.setCancelable(true)
            alertDialog.create().show()
        }


        rlProfile.setOnClickListener {
            //viewModel.getCurrentFragmentPosition(2)
            val intent = Intent(context, ProfileActivity::class.java)
            this.startActivity(intent)
        }

        /*  llProfile.setOnClickListener {
             // viewModel.getCurrentFragmentPosition(2)
              val intent = Intent(context, ProfileActivity::class.java)
              this.startActivity(intent)
          }*/

        lltPoints.setOnClickListener {
            context?.let { it1 -> BadgesDialog().showDialog(it1) }
        }
        lltMedals.setOnClickListener {
            context?.let { it1 -> MedalsDialog().showDialog(it1) }
        }

        swipeToRefresh.setOnRefreshListener {
            refreshLeaderBoard()
        }

        showDateDialog()
    }

    private fun refreshLeaderBoard() {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH)
        val date = when (selectedMode) {
            "daily" -> {
                modifiedDate
            }
            else -> {
                dateFormat.format(Date())
            }
        }
        val weeklyWinnersDate = when (selectedMode) {
            "daily" -> {
                modifiedDate
            }
            else -> {
                dateFormat.format(Date())
            }
        }
        viewModel.userRank.distinctUntilChanged().observe(viewLifecycleOwner, ::userRank)
        viewModel.weeklyWinnersRank.distinctUntilChanged().observe(viewLifecycleOwner, ::weeklyWinnersRank)
        viewModel.getUserRank(
                UserRankData(
                        date,
                        BuildConfig.SITE_ID,
                        selectedMode
                )
        )

        if (WonderPubSharedPrefs.getInstance(context).tickerVisibility) {
            viewModel.getWeeklyContestRank(
                UserRankData(
                    GenerateDates.getPreviousSaturday(),
                    BuildConfig.SITE_ID,
                    "weekly"
                )
            )
        }
    }

    private fun showDateDialog() {
        relativeDatePicker.setOnClickListener {
            val a =
                    modifiedDate.split("-").toTypedArray()
            val year = a[0].toInt()
            val month = a[1].toInt() - 1
            val day = a[2].toInt()
            val dpd = DatePickerDialog(
                    requireActivity(), R.style.datepicker,
                    { view: DatePicker?, yearCur: Int, monthOfYear: Int, dayOfMonth: Int ->
                        try {
                            // Display Selected date in textBox
                            val end =
                                    yearCur.toString() + "-" + (monthOfYear + 1) + "-" + dayOfMonth
                            // end = end.replace(" ".toRegex(), "-")
                            val formatter = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                            val displayFormatter = SimpleDateFormat("dd-MM-yyyy", Locale.getDefault())
                            val selectedDate = formatter.format(formatter.parse(end))
                            textDate.text = displayFormatter.format(formatter.parse(end))
                            modifiedDate = selectedDate
                            progressLoader.smoothToShow()
                            viewModel.userRank.observe(viewLifecycleOwner, ::userRank)
                            viewModel.getUserRank(UserRankData(modifiedDate, BuildConfig.SITE_ID, "daily"))
                        } catch (e: Exception) {
                            CrashlyticsLogger.logError(WonderPubSharedPrefs.getInstance(context).usermobile, "leaderboard showDateDialog", e)
                        }
                    },
                    year,
                    month,
                    day
            )

            dpd.datePicker.maxDate = Date().time
            dpd.show()
        }
    }

    private fun userRank(data: Data<JSONObject>) {
        swipeToRefresh.isRefreshing = false
        when (data.responseType) {
            Status.LOADING -> {
                progressLoader.smoothToShow()
                textNoRecord.visibility = View.GONE
            }
            Status.SUCCESSFUL -> {
                data.data?.let {
                    linearLeaderBoard.visibility = View.GONE
                    parseData(data.data!!)
                    progressLoader.smoothToHide()
                }

            }
            Status.HTTP_UNAVAILABLE, Status.ERROR -> {
                linearLeaderBoard.visibility = View.GONE
                textNoRecord.text = getString(R.string.no_record)
                textNoRecord.visibility = View.VISIBLE
                recyclerRankList.visibility = View.GONE
                rrltTopRanks.visibility = View.GONE
                progressLoader.smoothToHide()

                try {
                    if (data.error.toString().contains("401")) {
                        WonderPubSharedPrefs.getInstance(requireContext()).clearAllSharePref()
                        val intent = Intent(requireContext(), LoginActivity::class.java)
                        startActivity(intent)
                        requireActivity().finish()
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }

            }
        }
        viewModel.userRank.removeObservers(viewLifecycleOwner)
        viewModel.resetUserRank()
    }

    private fun weeklyWinnersRank(data: Data<JSONObject>) {
        swipeToRefresh.isRefreshing = false
        when (data.responseType) {
            Status.LOADING -> {
                progressLoader.smoothToShow()
                textNoRecord.visibility = View.GONE
            }
            Status.SUCCESSFUL -> {
                data.data?.let {
                    linearLeaderBoard.visibility = View.GONE
                    parseWeeklyWinners(data.data!!)
                    progressLoader.smoothToHide()
                }

            }
            Status.HTTP_UNAVAILABLE, Status.ERROR -> {
                linearLeaderBoard.visibility = View.GONE
                textNoRecord.text = getString(R.string.no_record)
                textNoRecord.visibility = View.VISIBLE
                recyclerRankList.visibility = View.GONE
                rrltTopRanks.visibility = View.GONE
                progressLoader.smoothToHide()

                try {
                    if (data.error.toString().contains("401")) {
                        WonderPubSharedPrefs.getInstance(requireContext()).clearAllSharePref()
                        val intent = Intent(requireContext(), LoginActivity::class.java)
                        startActivity(intent)
                        requireActivity().finish()
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }

            }
        }
        viewModel.weeklyWinnersRank.removeObservers(viewLifecycleOwner)
        viewModel.resetWeeklyWinnersRank()
    }

    private fun parseWeeklyWinners(jsonObject: JSONObject) {
        val arr = jsonObject.getJSONArray("arrayName")
        list = arrayListOf()
        if (arr.length() == 0) {
            weeklyWinnersParent.visibility = View.GONE
        } else {
            for (index in 0 until arr.length()) {
                val obj = arr.getJSONObject(index)
                var profileImage = ""
                if (obj.has("profilePic")) {
                    val imageFileName = obj.getString("profilePic")
                    val userId = obj.getString("userId")
                    profileImage = Utils.getImageUrl(userId, imageFileName)
                }
                list.add(UserRankDataCurrent(
                    obj.optString("name"),
                    obj.optString("rank"),
                    obj.optString("userPoints"),
                    obj.optString("username"),
                    profileImage, obj.optString("state")
                ))
            }

            if (list.size == 0 || list.isEmpty()) {
                weeklyWinnersParent.visibility = View.GONE
            } else {
                if (WonderPubSharedPrefs.getInstance(context).tickerVisibility) {
                    weeklyWinnersParent.visibility = View.VISIBLE
                    linearLeaderBoard.visibility = View.GONE
                    textNoRecord.visibility = View.GONE
                    recyclerRankList.visibility = View.VISIBLE
                    rrltTopRanks.visibility = View.VISIBLE
                    val templist: ArrayList<UserRankDataCurrent> = list
                    var user1: UserRankDataCurrent? = null
                    var user2: UserRankDataCurrent? = null
                    var user3: UserRankDataCurrent? = null
                    templist.forEachIndexed { index, userRankDataCurrent ->
                        when (index) {
                            0 -> user1 = userRankDataCurrent
                            1 -> user2 = userRankDataCurrent
                            2 -> user3 = userRankDataCurrent
                        }
                    }
                    setWeeklyWinnerRanks(user1, user2, user3)
                }
                else {
                    weeklyWinnersParent.visibility = View.GONE
                }
            }
        }
    }

    private fun parseData(jsonObject: JSONObject) {
        val arr = jsonObject.getJSONArray("arrayName")
        list = arrayListOf()
        if (arr.length() == 0) {
            textNoRecord.text = getString(R.string.no_record)
            textNoRecord.visibility = View.VISIBLE
        } else {
            for (index in 0 until arr.length()) {
                val obj = arr.getJSONObject(index)
                var profileImage = ""
                if (obj.has("profilePic")) {
                    val imageFileName = obj.getString("profilePic")
                    val userId = obj.getString("userId")
                    profileImage = Utils.getImageUrl(userId, imageFileName)
                }
                list.add(UserRankDataCurrent(
                    obj.optString("name"),
                    obj.optString("rank"),
                    obj.optString("userPoints"),
                    obj.optString("username"),
                    profileImage, obj.optString("state")
                ))
            }

            if (list.size == 0 || list.isEmpty()) {
                linearLeaderBoard.visibility = View.GONE
                textNoRecord.text = getString(R.string.no_record)
                textNoRecord.visibility = View.VISIBLE
                recyclerRankList.visibility = View.GONE
                rrltTopRanks.visibility = View.GONE
            } else {
                linearLeaderBoard.visibility = View.GONE
                textNoRecord.visibility = View.GONE
                recyclerRankList.visibility = View.VISIBLE
                rrltTopRanks.visibility = View.VISIBLE
                val templist: ArrayList<UserRankDataCurrent> = list
                var user1: UserRankDataCurrent? = null
                var user2: UserRankDataCurrent? = null
                var user3: UserRankDataCurrent? = null
                templist.forEachIndexed { index, userRankDataCurrent ->
                    when (index) {
                        0 -> user1 = userRankDataCurrent
                        1 -> user2 = userRankDataCurrent
                        2 -> user3 = userRankDataCurrent
                    }
                }
                setRanks(user1, user2, user3)
            }

            if (tabPosition != 0) {
                relativeDatePicker.visibility = View.GONE
            } else {
                relativeDatePicker.visibility = View.VISIBLE
            }
            val leaders = arrayListOf<UserRankDataCurrent>()
            list.forEachIndexed { index, userRankDataCurrent ->
                if (index > 2) {
                    leaders.add(userRankDataCurrent)
                }
            }
            loadAdapter(leaders)
        }
    }

    fun switchToWeekly() {
        tabLayout?.selectTab(tabLayout!!.getTabAt(1))
    }

    private fun loadAdapter(list: List<UserRankDataCurrent>) {
        if (list.isNotEmpty()) {
            val linearLayoutManager = LinearLayoutManager(context)
            val user = WonderPubSharedPrefs.getInstance(context).username
            recyclerRankList.layoutManager = linearLayoutManager
            recyclerRankList.adapter = LeaderBoardAdapter(list, user, this)
        } else {
            textNoRecord.text = getString(R.string.no_leaders)
            textNoRecord.visibility = View.VISIBLE
            recyclerRankList.visibility = View.GONE
        }
    }

    override fun onUserClicked(image: String, name: String, rank: String, points: String) {
        val bottomSheet = LBBottomSheetDialog()
        val bundle = Bundle()
        if (image.isNotEmpty()) {
            bundle.putString("image", image)
        }
        bundle.putString("name", name)
        bundle.putString("rank", rank)
        bundle.putString("points", points)
        bottomSheet.arguments = bundle
        bottomSheet.show(childFragmentManager, "Details")
    }

    private fun setWeeklyWinnerRanks(user1: UserRankDataCurrent?,
                                     user2: UserRankDataCurrent?,
                                     user3: UserRankDataCurrent?) {
        try {
            val visibility = if (resources.getBoolean(R.bool.show_states_leader_board)) {
                View.VISIBLE
            } else {
                View.INVISIBLE
            }
            user1?.let { user ->
                val profileImage1 = user.userProfileImage
                pwuserRankImage1.setImageResource(R.drawable.prepjoy_full_icon)
                if (profileImage1.isNotEmpty()) {
                    loadImage(pwuserRankImage1, profileImage1)
                }
                pwtxtUserPoints1.text = user.userPoints
                pwtxtUserName1.text = user.name
                pwtxtUserState1.text = user.userState
                pwuserRankImage1.setOnClickListener { showTopRankUser(user) }
                pwtxtUserState1.visibility = visibility
                pwUser1Rank.visibility = View.VISIBLE
            } ?: apply {
                pwUser1Rank.visibility = View.INVISIBLE
            }

            user2?.let { user ->
                val profileImage2 = user.userProfileImage
                pwuserRankImage2.setImageResource(R.drawable.prepjoy_full_icon)
                if (profileImage2.isNotEmpty()) {
                    loadImage(pwuserRankImage2, profileImage2)
                }
                pwtxtUserPoints2.text = user.userPoints
                pwtxtUserName2.text = user.name
                pwtxtUserState2.text = user.userState
                pwtxtUserState2.visibility = visibility
                pwuserRankImage2.setOnClickListener { showTopRankUser(user) }
                pwUser2Rank.visibility = View.VISIBLE
            } ?: apply {
                pwtxtUserPoints2.text = "-"
                pwtxtUserName2.text = "-"
                pwtxtUserState2.text = "-"
                pwtxtUserState2.visibility = visibility
                pwuserRankImage2.setOnClickListener {  }
                pwUser2Rank.visibility = View.INVISIBLE
            }

            user3?.let { user ->
                val profileImage3 = user.userProfileImage
                pwuserRankImage3.setImageResource(R.drawable.prepjoy_full_icon)
                if (profileImage3.isNotEmpty()) {
                    loadImage(pwuserRankImage3, profileImage3)
                }
                pwtxtUserPoints3.text = user.userPoints
                pwtxtUserName3.text = user.name
                pwtxtUserState3.text = user.userState
                pwtxtUserState3.visibility = visibility
                pwuserRankImage3.setOnClickListener { showTopRankUser(user) }
                pwUser3Rank.visibility = View.VISIBLE
            } ?: apply {
                pwtxtUserPoints3.text = "-"
                pwtxtUserName3.text = "-"
                pwtxtUserState3.text = "-"
                pwtxtUserState3.visibility = visibility
                pwuserRankImage3.setOnClickListener {  }
                pwUser3Rank.visibility = View.INVISIBLE
            }

        } catch (e: Exception) {
            e.message?.let { CrashlyticsLogger.logError(WonderPubSharedPrefs.getInstance(context).usermobile, "leaderboard setWeeklyWinnerRanks", e) }
        }
    }

    private fun setRanks(user1: UserRankDataCurrent?,
                         user2: UserRankDataCurrent?,
                         user3: UserRankDataCurrent?) {
        try {
            val visibility = if (resources.getBoolean(R.bool.show_states_leader_board)) {
                View.VISIBLE
            } else {
                View.INVISIBLE
            }
            user1?.let { user ->
                val profileImage1 = user.userProfileImage
                userRankImage1.setImageResource(R.drawable.prepjoy_full_icon)
                if (profileImage1.isNotEmpty()) {
                    loadImage(userRankImage1, profileImage1)
                }
                txtUserPoints1.text = user.userPoints
                txtUserName1.text = user.name
                txtUserState1.text = user.userState
                userRankImage1.setOnClickListener { showTopRankUser(user) }
                txtUserState1.visibility = visibility
                rrltUser1Rank.visibility = View.VISIBLE
            } ?: apply {
                rrltUser1Rank.visibility = View.INVISIBLE
            }

            user2?.let { user ->
                val profileImage2 = user.userProfileImage
                userRankImage2.setImageResource(R.drawable.prepjoy_full_icon)
                if (profileImage2.isNotEmpty()) {
                    loadImage(userRankImage2, profileImage2)
                }
                txtUserPoints2.text = user.userPoints
                txtUserName2.text = user.name
                txtUserState2.text = user.userState
                txtUserState2.visibility = visibility
                userRankImage2.setOnClickListener { showTopRankUser(user) }
                rrltUser2Rank.visibility = View.VISIBLE
            } ?: apply {
                txtUserPoints2.text = "-"
                txtUserName2.text = "-"
                txtUserState2.text = "-"
                txtUserState2.visibility = visibility
                userRankImage2.setOnClickListener {  }
                rrltUser2Rank.visibility = View.INVISIBLE
            }

            user3?.let { user ->
                val profileImage3 = user.userProfileImage
                userRankImage3.setImageResource(R.drawable.prepjoy_full_icon)
                if (profileImage3.isNotEmpty()) {
                    loadImage(userRankImage3, profileImage3)
                }
                txtUserPoints3.text = user.userPoints
                txtUserName3.text = user.name
                txtUserState3.text = user.userState
                txtUserState3.visibility = visibility
                userRankImage3.setOnClickListener { showTopRankUser(user) }
                rrltUser3Rank.visibility = View.VISIBLE
            } ?: apply {
                txtUserPoints3.text = "-"
                txtUserName3.text = "-"
                txtUserState3.text = "-"
                txtUserState3.visibility = visibility
                userRankImage3.setOnClickListener {  }
                rrltUser3Rank.visibility = View.INVISIBLE
            }

        } catch (e: Exception) {
            e.message?.let { CrashlyticsLogger.logError(WonderPubSharedPrefs.getInstance(context).usermobile, "leaderboard setRanks", e) }
        }
    }

    private fun loadImage(view: ImageView, url: String) {
        Glide.with(this).load(url)
            .diskCacheStrategy(DiskCacheStrategy.NONE)
            .skipMemoryCache(true)
            .dontAnimate()
            .into(object : CustomTarget<Drawable>() {
                override fun onResourceReady(resource: Drawable,
                                             transition: Transition<in Drawable>?) {
                    view.setImageDrawable(resource)
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                }

            })
    }

    private fun showTopRankUser(user: UserRankDataCurrent) {
        onUserClicked(user.userProfileImage, user.name, user.rank, user.userPoints)
    }
}