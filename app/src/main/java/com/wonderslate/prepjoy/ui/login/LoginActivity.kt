package com.wonderslate.prepjoy.ui.login

import android.Manifest
import android.app.Activity
import android.app.AlertDialog
import android.content.*
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.CountDownTimer
import android.os.Handler
import android.os.Looper
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.Button
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.wonderslate.commons.Data
import com.wonderslate.commons.Status
import com.wonderslate.data.network.WSAPIManager
import com.wonderslate.data.network.Wonderslate
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.domain.entities.*
import com.wonderslate.prepjoy.BuildConfig
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.Utils.FlavourHelper
import com.wonderslate.prepjoy.Utils.Utils
import com.wonderslate.prepjoy.Utils.Utils.disableScreenShot
import com.wonderslate.prepjoy.Utils.ValidationHelper
import com.wonderslate.prepjoy.ui.BaseActivity
import com.wonderslate.prepjoy.ui.dashboard.DashBoardActivity
import com.wonderslate.prepjoy.ui.signup.SignUpActivity
import com.wonderslate.prepjoy.databinding.ActivityLoginBinding
import org.json.JSONObject
import org.koin.androidx.viewmodel.ext.android.viewModel


class LoginActivity : BaseActivity() {
    private lateinit var context: Context
    private val viewModel by viewModel<LoginViewModel>()
    private lateinit var binding: ActivityLoginBinding

    private lateinit var btnGetOtp: Button
    private lateinit var editMobileNo: EditText
    private lateinit var editOtp: EditText
    private lateinit var editPassword: EditText
    private lateinit var signUpTxtBtn: TextView
    private lateinit var forgotPasswd: TextView
    private lateinit var llOtp: LinearLayout
    private lateinit var llPasswd: LinearLayout
    private lateinit var llSignUp: LinearLayout
    private lateinit var txtSignUp: TextView
    private lateinit var loader: ProgressBar

    private var isLogin: Boolean = false
    private var isResend: Boolean = false
    private var isOTPLogin: Boolean = false
    private var waitTimer: CountDownTimer? = null
    private var smsBroadcastReceiver: SmsBroadcastReceiver? = null
    private val REQUEST_ID_MULTIPLE_PERMISSIONS = 1

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLoginBinding.inflate(layoutInflater)
        setContentView(binding.root)
        isOTPLogin = FlavourHelper.isOTPLogin()
        initViews()
        initObserver()
        binding.progressbar.isIndeterminate = true
        binding.btnLogin.setOnClickListener {
            showHideLoader(true)
            if (isOTPLogin) {
                if (isLogin) {
                    viewModel.validateOtp(
                            ValidateOtpData(
                                    editMobileNo.text.toString(),
                                    editOtp.text.toString(),
                                    BuildConfig.SITE_ID
                            )
                    )
                } else {
                    if (isEmailOrMobile(editMobileNo.text.toString()).isNotEmpty()) {
                        // Call server API for requesting OTP and when you got success start
                        // SMS Listener for listing auto read message listener
                        
                        binding.txtResendOtp.setTextColor(ContextCompat.getColor(this, R.color.disablecolor))
                        if (isEmailOrMobile(editMobileNo.text.toString()).equals("mobile", true)) {
                            viewModel.sendOtp(
                                    SendOtpData(
                                            BuildConfig.SMS_APP_CODE_DEBUG,
                                            BuildConfig.SITE_ID,
                                            "",
                                            editMobileNo.text.toString(),
                                            "",
                                            false
                                    )
                            )
                        }
                        else {
                            viewModel.sendOtp(
                                    SendOtpData(
                                            BuildConfig.SMS_APP_CODE_DEBUG,
                                            BuildConfig.SITE_ID,
                                            "",
                                            "",
                                            editMobileNo.text.toString(),
                                            false
                                    )
                            )
                        }
                    } else {
                        Utils.showTopSnackBar("Enter valid phone number", this)
                    }
                }
            }
            else {
                
                WonderPubSharedPrefs.getInstance(this@LoginActivity).username =
                        editMobileNo.text.toString()
                WonderPubSharedPrefs.getInstance(this).usermobile =  editMobileNo.text.toString()
                stopTimer()
                viewModel.login(
                        LoginData(
                                editMobileNo.text.toString(),
                                editPassword.text.toString()
                                , BuildConfig.SITE_ID)
                )
            }
            showHideLoader(false)
        }
        binding.edtMobileNumber.setOnClickListener {
            binding.rrltResendOtp.visibility = View.GONE
            editOtp.visibility = View.GONE
            editMobileNo.isEnabled = true
        }
        binding.txtResendOtp.setOnClickListener {
            showHideLoader(true)
            binding.imgEditMobile.isEnabled = false
            binding.progressbar.visibility = View.VISIBLE
            isResend = true
            startOTPTimer()
            binding.txtReSendOtpSeconds.visibility = View.VISIBLE
            binding.txtResendOtp.setTextColor(ContextCompat.getColor(this, R.color.disablecolor))
            binding.txtResendOtp.isEnabled = false
            if (isEmailOrMobile(editMobileNo.text.toString()).isNotEmpty()) {
                viewModel.sendOtp(
                        if (isEmailOrMobile(editMobileNo.text.toString()).equals("mobile", true)) {
                            SendOtpData(
                                    BuildConfig.SMS_APP_CODE_DEBUG,
                                    BuildConfig.SITE_ID,
                                    "",
                                    editMobileNo.text.toString(),
                                    "",
                                    true
                            )
                        } else{
                            SendOtpData(
                                    BuildConfig.SMS_APP_CODE_DEBUG,
                                    BuildConfig.SITE_ID,
                                    "",
                                    "",
                                    editMobileNo.text.toString(),
                                    true
                            )
                        }
                )
            }
            startSmsUserConsent()
            showHideLoader(false)
        }
        if (supportActionBar != null) {
            supportActionBar!!.hide()
        }
        binding.imgEditMobile.setOnClickListener {
            binding.rrltResendOtp.visibility = View.GONE
            editOtp.text.clear()
            editOtp.visibility = View.GONE
            editMobileNo.isEnabled = true
            btnGetOtp.isEnabled = true
            if (editMobileNo.requestFocus()) {
                window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
            }
            btnGetOtp.setBackgroundResource(R.drawable.button_shape_default)
            binding.imgEditMobile.visibility = View.GONE
            isLogin = false
            isResend = false
            stopTimer()
            binding.txtValidateOTP.visibility = View.GONE
            btnGetOtp.text = resources.getString(R.string.get_otp)
        }

        editOtp.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(arg0: Editable) {
                //Do Nothing
            }

            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
                //Do Nothing
            }
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                if (editOtp.text.toString().length == 6) {
                    btnGetOtp.isEnabled = true
                    btnGetOtp.text = resources.getString(R.string.verify_otp)
                    btnGetOtp.setBackgroundResource(R.drawable.button_shape_default)

                } else {
                    btnGetOtp.isEnabled = false
                    btnGetOtp.text = resources.getString(R.string.verify_otp)
                    btnGetOtp.setBackgroundResource(R.drawable.button_shape_disabled)
                    binding.txtValidateOTP.visibility = View.GONE
                }
            }
        })

        editMobileNo.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(arg0: Editable) {
                //Do Nothing
            }

            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
                //Do Nothing
            }
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                binding.imgEditMobile.visibility = View.GONE
                binding.txtValidateOTP.visibility = View.GONE
                if (binding.txtOtpSentMobile.isVisible) {
                    binding.txtOtpSentMobile.visibility = View.GONE
                }

                if (isEmailOrMobile(editMobileNo.text.toString()).isNotEmpty()) {
                    binding.btnLogin.isEnabled = true
                    btnGetOtp.setBackgroundResource(R.drawable.button_shape_default)

                }
                else {
                    btnGetOtp.isEnabled = false
                    btnGetOtp.setBackgroundResource(R.drawable.button_shape_disabled)
                }
            }
        })

        if(WonderPubSharedPrefs.getInstance(context).getisUserLoggedIn())
        {
            val intent = Intent(context, DashBoardActivity::class.java)
            startActivity(intent)
        }
        startSmsUserConsent()
        disableScreenShot(this)
    }

    override fun getLayoutResource(): Int {
        return R.layout.activity_login
    }

    private fun startSmsUserConsent() {
        val client = SmsRetriever.getClient(this)
        client.startSmsUserConsent(null).addOnSuccessListener {}.addOnFailureListener {}
    }

    private fun getOtpFromMessage(message: String) {
        try {
            val result = message.substringAfter("Use ").substringBefore(" as")
            editOtp.setText(result.trim())

            
            viewModel.validateOtp(
                    ValidateOtpData(
                            editMobileNo.text.toString(),
                            editOtp.text.toString(),
                            BuildConfig.SITE_ID
                    )
            )
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }
    }

    private fun registerBroadcastReceiver() {
        smsBroadcastReceiver = SmsBroadcastReceiver()
        smsBroadcastReceiver!!.smsBroadcastReceiverListener = object : SmsBroadcastReceiver.SmsBroadcastReceiverListener {
            override fun onSuccess(intent: Intent?) {
                requestUserForOTP.launch(intent)
            }

            override fun onFailure() {
                //Do Nothing
            }
        }
        val intentFilter = IntentFilter(SmsRetriever.SMS_RETRIEVED_ACTION)
        registerReceiver(smsBroadcastReceiver, intentFilter, Context.RECEIVER_NOT_EXPORTED)
    }

    override fun onStart() {
        super.onStart()
        registerBroadcastReceiver()
    }

    override fun onStop() {
        super.onStop()
        unregisterReceiver(smsBroadcastReceiver)
    }

    var requestUserForOTP = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        try{
            if (result.resultCode == RESULT_OK) {
                val message = result.data?.getStringExtra(SmsRetriever.EXTRA_SMS_MESSAGE)
                getOtpFromMessage(message!!)
            }
        }catch (e:Exception){
            e.printStackTrace()
        }
    }

    private fun initViews() {
        context = this
        btnGetOtp = findViewById(R.id.btnLogin)
        editMobileNo = findViewById(R.id.edtMobileNumber)
        editOtp = findViewById(R.id.edtOtp)
        editPassword = findViewById(R.id.edtPassword)
        signUpTxtBtn = findViewById(R.id.txtSignUpBtn)
        txtSignUp = findViewById(R.id.txtSignUp)
        forgotPasswd = findViewById(R.id.txtForgotPassword)
        llOtp = findViewById(R.id.llOtp)
        llPasswd = findViewById(R.id.llPasswd)
        llSignUp = findViewById(R.id.llSignUp)
        loader = findViewById(R.id.lvCenter)

        signUpTxtBtn.setOnClickListener {
            val intent = Intent(context, SignUpActivity::class.java)
            startActivity(intent)
            finish()
        }

        if (!isOTPLogin) {
            llPasswd.visibility = View.VISIBLE
            llOtp.visibility = View.GONE
            btnGetOtp.text = "Login"
            txtSignUp.text = buildString {
                append("New to ")
                append(FlavourHelper.companyName())
                append("?")
            }
        }
        else {
            editPassword.visibility = View.GONE
            forgotPasswd.visibility = View.GONE
            signUpTxtBtn.visibility = View.GONE
            editOtp.visibility = View.GONE
        }

        forgotPasswd.setOnClickListener {
            if (isEmailOrMobile(editMobileNo.text.toString()).isNotEmpty()) {
                // Call server API for requesting OTP and when you got success start
                // SMS Listener for listing auto read message listener
                llPasswd.visibility = View.GONE
                llOtp.visibility = View.VISIBLE
                
                binding.txtResendOtp.setTextColor(ContextCompat.getColor(this, R.color.disablecolor))
                if (isEmailOrMobile(editMobileNo.text.toString()).equals("mobile", true)) {
                    viewModel.sendOtp(
                            SendOtpData(
                                    BuildConfig.SMS_APP_CODE_DEBUG,
                                    BuildConfig.SITE_ID,
                                    "",
                                    editMobileNo.text.toString(),
                                    "",
                                    false
                            )
                    )
                }
                else {
                    viewModel.sendOtp(
                            SendOtpData(
                                    BuildConfig.SMS_APP_CODE_DEBUG,
                                    BuildConfig.SITE_ID,
                                    "",
                                    "",
                                    editMobileNo.text.toString(),
                                    false
                            )
                    )
                }
            }
        }
    }

    private fun initObserver() {
        viewModel.sendOtpResponse.observe(this, ::sendOtpResponse)
        viewModel.validateOtpResponse.observe(this, ::validateOtpResponse)
        viewModel.loginResponse.observe(this, ::loginResponse)
        viewModel.userDetailsResponse.observe(this, ::userDetailsResponse)
        viewModel.defaultCategoryResponse.observe(this, ::defaultCategoryResponse)
        viewModel.userSelectedCategoryResponse.observe(this, ::userSelectedCategoryResponse)
    }

    private fun showHideLoader(value: Boolean) {
        if (value)
            loader.visibility = View.VISIBLE
        else
            loader.visibility = View.GONE
    }

    private fun defaultCategoryResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
                //Do Nothing
            }

            Status.SUCCESSFUL -> {
                data.data?.let {
                    try {
                        val jObj = JSONObject(data.data.toString())
                        Wonderslate.getInstance().sharedPrefs.defaultCategory = jObj.toString()
                        viewModel.getUserSelectedCategory(UserSelectedCategoryData(BuildConfig.SITE_ID))

                    } catch (e: Exception) {
                        e.printStackTrace()

                    }
                }
            }

            Status.HTTP_UNAVAILABLE -> {
                
                stopTimer()
                if (isOTPLogin) {
                    changeButtonText()
                }
                Utils.showTopSnackBar(resources.getString(R.string.server_under_maintenance), this)
            }

            Status.ERROR -> {
                
                stopTimer()
                if (isOTPLogin) {
                    changeButtonText()
                }
                Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), this)
            }
        }
    }

    private fun userSelectedCategoryResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
                //do nothing
            }

            Status.SUCCESSFUL -> {
                data.data?.let {
                    try {
                        val jObj = JSONObject(JSONObject(data.data.toString()).optString("userGrades")
                            .replaceFirst("\"", "").dropLast(1).replace("\\", ""))
                        Wonderslate.getInstance().sharedPrefs.userPrefs = jObj.toString()

                        val userPrefsObject = JSONObject(WonderPubSharedPrefs.getInstance(context).userPrefs)
                        val level = userPrefsObject.optString("selectedLevel")
                        val syllabus = userPrefsObject.optString("selectedSyllabus")
                        val grade = userPrefsObject.optString("selectedGrade")
                        WonderPubSharedPrefs.getInstance(context).sharedPrefsUserLevelPref = level
                        WonderPubSharedPrefs.getInstance(context).sharedPrefsUserSyllabusPref = listOf(syllabus)
                        WonderPubSharedPrefs.getInstance(context).sharedPrefsUserGradePref = listOf(grade)

                        val intent = Intent(context, DashBoardActivity::class.java)
                        intent.putExtra("isFirstTime", true)
                        startActivity(intent)
                        finish()

                    } catch (e: Exception) {
                        e.printStackTrace()
                        Wonderslate.getInstance().sharedPrefs.userPrefs = ""

                        WonderPubSharedPrefs.getInstance(context).sharedPrefsUserLevelPref = ""
                        WonderPubSharedPrefs.getInstance(context).sharedPrefsUserSyllabusPref = listOf("")
                        WonderPubSharedPrefs.getInstance(context).sharedPrefsUserGradePref = listOf("")

                        val intent = Intent(context, DashBoardActivity::class.java)
                        intent.putExtra("isFirstTime", true)
                        startActivity(intent)
                        finish()

                    }
                }
            }

            Status.HTTP_UNAVAILABLE, Status.ERROR -> {
                Wonderslate.getInstance().sharedPrefs.userPrefs = ""

                WonderPubSharedPrefs.getInstance(context).sharedPrefsUserLevelPref = ""
                WonderPubSharedPrefs.getInstance(context).sharedPrefsUserSyllabusPref = listOf("")
                WonderPubSharedPrefs.getInstance(context).sharedPrefsUserGradePref = listOf("")

                val intent = Intent(context, DashBoardActivity::class.java)
                intent.putExtra("isFirstTime", true)
                startActivity(intent)
                finish()
            }
        }
    }

    private fun isEmailOrMobile(userId: String): String {
        if (ValidationHelper.validatePhoneNumber(userId)) {
            return "mobile"
        }
        return if (ValidationHelper.ValidateEmail(userId)) {
            "email"
        } else ""
    }

    private fun sendOtpResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
                // do nothing
            }
            Status.SUCCESSFUL -> {
                // close loading
                if (isResend) {
                    binding.progressbar.visibility = View.GONE
                    binding.imgEditMobile.visibility = View.VISIBLE
                    binding.imgEditMobile.isClickable = true
                    binding.edtMobileNumber.isEnabled = false
                    btnGetOtp.setBackgroundResource(R.drawable.button_shape_disabled)
                    btnGetOtp.isEnabled = false
                    binding.edtOtp.visibility = View.VISIBLE
                    binding.rrltResendOtp.visibility = View.VISIBLE
                    startOTPTimer()
                    isLogin = true
                    data.data?.let {
                        changeOtpBtnText(it)
                    }
                } else {
                    binding.progressbar.visibility = View.GONE
                    binding.imgEditMobile.visibility = View.VISIBLE
                    binding.imgEditMobile.isClickable = true
                    binding.edtMobileNumber.isEnabled = false
                    btnGetOtp.setBackgroundResource(R.drawable.button_shape_disabled)
                    // btnGetOtp.text = resources.getString(R.string.verify_otp)
                    binding.txtReSendOtpSeconds.visibility = View.VISIBLE
                    btnGetOtp.isEnabled = false
                    binding.edtOtp.visibility = View.VISIBLE
                    binding.rrltResendOtp.visibility = View.VISIBLE
                    startOTPTimer()
                    isLogin = true
                    data.data?.let {
                        changeOtpBtnText(it)
                    }
                }
                isOTPLogin = true

            }

            Status.HTTP_UNAVAILABLE -> {
                
                stopTimer()
                btnGetOtp.setBackgroundResource(R.drawable.button_shape_default)
                Utils.showTopSnackBar(resources.getString(R.string.server_under_maintenance), this)
            }

            Status.ERROR -> {
                
                stopTimer()
                btnGetOtp.setBackgroundResource(R.drawable.button_shape_default)
                Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), this)
            }
        }
    }

    private fun validateOtpResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
                //do nothing
            }

            Status.SUCCESSFUL -> {
                data.data?.let {
                    if(editMobileNo.text.toString() == "5553335551")
                    {
                        WonderPubSharedPrefs.getInstance(this@LoginActivity).username =
                                editMobileNo.text.toString()
                        WonderPubSharedPrefs.getInstance(this).usermobile =  editMobileNo.text.toString()
                        stopTimer()
                        viewModel.login(
                                LoginData(
                                        editMobileNo.text.toString(),
                                        resources.getString(R.string.defaultPassword)
                                        , BuildConfig.SITE_ID)
                        )
                    }
                    else{
                        loginCheck(it)
                    }

                }
            }

            Status.HTTP_UNAVAILABLE -> {
                
                stopTimer()
                if (isOTPLogin) {
                    changeButtonText()
                }
                Utils.showTopSnackBar(resources.getString(R.string.server_under_maintenance), this)
            }

            Status.ERROR -> {
                
                stopTimer()
                if (isOTPLogin) {
                    changeButtonText()
                }
                Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), this)
            }
        }
    }

    private fun loginResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
                // do nothing
            }

            Status.SUCCESSFUL -> {
                viewModel.clearAllCachedData()
                data.data?.let {
                    WonderPubSharedPrefs.getInstance(this@LoginActivity).accessToken =
                        it.optString("access_token")
                    viewModel.userDetails(
                        UserDetailsData(BuildConfig.SITE_ID)
                    )
                }
            }

            Status.HTTP_UNAVAILABLE -> {
                
                stopTimer()
                if (isOTPLogin) {
                    changeButtonText()
                }
                Utils.showTopSnackBar(resources.getString(R.string.server_under_maintenance), this)
            }

            Status.ERROR -> {
                
                stopTimer()
                if (isOTPLogin) {
                    changeButtonText()
                }
                Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), this)
            }
        }
    }

    private fun userDetailsResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
                //do nothing
            }

            Status.SUCCESSFUL -> {
                data.data?.let {
                    try {
                        val jObj = JSONObject(data.data.toString())
                        stopTimer()
                        WonderPubSharedPrefs.getInstance(this).username = jObj.getString("name")
                        WonderPubSharedPrefs.getInstance(this).usermobile = jObj.getString("mobile")
                        WonderPubSharedPrefs.getInstance(this).userId = jObj.getString("id")
                        WonderPubSharedPrefs.getInstance(this).userState = jObj.getString("state")
                        WonderPubSharedPrefs.getInstance(this).setUserEmail(jObj.getString("email"))
                        WonderPubSharedPrefs.getInstance(this).userDistrict =
                            jObj.getString("district")
                        WonderPubSharedPrefs.getInstance(this).userImage =
                            WSAPIManager.SERVICE.toString()
                                .plus("funlearn/showProfileImage?id=" + jObj.getString("id"))
                                .plus("&fileName=")
                                .plus(jObj.getString("profilePic") + "&type=user&imgType=passport")

                        WonderPubSharedPrefs.getInstance(this).setisUserLoggedIn(true)
                        hideKeyboard(this)
                        viewModel.getDefaultStoreCategory(OnBoardingData(BuildConfig.SITE_ID))

                    } catch (e: Exception) {
                        e.printStackTrace()

                    }
                }
            }

            Status.HTTP_UNAVAILABLE -> {
                
                stopTimer()
                if (isOTPLogin) {
                    changeButtonText()
                }
                Utils.showTopSnackBar(resources.getString(R.string.server_under_maintenance), this)
            }

            Status.ERROR -> {
                
                stopTimer()
                if (isOTPLogin) {
                    changeButtonText()
                }
                Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), this)
            }
        }
    }

    private fun loginCheck(jsonObject: JSONObject) {
        if (jsonObject.getString("status") == "OK") {
            if (jsonObject.getString("userExists") == "true"
                    && jsonObject.getString("allowLogin") == "true" || jsonObject.getString("allowLogin") == "false"
            ) {           WonderPubSharedPrefs.getInstance(this@LoginActivity).username =
                    editMobileNo.text.toString()
                WonderPubSharedPrefs.getInstance(this).usermobile =  editMobileNo.text.toString()

                WonderPubSharedPrefs.getInstance(this@LoginActivity).accessToken =
                        jsonObject.optString("token")
                viewModel.userDetails(
                        UserDetailsData(BuildConfig.SITE_ID)
                )


            } else if (jsonObject.getString("userExists") == "false") {
                WonderPubSharedPrefs.getInstance(this@LoginActivity).username =
                    editMobileNo.text.toString()
                WonderPubSharedPrefs.getInstance(this).usermobile =  editMobileNo.text.toString()
                stopTimer()
                val intent = Intent(context, SignUpActivity::class.java)
                startActivity(intent)
                finish()
            }

        } else {

            if (isOTPLogin) {
                changeButtonText()
            }
            binding.txtValidateOTP.visibility = View.VISIBLE
            binding.txtValidateOTP.text = resources.getString(R.string.enter_valid_otp)
        }
    }

    private fun changeOtpBtnText(jsonObject: JSONObject) {
        if (jsonObject.getString("status") == "OK") {
            Handler(Looper.getMainLooper()).postDelayed({
                btnGetOtp.text = context.resources.getString(R.string.verify_otp)
            }, 1000)
        }
    }

    private fun changeButtonText() {
        Handler(Looper.getMainLooper()).postDelayed(
            {
                btnGetOtp.text = context.resources.getString(R.string.verify_otp)

            }, 1000
        )
    }

    private fun startOTPTimer() {
        stopTimer()
        waitTimer = object : CountDownTimer(20000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                val seconds = millisUntilFinished / 1000
                // val minutes = seconds / 60
                val time = String.format("%02d", seconds % 60)
                binding.txtReSendOtpSeconds.text = time
            }

            override fun onFinish() {
                binding.txtReSendOtpSeconds.visibility = View.GONE
                binding.txtResendOtp.isEnabled = true
                binding.imgEditMobile.isEnabled = true
                binding.txtResendOtp.setTextColor(
                    ContextCompat.getColor(
                        this@LoginActivity,
                        R.color.primary_bg_red
                    )
                )
            }
        }.start()
    }

    private fun stopTimer() {
        if (waitTimer != null) {
            waitTimer!!.cancel()
            waitTimer = null
        }
    }
    override fun onBackPressed() {
        super.onBackPressed()
        stopTimer()
        finish()
    }

    private fun checkAndRequestPermissions(): Boolean {
        val permissionSendMessage = ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE)
        val locationPermission = ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE)
        val listPermissionsNeeded: MutableList<String> = ArrayList()
        if (locationPermission != PackageManager.PERMISSION_GRANTED) {
            listPermissionsNeeded.add(Manifest.permission.READ_EXTERNAL_STORAGE)
        }
        if (permissionSendMessage != PackageManager.PERMISSION_GRANTED) {
            listPermissionsNeeded.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
        }
        if (listPermissionsNeeded.isNotEmpty()) {
            ActivityCompat.requestPermissions(this, listPermissionsNeeded.toTypedArray(), REQUEST_ID_MULTIPLE_PERMISSIONS)
            return false
        }
        return true
    }


    override fun onRequestPermissionsResult(requestCode : Int ,
                                            permissions: Array<String>,
                                            grantResults: IntArray)
    {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            REQUEST_ID_MULTIPLE_PERMISSIONS -> {
                val perms: MutableMap<String, Int> = HashMap()
                perms[Manifest.permission.READ_EXTERNAL_STORAGE] = PackageManager.PERMISSION_GRANTED
                perms[Manifest.permission.WRITE_EXTERNAL_STORAGE] = PackageManager.PERMISSION_GRANTED
                if (grantResults.isNotEmpty()) {
                    var i = 0
                    while (i < permissions.size) {
                        perms[permissions[i]] = grantResults[i]
                        i++
                    }
                    // Check for both permissions
                    if (perms[Manifest.permission.READ_EXTERNAL_STORAGE] != PackageManager.PERMISSION_GRANTED
                            && perms[Manifest.permission.WRITE_EXTERNAL_STORAGE] != PackageManager.PERMISSION_GRANTED) {
                        if (ActivityCompat.shouldShowRequestPermissionRationale(this, Manifest.permission.READ_EXTERNAL_STORAGE)
                                || ActivityCompat.shouldShowRequestPermissionRationale(this, Manifest.permission.WRITE_EXTERNAL_STORAGE))
                        {
                            showDialogOK("Storage Permission required for this app"
                            ) { _, which ->
                                when (which) {
                                    DialogInterface.BUTTON_POSITIVE -> checkAndRequestPermissions()
                                    DialogInterface.BUTTON_NEGATIVE -> {
                                        //Do Nothing
                                    }
                                }
                            }
                        }
                        else
                        {
                            Toast.makeText(this, "Go to settings and enable permissions", Toast.LENGTH_LONG).show()
                        }
                    }
                }
            }
        }
    }

    private fun showDialogOK(message: String, okListener: DialogInterface.OnClickListener) {
        AlertDialog.Builder(this)
                .setMessage(message)
                .setPositiveButton("OK", okListener)
                .setNegativeButton("Cancel", okListener)
                .create()
                .show()
    }
    private fun hideKeyboard(activity: Activity) {
        val imm = activity.getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        var view = activity.currentFocus
        if (view == null) {
            view = View(activity)
        }
        imm.hideSoftInputFromWindow(view.windowToken, 0)
    }
}