package com.wonderslate.prepjoy.ui.leaderboard

import android.annotation.SuppressLint
import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.wonderslate.domain.entities.UserRankDataCurrent
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.bumptech.glide.signature.ObjectKey
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.Utils.Utils
import com.wonderslate.prepjoy.ui.leaderboard.LeaderBoardAdapter.LeaderBoardHolder

class LeaderBoardAdapter internal constructor(private val list: List<UserRankDataCurrent>,
                                              val currentUser: String,
                                              val listener: LeaderBoardListener) : RecyclerView.Adapter<LeaderBoardHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LeaderBoardHolder {
        return LeaderBoardHolder(LayoutInflater.from(parent.context).inflate(R.layout.item_leader_board, parent, false))
    }

    override fun onBindViewHolder(holder: LeaderBoardHolder, @SuppressLint("RecyclerView") position: Int) {
        val name = list[position].name
        val points = list[position].userPoints
        val rank = list[position].rank
        val profileImage = list[position].userProfileImage
        val state = list[position].userState
        holder.textName.text = name
        holder.textPoints.text = points
        holder.textRank.text = rank
        holder.userName.text = name
        holder.userPoints.text = points
        holder.userRank.text = rank
        holder.userState.text = state
        holder.textState.text = state

        holder.userImage.setImageResource(R.drawable.prepjoy_full_icon)
        if (profileImage.isNotEmpty()) {
            Glide.with(holder.itemView.context)
                    .load(profileImage)
                    .diskCacheStrategy(DiskCacheStrategy.NONE)
                    .skipMemoryCache(true)
                    .dontAnimate()
                    .into(object: CustomTarget<Drawable>() {
                        override fun onResourceReady(resource: Drawable, transition: Transition<in Drawable>?) {
                            holder.userImage.setImageDrawable(resource)
                        }

                        override fun onLoadCleared(placeholder: Drawable?) {
                        }

                    })        }
        holder.parentView.setOnClickListener {
            listener.onUserClicked(profileImage, name, rank, points)
        }
        if (currentUser.equals(name, true)) {
            holder.showUser()
        } else {
            holder.showOthers()
        }
    }

    override fun getItemCount(): Int {
        return list.size
    }

    class LeaderBoardHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val textRank: TextView = itemView.findViewById(R.id.textRank)
        val textName: TextView = itemView.findViewById(R.id.textName)
        val textPoints: TextView = itemView.findViewById(R.id.textScore)
        val userName: TextView = itemView.findViewById(R.id.userName)
        val userRank: TextView = itemView.findViewById(R.id.userRank)
        val userPoints: TextView = itemView.findViewById(R.id.userScore)
        val userImage: ImageView = itemView.findViewById(R.id.userRankImage)
        val parentView: LinearLayout = itemView.findViewById(R.id.item_leader_board_parent)
        val userState: TextView = itemView.findViewById(R.id.userState)
        val textState: TextView = itemView.findViewById(R.id.textState)
        val layoutTextNamestate: LinearLayout = itemView.findViewById(R.id.lltTextNameState)
        val layoutUserNamestate: LinearLayout = itemView.findViewById(R.id.lltNameState)


        fun showUser() {
            userName.visibility = View.VISIBLE
            userRank.visibility = View.VISIBLE
            userPoints.visibility = View.VISIBLE
            userState.visibility = View.VISIBLE
            layoutUserNamestate.visibility = View.VISIBLE

            textRank.visibility = View.GONE
            textName.visibility = View.GONE
            textPoints.visibility = View.GONE
            textState.visibility = View.GONE
            layoutTextNamestate.visibility = View.GONE
            displayStateInfo()
        }

        fun showOthers() {
            userName.visibility = View.GONE
            userRank.visibility = View.GONE
            userPoints.visibility = View.GONE
            userState.visibility = View.GONE
            layoutUserNamestate.visibility = View.GONE

            textRank.visibility = View.VISIBLE
            textName.visibility = View.VISIBLE
            textPoints.visibility = View.VISIBLE

            textState.visibility = View.VISIBLE
            layoutTextNamestate.visibility = View.VISIBLE
            displayStateInfo()
        }

        private fun displayStateInfo() {
            val showState = textRank.context.resources.getBoolean(R.bool.show_states_leader_board)
            val visibility = if (showState) {
                View.VISIBLE
            } else {
                View.GONE
            }
            textState.visibility = visibility
            userState.visibility = visibility
        }
    }
}