package com.wonderslate.prepjoy.ui.videos

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.NonNull
import androidx.cardview.widget.CardView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.google.firebase.analytics.FirebaseAnalytics
import com.wonderslate.domain.entities.VideosData
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.Utils.FirebaseAnalyticsUtils
import java.io.UnsupportedEncodingException
import java.net.URLDecoder
import java.nio.charset.StandardCharsets
import java.util.regex.Pattern

class VideoAdapter(recyclerDataArrayList: ArrayList<VideosData>, private val mContext: Context,
                   private val videoActivity: VideoActivity
) :
        RecyclerView.Adapter<VideoAdapter.RecyclerViewHolder?>() {

    private val videoDataArrayList: ArrayList<VideosData> = recyclerDataArrayList

    @NonNull
    override fun onCreateViewHolder(@NonNull parent: ViewGroup, viewType: Int): RecyclerViewHolder {
        val view: View = LayoutInflater.from(parent.getContext()).inflate(R.layout.layout_video_card, parent, false)
        return RecyclerViewHolder(view)
    }
    override fun onBindViewHolder(@NonNull holder: RecyclerViewHolder, position: Int) {
        val videoData: VideosData = videoDataArrayList[position]
        holder.txtTitle.setText(videoData.title)

      /*  if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            holder.txtDescription.setText(Html.fromHtml(videoData.description, FROM_HTML_MODE_LEGACY))
        }*/
        holder.txtDescription.setText(videoData.dateCreated)
        holder.txtDescription.visibility = View.VISIBLE
        //holder.imgVideoCover.setImageResource(videoData.getImgid())
        holder.cardVideo.setOnClickListener{
            try {
                videoActivity.logVideoOpen(videoData.id.toString())
                // CustomMediaPlayer functionality temporarily disabled for demo
                // val videoPlayerIntent = Intent(mContext, CustomMediaPlayer::class.java)
                videoPlayerIntent.putExtra("resLink", videoData.videoLink)
                videoPlayerIntent.putExtra("videoResName", videoData.title)
                mContext.startActivity(videoPlayerIntent)
                val bundle = Bundle()
                bundle.putString(FirebaseAnalyticsUtils.ACTIVITY_NAME, "VideoAdapter watch")
                bundle.putString(FirebaseAnalyticsUtils.LOCATION, "VideoAdapter watch")
                FirebaseAnalytics.getInstance(mContext).logEvent(FirebaseAnalyticsUtils.ACTION, bundle)
            }
            catch (e:Exception)
            {
                e.printStackTrace()
            }
        }

        var pReslink: String? = null
        if (videoData.videoLink  != null) {
            pReslink = Uri.parse(videoData.videoLink .trim { it <= ' ' }).toString()
            try {
                pReslink = URLDecoder.decode(videoData.videoLink , StandardCharsets.UTF_8.name())
            } catch (e: UnsupportedEncodingException) {
                Log.e("VideoAdapter", "Exception while getting res link")
            }
        }

        val id: String
        if (pReslink != null && !pReslink.isEmpty()
                && (!pReslink.contains("/")
                        || pReslink.contains("youtube")
                        || pReslink.contains("youtu.be"))) {
            id = ("https://i.ytimg.com/vi/" + extractYTId(pReslink )
                    + "/0.jpg?sqp=-oaymwEZCOgCEMoBSFXyq4qpAwsIARUAAIhCGAFwAQ==&rs=AOn4CLBilBbDRvGZxa9XbFa4MHTPTUc-uA")
            Glide.with(mContext)
                    .load(id)
                    .placeholder(R.mipmap.prepjoy_icon_light)
                    .diskCacheStrategy(DiskCacheStrategy.DATA)
                    .error(R.mipmap.prepjoy_icon_light)
                    .centerCrop()
                    .into(holder.imgVideoCover)
        } else if (pReslink!= null && !pReslink.isEmpty() && pReslink.contains("jwplatform")) {
            val mediaId = JWPlayerMediaExtractor.jwPlayerMediaIdFromResLink(pReslink)
             id = "https://cdn.jwplayer.com/v2/media/$mediaId/poster.jpg?width=720"
            Glide.with(mContext)
                    .load(id)
                    .placeholder(R.mipmap.prepjoy_icon_light)
                    .diskCacheStrategy(DiskCacheStrategy.DATA)
                    .error(R.mipmap.prepjoy_icon_light)
                    .centerCrop()
                    .into(holder.imgVideoCover)
        }
    }
    inner class RecyclerViewHolder(@NonNull itemView: View) : RecyclerView.ViewHolder(itemView) {
        val txtTitle: TextView = itemView.findViewById(R.id.videolinktitle)
        val txtDescription : TextView = itemView.findViewById(R.id.videoDesciption)
        val imgVideoCover: ImageView = itemView.findViewById(R.id.videolinkthumbnail)
        val cardVideo : CardView = itemView.findViewById(R.id.videoCardLayout);
    }
    override fun getItemCount(): Int {
        return videoDataArrayList.size
    }


    fun extractYTId(videoUrl: String): String? {
        var videoId = ""
        val regex = "http(?:s)?:\\/\\/(?:m.)?(?:www\\.)?youtu(?:\\.be\\/|be\\.com\\/(?:watch\\?(?:feature=youtu.be\\&)?v=|v\\/|embed\\/|user\\/(?:[\\w#]+\\/)+))([^&#?\\n]+)"
        val pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE)
        val matcher = pattern.matcher(videoUrl)
        if (matcher.find()) {
            videoId = matcher.group(1)
        }
        return videoId
    }
}