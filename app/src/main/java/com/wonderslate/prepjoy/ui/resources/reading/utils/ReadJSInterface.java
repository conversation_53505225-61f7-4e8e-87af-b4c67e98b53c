package com.wonderslate.prepjoy.ui.resources.reading.utils;

import android.content.Context;
import android.util.Log;
import android.webkit.JavascriptInterface;
import android.webkit.WebView;

/**
 * Created by nakul on 3/26/2018.
 */

public class ReadJSInterface {
    WebView webview = null;
    private Context context;
    private static final String TAG = "ReadJSInterface";
    private ReadingCallbacks callbacks = null;

    public ReadJSInterface() {
    }

    public ReadJSInterface(WebView webview, ReadingCallbacks callbacks) {
        this.webview = webview;
        this.callbacks = callbacks;
    }

    public void connectInterface() {
        webview.addJavascriptInterface(this, "ReadJSInterface");
    }

    @JavascriptInterface
    public void annotationData(String annotationString, String action) {
        if(callbacks != null)
            callbacks.onAnnotationCall(annotationString, action);
    }

    @JavascriptInterface
    public void scrollEnd() {
        if(callbacks != null)
            callbacks.onScrolledBottom();
    }

    @JavascriptInterface
    public void onWebSearchTap() {
    }

    @JavascriptInterface
    public void onWebSearchTap(String query) {
        try {
            if(callbacks != null)
                callbacks.onWebSearchTap(query);
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
        }
    }

    @JavascriptInterface
    public void onAnnotationTap(boolean state) {
        if(callbacks != null)
            callbacks.onAnnotationTap(state);
    }

    @JavascriptInterface
    public void hideAppLoader() {

    }

    @JavascriptInterface
    public void scrollingUp(boolean value) {
        if (value) { //Scrolling up
            callbacks.onScrollUp();
        }
        else { //Scrolling down
            callbacks.onScrollDown();
        }
    }

    @JavascriptInterface
    public void storeLastReadPage(int value) {
        callbacks.savePDFPage(value);
    }

    @JavascriptInterface
    public void onQuoteAddToFlashcardTap(String value) {
        callbacks.onFlashcardTap(value);
    }

    @JavascriptInterface
    public void flashCardAddedEvent() {
        callbacks.onFlashCardAdded();
    }

    public interface ReadingCallbacks {
        void onAnnotationCall(String annotationString, String action);
        void onScrolledBottom();
        void onWebSearchTap(String query);
        void onAnnotationTap(boolean state);
        void onScrollUp();
        void onScrollDown();
        void savePDFPage(int value);
        void onFlashcardTap(String quote);
        void onFlashCardAdded();
    }
}
