package com.wonderslate.prepjoy.ui.dashboard

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.wonderslate.prepjoy.R
import com.ws.commons.extensions.clearAndAddAll
import com.ws.core_ui.extensions.*

class LevelSelectionAdapter(val latestValues: ArrayList<String> = arrayListOf(),private val listener: UserPrefrenceListener) : RecyclerView.Adapter<LevelSelectionAdapter.LevelHolder>() {


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LevelHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.level_selection_item, parent, false)
        return LevelHolder(view)
    }

    // binds the list items to a view
    override fun onBindViewHolder(holder: LevelHolder, position: Int) {
        val ItemsViewModel = latestValues[position]
        holder.textViewTitle.text = ItemsViewModel
       holder.relativeLayoutBg.setOnClickListener {
           holder.relativeLayoutBg.setBackgroundResource(R.drawable.round_border_white)
           holder.checkboxItem.visibility =View.VISIBLE
           listener.onUserPrefrenceSeleted(ItemsViewModel)
       }
       when (ItemsViewModel) {
            "College" -> holder.imgIcon.setImageResource(R.drawable.user_college)
            "Competitive Exams" -> holder.imgIcon.setImageResource(R.drawable.user_competitive)
            "Engineering Entrances" -> holder.imgIcon.setImageResource(R.drawable.user_engeneering)
            "Government Recruitments" ->holder.imgIcon.setImageResource(R.drawable.user_goverment)
           "Magazine" -> holder.imgIcon.setImageResource(R.drawable.user_magazine)
           "Medical Entrances" -> holder.imgIcon.setImageResource(R.drawable.user_medical)
           "School" -> holder.imgIcon.setImageResource(R.drawable.user_school)
            else -> holder.imgIcon.setImageResource(R.drawable.user_default_exam)
        }

    }
    override fun getItemCount(): Int {
        return latestValues.size
    }

    fun update(books: List<String>) {
        latestValues.clearAndAddAll(books)
        notifyDataSetChanged()
    }
    class LevelHolder(ItemView: View) : RecyclerView.ViewHolder(ItemView) {
        val relativeLayoutBg: RelativeLayout = itemView.findViewById(R.id.dashboard_current_affairs)
        val textViewTitle: TextView = itemView.findViewById(R.id.txtValues)
        val checkboxItem: ImageView = itemView.findViewById(R.id.chkBox)
        val imgIcon : ImageView = itemView.findViewById(R.id.imgIcon)

    }
}