package com.wonderslate.prepjoy.ui.profile

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.content.Intent
import android.database.Cursor
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.AsyncTask
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.text.Editable
import android.text.InputFilter
import android.text.Spanned
import android.text.TextWatcher
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.*
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.widget.AppCompatSpinner
import androidx.core.view.isVisible
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.bumptech.glide.signature.ObjectKey
import com.soundcloud.android.crop.CropImageActivity
import android.widget.ProgressBar
import com.wonderslate.commons.Data
import com.wonderslate.commons.Status
import com.wonderslate.data.network.WSAPIManager
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.domain.entities.UpdateUserProfileData
import com.wonderslate.prepjoy.BuildConfig
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.Utils.CustomViews.BadgesDialog
import com.wonderslate.prepjoy.Utils.CustomViews.MedalsDialog
import com.wonderslate.prepjoy.Utils.IndiaStatesData
import com.wonderslate.prepjoy.Utils.Utils
import com.wonderslate.prepjoy.Utils.Utils.disableScreenShot
import com.wonderslate.prepjoy.ui.BaseActivity
import com.wonderslate.prepjoy.ui.home.HomeBadge
import com.wonderslate.prepjoy.ui.home.HomeViewModel
import de.hdodenhof.circleimageview.CircleImageView
// import kotlinx.android.synthetic.main.fragment_profile.*
// import kotlinx.android.synthetic.main.header_language_change.*
import org.apache.http.HttpEntity
import org.apache.http.entity.ContentType
import org.apache.http.entity.mime.HttpMultipartMode
import org.apache.http.entity.mime.MultipartEntityBuilder
import org.apache.http.entity.mime.content.ByteArrayBody
import org.apache.http.entity.mime.content.StringBody
import org.json.JSONObject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.io.ByteArrayOutputStream
import java.io.File
import java.net.HttpURLConnection
import java.net.ProtocolException
import java.net.URL
import java.text.SimpleDateFormat
import java.util.*

class ProfileActivity : BaseActivity() {
    //private lateinit var rootView: View

    private lateinit var imageBadge: ImageView
    private lateinit var textBadge: TextView

    // private lateinit var textLifeTimePoints : TextView
    private lateinit var textLifeTimeMedals: TextView

    // private lateinit var imageUser : ImageView
    //  private lateinit var editName : EditText
    private lateinit var editCity: EditText
    private lateinit var editState: EditText
    private lateinit var lltPoints: LinearLayout
    private lateinit var lltMedals: LinearLayout
    private lateinit var edtName: EditText
    private lateinit var toggleLanguage: View
    private lateinit var rrltBack: View
    private lateinit var textLifeTimePoints: TextView


    private lateinit var txtvalidateName: TextView
    private lateinit var txtvalidateState: TextView
    private lateinit var txtvalidatecity: TextView
    private lateinit var wonderPubSharedPrefs: WonderPubSharedPrefs

    private var context: Context? = null

    private lateinit var spinnerstate: AppCompatSpinner
    private lateinit var spinnercity: AppCompatSpinner

    private lateinit var avLoadingIndicatorView: ProgressBar
    var statesData: IndiaStatesData? = null
    private var selectedState: String? = null
    private var selectedDistrict: String? = null
    private lateinit var btnupdateUser: Button
    private var userImageLayout: FrameLayout? = null
    private var userImageView: CircleImageView? = null
    private var modifiedDate: String = ""
    private var updateProfilePic = false
    // private val sharedPrefs: WonderPubSharedPrefs? = null

    private val viewModel: HomeViewModel by viewModel()


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        wonderPubSharedPrefs = WonderPubSharedPrefs.getInstance(this)
        try {
            if (supportActionBar != null) {
                supportActionBar!!.visibility = View.GONE
            }
            context = this
            intiViews()
            setProfile()
            edtName.setImeOptions(EditorInfo.IME_ACTION_NEXT);
        } catch (e: Exception) {
            e.printStackTrace()
        }
        viewModel.currentAffairsUserDetails.observe(this, ::currentAffairsUserDetails)
        disableScreenShot(this)
    }

    private fun currentAffairsUserDetails(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
            }
            Status.SUCCESSFUL -> {

                data.data?.let {
                    updateUserPoints(data.data!!)
                }
            }
            Status.HTTP_UNAVAILABLE -> {
            }

            Status.ERROR -> {
            }
        }
    }

    private fun updateUserPoints(jsonObject: JSONObject) {
        try {
            val s = jsonObject.getString("details")

            val arr = s.split(",").toTypedArray()

            if (s.contains("totalPoints")) {
                for (i in arr.indices) {
                    if (arr[i].contains("totalPoints")) {
                        WonderPubSharedPrefs.getInstance(context).totalPoints =
                                arr[i].split(":")[1]
                        break
                    }
                }
            }

            if (s.contains("currentBadge")) {
                for (i in arr.indices) {
                    if (arr[i].contains("currentBadge")) {
                        WonderPubSharedPrefs.getInstance(context).totalBades =
                                arr[i].split(":")[1]
                        break
                    }
                }
            }

            if (s.contains("totalMedals")) {
                for (i in arr.indices) {
                    if (arr[i].contains("totalMedals")) {
                        WonderPubSharedPrefs.getInstance(context).totalMedals =
                                arr[i].split(":")[1]
                        break
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        setProfile()
    }

    override fun onResume() {
        super.onResume()
        // setProfile()
        this.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        viewModel.getCurrentAffairsUserDetails(BuildConfig.SITE_ID)
    }

    private fun intiViews() {
        imageBadge = findViewById(R.id.imgPcommanderIcon)
        textBadge = findViewById(R.id.textBadge)
        // textLifeTimePoints = rootView.findViewById(R.id.textLifeTimePoints)
        textLifeTimeMedals = findViewById(R.id.textLifeTimeMedals)
        //  imageUser = rootView.findViewById(R.id.userImageView)
        //editName = rootView.findViewById(R.id.edtName)
        spinnerstate = findViewById(R.id.spinnerstate)
        spinnercity = findViewById(R.id.spinnercity)
        btnupdateUser = findViewById(R.id.btnupdateUser)
        avLoadingIndicatorView = findViewById(R.id.avlSignUp)
        val imageLayout: RelativeLayout = findViewById(R.id.userImageContainerLayout)
        userImageLayout = findViewById(R.id.userImageLayout)

        userImageView = findViewById(R.id.userImageView)
        txtvalidateName = findViewById(R.id.txtvalidateName)
        txtvalidateState = findViewById(R.id.txtvalidateState)
        txtvalidatecity = findViewById(R.id.txtvalidatecity)
        //sharedPrefs = WonderPubSharedPrefs.getInstance(mContext)


        lltPoints = findViewById(R.id.lltPoints)
        lltMedals = findViewById(R.id.lltMedals)
        edtName = findViewById(R.id.edtName)
        toggleLanguage = findViewById(R.id.toggleLanguage)
        rrltBack = findViewById(R.id.rrltBack)
        textLifeTimePoints = findViewById(R.id.textLifeTimePoints)


        statesData = IndiaStatesData(context)
        val states: MutableList<String?> = ArrayList()
        states.addAll(statesData!!.statesList)
        states.sortWith { obj: String?, str: String? ->
            obj!!.compareTo(
                    str!!,
                    ignoreCase = true
            )
        }
        states.add(0, "Select State")


        val stateSpinnerAdapter: ArrayAdapter<*> = ArrayAdapter<Any?>(
                context!!,
                R.layout.spinner_row_nothing_selected,
                states as List<Any?>
        )

        stateSpinnerAdapter.setDropDownViewResource(R.layout.spinner_textview)
        spinnerstate.adapter = stateSpinnerAdapter

        val districtList: MutableList<String?> = ArrayList()
        districtList.add(0, "Select District")


        val districtAdapter: ArrayAdapter<*> = ArrayAdapter<Any?>(context!!,
                R.layout.spinner_row_nothing_selected, districtList as List<Any?>)
        districtAdapter.setDropDownViewResource(R.layout.spinner_textview)
        spinnercity.adapter = districtAdapter
        spinnercity.isEnabled = false


        spinnerstate.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(
                    parent: AdapterView<*>?,
                    view: View?,
                    position: Int,
                    id: Long
            ) {
                try {
                    if (!states[position].equals(selectedState, ignoreCase = true)) {
                        selectedState = states[position]
                        if (spinnerstate.selectedItem.toString()
                                        .equals("Select State", ignoreCase = true)
                        ) {
                            districtList.clear()
                            districtList.add(0, "Select District")
                            selectedDistrict = "Select District"
                            districtAdapter.notifyDataSetChanged()
                            spinnercity.isEnabled = false
                        } else {
                            spinnercity.isEnabled = true
                            districtAdapter.clear()
                            districtList.addAll(statesData!!.getDistricts(selectedState))
                            districtAdapter.notifyDataSetChanged()
                            selectedDistrict = districtList[0]
                            spinnercity.setSelection(0)
                        }
                        hideKeyboard(this@ProfileActivity)
                    }
                    if (txtvalidateState.isVisible) {
                        txtvalidateState.visibility = View.GONE
                    }
                    if (wonderPubSharedPrefs.userDistrict != selectedDistrict) {
                        btnupdateUser.visibility = View.VISIBLE
                    } else {
                        if (!updateProfilePic) {
                            btnupdateUser.visibility = View.GONE
                        }                    }
                } catch (E: Exception) {
                    E.printStackTrace()
                }
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }

        spinnercity.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(
                    parent: AdapterView<*>?,
                    view: View?,
                    position: Int,
                    id: Long
            ) {
                try {
                    if (!districtList[position].equals(selectedDistrict, ignoreCase = true)) {
                        if (position < districtList.size) selectedDistrict = districtList[position]
                    }
                    if (wonderPubSharedPrefs.userDistrict != selectedDistrict) {
                        btnupdateUser.visibility = View.VISIBLE
                    } else {
                        if (!updateProfilePic) {
                            btnupdateUser.visibility = View.GONE
                        }                    }
                    hideKeyboard(this@ProfileActivity)
                    if (txtvalidatecity.isVisible) {
                        txtvalidatecity.visibility = View.GONE
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }

        edtName.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(arg0: Editable) {
                if (wonderPubSharedPrefs.username != arg0.toString()) {
                    btnupdateUser.visibility = View.VISIBLE
                } else {
                    if (!updateProfilePic) {
                        btnupdateUser.visibility = View.GONE
                    }
                }
            }

            override fun beforeTextChanged(
                    s: CharSequence,
                    start: Int,
                    count: Int,
                    after: Int
            ) {
            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                try {
                    if (txtvalidateName.isVisible) {
                        txtvalidateName.visibility = View.GONE
                    }

                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        })

        btnupdateUser.setOnClickListener {
            btnupdateUser.visibility = View.GONE
            startupdateProcess()
        }
        initObserver()
        edtName.setText(WonderPubSharedPrefs.getInstance(context).username)

        if (!WonderPubSharedPrefs.getInstance(context).getUserState().isEmpty()) {
            if (states.contains(WonderPubSharedPrefs.getInstance(context).getUserState())) {
                selectedState = WonderPubSharedPrefs.getInstance(context).getUserState()
                spinnerstate.setSelection(states.indexOf(selectedState))
                spinnercity.setEnabled(true)


                if (!WonderPubSharedPrefs.getInstance(context).getUserDistrict().isEmpty()) {
                    selectedDistrict = WonderPubSharedPrefs.getInstance(context).getUserDistrict()
                    districtAdapter.clear()
                    districtList.addAll(statesData!!.getDistricts(selectedState))
                    districtAdapter.notifyDataSetChanged()
                    spinnercity.setSelection(districtList.indexOf(selectedDistrict))
                }
            }
        }
        lltPoints.setOnClickListener {
            context?.let { it1 -> BadgesDialog().showDialog(it1) }
        }
        lltMedals.setOnClickListener {
            context?.let { it1 -> MedalsDialog().showDialog(it1) }
        }


        /* if ("true".equalsIgnoreCase(sharedPrefs.getUserLoginType())) {
            profilePhoneLayout.setVisibility(View.GONE);
            profileEmailLayout.setVisibility(View.VISIBLE);
            profileEmailLayout.setEnabled(false);
//            emailInputTxt.setText(sharedPrefs.getUseremail());
        } else {
            profilePhoneLayout.setVisibility(View.VISIBLE);
            profileEmailLayout.setVisibility(View.GONE);
            profilePhoneLayout.setEnabled(false);
//            userMobileInputTxt.setText(sharedPrefs.getUsermobile());
        }*/imageLayout.layoutParams.height = calculateDPI(180)
        imageLayout.layoutParams.width = calculateDPI(180)
        userImageView?.getLayoutParams()!!.height = calculateDPI(160)
        userImageView?.getLayoutParams()!!.width = calculateDPI(160)

        /*  Glide.with(this)
                  .load(WonderPubSharedPrefs.getInstance(mContext).getUserImage())
                  .placeholder(R.drawable.prepjoy_full_icon)
                  .signature(ObjectKey(System.currentTimeMillis()))
                  .into(userImageView!!)*/


        val url: String = WonderPubSharedPrefs.getInstance(context).userImage
        val check: Boolean = "null" in url
        if (check) {
            /* Glide.with(this)
                     .load(R.drawable.prepjoy_full_icon)
                     //.placeholder(R.drawable.prepjoy_full_icon)
                     //.signature(FObjectKey(System.currentTimeMillis()))
                     .into(userImageView!!)*/
            userImageView!!.setImageResource(R.drawable.prepjoy_full_icon);
            // userImageView!!.setImageDrawable(resources.getDrawable(R.drawable.prepjoy_full_icon));
            // JsonquizData.put("userImage", WonderPubSharedPrefs.getInstance(context).userImage )
        } else {
            Glide.with(this)
                    .load(WonderPubSharedPrefs.getInstance(context).getUserImage())
                    .diskCacheStrategy(DiskCacheStrategy.NONE)
                    .skipMemoryCache(true)
                    .dontAnimate()
                    .into(object: CustomTarget<Drawable>() {
                        override fun onResourceReady(resource: Drawable, transition: Transition<in Drawable>?) {
                            userImageView!!.setImageDrawable(resource)
                        }

                        override fun onLoadCleared(placeholder: Drawable?) {
                        }

                    })
        }


        userImageLayout!!.setOnClickListener(View.OnClickListener { v: View? ->
            val getIntent = Intent(Intent.ACTION_GET_CONTENT)
            getIntent.type = "image/*"
            val pickIntent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
            pickIntent.type = "image/*"
            val chooserIntent = Intent.createChooser(getIntent, "Select Image")
            chooserIntent.putExtra(Intent.EXTRA_INITIAL_INTENTS, arrayOf(pickIntent))
            cropImage.launch(chooserIntent)
        })

        toggleLanguage.visibility = View.GONE
        rrltBack.setOnClickListener {
            onBackPressed()
            /* val intent = Intent(context, DashBoardActivity::class.java)
             this.startActivity(intent)
             finish()*/
        }

        edtName.setFilters(arrayOf<InputFilter>(EmojiExcludeFilter()))
    }

    class EmojiExcludeFilter : InputFilter {
        override fun filter(source: CharSequence, start: Int, end: Int, dest: Spanned?, dstart: Int, dend: Int): CharSequence? {
            for (i in start until end) {
                val type = Character.getType(source[i])
                if (type == Character.SURROGATE.toInt() || type == Character.OTHER_SYMBOL.toInt()) {
                    return ""
                }
            }
            return null
        }
    }

    var cropImage = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        try {
            if (result.resultCode == RESULT_OK && result.data != null && result.data!!.data != null) {
                btnupdateUser.visibility = View.VISIBLE
                updateProfilePic = true
                val selectedImage = result.data!!.data
                val filePathColumn = arrayOf(MediaStore.Images.Media.DATA)
                val cursor: Cursor? = this.applicationContext?.contentResolver?.query(selectedImage!!, filePathColumn, null, null, null)
                if (cursor != null) {
                    cursor.moveToFirst()
                    val columnIndex = cursor.getColumnIndex(filePathColumn[0])
                    if (columnIndex > -1) {
                        if (null != cursor.getString(columnIndex)) {
                            picturePath = cursor.getString(columnIndex)
                        }
                        cursor.close()
                        val wrapper = ContextWrapper(context)
                        val directory = wrapper.getDir("profileImages", Context.MODE_PRIVATE)
                        outputFile = File(directory, "cropped.png")

                        val cropIntent = Intent(this, CropImageActivity::class.java)
                        cropIntent.data = selectedImage
                        cropIntent.putExtra("output", Uri.fromFile(outputFile))
                        cropIntent.putExtra("aspect_x", 1)
                        cropIntent.putExtra("aspect_y", 1)
                        updateProfilePhoto.launch(cropIntent)
                    }
                }
            }
        }catch (e: java.lang.Exception){
            e.printStackTrace()
        }

    }

    var updateProfilePhoto = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        try {
            if (result.resultCode == RESULT_OK) {
                UploadProfilePic(context!!, avLoadingIndicatorView, picturePath!!, userImageView, outputFile).execute()
            }
        }catch (e: java.lang.Exception){
            e.printStackTrace()
        }

    }

    var picturePath = ""
    private var outputFile: File? = null

    override fun getLayoutResource(): Int {
        return R.layout.fragment_profile
    }

    class UploadProfilePic(context: Context, avLoadingIndicatorView: ProgressBar,
                           picturePath: String, var userImageView: CircleImageView? = null, private var outputFile: File? = null) :
            AsyncTask<String, Void, Boolean>() {

        val mpcon: Context? = context

        val mavLoadingIndicatorView: ProgressBar? = avLoadingIndicatorView
        val mpicturePath: String? = picturePath

        override fun onPreExecute() {
            super.onPreExecute()
            if (mavLoadingIndicatorView != null) {
                mavLoadingIndicatorView.visibility = View.VISIBLE
            }
        }

        override fun doInBackground(vararg params: String?): Boolean? {

            var result = java.lang.Boolean.FALSE
            try {
                val boundary = "*****"
                val url = URL(WSAPIManager.SERVICE3.toString() + "api/uploadProfileImage")
                val conn = url.openConnection() as HttpURLConnection
                conn.requestMethod = "POST"
                conn.setRequestProperty("Connection", "Keep-Alive")
                conn.setRequestProperty("ENCTYPE", "multipart/form-data")
                conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=$boundary")
                conn.setRequestProperty("X-Auth-Token", WonderPubSharedPrefs.getInstance(mpcon).getAccessToken())
                conn.setRequestProperty("Accept", "application/json")
                conn.setRequestProperty("Accept-Charset", "UTF-8")
                conn.readTimeout = 30000
                conn.connectTimeout = 16000
                val entityBuilder: MultipartEntityBuilder = MultipartEntityBuilder.create()
                entityBuilder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE)
                val bos = ByteArrayOutputStream()
                val bitmap = BitmapFactory.decodeFile(outputFile!!.getPath())
                bitmap.compress(Bitmap.CompressFormat.JPEG, 70, bos)
                val data = bos.toByteArray()
                val bab = ByteArrayBody(data, "profileImage.jpg")
                entityBuilder.addPart("file", bab)
                entityBuilder.addPart("type", StringBody("user", ContentType.TEXT_PLAIN))
                val entity: HttpEntity = entityBuilder.build()
                conn.addRequestProperty("Content-length", entity.contentLength.toString() + "")
                conn.addRequestProperty(entity.contentType.name, entity.contentType.value)
                val os = conn.outputStream
                entity.writeTo(conn.outputStream)
                os.close()
                conn.connect()
                result = if (conn.responseCode == HttpURLConnection.HTTP_OK || conn.responseCode == HttpURLConnection.HTTP_MOVED_TEMP) {
                    val imageUrl: String = (WSAPIManager.SERVICE3.toString() + "funlearn/showProfileImage?id=" + WonderPubSharedPrefs.getInstance(mpcon).getUserId()
                            + "&fileName=profileImage.jpg&type=user&imgType=passport")
                    WonderPubSharedPrefs.getInstance(mpcon).setUserImage(imageUrl)
                    java.lang.Boolean.TRUE
                } else {
                    java.lang.Boolean.FALSE
                }
            } catch (e: Exception) {
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP && e is ProtocolException) result = java.lang.Boolean.TRUE

                return result
            }

            return result
        }


        override fun onPostExecute(uploaded: Boolean?) {
            super.onPostExecute(uploaded)


            mavLoadingIndicatorView!!.visibility = View.GONE
            if (uploaded == null || !uploaded) {
                Utils.showTopSnackBar("Could'nt upload profile image! Please try after some time.", mpcon as Activity?)
                // Toast.makeText(this@EditUserProfileActivity, "Could'nt upload profile image! Please try after some time.", Toast.LENGTH_SHORT).show()
            } else {
                Glide.with(mpcon!!)
                        .load(WonderPubSharedPrefs.getInstance(mpcon).getUserImage())
                        .placeholder(R.drawable.prepjoy_full_icon)
                        .signature(ObjectKey(System.currentTimeMillis()))
                        .into(userImageView!!)
                Utils.showTopSnackBar("Updated Profile pic successfully.", mpcon as Activity?)
            }

        }
    }


    fun hideKeyboard(activity: Activity) {
        val imm = activity.getSystemService(BaseActivity.INPUT_METHOD_SERVICE) as InputMethodManager
        //Find the currently focused view, so we can grab the correct window token from it.
        var view = activity.currentFocus
        //If no view currently has focus, create a new one, just so we can grab a window token from it
        if (view == null) {
            view = View(activity)
        }
        imm.hideSoftInputFromWindow(view.windowToken, 0)
    }

    private fun startupdateProcess() {
        try {
            if (validateFields()) {
                avLoadingIndicatorView.visibility = View.VISIBLE
                val date = Date()
                modifiedDate = SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH).format(date)
                viewModel.updateUserData(
                        UpdateUserProfileData(
                                edtName.text.toString(), WonderPubSharedPrefs.getInstance(context).usermobile,
                                selectedState!!, selectedDistrict!!, BuildConfig.SITE_ID
                        )
                )
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun initObserver() {
        viewModel.userDetailsResponse.observe(this, ::updateResponse)
    }

    private fun updateResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {

            }

            Status.SUCCESSFUL -> {
                try {
                    avLoadingIndicatorView.visibility = View.GONE
                    avLoadingIndicatorView.visibility = View.GONE
                    Utils.showTopSnackBar("Updated Successfully.", this@ProfileActivity)
                    btnupdateUser.setBackgroundResource(R.drawable.button_shape_default)
                    WonderPubSharedPrefs.getInstance(this).userDistrict =
                            selectedDistrict
                    WonderPubSharedPrefs.getInstance(this).username =
                            edtName.text.toString()
                    WonderPubSharedPrefs.getInstance(this).userState = selectedState
                } catch (e: Exception) {

                }
            }

            Status.HTTP_UNAVAILABLE -> {
                try {
                    Utils.showTopSnackBar(
                            resources.getString(R.string.server_under_maintenance),
                            this@ProfileActivity
                    )
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

            Status.ERROR -> {
                try {
                    avLoadingIndicatorView.visibility = View.GONE
                    Utils.showTopSnackBar(
                            resources.getString(R.string.some_thing_went_wrong),
                            this@ProfileActivity
                    )
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

    private fun validateFields(): Boolean {
        val name = edtName.text.toString()
        when {
            name.isEmpty() -> {
                txtvalidateName.visibility = View.VISIBLE
            }
            selectedState.equals(getString(R.string.select_state), ignoreCase = true) -> {
                txtvalidateState.visibility = View.VISIBLE
            }
            selectedDistrict.equals(getString(R.string.select_district), ignoreCase = true) -> {
                txtvalidatecity.visibility = View.VISIBLE
            }
            else -> {
                txtvalidateName.visibility = View.GONE
                txtvalidateState.visibility = View.GONE
                txtvalidatecity.visibility = View.GONE
                return true
            }
        }
        return false
    }

    private fun setProfile() {
        try {
            /*  Picasso.get().load(WonderPubSharedPrefs.getInstance(context).userImage)
                  .into(imageUser)*/

            edtName.setText(WonderPubSharedPrefs.getInstance(context).username)
            //  editCity.setText(WonderPubSharedPrefs.getInstance(context).userDistrict)
            // editState.setText(WonderPubSharedPrefs.getInstance(context).userState)


            textLifeTimePoints.text = WonderPubSharedPrefs.getInstance(context).totalPoints
            textLifeTimeMedals.text = WonderPubSharedPrefs.getInstance(context).totalMedals
            var home = HomeBadge(context, imageBadge, textBadge, WonderPubSharedPrefs.getInstance(context).totalBades)
            home.getImageIcon(context, imageBadge, textBadge, WonderPubSharedPrefs.getInstance(context).totalBades)
            // editName.setText("Satish")
            // editCity.setText("Jaipur")
            // editState.setText("KA")
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /*@Override
    public void handleStatus(String action, Integer status) {

    }

    @Override
    public void handleStatusIntent(Intent intent) {
        if (intent.getAction().equals(WonderComponentMessagingInterface.BROADCAST_ACTION_SERVICE_UPDATE_USER_PROFILE)) {
            mainLoader.visibility = View.GONE;
            int statusCode = intent.getIntExtra(WonderComponentMessagingInterface.BROADCAST_RESULT_STATUS,
                    WonderComponentMessagingInterface.RESULT_STATUS_SUCCESS);
            if (!Utils.checkResultSuccess(statusCode)){
                Utils.showErrorToast(this,statusCode);
                return;
            }
                sharedPrefs.setUsermobile(mobileStr);
                sharedPrefs.setUserEmail(emailStr);
                sharedPrefs.setUsername(userNameStr);

                Toast.makeText(this, "Updated Successfully.", Toast.LENGTH_SHORT).show();
                finish();

        }
    }*/
    private fun calculateDPI(calculateSizeInt: Int): Int {
        val metrics = resources.displayMetrics
        if (metrics.densityDpi >= 120 && metrics.densityDpi <= 200) {
            return calculateSizeInt
        } else if (metrics.densityDpi >= 201 && metrics.densityDpi <= 280) {
            val size = calculateSizeInt * 1.5
            return size.toInt()
        } else if (metrics.densityDpi >= 281 && metrics.densityDpi <= 400) {
            return calculateSizeInt * 2
        } else if (metrics.densityDpi >= 401 && metrics.densityDpi <= 540) {
            return calculateSizeInt * 3
        } else if (metrics.densityDpi >= 541) {
            return calculateSizeInt * 4
        }
        return calculateSizeInt
    }

    override fun onBackPressed() {
        super.onBackPressed()
    }
}