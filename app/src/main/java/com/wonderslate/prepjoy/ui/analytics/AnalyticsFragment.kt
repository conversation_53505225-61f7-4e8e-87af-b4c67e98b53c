package com.wonderslate.prepjoy.ui.analytics

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.core.widget.NestedScrollView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import butterknife.ButterKnife
import com.facebook.shimmer.ShimmerFrameLayout
import com.github.mikephil.charting.animation.Easing
import com.github.mikephil.charting.charts.PieChart
import com.github.mikephil.charting.components.Legend
import com.github.mikephil.charting.data.PieData
import com.github.mikephil.charting.data.PieDataSet
import com.github.mikephil.charting.data.PieEntry
import com.github.mikephil.charting.formatter.PercentFormatter
import android.widget.ProgressBar
import com.wonderslate.commons.Data
import com.wonderslate.commons.Status
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.domain.entities.QuizFullDetails
import com.wonderslate.domain.entities.UserHistoryforAllQuiz
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.Utils.FirebaseAnalyticsUtils
import com.wonderslate.prepjoy.Utils.InternetConnectionChecker
import com.wonderslate.prepjoy.Utils.Utils
import com.wonderslate.prepjoy.ui.home.HomeViewModel
import com.wonderslate.prepjoy.ui.home.QuizOptionsListener
import com.wonderslate.prepjoy.ui.login.LoginActivity
import com.wonderslate.prepjoy.ui.quiz.QuizActivity
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import java.text.DecimalFormat
import com.github.mikephil.charting.utils.MPPointF





class AnalyticsFragment : Fragment(), QuizOptionsListener {

    lateinit var recyclerViewGroupsDetails: RecyclerView
    lateinit var recyclerViewPages: RecyclerView
    lateinit var swipeRefreshLayout: SwipeRefreshLayout
    lateinit var progressBar: ProgressBar
    lateinit var nestedScrollViewPost: NestedScrollView
    lateinit var groupLoaderDetails: ProgressBar

    private var isPaginationOn = false
    private var myContext: FragmentActivity? = null
    lateinit var rootview: View
    private var mContext: Context? = null
    private val viewModel: HomeViewModel by sharedViewModel()
    private var analyticsUtils: FirebaseAnalyticsUtils? = null
    private var internetConnectionChecker: InternetConnectionChecker? = null
    private var loadPageNo = 0
    private var allowPagination = false
    val postDataList: ArrayList<AnalyticsDataModel> = arrayListOf()
    var pagesList: ArrayList<Int> = arrayListOf()
    var wonList: ArrayList<String> = arrayListOf()
    var loseList: ArrayList<String> = arrayListOf()
    var tieList: ArrayList<String> = arrayListOf()
    private var isLoadMoreBook = true
    private var isNexPostListLoaded = false
    private var totalQuizAttempted = 0
    private lateinit var txtQuiztitle: TextView
    private lateinit var txtScore: TextView
    private lateinit var txtQuizStatus: TextView
    private lateinit var avShimmer : ShimmerFrameLayout
    private lateinit var  lltLastQuiz : LinearLayout
    private lateinit var  layoutPagenation : RelativeLayout
    private lateinit var  pieChart : PieChart
    var adapter: AnalyticsAdapter? = null
    var pieDataSet: PieDataSet? = null
    private var tabPos :Int = 0
    lateinit var noDataLayout :LinearLayout
    lateinit var lltTitle : LinearLayout
    private val wonderPubSharedPrefs = WonderPubSharedPrefs.getInstance(context)
    private var selectedQuizName: String = ""
    override fun onAttach(activity: Activity) {
        myContext = activity as FragmentActivity
        super.onAttach(activity)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        rootview = inflater.inflate(R.layout.fragment_analytics, container, false)
        ButterKnife.bind(this, rootview)
        mContext = context
        analyticsUtils = FirebaseAnalyticsUtils(mContext!!)
        initObserver()
        init()
        return rootview
    }
    private fun init() {
        try {
            pieChart = rootview.findViewById(R.id.paichart)
            avShimmer = rootview.findViewById(R.id.item_home_options_new_shimmer)
           // avShimmer.startShimmerAnimation()
            lltLastQuiz =  rootview.findViewById(R.id.lltLastQuiz)
            txtQuiztitle = rootview.findViewById(R.id.txtQuiztitle)
            txtScore = rootview.findViewById(R.id.txtScore)
            txtQuizStatus = rootview.findViewById(R.id.txtQuizStatus)
            layoutPagenation = rootview.findViewById(R.id.layout_page)
            recyclerViewGroupsDetails = rootview.findViewById(R.id.recyclerView_groups_details)
            recyclerViewPages = rootview.findViewById(R.id.recycler_view_pages)
            swipeRefreshLayout = rootview.findViewById(R.id.swipeToRefresh)
            progressBar = rootview.findViewById(R.id.idPBLoading)
            nestedScrollViewPost = rootview.findViewById(R.id.nested_seroll_view_post)
            groupLoaderDetails = rootview.findViewById(R.id.groupLoader_details)
            groupLoaderDetails.visibility = View.VISIBLE
            analyticsUtils?.logEvent("Analytics Home", "Analytics")
            internetConnectionChecker = InternetConnectionChecker()
            recyclerViewGroupsDetails.layoutManager = LinearLayoutManager(mContext)
            recyclerViewGroupsDetails.setHasFixedSize(false)
            val manager: RecyclerView.LayoutManager = LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false)
            recyclerViewPages.layoutManager = manager
            swipeRefreshLayout.setOnRefreshListener {
                loadPageNo = 0
                allowPagination = false
                pagesList.clear()
                isLoadMoreBook = true
                if (allowPagination) {
                    loadPageNo += 20
                }
                pieChart.animate()
                    .alpha(1f)
                    .setDuration(500)
                    .setListener(object : AnimatorListenerAdapter() {
                        override fun onAnimationEnd(animation: Animator) {
                            super.onAnimationEnd(animation)
                            pieChart.visibility = View.VISIBLE
                        }
                    })
                viewModel.getHistoryForAllQuiz(UserHistoryforAllQuiz(loadPageNo.toString()))
            }


            nestedScrollViewPost.setOnScrollChangeListener(NestedScrollView.OnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY ->
                try {
                    if (scrollY == v.getChildAt(0).measuredHeight - v.measuredHeight) {
                        if (isLoadMoreBook) {
                            if (isNexPostListLoaded) {
                                isNexPostListLoaded = false
                                allowPagination = !allowPagination
                                progressBar.setVisibility(View.VISIBLE)
                                if (allowPagination) {
                                    loadPageNo+= 20
                                }
                                Log.e("xdrcfvgbh",":"+totalQuizAttempted +":"+postDataList.size + ":"+loadPageNo)

                                if(loadPageNo < totalQuizAttempted)
                                    viewModel.getHistoryForAllQuiz(UserHistoryforAllQuiz(loadPageNo.toString()))
                                else {
                                    Toast.makeText(
                                        activity,
                                        "No more history available.",
                                        Toast.LENGTH_SHORT
                                    ).show()
                                    progressBar.visibility = View.GONE
                                }
                            }
                        } else {
                            Toast.makeText(activity, "No more history available.", Toast.LENGTH_SHORT).show()
                            progressBar.visibility = View.GONE
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            })
            noDataLayout = rootview.findViewById(R.id.noDataLayout)
            lltTitle =  rootview.findViewById(R.id.lltTitle)
            viewModel.getQuizAttempted()
            viewModel.getHistoryForAllQuiz(UserHistoryforAllQuiz(loadPageNo.toString()))

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onResume() {
        super.onResume()
        try {
            groupLoaderDetails.visibility = View.VISIBLE
            viewModel.getQuizAttempted()
            viewModel.getHistoryForAllQuiz(UserHistoryforAllQuiz(loadPageNo.toString()))
        }
        catch (E:Exception)
        {
            E.printStackTrace()
        }
    }
    //region Observers
    private fun initObserver() {
        viewModel.lastQuizDetails.observe(viewLifecycleOwner, ::lastQuizDetails)
        viewModel.quizAttemptedDetails.observe(viewLifecycleOwner, ::quizAttemptedDetails)
        viewModel.historyForAllQuizDetails.observe(viewLifecycleOwner, ::historyForAllQuizDetails)
        viewModel.getAllFullQuizDetails.observe(viewLifecycleOwner, ::getAllDetailsForQuiz)
        viewModel.currentFragmentPosition.observe(viewLifecycleOwner, ::currentFragmentPosition)
    }
    private fun lastQuizDetails(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
            }
            Status.SUCCESSFUL -> {
                data.data?.let {
                    Log.e("Data",":"+data.data)
                    avShimmer.visibility = View.GONE
                    lltLastQuiz.visibility = View.GONE
                    val res =  it.getString("lastQuizDetails")
                    val jsonArray1: JSONArray = JSONArray(res)
                    val jsonObject = jsonArray1.getJSONObject(0)
                    txtQuiztitle.text = jsonObject.getString("quizName")
                    txtScore.text = jsonObject.getString("points")
                    if(jsonObject.has("matchStatus")){
                        txtQuizStatus.text = jsonObject.getString("matchStatus")
                    }
                    else
                    {
                        txtQuizStatus.text = "--"
                    }
                }
            }
            Status.HTTP_UNAVAILABLE -> {
            }
            Status.ERROR -> {
            }
        }
    }
    private fun quizAttemptedDetails(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
            }
            Status.SUCCESSFUL -> {
                data.data?.let {
                    Log.e("Data",":"+data.data)
                    totalQuizAttempted =  it.getString("totalQuizzesAttempted").toInt()
                }
            }
            Status.HTTP_UNAVAILABLE -> {
            }
            Status.ERROR -> {
            }
        }
    }
    private fun historyForAllQuizDetails(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
            }
            Status.SUCCESSFUL -> {
                data.data?.let {
                    Log.e("Data",":"+data.data)
                    getQuizHistoryDetails(it)
                }
            }
            Status.HTTP_UNAVAILABLE -> {
                recyclerViewGroupsDetails.visibility = View.GONE
                recyclerViewPages.visibility = View.GONE
                progressBar.visibility = View.GONE
                swipeRefreshLayout.isRefreshing = false
                groupLoaderDetails.visibility = View.GONE
                noDataLayout.visibility = View.VISIBLE
                lltTitle.visibility = View.GONE
                groupLoaderDetails.visibility = View.GONE
                pieChart.visibility = View.GONE
            }
            Status.ERROR -> {
                recyclerViewGroupsDetails.visibility = View.GONE
                recyclerViewPages.visibility = View.GONE
                progressBar.visibility = View.GONE
                swipeRefreshLayout.isRefreshing = false
                groupLoaderDetails.visibility = View.GONE
                noDataLayout.visibility = View.VISIBLE
                groupLoaderDetails.visibility = View.GONE
                lltTitle.visibility = View.GONE
                pieChart.visibility = View.GONE
            }
        }
    }
    private fun navigateToQuizStats() {
        val quizIntent = Intent(context, QuizActivity::class.java).also {
            it.putExtra("stats", true)
            it.putExtra("quizName", selectedQuizName)
        }
        startActivity(quizIntent)
    }

    private fun getAllDetailsForQuiz(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
            }
            Status.SUCCESSFUL -> {
                data.data?.let {
                    try {
                       // avShimmer.visibility = View.GONE
                        if(tabPos ==2) {
                            groupLoaderDetails.visibility = View.GONE
                            wonderPubSharedPrefs.userLastQuizResults = data.data.toString()
                            navigateToQuizStats()
                        }

                    } catch (e: JSONException) {
                        e.printStackTrace()
                    }
                }
            }
            Status.HTTP_UNAVAILABLE -> {

                try {
                    groupLoaderDetails.visibility = View.GONE
                } catch (e: Exception) {
                    groupLoaderDetails.visibility = View.GONE
                    Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), myContext)
                    e.printStackTrace()
                }
            }

            Status.ERROR -> {
                try {
                    groupLoaderDetails.visibility = View.GONE
                    if (null != data && data.error.toString().contains("401")) {
                        WonderPubSharedPrefs.getInstance(myContext).clearAllSharePref()
                        val intent = Intent(myContext, LoginActivity::class.java)
                        startActivity(intent)
                    } else {
                        Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), myContext)
                    }
                } catch (e: Exception) {
                    groupLoaderDetails.visibility = View.GONE
                    e.printStackTrace()
                }
            }
        }
    }
    private fun currentFragmentPosition(int: Int) {
        tabPos = int

    }

    private fun getQuizHistoryDetails(jsonObject: JSONObject)
    {
        try
        {
            wonList.clear()
            loseList.clear()
            tieList.clear()
            postDataList.clear()
            swipeRefreshLayout.isRefreshing = false
            groupLoaderDetails.visibility = View.GONE
            lltTitle.visibility = View.VISIBLE
            noDataLayout.visibility = View.GONE
            val result =  jsonObject.getString("totalQuizzesAttempted")
            if (result.equals("no records")) {
                swipeRefreshLayout.isRefreshing = false
                groupLoaderDetails.visibility = View.GONE
                noDataLayout.visibility = View.VISIBLE
                groupLoaderDetails.visibility = View.GONE
                lltTitle.visibility = View.GONE
                pieChart.visibility = View.GONE
            }
            else
            {
                val res =  jsonObject.getString("quizHistory")
                val jsonArray = JSONArray(res)
                for (i in 0 until jsonArray.length()) {
                    val jsonObj = jsonArray.getJSONObject(i)
                    val datalist = AnalyticsDataModel()
                    datalist.setquizName(jsonObj.getString("quizName"))
                    if(jsonObj.getString("matchStatus") == "null") {
                        // datalist.setmatchStatus("--")
                        if(jsonObj.has("quizType")) {
                            if (jsonObj.getString("quizType").equals("practice")) {
                                datalist.setmatchStatus("Practice")
                            } else if (jsonObj.getString("quizType").contains("test")) {
                                datalist.setmatchStatus("Test")
                            }
                            else
                            {
                                datalist.setmatchStatus("--")
                            }
                        }
                        else
                            datalist.setmatchStatus("--")
                    }
                    else {
                        if (jsonObj.getString("matchStatus").equals("win"))
                            wonList.add(jsonObj.getString("matchStatus"))
                        else if (jsonObj.getString("matchStatus").equals("Lose"))
                            loseList.add(jsonObj.getString("matchStatus"))
                        else if (jsonObj.getString("matchStatus").equals("Draw"))
                            tieList.add(jsonObj.getString("matchStatus"))
                        if(jsonObj.getString("matchStatus").equals("win"))
                            datalist.setmatchStatus("Won")
                        else
                            datalist.setmatchStatus(jsonObj.getString("matchStatus"))
                    }

                    if(jsonObj.has("points"))
                        datalist.setpoints(jsonObj.getString("points").toInt())
                    else
                        datalist.setpoints(0)
                    if (jsonObj.has("quizRecId") && !jsonObj.isNull("quizRecId")) {
                        datalist.setquizRecId(jsonObj.getString("quizRecId").toInt())
                        if (datalist.getmatchStatus() != "undefined") {
                            postDataList.add(datalist)
                        }
                    }
                    if(wonList.size>0 || loseList.size>0||tieList.size>0)
                        pieChart.visibility = View.VISIBLE
                }
                isNexPostListLoaded = true
                adapter = AnalyticsAdapter(mContext!!,this)
                recyclerViewGroupsDetails.adapter = adapter
                recyclerViewGroupsDetails.visibility = View.VISIBLE
                adapter!!.updateData(postDataList)

                //for pagination view
                if (!pagesList.contains(loadPageNo)) {
                    pagesList.add(loadPageNo)
                }
                if (pagesList.size > 1) {
                    layoutPagenation.visibility = View.VISIBLE
                    isPaginationOn = true
                    setPostPageToAdapter(pagesList, loadPageNo)
                } else {
                    layoutPagenation.visibility = View.GONE
                    isPaginationOn = false
                }
                initPieChart()
                setDataToPieChart()
            }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }
    }
    private fun initPieChart() {
        pieChart.setUsePercentValues(true)
        pieChart.description.text = ""
        //hollow pie chart
        pieChart.isDrawHoleEnabled = false
        pieChart.setTouchEnabled(false)
        pieChart.setDrawEntryLabels(false)
        //adding padding
        pieChart.setExtraOffsets(20f, 0f, 20f, 20f)
        pieChart.setUsePercentValues(true)
        pieChart.isRotationEnabled = true
        pieChart.setDrawEntryLabels(false)
        pieChart.legend.orientation = Legend.LegendOrientation.VERTICAL
        pieChart.legend.isWordWrapEnabled = true
    }

    private fun setDataToPieChart() {
        var dataUnavailable = true
        val dataEntries = ArrayList<PieEntry>()
        val colors: ArrayList<Int> = ArrayList()
        if (wonList.isNotEmpty()) {
            dataEntries.add(PieEntry(wonList.size.toFloat(), "Won"))
            colors.add(Color.parseColor("#59981A"))
            dataUnavailable = false
        }
        if (loseList.isNotEmpty()) {
            dataEntries.add(PieEntry(loseList.size.toFloat(), "Lose"))
            colors.add(Color.parseColor("#E83500"))
            dataUnavailable = false
        }
        if (tieList.isNotEmpty()) {
            dataEntries.add(PieEntry(tieList.size.toFloat(),"Draw"))
            colors.add(Color.parseColor("#F79420"))
            dataUnavailable = false
        }
        val dataSet = PieDataSet(dataEntries, "")
        dataSet.setDrawIcons(false)
        dataSet.sliceSpace = 3f
        dataSet.iconsOffset = MPPointF(0F, 40F)
        dataSet.selectionShift = 5f
        val data = PieData(dataSet)
        data.setValueTextSize(11f)
        data.setValueTextColor(Color.WHITE)
        val format = DecimalFormat("###,###,##0")
        data.setValueFormatter(PercentFormatter(format))
        dataSet.colors = colors
        pieChart.data = data
        pieChart.setExtraOffsets(5f, 10f, 5f, 5f)
        pieChart.animateY(1400, Easing.EaseInOutQuad)
        pieChart.holeRadius = 58f
        pieChart.transparentCircleRadius = 61f
        pieChart.isDrawHoleEnabled = true
        pieChart.setHoleColor(Color.WHITE)
        pieChart.setUsePercentValues(true)
        pieChart.setEntryLabelTextSize(11f)
        pieChart.setDrawCenterText(true)
        val desc = if (dataUnavailable) {
            "No quiz played here"
        } else {
            "Overview"
        }
        pieChart.centerText = desc
        pieChart.legend.isEnabled = true
        pieChart.legend.textColor =Color.WHITE
        // pieChart.legend.position = Legend.LegendPosition.RIGHT_OF_CHART
        pieChart.invalidate()
        groupLoaderDetails.visibility = View.GONE
        progressBar.visibility = View.GONE

    }

    private fun setPostPageToAdapter(postPage: List<Int>, currentPosition: Int) {
        try {
            val postPaginationAdapter = AnalyticsPaginationAdapter(this@AnalyticsFragment,this.requireActivity())
            postPaginationAdapter.setEntries(postPage, currentPosition)
            recyclerViewPages.adapter = postPaginationAdapter
            recyclerViewPages.visibility = View.VISIBLE
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }
    fun paginationClicked(pageNo: Int) {
        try {
            groupLoaderDetails.visibility = View.VISIBLE
            loadPageNo = pageNo
            isLoadMoreBook = true
            allowPagination = false
            viewModel.getHistoryForAllQuiz(UserHistoryforAllQuiz(loadPageNo.toString()))
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    override fun onOptionSelected(option: String, quizName: String) {

        groupLoaderDetails.visibility = View.VISIBLE
        // viewModel.getQuizQuestionsAnswers(lastquizId)
        selectedQuizName = quizName
        viewModel.getFullQuizDetails(QuizFullDetails(option))
    }

}