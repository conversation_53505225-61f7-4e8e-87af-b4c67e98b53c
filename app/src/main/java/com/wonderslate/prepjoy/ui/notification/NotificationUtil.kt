package com.wonderslate.prepjoy.ui.notification

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.Build
import android.text.format.DateUtils
import androidx.core.app.NotificationCompat
import com.google.firebase.messaging.FirebaseMessagingService
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.ui.dashboard.DashBoardActivity
import java.io.IOException
import java.net.URL
import java.text.Format
import java.text.SimpleDateFormat
import java.util.*

object NotificationUtil {

    private const val NOTIFICATION_INTENT_REQUEST_CODE = 258

    /**
     * Builds an instance of Notification.Builder with default icon
     */
    fun buildNotification(context: Context, title: String, body: String): NotificationCompat.Builder {
        val notificationManager = context.getSystemService(
            FirebaseMessagingService.NOTIFICATION_SERVICE) as NotificationManager
        val notificationBuilder: NotificationCompat.Builder =
            NotificationCompat.Builder(context, context.packageName)
            .setSmallIcon(R.mipmap.prepjoy_icon_light)
            .setColor(context.resources.getColor(R.color.white))
            .setColorized(true)
            .setContentTitle(title)
            .setContentText(body)
            .setPriority(Notification.PRIORITY_MAX)
            .setAutoCancel(true)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channelId = "YOUR_CHANNEL_ID"
            val channel = NotificationChannel(channelId,
                "Channel human readable title",
                NotificationManager.IMPORTANCE_DEFAULT)
            notificationManager.createNotificationChannel(channel)
            notificationBuilder.setChannelId(channelId)
        }
        return notificationBuilder
    }

    fun playQuizRequest(context: Context,
                        accessCode: String,
                        challenger: String? = null): NotificationCompat.Builder {
        val title = if (null == challenger) {
            "Invite to play"
        } else {
            "$challenger has invited to play"
        }
        val body = "Play quiz with your friend online!"
        val acceptIntent = Intent(context, DashBoardActivity::class.java).apply {
            action = "online_game_play"
            putExtra("accessCode", accessCode)
            putExtra("accept", true)
        }
        val builder = buildNotification(context, title, body)
//        val acceptPendingBroadcast = PendingIntent.getBroadcast(context, 0, acceptIntent, FLAG_IMMUTABLE)
//        val declinePendingBroadcast = PendingIntent.getBroadcast(context, 0, declineIntent, FLAG_IMMUTABLE)
//        builder.addAction(R.drawable.ic_done_all, "ACCEPT", acceptPendingBroadcast)
//        builder.addAction(R.drawable.crop__ic_cancel, "DECLINE", declinePendingBroadcast)
        return builder
    }

    fun buildBigPictureNotification(context: Context, title: String, body: String, imgUrl: String): NotificationCompat.Builder {
        val image: Bitmap? = try {
            val url = URL(imgUrl)
            BitmapFactory.decodeStream(url.openConnection().getInputStream())
        } catch (e: IOException) {
            null
        }

        val notificationManager = context.getSystemService(
            FirebaseMessagingService.NOTIFICATION_SERVICE) as NotificationManager
        val notificationBuilder: NotificationCompat.Builder =
            NotificationCompat.Builder(context, context.packageName)
                .setSmallIcon(R.mipmap.prepjoy_icon_light)
                .setColor(context.resources.getColor(R.color.white))
                .setColorized(true)
                .setContentTitle(title)
                .setContentText(body)
                .setPriority(Notification.PRIORITY_MAX)
                .setAutoCancel(true)
                .setLargeIcon(image)
                .setStyle(NotificationCompat.BigPictureStyle()
                    .bigPicture(image)
                    .bigLargeIcon(null as Bitmap?))
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channelId = "YOUR_CHANNEL_ID"
            val channel = NotificationChannel(channelId,
                "Channel human readable title",
                NotificationManager.IMPORTANCE_DEFAULT)
            notificationManager.createNotificationChannel(channel)
            notificationBuilder.setChannelId(channelId)

        }
        return notificationBuilder
    }
}