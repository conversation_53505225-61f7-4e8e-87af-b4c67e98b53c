package com.wonderslate.prepjoy.ui.videos;

import android.app.Notification;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.IBinder;
import android.support.v4.media.MediaMetadataCompat;
import android.support.v4.media.session.MediaSessionCompat;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;
import androidx.media3.common.AudioAttributes;
import androidx.media3.common.C;
import androidx.media3.common.MediaItem;
import androidx.media3.common.Player;
import androidx.media3.common.util.NotificationUtil;
import androidx.media3.common.util.Util;
import androidx.media3.datasource.DataSource;
import androidx.media3.datasource.DefaultDataSourceFactory;
import androidx.media3.exoplayer.DefaultLoadControl;
import androidx.media3.exoplayer.DefaultRenderersFactory;
import androidx.media3.exoplayer.SimpleExoPlayer;
import androidx.media3.exoplayer.hls.HlsMediaSource;
import androidx.media3.exoplayer.source.ProgressiveMediaSource;
import androidx.media3.exoplayer.trackselection.DefaultTrackSelector;
import androidx.media3.ui.PlayerNotificationManager;

import com.bumptech.glide.Glide;
import com.facebook.network.connectionclass.ConnectionClassManager;
import com.facebook.network.connectionclass.ConnectionQuality;
import com.facebook.network.connectionclass.DeviceBandwidthSampler;
import com.naveed.ytextractor.ExtractorException;
import com.naveed.ytextractor.YoutubeStreamExtractor;
import com.naveed.ytextractor.model.YoutubeMedia;
import com.naveed.ytextractor.model.YoutubeMeta;
import com.wonderslate.data.network.Wonderslate;
import com.wonderslate.data.preferences.WonderPubSharedPrefs;
import com.wonderslate.prepjoy.R;
import com.wonderslate.prepjoy.Utils.Utils;
import com.wonderslate.prepjoy.Views.Activity.SplashActivity;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.concurrent.ExecutionException;

public class AudioPlayerService extends Service {
    private static final String TAG = "AudioPlayerService";
    private static final String NOTIFICATION_CHANNEL = "Wonderslate_Channel";

    SimpleExoPlayer player;
    private PlayerNotificationManager playerNotificationManager;
    private MediaSessionCompat mediaSession;

    private static String youtubeUri, resLink, audioTitle, audioDesc, audioThumbnail,bookName,bookImage;
    private static int bookId;
    public static  String resId;
    public static boolean hasPreview;
    private Bitmap audioThumbnailBitmap;
    private Uri videoUri;
   // private Uri shareAudioUri;
    private static long playPosition;
    private static final int currentVideoSource = 0;
    private static int audioNotificationId;
    private static final boolean playStatus = true;
    private boolean isHlsContent, isDashContent, isLiveStream;
    public static boolean isAudioPlaying;
    private String pResID =  null;
    //private ImageButton prevTrack, nextTrack;
    /*private List<Integer> sparseKeyList, sparseResolutionList;
    private HashMap<Integer, String> sparseVideoUrlList;
    private SparseArray<YtFile> sparseVideoMetaArray;*/
    HlsMediaSource hlsMediaSource;
    ProgressiveMediaSource extractorMediaSource;
    private List<Integer> sparseAdaptiveResolutionList, sparseMuxedResolutionList;
    private HashMap<Integer, String> sparseAdaptiveVideoUrlList, sparseMuxedVideoUrlList;
    private List<String> sparseOPUSAudioUrl;
    private boolean isProgressive, isAudioOnly, isJWPlayer, isVimeo;
    private DeviceBandwidthSampler mDeviceBandwidthSampler;

    private ConnectionQuality mConnectionClass = ConnectionQuality.UNKNOWN;
    private Context context;

    @Override
    public void onCreate() {
        //Initialize All Things
        super.onCreate();
        context = this;

        //To be used only if Custom Actions are not needed
        /*playerNotificationManager = PlayerNotificationManager.createWithNotificationChannel(
                context,
                NOTIFICATION_CHANNEL,
                R.string.application_name,
                audioNotificationID(),
                new MediaDescriptionAdapter() {
                    @Override
                    public String getCurrentContentTitle(Player player) {
                        return audioTitle;
                    }

                    @Nullable
                    @Override
                    public PendingIntent createCurrentContentIntent(Player player) {
                        return null;
                    }

                    @Nullable
                    @Override
                    public String getCurrentContentText(Player player) {
                        return audioDesc;
                    }

                    @Nullable
                    @Override
                    public Bitmap getCurrentLargeIcon(Player player, BitmapCallback callback) {
                        return audioThumbnailBitmap;
                    }
                }
        );*/

        //For supporting Custom Actions use this
        /*NotificationUtil.createNotificationChannel(
                context, NOTIFICATION_CHANNEL, R.string.application_name, NotificationUtil.IMPORTANCE_NONE);*/

        //initNotificationManager();

    }

    private void releaseAll() {
        try {
            if (playerNotificationManager != null) {
                playerNotificationManager.setPlayer(null);
                playerNotificationManager = null;
            }

            if (player != null) {
                player.release();
                player = null;
            }

            if (mediaSession != null) {
                if (mediaSession.isActive())
                    mediaSession.setActive(false);
                mediaSession.release();
                mediaSession = null;
            }

            isAudioPlaying = false;
            resId =  null;
        } catch (Exception e) {
            Log.e(TAG, "releaseAll: ", e);
        }
    }

    /*private void initNotificationManager() {

        try {
            if (context == null)
                context = this;

            releaseAll();

            *//*internetConnectionChecker =  new InternetConnectionChecker();*//*
            NotificationUtil.createNotificationChannel(context, NOTIFICATION_CHANNEL, R.string.app_name,
                    R.string.login_screen_login_text_desc, NotificationUtil.IMPORTANCE_NONE);

            player = ExoPlayerFactory.newSimpleInstance(this, new DefaultRenderersFactory(this),
                    new DefaultTrackSelector(), new DefaultLoadControl());

            playerNotificationManager = new PlayerNotificationManager(
                    context,
                    NOTIFICATION_CHANNEL,
                    audioNotificationID(),
                    new PlayerNotificationManager.MediaDescriptionAdapter() {
                        @Override
                        public String getCurrentContentTitle(Player player) {
                            return audioTitle;
                        }

                        @Nullable
                        @Override
                        public PendingIntent createCurrentContentIntent(Player player) {
                            Intent intent = new Intent(AudioPlayerService.this, SplashActivity.class);
                            intent.putExtra("bookId", bookId);
                            intent.putExtra("hasPreview",hasPreview);
                            intent.setAction(Intent.ACTION_SEND);
                            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                            return PendingIntent.getActivity(getApplicationContext(), audioNotificationId, intent,
                                    PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
                        }

                        @Nullable
                        @Override
                        public String getCurrentContentText(Player player) {
                            return audioDesc;
                        }

                        @Nullable
                        @Override
                        public Bitmap getCurrentLargeIcon(Player player, PlayerNotificationManager.BitmapCallback callback) {
                            return audioThumbnailBitmap;
                        }
                    },
                    new CustomAudioActionReceiver()
            );

            playerNotificationManager.setNotificationListener(new NotificationListener() {
                @Override
                public void onNotificationStarted(int notificationId, Notification notification) {
                    try {
                        startForeground(notificationId, notification);
                    } catch (Exception e) {
                        Log.e(TAG, "onNotificationStarted: ", e);
                    }
                }

                @Override
                public void onNotificationCancelled(int notificationId) {
                    stopSelf();
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "initNotificationManager: ", e);
        }

    }*/

    @Override
    public void onDestroy() {
        playerNotificationManager.setPlayer(null);
        playerNotificationManager = null;
        player.release();
        player = null;

        super.onDestroy();
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null && intent.getAction() != null && !intent.getAction().isEmpty()) {
            if (intent.getAction().equalsIgnoreCase("Start_Service")) {
                youtubeUri = intent.getStringExtra("videoUrl");
                resLink = intent.getStringExtra("resLink");
                audioTitle = intent.getStringExtra("audioTitle");
                audioDesc = intent.getStringExtra("audioDesc");
                bookId = intent.getIntExtra("bookId",0);
                hasPreview = intent.getBooleanExtra("hasPreview",false);
                if(resLink != null) {
                    resLink = Uri.parse(resLink.trim()).toString();
                    try {
                        resLink = URLDecoder.decode(resLink, StandardCharsets.UTF_8.name());
                    } catch (UnsupportedEncodingException e) {
                        Log.e(TAG, "Exception while getting res link");
                    }
                }

                if (resLink != null && !resLink.isEmpty() && (!resLink.contains("/") || resLink.contains("youtube") || resLink.contains("youtu.be"))){
                    isJWPlayer = false;
                    audioThumbnail = "https://img.youtube.com/vi/" + resLink + "/0.jpg";
                }
                else if (resLink != null && !resLink.isEmpty() && resLink.contains("jwplatform")) {
                    isJWPlayer = true;
                    String mediaId = JWPlayerMediaExtractor.jwPlayerMediaIdFromResLink(resLink);

                    audioThumbnail = "https://cdn.jwplayer.com/v2/media/" + mediaId + "/poster.jpg?width=720";
                }
                bookImage = intent.getStringExtra("bookImage");
                bookName = intent.getStringExtra("bookName");
                pResID = intent.getStringExtra("pResID");

             //   shareAudioUri = Uri.parse(intent.getStringExtra("shareAudio"));

                AsyncTask.execute(() -> {
                    try {
                        audioThumbnailBitmap = Glide.with(AudioPlayerService.this)
                                .asBitmap()
                                .load(audioThumbnail)
                                .submit()
                                .get();
                    } catch (ExecutionException | InterruptedException e) {
                        android.util.Log.e(TAG, "Error while getting audio thumbnail", e);
                    }
                });

                //Start Youtube Audio Extraction
                if (resLink != null && !resLink.isEmpty() && (!resLink.contains("/") || resLink.contains("youtube") || resLink.contains("youtu.be"))){
                    isJWPlayer = false;
                    extractYoutubeUrl();
                }
                else if (resLink != null && !resLink.isEmpty() && (resLink.contains("jwplatform") || resLink.contains("jwplayer"))) {
                    isJWPlayer = true;
                    mDeviceBandwidthSampler = DeviceBandwidthSampler.getInstance();
                    String mediaId = JWPlayerMediaExtractor.jwPlayerMediaIdFromResLink(resLink);
                    extractJWPlayerUrl(mediaId);
                }
                return START_STICKY;
            } else if (intent.getAction().equalsIgnoreCase("Stop_Service") && isAudioPlaying) {
                initPlayer();
                isAudioPlaying = false;
                resId =  null;
                stopForeground(true);
                stopSelf(startId);
                playerNotificationManager.setPlayer(null);
                return START_NOT_STICKY;
            } else {
                return START_NOT_STICKY;
            }
        } else {
            return START_NOT_STICKY;
        }
    }

    private int audioNotificationID() {
        Date now = new Date();
        audioNotificationId = Integer.parseInt(new SimpleDateFormat("ddHHmmss", Locale.getDefault()).format(now));
        return audioNotificationId;
    }

    private void extractYoutubeUrl() {
        try {
            new YoutubeStreamExtractor(new YoutubeStreamExtractor.ExtractorListner() {
                @Override
                public void onExtractionGoesWrong(ExtractorException e, String message, String data) {
                    Log.e(TAG, "Exception while getting youtube links", e);
                    initPlayer();
                    Wonderslate.getInstance().getSharedPrefs().setCurrentAudioTrack("");
                    Intent intent = new Intent();
                    intent.setAction("AUDIO_ERROR");
                    sendBroadcast(intent);
                }

                @Override
                public void onExtractionDone(List<YoutubeMedia> adativeStream, List<YoutubeMedia> muxedStream, YoutubeMeta meta) {
                    sparseAdaptiveResolutionList = new ArrayList<>();
                    sparseMuxedResolutionList = new ArrayList<>();
                    sparseAdaptiveVideoUrlList = new HashMap<>();
                    sparseMuxedVideoUrlList = new HashMap<>();
                    sparseOPUSAudioUrl = new ArrayList<>();
                    //Use Adaptive Streams for DASH Content
                    //This stream provides all video resolutions
                    //In the adaptive streams, you will find Audio Only and Video Only streams.
                    //Merge both the streams together before starting exoplayer.
                    //Refer below to see how it was working earlier.
                    //This code has a new extraction library.
                    //This library uses the same underlying google api but, different way of data extraction

                    //While Merging select video url according to resolution
                    //Select any Audio URL(OPUS Audio) from audio url list.
                    //Selection of audio bitrate, does not matter now.
                    //Later we can give better adaptive audio support.
                    if (meta.getisLive() || meta.getIsLiveContent()) {
                        //For Live Video
                        if (meta.getisLive()){
                            isLiveStream = true;
                        }
                        if (muxedStream != null && muxedStream.size() > 0){
                            for (int i = 0; i < muxedStream.size(); i++) {
                                YoutubeMedia media = muxedStream.get(i);
                                if (media.isMuxed()) {
                                    //Muxed Video URL
                                    int resolution = getLiveIndexResolution(i);
                                    if (!sparseAdaptiveResolutionList.contains(resolution) && resolution <= 1080) {
                                        isHlsContent = true;
                                        isDashContent = false;
                                        sparseAdaptiveResolutionList.add(resolution);
                                        sparseAdaptiveVideoUrlList.put(resolution,
                                                media.getUrl());
                                    }
                                }
                            }
                        }
                        else if (adativeStream != null && adativeStream.size() > 0) {
                            for (YoutubeMedia media : adativeStream) {
                                if (media.isAudioOnly()) {
                                    //OPUS Audio URL
                                    if (!sparseOPUSAudioUrl.contains(media.getUrl())) {
                                        isHlsContent = false;
                                        isDashContent = false;
                                        isAudioOnly = true;
                                        sparseOPUSAudioUrl.add(media.getUrl());
                                    }
                                }
                                else if (media.isMuxed()) {
                                    //Progressive Video URL
                                    int resolution = Integer.valueOf(media.getResolution().replace("p", ""));
                                    if (!sparseAdaptiveResolutionList.contains(resolution) && resolution <= 1080) {
                                        isHlsContent = false;
                                        isDashContent = false;
                                        isProgressive = true;
                                        sparseAdaptiveResolutionList.add(resolution);
                                        sparseAdaptiveVideoUrlList.put(resolution, media.getUrl());
                                    }
                                }
                            }
                        }
                    }
                    else {
                        isLiveStream = false;
                        if (adativeStream != null && adativeStream.size() > 0) {
                            for (YoutubeMedia media : adativeStream) {
                                if (media.isAudioOnly()) {
                                    //OPUS Audio URL
                                    if (!sparseOPUSAudioUrl.contains(media.getUrl())) {
                                        isHlsContent = false;
                                        isDashContent = false;
                                        isAudioOnly = true;
                                        sparseOPUSAudioUrl.add(media.getUrl());
                                    }
                                }
                                else if (media.isMuxed()) {
                                    //Progressive Video URL
                                    int resolution = Integer.valueOf(media.getResolution().replace("p", ""));
                                    if (!sparseAdaptiveResolutionList.contains(resolution) && resolution <= 1080) {
                                        isHlsContent = false;
                                        isDashContent = false;
                                        isProgressive = true;
                                        sparseAdaptiveResolutionList.add(resolution);
                                        sparseAdaptiveVideoUrlList.put(resolution, media.getUrl());
                                    }
                                }
                            }
                        }
                        else if (muxedStream != null && muxedStream.size() > 0) {
                            for (int i = 0; i < muxedStream.size(); i++) {
                                YoutubeMedia media = muxedStream.get(i);
                                if (media.isMuxed()) {
                                    //Muxed Video URL
                                    int resolution = getLiveIndexResolution(i);
                                    if (!sparseAdaptiveResolutionList.contains(resolution) && resolution <= 1080) {
                                        isHlsContent = false;
                                        isDashContent = false;
                                        sparseAdaptiveResolutionList.add(resolution);
                                        sparseAdaptiveVideoUrlList.put(resolution,
                                                media.getUrl());
                                    }
                                }
                            }
                        }
                    }

                    if (isAudioOnly) {
                        if (isLiveStream && sparseAdaptiveResolutionList != null && sparseAdaptiveResolutionList.size() > 0) {
                            youtubeUri = sparseAdaptiveVideoUrlList.get(sparseAdaptiveResolutionList.get(0));
                            initPlayer();
                        }
                        else if (sparseOPUSAudioUrl != null && sparseOPUSAudioUrl.size() > 0) {
                            youtubeUri = sparseOPUSAudioUrl.get(0);
                            initPlayer();
                        }
                        else {
                            initPlayer();
                            Wonderslate.getInstance().getSharedPrefs().setCurrentAudioTrack("");
                            Intent intent = new Intent();
                            intent.setAction("AUDIO_ERROR");
                            sendBroadcast(intent);
                        }
                    }
                    else if (sparseAdaptiveResolutionList != null && sparseAdaptiveResolutionList.size() > 0) {
                        youtubeUri = sparseAdaptiveVideoUrlList.get(sparseAdaptiveResolutionList.get(0));
                        initPlayer();
                    }
                    else {
                        initPlayer();
                        Wonderslate.getInstance().getSharedPrefs().setCurrentAudioTrack("");
                        Intent intent = new Intent();
                        intent.setAction("AUDIO_ERROR");
                        sendBroadcast(intent);
                    }
                }
            }).Extract(resLink);
        } catch (Exception e) {
            Log.e(TAG, "extractYoutubeUrl: ", e);
        }
    }

    private void extractJWPlayerUrl(String mediaId) {
        JWPlayerMediaExtractor.getAllJWPlayerLinks(mediaId, Utils.secureJWPlayer() ? Utils.token(mediaId) : "", new JWPlayerExtractorCallBack() {
            @Override
            public void onExtractorSuccess(int responseCode, HashMap<Integer, String> mediaResponseMap, String status) {
                boolean isVideoLive = VideoType.getType(status) == VideoType.LIVE;

                if(isVideoLive) {
                    isLiveStream = true;
                    isHlsContent = true;
                } else {
                    isLiveStream = false;
                }

                sparseAdaptiveVideoUrlList = mediaResponseMap;
                sparseAdaptiveResolutionList = new ArrayList<>();
                sparseAdaptiveResolutionList.addAll(mediaResponseMap.keySet());
                if (sparseAdaptiveVideoUrlList != null && sparseAdaptiveVideoUrlList.size() > 0) {
                    Collections.sort(sparseAdaptiveResolutionList, Collections.reverseOrder());

                    mDeviceBandwidthSampler.startSampling();

                    mConnectionClass = ConnectionClassManager.getInstance().getCurrentBandwidthQuality();

                    youtubeUri = "";
                    if(isVideoLive) {
                        youtubeUri = Uri.parse(sparseAdaptiveVideoUrlList.get(-1)).toString();
                    } else if (sparseAdaptiveVideoUrlList != null && sparseAdaptiveVideoUrlList.size() > 0 &&
                            sparseAdaptiveVideoUrlList.get(0) != null && !Objects.requireNonNull(sparseAdaptiveVideoUrlList.get(0)).isEmpty() &&
                            !Objects.requireNonNull(sparseAdaptiveVideoUrlList.get(0)).equalsIgnoreCase("null")){
                        youtubeUri = Uri.parse(sparseAdaptiveVideoUrlList.get(0)).toString();
                    }
                    else {
                        audioError();
                    }

                    mDeviceBandwidthSampler.stopSampling();
                    initPlayer();
                } else {
                    audioError();
                }
            }

            @Override
            public void onExtractorError(int responseCode, String error, String status) {
                audioError();
            }
        });
    }

    private void audioError() {
        Intent intent = new Intent();
        intent.setAction("AUDIO_ERROR");
        sendBroadcast(intent);
    }

    private int getLiveIndexResolution(int index) {
        switch (index) {
            case 0:
                return 144;
            case 1:
                return 240;
            case 2:
                return 360;
            case 3:
                return 480;
            case 4:
                return 720;
            case 5:
                return 1080;
            case 6:
                return 1440;
        }
        return 144;
    }

    private void initPlayer() {
        try {
            if (playerNotificationManager == null) {
                //initNotificationManager();
            }

            playerNotificationManager.setPlayer(player);
            playerNotificationManager.setColorized(true);
            playerNotificationManager.setUseChronometer(true);
            playerNotificationManager.setSmallIcon(R.drawable.exo_notification_small_icon);
            playerNotificationManager.setBadgeIconType(NotificationCompat.BADGE_ICON_NONE);
            playerNotificationManager.setVisibility(NotificationCompat.VISIBILITY_PUBLIC);
            /*playerNotificationManager.setUseNavigationActions(false);
            playerNotificationManager.setFastForwardIncrementMs(10000);
            playerNotificationManager.setRewindIncrementMs(10000);*/
            playerNotificationManager.setPriority(NotificationCompat.PRIORITY_DEFAULT);

            Log.e(TAG, "Audio Url: " + youtubeUri);

            videoUri = Uri.parse(youtubeUri);

            // Produces DataSource instances through which media data is loaded.
            // Default parameters, except allowCrossProtocolRedirects is true

// This is the MediaSource representing the media to be played.
            DataSource.Factory dataSourceFactory = new DefaultDataSourceFactory(this,
                    Util.getUserAgent(this, getResources().getString(R.string.appNameID)));

            if (isHlsContent || videoUri.toString().contains(".m3u8")) {
                Log.e(TAG, "Audio Url: " + videoUri.toString());
                hlsMediaSource = new HlsMediaSource.Factory(dataSourceFactory).createMediaSource(MediaItem.fromUri(videoUri));
                player.setPlayWhenReady(true);

                // Prepare the player with the source.
                player.prepare(hlsMediaSource);
            }
            else {
                extractorMediaSource = new ProgressiveMediaSource.Factory(dataSourceFactory).createMediaSource(MediaItem.fromUri(videoUri));
                player.setPlayWhenReady(true);

                // Prepare the player with the source.
                player.prepare(extractorMediaSource);
            }

            try {
                if (playPosition > 0) {
                    player.seekTo(playPosition);
                } else if (!WonderPubSharedPrefs.getInstance(AudioPlayerService.this).getVideoResumePositionList().isEmpty()) {
                    HashMap<String, Long> videoResumePosList = new HashMap<>();
                    String[] pairs = WonderPubSharedPrefs.getInstance(AudioPlayerService.this).getVideoResumePositionList().split(",");
                    for (int i = 0; i < pairs.length; i++) {
                        String pair = pairs[i];
                        int index = pair.lastIndexOf("=");
                        String link = pair.substring(0, index);
                        String videoPlayBackPosition = pair.substring(index).replace("=", "");
                        //String[] keyValue = pair.split("=");
                        videoResumePosList.put(link.replace("{", "").trim(),
                                Long.valueOf(videoPlayBackPosition.replace("}", "").trim()));
                    }
                    if (!videoResumePosList.isEmpty()) {
                        if (videoResumePosList.get(resLink) != null && videoResumePosList.get(resLink) > 0) {
                            player.seekTo(videoResumePosList.get(resLink));
                        }
                    }
                } else {
                    player.seekTo(0);
                }
            } catch (NumberFormatException e) {
                player.seekTo(0);
            }


            AudioAttributes audioAttributes = new AudioAttributes.Builder()
                    .setUsage(C.USAGE_MEDIA)
                    .setContentType(C.AUDIO_CONTENT_TYPE_MUSIC)
                    .build();

            player.setAudioAttributes(audioAttributes, true);

            MediaMetadataCompat mediaMetadataCompat = new MediaMetadataCompat.Builder()
                    .putBitmap(MediaMetadataCompat.METADATA_KEY_ALBUM_ART, audioThumbnailBitmap)
                    .build();


            mediaSession = new MediaSessionCompat(AudioPlayerService.this, "WONDERSLATE_AUDIO");
            mediaSession.setActive(true);
            mediaSession.setMetadata(mediaMetadataCompat);
            playerNotificationManager.setMediaSessionToken(mediaSession.getSessionToken());

            isAudioPlaying = true;
            resId = pResID;
        } catch (Exception e) {
            Log.e(TAG, "initPlayer: ", e);
        }
    }
}


