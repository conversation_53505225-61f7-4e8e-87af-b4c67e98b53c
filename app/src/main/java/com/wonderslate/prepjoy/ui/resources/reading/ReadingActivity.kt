package com.wonderslate.prepjoy.ui.resources.reading

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.wonderslate.prepjoy.ui.ibookgpt.GPTChatActivity
import com.wonderslate.prepjoy.ui.resources.reading.utils.DataHolder
import com.ws.chapter_list.data.models.ChapterResources
import com.ws.core_ui.base.ActBase
import com.ws.core_ui.extensions.addFragment
import com.ws.core_ui.extensions.finishWithAnim
import com.ws.core_ui.extensions.replaceFragment
import com.ws.database.room.entity.Chapter
import com.ws.database.room.entity.Resource
import com.ws.resources.R
import com.ws.resources.data.models.AnnotationsRequest
import com.ws.resources.data.models.ReadingFragConfig
import com.ws.resources.databinding.LayoutReadingActBinding
import com.ws.resources.ui.annotations.NotesAndHighlightsFrag
import com.ws.resources.ui.weblink.WeblinkFrag

class ReadingActivity: ActBase<LayoutReadingActBinding>(isSecure = true),
    ReadingFrag.OnReadingFragInteractionListener,
    WeblinkFrag.OnWeblinkFragInteractionListener,
    NotesAndHighlightsFrag.OnNotesAndHighlightsFragInteractionListener{

    private var isDeepLinked: Boolean = false

    private var config: ReadingFragConfig? = null

    private lateinit var chapterList: ArrayList<ChapterResources>

    private val requestPermissionLauncher =
        registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->

            permissions.entries.forEach { (_, isGranted) ->
                if (isGranted) {
                    // Permission is granted. Continue the action or workflow in your
                    // app.
                    readingFrag?.onStoragePermissionGranted()
                } else {
                    // Explain to the user that the feature is unavailable because the
                    // features requires a permission that the user has denied. At the
                    // same time, respect the user's decision. Don't link to system
                    // settings in an effort to convince the user to change their
                    // decision.
                    onBackPressed()
                }
            }
        }


    private var readingFrag: ReadingFrag? = null

    override fun bindView(): LayoutReadingActBinding {
        return LayoutReadingActBinding.inflate(layoutInflater)
    }

    override fun init() {
        supportActionBar?.hide()
        config = intent.getSerializableExtra(ReadingFrag.ARG_READING_CONFIG) as ReadingFragConfig
        chapterList = if (intent.getSerializableExtra(ReadingFrag.ARG_CHAPTERS_LIST) != null)
            intent.getSerializableExtra(ReadingFrag.ARG_CHAPTERS_LIST) as ArrayList<ChapterResources>
        else
            ArrayList()
        val resource = if (intent.getSerializableExtra("readingDeepLinkData") != null) {
            intent.getSerializableExtra("readingDeepLinkData") as Resource
        } else {
            null
        }
        val chapter = if (intent.getSerializableExtra("chapterDeepLinkData") != null) {
            intent.getSerializableExtra("chapterDeepLinkData") as Chapter
        }
        else {
            null
        }

        isDeepLinked = intent.getBooleanExtra("deepLink", false)

        replaceFragment(
            R.id.fragmentContainer,
            ReadingFrag.newInstance(config!!, resource, chapter, chapterList).also {
                readingFrag = it
            }
        )
    }

    override fun onBackBtnClicked() {
        onBackPressed()
    }

    override fun checkForStoragePermission() {
        when {
            ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED &&
                ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED -> {
                // You can use the API that requires the permission.
                readingFrag?.onStoragePermissionGranted()
            }
            ActivityCompat.shouldShowRequestPermissionRationale(this, Manifest.permission.READ_EXTERNAL_STORAGE)
                || ActivityCompat.shouldShowRequestPermissionRationale(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) -> {
                // In an educational UI, explain to the user why your app requires this
                // permission for a specific feature to behave as expected. In this UI,
                // include a "cancel" or "no thanks" button that allows the user to
                // continue using your app without granting the permission.
                //showInContextUI(...)
                //onBackPressed()
                requestPermissionLauncher.launch(
                    arrayOf(
                        Manifest.permission.READ_EXTERNAL_STORAGE,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE
                    )
                )
            }
            else -> {
                // You can directly ask for the permission.
                // The registered ActivityResultCallback gets the result of this request.
                requestPermissionLauncher.launch(
                    arrayOf(
                        Manifest.permission.READ_EXTERNAL_STORAGE,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE
                    )
                )
            }
        }

    }

    override fun onWebSearch(resource: Resource, term: String) {
        replaceFragment(
            R.id.fragmentContainer,
            WeblinkFrag.newInstance(resource)
        ) {
            addToBackStack("WebSearch")
        }
    }

    override fun onOpenNotesList(annotationsRequest: AnnotationsRequest) {
        addFragment(
            R.id.fragmentContainer,
            NotesAndHighlightsFrag.newInstance(annotationsRequest)
        ) {
            addToBackStack("Notes")
        }
    }

    override fun onOpenGPTMCQChat(
        resource: Resource,
        chapter: Chapter,
        doubts: String,
        imgData: String,
        imgUrl: String,
        resOptionType: String,
        lang: String,
        query: String,
        quizId: Int
    ) {
        // Open GPT Chat Activity. Pass resId, chapterId, username
        val intent: Intent = Intent(
            this,
            GPTChatActivity::class.java
        )
        intent.putExtra(
            "resId",
            resource.id
        )
        intent.putExtra(
            "chapterId",
            resource.topicId
        )
        intent.putExtra("bookTitle", "")
        intent.putExtra("bookId", chapter.bookId)
        intent.putExtra(
            "chapterTitle",
            chapter.name
        )
        intent.putExtra(
            "resName",
            resource.resName
        )
        intent.putExtra("selectedChapter", 0)
        intent.putExtra("doubt", doubts)
        DataHolder.largeData = imgData
        intent.putExtra("imgUrl", imgUrl)
        intent.putExtra("bookLanguage", "english")
        intent.putExtra("isResOption", true)
        intent.putExtra("resOption", resOptionType)
        intent.putExtra("resOptionLang", lang)
        intent.putExtra("resOptionQuery", query)
        intent.putExtra("quizId", quizId)
        intent.putExtra(
            "bookType",
            if (config!!.bookType.equals("bookgpt")) "gptbook" else (if (config!!.bookType.equals("ebookwithai")) "ebookwithai" else "ebook")
        )

        if (chapter.previewChapter) {
            intent.putExtra("isPreview", true)
        }
        startActivity(intent)
    }

    override fun onOpenGPTChat(resource: Resource, chapter: Chapter, doubts: String, imgData: String, imgUrl: String) {
        // Open GPT Chat Activity. Pass resId, chapterId, username
        val intent: Intent = Intent(
            this,
            GPTChatActivity::class.java
        )
        intent.putExtra(
            "resId",
            resource.id
        )
        intent.putExtra(
            "chapterId",
            resource.topicId
        )
        intent.putExtra("bookTitle", "")
        intent.putExtra("bookId", chapter.bookId)
        intent.putExtra(
            "chapterTitle",
            chapter.name
        )
        intent.putExtra(
            "resName",
            resource.resName
        )
        intent.putExtra("selectedChapter", 0)
        intent.putExtra("doubt", doubts)
        DataHolder.largeData = imgData
        intent.putExtra("imgUrl", imgUrl)
        intent.putExtra("bookLanguage", "english")
        intent.putExtra(
            "bookType",
            if (config!!.bookType.equals("bookgpt")) "gptbook" else (if (config!!.bookType.equals("ebookwithai")) "ebookwithai" else "ebook")
        )

        if (chapter.previewChapter) {
            intent.putExtra("isPreview", true)
        }
        startActivity(intent)
        if (chapter.previewChapter) {
            finish()
        }
    }

    override fun onBackPressed() {
        if (isDeepLinked) {
            setResult(Activity.RESULT_OK, Intent())
            finishWithAnim()
        }
        else {
            if (readingFrag?.onBackPressed() == false) {
                if(isFinishing || isDestroyed)
                    return

                if(supportFragmentManager.backStackEntryCount > 0) {
                    supportFragmentManager.popBackStackImmediate()
                    return
                }
                finishWithAnim()
            }
        }
    }

    companion object {

        @JvmStatic
        fun createIntent(context: Context, readingFragConfig: ReadingFragConfig): Intent {
            return Intent(context, ReadingActivity::class.java).also {
                it.putExtra(ReadingFrag.ARG_READING_CONFIG, readingFragConfig)
            }
        }

        @JvmStatic
        fun createIntentWithChapterList(context: Context, readingFragConfig: ReadingFragConfig, chapterList: List<ChapterResources>): Intent {
            return Intent(context, ReadingActivity::class.java).also {
                it.putExtra(ReadingFrag.ARG_READING_CONFIG, readingFragConfig)
                it.putExtra(ReadingFrag.ARG_CHAPTERS_LIST, ArrayList(chapterList)) // Convert to ArrayList
            }
        }
    }

    override fun onCloseWebView() {
        if(supportFragmentManager.backStackEntryCount > 0)
            supportFragmentManager.popBackStackImmediate()
    }
}