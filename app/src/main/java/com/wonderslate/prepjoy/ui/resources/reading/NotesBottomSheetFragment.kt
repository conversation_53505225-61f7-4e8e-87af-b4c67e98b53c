package com.wonderslate.prepjoy.ui.resources.reading

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.ws.core_ui.utils.DateTimeUtils
import com.ws.resources.databinding.FragmentNoteBottomSheetBinding
import org.json.JSONObject

class NotesBottomSheetFragment : DialogFragment() {

    companion object {
        private const val TAG = "ContactBottomSheet"

        @JvmStatic
        fun showWithAnnotation(
            fManager: FragmentManager?,
            selected: JSONObject,
            listener: OnNoteEditListener?
        ) {
            NotesBottomSheetFragment().also { dialog ->
                dialog.selected = selected
                dialog.listener = listener
                fManager?.let {
                    dialog.show(it, TAG)
                }
            }
        }

    }

    private var listener: OnNoteEditListener? = null
    private var isFromDone = false
    private var selected = JSONObject()
    private var isUpdate = false

    private var binding: FragmentNoteBottomSheetBinding? = null


    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        if (!isFromDone)
            listener?.onDismiss()
        isFromDone = false
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentNoteBottomSheetBinding.inflate(inflater, container, false)
        return binding?.root
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        dialog?.window?.apply {
            attributes?.apply {
                width = ViewGroup.LayoutParams.MATCH_PARENT
                height = ViewGroup.LayoutParams.MATCH_PARENT
            }
            setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
        }
        binding?.tvSelected?.text = selected.optString("quote")
        selected.optString("text").takeIf { it.isNotEmpty() }?.let { text ->
            binding?.etNote?.setText(text)
            isUpdate = true
        }
        binding?.tvTime?.text = DateTimeUtils.getCurrentDeviceTime("hh:mm a")
        binding?.etNote?.requestFocus()
        binding?.tvDone?.setOnClickListener {
            isFromDone = true
            (binding?.etNote?.text?.toString() ?: "").also { note ->
                if (isUpdate) {
                    listener?.onUpdate(note)
                } else
                    listener?.onNew(note)
            }
            dismissAllowingStateLoss()
        }

        binding?.btnClose?.setOnClickListener {
            dismissAllowingStateLoss()
        }
    }

    interface OnNoteEditListener {
        fun onNew(note: String)
        fun onUpdate(note: String)
        fun onDismiss()
    }
}
