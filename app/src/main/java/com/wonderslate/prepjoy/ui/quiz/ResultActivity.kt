package com.wonderslate.prepjoy.ui.quiz

import android.annotation.SuppressLint
import android.app.Activity
import android.content.*
import android.os.Bundle
import android.util.Log
import android.view.View
import android.webkit.*
import android.widget.Toast
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.Lifecycle
import com.wang.avi.AVLoadingIndicatorView
import com.wonderslate.commons.Data
import com.wonderslate.commons.Status
import com.wonderslate.data.network.WSAPIManager
import com.wonderslate.data.network.Wonderslate
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.Utils.*
import com.wonderslate.prepjoy.ui.BaseActivity
import com.wonderslate.prepjoy.ui.tests.DailyTestViewModel
import com.ws.commons.enums.BotPlay
import com.ws.commons.enums.JSAudio
import com.ws.core_ui.extensions.finishWithAnim
// import kotlinx.android.synthetic.main.activity_quiz.*
import org.json.JSONObject
import org.koin.androidx.viewmodel.ext.android.viewModel

class ResultActivity : BaseActivity(), QuizInterface {
    private val dailyViewModel by viewModel<DailyTestViewModel>()
    var mContext: Context? = null
    private var webview: NestedWebView? = null
    private var parentView: ConstraintLayout? = null
    var progressLoader: AVLoadingIndicatorView? = null
    var summaryAnswers: String? = ""
    var summaryQuestion: String? = ""


    override fun onStart() {
        super.onStart()
        Wonderslate.APP_STATE = Lifecycle.Event.ON_START
    }

    override fun onResume() {
        super.onResume()
        Wonderslate.APP_STATE = Lifecycle.Event.ON_RESUME
    }

    override fun onStop() {
        super.onStop()
        Wonderslate.APP_STATE = Lifecycle.Event.ON_STOP
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Wonderslate.aClass = javaClass
        if (supportActionBar != null) {
            supportActionBar!!.hide()
        }
        mContext = this
        initObserver()
        summaryAnswers = intent.getStringExtra("answers")
        summaryQuestion = intent.getStringExtra("questions")
        webview = findViewById(R.id.webView)
        webview?.visibility = View.GONE
        progressLoader = findViewById(R.id.progressLoader)
        parentView = findViewById(R.id.parent_view)
        progressLoader?.smoothToShow()
        loadWebView()

        Utils.disableScreenShot(this)
    }

    private fun initObserver() {
        dailyViewModel.reportIssue.observe(this, ::observeIssue)
    }

    override fun getLayoutResource(): Int {
        return R.layout.activity_result
    }

    private fun observeIssue(data: Data<JSONObject>) {
        when(data.responseType) {
            Status.LOADING -> {

            }
            Status.SUCCESSFUL -> {
                progressLoader?.smoothToHide()
                data.data?.let {
                    if (null != webview) {
                        progressLoader?.smoothToHide()
                        webview?.loadUrl("javascript:clearModal()")
                        Toast.makeText(applicationContext,
                            "Thanks for your valuable feedback", Toast.LENGTH_SHORT).show()
                    }
                }
            }
            Status.ERROR -> {
                serverError()
            }
            Status.HTTP_UNAVAILABLE -> {
                networkError()
            }
        }
    }

    private fun serverError() {
        progressLoader?.hide()
        if (Utils.isNetworkAvailable(this)) {
            Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), this)
        } else {
            Utils.showTopSnackBar(resources.getString(R.string.internet_connection_offline_text), this)
        }
    }

    private fun networkError() {
        progressLoader?.hide()
        if (Utils.isNetworkAvailable(this)) {
            Utils.showTopSnackBar(resources.getString(R.string.server_under_maintenance), this)
        } else {
            Utils.showTopSnackBar(resources.getString(R.string.internet_connection_offline_text), this)
        }
    }

    @SuppressLint("SetJavaScriptEnabled")
    fun setupWebView() {
        webview?.settings?.javaScriptEnabled = true
        webview?.settings?.setSupportZoom(false)
        webview?.settings?.builtInZoomControls = false
        webview?.isVerticalScrollBarEnabled = true
        webview?.isHorizontalScrollBarEnabled = true
        webview?.settings?.domStorageEnabled = true
        webview?.settings?.displayZoomControls = false
        webview?.setOnLongClickListener { true }
        webview?.settings?.loadsImagesAutomatically = true
        webview?.settings?.javaScriptCanOpenWindowsAutomatically = true
        webview?.settings?.allowUniversalAccessFromFileURLs = true
        WebView.setWebContentsDebuggingEnabled(true)
        webview?.clearCache(true)
        webview?.let { progressLoader?.let {
            QuizJSInterface(this) }
        }?.let { webview?.addJavascriptInterface(it, "JSInterface") }
    }

    private fun loadWebView() {
        setupWebView()
        try {
            webview?.webViewClient = object : WebViewClient() {
                @Deprecated("Deprecated in Java")
                override fun onReceivedError(view: WebView, errorCode: Int, description: String, failingUrl: String) {
                    try {
                        Log.e("data error", ":")
                        webview?.visibility = View.VISIBLE
                        webview?.loadUrl("about:blank")
                    } catch (e: java.lang.Exception) {
                        e.printStackTrace()
                    }
                }

                override fun onPageFinished(view: WebView?, url: String?) {
                    try {
                        progressLoader?.smoothToHide()
                        webview?.visibility = View.VISIBLE
                        val summaryObject = summaryAnswers?.let { JSONObject(it) }
                        summaryObject?.put("serviceURL", WSAPIManager.SERVICE3.toString())
                        summaryAnswers = summaryObject.toString()
                        webview?.loadUrl("javascript:openSummary($summaryAnswers,$summaryQuestion)")
                    } catch (e: Exception) {
                        Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), mContext as Activity?)
                        e.printStackTrace()
                    }

                }
            }
            webview?.webChromeClient = object : WebChromeClient() {
                override fun onConsoleMessage(cm: ConsoleMessage): Boolean {

                    return true
                }

                override fun onProgressChanged(view: WebView, newProgress: Int) {
                    super.onProgressChanged(view, newProgress)

                }
            }
            webview?.loadUrl("file:///android_asset/quiz/assets/resultSummary.html")

        } catch (e: Exception) {
            Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), this)
            e.printStackTrace()
        }

    }

    override fun onBackPressed() {
        try {
            exitScreen()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /*private fun loadBlank() {
        runOnUiThread {
            webview?.webViewClient = WebViewClient()
            webview?.loadUrl("about:blank")
        }
    }*/

    private fun exitScreen() {
        //loadBlank()
        finishWithAnim()
    }

    private fun reportQuizIssue(json: JSONObject) {
        runOnUiThread {
            if (json.has("issuetext") && json.has("questionIssueId") &&
                json.has("selectedIssue")) {
                val issue = json.getString("selectedIssue")
                val issueId = json.getString("questionIssueId")
                if (issue.isNotEmpty() && issueId.isNotEmpty()) {
                    dailyViewModel.reportIssue(json)
                    progressLoader?.smoothToShow()
                } else {
                    Toast.makeText(applicationContext,
                        "Nothing to report", Toast.LENGTH_SHORT).show()
                }
            }

        }
    }

    override fun onPause() {
        Wonderslate.APP_STATE = Lifecycle.Event.ON_PAUSE
        try {
            super.onPause()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onQuizQuestionIssueReported(reports: String) {
        reportQuizIssue(JSONObject(reports))
    }

    override fun onAudio(type: JSAudio, looping: Boolean) {}

    override fun onAudioChange(isSet: Boolean) {}

    override fun onBotRestart(nextChallenger: String, nextchallengerPlace: String) {}

    override fun onBotPlay(mode: BotPlay) {}

    override fun onOnlineGame(mode: String) {}

    override fun onStoringLastOpponent(opponentName: String, id: String) {}

    override fun onPeerAccessCode(
        quizId: String?,
        resId: String?,
        quizMode: String,
        connectionCode: String
    ) {}

    override fun onGameCodeGen(connectionCode: String) {}

    override fun onQuizRecordId(userDetails: String) {}

    override fun onShareGameCode(gameCode: String?) {}

    override fun onQuizExit() {}

    override fun onInternetConnectionLost() {}

    override fun onQuizSubmit() {}

    override fun onSocketConnectionLost() {}

    override fun onSummaryClick(userAnswers: String?, questions: String?) {}

    override fun resizeWebview(resize: Boolean) {}

    override fun summaryBack() {
        exitScreen()
    }

    override fun loadMoreTests(resName: String) {}

    override fun onScrollContainer(value: Boolean) {}
    override fun onSaveQuizData(quizData: String) {}
}