package com.wonderslate.prepjoy.ui.new_book_details

import android.annotation.SuppressLint
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.ColorRes
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.core.os.bundleOf
import androidx.lifecycle.asLiveData
import androidx.recyclerview.widget.GridLayoutManager
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.wonderslate.data.interfaces.WSCallback
import com.wonderslate.data.network.Wonderslate
import com.wonderslate.data.repository.WSBookStore
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.Utils.FlavourHelper
import com.wonderslate.prepjoy.Utils.Utils.disableScreenShot
import com.wonderslate.prepjoy.Utils.Utils.showBottomSnackBar
import com.wonderslate.prepjoy.databinding.FragmentBookDetailsV2Binding
import com.ws.book_details.data.models.BookDetails
import com.ws.book_details.data.models.BookDetailsRequest
import com.ws.book_details.data.models.BookDetailsResponse
import com.ws.book_details.data.models.BookPriceDetail
import com.ws.book_details.data.models.BookResource
import com.ws.book_details.ui.BookDetailsFragViewModel
import com.ws.commons.Status
import com.ws.commons.extensions.isFreeBook
import com.ws.commons.extensions.toFormattedPrice
import com.ws.commons.interfaces.HTMLParser
import com.ws.commons.interfaces.ImageUrlProvider
import com.ws.commons.models.BookDetailsCoverUrlRequest
import com.ws.core_ui.base.BaseFragmentWithListener
import com.ws.core_ui.extensions.getFormattedResTypeAndIcon
import com.ws.core_ui.extensions.hideView
import com.ws.core_ui.extensions.loadImage
import com.ws.core_ui.extensions.showToast
import com.ws.core_ui.extensions.showView
import com.ws.core_ui.extensions.strikeThrough
import com.ws.core_ui.extensions.visibility
import com.ws.core_ui.utils.IntentConstants
import org.json.JSONObject
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import kotlin.math.roundToInt

class BookDetailsFragV2: BaseFragmentWithListener<FragmentBookDetailsV2Binding, BookDetailsFragV2.OnBookDetailsFragV2InteractionListener>() {

    private val viewModel by viewModel<BookDetailsFragViewModel>()

    // Disabled in first release
    //private val deeplinkCreator by inject<DeeplinkCreator>()

    private val htmlParser by inject<HTMLParser>()

    private val imageUrlProvider by inject<ImageUrlProvider>()

    private var fullCoverUrl = ""

    private var bookDetails: BookDetails? = null

    private var txt: String = ""

    private var isEbookSelected: Boolean = false

    private var isTestSeriesSelected: Boolean = false

    private var isPaperbackSelected: Boolean = false

    private var isComboSelected: Boolean = false

    private var isEBookaiSelected: Boolean = false

    private var isBookGPTSelected: Boolean = false

    private var isBookGPTProSelected: Boolean = false

    private var selectedBookType: String = ""

    private var bookType: String = ""

    private var isGPTBook: Boolean = false

    var isUpgradeMode: Boolean = false

    private var upgradePrice: String = ""

    private var isEBookPresent: Boolean = false

    private var isTestSeriesPresent: Boolean = false

    private var isPaperbackPresent: Boolean = false

    private var isComboPresent: Boolean = false

    private val priceObjectMap: HashMap<String, BookPriceDetail> = HashMap()

    private var canSell = true
    private var amazonLink: String = ""
    private var flipkartLink: String = ""

    private var sellPrintBook: Boolean = false
    private var bookOutOfStock: Boolean = false

    private val maxLength = 250

    private val bookContainsAdapter: BookContainsAdapter by lazy {
        BookContainsAdapter(arrayListOf())
    }

    override fun inflateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentBookDetailsV2Binding {
        return FragmentBookDetailsV2Binding.inflate(inflater, container, false)
    }

    override fun initArguments(bundle: Bundle?) {
        bookDetails = bundle?.getSerializable(IntentConstants.PARAM_BOOK_DETAILS) as BookDetails?
    }

    override fun initView() {
        binding?.btnShare?.hideView()
        initHeader()
        showInitialValues()
        initRecyclers()
        initObservers()
        disableScreenShot(activity)
    }


    override fun load() {
        requestBookDetails()
    }

    private fun initHeader() {
        binding?.btnBack?.setOnClickListener {
            listener?.onBackButtonPressed()
        }

        binding?.btnShare?.setOnClickListener {
            bookDetails?.let {
                listener?.onShareButtonClicked(it)
            }
        }
    }

    private fun initRecyclers() {
        binding?.recBookContains?.apply {
            updateRecyclerContainsLayoutManager(3)
            adapter = bookContainsAdapter
        }
    }

    private fun updateRecyclerContainsLayoutManager(spanCount: Int) {
        binding?.recBookContains?.layoutManager = GridLayoutManager(requireContext(), spanCount, GridLayoutManager.VERTICAL, false)
    }

    private fun initObservers() {
        viewModel.bookDetails.asLiveData().observe(this) { data ->
            when (data.responseType) {
                Status.LOADING -> {
                    binding?.noDataLayout?.hideView()
                    binding?.shimmer?.apply {
                        showView()
                        try {
                            startShimmer()
                        } catch (e: Exception) {
                            // Fallback if shimmer method doesn't exist
                        }
                    }
                }

                Status.SUCCESSFUL -> {
                    //binding?.btnShare?.showView()
                    binding?.shimmer?.apply {
                        hideView()
                        try {
                            stopShimmer()
                        } catch (e: Exception) {
                            // Fallback if shimmer method doesn't exist
                        }
                    }
                    data.data?.let { response ->
                        handleBookDetailsResponse(response)
                    }
                }

                Status.ERROR, Status.HTTP_UNAVAILABLE -> {
                    binding?.noDataLayout?.showView()
                    binding?.shimmer?.apply {
                        hideView()
                        try {
                            stopShimmer()
                        } catch (e: Exception) {
                            // Fallback if shimmer method doesn't exist
                        }
                    }
                }

                else -> {}
            }
        }
    }

    private fun requestBookDetails() {
        bookDetails?.let {
            viewModel.getBooksDetails(BookDetailsRequest(it.bookId.toString()))
        } ?: showToast("Problem while loading book details. Please try again.")
    }

    private fun handleBookDetailsResponse(response: BookDetailsResponse) {

        canSell = response.canSell
        sellPrintBook = response.sellPrintBook
        bookOutOfStock = response.currentStock == 0

        bookType = response.bookType

        prepareBookOptions(response)

        bookDetails = BookDetails(
            response.bookId,
            response.title,
            response.coverImage,
            false,
            -1,
            response.publisherName,
            response.price.toString(),
            response.bookDesc ?: "",
            false,
            response.listPrice?.toString() ?: "",
            response.testsListprice?.toString() ?: "",
            if (isUpgradeMode) upgradePrice else response.testsPrice?.toString() ?: "",
            if (isUpgradeMode) "upgrade" else response.bookType,
            response.isbn ?: "",
            language = response.bookLangauge ?: "",
            sellPrintBook = response.sellPrintBook,
            currentStock = response.currentStock,
            bookPriceDtls = response.bookPriceDtls,
            canSell = response.canSell,
            bookExpiry = response.bookExpiry ?: ""
        )

        if (isUpgradeMode) {
            listener?.onAddBookToCart(bookDetails!!)
        }
        else {
            hideUpgradeAnim()
        }

        if (FlavourHelper.isAffiliationEnabled()) {
            getAffiliationPriceDetails()
        }

        //Update basic book details
        updateBasicBookDetails(
            bookId = response.bookId.toString(),
            bookName = response.title,
            coverImage = response.coverImage ?: "",
            publisherName = response.publisherName ?: ""
        )

        binding?.textViewPublisherName?.text = when {
            !response.publisherName.isNullOrBlank() -> response.publisherName
            bookDetails?.publisherName?.isNotBlank() == true -> bookDetails?.publisherName
            else -> getString(R.string.app_name)
        }

        // Show offer and list price if book is not in library
        if(response.inLibrary) {
            binding?.llPrice?.hideView()
            binding?.priceOptionHelpTxt?.hideView()
        } else {
            //Price card parent visibility
            if (!priceObjectMap.containsKey("eBook") && !priceObjectMap.containsKey("testSeries") &&
                !priceObjectMap.containsKey("printbook") && !priceObjectMap.containsKey("combo") && !priceObjectMap.containsKey("bookGPT")) {
                binding?.llPrice?.visibility = View.GONE
            }
            else {
                binding?.llPrice?.visibility = View.VISIBLE
            }

            //Price card visibility
            binding?.eBookParent?.apply {
                visibility(priceObjectMap.containsKey("eBook") && bookType.equals("ebook", ignoreCase = true))
            }

            binding?.testSeriesParent?.apply {
                visibility(priceObjectMap.containsKey("testSeries") && bookType.equals("ebookwithai", ignoreCase = true))
            }

            binding?.paperbackParent?.apply {
                visibility(priceObjectMap.containsKey("printbook"))
            }

            binding?.comboParent?.apply {
                visibility(priceObjectMap.containsKey("combo"))
            }

            binding?.ebookaiParent?.apply {
                visibility(
                    priceObjectMap.containsKey("eBook") && (bookType.equals(
                        "ebookwithai",
                        ignoreCase = true
                    ) || bookType.equals("bookGPT", ignoreCase = true)))
            }

            binding?.bookgptParent?.apply { visibility(
                priceObjectMap.containsKey("bookGPT") && bookType.equals(
                    "bookGPT",
                    ignoreCase = true
                )) }

            binding?.bookgptproParent?.apply { visibility(
                priceObjectMap.containsKey("ibookgptpro") && bookType.equals("bookGPT", ignoreCase = true)) }


            //Price card text
            if (priceObjectMap.containsKey("eBook")) {
                binding?.tvOfferPrice?.text = priceObjectMap["eBook"]?.sellPrice?.toFormattedPrice()

                binding?.tvListPrice?.apply {
                    visibility(priceObjectMap["eBook"]?.listPrice != 0.0)
                    text = priceObjectMap["eBook"]?.listPrice?.toFormattedPrice() ?: ""
                    strikeThrough(true)
                }

                binding?.textViewDiscount?.apply {
                    getDiscount(priceObjectMap["eBook"]?.listPrice?.toString(), priceObjectMap["eBook"]?.sellPrice?.toString())?.let {
                        visibility(it.isNotEmpty())
                        text = String.format(
                            "save %s",
                            it
                        )
                    }
                }
            }

            if (priceObjectMap.containsKey("testSeries")) {
                binding?.tvTestSeriesOfferPrice?.text = priceObjectMap["testSeries"]?.sellPrice?.toFormattedPrice()

                binding?.tvTestSeriesListPrice?.apply {
                    visibility(priceObjectMap["testSeries"]?.listPrice != 0.0)
                    text = priceObjectMap["testSeries"]?.listPrice?.toFormattedPrice() ?: ""
                    strikeThrough(true)
                }

                binding?.textViewDiscountTestSeries?.apply {
                    getDiscount(priceObjectMap["testSeries"]?.listPrice?.toString(), priceObjectMap["testSeries"]?.sellPrice?.toString())?.let {
                        visibility(it.isNotEmpty())
                        text = String.format(
                            "save %s",
                            it
                        )
                    }
                }
            }

            if (priceObjectMap.containsKey("printbook") && sellPrintBook) {
                binding?.tvPaperbackOfferPrice?.text = priceObjectMap["printbook"]?.sellPrice?.toFormattedPrice()

                binding?.tvPaperbackListPrice?.apply {
                    visibility(priceObjectMap["printbook"]?.listPrice != 0.0)
                    text = priceObjectMap["printbook"]?.listPrice?.toFormattedPrice() ?: ""
                    strikeThrough(true)
                }

                binding?.textViewDiscountPaperback?.apply {
                    getDiscount(priceObjectMap["printbook"]?.listPrice?.toString(), priceObjectMap["printbook"]?.sellPrice?.toString())?.let {
                        visibility(it.isNotEmpty())
                        text = String.format(
                            "save %s",
                            it
                        )
                    }
                }

                if (bookOutOfStock) {
                    binding?.paperbackTextViewOutofstock?.visibility = View.VISIBLE
                }
                else {
                    binding?.paperbackTextViewOutofstock?.visibility = View.GONE
                }
            }
            else {
                binding?.paperbackParent?.visibility = View.GONE
            }

            if (priceObjectMap.containsKey("combo") && sellPrintBook) {
                binding?.tvComboOfferPrice?.text = priceObjectMap["combo"]?.sellPrice?.toFormattedPrice()

                binding?.tvComboListPrice?.apply {
                    visibility(priceObjectMap["combo"]?.listPrice != 0.0)
                    text = priceObjectMap["combo"]?.listPrice?.toFormattedPrice() ?: ""
                    strikeThrough(true)
                }

                binding?.textViewDiscountCombo?.apply {
                    getDiscount(priceObjectMap["combo"]?.listPrice?.toString(), priceObjectMap["combo"]?.sellPrice?.toString())?.let {
                        visibility(it.isNotEmpty())
                        text = String.format(
                            "save %s",
                            it
                        )
                    }
                }

                if (bookOutOfStock) {
                    binding?.comboTextViewOutofstock?.visibility = View.VISIBLE
                }
                else {
                    binding?.comboTextViewOutofstock?.visibility = View.GONE
                }
            }
            else {
                binding?.comboParent?.visibility = View.GONE
            }

            if (priceObjectMap.containsKey("eBook") && (bookType.equals(
                    "ebookwithai",
                    ignoreCase = true
                ) || bookType.equals("bookGPT", ignoreCase = true))) {
                binding?.ebookaiOfferPrice?.text = priceObjectMap["eBook"]?.sellPrice?.toFormattedPrice()

                binding?.ebookaiListPrice?.apply {
                    visibility(priceObjectMap["eBook"]?.listPrice != 0.0)
                    text = priceObjectMap["eBook"]?.listPrice?.toFormattedPrice() ?: ""
                    strikeThrough(true)
                }

                binding?.ebookaiTextViewDiscount?.apply {
                    getDiscount(priceObjectMap["eBook"]?.listPrice?.toString(), priceObjectMap["eBook"]?.sellPrice?.toString())?.let {
                        visibility(it.isNotEmpty())
                        text = String.format(
                            "save %s",
                            it
                        )
                    }
                }
            }

            if (priceObjectMap.containsKey("bookGPT") && bookType.equals(
                    "bookGPT",
                    ignoreCase = true
                )) {
                binding?.bookgptOfferPrice?.text = priceObjectMap["bookGPT"]?.sellPrice?.toFormattedPrice()

                binding?.bookgptListPrice?.apply {
                    visibility(priceObjectMap["bookGPT"]?.listPrice != 0.0)
                    text = priceObjectMap["bookGPT"]?.listPrice?.toFormattedPrice() ?: ""
                    strikeThrough(true)
                }

                binding?.bookgptTextViewDiscount?.apply {
                    getDiscount(priceObjectMap["bookGPT"]?.listPrice?.toString(), priceObjectMap["bookGPT"]?.sellPrice?.toString())?.let {
                        visibility(it.isNotEmpty())
                        text = String.format(
                            "save %s",
                            it
                        )
                    }
                }
            }

            if (priceObjectMap.containsKey("ibookgptpro") && bookType.equals("bookGPT", ignoreCase = true)) {
                binding?.bookgptproOfferPrice?.text = priceObjectMap["ibookgptpro"]?.sellPrice?.toFormattedPrice()

                binding?.bookgptproListPrice?.apply {
                    visibility(priceObjectMap["ibookgptpro"]?.listPrice != 0.0)
                    text = priceObjectMap["ibookgptpro"]?.listPrice?.toFormattedPrice() ?: ""
                    strikeThrough(true)
                }

                binding?.bookgptproTextViewDiscount?.apply {
                    getDiscount(priceObjectMap["ibookgptpro"]?.listPrice?.toString(), priceObjectMap["ibookgptpro"]?.sellPrice?.toString())?.let {
                        visibility(it.isNotEmpty())
                        text = String.format(
                            "save %s",
                            it
                        )
                    }
                }
            }


            //Price card clicks
            binding?.eBookPriceHolder?.setOnClickListener {
                if (canSell) {
                    binding?.eBookPriceHolder?.setBackgroundResource(R.drawable.test_series_price_bg)
                    isEbookSelected = true
                    isTestSeriesSelected = false
                    isPaperbackSelected = false
                    isComboSelected = false
                    selectedBookType = "eBook"
                    bookDetails?.bookType = selectedBookType
                    binding?.buyBtn?.setBackgroundResource(R.drawable.button_background_filled)
                    binding?.testSeriesPriceHolder?.setBackgroundColor(
                        ResourcesCompat.getColor(
                            resources,
                            R.color.white, null
                        ))
                    binding?.paperbackPriceHolder?.setBackgroundColor(
                        ResourcesCompat.getColor(
                            resources,
                            R.color.white, null
                        ))
                    binding?.comboPriceHolder?.setBackgroundColor(
                        ResourcesCompat.getColor(
                            resources,
                            R.color.white, null
                        ))
                }
            }

            binding?.testSeriesPriceHolder?.setOnClickListener {
                if (canSell) {
                    binding?.testSeriesPriceHolder?.setBackgroundResource(R.drawable.test_series_price_bg)
                    isEbookSelected = false
                    isTestSeriesSelected = true
                    isPaperbackSelected = false
                    isComboSelected = false
                    selectedBookType = "testSeries"
                    bookDetails?.bookType = selectedBookType
                    binding?.buyBtn?.setBackgroundResource(R.drawable.button_background_filled)
                    binding?.eBookPriceHolder?.setBackgroundColor(
                        ResourcesCompat.getColor(
                            resources,
                            R.color.white, null
                        ))
                    binding?.paperbackPriceHolder?.setBackgroundColor(
                        ResourcesCompat.getColor(
                            resources,
                            R.color.white, null
                        ))
                    binding?.comboPriceHolder?.setBackgroundColor(
                        ResourcesCompat.getColor(
                            resources,
                            R.color.white, null
                        ))
                }
            }

            binding?.paperbackPriceHolder?.setOnClickListener {
                if (!bookOutOfStock) {
                    binding?.paperbackPriceHolder?.setBackgroundResource(R.drawable.test_series_price_bg)
                    isEbookSelected = false
                    isTestSeriesSelected = false
                    isPaperbackSelected = true
                    isComboSelected = false
                    selectedBookType = "printbook"
                    bookDetails?.bookType = selectedBookType
                    binding?.buyBtn?.setBackgroundResource(R.drawable.button_background_filled)
                    binding?.eBookPriceHolder?.setBackgroundColor(
                        ResourcesCompat.getColor(
                            resources,
                            R.color.white, null
                        ))
                    binding?.testSeriesPriceHolder?.setBackgroundColor(
                        ResourcesCompat.getColor(
                            resources,
                            R.color.white, null
                        ))
                    binding?.comboPriceHolder?.setBackgroundColor(
                        ResourcesCompat.getColor(
                            resources,
                            R.color.white, null
                        ))
                }
            }

            binding?.comboPriceHolder?.setOnClickListener {
                if (!bookOutOfStock) {
                    binding?.comboPriceHolder?.setBackgroundResource(R.drawable.test_series_price_bg)
                    isEbookSelected = false
                    isTestSeriesSelected = false
                    isComboSelected = true
                    isPaperbackSelected = false
                    selectedBookType = "combo"
                    bookDetails?.bookType = selectedBookType
                    binding?.buyBtn?.setBackgroundResource(R.drawable.button_background_filled)
                    binding?.eBookPriceHolder?.setBackgroundColor(
                        ResourcesCompat.getColor(
                            resources,
                            R.color.white, null
                        ))
                    binding?.testSeriesPriceHolder?.setBackgroundColor(
                        ResourcesCompat.getColor(
                            resources,
                            R.color.white, null
                        ))
                    binding?.paperbackPriceHolder?.setBackgroundColor(
                        ResourcesCompat.getColor(
                            resources,
                            R.color.white, null
                        ))
                }
            }

            binding?.ebookaiPriceHolder?.setOnClickListener {
                binding?.ebookaiPriceHolder?.setBackgroundResource(R.drawable.test_series_price_bg)
                isEbookSelected = false
                isTestSeriesSelected = false
                isComboSelected = false
                isPaperbackSelected = false
                isEBookaiSelected = true
                isBookGPTSelected = false
                isBookGPTProSelected = false
                selectedBookType = "eBookWithAI"
                bookDetails?.bookType = selectedBookType
                binding?.buyBtn?.setBackgroundResource(R.drawable.button_background_filled)
                binding?.eBookPriceHolder?.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources,
                        R.color.white, null
                    ))
                binding?.testSeriesPriceHolder?.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources,
                        R.color.white, null
                    ))
                binding?.paperbackPriceHolder?.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources,
                        R.color.white, null
                    ))

                binding?.bookgptPriceHolder?.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources,
                        R.color.white, null
                    ))

                binding?.bookgptproPriceHolder?.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources,
                        R.color.white, null
                    ))

                binding?.comboPriceHolder?.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources,
                        R.color.white, null
                    ))
            }

            binding?.bookgptPriceHolder?.setOnClickListener {
                binding?.bookgptPriceHolder?.setBackgroundResource(R.drawable.test_series_price_bg)
                isEbookSelected = false
                isTestSeriesSelected = false
                isComboSelected = false
                isPaperbackSelected = false
                isEBookaiSelected = false
                isBookGPTSelected = true
                isBookGPTProSelected = false
                selectedBookType = "bookGPT"
                bookDetails?.bookType = selectedBookType
                binding?.buyBtn?.setBackgroundResource(R.drawable.button_background_filled)
                binding?.eBookPriceHolder?.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources,
                        R.color.white, null
                    ))
                binding?.testSeriesPriceHolder?.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources,
                        R.color.white, null
                    ))
                binding?.paperbackPriceHolder?.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources,
                        R.color.white, null
                    ))

                binding?.ebookaiPriceHolder?.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources,
                        R.color.white, null
                    ))

                binding?.bookgptproPriceHolder?.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources,
                        R.color.white, null
                    ))

                binding?.comboPriceHolder?.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources,
                        R.color.white, null
                    ))
            }

            binding?.bookgptproPriceHolder?.setOnClickListener {
                binding?.bookgptproPriceHolder?.setBackgroundResource(R.drawable.test_series_price_bg)
                isEbookSelected = false
                isTestSeriesSelected = false
                isComboSelected = false
                isPaperbackSelected = false
                isEBookaiSelected = false
                isBookGPTSelected = false
                isBookGPTProSelected = true
                selectedBookType = "ibookgptpro"
                bookDetails?.bookType = selectedBookType
                binding?.buyBtn?.setBackgroundResource(R.drawable.button_background_filled)
                binding?.eBookPriceHolder?.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources,
                        R.color.white, null
                    ))
                binding?.testSeriesPriceHolder?.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources,
                        R.color.white, null
                    ))
                binding?.paperbackPriceHolder?.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources,
                        R.color.white, null
                    ))

                binding?.ebookaiPriceHolder?.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources,
                        R.color.white, null
                    ))

                binding?.bookgptPriceHolder?.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources,
                        R.color.white, null
                    ))

                binding?.comboPriceHolder?.setBackgroundColor(
                    ResourcesCompat.getColor(
                        resources,
                        R.color.white, null
                    ))
            }

            binding?.amazonAffiliationPriceLayout?.setOnClickListener {
                val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(amazonLink))
                startActivity(browserIntent)
            }

            binding?.flipkartAffiliationPriceLayout?.setOnClickListener {
                val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(flipkartLink))
                startActivity(browserIntent)
            }

            //Auto select price card if only 1 present
            if (priceObjectMap.size == 1 || (priceObjectMap.size == 2 && priceObjectMap.containsKey("upgrade"))) {
                binding?.priceOptionHelpTxt?.hideView()
                if (priceObjectMap.containsKey("eBook")) {
                    binding?.eBookPriceHolder?.performClick()
                }
                if (priceObjectMap.containsKey("testSeries")) {
                    binding?.testSeriesPriceHolder?.performClick()
                }
                if (priceObjectMap.containsKey("printbook")) {
                    binding?.paperbackPriceHolder?.performClick()
                }
                if (priceObjectMap.containsKey("combo")) {
                    binding?.comboPriceHolder?.performClick()
                }
            }
        }

        handleBottomBar(priceObjectMap, response.inLibrary)

        val originalBookDesc = if (response.bookDesc.isNullOrBlank()) {
            "No description available"
        }
        else {
            response.bookDesc!!
        }

        /*binding?.tvBookDesc?.text = if(response.bookDesc.isNullOrBlank()) {
            "No description available"
        } else {
            Html.fromHtml(response.bookDesc!!)
        }*/

        if (originalBookDesc == "No description available") {
            binding?.tvBookDesc?.text = originalBookDesc
            binding?.readMore?.visibility = View.GONE
            binding?.readLess?.visibility = View.GONE
        }
        else {
            if (originalBookDesc.length > maxLength) {
                val truncatedText = response.bookDesc?.substring(0, maxLength)

                binding?.tvBookDesc?.text = Html.fromHtml(truncatedText)
                binding?.readMore?.visibility = View.VISIBLE
                binding?.readLess?.visibility = View.GONE
                binding?.readMore?.setOnClickListener {
                    binding?.tvBookDesc?.text = Html.fromHtml(originalBookDesc)
                    binding?.readMore?.visibility = View.GONE
                    binding?.readLess?.visibility = View.VISIBLE
                }
                binding?.readLess?.setOnClickListener {
                    binding?.tvBookDesc?.text = Html.fromHtml(truncatedText)
                    binding?.readLess?.visibility = View.GONE
                    binding?.readMore?.visibility = View.VISIBLE
                }
            }
            else {
                binding?.tvBookDesc?.text = Html.fromHtml(originalBookDesc)
                binding?.readMore?.visibility = View.GONE
                binding?.readLess?.visibility = View.GONE
            }
        }

        binding?.tvLanguages?.visibility(!response.bookLangauge.isNullOrBlank())
        binding?.tvLanguages?.text = buildString {
            append("Language: ")
            append(response.bookLangauge)
        }


        var containsQuiz = false
        val resources = response.bookResources
            .filter {
                //Hide flashcards(KeyValues)
                it.res_type != "KeyValues"
            }
            .map {
                val textAndIcon = it.res_type.getFormattedResTypeAndIcon()

                var count = it.res_count
                if(textAndIcon.first == "MCQ's") {
                    containsQuiz = true
                    count = response.totalMcqs ?: count
                }

                BookResource(
                    textAndIcon.first,
                    count,
                    ContextCompat.getDrawable(requireContext(), textAndIcon.second)
                )
            }
        if (!containsQuiz) {
            binding?.recBookContains?.visibility = View.GONE
        }
        else {
            binding?.recBookContains?.visibility = View.VISIBLE
        }
        bookContainsAdapter.updateData(resources)

        //Update layout manager of contains recyclerview based on number of resources
        updateRecyclerContainsLayoutManager(getContainsSpanCount(resources.size))

        if (!canSell) {
            binding?.buyBtn?.setBackgroundResource(R.drawable.buy_btn_back)
            binding?.buyBtn?.text = "Available only in India"
        }

        if (!response.bookExpiry.isNullOrEmpty()) {
            binding?.expiryTxt?.text = "Valid till " + response.bookExpiry
        }
        else {
            binding?.expiryTxt?.visibility = View.GONE
        }

    }

    private fun getAffiliationPriceDetails() {
        if (isGPTBook) {
            binding?.affiliationPriceParent?.setVisibility(View.GONE)
        }
        else {
            binding?.affiliationPriceParent?.visibility = View.VISIBLE
        }
        WSBookStore.getAffiliationPrice(bookDetails?.bookId.toString(), object : WSCallback {
            override fun onWSResultSuccess(jsonObject: JSONObject, responseCode: Int) {
                if (jsonObject.optString("status").equals("ok", ignoreCase = true)) {
                    if (jsonObject.optString("amazonLink")
                            .isEmpty() || jsonObject.optString("amazonLink")
                            .equals("null", ignoreCase = true)
                    ) {
                        binding?.amazonAffiliationPriceLayout?.visibility = View.GONE
                    } else {
                        amazonLink = jsonObject.optString("amazonLink")
                        if (jsonObject.optString("amazonPrice")
                                .isEmpty() || jsonObject.optString("amazonPrice")
                                .equals("null", ignoreCase = true)
                        ) {
                            binding?.textViewAmazonPrice?.text = "Buy"
                        } else {
                            binding?.textViewAmazonPrice?.text = String.format(
                                "₹%s",
                                getParsedPrice(jsonObject.optString("amazonPrice"))
                            )
                        }
                        binding?.amazonAffiliationPriceLayout?.visibility = View.VISIBLE
                    }
                    if (jsonObject.optString("flipkartLink")
                            .isEmpty() || jsonObject.optString("flipkartLink")
                            .equals("null", ignoreCase = true)
                    ) {
                        binding?.flipkartAffiliationPriceLayout?.visibility = View.GONE
                    } else {
                        flipkartLink = jsonObject.optString("flipkartLink")
                        if (jsonObject.optString("flipkartPrice").isEmpty() || jsonObject.optString(
                                "flipkartPrice"
                            ).equals("null", ignoreCase = true)
                        ) {
                            binding?.textViewFlipkartPrice?.text = "Buy"
                        } else {
                            binding?.textViewFlipkartPrice?.text = String.format(
                                "₹%s",
                                getParsedPrice(jsonObject.optString("flipkartPrice"))
                            )
                        }
                        binding?.flipkartAffiliationPriceLayout?.visibility = View.VISIBLE
                    }
                    if ((jsonObject.optString("amazonLink")
                            .isEmpty() || jsonObject.optString("amazonLink")
                            .equals("null", ignoreCase = true))
                        && (jsonObject.optString("flipkartLink")
                            .isEmpty() || jsonObject.optString("flipkartLink")
                            .equals("null", ignoreCase = true))
                    ) {
                        binding?.affiliationPriceParent?.visibility = View.GONE
                    }
                } else {
                    binding?.affiliationPriceParent?.visibility = View.GONE
                }
            }

            override fun onWSResultFailed(resString: String?, responseCode: Int) {
                //Handle failed cases
                binding?.affiliationPriceParent?.visibility = View.GONE
            }
        })
    }

    private fun getParsedPrice(price: String): String? {
        var price = price
        if (price.contains(".0") || price.contains(".00")) {
            price = price.substring(0, price.indexOf("."))
        }
        return price
    }

    private fun prepareBookOptions(response: BookDetailsResponse) {
        priceObjectMap.clear()
        for (item in response.bookPriceDtls) {
            priceObjectMap[item.bookType] = item.sellPrice
        }

        if (priceObjectMap.containsKey("bookGPT") && bookType.equals("bookGPT", ignoreCase = true))
            isGPTBook = true
    }

    /* Calculating the discount percentage. */
    private fun getDiscount(listPrice: String?, offerPrice: String?): String? {
        if (listPrice == null || offerPrice == null) {
            return ""
        }
        return if (listPrice.isEmpty() && offerPrice.isEmpty()) {
            ""
        } else if (listPrice.isNotEmpty() && offerPrice.isEmpty()) {
            ""
        } else if (listPrice.isEmpty()) {
            ""
        } else {
            val discount: Float
            val listPriceInt = listPrice.toFloat()
            val offerPriceInt = offerPrice.toFloat()
            if (listPriceInt > 100) {
                discount = listPriceInt - offerPriceInt
                "Rs " + discount.toInt()
            } else {
                discount = (listPriceInt - offerPriceInt) / listPriceInt * 100
                if (discount.isNaN()) {
                    ""
                }
                else {
                    val finalDiscount = discount.roundToInt()
                    if (finalDiscount == 0) {
                        ""
                    } else {
                        "$finalDiscount%"
                    }
                }
            }
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun handleBottomBar(
        priceMap: HashMap<String, BookPriceDetail>,
        isInLibrary: Boolean
    ) {
        binding?.buyPreviewBtnLayout?.showView()
        binding?.buyPreviewBtnLayout?.apply {
            binding?.buyBtn?.text = if (priceMap["eBook"]?.sellPrice?.isFreeBook() == true) TEXT_ADD_TO_LIBRARY else TEXT_BUY_NOW
            txt = TEXT_OPEN
            if(isInLibrary) {
                binding?.buyPreviewBtnLayout?.visibility = View.GONE
                if (isUpgradeMode) {
                    binding?.openInLibraryBtn?.visibility = View.GONE
                }
                else {
                    binding?.openInLibraryBtn?.visibility = View.VISIBLE
                }
                binding?.openInLibraryBtn?.setOnClickListener {
                    txt = TEXT_OPEN
                    handleTabClick(txt)
                }
                binding?.buyBtn?.text = txt
                binding?.buyBtn?.setOnClickListener {
                    txt = TEXT_OPEN
                    handleTabClick(txt)
                }
            } else {
                if (!priceObjectMap.containsKey("eBook") && !priceObjectMap.containsKey("testSeries") && !priceObjectMap.containsKey("printbook")) {
                    binding?.buyPreviewBtnLayout?.visibility = View.GONE
                }
                else {
                    binding?.buyPreviewBtnLayout?.visibility = View.VISIBLE
                }

                if (!priceObjectMap.containsKey("eBook") && !priceObjectMap.containsKey("testSeries")) {
                    binding?.previewBtn?.visibility = View.GONE
                }
                else {
                    binding?.previewBtn?.visibility = View.VISIBLE
                }
                binding?.openInLibraryBtn?.visibility = View.GONE
                txt = TEXT_PREVIEW
                binding?.previewBtn?.text = txt
                binding?.previewBtn?.setOnClickListener {
                    txt = TEXT_PREVIEW
                    handleTabClick(txt)
                }

                txt = if (isEbookSelected || isTestSeriesSelected || isPaperbackSelected || isComboSelected) {
                    if (priceMap["eBook"]?.sellPrice?.isFreeBook() == true) TEXT_ADD_TO_LIBRARY else TEXT_BUY_NOW
                } else {
                    if (priceMap["eBook"]?.sellPrice?.isFreeBook() == true) TEXT_ADD_TO_LIBRARY else TEXT_BUY_NOW
                }
                binding?.buyBtn?.text = txt
                binding?.buyBtn?.setOnClickListener {
                    if (binding?.buyBtn?.text?.equals(TEXT_BUY_NOW) == true) {
                        txt = TEXT_BUY_NOW
                    }
                    if (isEbookSelected || isTestSeriesSelected || isPaperbackSelected || isComboSelected
                        || isBookGPTSelected || isEBookaiSelected || isBookGPTProSelected) {
                        handleTabClick(txt)
                    }
                    else {
                        showBottomSnackBar("Please select an option to purchase", requireActivity())
                    }

                }
            }
        }
    }

    private fun handleTabClick(tabString: String) {
        bookDetails?.let { details ->
            when (tabString) {
                TEXT_OPEN -> {
                    bookDetails?.bookType = bookType
                    listener?.onOpenInLibrary(details)
                }
                TEXT_PREVIEW -> {
                    bookDetails?.bookType = bookType
                    listener?.onPreviewBook(details)
                }
                TEXT_ADD_TO_LIBRARY -> {
                    bookDetails?.bookType = bookType
                    listener?.onAddBookToLibrary(details)
                }
                TEXT_BUY_NOW -> listener?.onAddBookToCart(details)
                else -> {}
            }
        }
    }

    private fun getContainsSpanCount(size: Int): Int {
        return when {
            size < 1 -> 1
            size >= 3 -> 3
            else -> size
        }
    }

    private fun showInitialValues() {
        if (bookDetails != null && bookDetails?.bookType != null && bookDetails?.bookType!!.isNotEmpty()
            && bookDetails?.bookType!!.equals("upgrade", true)) {
            if (!Wonderslate.getInstance().sharedPrefs.testSeriesPurchaseStatus) {
                isUpgradeMode = true
                upgradePrice = bookDetails?.testsPrice.toString()
                showUpgradeAnim()
            }
            else {
                isUpgradeMode = false
                updateBasicBookDetails(
                    bookDetails?.bookId.toString(),
                    bookDetails?.coverImage ?: "",
                    bookDetails?.bookName ?: "",
                    bookDetails?.publisherName ?: ""
                )
            }
        }
        else {
            updateBasicBookDetails(
                bookDetails?.bookId.toString(),
                bookDetails?.coverImage ?: "",
                bookDetails?.bookName ?: "",
                bookDetails?.publisherName ?: ""
            )
        }
    }


    /**
     * `showUpgradeAnim()` is a private function that sets the visibility of the upgrade loader to
     * visible, the visibility of the buy preview button layout to gone, the visibility of the linear
     * layout to gone, and the visibility of the book details parent to gone
     */
    private fun showUpgradeAnim() {
        binding?.upgradeLoader?.visibility = View.VISIBLE
        binding?.buyPreviewBtnLayout?.visibility = View.GONE
        binding?.linearLayout?.visibility = View.GONE
        binding?.bookDetailsParent?.visibility = View.GONE
        binding?.openInLibraryBtn?.visibility = View.GONE
    }


    /**
     * `hideUpgradeAnim()` hides the upgrade loader and shows the buy preview button layout, linear
     * layout and book details parent
     */
    private fun hideUpgradeAnim() {
        isUpgradeMode = false
        binding?.upgradeLoader?.visibility = View.GONE
        binding?.buyPreviewBtnLayout?.visibility = View.VISIBLE
        binding?.linearLayout?.visibility = View.VISIBLE
        binding?.bookDetailsParent?.visibility = View.VISIBLE
    }

    private fun updateBasicBookDetails(
        bookId: String,
        coverImage: String,
        bookName: String,
        publisherName: String
    ) {
        binding?.apply {
            fullCoverUrl = if (coverImage.startsWith("https://")) {
                coverImage
            } else {
                imageUrlProvider.getBookDetailsCoverUrl(BookDetailsCoverUrlRequest(
                        fileName = coverImage,
                        bookId = bookId
                ))
            }
            ivBookCover.loadImage(fullCoverUrl) {
                transition(DrawableTransitionOptions.withCrossFade())
            }
            tvBookTitle.text = bookName
            textViewPublisherName.text = let {
                if(publisherName.isBlank())
                    getString(R.string.app_name)
                else
                    publisherName
            }
        }
    }

    private fun getCustomText(text: String, @ColorRes color: Int = R.color.black): TextView {
        return (LayoutInflater.from(requireContext()).inflate(R.layout.custom_tab_text,null) as TextView).also { tv ->
            tv.setTextColor(ContextCompat.getColor(requireContext(), color))
            tv.text = text
        }
    }

    /* This class is an interface that defines the methods that the BookDetailsFragmentV2 class will
    use to communicate with the MainActivity class */
    interface OnBookDetailsFragV2InteractionListener {
        fun onBackButtonPressed()
        fun onShareButtonClicked(bookDetails: BookDetails)
        fun onOpenInLibrary(bookDetails: BookDetails)
        fun onPreviewBook(bookDetails: BookDetails)
        fun onAddBookToLibrary(bookDetails: BookDetails)
        fun onAddBookToCart(bookDetails: BookDetails)
        fun onBookAddedByAccessCode(bookDetails: BookDetails)
        fun onBookDetailsChanged(bookDetails: BookDetails)
    }

    companion object {

        private const val TEXT_OPEN = "Open"
        private const val TEXT_BUY_NOW = "Add to Cart"
        private const val TEXT_ADD_TO_LIBRARY = "Add to library"
        private const val TEXT_PREVIEW = "Open Book"
        @JvmStatic
        fun newInstance(bookDetails: BookDetails) = BookDetailsFragV2().also {
            it.arguments = bundleOf(
                IntentConstants.PARAM_BOOK_DETAILS to bookDetails
            )
        }
    }
}

