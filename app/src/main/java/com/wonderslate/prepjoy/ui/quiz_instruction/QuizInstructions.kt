package com.wonderslate.prepjoy.ui.quiz_instruction

import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.Spinner
import android.widget.Switch
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import android.widget.ProgressBar
import com.wonderslate.commons.Data
import com.wonderslate.commons.Status
import com.wonderslate.data.network.Wonderslate
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.Utils.GenerateDates
import com.wonderslate.prepjoy.Utils.Utils
import com.wonderslate.prepjoy.Utils.Utils.disableScreenShot
import com.wonderslate.prepjoy.ui.BaseActivity
import com.wonderslate.prepjoy.ui.dashboard.DashBoardActivity
import com.wonderslate.prepjoy.ui.dashboard.DashBoardViewModel
import com.wonderslate.prepjoy.ui.home.HomeViewModel
import com.wonderslate.prepjoy.ui.login.LoginActivity
import com.wonderslate.prepjoy.ui.quiz.QuizActivity
import com.ws.commons.enums.EventType
import com.ws.commons.interfaces.EventLogger
import com.ws.commons.models.Event
// import kotlinx.android.synthetic.main.activity_quiz_instuctions.*
// import kotlinx.android.synthetic.main.header_language_change.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONException
import org.json.JSONObject
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.*

class QuizInstructions : BaseActivity() {
    private lateinit var firebaseAnalytics: FirebaseAnalytics

    var context : Context? =  null
    var quizid : String? = null
    var language1 : String? = null
    var language2 : String? = null
    var selectedDate : String? =  null
    var selectedDuration : String? =  null
    var previosVal : String? = "yes"
    var nextVal : String? =  "yes"
    private var quizType: String? = null
    private var multiQuizData: String? = null
    private var challengerName: String? = null
    private var challengerPlace: String? = null
    private var realDailyTestDtlId : String? = null
    private var noOfQuestions: String? = null
    private var noOfDays: String? = null
    private var selectedWeek: String? = null
    private var selectedMonth: String? = null
    private var selectedTestId: String? = null
    private var selectedTestMode: String? = null
    private var selectedTestDate: String? = null
    private var isFromShop: Boolean? = false
    private var isFromDeepLink: Boolean? = false
    private var bookId: String = ""
    private var bookTitle: String = ""
    private var bookPublisher: String = ""
    private var bookPublisherId: String = ""
    var selectedQuizMode: String? = "play"
    val viewModel by viewModel<HomeViewModel>()
    val dashboardViewModel by inject<DashBoardViewModel>()
    private val eventLogger by inject<EventLogger>()
    var progressLoader: ProgressBar? = null
    private lateinit var quizInstructionHolder: View
    private lateinit var btnStart: View
    private lateinit var btnChallengeFriend: android.widget.Button
    private lateinit var btnChallenge: View
    private lateinit var spinnerlanguage: Spinner
    private lateinit var spinner_language_text: View
    private lateinit var spinnertime: Spinner
    private lateinit var switchgamesound: Switch
    private lateinit var toggleLanguage: View
    private lateinit var rrltBack: View

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Wonderslate.aClass = javaClass
        try {
            context = this
            if (supportActionBar != null) {
                supportActionBar!!.hide()
            }
            isFromShop = intent.getBooleanExtra("isFromShop", false).takeIf {
                intent.hasExtra("isFromShop")
            }
            isFromDeepLink = intent.getBooleanExtra("isDeepLink", false).takeIf {
                intent.hasExtra("isDeepLink")
            }
            bookId = intent.getStringExtra("bookId").takeIf {
                intent.hasExtra("bookId")
            }.toString()
            bookTitle = intent.getStringExtra("bookTitle").takeIf {
                intent.hasExtra("bookTitle")
            }.toString()
            bookPublisher = intent.getStringExtra("publisher").takeIf {
                intent.hasExtra("publisher")
            }.toString()
            bookPublisherId = intent.getStringExtra("publisherId").takeIf {
                intent.hasExtra("publisherId")
            }.toString()
            quizid = intent.getStringExtra("quizId").takeIf { intent.hasExtra("quizId") }

            // Only initialize observer if we don't have quiz data passed directly
            val passedQuizData = intent.getStringExtra("quizData")
            Log.d("QuizInstructions", "=== ONCREATE ===")
            Log.d("QuizInstructions", "passedQuizData: ${passedQuizData?.length ?: 0} characters")

            if (passedQuizData == null || passedQuizData.isEmpty()) {
                Log.d("QuizInstructions", "No passed quiz data, initializing observer")
                try {
                    initObserver()
                } catch (e: Exception) {
                    Log.e("QuizInstructions", "Error initializing observer", e)
                    // Continue without observer - we'll handle quiz data directly
                }
            } else {
                Log.d("QuizInstructions", "Quiz data passed directly, skipping observer initialization")
            }

            language1 = intent.getStringExtra("language1").takeIf {
                intent.hasExtra("language1")
            }
            language2 = intent.getStringExtra("language2").takeIf {
                intent.hasExtra("language2")
            }
            val defLanguages = resources.getStringArray(R.array.quiz_instruction_languages)
            if (!defLanguages.contains(language1)) {
                language1 = ""
            }
            if (!defLanguages.contains(language2)) {
                language2 = ""
            }
            if (isFromShop == true) {
                Log.d("QuizInstructions", "=== isFromShop == true ===")
                Log.d("QuizInstructions", "quizid: $quizid")

                // Check if we already have quizData passed from ResourceInputActivity
                val passedQuizData = intent.getStringExtra("quizData")
                Log.d("QuizInstructions", "passedQuizData: ${passedQuizData?.length ?: 0} characters")

                if (passedQuizData != null && passedQuizData.isNotEmpty()) {
                    Log.d("QuizInstructions", "Using passed quiz data directly")
                    multiQuizData = passedQuizData
                    init()
                } else {
                    Log.d("QuizInstructions", "No passed quiz data, checking cache")
                    CoroutineScope(Dispatchers.IO).launch {
                        val cachedData = quizid?.let { dashboardViewModel.getQuizData(it) }
                        withContext(Dispatchers.Main) {
                            if (cachedData != null) {
                                Log.d("QuizInstructions", "Using cached data")
                                language1 = cachedData.language1
                                language2 = cachedData.language2
                                multiQuizData = cachedData.quizData
                                init()
                            } else {
                                Log.d("QuizInstructions", "No cached data, calling API")
                                progressLoader = findViewById(R.id.progressLoader)
                                progressLoader?.visibility = View.VISIBLE
                                quizid?.let { viewModel.getQuizQuestionsAnswers(it) }
                            }
                        }
                    }
                }
            } else {
                Log.d("QuizInstructions", "=== isFromShop == false ===")
                init()
            }

        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }
        disableScreenShot(this)
    }
    fun init()
    {
        try {
            Log.d("QuizInstructions", "=== INIT CALLED ===")
            Log.d("QuizInstructions", "language1: $language1, language2: $language2")

            // Initialize views first to avoid null pointer exceptions
            initializeViews()

            selectedDate = intent.getStringExtra("selectedDate").takeIf {
                intent.hasExtra("selectedDate")
            }
            selectedQuizMode = intent.getStringExtra("selectedFormat").takeIf {
                intent.hasExtra("selectedFormat")
            }
            Log.d("QuizInstructions", "selectedQuizMode: $selectedQuizMode")

            selectedTestId = intent.getStringExtra("testId").
            takeIf { intent.hasExtra("testId") }
            selectedTestMode = intent.getStringExtra("testMode").takeIf {
                intent.hasExtra("testMode")
            }
            selectedTestDate = intent.getStringExtra("testDate").takeIf {
                intent.hasExtra("testDate")
            }

            getMultiQuizData()
            Log.d("QuizInstructions", "multiQuizData length: ${multiQuizData?.length ?: 0}")

            val isPractice = (null != selectedTestMode && selectedTestMode?.isNotEmpty() == true) ||
                    (null != selectedQuizMode && selectedQuizMode?.isNotEmpty() == true)
            Log.d("QuizInstructions", "isPractice: $isPractice")
            Log.d("QuizInstructions", "selectedTestMode: $selectedTestMode")
            Log.d("QuizInstructions", "selectedQuizMode: $selectedQuizMode")

            if (isPractice) {
                Log.d("QuizInstructions", "Going directly to quiz (practice mode)")
                quizInstructionHolder.visibility = View.GONE
                proceedToQuiz()
            } else {
                Log.d("QuizInstructions", "Showing quiz instructions")
                quizInstructionHolder.visibility = View.VISIBLE
                firebaseAnalytics = Firebase.analytics
                initViews()
            }
        } catch (e: Exception) {
            Log.e("QuizInstructions", "Error in init()", e)
            e.printStackTrace()
        }
    }

    private fun initializeViews() {
        try {
            Log.d("QuizInstructions", "Initializing views")
            quizInstructionHolder = findViewById(R.id.quizInstructionHolder)
            Log.d("QuizInstructions", "quizInstructionHolder initialized: ${::quizInstructionHolder.isInitialized}")
        } catch (e: Exception) {
            Log.e("QuizInstructions", "Error initializing views", e)
            e.printStackTrace()
        }
    }

    override fun getLayoutResource(): Int {
        return R.layout.activity_quiz_instuctions
    }


    private fun getMultiQuizData() {
        Log.d("QuizInstructions", "=== getMultiQuizData ===")
        Log.d("QuizInstructions", "intent.hasExtra('quizData'): ${intent.hasExtra("quizData")}")

        multiQuizData = intent.getStringExtra("quizData").
        takeIf { intent.hasExtra("quizData") }
        Log.d("QuizInstructions", "multiQuizData set to: ${multiQuizData?.length ?: 0} characters")

        quizType = intent.getStringExtra("quizType").
        takeIf { intent.hasExtra("quizType") }
        challengerName = intent.getStringExtra("challengerName").takeIf {
            intent.hasExtra("challengerName")
        }
        challengerPlace = intent.getStringExtra("challengerPlace").takeIf {
            intent.hasExtra("challengerPlace")
        }
        realDailyTestDtlId  = intent.getStringExtra("realDailyTestDtlId").takeIf {
            intent.hasExtra("realDailyTestDtlId")
        }
        noOfQuestions = intent.getStringExtra("noOfQuestions").takeIf {
            intent.hasExtra("noOfQuestions")
        }
        noOfDays = intent.getStringExtra("noOfDays").takeIf {
            intent.hasExtra("noOfDays")
        }
        selectedWeek = intent.getStringExtra("selectedWeek").takeIf {
            intent.hasExtra("selectedWeek")
        }
        selectedMonth = intent.getStringExtra("selectedMonth").takeIf {
            intent.hasExtra("selectedMonth")
        }
    }

    private fun initObserver() {
        viewModel.quizQuestionAnswers.observe(this, ::quizQuestionAnswersResponse)
    }
    private fun initViews() {
        try {
            // Initialize views
            quizInstructionHolder = findViewById(R.id.quizInstructionHolder)
            btnStart = findViewById(R.id.btnStart)
            btnChallengeFriend = findViewById(R.id.btnChallengeFriend)
            btnChallenge = findViewById(R.id.btnChallenge)
            spinnerlanguage = findViewById(R.id.spinnerlanguage)
            spinner_language_text = findViewById(R.id.spinner_language_text)
            spinnertime = findViewById(R.id.spinnertime)
            switchgamesound = findViewById(R.id.switchgamesound)
            toggleLanguage = findViewById(R.id.toggleLanguage)
            rrltBack = findViewById(R.id.rrltBack)

            val wonderPubSharedPrefs = WonderPubSharedPrefs.getInstance(context)

            btnStart.setOnClickListener {
                eventLogger.logEvent(
                    Event(
                        type = EventType.BOT_GAME_START,
                        currentScreen = QuizInstructions::class.simpleName
                    )
                )
                proceedToQuiz()
            }
                val lastUser = wonderPubSharedPrefs.lastUserOnline
                if (lastUser.isNotEmpty()) {
                    val name = lastUser.split("*")[0]
                    val id = lastUser.split("*")[1]
                    btnChallengeFriend.visibility = View.VISIBLE
                    btnChallengeFriend.text = "Challenge $name"
                    btnChallengeFriend.setOnClickListener {
                        eventLogger.logEvent(
                            Event(
                                type = EventType.ONLINE_GAME_START,
                                currentScreen = QuizInstructions::class.simpleName,
                                value = Pair("opponent", name)
                            )
                        )
                        prepareForQuiz()
                        val challengeIntent = intentForQuiz().also {
                            it.putExtra("challenge", true)
                            it.putExtra("lastUser", id)
                            if (intent.hasExtra("bookId")) {
                                it.putExtra("bookId", intent.getStringExtra("bookId"))
                            }
                            it.putExtra("onlineChallenge", true)
                        }
                        startActivity(challengeIntent)
                        finish()
                    }
                }
                btnChallenge.setOnClickListener {
                    eventLogger.logEvent(Event(
                        type = EventType.CODE_GAME_START,
                        currentScreen = QuizInstructions::class.simpleName
                    ))
                    prepareForQuiz()
                    val challengeIntent = intentForQuiz().also {
                        it.putExtra("challenge", true)
                    }
                    startActivity(challengeIntent)
                    finish()
                }
            val languages: MutableList<String?> = ArrayList()
            if (!language1?.isEmpty()!! && !language1.equals("null")) {
                languages.add(0, language1)

            }
            if (!language2?.isEmpty()!! && !language2.equals("null"))
                languages.add(1, language2)

            val languageSpinnerAdapter: ArrayAdapter<*> = ArrayAdapter<Any?>(
                    this,
                    R.layout.spinner_row_noting_lang,
                    languages as List<Any?>
            )
            languageSpinnerAdapter.setDropDownViewResource(R.layout.spinner_textview)
            spinnerlanguage.adapter = languageSpinnerAdapter

            if (languages.isEmpty()) {
                spinner_language_text.visibility = View.GONE
                spinnerlanguage.visibility = View.GONE
            }
            spinnerlanguage.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
                override fun onItemSelected(
                        parent: AdapterView<*>?,
                        view: View,
                        position: Int,
                        id: Long
                ) {
                    wonderPubSharedPrefs.sharedPrefsContentLanguagePref = languages[position]
                    val bundle = Bundle()
                    bundle.putString("activity_name", "QuizInstructions")
                    bundle.putString("activity_method_name", "QuizInstructions language")
                    firebaseAnalytics.logEvent("QuizInstructions_language", bundle)
                }

                override fun onNothingSelected(parent: AdapterView<*>?) {}
            }
            val contentLang = wonderPubSharedPrefs.sharedPrefsContentLanguagePref
            if (contentLang.isNotEmpty()) {
                if (languages.contains(contentLang)) {
                    wonderPubSharedPrefs.sharedPrefsContentLanguagePref = contentLang
                    spinnerlanguage.setSelection(languages.indexOf(contentLang))
                    /*if (! WonderPubSharedPrefs.getInstance(mContext).getUserDistrict().isEmpty()) {
                    selectedDistrict =  WonderPubSharedPrefs.getInstance(mContext).getUserDistrict()
                    districtAdapter.clear()
                    districtList.addAll(statesData!!.getDistricts(selectedState))
                    districtAdapter.notifyDataSetChanged()
                    spinnercity.setSelection(districtList.indexOf(selectedDistrict))
                }*/
                }
            }

            val gameDuration: MutableList<String?> = ArrayList()
            gameDuration.add("15")
            gameDuration.add("30")
            gameDuration.add("45")
            gameDuration.add("60")

            val gameDurationSpinnerAdapter: ArrayAdapter<*> = ArrayAdapter<Any?>(
                    this,
                    R.layout.spinner_row_noting_lang,
                    gameDuration as List<Any?>
            )
            gameDurationSpinnerAdapter.setDropDownViewResource(R.layout.spinner_textview)
            spinnertime.adapter = gameDurationSpinnerAdapter

            spinnertime.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
                override fun onItemSelected(
                        parent: AdapterView<*>?,
                        view: View?,
                        position: Int,
                        id: Long
                ) {

                    val gametime = gameDuration[position]?.toInt()
                    wonderPubSharedPrefs.sharedPrefsQuizDurationPref = gametime.toString()
                    val bundle = Bundle()
                    bundle.putString("activity_name", "QuizInstructions")
                    bundle.putString("activity_method_name", "QuizInstructions Timeout")
                    firebaseAnalytics.logEvent("QuizInstructions_timeout", bundle)
                }

                override fun onNothingSelected(parent: AdapterView<*>?) {}
            }
            val quizDuration = wonderPubSharedPrefs.sharedPrefsQuizDurationPref
            if (quizDuration.isNotEmpty())
            {
                val gametime = (quizDuration?.toInt() ?: 30) /100
                if (gameDuration.contains(gametime.toString()))
                {
                    wonderPubSharedPrefs.sharedPrefsQuizDurationPref = wonderPubSharedPrefs.sharedPrefsQuizDurationPref
                    spinnertime.setSelection(gameDuration.indexOf(gametime.toString()))
                    /*if (! WonderPubSharedPrefs.getInstance(mContext).getUserDistrict().isEmpty()) {
                    selectedDistrict =  WonderPubSharedPrefs.getInstance(mContext).getUserDistrict()
                    districtAdapter.clear()
                    districtList.addAll(statesData!!.getDistricts(selectedState))
                    districtAdapter.notifyDataSetChanged()
                    spinnercity.setSelection(districtList.indexOf(selectedDistrict))
                }*/
                }
            }

            switchgamesound.setOnCheckedChangeListener { buttonView, isChecked ->
                wonderPubSharedPrefs.sharedPrefsGameSound = isChecked
                val bundle = Bundle()
                bundle.putString("activity_name", "QuizInstructions")
                bundle.putString("activity_method_name", "QuizInstructions sound $isChecked")
                firebaseAnalytics.logEvent("QuizInstructions_language", bundle)
            }
            toggleLanguage.visibility = View.GONE

            rrltBack.setOnClickListener {
                //  onBackPressed()

//                val intent = Intent(context, DashBoardActivity::class.java)
//                (context as QuizInstructions).startActivity(intent)
                if (isFromDeepLink == true) {
                    val dashboard = Intent(
                        this@QuizInstructions,
                        DashBoardActivity::class.java
                    )
                    startActivity(dashboard)
                    finish()
                }
                else {
                    finish()
                }
            }

            switchgamesound.isChecked = wonderPubSharedPrefs.sharedPrefsGameSound

            // WonderPubSharedPrefs.getInstance(this).sharedPrefsGameSound = true
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }
    }

    private fun prepareForQuiz() {
        val wonderPubSharedPrefs = WonderPubSharedPrefs.getInstance(context)
        val startAndEndDate = wonderPubSharedPrefs.datesResponse
        selectedDate?.let {
            val result = GenerateDates.checkifDateExist(it, startAndEndDate)
            previosVal = result.split("-")[0]
            nextVal = result.split("-")[1]
        }
        selectedWeek?.let {
            val result = GenerateDates.validateWeek(it, startAndEndDate)
            previosVal = result.split("-")[0]
            nextVal = result.split("-")[1]
        }
        selectedMonth?.let {
            val result = GenerateDates.validateMonth(it, startAndEndDate)
            previosVal = result.split("-")[0]
            nextVal = result.split("-")[1]
        }
        selectedTestDate?.let {
            val testResponse = wonderPubSharedPrefs.testDatesResponse
            val result = GenerateDates.checkifDateExist(it, testResponse)
            previosVal = result.split("-")[0]
            nextVal = result.split("-")[1]
        }
        // Handle language preference - spinnerlanguage might not be initialized in practice mode
        try {
            if(::spinnerlanguage.isInitialized && spinnerlanguage.selectedItem != null) {
                wonderPubSharedPrefs.sharedPrefsContentLanguagePref = spinnerlanguage.selectedItem.toString()
                Log.d("QuizInstructions", "Language set from spinner: ${spinnerlanguage.selectedItem}")
            } else {
                // Use language1 from intent if available, otherwise default to English
                val languageToUse = if (!language1.isNullOrEmpty() && !language1.equals("null")) {
                    language1!!
                } else {
                    "English"
                }
                wonderPubSharedPrefs.sharedPrefsContentLanguagePref = languageToUse
                Log.d("QuizInstructions", "Language set from intent/default: $languageToUse")
            }
        } catch (e: Exception) {
            Log.e("QuizInstructions", "Error setting language preference", e)
            wonderPubSharedPrefs.sharedPrefsContentLanguagePref = "English"
        }
    }

    private fun proceedToQuiz() {
        Log.d("QuizInstructions", "=== proceedToQuiz ===")
        Log.d("QuizInstructions", "multiQuizData: ${multiQuizData?.length ?: 0} characters")

        if(isNetworkConnected())
        {
            prepareForQuiz()
            val intent = intentForQuiz()
            Log.d("QuizInstructions", "Starting QuizActivity with intent")
            context?.startActivity(intent)
            finish()
        } else {
            Log.e("QuizInstructions", "No internet connection")
            Utils.showTopSnackBar("No internet connection found.", this)
        }
    }

    private fun isNetworkConnected(): Boolean {
        val cm: ConnectivityManager = this.getSystemService(CONNECTIVITY_SERVICE) as ConnectivityManager
        return cm.getActiveNetworkInfo() != null && cm.getActiveNetworkInfo()!!.isConnected()
    }

    private fun intentForQuiz(): Intent {
        Log.d("QuizInstructions", "=== intentForQuiz ===")
        Log.d("QuizInstructions", "Creating intent for QuizActivity")
        Log.d("QuizInstructions", "quizid: $quizid")
        Log.d("QuizInstructions", "multiQuizData: ${multiQuizData?.length ?: 0} characters")
        Log.d("QuizInstructions", "selectedQuizMode: $selectedQuizMode")

        val intent = Intent(context, QuizActivity::class.java)
        quizid.let { intent.putExtra("quizId", it) }
        intent.putExtra("language1", language1)
        intent.putExtra("language2", language2)
        selectedDate?.let { intent.putExtra("selectedDate",it) }
        selectedQuizMode.let{intent.putExtra("selectedFormat",it) }
        multiQuizData?.let {
            intent.putExtra("quizData", it)
            Log.d("QuizInstructions", "Added quizData to intent: ${it.length} characters")
        }
        quizType?.let { intent.putExtra("quizType", it) }
        challengerName?.let { intent.putExtra("challengerName", it) }
        challengerPlace?.let { intent.putExtra("challengerPlace", it) }
        realDailyTestDtlId ?.let { intent.putExtra("realDailyTestDtlId", it) }
        noOfDays?.let { intent.putExtra("noOfDays", it) }
        noOfQuestions?.let { intent.putExtra("noOfQuestions", it) }
        selectedWeek?.let { intent.putExtra("selectedWeek", it) }
        selectedMonth?.let { intent.putExtra("selectedMonth", it) }
        selectedTestId?.let { intent.putExtra("testId", it) }
        selectedTestMode?.let { intent.putExtra("testMode", it) }
        selectedTestDate?.let{ intent.putExtra("testDate", it) }
        intent.putExtra("previous", previosVal)
        intent.putExtra("next",nextVal)
        intent.putExtra("isFromShop",isFromShop)
        intent.putExtra("isFromDeepLink", isFromDeepLink)
        intent.putExtra("bookId", bookId)
        intent.putExtra("publisher", bookPublisher)
        intent.putExtra("publisherId", bookPublisherId)
        intent.putExtra("bookTitle", bookTitle)
        if (getIntent().hasExtra("history")) {
            intent.putExtra("history", true)
        }

        Log.d("QuizInstructions", "Intent created successfully")
        return intent
    }

    override fun onBackPressed() {
        // super.onBackPressed()

//        val intent = Intent(context, DashBoardActivity::class.java)
//        (context as QuizInstructions).startActivity(intent)
        if (isFromDeepLink == true) {
            val dashboard = Intent(
                this@QuizInstructions,
                DashBoardActivity::class.java
            )
            startActivity(dashboard)
        }
        else {
            finish()
        }
    }
    private fun quizQuestionAnswersResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
            }
            Status.SUCCESSFUL -> {
                data.data?.let {
                    try {
                        progressLoader?.visibility = View.GONE
                        language1 =  data.data!!.getString("language1")
                        language2 =  data.data!!.getString("language2")
                        multiQuizData = data.data.toString()
                        init()
                    } catch (e: JSONException) {
                        e.printStackTrace()
                    }
                }
            }
            Status.HTTP_UNAVAILABLE -> {

                try {
                    progressLoader?.visibility = View.GONE
                } catch (e: Exception) {
                    progressLoader?.visibility = View.GONE
                    Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), this)
                    e.printStackTrace()
                }
            }

            Status.ERROR -> {
                try {
                    progressLoader?.visibility = View.GONE
                    if (data.error.toString().contains("401")) {
                        WonderPubSharedPrefs.getInstance(this).clearAllSharePref()
                        val intent = Intent(this, LoginActivity::class.java)
                        startActivity(intent)
                        finish()
                    }
                } catch (e: Exception) {
                    progressLoader?.visibility = View.GONE
                    e.printStackTrace()
                }
            }
        }
    }
}

