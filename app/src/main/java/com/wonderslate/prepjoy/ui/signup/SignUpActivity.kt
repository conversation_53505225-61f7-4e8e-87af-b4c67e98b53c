package com.wonderslate.prepjoy.ui.signup

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.Button
import android.widget.EditText
import android.widget.Spinner
import android.widget.TextView
import androidx.core.view.isVisible
import com.wonderslate.commons.Data
import com.wonderslate.commons.Status
import android.widget.ProgressBar
import com.wonderslate.data.network.WSAPIManager
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.domain.entities.LoginData
import com.wonderslate.domain.entities.SignUpData
import com.wonderslate.domain.entities.UserDetailsData
import com.wonderslate.prepjoy.BuildConfig
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.Utils.FlavourHelper
import com.wonderslate.prepjoy.Utils.Flavours
import com.wonderslate.prepjoy.Utils.IndiaStatesData
import com.wonderslate.prepjoy.Utils.Utils
import com.wonderslate.prepjoy.Utils.Utils.disableScreenShot
import com.wonderslate.prepjoy.Utils.ValidationHelper
import com.wonderslate.prepjoy.databinding.ActivitySignUpBinding
import com.wonderslate.prepjoy.ui.BaseActivity
import com.wonderslate.prepjoy.ui.dashboard.DashBoardActivity
// import kotlinx.android.synthetic.main.activity_sign_up.*
import org.json.JSONObject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.*

class SignUpActivity : BaseActivity() {

    private lateinit var avLoadingIndicatorView: ProgressBar
    private var mContext: Context? = null
    var statesData: IndiaStatesData? = null
    private var selectedState: String? = null
    private var selectedDistrict: String? = null
    private val viewModel by viewModel<SignupViewModel>()

    private var isUserNameEmail: Boolean = false
    private var isUserNameMobile: Boolean = false
    private lateinit var binding: ActivitySignUpBinding

    // View references
    private lateinit var spinnerstate: Spinner
    private lateinit var spinnercity: Spinner
    private lateinit var txtvalidateState: TextView
    private lateinit var txtvalidatecity: TextView
    private lateinit var txtvalidateName: TextView
    private lateinit var edtName: EditText
    private lateinit var edtUsername: EditText
    private lateinit var edtPassword: EditText
    private lateinit var btnSignup: Button

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (supportActionBar != null) {
            supportActionBar!!.hide()
        }
        mContext = this
        initViews()
        initObserver()
        disableScreenShot(this)
    }

    private fun initViews() {
        try {
            // Initialize views
            avLoadingIndicatorView = findViewById(R.id.avlSignUp)
            spinnerstate = findViewById(R.id.spinnerstate)
            spinnercity = findViewById(R.id.spinnercity)
            txtvalidateState = findViewById(R.id.txtvalidateState)
            txtvalidatecity = findViewById(R.id.txtvalidatecity)
            txtvalidateName = findViewById(R.id.txtvalidateName)
            edtName = findViewById(R.id.edtName)
            edtUsername = findViewById(R.id.edtUsername)
            edtPassword = findViewById(R.id.edtPassword)
            btnSignup = findViewById(R.id.btnSignup)

            statesData = IndiaStatesData(this)
            val states: MutableList<String?> = ArrayList()
            states.addAll(statesData!!.statesList)
            states.sortWith { obj: String?, str: String? ->
                obj!!.compareTo(
                    str!!,
                    ignoreCase = true
                )
            }
            states.add(0, getString(R.string.sign_up_select_state))
            val stateSpinnerAdapter: ArrayAdapter<*> = ArrayAdapter<Any?>(
                this,
                R.layout.spinner_row_nothing_selected,
                states as List<Any?>
            )
            stateSpinnerAdapter.setDropDownViewResource(R.layout.spinner_textview)
            spinnerstate.adapter = stateSpinnerAdapter
            flavourSpecificSelection(states)
            val districtList: MutableList<String?> = ArrayList()
            districtList.add(0, getString(R.string.sign_up_select_district))
            val districtAdapter: ArrayAdapter<*> = ArrayAdapter<Any?>(
                this,
                R.layout.spinner_row_nothing_selected,
                districtList as List<Any?>
            )
            districtAdapter.setDropDownViewResource(R.layout.spinner_textview)
            spinnercity.adapter = districtAdapter
            spinnercity.isEnabled = false

            spinnerstate.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
                override fun onItemSelected(
                    parent: AdapterView<*>?,
                    view: View,
                    position: Int,
                    id: Long
                ) {
                    if (!states[position].equals(selectedState, ignoreCase = true)) {
                        selectedState = states[position]
                        val selectState = getString(R.string.sign_up_select_state)
                        val selectDistrict = getString(R.string.sign_up_select_district)
                        if (spinnerstate.selectedItem.toString()
                                .equals(selectState, ignoreCase = true)
                        ) {
                            districtList.clear()
                            districtList.add(0, selectDistrict)
                            selectedDistrict = selectDistrict
                            districtAdapter.notifyDataSetChanged()
                            spinnercity.isEnabled = false
                        } else {
                            spinnercity.isEnabled = true
                            districtAdapter.clear()
                            districtList.addAll(statesData!!.getDistricts(selectedState))
                            districtAdapter.notifyDataSetChanged()
                            selectedDistrict = districtList[0]
                            spinnercity.setSelection(0)
                        }
                        hideKeyboard(this@SignUpActivity)
                    }
                    if (txtvalidateState.isVisible) {
                        txtvalidateState.visibility = View.GONE
                    }
                }

                override fun onNothingSelected(parent: AdapterView<*>?) {}
            }

            spinnercity.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
                override fun onItemSelected(
                    parent: AdapterView<*>?,
                    view: View,
                    position: Int,
                    id: Long
                ) {
                    if (position < districtList.size) selectedDistrict = districtList[position]
                    hideKeyboard(this@SignUpActivity)
                    if (txtvalidatecity.isVisible) {
                        txtvalidatecity.visibility = View.GONE
                    }
                }

                override fun onNothingSelected(parent: AdapterView<*>?) {}
            }

            edtName.addTextChangedListener(object : TextWatcher {
                override fun afterTextChanged(arg0: Editable) {
                }

                override fun beforeTextChanged(
                    s: CharSequence,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                    if (txtvalidateName.isVisible) {
                        txtvalidateName.visibility = View.GONE
                    }
                }
            })

            btnSignup.setOnClickListener {
                startSignupProcess()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        finish()
    }

    override fun getLayoutResource(): Int {
        return R.layout.activity_sign_up
    }

    fun hideKeyboard(activity: Activity) {
        val imm = activity.getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        //Find the currently focused view, so we can grab the correct window token from it.
        var view = activity.currentFocus
        //If no view currently has focus, create a new one, just so we can grab a window token from it
        if (view == null) {
            view = View(activity)
        }
        imm.hideSoftInputFromWindow(view.windowToken, 0)
    }

    private fun isEmailOrMobile(userId: String): String {
        if (ValidationHelper.validatePhoneNumber(userId)) {
            return "mobile"
        }
        return if (ValidationHelper.ValidateEmail(userId)) {
            "email"
        } else ""
    }

    private fun startSignupProcess() {
        if (validateFields()) {
            if (FlavourHelper.isOTPLogin()) {
                viewModel.signup(
                        SignUpData(
                                edtName.text.toString(), WonderPubSharedPrefs.getInstance(this).username,
                                resources.getString(R.string.defaultPassword),
                                WonderPubSharedPrefs.getInstance(this).usermobile, "null",
                                selectedState!!, "true",BuildConfig.SITE_ID,selectedDistrict!!
                        )
                )
            }
            else {
                if (isEmailOrMobile(edtUsername.text.toString()) == "mobile") {
                    viewModel.signup(
                            SignUpData(
                                    edtName.text.toString(), edtUsername.text.toString(),
                                    edtPassword.text.toString(),
                                    edtUsername.text.toString(), "null",
                                    selectedState!!, "true",BuildConfig.SITE_ID,selectedDistrict!!
                            )
                    )
                }
                else {
                    viewModel.signup(
                            SignUpData(
                                    edtName.text.toString(), edtUsername.text.toString(),
                                    edtPassword.text.toString(),
                                    "null", edtUsername.text.toString(),
                                    selectedState!!, "true",BuildConfig.SITE_ID,selectedDistrict!!
                            )
                    )
                }
            }
        }
    }

    private fun validateFields(): Boolean {
        val name = edtName.text.toString()
        when {
            name.isEmpty() -> {
                txtvalidateName.visibility = View.VISIBLE
            }
            selectedState.equals(getString(R.string.select_state), ignoreCase = true) -> {
                txtvalidateState.visibility = View.VISIBLE
            }
            selectedDistrict.equals(getString(R.string.select_district), ignoreCase = true) -> {
                txtvalidatecity.visibility = View.VISIBLE
            }
            else -> {
                txtvalidateName.visibility = View.GONE
                txtvalidateState.visibility = View.GONE
                txtvalidatecity.visibility = View.GONE
                return true
            }
        }
        return false
    }

    private fun initObserver() {
        viewModel.signupResponse.observe(this, ::signUpResponse)
        viewModel.loginResponse.observe(this, ::loginResponse)
        viewModel.userDetailsResponse.observe(this, ::userDetailsResponse)
    }

    private fun signUpResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {

            }

            Status.SUCCESSFUL -> {
                data.data?.let {
                    if (FlavourHelper.isOTPLogin()) {
                        viewModel.login(
                                LoginData(
                                        WonderPubSharedPrefs.getInstance(this).username,
                                        resources.getString(R.string.defaultPassword),
                                        BuildConfig.SITE_ID
                                )
                        )
                    }
                    else {
                        viewModel.login(
                                LoginData(
                                        edtUsername.text.toString(),
                                        edtPassword.text.toString(),
                                        BuildConfig.SITE_ID
                                )
                        )
                    }

                    WonderPubSharedPrefs.getInstance(this).userDistrict = selectedDistrict
                }
            }

            Status.HTTP_UNAVAILABLE -> {
                Utils.showTopSnackBar(resources.getString(R.string.server_under_maintenance), this)
            }

            Status.ERROR -> {
                Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), this)
            }
        }
    }

    private fun loginResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {

            }

            Status.SUCCESSFUL -> {
                //avLoadingIndicatorView.hide()
                data.data?.let {
                   // avLoadingIndicatorView.visibility = View.VISIBLE
                    WonderPubSharedPrefs.getInstance(this).accessToken =
                        it.optString("access_token")
                    viewModel.userDetails(
                        UserDetailsData(BuildConfig.SITE_ID)
                    )
                }
            }

            Status.HTTP_UNAVAILABLE -> {
                Utils.showTopSnackBar(resources.getString(R.string.server_under_maintenance), this)
            }

            Status.ERROR -> {
                Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), this)
            }
        }
    }

    private fun userDetailsResponse(data: Data<JSONObject>) {
       // avLoadingIndicatorView.hide()
        when (data.responseType) {
            Status.LOADING -> {

            }

            Status.SUCCESSFUL -> {
               // avLoadingIndicatorView.hide()
                data.data?.let {

                    try {
                        val jObj = JSONObject(data.data.toString())

                        WonderPubSharedPrefs.getInstance(this).username = jObj.getString("name")
                        WonderPubSharedPrefs.getInstance(this).usermobile = jObj.getString("mobile")
                        WonderPubSharedPrefs.getInstance(this).userId = jObj.getString("id")
                        WonderPubSharedPrefs.getInstance(this).userState = jObj.getString("state")
                       // WonderPubSharedPrefs.getInstance(this).userDistrict = jObj.getString("district")
                        WonderPubSharedPrefs.getInstance(this).userDistrict = if(jObj.getString("district").toString() != "null")  jObj.getString("district") else selectedDistrict

                        WonderPubSharedPrefs.getInstance(this).userImage =
                            WSAPIManager.SERVICE.toString()
                                .plus("funlearn/showProfileImage?id=" + jObj.getString("id"))
                                .plus("&fileName=")
                                .plus(jObj.getString("profilePic") + "&type=user&imgType=passport")

                        WonderPubSharedPrefs.getInstance(this).setisUserLoggedIn(true)
                        hideKeyboard(this)
                        val intent = Intent(this, DashBoardActivity::class.java)
                        intent.putExtra("isFirstTime", true)
                        startActivity(intent)
                        finish()

                    } catch (e: Exception) {
                        e.printStackTrace()

                    }
                }
            }

            Status.HTTP_UNAVAILABLE -> {
                Utils.showTopSnackBar(resources.getString(R.string.server_under_maintenance), this)
            }

            Status.ERROR -> {
                Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), this)
            }
        }
    }

    /**
     * Selects the state based on the flavour of the application.
     * @param states list of available states for India
     */
    private fun flavourSpecificSelection(states: List<String?>) {
        when(BuildConfig.FLAVOR) {
            Flavours.KARNATAKA.flavour -> {
                var position = 0
                states.forEachIndexed { index, s ->
                    if (s.equals(Flavours.KARNATAKA.flavour, true)) {
                        position = index
                    }
                }
                if (position != -1) {
                    spinnerstate.setSelection(position)
                }
            }
        }
    }
}