package com.wonderslate.prepjoy.ui.videos;

import static android.view.ViewGroup.LayoutParams.MATCH_PARENT;
import static androidx.media3.ui.AspectRatioFrameLayout.RESIZE_MODE_FIT;
import static androidx.media3.ui.AspectRatioFrameLayout.RESIZE_MODE_ZOOM;
import static com.wonderslate.prepjoy.Utils.Utils.disableScreenShot;
import static com.wonderslate.prepjoy.ui.videos.JWPlayerMediaExtractor.jwPlayerMediaIdFromResLink;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.net.ConnectivityManager;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.core.view.WindowInsetsControllerCompat;
import androidx.media3.common.MediaItem;
import androidx.media3.common.Player;
import androidx.media3.common.util.Util;
import androidx.media3.datasource.DataSource;
import androidx.media3.datasource.DefaultDataSourceFactory;
import androidx.media3.exoplayer.DefaultLoadControl;
import androidx.media3.exoplayer.DefaultRenderersFactory;
import androidx.media3.exoplayer.SimpleExoPlayer;
import androidx.media3.exoplayer.hls.HlsManifest;
import androidx.media3.exoplayer.hls.HlsMediaSource;
import androidx.media3.exoplayer.hls.playlist.HlsMasterPlaylist;
import androidx.media3.exoplayer.source.MediaSource;
import androidx.media3.exoplayer.source.MergingMediaSource;
import androidx.media3.exoplayer.source.ProgressiveMediaSource;
import androidx.media3.exoplayer.trackselection.DefaultTrackSelector;
import androidx.media3.exoplayer.upstream.DefaultAllocator;
import androidx.media3.ui.PlayerView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.facebook.network.connectionclass.ConnectionClassManager;
import com.facebook.network.connectionclass.ConnectionQuality;
import com.facebook.network.connectionclass.DeviceBandwidthSampler;
import com.naveed.ytextractor.ExtractorException;
import com.naveed.ytextractor.YoutubeStreamExtractor;
import com.naveed.ytextractor.model.YoutubeMedia;
import com.naveed.ytextractor.model.YoutubeMeta;
import com.wang.avi.AVLoadingIndicatorView;
import com.wonderslate.data.network.Wonderslate;
import com.wonderslate.data.preferences.WonderPubSharedPrefs;
import com.wonderslate.prepjoy.R;
import com.wonderslate.prepjoy.Utils.CustomMediaPlayerConfig;
import com.wonderslate.prepjoy.Utils.CustomSnackBar;
import com.wonderslate.prepjoy.Utils.HTTPUrlChecker;
import com.wonderslate.prepjoy.Utils.InternetConnectionChecker;
import com.wonderslate.prepjoy.Utils.KeyBoardHeightProvider;
import com.wonderslate.prepjoy.Utils.Utils;
import com.wonderslate.prepjoy.ui.BaseActivity;

import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.json.JSONObject;
import org.koin.android.BuildConfig;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.Timer;

public class CustomMediaPlayer extends BaseActivity implements KeyBoardHeightProvider.KeyboardHeightObserver {

    private static final String TAG = "CustomPlayer";
    PlayerView videoPlayer;
    SimpleExoPlayer player;
    private Uri audioUri;
    private static long playPosition;
    private static int currentVideoSource = 0,videoHeight,newOrientation;
    private static String youtubeUri, resLink;
    private static boolean playStatus = true,playerInitialized;
    private ImageButton playSpeedBtn, playBtn, pauseBtn, playQualityBtn, fullScreenBtn, resizeBtn;
    private TextView liveStatusText,videoTitle,videoTitleLand;
    private List<Integer> sparseAdaptiveResolutionList;
    private HashMap<Integer, String> sparseAdaptiveVideoUrlList;
    private List<String> sparseOPUSAudioUrl;
    private RecyclerView popupList;
    private PopupWindow popupWindow;
    private String  videoResName;
    private DeviceBandwidthSampler mDeviceBandwidthSampler;
    private ConnectionQuality mConnectionClass = ConnectionQuality.UNKNOWN;
    private KeyBoardHeightProvider keyboardHeightProvider;
    InternetConnectionChecker internetConnectionChecker;
    private boolean isProgressive, isJWPlayer, isVimeo,haveCustomStartPosition,
            isInitialResChanged,isHlsContent, isDashContent, isLiveStream;
    DataSource.Factory dataSourceFactory;
    MediaSource mediaSource;
    DefaultLoadControl loadControl;
    HashMap<Integer, String> refinedStreamMap;
    List<Integer> refinedResolutionList;
    DateTime startTime, endTime;
    TextView videoLoadingText;
    int videoFirstTry = 0;
    AVLoadingIndicatorView videoLoader;

    @Override
    protected void onCreate(Bundle savedInstanceState) {

        try {
            super.onCreate(savedInstanceState);
            Log.e(TAG, "On Create");
            JodaTimeAndroid.init(this);
            startTime = DateTime.now();
            dataSourceFactory = new DefaultDataSourceFactory(this);
            Log.e(TAG, "Default Data Source");
            loadControl = new DefaultLoadControl.Builder()
                    .setAllocator(new DefaultAllocator(true, 16))
                    .setBufferDurationsMs(CustomMediaPlayerConfig.MAX_BUFFER_DURATION,
                            CustomMediaPlayerConfig.MAX_BUFFER_DURATION,
                            CustomMediaPlayerConfig.MIN_PLAYBACK_START_BUFFER,
                            CustomMediaPlayerConfig.MIN_PLAYBACK_START_BUFFER)
                    .setTargetBufferBytes(-1)
                    .setPrioritizeTimeOverSizeThresholds(true).createDefaultLoadControl();
            Log.d(TAG, "default Load Control");
            playerInitialized = true;
            internetConnectionChecker = new InternetConnectionChecker();
            videoHeight = (int) (Resources.getSystem().getDisplayMetrics().heightPixels / 2.6);
            if (getSupportActionBar() != null) {
                getSupportActionBar().hide();
            }
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
            getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN | WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING);
            retrieveResLink();
            videoResName = getIntent().getStringExtra("videoResName");
            Log.d(TAG, "init to be called");
            init();
            setFullScreen();
            disableScreenShot(this);
            setFullScreen(Configuration.ORIENTATION_PORTRAIT, true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected int getLayoutResource() {
        return R.layout.activity_custom_player;
    }

    @Override
    protected void networkStatusChecker(boolean state) {
        try {
            if (state) {
                if (!playerInitialized) {
                    customSnackBar.show(getResources().getString(R.string.internet_connection_online_text) + " Resuming Video", CustomSnackBar.LENGTH_SHORT);
                    if (player != null) {
                        player.release();
                        playStatus = true;
                        initPlayer();
                    }
                }
            } else {
                customSnackBar.show(getResources().getString(R.string.internet_connection_offline_text), CustomSnackBar.LENGTH_SHORT);
            }
        } catch (Resources.NotFoundException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onConfigurationChanged(@NotNull Configuration newConfig) {
        try {
            super.onConfigurationChanged(newConfig);
            if (getSupportActionBar() != null) {
                getSupportActionBar().hide();
            }
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
            newOrientation = newConfig.orientation;
            if (newOrientation == Configuration.ORIENTATION_LANDSCAPE) {
                if (videoTitle != null && videoTitleLand != null) {
                    videoTitle.setVisibility(View.GONE);
                    videoTitleLand.setVisibility(View.VISIBLE);
                    videoTitleLand.setText(videoResName);
                }
            } else if (newOrientation == Configuration.ORIENTATION_PORTRAIT) {
                if (videoTitle != null && videoTitleLand != null) {
                    videoTitle.setVisibility(View.VISIBLE);
                    videoTitleLand.setVisibility(View.GONE);
                    videoTitleLand.setText(videoResName);
                }
            }
            setFullScreen(newConfig.orientation, false);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void setFullScreen()
    {
        try {
            if (getSupportActionBar() != null) {
                getSupportActionBar().hide();
            }
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);

            newOrientation = getResources().getConfiguration().orientation;

            if (videoTitle != null && videoTitleLand != null) {
                videoTitle.setVisibility(View.GONE);
                videoTitleLand.setVisibility(View.VISIBLE);
                videoTitleLand.setText(videoResName);
            }
            setFullScreen(Configuration.ORIENTATION_LANDSCAPE, true);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    private void init() {
        try {
            Log.d(TAG, "inside init");
            WonderPubSharedPrefs sharedPrefs = Wonderslate.getInstance().getSharedPrefs();
            keyboardHeightProvider = new KeyBoardHeightProvider(this);
            keyboardHeightProvider.setKeyboardHeightObserver(this);
            videoPlayer = findViewById(R.id.video_view);
            videoLoader = findViewById(R.id.videoLoader);
            videoLoadingText = findViewById(R.id.video_loading_text);
            playSpeedBtn = findViewById(R.id.exo_playspeed);
            playBtn = findViewById(R.id.exo_play);
            resizeBtn = findViewById(R.id.exo_resize);
            pauseBtn = findViewById(R.id.exo_pause);
            videoTitle = findViewById(R.id.exo_videotitle);
            videoTitleLand = findViewById(R.id.exo_videotitle_land);
            playQualityBtn = findViewById(R.id.exo_videoquality);
            TextView overlayTextView = findViewById(R.id.overlaytext);
            overlayTextView.setVisibility(View.GONE);
            fullScreenBtn = findViewById(R.id.exo_fullscreen);
            liveStatusText = findViewById(R.id.exo_live_video);
            LinearLayout resumeVideoLayout = findViewById(R.id.resumevideolayout);
            resumeVideoLayout.setVisibility(View.GONE);
            mDeviceBandwidthSampler = DeviceBandwidthSampler.getInstance();
            newOrientation = getResources().getConfiguration().orientation;
            setFullScreen(getResources().getConfiguration().orientation, false);
            videoLoader.setVisibility(View.VISIBLE);
            videoLoadingText.setVisibility(View.VISIBLE);
            liveStatusText.setOnClickListener(view -> {
                if (player != null) {
                    player.seekTo(player.getDuration());
                }
            });
            videoPlayer.setControllerVisibilityListener((PlayerView.ControllerVisibilityListener) visibility -> {
                if (newOrientation == Configuration.ORIENTATION_LANDSCAPE) {
                    if (visibility == View.VISIBLE) {
                        videoTitleLand.setVisibility(View.VISIBLE);
                    } else {
                        videoTitleLand.setVisibility(View.GONE);
                    }
                }
            });
            videoTitle.setTextColor(getResources().getColor(R.color.black));
            if (fullScreenBtn != null && getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
                fullScreenBtn.setImageResource(R.drawable.ic_fullscreen_white);
                videoTitle.setText(videoResName);
                videoTitleLand.setVisibility(View.GONE);
            } else if (fullScreenBtn != null && getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE) {
                fullScreenBtn.setImageResource(R.drawable.ic_fullscreen_exit_white);
                videoTitle.setVisibility(View.GONE);
                videoTitleLand.setText(videoResName);
            }

            if (getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
                videoPlayer.setResizeMode(RESIZE_MODE_FIT);
                resizeBtn.setVisibility(View.GONE);
            }

            fullScreenBtn.setOnClickListener(view -> {
                if (fullScreenBtn != null && getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
                    fullScreenBtn.setImageResource(R.drawable.ic_fullscreen_exit_white);
                    resizeBtn.setVisibility(View.VISIBLE);
                } else if (fullScreenBtn != null && getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    fullScreenBtn.setImageResource(R.drawable.ic_fullscreen_white);
                    videoPlayer.setResizeMode(RESIZE_MODE_FIT);
                    resizeBtn.setVisibility(View.GONE);
                }
                setFullScreen(newOrientation, true);
            });

            playBtn.setOnClickListener(view -> {
                if (player != null && player.getPlaybackState() == Player.STATE_READY) {
                    playBtn.setVisibility(View.GONE);
                    pauseBtn.setVisibility(View.VISIBLE);
                    playStatus = true;
                    player.setPlayWhenReady(true);
                }
            });

            pauseBtn.setOnClickListener(view -> {
                if (player != null && player.getPlaybackState() == Player.STATE_READY) {
                    playBtn.setVisibility(View.VISIBLE);
                    pauseBtn.setVisibility(View.GONE);
                    playStatus = false;
                    player.setPlayWhenReady(false);
                }
            });

            playSpeedBtn.setOnClickListener(view -> {
                if (player != null) {
                    //Creating the instance of PopupMenu
                    showPopUpMenu("speed", playSpeedBtn);
                }
            });

            playQualityBtn.setOnClickListener(view -> {
                //Creating the instance of PopupMenu
                showPopUpMenu("quality", playQualityBtn);
            });

            resizeBtn.setOnClickListener(view -> {
                if (videoPlayer.getResizeMode() == RESIZE_MODE_FIT) {
                    videoPlayer.setResizeMode(RESIZE_MODE_ZOOM);
                } else {
                    videoPlayer.setResizeMode(RESIZE_MODE_FIT);
                }
            });

            startApiTimer(() -> {
                if (customSnackBar != null)
                    customSnackBar.showActionSnackBar("Taking longer than expected. We are getting your video data. Please wait...", "OK",
                            CustomSnackBar.LENGTH_INDEFINITE, () -> {
                                if (customSnackBar != null)
                                    customSnackBar.dismiss();
                                stopApiTimer();
                            });
            }, 5000);

            if (resLink != null && !resLink.isEmpty() && (!resLink.contains("/") || resLink.contains("youtube") || resLink.contains("youtu.be")))
            {
                isJWPlayer = false;
                isVimeo = false;
                Log.e(TAG, "extract yotube url");
                extractYoutubeUrl();
            }
            else if (resLink != null && !resLink.isEmpty() && (resLink.contains("jwplayer") || resLink.contains("jwplatform"))) {
                isJWPlayer = true;
                isVimeo = false;
                String mediaId = jwPlayerMediaIdFromResLink(resLink);
                extractJWPlayerUrl(mediaId);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void changeVideoQuality(int quality) {
        if (player != null && sparseAdaptiveVideoUrlList != null && sparseAdaptiveVideoUrlList.size() > 0) {
            playPosition = player.getCurrentPosition();
            player.release();
            youtubeUri = sparseAdaptiveVideoUrlList.get(quality);
            currentVideoSource = quality;
            Wonderslate.getInstance().getSharedPrefs().setUserPreferredVideoResolution(quality);
            VideoPopUpMenuAdapter.selectedPlayBackSpeed = 3;
            if (popupList != null && popupWindow != null && popupWindow.isShowing()) {
                new Handler().postDelayed(() -> popupWindow.dismiss(), 250);
            }
            initPlayer();
        }
    }
    private void showPopUpMenu(String action, View anchorView) {
        View layout = getLayoutInflater().inflate(R.layout.video_pop_up_menu_layout, null);
        popupWindow = new PopupWindow(layout, LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT, true);
        popupList = layout.findViewById(R.id.videopopuplist);
        RecyclerView.LayoutManager mLayoutManager = new LinearLayoutManager(CustomMediaPlayer.this);
        popupList.setLayoutManager(mLayoutManager);
        //popupList.setHasFixedSize(true);

        //Set Pop Up Menu Adapter
        VideoPopUpMenuAdapter mAdapter;
        switch (action) {
            case "speed":
                if (player != null) {
                    mAdapter = new VideoPopUpMenuAdapter(CustomMediaPlayer.this,
                            getResources().getStringArray(R.array.video_playback_speed),
                            player, "speed", isLiveStream);
                    popupList.setAdapter(mAdapter);
                    mAdapter.notifyDataSetChanged();
                }
                break;
            case "quality":
                if (sparseAdaptiveResolutionList != null && !sparseAdaptiveResolutionList.isEmpty() && player != null) {
                    Collections.sort(sparseAdaptiveResolutionList);

                    for (Iterator<Integer> iterator = sparseAdaptiveResolutionList.iterator(); iterator.hasNext(); ) {
                        int value = iterator.next();
                        if (value == -1 || value == 0) {
                            iterator.remove();
                        }
                    }

                    mAdapter = new VideoPopUpMenuAdapter(CustomMediaPlayer.this, sparseAdaptiveResolutionList,
                            player, "quality", currentVideoSource, isJWPlayer, isVimeo, isLiveStream);
                    popupList.setAdapter(mAdapter);
                    mAdapter.notifyDataSetChanged();
                }
                break;
        }

        popupWindow.setOutsideTouchable(true);
        // Clear the default translucent background
        // Needed specifically for Android 5.0.1 and 5.1.1
        // If this is not there, Touch on Custom Video Player won't work as expected
        // Do not remove this line, or else Custom Video Player will not work accordingly
        // Don't set BackgroundDrawable to null
        popupWindow.setBackgroundDrawable(new ColorDrawable(Color.WHITE));

        popupWindow.setAnimationStyle(android.R.style.Animation_Dialog);
        int[] location = new int[2];
        Rect rect = new Rect();

        // Get the View's(the one that was clicked in the Fragment) location
        anchorView.getLocationInWindow(location);
        anchorView.getLocalVisibleRect(rect);

        // Using location, the PopupWindow will be displayed right under anchorView
        if (newOrientation == Configuration.ORIENTATION_PORTRAIT) {
            popupWindow.showAsDropDown(anchorView);
        } else {
            if (action.equalsIgnoreCase("speed")) {
                popupWindow.showAtLocation(getWindow().getDecorView(), Gravity.NO_GRAVITY, location[0] + (anchorView.getWidth() / 2),
                        location[1] + anchorView.getHeight() + 50);
                //popupWindow.showAsDropDown(anchorView, 0, (-rect.top -780));
            } else {
                popupWindow.showAtLocation(getWindow().getDecorView(), Gravity.NO_GRAVITY, location[0] + (anchorView.getWidth() / 2),
                        location[1] + anchorView.getHeight() + 50);
            }
        }
    }

    private void setFullScreen(int orientation, boolean forced)
    {
        try {
            if (forced) {
                if (orientation == Configuration.ORIENTATION_PORTRAIT) {

                    setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE);
                } else if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_SENSOR_PORTRAIT);
                    setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED);
                }
            } else {
                if (orientation == Configuration.ORIENTATION_PORTRAIT) {
                    videoPlayer.getLayoutParams().height = videoHeight;
                    videoPlayer.requestLayout();
                } else if (orientation == Configuration.ORIENTATION_LANDSCAPE) {

                    videoPlayer.getLayoutParams().height = MATCH_PARENT;
                    videoPlayer.requestLayout();
                }

                if (fullScreenBtn != null && orientation == Configuration.ORIENTATION_PORTRAIT) {
                    fullScreenBtn.setImageResource(R.drawable.ic_fullscreen_white);
                } else if (fullScreenBtn != null && orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    fullScreenBtn.setImageResource(R.drawable.ic_fullscreen_exit_white);
                }
            }

            WindowInsetsControllerCompat windowInsetsController =
                    WindowCompat.getInsetsController(getWindow(), getWindow().getDecorView());
            // Configure the behavior of the hidden system bars.
            windowInsetsController.setSystemBarsBehavior(
                    WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
            );

            // Add a listener to update the behavior of the toggle fullscreen button when
            // the system bars are hidden or revealed.
            ViewCompat.setOnApplyWindowInsetsListener(
                    getWindow().getDecorView(),
                    (view, windowInsets) -> {
                        // You can hide the caption bar even when the other system bars are visible.
                        // To account for this, explicitly check the visibility of navigationBars()
                        // and statusBars() rather than checking the visibility of systemBars().
                        if (orientation == Configuration.ORIENTATION_PORTRAIT) {
                            windowInsetsController.show(WindowInsetsCompat.Type.systemBars());
                        }
                        else {
                            if (windowInsets.isVisible(WindowInsetsCompat.Type.navigationBars())
                                    || windowInsets.isVisible(WindowInsetsCompat.Type.statusBars())) {
                                windowInsetsController.hide(WindowInsetsCompat.Type.systemBars());
                            }
                        }

                        return ViewCompat.onApplyWindowInsets(view, windowInsets);
                    });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onBackPressed() {
        if (player != null) {
            if (!isLiveStream && (player.getCurrentPosition() != player.getDuration()) && (player.getCurrentPosition() != 0)) {
                try {
                    WonderPubSharedPrefs.getInstance(CustomMediaPlayer.this).setVideoResumePosition(player.getCurrentPosition(), resLink);
                } catch (NumberFormatException exception) {
                    Log.e(TAG, "Number Format exception", exception);
                }

            }
            mDeviceBandwidthSampler.stopSampling();
            player.release();
            player = null;
            playPosition = 0;
            youtubeUri = "";
            currentVideoSource = 0;
            playStatus = true;
            newOrientation = 0;
            VideoPopUpMenuAdapter.selectedPlayBackSpeed = 3;
            //pictureInPicture();
        }
        if (keyboardHeightProvider != null) {
            keyboardHeightProvider.close();
        }
        super.onBackPressed();
    }

    private void showRestartAlertDialog() {

        if (isFinishing() || isDestroyed())
            return;

        if (customSnackBar != null) {
            customSnackBar.dismiss();
            stopApiTimer();
        }

        final androidx.appcompat.app.AlertDialog.Builder dialog = new androidx.appcompat.app.AlertDialog.Builder(this);
        /*dialog.setMessage("Video playback has failed.");
        dialog.setPositiveButton("Retry", null);
        dialog.setNegativeButton("Cancel", null);
        dialog.setTitle(new FlavorsSettingHelper().getAppName());*/
        dialog.setCancelable(false);
        final androidx.appcompat.app.AlertDialog alertDialog = dialog.create();
        View dialogView = getLayoutInflater().inflate(R.layout.retry_video_dialog, null, false);
        alertDialog.setView(dialogView);
        alertDialog.setCancelable(false);
        alertDialog.setCanceledOnTouchOutside(false);
        alertDialog.show();

        TextView dialogBodyTxtView = dialogView.findViewById(R.id.body);
        TextView dialogTitleTextView = dialogView.findViewById(R.id.title);
        dialogTitleTextView.setText(videoResName);
        dialogBodyTxtView.setText("Video playback has failed.");
        Button retryBtn = dialogView.findViewById(R.id.positiveBtn);
        Button cancelBtn = dialogView.findViewById(R.id.negativeBtn);

        retryBtn.setOnClickListener(view -> {
            //Restart Video Process
            startTime = DateTime.now();
            customSnackBar.show("Reloading video. Please wait...", CustomSnackBar.LENGTH_SHORT);
            if (resLink != null && !resLink.isEmpty() && (!resLink.contains("/") || resLink.contains("youtube") || resLink.contains("youtu.be"))) {
                isJWPlayer = false;
                isVimeo = false;
                Log.e(TAG, "extract yotube url");
                /*endTime = DateTime.now();
                Duration duration = new Duration(startTime, endTime);
                Log.e(TAG, "Duration to reach youtube extractor: " + duration.getStandardSeconds());*/
                extractYoutubeUrl();
            }
            alertDialog.dismiss();
        });

        cancelBtn.setOnClickListener(view -> {
            alertDialog.dismiss();
            onBackPressed();
        });

    }

    private void extractYoutubeUrl() {
        try{
        Log.e(TAG, "youtube extraction started");
        new YoutubeStreamExtractor(new YoutubeStreamExtractor.ExtractorListner() {
            @Override
            public void onExtractionGoesWrong(ExtractorException e, String message, String data) {
                Log.e(TAG, "extract youtube url done");
                endTime = DateTime.now();
                Duration duration = new Duration(startTime, endTime);
                showRestartAlertDialog();
            }

            @Override
            public void onExtractionDone(List<YoutubeMedia> adativeStream, List<YoutubeMedia> muxedStream, YoutubeMeta meta) {
                Log.e(TAG, "extract youtube url done");
                endTime = DateTime.now();
                Duration duration = new Duration(startTime, endTime);
                startTime = DateTime.now();
                customSnackBar.show("Loading Video.. Please Wait.", CustomSnackBar.LENGTH_LONG);
                Log.e(TAG, "parse youtube url started");
                sparseAdaptiveResolutionList = new ArrayList<>();
                sparseAdaptiveVideoUrlList = new HashMap<>();
                sparseOPUSAudioUrl = new ArrayList<>();
                if (meta.getisLive()) {
                    isLiveStream = true;
                    liveStatusText.setVisibility(View.VISIBLE);
                    for (int i = 0; i < muxedStream.size(); i++) {
                        YoutubeMedia media = muxedStream.get(i);
                        if (media.isMuxed()) {
                            int resolution = getLiveIndexResolution(i);
                            if (!sparseAdaptiveResolutionList.contains(resolution) && resolution <= 1080) {
                                isHlsContent = true;
                                isDashContent = false;
                                sparseAdaptiveResolutionList.add(resolution);
                                sparseAdaptiveVideoUrlList.put(resolution,
                                        media.getUrl());
                            }
                        }
                    }
                } else if (adativeStream.size() > 0) {
                    isLiveStream = false;
                    liveStatusText.setVisibility(View.GONE);
                    for (YoutubeMedia media : adativeStream) {
                        if (media.isVideoOnly()) {
                            //Adaptive Video URL
                            int resolution = Integer.valueOf(media.getResolution().replace("p", ""));
                            if (resolution > 1080)
                                continue;
                            if (!sparseAdaptiveResolutionList.contains(resolution)) {
                                isHlsContent = false;
                                isDashContent = false;
                                sparseAdaptiveResolutionList.add(resolution);
                                sparseAdaptiveVideoUrlList.put(resolution, media.getUrl());
                            }
                        } else if (media.isAudioOnly()) {
                            //OPUS Audio URL
                            if (!sparseOPUSAudioUrl.contains(media.getUrl())) {
                                sparseOPUSAudioUrl.add(media.getUrl());
                            }
                        } else if (media.isMuxed()) {
                            //Progressive Video URL
                            int resolution = Integer.valueOf(media.getResolution().replace("p", ""));
                            if (!sparseAdaptiveResolutionList.contains(resolution) && resolution <= 1080) {
                                isHlsContent = false;
                                isDashContent = false;
                                isProgressive = true;
                                sparseAdaptiveResolutionList.add(resolution);
                                sparseAdaptiveVideoUrlList.put(resolution, media.getUrl());
                            }
                        }
                    }
                } else if (muxedStream.size() > 0) {
                    //For Transition Video(From Live to Uploaded)
                    isLiveStream = false;
                    /*chatParentLayout.setVisibility(View.GONE);
                    chatRefreshBtn.setVisibility(View.GONE);*/
                    liveStatusText.setVisibility(View.GONE);
                    for (int i = 0; i < muxedStream.size(); i++) {
                        YoutubeMedia media = muxedStream.get(i);
                        if (media.isMuxed()) {
                            //Muxed Video URL
                            int resolution = getLiveIndexResolution(i);
                            if (!sparseAdaptiveResolutionList.contains(resolution) && resolution <= 1080) {
                                isHlsContent = true;
                                isDashContent = false;
                                sparseAdaptiveResolutionList.add(resolution);
                                sparseAdaptiveVideoUrlList.put(resolution,
                                        media.getUrl());
                            }
                        }
                    }
                } else {
                    if (meta.getIsLiveContent() && meta.getIsLowLatencyLiveStream() && !meta.getisLive()) {
                        customSnackBar.showActionSnackBar("Video is being processed. Please check later.", "OK", CustomSnackBar.LENGTH_INDEFINITE, () -> {
                            Log.e(TAG, "No streaming Links were found. Video transitioning from live to uploaded.");
                            customSnackBar.dismiss();
                            onBackPressed();
                        });
                    } else {
                        customSnackBar.showActionSnackBar("Video is not available.", "OK", CustomSnackBar.LENGTH_INDEFINITE, () -> {
                            Log.e(TAG, "Video not found.");
                            customSnackBar.dismiss();
                            onBackPressed();
                        });
                    }
                }
                //Check stream validity - new logic
                Log.e(TAG, "parse youtube url done");
                endTime = DateTime.now();
                Duration duration2 = new Duration(startTime, endTime);
                Log.e(TAG, "Duration to parse -- success: " + duration2.getStandardSeconds());

                Log.e(TAG, "check youtube url started");
                //startTime = DateTime.now();
                checkUrlValidity(sparseAdaptiveVideoUrlList, sparseAdaptiveResolutionList, new UrlValidityCheckerCallBack() {
                    @Override
                    public void onUrlValidatorResponse(HashMap<Integer, String> streamMap, List<Integer> resolutionList) {
                        Log.e(TAG, "check youtube url done");
                        endTime = DateTime.now();
                        Duration duration3 = new Duration(startTime, endTime);
                        Log.e(TAG, "Duration to check -- success: " + duration3.getStandardSeconds());

                        Log.e(TAG, "map youtube url started");
                        //startTime = DateTime.now();

                        sparseAdaptiveVideoUrlList = streamMap;
                        sparseAdaptiveVideoUrlList.remove(144);
                        sparseAdaptiveResolutionList = resolutionList;
                        for (Iterator<Integer> iterator = sparseAdaptiveResolutionList.iterator(); iterator.hasNext(); ) {
                            Integer resolution = iterator.next();
                            if (resolution == 144) {
                                iterator.remove();
                            }
                        }
                        if (sparseAdaptiveVideoUrlList != null && sparseAdaptiveVideoUrlList.size() > 0) {
                            Collections.sort(sparseAdaptiveResolutionList, Collections.reverseOrder());
                            //Collections.sort(sparseAdaptiveResolutionList, Collections.reverseOrder());

                            mDeviceBandwidthSampler.startSampling();

                            mConnectionClass = ConnectionClassManager.getInstance().getCurrentBandwidthQuality();

                            //Check if user has preferred video quality or not
                            if (Wonderslate.getInstance().getSharedPrefs().getUserPreferredVideoResolution() == 0) {
                                //If no user preferred video quality
                                //Normal flow
                                switch (mConnectionClass.name()) {
                                    case "POOR":
                                        if (sparseAdaptiveVideoUrlList.containsKey(240)) {
                                            youtubeUri = sparseAdaptiveVideoUrlList.get(240);
                                            currentVideoSource = 240;
                                        } else if (sparseAdaptiveVideoUrlList.containsKey(360)) {
                                            youtubeUri = sparseAdaptiveVideoUrlList.get(360);
                                            currentVideoSource = 360;
                                        } else {
                                            youtubeUri = sparseAdaptiveVideoUrlList.get(sparseAdaptiveResolutionList.get(0));
                                            currentVideoSource = sparseAdaptiveResolutionList.get(0);
                                        }
                                        break;
                                    case "MODERATE":
                                        if (sparseAdaptiveVideoUrlList.containsKey(480)) {
                                            youtubeUri = sparseAdaptiveVideoUrlList.get(480);
                                            currentVideoSource = 480;
                                        } else if (sparseAdaptiveVideoUrlList.containsKey(360)) {
                                            youtubeUri = sparseAdaptiveVideoUrlList.get(360);
                                            currentVideoSource = 360;
                                        } else if (sparseAdaptiveVideoUrlList.containsKey(240)) {
                                            youtubeUri = sparseAdaptiveVideoUrlList.get(240);
                                            currentVideoSource = 240;
                                        } else {
                                            youtubeUri = sparseAdaptiveVideoUrlList.get(sparseAdaptiveResolutionList.get(0));
                                            currentVideoSource = sparseAdaptiveResolutionList.get(0);
                                        }
                                        break;
                                    case "GOOD":
                                        youtubeUri = sparseAdaptiveVideoUrlList.get(sparseAdaptiveResolutionList.get(0));
                                        currentVideoSource = sparseAdaptiveResolutionList.get(0);
                                        break;
                                    case "EXCELLENT":
                                        youtubeUri = sparseAdaptiveVideoUrlList.get(sparseAdaptiveResolutionList.get(0));
                                        currentVideoSource = sparseAdaptiveResolutionList.get(0);
                                        break;
                                    case "UNKNOWN":
                                        youtubeUri = sparseAdaptiveVideoUrlList.get(sparseAdaptiveResolutionList.get(sparseAdaptiveResolutionList.size() - 1));
                                        currentVideoSource = sparseAdaptiveResolutionList.get(sparseAdaptiveResolutionList.size() - 1);
                                        break;
                                }

                            } else {
                                switch (mConnectionClass.name()) {
                                    case "POOR":
                                        if (sparseAdaptiveVideoUrlList.containsKey(240)) {
                                            youtubeUri = sparseAdaptiveVideoUrlList.get(240);
                                            currentVideoSource = 240;
                                        } else if (sparseAdaptiveVideoUrlList.containsKey(360)) {
                                            youtubeUri = sparseAdaptiveVideoUrlList.get(360);
                                            currentVideoSource = 360;
                                        } else {
                                            youtubeUri = sparseAdaptiveVideoUrlList.get(sparseAdaptiveResolutionList.get(0));
                                            currentVideoSource = sparseAdaptiveResolutionList.get(0);
                                        }
                                        break;
                                    case "MODERATE":
                                        if (sparseAdaptiveVideoUrlList.containsKey(480)) {
                                            youtubeUri = sparseAdaptiveVideoUrlList.get(480);
                                            currentVideoSource = 480;
                                        } else if (sparseAdaptiveVideoUrlList.containsKey(360)) {
                                            youtubeUri = sparseAdaptiveVideoUrlList.get(360);
                                            currentVideoSource = 360;
                                        } else if (sparseAdaptiveVideoUrlList.containsKey(240)) {
                                            youtubeUri = sparseAdaptiveVideoUrlList.get(240);
                                            currentVideoSource = 240;
                                        } else {
                                            youtubeUri = sparseAdaptiveVideoUrlList.get(sparseAdaptiveResolutionList.get(0));
                                            currentVideoSource = sparseAdaptiveResolutionList.get(0);
                                        }
                                        break;
                                    case "GOOD":
                                    case "EXCELLENT":
                                        youtubeUri = sparseAdaptiveVideoUrlList.get(sparseAdaptiveResolutionList.get(0));
                                        currentVideoSource = sparseAdaptiveResolutionList.get(0);
                                        break;
                                    case "UNKNOWN":
                                        youtubeUri = sparseAdaptiveVideoUrlList.get(sparseAdaptiveResolutionList.get(sparseAdaptiveResolutionList.size() - 1));
                                        currentVideoSource = sparseAdaptiveResolutionList.get(sparseAdaptiveResolutionList.size() - 1);
                                        break;
                                }
                            }

                            mDeviceBandwidthSampler.stopSampling();
                            /*endDate = new java.util.Date();
                            Log.e(TAG, "time taken to prepare video data: " + TimeUnit.MILLISECONDS.toSeconds((endDate.getTime() - startDate.getTime())));*/
                            Log.e(TAG, "Coming from youtube extractor, is finishing: " + isFinishing());
                            Log.e(TAG, "Coming from youtube extractor, is destroyed: " + isDestroyed());
                            if (!isFinishing() && !isDestroyed()) {
                                Log.e(TAG, "map youtube url done");
                                endTime = DateTime.now();
                                Duration duration4 = new Duration(startTime, endTime);
                                Log.e(TAG, "Duration to map -- success: " + duration4.getStandardSeconds());
                                initPlayer();
                            }
                        } else {
                            customSnackBar.showActionSnackBar("This video is not available.",
                                    "OK", CustomSnackBar.LENGTH_INDEFINITE, () -> {
                                        Log.e(TAG, "Video not found.");
                                        customSnackBar.dismiss();
                                        onBackPressed();
                                    });
                        }
                    }
                });
            }
        }).Extract(resLink);


    }catch (Exception e)
    {
        e.printStackTrace();
    }
    }


    private void extractJWPlayerUrl(String mediaId) {
        JWPlayerMediaExtractor.getAllJWPlayerLinks(mediaId, Utils.secureJWPlayer() ? Utils.token(mediaId) : "", new JWPlayerExtractorCallBack() {
            @Override
            public void onExtractorSuccess(int responseCode, HashMap<Integer, String> mediaResponseMap, String status) {
                sparseAdaptiveVideoUrlList = mediaResponseMap;
                sparseAdaptiveResolutionList = new ArrayList<>();
                sparseAdaptiveResolutionList.addAll(mediaResponseMap.keySet());
                if (sparseAdaptiveVideoUrlList != null && sparseAdaptiveVideoUrlList.size() > 0) {
                    if (VideoType.getType(status) == VideoType.LIVE) {
                        isLiveStream = true;
                        isHlsContent = true;
                        currentVideoSource = JWPlayerMediaExtractor.LIVE_URL_POS;
                        youtubeUri = sparseAdaptiveVideoUrlList.get(currentVideoSource);
                        liveStatusText.setVisibility(View.VISIBLE);
                    } else {
                        Collections.sort(sparseAdaptiveResolutionList, Collections.reverseOrder());

                        mDeviceBandwidthSampler.startSampling();

                        mConnectionClass = ConnectionClassManager.getInstance().getCurrentBandwidthQuality();

                        switch (mConnectionClass.name()) {
                            case "POOR":
                                if (sparseAdaptiveVideoUrlList.containsKey(180)) {
                                    youtubeUri = sparseAdaptiveVideoUrlList.get(180);
                                    currentVideoSource = 180;
                                } else if (sparseAdaptiveVideoUrlList.containsKey(270)) {
                                    youtubeUri = sparseAdaptiveVideoUrlList.get(270);
                                    currentVideoSource = 270;
                                } else if (sparseAdaptiveVideoUrlList.containsKey(360)) {
                                    youtubeUri = sparseAdaptiveVideoUrlList.get(360);
                                    currentVideoSource = 360;
                                } else {
                                    youtubeUri = sparseAdaptiveVideoUrlList.get(sparseAdaptiveResolutionList.get(0));
                                    currentVideoSource = sparseAdaptiveResolutionList.get(0);
                                }
                                break;
                            case "MODERATE":
                                if (sparseAdaptiveVideoUrlList.containsKey(540)) {
                                    youtubeUri = sparseAdaptiveVideoUrlList.get(540);
                                    currentVideoSource = 540;
                                } else if (sparseAdaptiveVideoUrlList.containsKey(360)) {
                                    youtubeUri = sparseAdaptiveVideoUrlList.get(360);
                                    currentVideoSource = 360;
                                } else if (sparseAdaptiveVideoUrlList.containsKey(270)) {
                                    youtubeUri = sparseAdaptiveVideoUrlList.get(270);
                                    currentVideoSource = 270;
                                } else if (sparseAdaptiveVideoUrlList.containsKey(180)) {
                                    youtubeUri = sparseAdaptiveVideoUrlList.get(180);
                                    currentVideoSource = 180;
                                } else {
                                    youtubeUri = sparseAdaptiveVideoUrlList.get(sparseAdaptiveResolutionList.get(0));
                                    currentVideoSource = sparseAdaptiveResolutionList.get(0);
                                }
                                break;
                            case "GOOD":
                                youtubeUri = sparseAdaptiveVideoUrlList.get(sparseAdaptiveResolutionList.get(0));
                                currentVideoSource = sparseAdaptiveResolutionList.get(0);
                                break;
                            case "EXCELLENT":
                                youtubeUri = sparseAdaptiveVideoUrlList.get(sparseAdaptiveResolutionList.get(0));
                                currentVideoSource = sparseAdaptiveResolutionList.get(0);
                                break;
                            case "UNKNOWN":
                                youtubeUri = sparseAdaptiveVideoUrlList.get(180);
                                currentVideoSource = 180;
                                break;
                        }

                        mDeviceBandwidthSampler.stopSampling();
                    }
                    if (!isFinishing() && !isDestroyed()) {
                        initPlayer();
                    }
                } else {
                    customSnackBar.showActionSnackBar("This video is not available.",
                            "OK", CustomSnackBar.LENGTH_INDEFINITE, () -> {
                                Log.e(TAG, "Video not found.");
                                customSnackBar.dismiss();
                                onBackPressed();
                            });
                }
            }

            @Override
            public void onExtractorError(int responseCode, String error, String status) {
                showRestartAlertDialog();
            }
        });
    }

    private void checkUrlValidity(HashMap<Integer, String> streamMap, List<Integer> resolutionList, final UrlValidityCheckerCallBack urlValidityCheckerCallBack) {
        refinedStreamMap = streamMap;
        refinedResolutionList = resolutionList;

        if (!isLiveStream && !isHlsContent && !isProgressive) {
            try {
                final int[] iteratorCount = {0};
                Iterator iterator = streamMap.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<Integer, String> streamPair = (Map.Entry) iterator.next();

                    new HTTPUrlChecker(new UrlCheckerCallBack() {
                        @Override
                        public void onWSStreamValidatorResponse(int responseCode) {
                            iteratorCount[0]++;
                            if (responseCode != 200) {
                                refinedStreamMap.remove(streamPair.getKey());
                                refinedResolutionList.remove(streamPair.getKey());
                            }

                            if (iteratorCount[0] >= streamMap.size()) {
                                Log.d(TAG, "valid resolutions: " + refinedResolutionList.toString());
                                urlValidityCheckerCallBack.onUrlValidatorResponse(refinedStreamMap, refinedResolutionList);
                            }
                        }
                    }).execute(streamPair.getValue());
                }
            } catch (Exception ex) {
                urlValidityCheckerCallBack.onUrlValidatorResponse(refinedStreamMap, refinedResolutionList);
            }
        } else {
            urlValidityCheckerCallBack.onUrlValidatorResponse(refinedStreamMap, refinedResolutionList);
        }
    }

    private void changeVideoSource() {
        if (currentVideoSource != 0) {
            if (sparseAdaptiveResolutionList != null && sparseAdaptiveResolutionList.size() > 0) {
                for (int i = 0; i < sparseAdaptiveResolutionList.size(); i++) {
                    if (sparseAdaptiveResolutionList.get(i) == currentVideoSource && videoFirstTry <= 1) {
                        youtubeUri = sparseAdaptiveVideoUrlList.get(currentVideoSource);
                        if (player != null) {
                            player.release();
                            player = null;
                            initPlayer();
                            return;
                        }
                    }
                    else {
                        if (sparseAdaptiveResolutionList.get(i) == currentVideoSource) {
                            if ((i + 1) < sparseAdaptiveResolutionList.size()) {
                                youtubeUri = sparseAdaptiveVideoUrlList.get(sparseAdaptiveResolutionList.get(i + 1));
                                currentVideoSource = sparseAdaptiveResolutionList.get(i + 1);
                                if (player != null) {
                                    player.release();
                                    player = null;
                                    initPlayer();
                                    return;
                                }
                            } else if (((i - 1) < sparseAdaptiveResolutionList.size()) && ((i - 1) >= 0)) {
                                youtubeUri = sparseAdaptiveVideoUrlList.get(sparseAdaptiveResolutionList.get(i - 1));
                                currentVideoSource = sparseAdaptiveResolutionList.get(i - 1);
                                if (player != null) {
                                    player.release();
                                    player = null;
                                    initPlayer();
                                    return;
                                }
                            }
                        }
                    }
                }
                VideoPopUpMenuAdapter.selectedPlayBackSpeed = 3;
            }
        } else {
            startTime = DateTime.now();
            if (player != null) {
                player.release();
                if (resLink != null && !resLink.isEmpty() && (!resLink.contains("/") || resLink.contains("youtube") || resLink.contains("youtu.be"))) {
                    isJWPlayer = false;
                    isVimeo = false;
                    Log.e(TAG, "extract yotube url");
                    extractYoutubeUrl();
                }
            }
        }
    }

    private void initPlayer() {

        try {
            videoLoader.setVisibility(View.GONE);
            videoLoadingText.setVisibility(View.GONE);
            Log.e(TAG, "init player started");
            //startTime = DateTime.now();
            Uri videoUri = Uri.parse(youtubeUri);
            //videoUri = Uri.parse(tempLiveUrl);
            if (sparseOPUSAudioUrl != null && !sparseOPUSAudioUrl.isEmpty()) {
                audioUri = Uri.parse(sparseOPUSAudioUrl.get(0));
            }

            if (player != null) {
                player.release();
                player = null;
            }

            DefaultTrackSelector trackSelector = new DefaultTrackSelector();

            /*if (isLiveStream) {
                player = ExoPlayerFactory.newSimpleInstance(this, new DefaultRenderersFactory(this),
                        trackSelector, new DefaultLoadControl());
            } else {
                player = ExoPlayerFactory.newSimpleInstance(this, new DefaultRenderersFactory(this), trackSelector, loadControl);
            }*/

            if (isHlsContent) {
                //HlsMasterPlaylist playlist = new HlsMasterPlaylist(videoUri);
                mediaSource = new HlsMediaSource.Factory(dataSourceFactory).createMediaSource(MediaItem.fromUri(videoUri));
            } else if (isProgressive) {
                mediaSource = new ProgressiveMediaSource.Factory(dataSourceFactory).createMediaSource(MediaItem.fromUri(videoUri));
            } else if (isJWPlayer) {
                if (videoUri.toString().contains("m3u8")) {
                    mediaSource = new HlsMediaSource.Factory(dataSourceFactory).createMediaSource(MediaItem.fromUri(videoUri));
                    playQualityBtn.setVisibility(View.GONE);
                } else {
                    mediaSource = new ProgressiveMediaSource.Factory(dataSourceFactory).createMediaSource(MediaItem.fromUri(videoUri));
                    playQualityBtn.setVisibility(View.VISIBLE);
                }
            } else if (isVimeo) {
                if (videoUri.toString().contains("m3u8")) {
                    mediaSource = new HlsMediaSource.Factory(dataSourceFactory).createMediaSource(MediaItem.fromUri(videoUri));
                    playQualityBtn.setVisibility(View.GONE);
                } else {
                    mediaSource = new ProgressiveMediaSource.Factory(dataSourceFactory).createMediaSource(MediaItem.fromUri(videoUri));
                    playQualityBtn.setVisibility(View.VISIBLE);
                }
            } else {
                ProgressiveMediaSource videoSource = new ProgressiveMediaSource.Factory(dataSourceFactory).createMediaSource(MediaItem.fromUri(videoUri));
                ProgressiveMediaSource audioSource = new ProgressiveMediaSource.Factory(dataSourceFactory).createMediaSource(MediaItem.fromUri(audioUri));
                mediaSource = new MergingMediaSource(videoSource, audioSource);
            }

            playerInitialized = true;

            player.setPlayWhenReady(true);

            videoPlayer.setPlayer(player);

            if (!isLiveStream) {
                try {
                    if (playPosition > 0) {
                        haveCustomStartPosition = true;
                        player.seekTo(playPosition);
                    } else if (!WonderPubSharedPrefs.getInstance(CustomMediaPlayer.this).getVideoResumePositionList().isEmpty()) {
                        HashMap<String, Long> videoResumePosList = new HashMap<>();
                        String[] pairs = WonderPubSharedPrefs.getInstance(CustomMediaPlayer.this).getVideoResumePositionList().split(",");
                        for (String pair : pairs) {
                            int index = pair.lastIndexOf("=");
                            String link = pair.substring(0, index);
                            String videoPlayBackPosition = pair.substring(index).replace("=", "");
                            //String[] keyValue = pair.split("=");
                            videoResumePosList.put(link.replace("{", "").trim(),
                                    Long.valueOf(videoPlayBackPosition.replace("}", "").trim()));
                        }
                        if (!videoResumePosList.isEmpty()) {
                            if (videoResumePosList.get(resLink) != null && videoResumePosList.get(resLink) > 0) {
                                haveCustomStartPosition = true;
                                player.seekTo(videoResumePosList.get(resLink));
                            }
                        }
                    } else {
                        haveCustomStartPosition = false;
                        player.seekTo(0);
                    }
                } catch (NumberFormatException e) {
                    haveCustomStartPosition = false;
                    player.seekTo(0);
                }
            }

            Log.e(TAG, "prepare player done");
            endTime = DateTime.now();
            Duration duration4 = new Duration(startTime, endTime);
            Log.e(TAG, "Duration to map -- success: " + duration4.getStandardSeconds());
            if (isLiveStream) {
                player.prepare(mediaSource);
            } else {
                player.prepare(mediaSource, !haveCustomStartPosition, false);
            }

            Log.e(TAG, "preparing player started");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private int getLiveIndexResolution(int index) {
        switch (index) {
            case 0:
                return 144;
            case 1:
                return 240;
            case 2:
                return 360;
            case 3:
                return 480;
            case 4:
                return 720;
            case 5:
                return 1080;
            case 6:
                return 1440;
        }
        return 144;
    }
    @Override
    public void onPause() {
        if (player != null) {
            mDeviceBandwidthSampler.stopSampling();
            playPosition = player.getCurrentPosition();
            player.release();
            player = null;
        }
        super.onPause();
        if (keyboardHeightProvider != null) {
            keyboardHeightProvider.setKeyboardHeightObserver(null);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.e(TAG, "On Destroy");
        Log.e(TAG, "On Destroy player " + player);
        if (player != null) {
            if (!isLiveStream && (player.getCurrentPosition() != player.getDuration()) && (player.getCurrentPosition() != 0)) {
                WonderPubSharedPrefs.getInstance(CustomMediaPlayer.this).setVideoResumePosition(player.getCurrentPosition(), resLink);
            }
            mDeviceBandwidthSampler.stopSampling();
            player.release();
            videoPlayer.setPlayer(null);
            player = null;
        }
        playPosition = 0;
        youtubeUri = "";
        currentVideoSource = 0;
        playStatus = true;
        newOrientation = 0;
        VideoPopUpMenuAdapter.selectedPlayBackSpeed = 3;
        playerInitialized = true;
        videoPlayer = null;
        if (keyboardHeightProvider != null) {
            keyboardHeightProvider.close();
        }
    }

    @Override
    public void onResume() {
        try {
            if (player == null) {
                if (currentVideoSource != 0) {
                    if (sparseAdaptiveVideoUrlList != null) {
                        youtubeUri = sparseAdaptiveVideoUrlList.get(currentVideoSource);
                        initPlayer();
                    }
                }
            }
            super.onResume();
            if (keyboardHeightProvider != null) {
                keyboardHeightProvider.setKeyboardHeightObserver(this);
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    @Override
    public void onKeyboardHeightChanged(int height, int orientation) {

    }

    @Override
    public void onPictureInPictureModeChanged(boolean isInPictureInPictureMode, Configuration newConfig) {
    }
    /**
     * NOTE: Only use this for Vimeo live links
     * <p>
     * Update the streaming links(for different resolutions) from
     * the Manifest specifically used for Vimeo live links
     *
     * @param manifest of type HlsManifest from which links will be extracted
     */
    private void updateStreamLinksFrom(HlsManifest manifest) {

        if (manifest.masterPlaylist.variants != null) {

            HashMap<Integer, String> urlsMap = new HashMap<>();

            //For log
            StringBuilder builder = new StringBuilder();
            builder.append("found -> ");

            //Check for links
            for (HlsMasterPlaylist.Variant variant : manifest.masterPlaylist.variants) {

                if (variant != null) {

                    //For log
                    builder.append("\n")
                            .append(variant.format.height)
                            .append("|")
                            .append(variant.url.toString());

                    try {
                        urlsMap.put(variant.format.height, variant.url.toString());
                    } catch (NullPointerException e) {
                        android.util.Log.e(TAG, "updateStreamLinksFrom: ", e);
                    }
                }

            }

            //For log
            android.util.Log.e(TAG, "Resolutions " + builder);


            //Update lists
            if (sparseAdaptiveVideoUrlList == null)
                sparseAdaptiveVideoUrlList = new HashMap<>();

            if (sparseAdaptiveResolutionList == null)
                sparseAdaptiveResolutionList = new ArrayList<>();

            sparseAdaptiveVideoUrlList.putAll(urlsMap);
            sparseAdaptiveResolutionList.addAll(urlsMap.keySet());

        }

    }


    private void retrieveResLink() {
        resLink = getIntent().getStringExtra("resLink");
        if(resLink != null) {
            resLink = Uri.parse(resLink.trim()).toString();
            try {
                resLink = URLDecoder.decode(resLink, StandardCharsets.UTF_8.name());
            } catch (UnsupportedEncodingException e) {
                Log.e(TAG, "Exception while getting res link");
            }
        }
    }
}
