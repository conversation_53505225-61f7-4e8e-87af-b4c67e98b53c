package com.wonderslate.prepjoy.ui.videos;

public enum VideoType {
    UNKNOWN("unknown"), VOD("vod"), LIVE("live");

    private String text = "";
    VideoType(String text) {
        this.text = text;
    }

    public String getText() {
        return text;
    }

    public static VideoType getType(String text) {
        for(VideoType type: VideoType.values()) {
            if(type.text.equalsIgnoreCase(text))
                return type;
        }
        return UNKNOWN;
    }
}
