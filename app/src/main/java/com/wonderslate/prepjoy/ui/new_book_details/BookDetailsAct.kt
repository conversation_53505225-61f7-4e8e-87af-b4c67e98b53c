package com.wonderslate.prepjoy.ui.new_book_details

import android.content.Context
import android.content.Intent
import android.util.Log
import com.wonderslate.data.helper.ServerTimeHelper
import com.wonderslate.data.interfaces.WSResponseCallback
import com.wonderslate.data.network.ServerTimeCallBack
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.data.repository.WSBookStore
import com.wonderslate.prepjoy.R
// import com.wonderslate.prepjoy.Utils.CartDialog
import com.wonderslate.prepjoy.Utils.CustomSnackBar
import com.wonderslate.prepjoy.Utils.FirebaseAnalyticsUtils
import com.wonderslate.prepjoy.Utils.FlavourHelper
import com.wonderslate.prepjoy.Utils.Utils
import com.wonderslate.prepjoy.databinding.ActivityBookDetailsBinding
import com.wonderslate.prepjoy.ui.chapters_list.ChaptersListAct
import com.wonderslate.prepjoy.ui.dashboard.DashBoardActivity
// import com.wonderslate.prepjoy.ui.shoppingcart.ShoppingCartIcon
import com.ws.book_details.data.models.BookDetails
import com.ws.chapter_list.data.models.ChaptersListConfig
import com.ws.commons.ErrorMessageConstants
import com.ws.commons.enums.EventType
import com.ws.commons.extensions.isFreeBook
import com.ws.commons.interfaces.EventLogger
import com.ws.commons.interfaces.UserDetailsProvider
import com.ws.commons.models.Event
import com.ws.core_ui.base.ActBase
import com.ws.core_ui.extensions.finishWithAnim
import com.ws.core_ui.extensions.replaceFragment
import com.ws.core_ui.extensions.showToast
import com.ws.core_ui.extensions.startActivityWithAnim
import com.ws.core_ui.utils.IntentConstants.PARAM_BOOK_DETAILS
import com.ws.core_ui.utils.TempConfig
import com.ws.library.books.data.models.LibraryBooksRequest
import com.ws.purchase.data.models.PaymentDetails
import com.ws.purchase.ui.fragment.BookAddedFrag
import com.ws.purchase.ui.fragment.PaymentFrag
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import org.json.JSONObject
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class BookDetailsAct: ActBase<ActivityBookDetailsBinding>(),
    PaymentFrag.PaymentScreenListener,
    BookAddedFrag.OnBookAddedFragListener,
    BookDetailsFragV2.OnBookDetailsFragV2InteractionListener{

    private lateinit var fragBookDetails: BookDetailsFragV2

    private lateinit var bookDetails: BookDetails

    private var paymentFrag: PaymentFrag? = null

    private val viewModel by viewModel<BookDetailsActViewModel>()

    private val userDetailsProvider by inject<UserDetailsProvider>()

    private var customSnackBar: CustomSnackBar? = null

    private var analyticsUtils: FirebaseAnalyticsUtils? = null

    private val eventLogger by inject<EventLogger>()

    // private var shoppingCartIcon: ShoppingCartIcon? = null

    override fun bindView(): ActivityBookDetailsBinding {
        return ActivityBookDetailsBinding.inflate(layoutInflater)
    }

    override fun init() {
        supportActionBar?.hide()
        customSnackBar = CustomSnackBar(this, binding?.root)
        bookDetails = intent.getSerializableExtra(PARAM_BOOK_DETAILS)?.let {
            it as BookDetails
        } ?: BookDetails(-1)
        fragBookDetails = BookDetailsFragV2.newInstance(bookDetails)
        replaceFragment(
            R.id.fragmentContainer,
            fragBookDetails
        )
        //Server time invocation for Affiliation Code validation
        ServerTimeHelper.newInstance.getServerTime(object : ServerTimeCallBack {
            override fun onSuccess(time: LocalDateTime) {
            }

            override fun onFailed(resString: String?, responseCode: Int) {
            }

        })
        // shoppingCartIcon = ShoppingCartIcon(this, findViewById(R.id.flCart))
    }

    override fun onOpenInLibrary(bookDetails: BookDetails) {
        analyticsUtils?.logBookDetailsEventUTM("Open In Library", this.bookDetails.utmSource, this.bookDetails.utmCampaign)
        actScope.launch {
            if(viewModel.bookAvailableOffline(bookDetails.bookId)) {
                openBook(bookDetails)
            } else {
                refreshLibrary(
                    onSuccess = {
                        fragBookDetails.load()
                        openBook(bookDetails)
                    },
                    onError = {
                        supportFragmentManager.popBackStackImmediate()
                        showToast(ErrorMessageConstants.updatingLibraryBooksError)
                    }
                )
            }
        }
    }

    override fun onPreviewBook(bookDetails: BookDetails) {
        analyticsUtils?.logBookDetailsEventUTM("Open Preview", this.bookDetails.utmSource, this.bookDetails.utmCampaign)
        openBook(bookDetails, true)
    }

    override fun onAddBookToLibrary(bookDetails: BookDetails) {
        analyticsUtils?.logBookDetailsEventUTM("Add To Library", this.bookDetails.utmSource, this.bookDetails.utmCampaign)
        paymentFrag = PaymentFrag.newInstance(
            PaymentDetails(
                bookName = bookDetails.bookName ?: "",
                bookId = bookDetails.bookId.toString(),
                coverImage = bookDetails.coverImage ?: "",
                author = bookDetails.publisherName ?: "",
                amount = (if (bookDetails.bookType.equals("eBook", true)) {
                    bookDetails.amount
                } else {
                    bookDetails.testsPrice
                }).toString(),
                organization = FlavourHelper.companyName(),
                logo = R.drawable.app_icon,
                description = bookDetails.desc,
                themeColor = "#F05A2A",
                bookType = bookDetails.bookType ?: "book"
            )
        )
        replaceFragment(
            R.id.fragmentContainer,
            paymentFrag!!
        ) {
            addToBackStack(PAYMENT_BACKSTACK_TAG)
        }
    }

    override fun onAddBookToCart(bookDetails: BookDetails) {
        analyticsUtils?.logBookDetailsEventUTM("Add To Cart", this.bookDetails.utmSource, this.bookDetails.utmCampaign)
        if (bookDetails?.bookType.equals("bookGPT", ignoreCase = true)) {
            bookDetails?.bookType = "bookGPT"
        } else if (bookDetails?.bookType.equals("eBook", ignoreCase = true) || bookDetails?.bookType.equals(
                "eBookWithAI",
                ignoreCase = true
            )
        ) {
            bookDetails?.bookType = "eBook"
        }
        val bookStore = WSBookStore()
        bookStore.addBookToCart(bookDetails.bookId.toString(), bookDetails.bookType, object :
            WSResponseCallback<JSONObject?> {
            override fun onWSResultSuccess(response: JSONObject?, responseCode: Int) {
                val status = response?.optString("status")
                fragBookDetails.isUpgradeMode = false
                if ("OK".equals(status, ignoreCase = true)) {
                    // Shopping cart functionality temporarily disabled for demo
                    // val count: Int = WonderPubSharedPrefs.getInstance(this@BookDetailsAct).cartCount
                    // WonderPubSharedPrefs.getInstance(this@BookDetailsAct).cartCount = count + 1
                    // shoppingCartIcon?.openCart()
                    showToast("Book added successfully!")
                } else if ("Already exist".equals(status, ignoreCase = true)) {
                    // CartDialog functionality temporarily disabled for demo
                    showToast("Book already exists in cart!")
                }
            }

            override fun onWSResultFailed(resString: String?, responseCode: Int) {
                Utils.showErrorToast(this@BookDetailsAct, responseCode)
            }
        })
    }

    override fun onBookAddedByAccessCode(bookDetails: BookDetails) {
        showToast("Book is added to your library successfully.")
        refreshLibrary(
            onSuccess = {
                showBookAddedSuccess(bookDetails)
            },
            onError = {
                showToast("Problem while refreshing library. Please try again.")
            }
        )
    }

    private fun showBookAddedSuccess(bookDetails: BookDetails) {
        replaceFragment(
            R.id.fragmentContainer,
            BookAddedFrag.newInstance(
                PaymentDetails(
                    bookName = bookDetails.bookName ?: "",
                    bookId = bookDetails.bookId.toString(),
                    coverImage = bookDetails.coverImage ?: "",
                    author = bookDetails.publisherName ?: "",
                    amount = bookDetails.amount,
                    organization = getString(R.string.app_name),
                    logo = R.mipmap.ic_launcher,
                    description = bookDetails.desc,
                    themeColor = "#F05A2A",
                    bookType = bookDetails.bookType ?: "book"
                )
            )
        ) {
            addToBackStack(BOOK_ADDED_BACKSTACK_TAG)
        }
    }

    override fun onBookDetailsChanged(bookDetails: BookDetails) {
        this.bookDetails = bookDetails
    }

    override fun onPaymentProcessStarted(paymentDetails: PaymentDetails) {
        analyticsUtils?.logBookDetailsEventUTM("Payment Started", this.bookDetails.utmSource, this.bookDetails.utmCampaign)
        eventLogger.logEvent(
            Event(
                type = EventType.BUY_NOW_BUTTON_CLICKED,
                currentScreen = "Book details page",
                value = Pair("BookId", paymentDetails.bookId)
            )
        )
    }

    override fun onPurchaseSuccess(paymentId: String?, paymentDetails: PaymentDetails): Boolean {
        analyticsUtils?.logBookDetailsEventUTM("Payment Success", this.bookDetails.utmSource, this.bookDetails.utmCampaign)

        if(userDetailsProvider.getUserEmail().isBlank()) {
            viewModel.updateUserDetails()
        }

        eventLogger.logEvent(
            Event(
                type = EventType.PAYMENT_SUCCESS,
                currentScreen = "Book details page",
                value = Pair("BookId", paymentDetails.bookId)
            )
        )
        return false
    }

    override fun bookAlreadyPurchased(bookId: String) {
        onOpenInLibrary(BookDetails(bookId.toInt()))
    }

    override fun onStartReading(bookId: String) {
        onOpenInLibrary(BookDetails(bookId = bookId.toInt()))
    }

    override fun onPaymentCancel(response: String?) {
        analyticsUtils?.logBookDetailsEventUTM("Payment Cancelled", this.bookDetails.utmSource, this.bookDetails.utmCampaign)
        var message = "Payment cancelled"
        try {
            val jsonObject = JSONObject(response ?: "{}")
            val msg = jsonObject.optJSONObject("error")?.optString("description")
            if (!msg.isNullOrBlank()) {
                message = msg
            }
        } catch (e: Exception) {
            Log.e("BookDetailsAct", "onPaymentCancel: ", e)
        } finally {
            supportFragmentManager.popBackStackImmediate()
            customSnackBar?.show(message, CustomSnackBar.LENGTH_LONG)
        }
    }

    override fun getPaymentResultListener(): PaymentFrag? {
        return paymentFrag
    }

    private fun openBook(bookDetails: BookDetails, isPreview: Boolean = false) {
        if(isPreview) {
            eventLogger.logEvent(
                Event(
                    type = EventType.PREVIEW_BUTTON_CLICKED,
                    currentScreen = "Book details page",
                    value = Pair("BookId", bookDetails.bookId.toString())
                )
            )
        }

        //Hide payment page if showing
        if(supportFragmentManager.backStackEntryCount >= 1) {
            supportFragmentManager.popBackStackImmediate()
        }

        TempConfig.isEnglishBook = bookDetails.language.equals("english", true) || bookDetails.language.isEmpty()

        startActivityWithAnim(
            ChaptersListAct.createIntent(
                this,
                ChaptersListConfig(
                    bookId = bookDetails.bookId,
                    isPreview = if(bookDetails.amount.isFreeBook()) false else isPreview,
                    bookName = bookDetails.bookName ?: "",
                    bookCoverImage = bookDetails.coverImage ?: "",
                    isFromShop = true,
                    bookType = bookDetails.bookType ?: ""
                )
            )
        )
    }

    private fun refreshLibrary(
        onSuccess: () -> Unit,
        onError: () -> Unit
    ) = actScope.launch {
        viewModel.refreshLibraryBooks(LibraryBooksRequest(forceRefresh = true)) { isSuccessful ->
            if (isSuccessful)
                onSuccess()
            else
                onError()
        }
    }

    override fun onBackButtonPressed() {
        onBackPressed()
    }

    override fun onBackPressed() {
        analyticsUtils?.logBookDetailsEventUTM("Back Press", this.bookDetails.utmSource, this.bookDetails.utmCampaign)
        if(supportFragmentManager.backStackEntryCount == 0) {
            if (bookDetails.isDeepLinked) {
                val dashboard = Intent(
                    this@BookDetailsAct,
                    DashBoardActivity::class.java
                )
                if (bookDetails.isDeepLinked) {
                    dashboard.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                }
                startActivity(dashboard)
            }
            finishWithAnim()
        }
    }

    override fun onShareButtonClicked(bookDetails: BookDetails) {
        //TODO: in next release
    }


    companion object {
        private const val BOOK_ADDED_BACKSTACK_TAG = "bookAddSuccess"
        private const val PAYMENT_BACKSTACK_TAG = "payment"

        @JvmStatic
        fun createIntent(context: Context, bookDetails: BookDetails) = Intent(context, BookDetailsAct::class.java).also {
            it.putExtra(PARAM_BOOK_DETAILS, bookDetails)
            it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
        }
    }

}