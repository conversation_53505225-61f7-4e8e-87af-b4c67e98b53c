package com.wonderslate.prepjoy.ui.analytics

import android.app.Activity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.annotation.NonNull
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
// import butterknife.BindView
// import butterknife.ButterKnife
import com.wonderslate.prepjoy.R
// import com.wonderslate.prepjoy.ui.groupwall.GroupWallFragment

class AnalyticsPaginationAdapter(contexts: AnalyticsFragment, activity: Activity) : RecyclerView.Adapter<AnalyticsPaginationAdapter.ViewHolder?>() {

    var mData: List<Int>? = null
    var mInflater: LayoutInflater? = null
    var context: AnalyticsFragment? = null
    var selectPosition = 0
    var mContext: Activity? = null

    init {
        this.context = contexts
        this.mContext = activity
        this.mInflater = LayoutInflater.from(mContext)
    }

    fun setEntries(list: List<Int>, currentPosition: Int) {
        this.mData = list
        this.selectPosition = currentPosition
        notifyDataSetChanged()
    }

    @NonNull
    override fun onCreateViewHolder(@NonNull parent: ViewGroup, viewType: Int): ViewHolder{
        val view: View = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_post_pagination, parent, false)
        return ViewHolder(view)
    }

  override  fun onBindViewHolder(holder: ViewHolder, position: Int) {
        try {
            val data = mData!![position] /20 +1
            holder.textviewPageNumber.setText("" + data)

            if ((selectPosition/20 -1 +1 ) == position) {
                holder.relativeLayoutContainer.setBackgroundResource(R.drawable.bg_round_scrore)
                holder.textviewPageNumber.setTextColor(
                    ContextCompat.getColor(
                        mContext!!, R.color.white
                    )
                )
            } else {
                holder.relativeLayoutContainer.setBackgroundResource(R.drawable.button_background_light_rounded)
                holder.textviewPageNumber.setTextColor(
                    ContextCompat.getColor(
                        mContext!!, R.color.black
                    )
                )
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

  override  fun getItemCount(): Int {
        return mData!!.size
    }

  override  fun getItemViewType(position: Int): Int {
        return position
    }


  inner  class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView), View.OnClickListener
  {

      val relativeLayoutContainer: RelativeLayout = itemView.findViewById(R.id.layout_container)
      val textviewPageNumber : TextView = itemView.findViewById(R.id.textView_page_number)

        override fun onClick(view: View) {
            try {
                when (view.id) {
                    R.id.layout_container -> {
                        if(adapterPosition == 0)
                        {
                            val page = mData!![adapterPosition]
                            context!!.paginationClicked(mData!![page])
                        }
                        else{
                            val page = mData!![adapterPosition]
                            context!!.paginationClicked(page)
                        }
                    }
                    else -> {}
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        init {
            // ButterKnife.bind(this, itemView)
            relativeLayoutContainer!!.setOnClickListener(this)
            itemView.setOnClickListener(this)
        }
    }

    interface ItemClickListener {
        fun onItemClick(view: View?, position: Int)
    }
}
