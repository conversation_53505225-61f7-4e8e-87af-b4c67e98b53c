package com.wonderslate.prepjoy.ui.library.adapters

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ws.commons.interfaces.ImageUrlProvider
import com.ws.commons.models.BookCoverUrlRequest
import com.ws.core_ui.extensions.loadImage
import com.ws.core_ui.extensions.newLayoutInflater
import com.ws.database.room.entity.LibraryBook
import com.ws.library.books.databinding.ItemPackageBookBinding
import java.lang.Exception

class PackageBookAdapter(
    private val packageBookList: List<LibraryBook>,
    private val imageUrlProvider: ImageUrlProvider
) : RecyclerView.Adapter<PackageBookAdapter.ViewHolder>() {

    var onBookClick: ((LibraryBook) -> Unit)? = null

    override fun onCreateViewHolder(viewGroup: ViewGroup, i: Int): ViewHolder {
        return ViewHolder(ItemPackageBookBinding.inflate(viewGroup.context.newLayoutInflater(), viewGroup, false))
    }

    override fun onBindViewHolder(viewHolder: ViewHolder, i: Int) {
        try {
            viewHolder.binding.tvBookName.text = packageBookList[i].title

            var coverImgLoc = ""

            coverImgLoc = if (packageBookList[i].coverImage.startsWith("https://")) {
                packageBookList[i].coverImage
            } else {
                imageUrlProvider.getBookCoverUrl(BookCoverUrlRequest(packageBookList[i].coverImage,
                        packageBookList[i].id.toString()))
            }
            viewHolder.binding.ivBookCover.loadImage(coverImgLoc, animate = true)

            viewHolder.binding.cardBook.setOnClickListener {
                onBookClick?.invoke(packageBookList[i])
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun getItemCount(): Int {
        return packageBookList.size
    }

    inner class ViewHolder(val binding: ItemPackageBookBinding) : RecyclerView.ViewHolder(binding.root)

    companion object {
        private const val TAG = "PackageBookAdapter"
    }
}