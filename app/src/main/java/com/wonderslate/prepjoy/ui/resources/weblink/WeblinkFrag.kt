package com.wonderslate.prepjoy.ui.resources.weblink

import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.webkit.WebView
import androidx.core.os.bundleOf
import com.ws.commons.extensions.toDecoded
import com.ws.core_ui.base.BaseFragmentWithListener
import com.ws.core_ui.extensions.showToast
import com.ws.core_ui.extensions.visibility
import com.ws.database.room.entity.Resource
import com.ws.resources.databinding.LayoutWebLinkFragBinding
import com.ws.resources.ui.configure
import com.ws.resources.ui.handleUrlLoading


class WeblinkFrag : BaseFragmentWithListener<LayoutWebLinkFragBinding, WeblinkFrag.OnWeblinkFragInteractionListener>() {

    companion object {

        const val ARG_RESOURCE_DATA = "argResData"

        fun newInstance(resource: Resource) = WeblinkFrag().also {
            it.arguments = bundleOf(
                ARG_RESOURCE_DATA to resource
            )
        }
    }

    private var resource: Resource? = null

    override fun inflateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): LayoutWebLinkFragBinding {
        return LayoutWebLinkFragBinding.inflate(inflater, container, false)
    }

    override fun initArguments(bundle: Bundle?) {
        resource = bundle?.getSerializable(ARG_RESOURCE_DATA) as Resource
    }

    override fun initView() {
        initHeader()
        configureWebView()
        initClicks()
    }

    private fun initHeader() {
        binding?.tvResourceName?.text = resource?.resName?.toDecoded()
        if (resource?.resLink?.contains("displayFlashCards") == true) {
            binding?.btnNextPage?.visibility = View.GONE
            binding?.btnPreviousPage?.visibility = View.GONE
            resource?.resLink = resource?.resLink + "&prepjoyApp=true"
        }
    }

    private fun configureWebView() {
        binding?.pbProcessing?.max = 100
        binding?.webView?.configure(true) { newProgress ->
            binding?.pbProcessing?.progress = newProgress
            binding?.pbProcessing?.visibility(newProgress != 100)
            binding?.lvCenter?.visibility(newProgress != 100)
        }
        binding?.webView?.handleUrlLoading()
        binding?.webView?.settings?.javaScriptEnabled = true
        binding?.webView?.settings?.domStorageEnabled = true

        binding?.webView?.setOnTouchListener(View.OnTouchListener { v, event ->
            // Pass touch events as they are to the WebView.
            if ((event.action == MotionEvent.ACTION_DOWN) ||
                (event.action == MotionEvent.ACTION_UP)
            ) {
                v.requestFocusFromTouch()
            }
            false // Return false to let WebView handle the event itself.
        })

        val flashCardJSInterface = createJSInterface(binding?.webView)
        flashCardJSInterface.connectInterface()
    }

    private fun initClicks() {
        binding?.btnClose?.setOnClickListener {
            listener?.onCloseWebView()
        }

        binding?.btnPreviousPage?.setOnClickListener {
            if(binding?.webView?.canGoBack() == true) {
                binding?.webView?.goBack()
            }
        }

        binding?.btnNextPage?.setOnClickListener {
            if(binding?.webView?.canGoForward() == true) {
                binding?.webView?.goForward()
            }
        }

        binding?.btnRefresh?.setOnClickListener {
            binding?.webView?.reload()
        }
    }

    override fun load() {
        resource?.resLink?.let {
            binding?.webView?.loadUrl(it.toDecoded())
        } ?: run {
            showToast("Problem while opening this link. Please try again.")
            listener?.onCloseWebView()
        }
    }

    interface OnWeblinkFragInteractionListener {
        fun onCloseWebView()
    }

    private fun createJSInterface(webView: WebView?): JSInterface {
        return JSInterface(webView)
    }

}