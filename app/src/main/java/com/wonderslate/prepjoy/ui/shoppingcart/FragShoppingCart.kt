package com.wonderslate.prepjoy.ui.shoppingcart

import android.animation.Animator
import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import androidx.recyclerview.widget.SimpleItemAnimator
import com.android.wslibrary.models.cart.CartBook
import com.wonderslate.commons.Status
import com.wonderslate.data.interfaces.WSCallback
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.data.repository.WSBookStore
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.Utils.CartDialog
import com.wonderslate.prepjoy.Utils.CustomSnackBar
import com.wonderslate.prepjoy.Utils.InternetConnectionChecker
import com.wonderslate.prepjoy.Utils.PaymentSuccessDialog
// import kotlinx.android.synthetic.main.frag_shopping_cart.*
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.io.Serializable
import java.util.regex.Matcher
import java.util.regex.Pattern

class FragShoppingCart : Fragment() {

    private var listener: OnFragShoppingCartListener? = null

    private lateinit var viewModel: FragShoppingCartViewModel

    private var config: Config? = null

    private lateinit var adapter: ShoppingCartBooksAdapter

    private var isShippingDetailsCorrect = true
    private var isBillingDetailsCorrect = true
    private var isBillingAddressSame = true

    private val internetConnectionChecker by lazy { InternetConnectionChecker() }

    private var addressMap: HashMap<String, String> = HashMap()
    private var totalPrice = 0.0
    private var totalBookPrice = 0.0
    private var isPrintBook: Boolean = false

    private val VALID_EMAIL_ADDRESS_REGEX: Pattern =
        Pattern.compile("^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,6}$", Pattern.CASE_INSENSITIVE)

    private val customSnackBar by lazy {
        CustomSnackBar(requireContext(), view?.findViewById(R.id.content))
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.frag_shopping_cart, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel = ViewModelProvider(this).get(FragShoppingCartViewModel::class.java)

        config = arguments?.getSerializable(SHOPPING_CART_CONFIG) as? Config

        updateTitle(WonderPubSharedPrefs.getInstance(requireContext()).cartCount)

        initRecycler()

        initObservers()

        initClicks()

        initAnimations()
    }

    override fun onResume() {
        super.onResume()
        getFragmentManager()?.beginTransaction()?.detach(this)?.attach(this)?.commit()
        resetPrintFields()
        refreshCartItems(true)
    }

    private fun initObservers() {
        viewModel.cartBooks.observe(viewLifecycleOwner) { data ->
            when (data?.responseType) {
                Status.LOADING -> {
                    cartLoader?.visibility = View.VISIBLE
                    hideMessage()
                    hideBottomButtons()
                }

                Status.SUCCESSFUL -> {
                    cartLoader?.visibility = View.GONE
                    val books = (data.data ?: listOf())
                    books.forEach {
                        if (it.bookType == "printbook" || it.bookType == "combo") {
                            isPrintBook = true
                        }
                    }
                    val size = books.size
                    updateTitle(size)
                    adapter.update(books)
                    recCartBooks?.visibility = if(size == 0) View.GONE else View.VISIBLE
                    btnBuy?.visibility = if (size == 0) View.GONE else View.VISIBLE
                    if (size == 0)

                        showMessage(EMPTY_CART_MSG)
                    else
                        hideMessage()
                }

                else -> {
                    cartLoader?.visibility = View.GONE
                    recCartBooks?.visibility = View.GONE
                    isPrintBook = false
                    // show error message
                    showMessage(PROBLEM_MSG)
                    Toast.makeText(requireContext(), "Problem while getting cart details", Toast.LENGTH_SHORT).show()
                }
            }
        }

        viewModel.priceData.observe(viewLifecycleOwner) { data ->
            llDiscount?.visibility = if (data.totalDiscount > 0) View.VISIBLE else View.GONE
            llTotal?.visibility = if (data.totalPrice > 0) View.VISIBLE else View.GONE

            val savedTemplate = "You saved ₹ %.2f"
            tvTotalDiscount?.text = savedTemplate.format(data.totalDiscount)

            val priceTemplate = "₹ %.2f"
            tvTotal?.text = priceTemplate.format(data.totalDiscountedPrice)
            totalBookPrice = data.totalDiscountedPrice
            tvSummarySubTotal?.text = priceTemplate.format(data.totalPrice)
            tvSummaryDiscount?.text = priceTemplate.format(data.totalDiscount)
            tvSummaryTotal?.text = priceTemplate.format(data.totalDiscountedPrice)
            tvSummaryShipping?.text = "-"

            if (data.isNewDiscountAdded)
                startConfettiAnimation()

        }
    }

    private fun resetPrintFields() {
        llAddress?.visibility = View.GONE
        btnBuy?.text = "Proceed to Buy"
        isShippingDetailsCorrect = true
        isBillingDetailsCorrect = true
        isBillingAddressSame = true
        addressMap.clear()
    }

    private fun initRecycler() {
        adapter = ShoppingCartBooksAdapter(arrayListOf())
        recCartBooks?.adapter = adapter
        (recCartBooks?.itemAnimator as SimpleItemAnimator).supportsChangeAnimations = false
    }


    private fun initClicks() {

        llTotal?.setOnClickListener {
            if(llOrderSummary?.visibility == View.VISIBLE) {
                tvViewSummary?.text = "View Summary"
                llOrderSummary?.visibility = View.GONE
            } else {
                tvViewSummary?.text = "Hide Summary"
                llOrderSummary?.visibility = View.VISIBLE
            }
        }

        btnDismissSummary?.setOnClickListener {
            llOrderSummary?.visibility = View.GONE
            tvViewSummary?.text = "View Summary"
        }

        btnBack?.setOnClickListener {
            listener?.onShoppingCartBackClicked()
        }

        btnShop?.setOnClickListener {
            when(btnShop.text.toString()) {
                TEXT_RETRY -> refreshCartItems()
                TEXT_GO_TO_SHOP -> listener?.onGotoShopClicked()
            }
        }

        btnBuy?.setOnClickListener {
            if (btnBuy?.text?.equals("Confirm Shipping Address") == true) {
                cartLoader?.show()
                if (isPrintBook) {
                    validateShippingFields()
                }
                if (isShippingDetailsCorrect) {
                    addressMap = getShippingAddress()
                    WSBookStore.addBillShipAddress(
                        addressMap["billName"],
                        addressMap["billLastName"],
                        addressMap["billMobile"],
                        addressMap["billEmail"],
                        addressMap["billAddress1"],
                        addressMap["billAddress2"],
                        addressMap["billCity"],
                        addressMap["billState"],
                        addressMap["billPincode"],
                        addressMap["shipName"],
                        addressMap["shipLastName"],
                        addressMap["shipMobile"],
                        addressMap["shipEmail"],
                        addressMap["shipAddress1"],
                        addressMap["shipAddress2"],
                        addressMap["shipCity"],
                        addressMap["shipState"],
                        addressMap["shipPincode"],
                        object : WSCallback {
                            override fun onWSResultSuccess(jsonObject: JSONObject?, responseCode: Int) {
                                btnBuy?.text = "Pay Now"
                                totalPrice = totalBookPrice + jsonObject?.optString("shippingCharges")?.toDouble()!!
                                val priceTemplate = "₹ %.2f"
                                tvTotal?.text = priceTemplate.format(totalPrice)
                                tvSummaryTotal?.text = priceTemplate.format(totalPrice)
                                tvSummaryShipping?.text = "₹ " + jsonObject?.optString("shippingCharges")
                                cartLoader?.hide()
                            }

                            override fun onWSResultFailed(resString: String?, responseCode: Int) {
                                Toast.makeText(requireContext(), "Problem while getting cart details", Toast.LENGTH_SHORT).show()
                                cartLoader?.hide()
                            }

                        })
                }
                else {
                    cartLoader?.hide()
                }
            }
            else if (btnBuy?.text?.equals("Pay Now") == true) {
                viewModel.viewModelScope.launch {
                    val msg = startPurchaseProcess(totalPrice)
                    if(msg.isNotBlank()) {
                        customSnackBar.showActionSnackBar(msg, "Retry", CustomSnackBar.LENGTH_LONG) {
                            btnBuy?.performClick()
                        }
                    }
                }
            }
            else {
                viewModel.viewModelScope.launch {
                    if (isPrintBook) {
                        btnBuy?.text = "Confirm Shipping Address"
                        llAddress?.visibility = View.VISIBLE
                        etShippingState?.isEnabled = false;
                        etShippingCity?.isEnabled = false;
                        val scrollTo: Int =
                            (llAddress?.parent?.parent as View).top + llAddress?.top!!
                        nsvCartContent?.smoothScrollTo(0, scrollTo)
                        etShippingName.requestFocus()
                        cbShowBillingAddress?.setOnClickListener {
                            if (cbShowBillingAddress?.isChecked == true) {
                                llBillingAddress?.visibility = View.VISIBLE
                                isBillingAddressSame = false
                            }
                            else {
                                llBillingAddress?.visibility = View.GONE
                                isBillingAddressSame = true
                            }
                        }

                        etShippingPincode.addTextChangedListener(object : TextWatcher {
                            override fun afterTextChanged(s: Editable?) {
                                btnBuy?.text = "Confirm Shipping Address"
                                if (s.toString().length == 6) {
                                    WSBookStore.getStateCityFromPin(s.toString().trim(), object : WSCallback {
                                        override fun onWSResultSuccess(
                                            jsonObject: JSONObject,
                                            responseCode: Int
                                        ) {
                                            if (jsonObject.optString("status")
                                                    .equals("ok", ignoreCase = true)
                                            ) {
                                                if (jsonObject.optString("state")
                                                        .isEmpty() || jsonObject.optString("state")
                                                        .equals("null", ignoreCase = true)
                                                ) {
                                                    etShippingState?.isEnabled = false
                                                    etShippingCity?.isEnabled = false
                                                } else {
                                                    etShippingState?.setText(jsonObject.optString("state"))
                                                    etShippingCity?.setText(jsonObject.optString("city"))
                                                    etShippingState?.error = null
                                                    etShippingCity?.error = null
                                                }
                                            } else {
                                                etShippingPincode?.error = "Pincode not found"
                                                isShippingDetailsCorrect = false
                                                Toast.makeText(requireContext(), "Invalid Pincode", Toast.LENGTH_SHORT).show()
                                            }
                                        }

                                        override fun onWSResultFailed(
                                            resString: String,
                                            responseCode: Int
                                        ) {
                                            //Handle failed cases
                                            etShippingPincode?.error = "Pincode not found"
                                            isShippingDetailsCorrect = false;
                                            Toast.makeText(requireContext(), "Invalid Pincode", Toast.LENGTH_SHORT).show()
                                        }
                                    })
                                }
                            }

                            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
                            }

                            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                            }
                        })
                    }
                    else {
                        val msg = startPurchaseProcess(totalBookPrice)
                        if(msg.isNotBlank()) {
                            customSnackBar.showActionSnackBar(msg, "Retry", CustomSnackBar.LENGTH_LONG) {
                                btnBuy?.performClick()
                            }
                        }
                    }
                }
            }
        }

        adapter.onCartItemClickListener = object : ShoppingCartBooksAdapter.OnCartItemClickListener {
            override fun onApplyDiscount(book: CartBook, position: Int) {
                viewModel.applyDiscount(book)
            }

            override fun onDelete(book: CartBook, position: Int) {
                CartDialog().showDialog(requireActivity(), CartDialog.SURE_TO_DELETE) {
                    viewModel.deleteBook(book)
                    it.dismiss()
                }
            }
        }
    }

    private fun getShippingAddress(): HashMap<String, String> {
        val map: HashMap<String, String> = HashMap()
        map["shipName"] = etShippingName?.text.toString().trim()
        map["shipLastName"] = etShippingName?.text.toString().trim()
        map["shipAddress1"] = etShippingAddress1?.text.toString().trim()
        map["shipAddress2"] = etShippingAddress2?.text.toString().trim()
        map["shipMobile"] = etShippingMobile?.text.toString().trim()
        map["shipEmail"] = etShippingEmail?.text.toString().trim()
        map["shipPincode"] = etShippingPincode?.text.toString().trim()
        map["shipCity"] = etShippingCity?.text.toString().trim()
        map["shipState"] = etShippingState?.text.toString().trim()

        if (isBillingAddressSame) {
            map["billName"] = etShippingName?.text.toString().trim()
            map["billLastName"] = etShippingName?.text.toString().trim()
            map["billAddress1"] = etShippingAddress1?.text.toString().trim()
            map["billAddress2"] = etShippingAddress2?.text.toString().trim()
            map["billMobile"] = etShippingMobile?.text.toString().trim()
            map["billEmail"] = etShippingEmail?.text.toString().trim()
            map["billPincode"] = etShippingPincode?.text.toString().trim()
            map["billCity"] = etShippingCity?.text.toString().trim()
            map["billState"] = etShippingState?.text.toString().trim()
        }
        else {
            map["billName"] = etBillingName?.text.toString().trim()
            map["billLastName"] = etBillingName?.text.toString().trim()
            map["billAddress1"] = etBillingAddress1?.text.toString().trim()
            map["billAddress2"] = etBillingAddress2?.text.toString().trim()
            map["billMobile"] = etBillingMobile?.text.toString().trim()
            map["billEmail"] = etBillingEmail?.text.toString().trim()
            map["billPincode"] = etBillingPincode?.text.toString().trim()
            map["billCity"] = etBillingCity?.text.toString().trim()
            map["billState"] = etBillingState?.text.toString().trim()
        }

        return map
    }

    private fun validateShippingFields() {
        isShippingDetailsCorrect = true;
        if (etShippingName?.text.isNullOrEmpty()) {
            etShippingName?.error = "Field is mandatory"
            isShippingDetailsCorrect = false
        }

        if (etShippingMobile?.text.isNullOrEmpty()) {
            etShippingMobile?.error = "Field is mandatory"
            isShippingDetailsCorrect = false
        }
        else {
            if (etShippingMobile?.text?.length != 10) {
                etShippingMobile?.error = "Invalid Mobile Number"
                isShippingDetailsCorrect = false
            }
        }

        if (etShippingEmail?.text.isNullOrEmpty()) {
            etShippingEmail?.error = "Field is mandatory"
            isShippingDetailsCorrect = false
        }
        else {
            if (!validate(etShippingEmail?.text.toString())) {
                etShippingEmail?.error = "Invalid Email"
                isShippingDetailsCorrect = false
            }
        }

        if (etShippingAddress1?.text.isNullOrEmpty()) {
            etShippingAddress1?.error = "Field is mandatory"
            isShippingDetailsCorrect = false
        }

        if (etShippingCity?.text.isNullOrEmpty()) {
            etShippingCity?.error = "Field is mandatory"
            isShippingDetailsCorrect = false
        }

        if (etShippingState?.text.isNullOrEmpty()) {
            etShippingState?.error = "Field is mandatory"
            isShippingDetailsCorrect = false
        }

        if (etShippingPincode?.text.isNullOrEmpty()) {
            etShippingPincode?.error = "Field is mandatory"
            isShippingDetailsCorrect = false
        }
        else {
            if (etShippingPincode?.text?.length != 6) {
                etShippingPincode?.error = "Invalid Pin Code"
                isShippingDetailsCorrect = false
            }
        }
    }

    fun validate(emailStr: String?): Boolean {
        val matcher: Matcher = VALID_EMAIL_ADDRESS_REGEX.matcher(emailStr!!)
        return matcher.matches()
    }

    private fun validateBillingFields() {
        isBillingDetailsCorrect = true;
        if (etBillingName?.text.isNullOrEmpty()) {
            etBillingName?.error = "Field is mandatory"
            isBillingDetailsCorrect = false
        }

        if (etBillingMobile?.text.isNullOrEmpty()) {
            etBillingMobile?.error = "Field is mandatory"
            isBillingDetailsCorrect = false
        }
        else {
            val regex = "\\d{1,10}".toRegex()
            if (etBillingMobile?.text?.matches(regex) == false) {
                etBillingMobile?.error = "Invalid Mobile Number"
                isBillingDetailsCorrect = false
            }
        }

        if (etBillingAddress1?.text.isNullOrEmpty()) {
            etBillingAddress1?.error = "Field is mandatory"
            isBillingDetailsCorrect = false
        }

        if (etBillingCity?.text.isNullOrEmpty()) {
            etBillingCity?.error = "Field is mandatory"
            isBillingDetailsCorrect = false
        }

        if (etBillingState?.text.isNullOrEmpty()) {
            etBillingState?.error = "Field is mandatory"
            isBillingDetailsCorrect = false
        }

        if (etBillingPincode?.text.isNullOrEmpty()) {
            etBillingPincode?.error = "Field is mandatory"
            isBillingDetailsCorrect = false
        }
        else {
            val regex = "\\d{1,6}".toRegex()
            if (etBillingPincode?.text?.matches(regex) == false) {
                etBillingPincode?.error = "Invalid Pin Code"
                isBillingDetailsCorrect = false
            }
        }
    }

    private suspend fun startPurchaseProcess(totalPrice: Double): String {
        cartLoader?.visibility = View.VISIBLE
        val id = viewModel.createShoppingCartId(totalPrice, requireContext())
        if (id.isEmpty()) {
            return "Problem while getting Cart details. Please try again"
        }

        val totalPayableAmount = viewModel.getTotalPayablePrice()
        val totalBooksCount = viewModel.getTotalBooks()
        if (totalBooksCount == 0 || totalPayableAmount == null) {
            return "Problem while getting Cart details. Please try again"
        }

        val desc = if(totalBooksCount == 1)
            viewModel.getBookAt(0)?.bookTitle ?: "1 Book"
        else
            "$totalBooksCount Book(s)"

        listener?.startPayment(
            desc = desc,
            totalPayableAmount = totalPayableAmount,
            cartId = id
        )
        return ""
    }

    private fun updateTitle(books: Int = 0) {
        val title = "$PAGE_TITLE ($books)"
        tvCartTitle?.text = title
        listener?.onUpdateCartCount(books)
    }

    private fun initAnimations() {
        initConfettiAnimation()
    }

    private fun refreshCartItems(isFromResume: Boolean = false) {
        if(internetConnectionChecker.isNetworkConnected(requireContext())) {
            if(isFromResume)
                recCartBooks?.visibility = View.GONE
            viewModel.getBookList()
        } else {
            hideBottomButtons()
            recCartBooks?.visibility = View.GONE
            cartLoader?.visibility = View.GONE
            showMessage(NO_INTERNET_MSG)
        }
    }

    private fun initConfettiAnimation() {
        lottieDiscountApplied?.setMaxFrame(75)
        lottieDiscountApplied?.addAnimatorListener(object : Animator.AnimatorListener {
            override fun onAnimationStart(p0: Animator) {}

            override fun onAnimationEnd(p0: Animator) {
                lottieDiscountApplied?.visibility = View.GONE
            }

            override fun onAnimationCancel(p0: Animator) {
                lottieDiscountApplied?.visibility = View.GONE
            }

            override fun onAnimationRepeat(p0: Animator) {}
        })
    }

    private fun startConfettiAnimation() {
        lottieDiscountApplied?.visibility = View.VISIBLE
        lottieDiscountApplied?.playAnimation()
    }

    private fun showMessage(msg: String) {
        tvError?.text = msg
        when(msg) {
            EMPTY_CART_MSG -> {
                btnShop?.text = TEXT_GO_TO_SHOP
            }
            PROBLEM_MSG, NO_INTERNET_MSG -> {
                btnShop?.text = TEXT_RETRY
            }
        }
        vBorder?.visibility = View.GONE
        llEmpty?.visibility = View.VISIBLE
    }

    private fun hideMessage() {
        vBorder?.visibility = View.VISIBLE
        llEmpty?.visibility = View.GONE
    }

    private fun hideBottomButtons() {
        llDiscount?.visibility = View.GONE
        llTotal?.visibility = View.GONE
        btnBuy?.visibility = View.GONE
        llOrderSummary?.visibility = View.GONE
    }

    fun hideLoader() {
        cartLoader?.visibility = View.GONE
    }

    fun onPurchaseSuccess(paymentId: String?, totalPayableAmount: Double?) {
        hideLoader()
        WonderPubSharedPrefs.getInstance(requireContext()).cartCount = 0
        val paymentDialog = PaymentSuccessDialog()
        paymentDialog.showDialog(requireActivity(), paymentId, totalPayableAmount, viewModel.getTotalBooks())
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        if (context is OnFragShoppingCartListener)
            listener = context
    }

    override fun onDestroy() {
        super.onDestroy()
        listener = null
    }

    companion object {
        private const val SHOPPING_CART_CONFIG = "shoppingCartConfig"

        const val PAGE_TITLE = "Cart"

        private const val EMPTY_CART_MSG = "Your cart is empty."
        private const val NO_INTERNET_MSG = "Connect to internet to see cart items."
        private const val PROBLEM_MSG = "Problem while preparing cart.\nPlease try again."
        private const val TEXT_RETRY = "Retry"
        private const val TEXT_GO_TO_SHOP = "Go to store"

        fun newInstance(config: Config) = FragShoppingCart().also {
            val bundle = Bundle()
            bundle.putSerializable(SHOPPING_CART_CONFIG, config)
            it.arguments = bundle
        }
    }

    data class Config(
        val showHeader: Boolean,
    ) : Serializable

    interface OnFragShoppingCartListener {
        fun onUpdateCartCount(count: Int)
        fun onShoppingCartBackClicked()
        fun onGotoShopClicked()
        fun startPayment(desc: String, totalPayableAmount: Double, cartId: String)
    }

}