package com.wonderslate.prepjoy.ui.dashboard

import android.annotation.SuppressLint
import android.app.Activity
import android.app.AlertDialog
import android.content.*
import android.content.res.Resources
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.webkit.URLUtil
import android.widget.*
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.ContextCompat
import androidx.core.view.GravityCompat
import androidx.core.view.isVisible
import androidx.drawerlayout.widget.DrawerLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentStatePagerAdapter
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.Observer
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.ViewPager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.etebarian.meowbottomnavigation.MeowBottomNavigation
import com.google.android.gms.tasks.OnCompleteListener
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.google.android.material.progressindicator.LinearProgressIndicator
import com.google.android.material.tabs.TabLayout
import com.google.android.play.core.appupdate.AppUpdateInfo
import com.google.android.play.core.appupdate.AppUpdateManager
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.android.play.core.install.InstallState
import com.google.android.play.core.install.InstallStateUpdatedListener
import com.google.android.play.core.install.model.ActivityResult
import com.google.android.play.core.install.model.AppUpdateType
import com.google.android.play.core.install.model.InstallStatus
import com.google.android.play.core.install.model.UpdateAvailability
import com.google.android.play.core.review.ReviewManagerFactory
import com.google.android.gms.tasks.Task
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.google.firebase.messaging.FirebaseMessaging
import com.wonderslate.commons.Data
import com.wonderslate.commons.Status
import com.wonderslate.data.helper.ConstantsHelper
import com.wonderslate.data.helper.ConstantsHelper.BROADCAST_ACTION_NOTIFICATION_DISMISS
import com.wonderslate.data.interfaces.WSResponseCallback
import com.wonderslate.data.models.UserStudyPreference
import com.wonderslate.data.network.Wonderslate
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.data.repository.WSBookStore
import com.wonderslate.domain.entities.OnBoardingData
import com.wonderslate.domain.entities.UpdateUserSelectedCategoryData
import com.wonderslate.domain.entities.UserRankData
import com.wonderslate.domain.entities.UserSelectedCategoryData
import com.wonderslate.prepjoy.BuildConfig
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.Utils.*
import com.wonderslate.prepjoy.Utils.Utils.navigateToRateApp
import com.wonderslate.prepjoy.service.PrepjoyComponentMessagingInterface
import com.wonderslate.prepjoy.ui.BaseActivity
import com.wonderslate.prepjoy.ui.analytics.AnalyticsFragment
import com.wonderslate.prepjoy.ui.chapters_list.ChaptersListAct
import com.wonderslate.prepjoy.ui.customer_support.HelpUtility
import com.wonderslate.prepjoy.ui.ebooks.EbooksFragment
import com.wonderslate.prepjoy.ui.groupwall.GroupWallActivity
import com.wonderslate.prepjoy.ui.home.HomeFragment
import com.wonderslate.prepjoy.ui.home.HomeViewModel
import com.wonderslate.prepjoy.ui.leaderboard.LeaderboardFragment
import com.wonderslate.prepjoy.ui.library.LibraryBooksFrag
import com.wonderslate.prepjoy.ui.login.LoginActivity
import com.wonderslate.prepjoy.ui.new_book_details.BookDetailsAct
import com.wonderslate.prepjoy.ui.notification.NotificationActivity
import com.wonderslate.prepjoy.ui.profile.ProfileActivity
import com.wonderslate.prepjoy.ui.quiz.QuizActivity
import com.wonderslate.prepjoy.ui.settings.SettingsActivity
// import com.wonderslate.prepjoy.ui.shoppingcart.ShoppingCartIcon
import com.ws.book_details.data.models.BookDetails
import com.ws.chapter_list.data.models.ChaptersListConfig
import com.ws.commons.enums.EventType
import com.ws.commons.models.Event
import com.ws.commons.models.ShopBookData
import com.ws.core_ui.extensions.setEnabled
import com.ws.core_ui.extensions.startActivityWithAnim
import com.ws.shop.ui.ShopBooksFrag
import com.wonderslate.prepjoy.databinding.ActivityDashBoardBinding
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.atomic.AtomicInteger


class DashBoardActivity : BaseActivity(),
        ShopBooksFrag.ShopBooksFragmentListener,
        LibraryBooksFrag.LibraryBooksFragmentListener, HomeFragment.OnHomeFrgmentFragInteraction, BaseActivity.weeklyContestListener {

    private lateinit var binding: ActivityDashBoardBinding
    var tabLayout: TabLayout? = null
    private var viewPager: ViewPager? = null
    var context: Context? = null
    val viewModel: HomeViewModel by viewModel()
    private val dashBoardViewModel: DashBoardViewModel by viewModel()
    var selectedTab: Int = 1
    private var isDailyTestOpen = false

    private lateinit var imageHumber: ImageView
    private lateinit var rlProfile: RelativeLayout
    private lateinit var textUserName: TextView
    private lateinit var textOpenNav :TextView
    private lateinit var imageUser: ImageView
    private lateinit var framePrivacy: FrameLayout
    private lateinit var frameSettings: FrameLayout

    private lateinit var frameTermCondition: FrameLayout
    private lateinit var frameLogout: FrameLayout
    private lateinit var layoutRating: RelativeLayout
    private lateinit var layoutInvite: RelativeLayout
    private lateinit var contactUsFrameLayout: FrameLayout

    private lateinit var ourAppsRecyclerView: RecyclerView

    private lateinit var drawerLayout: DrawerLayout
    private lateinit var shareApp: FloatingActionButton
    private lateinit var dashBoardAdapter: DashBoardAdapter
    private var isFirstTime: Boolean = false
    var isCafeRefresh: Boolean = false

    private var imageChangePreferance: ImageView? = null

    //In-app update task
    private var mAppUpdateManager: AppUpdateManager? = null
    private val RC_APP_UPDATE = 999
    private val inAppUpdateType = AppUpdateType.IMMEDIATE
    private var appUpdateInfoTask: Task<AppUpdateInfo>? = null
    private var installStateUpdatedListener: InstallStateUpdatedListener? = null

    private lateinit var firebaseAnalytics: FirebaseAnalytics
    private lateinit var frameNotification: FrameLayout
    private lateinit var frameHelp: FrameLayout
    private lateinit var textNotificationCount: TextView
    private var notificationReceiver: BroadcastReceiver? = null
    private var homeFragment: HomeFragment? = null
    private var leaderBoardFragment: LeaderboardFragment? = null
    private var reloadContext: String? = null
    private var notificationDismissReceiver: BroadcastReceiver? = null
    private lateinit var viewWallNotificationDot: ImageView
    private var alertDialog: AlertDialog? = null
    private var isDeclineSelected = false
    private lateinit var quizIntent: Intent
    private val c: AtomicInteger = AtomicInteger(0)
    // private var shoppingCartIcon: ShoppingCartIcon? = null

    private val ourAppsClicked = { selected: String ->
        when(selected) {
            "Current\nAffairs" -> navigateToRateApp(context!!, "")
            "Engg" -> navigateToRateApp(context!!, "engineering")
            "Teaching" -> navigateToRateApp(context!!, "ctet")
            "NEET" -> navigateToRateApp(context!!, "neet")
            "CA CS\nCMA" -> navigateToRateApp(context!!, "ca")
            "ಕರ್ನಾಟಕ" -> navigateToRateApp(context!!, "karnataka")
            else -> {navigateToRateApp(context!!, "")}
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDashBoardBinding.inflate(layoutInflater)
        setContentView(binding.root)
        dashBoardViewModel.defaultCategoryResponse.observe(this, ::defaultCategoryResponse)
        dashBoardViewModel.userSelectedCategoryResponse.observe(this, ::userSelectedCategoryResponse)
        dashBoardViewModel.updateUserSelectedCategoryResponse.observe(this, ::updateUserSelectedCategoryResponse)
        Wonderslate.APP_STATE = Lifecycle.Event.ON_CREATE
        Wonderslate.aClass = javaClass
        try {
            Utils.userStats(baseContext)
            quizIntent = Intent(baseContext, QuizActivity::class.java).also {
                it.putExtra("joinGame", true)
            }
            WonderPubSharedPrefs.getInstance(baseContext).setFlavourValue(BuildConfig.FLAVOR)
            isFirstTime = intent.getBooleanExtra("isFirstTime", false)
            initializeContestListener(this)
            initViews()
            try {
                val date = Date()
                viewModel.getUserRank(
                        UserRankData(SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH).format(date), BuildConfig.SITE_ID, "daily")
                )
            } catch (e: Exception) {

            }

            if (isFirstTime && FlavourHelper.languageSelectionEnabled())
                languageSelectionDialog()

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                if (WonderPubSharedPrefs.getInstance(baseContext).isFirstTimeLaunch) {
                    // GrantPermissionActivity functionality temporarily disabled for demo
                    // startActivity(Intent(this, GrantPermissionActivity::class.java))
                }
            }

            //In-app update task
            // Creates instance of the manager.

            //In-app update task
            // Creates instance of the manager.
            mAppUpdateManager = AppUpdateManagerFactory.create(this)
            // Returns an intent object that you use to check for an update.
            // Returns an intent object that you use to check for an update.
            appUpdateInfoTask = mAppUpdateManager!!.appUpdateInfo
            //lambda operation used for below listener
            //lambda operation used for below listener
            installStateUpdatedListener = InstallStateUpdatedListener { installState: InstallState ->
                if (installState.installStatus() == InstallStatus.DOWNLOADED) {
                    popupSnackbarForCompleteUpdate()
                }
            }
            mAppUpdateManager!!.registerListener(installStateUpdatedListener!!)

            checkAppUpdate()

            dashBoardViewModel.getDefaultStoreCategory(OnBoardingData(BuildConfig.SITE_ID))

            try {
                val window: Window = <EMAIL>
                window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
                window.navigationBarColor =
                        ContextCompat.getColor(this@DashBoardActivity, R.color.primary_bg_red)

                val decorView = getWindow().decorView //set status background black
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    decorView.systemUiVisibility =
                            decorView.systemUiVisibility and View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv()
                } //set status text  light
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
            }


        } catch (e: Exception) {
            e.printStackTrace()
        }

        firebaseAnalytics = Firebase.analytics
        val bundle = Bundle()
        bundle.putString(FirebaseAnalyticsUtils.ACTIVITY_NAME, "DashboardActivity")
        bundle.putString(FirebaseAnalyticsUtils.ACTION, "DashboardActivity onCreate")
        firebaseAnalytics.logEvent(FirebaseAnalyticsUtils.LOCATION, bundle)

        sendDeviceIdToServer()
        registerNotificationReceivers()
        getRemoteConfigValues()
        getRemoteConfigDailyTestValues()

        // fetchCartCount()
        // shoppingCartIcon = ShoppingCartIcon(this@DashBoardActivity, findViewById(R.id.flCart))
        //For check user has selecetd any prefered level, syllabus and grade

        /*  if (android.os.Build.VERSION.SDK_INT <= Build.VERSION_CODES.M) {
                lltNotification.setBackgroundResource(R.drawable.ic_notification_back);
            } else {
                lltNotification.setBackgroundResource(R.drawable.ic_notification_circle);
            }*/

        //For check user has selecetd any prefered level, syllabus and grade
        val wonderPubSharedPrefs = WonderPubSharedPrefs.getInstance(context)
        var level = wonderPubSharedPrefs.sharedPrefsUserLevelPref

        reloadContext = intent!!.getStringExtra("context")

        if (reloadContext != null && reloadContext.equals("navigate_to_store", ignoreCase = true)) {
            level = intent!!.getStringExtra("level").toString()
            wonderPubSharedPrefs.quizFilterLevelSearch = level
        }
        else {
            wonderPubSharedPrefs.quizFilterLevelSearch = ""
        }

        if (isFirstTime || (wonderPubSharedPrefs.sharedPrefsLastFilterLevelSearch.isEmpty())) {
            when (BuildConfig.FLAVOR) {
                Flavours.CACSCMA.flavour, Flavours.CTET.flavour -> {
                    wonderPubSharedPrefs.sharedPrefsUserSyllabusPref = listOf(getString(R.string.syllabus))
                    wonderPubSharedPrefs.sharedPrefsLastFilterSyllabusSearch = getString(R.string.syllabus)
                }
                Flavours.NEET.flavour -> {
                    imageChangePreferance?.visibility = View.GONE
                }
            }
            wonderPubSharedPrefs.sharedPrefsUserLevelPref = level
            wonderPubSharedPrefs.sharedPrefsLastFilterLevelSearch = level
           // checkUserPreference()
        }

        if (intent.hasExtra("isAccess")) {
            val code = intent.getStringExtra("code")
            val challenger = intent.getStringExtra("challenger")
            if (intent.hasExtra("bookId")) {
                buildDialog(code, challenger, intent.getStringExtra("bookId"))
            } else {
                buildDialog(code, challenger)
            }
            if (!isFinishing) {
                alertDialog?.show()
            }
        }

        if (reloadContext != null && reloadContext.equals("navigate_to_store", ignoreCase = true)) {
            onEBooksClicked("Quiz Summary")
        }
        else if (reloadContext != null && reloadContext.equals("navigate_to_shop", ignoreCase = true)) {
            onEBooksClicked("Cart")
        }
        else if (reloadContext != null && reloadContext.equals("navigate_to_ebook_purchase", ignoreCase = true)) {
            onEBooksClicked("Purchased")
        }
    }

    private fun sendDeviceIdToServer() {
        var token = WonderPubSharedPrefs.getInstance(this).firebaseToken
        if (token.isNotEmpty()) {
            viewModel.sendFirebaseTokenToServer(WonderPubSharedPrefs.getInstance(this).firebaseToken)
        }
        else {
            FirebaseMessaging.getInstance().token.addOnCompleteListener(
                OnCompleteListener { task ->
                    if (!task.isSuccessful) {
                        Log.w("Dashboard", "Fetching FCM registration token failed", task.exception)
                        return@OnCompleteListener
                    }

                    // Get new FCM registration token & send to server
                    token = task.result
                    viewModel.sendFirebaseTokenToServer(token)

                })
        }
    }

    private fun getRemoteConfigValues() {
        val remoteConfig = FirebaseABTestingUtils().remoteConfigQuiz
        remoteConfig.fetchAndActivate().addOnCompleteListener(this) { task ->
            if (task.isSuccessful) {
                Log.e("Dash", "Config params updated1: " + remoteConfig.getString("questions_label_key"))
                // Toast.makeText(this, "Fetch and activate succeeded:"+remoteConfig.getString("quiz_type_key"), Toast.LENGTH_SHORT).show()
                WonderPubSharedPrefs.getInstance(context).quizABTestValue = remoteConfig.getString("questions_label_key")
            }
        }
    }

    private fun defaultCategoryResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {

            }

            Status.SUCCESSFUL -> {
                data.data?.let {
                    try {
                        dashBoardViewModel.getUserSelectedCategory(UserSelectedCategoryData(BuildConfig.SITE_ID))
                        val jObj = JSONObject(data.data.toString())
                        val defaultCategoryString = jObj.optString("defaultCategoryStructure")
                        val defaultCategoryJsonArray = JSONArray(defaultCategoryString)
                        Wonderslate.getInstance().sharedPrefs.defaultCategory = defaultCategoryJsonArray.toString()

                    } catch (e: Exception) {
                        e.printStackTrace()

                    }
                }
            }

            Status.HTTP_UNAVAILABLE -> {
            }

            Status.ERROR -> {
            }
        }
    }

    private fun userSelectedCategoryResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {

            }

            Status.SUCCESSFUL -> {
                data.data?.let {
                    try {
                        val jObj = JSONObject(JSONObject(data.data.toString()).optString("userGrades")
                            .replaceFirst("\"", "").dropLast(1).replace("\\", ""))
                        Wonderslate.getInstance().sharedPrefs.userPrefs = jObj.toString()

                        if (!(BuildConfig.FLAVOR.equals(Flavours.NEET.flavour, ignoreCase = true)))
                            checkUserPreference()

                    } catch (e: Exception) {
                        e.printStackTrace()
                        runOnUiThread {
                            // UserPreferanceSelectionActivity functionality temporarily disabled for demo
                            // val goToPreferance = Intent(this@DashBoardActivity, UserPreferanceSelectionActivity::class.java)
                            // goToPreferance.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
                            // goToPreferance.putExtra("SHOW_FROM", "level")
                            // goToPreferance.putExtra("TabPosition",viewPager?.currentItem)
                            // goToPreferance.putExtra("MODE", "set")
                            // preferenceResult.launch(goToPreferance)
                            showToast("User preference selection temporarily disabled for demo")
                        }

                    }
                }
            }

            Status.HTTP_UNAVAILABLE -> {
            }

            Status.ERROR -> {
            }
        }
    }

    private fun updateUserSelectedCategoryResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {

            }

            Status.SUCCESSFUL -> {
                data.data?.let {
                }
            }

            Status.HTTP_UNAVAILABLE -> {
            }

            Status.ERROR -> {
            }
        }
    }

    private fun fetchCartCount() {
        val wsBookStore = WSBookStore()
        wsBookStore.getCartCount(object : WSResponseCallback<Int?> {
            override fun onWSResultSuccess(response: Int?, responseCode: Int) {
                // WonderPubSharedPrefs.getInstance(this@DashBoardActivity).cartCount = response
                // updateShoppingCartIcon()
            }

            override fun onWSResultFailed(resString: String?, responseCode: Int) {
                // WonderPubSharedPrefs.getInstance(this@DashBoardActivity).cartCount = 0
                // updateShoppingCartIcon()
            }
        })
    }

    fun updateShoppingCartIcon() {
        // Shopping cart functionality temporarily disabled for demo
        // if (shoppingCartIcon != null) {
        //     shoppingCartIcon!!.updateCountUI()
        // }
    }

    //For check user has selecetd any prefered level, syllabus and grade
    private fun checkUserPreference() {
        try {
            val userPrefsObject = JSONObject(WonderPubSharedPrefs.getInstance(context).userPrefs)
            val level = userPrefsObject.optString("selectedLevel")
            val syllabus = userPrefsObject.optString("selectedSyllabus")
            val grade = userPrefsObject.optString("selectedGrade")
            if (level.isEmpty() && WonderPubSharedPrefs.getInstance(context).sharedPrefsUserLevelPref.isEmpty()) {
                // UserPreferanceSelectionActivity functionality temporarily disabled for demo
                // val goToPreferance = Intent(this@DashBoardActivity, UserPreferanceSelectionActivity::class.java)
                // goToPreferance.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
                // goToPreferance.putExtra("SHOW_FROM", "level")
                // goToPreferance.putExtra("TabPosition",viewPager?.currentItem)
                // goToPreferance.putExtra("MODE", "set")
                // preferenceResult.launch(goToPreferance)
                showToast("User preference selection temporarily disabled for demo")
            }
            else {
                WonderPubSharedPrefs.getInstance(context).sharedPrefsUserLevelPref = level
                WonderPubSharedPrefs.getInstance(context).sharedPrefsUserSyllabusPref = listOf(syllabus)
                WonderPubSharedPrefs.getInstance(context).sharedPrefsUserGradePref = listOf(grade)
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }


    private fun getRemoteConfigDailyTestValues() {

        WonderPubSharedPrefs.getInstance(context).dailyTestABTestValue = "Daily Games"
       /* val remoteConfig = FirebaseABTestingUtils().remoteConfigDailyTest
        remoteConfig.fetchAndActivate().addOnCompleteListener(this) { task ->
            if (task.isSuccessful) {

                Log.e("Dash", "Config params updated2: " + remoteConfig.getString("daily_test_label_key"))
                // Toast.makeText(this, "Fetch and activate succeeded:"+remoteConfig.getString("quiz_type_key"), Toast.LENGTH_SHORT).show()
                WonderPubSharedPrefs.getInstance(context).dailyTestABTestValue = remoteConfig.getString("daily_test_label_key")

            }
        }*/
    }

    override fun getLayoutResource(): Int {
        return R.layout.activity_dash_board
    }


    //For DeepLink
    //In-app update
    private fun checkAppUpdate() {
        try {
            // Checks that the platform will allow the specified type of update.
            appUpdateInfoTask!!.addOnSuccessListener { appUpdateInfo: AppUpdateInfo ->
                if (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE // For a flexible update, use AppUpdateType.FLEXIBLE
                        && appUpdateInfo.isUpdateTypeAllowed(inAppUpdateType)) {
                    // Request the update.
                    try {
                        mAppUpdateManager?.startUpdateFlowForResult( // Pass the intent that is returned by 'getAppUpdateInfo()'.
                                appUpdateInfo,  // Or 'AppUpdateType.FLEXIBLE' for flexible updates.
                                inAppUpdateType,  // The current activity making the update request.
                                this@DashBoardActivity,  // Include a request code to later monitor this update request.
                                RC_APP_UPDATE)
                    } catch (ignored: IntentSender.SendIntentException) {
                        ignored.printStackTrace()
                    }
                }
            }
        } catch (e: java.lang.Exception) {
            Log.e("prepjoy", "checkAppUpdate: ", e)
        }
    }

    private fun popupSnackbarForCompleteUpdate() {
        try {
            customSnackBar.showActionSnackBar("An update has just been downloaded.\nRestart to update", "RESTART",
                    CustomSnackBar.LENGTH_INDEFINITE) { mAppUpdateManager!!.completeUpdate() }
        } catch (e: Resources.NotFoundException) {
            e.printStackTrace()
        }
    }


    var preferenceResult = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        try {
            if (result.resultCode == Activity.RESULT_OK)
            {
                val userStudyPrefsObject : UserStudyPreference = result.data!!.getSerializableExtra("userSelectedPrefsObject") as UserStudyPreference
                val returnResult = result.data!!.getStringExtra("PRE_STATUS")

                if (returnResult.equals("done") || returnResult.equals("change")) {
                    val selectedLevel = userStudyPrefsObject.levelPreference
                    val selectedSyllabus = userStudyPrefsObject.syllabusPreference
                    val selectedGrade = userStudyPrefsObject.gradePreference

                    Wonderslate.getInstance().sharedPrefs.sharedPrefsUserLevelPref = selectedLevel
                    val selectedSyllabusList : List<String> = listOf(selectedSyllabus)
                    Wonderslate.getInstance().sharedPrefs.sharedPrefsUserSyllabusPref = selectedSyllabusList
                    val selectedGradeList : List<String> = listOf(selectedGrade)
                    Wonderslate.getInstance().sharedPrefs.sharedPrefsUserGradePref = selectedGradeList

                    val updateUserPrefsObject = JSONObject()
                    updateUserPrefsObject.put("selectedLevel", selectedLevel)
                    updateUserPrefsObject.put("selectedSyllabus", selectedSyllabus)
                    updateUserPrefsObject.put("selectedGrade", selectedGrade)

                    dashBoardViewModel.setUserSelectedCategory(UpdateUserSelectedCategoryData(BuildConfig.SITE_ID, updateUserPrefsObject.toString()))
                }

                if (returnResult.equals("done", ignoreCase = true))
                {
                    val intent = Intent(context, DashBoardActivity::class.java)
                    intent.putExtra("isFirstTime", true)
                    startActivity(intent)
                    finish()
                }
                else
                {
                    val cafeFragment = dashBoardAdapter.getItem(1) as EbooksFragment
                    cafeFragment.refreshTab()
                }
            }
        }catch (e: java.lang.Exception){
            e.printStackTrace()
        }

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        try {
            //In-app update
            if (requestCode == RC_APP_UPDATE) {
                //In-app update task
                var msg = ""
                when {
                    resultCode == RESULT_OK -> msg = "App download starts..."
                    resultCode != RESULT_CANCELED -> msg = "App download canceled."
                    resultCode == ActivityResult.RESULT_IN_APP_UPDATE_FAILED -> msg = "App download failed."
                }
                if (msg.isNotEmpty()) {
                    Toast.makeText(this@DashBoardActivity, msg, Toast.LENGTH_LONG).show()
                }
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }
    fun showOrHideUserPreference(visibility: Boolean) {
        imageChangePreferance!!.isVisible = visibility
    }

    private fun initViews() {
        if (supportActionBar != null) {
            supportActionBar!!.hide()
        }
        context = this
        tabLayout = binding.tabLayout
        viewPager = binding.viewPager
        shareApp = binding.fabShare
        initBottomNavigation()
        viewModel.getCurrentFragmentPosition(0)
        imageHumber = findViewById(R.id.imagNav)
        textOpenNav = findViewById(R.id.textOpenNav)
        rlProfile = binding.relativeLayoutAvatarView
        textUserName = binding.textViewName
        imageUser = binding.imageViewAvatar
        framePrivacy = binding.manageStorageLayout
        frameSettings = binding.settingsTxtLayout
        frameTermCondition = binding.historyTxtLayout
        frameLogout = binding.inviteTxtLayout
        layoutInvite = binding.layoutShareApp
        contactUsFrameLayout = binding.contactUsFrameLayout
        ourAppsRecyclerView = binding.ourAppsRecyclerView

        layoutRating = binding.inAppRating
        drawerLayout = binding.activityMain
        frameNotification = findViewById(R.id.frameLayout_notification)
        frameHelp = findViewById(R.id.frameLayout_help)
        textNotificationCount = findViewById(R.id.notification_count)

        viewWallNotificationDot = binding.viewWallNotificationDot
        viewWallNotificationDot.visibility = View.GONE
        textNotificationCount.text = WonderPubSharedPrefs.getInstance(this)
                .firebaseNotificationCount.toString()

        shareApp.setOnClickListener { shareClickOperation() }
        frameNotification.setOnClickListener {
            WonderPubSharedPrefs.getInstance(this).firebaseNotificationCount = 0

            textNotificationCount.visibility = View.GONE
            viewWallNotificationDot.visibility = View.GONE
            val intent = Intent(this, NotificationActivity::class.java)
            startActivity(intent)
        }

        frameHelp.setOnClickListener {
            val helpUtility = HelpUtility(this)
            helpUtility.showHelpOptions(it)
        }

        imageHumber.setOnClickListener {
            val url: String = WonderPubSharedPrefs.getInstance(context).userImage
            val check: Boolean = "null" in url
            if (check) {
                imageUser.setImageResource(R.drawable.prepjoy_full_icon)
            } else {
                Glide.with(this)
                        .load(WonderPubSharedPrefs.getInstance(context).userImage)
                        .diskCacheStrategy(DiskCacheStrategy.NONE)
                        .skipMemoryCache(true)
                        .dontAnimate()
                        .into(object : CustomTarget<Drawable>() {
                            override fun onResourceReady(resource: Drawable, transition: Transition<in Drawable>?) {
                                imageUser.setImageDrawable(resource)
                            }

                            override fun onLoadCleared(placeholder: Drawable?) {
                            }

                        })
            }

            textUserName.text = WonderPubSharedPrefs.getInstance(context).username
            drawerLayout.openDrawer(GravityCompat.START)
        }


        framePrivacy.setOnClickListener {
            drawerLayout.closeDrawer(GravityCompat.START)
            openWebPage(this, getString(R.string.privacy_url))
        }

        frameTermCondition.setOnClickListener {
            drawerLayout.closeDrawer(GravityCompat.START)
            openWebPage(this, getString(R.string.term_condition_url))
        }

        frameLogout.setOnClickListener {
            drawerLayout.closeDrawer(GravityCompat.START)
            logoutDialog()
        }
        layoutInvite.setOnClickListener {
            drawerLayout.closeDrawer(GravityCompat.START)
            val intent = Utils.homeScreenIntent(context)
            startActivity(intent)
        }
        contactUsFrameLayout.setOnClickListener {
            //drawerLayout.closeDrawer(GravityCompat.START)
            /*val internetConnectionChecker = InternetConnectionChecker()
            if (!internetConnectionChecker.isNetworkConnected(context)) {
                Toast.makeText(context, "No internet available.", Toast.LENGTH_SHORT).show()
            } else {
                val emailIntent = Intent(Intent.ACTION_SENDTO, Uri.fromParts("mailto", resources.getString(R.string.support_email), null))
                this.startActivity(Intent.createChooser(emailIntent, "Send email..."))
            }*/
            val helpUtility = HelpUtility(this)
            helpUtility.showHelpOptions(it)
        }

        var appsList = arrayListOf(
                "CA CS\nCMA",
                "Current\nAffairs",
                "ಕರ್ನಾಟಕ",
                "Engg",
                "NEET",
                "Teaching"
        )
        when (BuildConfig.FLAVOR) {
            Flavours.ENGINEERING.flavour -> appsList.remove("Engg")
            Flavours.NEET.flavour -> appsList.remove("NEET")
            Flavours.KARNATAKA.flavour -> appsList.remove("ಕರ್ನಾಟಕ")
            Flavours.CURRENT_AFFAIRS.flavour -> appsList.remove("Current\nAffairs")
            Flavours.CACSCMA.flavour -> appsList.remove("CA CS\nCMA")
            Flavours.CTET.flavour -> appsList.remove("Teaching")
        }
        ourAppsRecyclerView.layoutManager = GridLayoutManager(context, 3)
        val adapter = OurAppsAdapter(appsList, ourAppsClicked)
        ourAppsRecyclerView.adapter = adapter

        frameSettings.setOnClickListener {
            val intent = Intent(context, SettingsActivity::class.java)
            this.startActivity(intent)
            drawerLayout.closeDrawer(GravityCompat.START)
        }

        layoutRating.setOnClickListener {
            rateApp()
        }

        rlProfile.setOnClickListener {
            val intent = Intent(context, ProfileActivity::class.java)
            this.startActivity(intent)
            drawerLayout.closeDrawer(GravityCompat.START)
        }

        binding.btnDiscord.isVisible = getString(R.string.social_link_discord).isNotBlank()
        binding.facebookBtn.isVisible = getString(R.string.social_link_facebook).isNotBlank()
        binding.instagramBtn.isVisible = getString(R.string.social_link_instagram).isNotBlank()
        binding.btnYoutube.isVisible = getString(R.string.social_link_youtube).isNotBlank()
        binding.telegramBtn.isVisible = getString(R.string.social_link_telegram).isNotBlank()
        //binding.btnLinkedIn.isVisible = getString(R.string.social_link_linkedin).isNotBlank()
        binding.btnLinkedIn.isVisible = false

        binding.facebookBtn.setOnClickListener {
            openWebBrowser(getString(R.string.social_link_facebook))
        }
        binding.instagramBtn.setOnClickListener {
            openWebBrowser(getString(R.string.social_link_instagram))
        }
        binding.btnYoutube.setOnClickListener {
            openWebBrowser(getString(R.string.social_link_youtube))
        }
        binding.telegramBtn.setOnClickListener {
            openWebBrowser(getString(R.string.social_link_telegram))
        }
        binding.browserBtn.setOnClickListener {
            openWebBrowser(getString(R.string.social_link_website))
        }
        binding.btnTwitter.setOnClickListener {
            openWebBrowser(getString(R.string.social_link_twitter))
        }
        binding.btnDiscord.setOnClickListener {
            openWebBrowser(getString(R.string.social_link_discord))
        }

        binding.btnLinkedIn.setOnClickListener {
            openWebBrowser(getString(R.string.social_link_linkedin))
        }

        textUserName.text = WonderPubSharedPrefs.getInstance(context).username

        //For group wall - notification
        registerNotificationReceivers()
        imageChangePreferance = findViewById(R.id.imageView_change_preferance)
        imageChangePreferance!!.setOnClickListener {

            // UserPreferanceSelectionActivity functionality temporarily disabled for demo
            // val goToPreferance = Intent(this@DashBoardActivity, UserPreferanceSelectionActivity::class.java)
            // goToPreferance.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
            // if (BuildConfig.FLAVOR.equals(Flavours.CACSCMA.flavour, ignoreCase = true) || BuildConfig.FLAVOR.equals(Flavours.CTET.flavour, ignoreCase = true)) {
            //     goToPreferance.putExtra("SHOW_FROM", "grade")
            // } else {
            //     goToPreferance.putExtra("SHOW_FROM", "level")
            // }
            // goToPreferance.putExtra("TabPosition", viewPager?.currentItem)
            // goToPreferance.putExtra("MODE", "change")
            // //startActivityForResult(goToPreferance, 199)
            // preferenceResult.launch(goToPreferance)
            showToast("User preference selection temporarily disabled for demo")
        }
        viewModel.challengerCode.observe(this, observeJoinGame())
        viewModel.weeklyWinnersRank.observe(this, ::weeklyWinnersRank)

        try {
            val date = GenerateDates.getPreviousSaturday()
            viewModel.getWeeklyContestRank(
                UserRankData(SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH).format(date), BuildConfig.SITE_ID, "weekly")
            )
        } catch (e: Exception) {
            val date = GenerateDates.getPreviousSaturday()
            viewModel.getWeeklyContestRank(
                UserRankData(date, BuildConfig.SITE_ID, "weekly")
            )
        }
    }

    override fun onDestroy() {
        //In-app update task
        mAppUpdateManager!!.unregisterListener(installStateUpdatedListener!!)
        super.onDestroy()
    }

    private fun openWebBrowser(url: String) {
        drawerLayout.closeDrawer(GravityCompat.START)
        val webpage = Uri.parse(url)
        val intent = Intent(Intent.ACTION_VIEW, webpage)
        if (intent.resolveActivity(packageManager) != null) {
            try {
                startActivity(intent)
            } catch (e: ActivityNotFoundException) {
                customSnackBar.showActionSnackBar("Unable to find any web browser.", "OK", CustomSnackBar.LENGTH_INDEFINITE) { customSnackBar.dismiss() }
            }
        }
    }

    override fun onResume() {
        initializeContestListener(this)
        // updateShoppingCartIcon()

        try {
            viewModel.getCurrentAffairsUserDetails(BuildConfig.SITE_ID)

            //In-app update task
            try {
                if (null != mAppUpdateManager) {
                    mAppUpdateManager!!.appUpdateInfo.addOnSuccessListener { appUpdateInfo: AppUpdateInfo ->
                        if (appUpdateInfo.updateAvailability() == UpdateAvailability.DEVELOPER_TRIGGERED_UPDATE_IN_PROGRESS) {
                            // If an in-app update is already running, resume the update.
                            try {
                                mAppUpdateManager!!.startUpdateFlowForResult(
                                        appUpdateInfo,
                                        inAppUpdateType,
                                        this,
                                        RC_APP_UPDATE)
                            } catch (e: IntentSender.SendIntentException) {
                                e.printStackTrace()
                            }
                        }
                    }
                    mAppUpdateManager!!.appUpdateInfo.addOnSuccessListener { appUpdateInfo: AppUpdateInfo ->
                        if (appUpdateInfo.installStatus() == InstallStatus.DOWNLOADED) {
                            popupSnackbarForCompleteUpdate()
                        }
                    }

                }
                if (notificationReceiver != null) {
                    registerReceiver(notificationReceiver, IntentFilter(PrepjoyComponentMessagingInterface.BROADCAST_ACTION_NOTIFICATION_RECEIVE), Context.RECEIVER_NOT_EXPORTED)
                }
                if (this::textUserName.isInitialized)
                    textUserName.text = WonderPubSharedPrefs.getInstance(context).username


                try {
                    val window: Window = <EMAIL>()
                    window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
                    window.navigationBarColor =
                            ContextCompat.getColor(this@DashBoardActivity, R.color.primary_bg_red)

                    val decorView = getWindow().decorView //set status background black
                    decorView.systemUiVisibility =
                            decorView.systemUiVisibility and View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv() //set status text  light
                } catch (e: java.lang.Exception) {
                    e.printStackTrace()
                }

            } catch (e: java.lang.Exception) {
                e.printStackTrace()
            }
            //For Group wall
            if (notificationReceiver != null) {
                registerReceiver(notificationReceiver, IntentFilter(ConstantsHelper.BROADCAST_ACTION_NOTIFICATION_RECEIVE), Context.RECEIVER_NOT_EXPORTED)
            }
            if (notificationDismissReceiver != null) {
                registerReceiver(notificationDismissReceiver, IntentFilter(BROADCAST_ACTION_NOTIFICATION_DISMISS), Context.RECEIVER_NOT_EXPORTED)
            }


            super.onResume()
            Wonderslate.APP_STATE = Lifecycle.Event.ON_RESUME
            Wonderslate.aClass = javaClass
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onPause() {
        Wonderslate.APP_STATE = Lifecycle.Event.ON_PAUSE
        try {
            //For Group wall
            if (notificationReceiver != null)
                unregisterReceiver(notificationReceiver)
            if (notificationDismissReceiver != null)
                unregisterReceiver(notificationDismissReceiver)
            super.onPause()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        super.onPause()
    }

    private fun shareClickOperation() {
        val intent: Intent = if (selectedTab == 4) {
            val fragment = dashBoardAdapter.getItem(viewPager!!.currentItem)
            val screenShot = Utils.getScreenShot(fragment.view)
            if (null == screenShot) {
                Toast.makeText(
                    context,
                    "There was a problem while taking screenshot. Please try again after sometime.",
                    Toast.LENGTH_SHORT
                ).show()
            }
            Utils.shareIntent(Utils.screenShotPath(screenShot, context), context,
                    getString(R.string.leaderboard_share_content))
        } else {
            Utils.homeScreenIntent(context)
        }
        startActivity(intent)
    }

    class DashBoardAdapter(fm: FragmentManager) : FragmentStatePagerAdapter(fm,
            BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT) {
        // Fragments will be loaded in a sequence they
        // are added in the loaded
        private val fragments = listOf(
                HomeFragment(),
                EbooksFragment(),
                AnalyticsFragment(),
                LeaderboardFragment(),
        )

        override fun getItem(position: Int): Fragment {
            return fragments[position]
        }

        override fun getCount(): Int {
            return fragments.size
        }

        fun getEbooksFragment() = fragments[1] as? EbooksFragment

        fun getLeaderBoardFragment() = fragments[2]
    }

    private fun openWebPage(context: Context, url: String?) {
        try {
            if (!URLUtil.isValidUrl(url)) {
                Toast.makeText(context, " This is not a valid link", Toast.LENGTH_LONG).show()
            } else {
                val intent = Intent(Intent.ACTION_VIEW)
                intent.data = Uri.parse(url)
                context.startActivity(intent)
            }
        } catch (e: ActivityNotFoundException) {
            Toast.makeText(
                    context,
                    " You don't have any browser to open web page",
                    Toast.LENGTH_LONG
            ).show()
        }
    }

    @SuppressLint("LongLogTag")
    private fun inAppRating() {
        val reviewManager = ReviewManagerFactory.create(applicationContext)
        val reviewInfo = reviewManager.requestReviewFlow()
        reviewInfo.addOnCompleteListener { task ->
            if (task.isSuccessful) {
                val activity = context as Activity
                val flow = reviewManager.launchReviewFlow(activity, task.result)
                flow.addOnCompleteListener {
                    if (it.isSuccessful) {
                        Log.e("Completed the rate flow", "Success")
                    } else {
                        it.exception?.message?.let { msg ->
                            Log.e("Rating flow not successful", msg)
                        }
                    }
                }
            } else {
                val reviewErrorMessage = task.exception?.message
                reviewErrorMessage?.let { msg ->
                    Log.e("Error requesting in app rating", msg)
                }
            }
        }
    }

    private fun rateApp() {
        Utils.navigateToRateApp(context)
    }

    private fun logoutDialog() {
        val dialogClickListener = DialogInterface.OnClickListener { dialog, which ->
            when (which) {
                DialogInterface.BUTTON_POSITIVE -> {
                    // applicationContext.getSharedPreferences("WonderPublishSP", 0).edit().clear().apply();
                    CoroutineScope(Dispatchers.IO).launch {
                        WonderPubSharedPrefs.getInstance(this@DashBoardActivity).clearAllSharePref()
                        viewModel.clearDB()
                        withContext(Dispatchers.Main) {
                            val intent = Intent(this@DashBoardActivity, LoginActivity::class.java)
                            startActivity(intent)
                            finish()
                        }
                    }
                }
                DialogInterface.BUTTON_NEGATIVE ->
                    dialog.dismiss()
            }
        }
        val builder = AlertDialog.Builder(this)
        builder.setTitle("Confirm Logout?")
        builder.setMessage("Are you sure want to logout?").setPositiveButton("YES", dialogClickListener)
                .setNegativeButton("NO", dialogClickListener).show()
    }

    private fun languageSelectionDialog() {
        try {
            val dialogBuilder: AlertDialog.Builder = AlertDialog.Builder(context)
            val inflater = this.layoutInflater
            val dialogView: View = inflater.inflate(R.layout.dialog_language_selection, null)
            dialogBuilder.setView(dialogView)
            val alertDialog: AlertDialog = dialogBuilder.create()
            val btnEnglish: Button = dialogView.findViewById<View>(R.id.btnEnglish) as Button
            val btnHindi: Button = dialogView.findViewById<View>(R.id.btnHindi) as Button
            val btnCancel: Button = dialogView.findViewById<View>(R.id.btnCacle) as Button
            btnEnglish.setOnClickListener {
                WonderPubSharedPrefs.getInstance(context).sharedPrefsContentLanguagePref = "English"
                alertDialog.dismiss()
            }
            btnHindi.setOnClickListener {
                WonderPubSharedPrefs.getInstance(context).sharedPrefsContentLanguagePref = "Hindi"
                alertDialog.dismiss()
            }
            btnCancel.setOnClickListener {
                alertDialog.dismiss()
            }

            alertDialog.show()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onStart() {
        super.onStart()
        Wonderslate.APP_STATE = Lifecycle.Event.ON_START
        val manager = LocalBroadcastManager.getInstance(this)
        manager.registerReceiver(mMessageReceiver,
                IntentFilter("notification_count"))
        manager.registerReceiver(mMessageReceiver,
                IntentFilter("cafe_notification_count"))
        manager.
        registerReceiver(onlineGameRequestReceiver, IntentFilter("online_game"))
        val wonderPubSharedPrefs = WonderPubSharedPrefs.getInstance(applicationContext)
        if (intent.action != null) {
            val uri = intent.data
            uri?.let {
                if (it.queryParameterNames.contains("utm_source")) {
                    it.getQueryParameter("utm_source")?.let { bookId ->
                        try {
                            val bookDetails = BookDetails(bookId = bookId.toInt())
                            if (!wonderPubSharedPrefs.accessToken.isNullOrEmpty()) {
                                viewModel.logEvent(Event(EventType.IN_APP_MESSAGE))
                                startActivityWithAnim(BookDetailsAct.createIntent(applicationContext, bookDetails))
                            } else {
                                finish()
                            }
                            intent.action = null
                        } catch (e: Exception) {
                            e.message?.let { it1 -> CrashlyticsLogger.logError(WonderPubSharedPrefs.getInstance(applicationContext).usermobile, it1, e) }
                        }
                    }
                }
            }
        }
    }

    override fun onStop() {
        super.onStop()
        Wonderslate.APP_STATE = Lifecycle.Event.ON_STOP
        val manager = LocalBroadcastManager.getInstance(this)
        manager.unregisterReceiver(mMessageReceiver)
        manager.unregisterReceiver(onlineGameRequestReceiver)
    }

    private val mMessageReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            if (intent.hasExtra("cafe_count")) {
                // Get extra data included in the Intent
                val count = intent.getIntExtra("cafe_count", 0)
                Log.d("cafe_count", "Got message: $count")
                if (count > 0) {
                    if (selectedTab != 3) {
                        if (count >= 10)
                            binding.bottomNavigation.setCount(3, "10+")
                        else
                            binding.bottomNavigation.setCount(3, count.toString())

                        isCafeRefresh = true
                    }
                } else {
                    binding.bottomNavigation.setCount(3, "0")
                }

            } else {
                // Get extra data included in the Intent
                val count = intent.getIntExtra("count", 0)
                Log.d("receiver", "Got message: $count")
                if (count > 0) {
                    textNotificationCount.visibility = View.VISIBLE
                    textNotificationCount.text = "$count"
                } else {
                    textNotificationCount.visibility = View.GONE
                }
            }
        }
    }

    private val onlineGameRequestReceiver = object: BroadcastReceiver() {
        override fun onReceive(p0: Context?, p1: Intent?) {
            p1?.let { intent ->
                onGameIntent(intent)
            }
        }
    }

    private fun inflateBookAccess(bookId: String) {
        val sharedPrefs = WonderPubSharedPrefs.getInstance(this@DashBoardActivity)
        val title = alertDialog?.findViewById<TextView>(R.id.title)
        title?.text = getString(R.string.book_access)
        val btnBuy: Button? = alertDialog?.findViewById(R.id.btnHindi)
        btnBuy?.text = "ADD"
        btnBuy?.setEnabled()
        btnBuy?.setOnClickListener {
            try {
                if (bookId.isNotEmpty()) {
                    val id = bookId.toInt()
                    val bookDetails = BookDetails(
                        bookId = id
                    )
                    startActivityWithAnim(BookDetailsAct.createIntent(this, bookDetails))
                    alertDialog?.dismiss()
                } else {
                    eBookNotFound()
                }
            } catch (e: Exception) {
                e.message?.let { it1 -> CrashlyticsLogger.logError(WonderPubSharedPrefs.getInstance(applicationContext).usermobile, it1, e) }
                eBookNotFound()
            }
        }
    }

    private fun eBookNotFound() {
        Toast.makeText(applicationContext,
            "Sorry it seems we could not find the ebook", Toast.LENGTH_SHORT).show()
    }

    private fun buildDialog(code: String? = null,
                            challenger: String? = null,
                            bookId: String? = null) {
        val dialogBuilder: AlertDialog.Builder = AlertDialog.Builder(this@DashBoardActivity)
        val sharedPrefs = WonderPubSharedPrefs.getInstance(this@DashBoardActivity)
        val dialogView: View =
            layoutInflater.inflate(R.layout.dialog_language_selection, null)
        dialogBuilder.setView(dialogView)
        dialogBuilder.setCancelable(false)
        alertDialog = dialogBuilder.create()
        val title = dialogView.findViewById<TextView>(R.id.title)
        title.text = "Invite to play"
        if (null != challenger) {
            title.text = "$challenger has invited you to play"
        }
        val btnDecline: Button =
            dialogView.findViewById<View>(R.id.btnEnglish) as Button
        val btnAccept: Button = dialogView.findViewById<View>(R.id.btnHindi) as Button
        val btnCancel: Button = dialogView.findViewById<View>(R.id.btnCacle) as Button
        btnAccept.text = "Accept"
        btnDecline.text = "Decline"
        btnDecline.background = AppCompatResources.getDrawable(baseContext,
            R.drawable.textview_rounded)
        btnDecline.setTextColor(resources.getColor(R.color.primary_bg_red))
        btnAccept.setOnClickListener {
            if (code != null) {
                viewModel.challengerCode(code, bookId = bookId)
                isDeclineSelected = false
            } else {
                if (sharedPrefs.accessCode.isNotEmpty()) {
                    viewModel.challengerCode(sharedPrefs.accessCode)
                    isDeclineSelected = false
                }
            }
            btnAccept.isEnabled = false
        }
        btnDecline.setOnClickListener {
            if (code != null) {
                viewModel.challengerCode(code)
                isDeclineSelected = true
            } else {
                if (sharedPrefs.accessCode.isNotEmpty()) {
                    viewModel.challengerCode(sharedPrefs.accessCode)
                    isDeclineSelected = true
                }
            }
            btnDecline.isEnabled = false
        }
        btnCancel.setOnClickListener {
            if (code != null) {
                viewModel.challengerCode(code)
                isDeclineSelected = true
            } else {
                if (sharedPrefs.accessCode.isNotEmpty()) {
                    viewModel.challengerCode(sharedPrefs.accessCode)
                    isDeclineSelected = true
                }
            }
            alertDialog?.dismiss()
        }
    }

    private fun onGameIntent(intent: Intent): Boolean {
        var result = false
        if (intent.hasExtra("accessCode") && !intent.hasExtra("accept")) {
            alertDialog?.dismiss()
            val code = intent.getStringExtra("accessCode")
            val challenger = intent.getStringExtra("challenger").takeIf {
                intent.hasExtra("challenger")
            }
            val bookId = intent.getStringExtra("bookId").takeIf { intent.hasExtra("bookId") }
            val sharedPrefs = WonderPubSharedPrefs.getInstance(this@DashBoardActivity)
            sharedPrefs.accessCode = code
            if (null != challenger) {
                sharedPrefs.challenger = challenger
            }
            if (null != bookId) {
                sharedPrefs.sharedPrefsUserChallengerBookId = bookId
            }
            buildDialog(code, challenger, bookId)
            if (!isFinishing) {
                    alertDialog?.show()
            }
            result = true
        }
        return result
    }

    private fun validateGameCode(data: JSONObject): Boolean {
        return data.has("status") && data.getString("status").
        lowercase(Locale.getDefault()) == "success"
    }

    private fun observeJoinGame(): Observer<Data<JSONObject>> {
        return Observer<Data<JSONObject>> {
                data ->

                    val indicator: LinearProgressIndicator? = alertDialog?.findViewById(R.id.progressIndicator)
                    when(data.responseType) {
                        Status.LOADING -> { indicator?.visibility = View.VISIBLE }
                        Status.SUCCESSFUL -> {
                            if (data.data != null) {
                                val jsonObject = JSONObject(data.data.toString())
                                if (validateGameCode(jsonObject)) {
                                    if (isDeclineSelected) {
                                        val json = JSONObject(data.data.toString())
                                        if (json.has("userChallengeId")) {
                                            val user = WonderPubSharedPrefs.getInstance(context).username
                                            val id = json.optString("userChallengeId")
                                            viewModel.declineCode(id, user)
                                            alertDialog?.dismiss()
                                        }
                                    } else if (jsonObject.has("hasAccess")
                                        && !jsonObject.optBoolean("hasAccess")
                                        && jsonObject.has("bookId")) {
                                        inflateBookAccess(jsonObject.optString("bookId"))
                                    } else {
                                        alertDialog?.dismiss()
                                        quizIntent.putExtra("data", data.data.toString())
                                        startActivity(quizIntent)
                                    }
                                }
                                indicator?.visibility = View.GONE
                            }
                        }
                        Status.ERROR, Status.HTTP_UNAVAILABLE -> {
                            indicator?.visibility = View.GONE
                            Toast.makeText(applicationContext, "Could not load access code",
                                Toast.LENGTH_SHORT).show()
                        }
                    }
        }
    }

    private fun weeklyWinnersRank(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {

            }
            Status.SUCCESSFUL -> {
                data.data?.let {
                    if (selectedTab != 4) {
                        activateTicker(data.data)
                    }
                }

            }
            Status.HTTP_UNAVAILABLE, Status.ERROR -> {

            }
        }
    }

    private fun registerNotificationReceivers() {
        notificationReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                if (intent.getStringExtra("resType") != null && intent.getStringExtra("resType")!!.isNotEmpty()) {
                    if (null != homeFragment) {
                        homeFragment?.refreshCards(intent.getStringExtra("resType"))
                    }
                }
            }
        }
        registerReceiver(notificationReceiver, IntentFilter(PrepjoyComponentMessagingInterface.BROADCAST_ACTION_NOTIFICATION_RECEIVE), Context.RECEIVER_NOT_EXPORTED)

    }

    override fun onNewIntent(intent: Intent?) {
        try {
            reloadContext = intent!!.getStringExtra("context")
            if (reloadContext != null && reloadContext.equals("navigate_to_group_wall", ignoreCase = true)) {
                viewPager!!.currentItem = 2
            } else if (reloadContext != null && reloadContext.equals("navigate_to_current_affairs", ignoreCase = true)) {
                homeFragment!!.refreshHomePage()
            }
            else if (reloadContext != null && reloadContext.equals("navigate_to_shop", ignoreCase = true)) {
                onEBooksClicked("Cart")
            }
            super.onNewIntent(intent)
        } catch (e: Exception) {
        }
    }

    private fun initBottomNavigation() {
        try {
            dashBoardAdapter = DashBoardAdapter(supportFragmentManager)
            homeFragment = dashBoardAdapter.getItem(0) as HomeFragment
            leaderBoardFragment = dashBoardAdapter.getItem(3) as LeaderboardFragment
            viewPager!!.adapter = dashBoardAdapter
            viewPager?.let {
                it.offscreenPageLimit = 5
            }
            selectedTab = 1
            binding.bottomNavigation.setOnShowListener { }
            binding.bottomNavigation.show(1, true)
            shareApp.show()
            binding.bottomNavigation.add(MeowBottomNavigation.Model(1, R.drawable.home_white_checked))
            binding.bottomNavigation.add(MeowBottomNavigation.Model(2, R.drawable.book))
            binding.bottomNavigation.add(MeowBottomNavigation.Model(3, R.drawable.analytics_icon))
            binding.bottomNavigation.add(MeowBottomNavigation.Model(4, R.drawable.leaderboard_2))
            binding.bottomNavigation.selectedIconColor = R.color.primary_bg_red
            binding.bottomNavigation.setOnClickMenuListener {
                when (it.id) {
                    1 -> {
                        enableTicker()
                        selectedTab = 1
                        binding.bottomNavigation.show(1, true)
                        if (viewPager?.currentItem == 0) {
                            homeFragment?.displayDashBoard()
                            isDailyTestOpen = false
                        }
                        viewPager?.currentItem = 0
                        shareApp.show()

                        imageChangePreferance?.visibility = if ((!BuildConfig.FLAVOR.equals(Flavours.NEET.flavour, ignoreCase = true))) View.VISIBLE else View.GONE
                        viewModel.getCurrentFragmentPosition(0)
                        textOpenNav.text = "Home"
                    }
                    2 -> {
                        enableTicker()
                        selectedTab = 2
                        binding.bottomNavigation.show(2, true)
                        viewPager?.currentItem = 1
                        shareApp.hide()
                        viewModel.logEvent(
                                Event(
                                        type = EventType.SHOP_BOOKS_PAGE_OPENED,
                                        currentScreen = "Bottom navigation",
                                )
                        )
                        viewModel.getCurrentFragmentPosition(1)
                        textOpenNav.text = "Books"
                    }
                    3 -> {
                        enableTicker()
                        selectedTab = 3
                        binding.bottomNavigation.show(3, true)
                        viewPager?.currentItem = 2
                        shareApp.hide()
                        imageChangePreferance?.visibility = View.GONE
                        /*WonderPubSharedPrefs.getInstance(this).cafeNotificationCount = 0
                        bottomNavigation.setCount(3, MeowBottomNavigationCell.EMPTY_VALUE)
                        if (isCafeRefresh) {
                            val cafeFragment = dashBoardAdapter.getItem(2) as GroupWallFragment
                            cafeFragment.updateCafe()
                        }*/
                        viewModel.getCurrentFragmentPosition(2)
                        textOpenNav.text = "Analytics"
                    }
                    4 -> {
                        disableTicker()
                        selectedTab = 4
                        binding.bottomNavigation.show(4, true)
                        viewPager?.currentItem = 3
                        shareApp.show()
                        imageChangePreferance?.visibility = View.GONE
                        viewModel.getCurrentFragmentPosition(3)
                        textOpenNav.text = "Leaderboard"
                    }
                    else -> ""
                }
            }
            viewPager?.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
                override fun onPageScrolled(
                        position: Int,
                        positionOffset: Float,
                        positionOffsetPixels: Int
                ) {

                }

                override fun onPageSelected(position: Int) {
                    val item = position + 1
                    binding.bottomNavigation.show(item, true)
                    when (position) {
                        0 -> shareApp.show()
                        3 -> shareApp.show()
                        else -> shareApp.hide()
                    }
                }

                override fun onPageScrollStateChanged(state: Int) {

                }

            })
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onBookItemClicked(bookData: ShopBookData, from: String) {
        viewModel.logEvent(
                Event(
                        type = EventType.BOOK_DETAILS_PAGE_OPENED,
                        currentScreen = from,
                        value = Pair("BookId", bookData.id.toString())
                )
        )
        val bookDetails = BookDetails(
                bookData.id,
                bookData.title,
                bookData.coverImage,
                false,
                bookData.publisherId,
                bookData.publisher
        )
        startActivityWithAnim(BookDetailsAct.createIntent(this, bookDetails))
    }

    override fun onLibraryBookItemClicked(bookId: Int, bookType: String) {
        startActivityWithAnim(
                ChaptersListAct.createIntent(
                        this,
                        ChaptersListConfig(
                                bookId = bookId,
                                isPreview = false,
                                bookType = bookType
                        )
                )
        )
    }

    override fun onEBooksClicked(from: String) {
        if (!from.equals("Quiz Summary", true)) {
            WonderPubSharedPrefs.getInstance(context).quizFilterLevelSearch = ""
        }
        viewModel.logEvent(
                Event(
                        type = EventType.SHOP_BOOKS_PAGE_OPENED,
                        currentScreen = from
                )
        )
        selectedTab = 2
        binding.bottomNavigation.show(2, true)
        viewPager?.currentItem = 1
        textOpenNav.text = "Books"

        // Select "Ebooks" tab
        if (from.equals("purchased", true)) {
            WonderPubSharedPrefs.getInstance(this).setRedirectToLibrary(true)
        }
        else {
            WonderPubSharedPrefs.getInstance(this).setRedirectToLibrary(false)
        }
        dashBoardAdapter.getEbooksFragment()?.selectTab(0)

        shareApp.hide()

    }

    override fun onCafeClick() {
        viewModel.logEvent(
                Event(
                        type = EventType.CAFE_PAGE_OPENED,
                        currentScreen = "Dashboard - Cafe card"
                )
        )

        val intent = Intent(this@DashBoardActivity,GroupWallActivity::class.java)
        startActivity(intent)
    }

    override fun onBackPressed() {
        if (selectedTab == 2 || selectedTab == 3 || selectedTab == 4) {
            selectedTab = 1
            binding.bottomNavigation.show(1, true)
            viewPager?.currentItem = 0
            textOpenNav.text = "Home"
            shareApp.show()
            enableTicker()
        } else {
            if (isDailyTestOpen) {
                homeFragment!!.navigateBack()
                isDailyTestOpen = false
            } else {
                super.onBackPressed()
                finish()
            }
        }
    }

    fun isDailyTestOpen(isOpen: Boolean) {
        isDailyTestOpen = isOpen
    }

    internal object NotificationID {
        private val c = AtomicInteger(0)
        val iD: Int
            get() = c.incrementAndGet()
    }

    override fun redirectToLeaderBoard(quizMode: String?) {
        disableTicker()
        if (leaderBoardFragment != null && quizMode.equals("weekly", true)) {
            leaderBoardFragment!!.switchToWeekly()
        }
        selectedTab = 4
        binding.bottomNavigation.show(4, true)
        viewPager?.currentItem = 3
        shareApp.show()
        imageChangePreferance?.visibility = View.GONE
        viewModel.getCurrentFragmentPosition(3)
        textOpenNav.text = "Leaderboard"
    }
}