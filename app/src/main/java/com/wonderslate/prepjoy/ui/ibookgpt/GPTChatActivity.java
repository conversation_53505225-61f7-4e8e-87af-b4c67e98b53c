package com.wonderslate.prepjoy.ui.ibookgpt;

import static com.wonderslate.data.db.WonderPublishDBContract.USER_DATABASE_NAME;

import android.app.Dialog;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.itextpdf.io.font.constants.StandardFonts;
import com.itextpdf.io.image.ImageData;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.io.source.ByteArrayOutputStream;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.AffineTransform;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.extgstate.PdfExtGState;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.VerticalAlignment;
import com.skydoves.powerspinner.OnSpinnerItemSelectedListener;
import com.skydoves.powerspinner.PowerSpinnerView;
import com.wonderslate.data.db.WonderPublishDataSource;
import com.wonderslate.data.interfaces.WSCallback;
import com.wonderslate.data.models.ChatExtra;
import com.wonderslate.data.models.ChatMessage;
import com.wonderslate.data.models.FlashCard;
import com.wonderslate.data.models.GPTPrompts;
import com.wonderslate.data.models.GptLog;
import com.wonderslate.data.models.GptLogResponse;
import com.wonderslate.data.models.GptLogsResponse;
import com.wonderslate.data.models.MCQ;
import com.wonderslate.data.models.QA;
import com.wonderslate.data.network.Wonderslate;
import com.wonderslate.data.preferences.WonderPubSharedPrefs;
import com.wonderslate.data.repository.BookGPT;
import com.wonderslate.prepjoy.R;
import com.wonderslate.prepjoy.Utils.CustomSnackBar;
import com.wonderslate.prepjoy.Utils.FlashcardFormatter;
import com.wonderslate.prepjoy.Utils.FlashcardJSONParser;
import com.wonderslate.prepjoy.Utils.InternetConnectionChecker;
import com.wonderslate.prepjoy.Utils.MCQFormatter;
import com.wonderslate.prepjoy.Utils.MCQJSONParser;
import com.wonderslate.prepjoy.Utils.QAFormatter;
import com.wonderslate.prepjoy.Utils.QAJSONParser;
import com.wonderslate.prepjoy.ui.BaseActivity;
import com.wonderslate.prepjoy.ui.quiz.QuizActivity;
import com.wonderslate.prepjoy.ui.resources.reading.utils.DataHolder;
import com.wonderslate.prepjoy.ui.resources.weblink.WeblinkAct;
import com.ws.database.room.entity.Resource;
import com.ws.resources.data.models.AnnotationsRequest;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.stream.Collectors;

public class GPTChatActivity extends BaseActivity{

    private static final String TAG = "GPTChatActivity";

    private CardView btnBack;
    private TextView pageTitle;

    private RecyclerView recyclerView, promptRecyclerView;
    private CardView promptCardView;
    private GPTChatAdapter chatAdapter;
    private List<ChatMessage> messages;
    private EditText messageInput;
    private Button sendButton, upgradeGPTButton;
    private ImageView sttButton, attachmentIcon;
    private LinearLayout gptLoaderLayout;
    private RelativeLayout messageInputLayout, attachmentContainer;
    private ImageButton removeAttachment;
    private TextView mcqDisclaimerText, emptyChatTxt;

    private String resId, chapterId, chapterTitle, bookId, resName, bookTitle;
    private int selectedChapter;
    private List<GPTPrompts> promptList;
    boolean containsQNA = false;
    boolean containsMCQS = false;
    private PromptAdapter promptAdapter;
    private JSONArray chatHistoryArray;
    private String formattedMCQ, formattedFlashcards, noOfFlashcards, formattedQna, qnaRawString,
            flashCardResId;
    public static JSONObject mcqObject;
    private boolean showAnswers = false;
    private boolean isPreview = false;
    private int freeTokenCount = 0;
    private int paidTokenCount = 0;
    //private TextView tvDoubtsRecharge, tvFreeTokenCount, tvPaidTokenCount, tvBuyBook;
    private List<GptLogResponse> gptLogResponses;
    private boolean hasPurchasedGPT, showGPTUpgrade, isResOption;
    private String gptUpgradePrice;
    private CardView gptUpgradeCardView;
    private String highLightedDoubt = "";
    private String bookLanguage, bookType, resOptionType, resOptionLang, resOptionQuery;
    private static final int REQUEST_RECORD_AUDIO_PERMISSION = 200;
    private String imgData, imgUrl, finalNumberOfMCQs, finalNumberOfQuestions;
    private String mcqQuizId, systemPrompt;

    private PowerSpinnerView promptSpinner;
    private LinearLayout emptyChatView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        initFieldsFromIntent();
        init();
    }

    // Initialize fields from intent
    private void initFieldsFromIntent() {
        resId = getIntent().getStringExtra("resId");
        chapterId = getIntent().getStringExtra("chapterId");
        bookId = getIntent().getStringExtra("bookId");
        chapterTitle = getIntent().getStringExtra("chapterTitle");
        bookTitle = getIntent().getStringExtra("bookTitle");
        resName = getIntent().getStringExtra("resName");
        selectedChapter = getIntent().getIntExtra("selectedChapter", 0);
        isPreview = getIntent().getBooleanExtra("isPreview", false);
        highLightedDoubt = getIntent().getStringExtra("doubt");
        bookLanguage = getIntent().getStringExtra("bookLanguage");
        bookType = getIntent().getStringExtra("bookType");
        imgData = DataHolder.INSTANCE.getLargeData();
        imgUrl = getIntent().getStringExtra("imgUrl");

        if (getIntent().hasExtra("isResOption") && getIntent().getBooleanExtra("isResOption", false)) {
            isResOption = true;
            resOptionType = getIntent().getStringExtra("resOption");
            resOptionLang = getIntent().getStringExtra("resOptionLang");
            resOptionQuery = getIntent().getStringExtra("resOptionQuery");
            mcqQuizId = String.valueOf(getIntent().getIntExtra("quizId", 0));
        }

        chatHistoryArray = new JSONArray();
    }

    @Override
    protected int getLayoutResource() {
        return R.layout.activity_gptchat;
    }

    private void init() {

        String gptPurchaseDetails = Wonderslate.getInstance().getSharedPrefs().getGPTPurchaseStatus();

        try {
            if (!gptPurchaseDetails.isEmpty()) {
                JSONObject gptPurchaseDetailsObject = new JSONObject(gptPurchaseDetails);
                hasPurchasedGPT = gptPurchaseDetailsObject.optBoolean("purchasedGPT");
                showGPTUpgrade = gptPurchaseDetailsObject.optBoolean("showUpgrade");
                gptUpgradePrice = gptPurchaseDetailsObject.optString("prices");
            }

        } catch (JSONException e) {
            Log.e(TAG, "Exception while convering string to json", e);
        }

        btnBack = findViewById(R.id.btnBack);
        pageTitle = findViewById(R.id.tvResourceName);
        pageTitle.setText(chapterTitle);
        recyclerView = findViewById(R.id.recyclerView);
        promptRecyclerView = findViewById(R.id.promptRecyclerView);
        DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(recyclerView.getContext(), DividerItemDecoration.VERTICAL);
        dividerItemDecoration.setDrawable(Objects.requireNonNull(ContextCompat.getDrawable(this, R.drawable.prompt_divider_item_decoration)));
        promptRecyclerView.addItemDecoration(dividerItemDecoration);
        promptCardView = findViewById(R.id.promptLayout);
        messageInput = findViewById(R.id.messageInput);
        sendButton = findViewById(R.id.sendButton);
        sttButton = findViewById(R.id.sttButton);
        gptLoaderLayout = findViewById(R.id.gptLoaderLayout);
        showHideLoader(true);
        promptSpinner = findViewById(R.id.promptSpinner);
        if (isResOption) {
            promptSpinner.setVisibility(View.GONE);
        }
        messageInputLayout = findViewById(R.id.messageInputLayout);
        messageInputLayout.setVisibility(View.VISIBLE);
        attachmentContainer = findViewById(R.id.attachmentContainer);
        attachmentIcon = findViewById(R.id.attachmentIcon);
        removeAttachment = findViewById(R.id.removeAttachment);
        mcqDisclaimerText = findViewById(R.id.mcqDisclaimerTxt);
        if (isResOption) {
            mcqDisclaimerText.setVisibility(View.VISIBLE);
        }
        if (highLightedDoubt != null && !highLightedDoubt.isEmpty()) {
            messageInput.setText(highLightedDoubt);
        }
        if (imgData != null && !imgData.isEmpty()) {
            attachmentContainer.setVisibility(View.VISIBLE);
            setupAttachmentFeature(attachmentContainer, removeAttachment);
        }
        gptUpgradeCardView = findViewById(R.id.gptUpgradeLayout);
        upgradeGPTButton = findViewById(R.id.upgradeGPTButton);
        emptyChatView = findViewById(R.id.emptyChatView);
        emptyChatTxt = findViewById(R.id.emptyChatTxt);

        //Add text watcher to messageInput
        messageInput.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                if (!charSequence.toString().trim().isEmpty()) {
                    promptCardView.setVisibility(View.GONE);
                    //gptFabMain.setImageResource(R.drawable.gpt_menu_24); // Set to hamburger icon
                    gptUpgradeCardView.setVisibility(View.GONE);
                    sttButton.setVisibility(View.GONE);
                }
                else {
                    sttButton.setVisibility(View.GONE);
                }
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });

        if (!hasPurchasedGPT && showGPTUpgrade) {
            gptUpgradeCardView.setVisibility(View.GONE);
        }
        else {
            gptUpgradeCardView.setVisibility(View.GONE);
        }

        btnBack.setOnClickListener(v -> {
            deleteSnip(imgUrl);
            finish();
        });

        checkPDFExist();

        setChatAdapter();

        sendButton.setOnClickListener(v -> {
            hideKeyboard();
            String messageText = messageInput.getText().toString();
            if (!messageText.isEmpty()) {
                List<ChatExtra> extras = new ArrayList<>();
                if (imgUrl != null && !imgUrl.isEmpty()) {
                    extras.add(new ChatExtra("image", "image", imgUrl));
                }
                ChatMessage userMessage = new ChatMessage(messageText, true, extras, true, false);
                messages.add(userMessage);
                chatAdapter.notifyItemInserted(messages.size() - 1);
                toggleEmptyChatView();
                scrollToNewMessage();
                messageInput.setText("");
                showChatLoader();

                new BookGPT().getAIResponse(resId, chapterId, messageText, chatHistoryArray, imgData, new WSCallback() {
                    @Override
                    public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                        imgData = "";
                        DataHolder.INSTANCE.setLargeData("");
                        deleteSnip(imgUrl);
                        imgUrl = "";
                        setupAttachmentFeature(attachmentContainer, removeAttachment);
                        JSONObject chatHistoryObject = new JSONObject();
                        try {
                            chatHistoryObject.put("user", messageText);
                            chatHistoryObject.put("ai", jsonObject.optString("answer"));
                            chatHistoryArray.put(chatHistoryObject);
                            saveChat(messageText, jsonObject.optString("answer"), jsonObject.optString("imgLink"));
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        String response = jsonObject.optString("answer");
                        replaceLoaderWithAIResponse(response, null, "", true);
                    }

                    @Override
                    public void onWSResultFailed(String resString, int responseCode) {
                        imgData = "";
                        DataHolder.INSTANCE.setLargeData("");
                        setupAttachmentFeature(attachmentContainer, removeAttachment);
                        replaceLoaderWithAIResponse("K.ai is not available for this chapter. Please contact support.", null, "", true);
                    }
                });
            }
        });

        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                ImageView downArrow = findViewById(R.id.downArrow);

                if (!recyclerView.canScrollVertically(1)) {
                    // User is at the bottom of the list
                    downArrow.setVisibility(View.GONE);
                } else if (dy < 0) {
                    // User has scrolled up
                    downArrow.setVisibility(View.GONE);
                } else if (dy > 0) {
                    // User has scrolled down
                    downArrow.setVisibility(View.GONE);
                }

                downArrow.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        if (chatAdapter != null && chatAdapter.getItemCount() > 0) {
                            recyclerView.scrollToPosition(chatAdapter.getItemCount() - 1);
                        }
                    }
                });
            }
        });
    }

    private void scrollToNewMessage() {
        recyclerView.post(() -> {
            if (chatAdapter.getItemCount() > 0) {
                int lastPosition = chatAdapter.getItemCount() - 1;

                // Find the last item view
                RecyclerView.ViewHolder holder = recyclerView.findViewHolderForAdapterPosition(lastPosition);
                if (holder != null) {
                    int itemHeight = holder.itemView.getHeight(); // Get item height
                    recyclerView.smoothScrollBy(0, itemHeight / 2); // Scroll half of the new message
                } else {
                    // Fallback: scroll to position normally
                    recyclerView.scrollToPosition(lastPosition);
                }
            }
        });
    }

    public void hideKeyboard() {
        View view = getCurrentFocus();
        if (view != null) {
            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
        }
    }

    private void checkPDFExist() {
        new BookGPT().checkPDFExist(resId, chapterId, new WSCallback() {
            @Override
            public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                try {
                    boolean isExist = jsonObject.getBoolean("isExist");

                    if (!isResOption) {
                        if (!isExist) {
                            new BookGPT().uploadPDF(bookId, chapterId
                                    , resId, new WSCallback() {
                                        @Override
                                        public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                                            getChatHistory();
                                            getDefaultPrompts();
                                        }

                                        @Override
                                        public void onWSResultFailed(String resString, int responseCode) {
                                            Log.d(TAG, "Response: " + resString);
                                        }
                                    });
                        }
                        else {
                            getChatHistory();
                            getDefaultPrompts();
                        }
                    }
                    else {
                        getChatHistory();
                    }

                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onWSResultFailed(String resString, int responseCode) {
                Log.d(TAG, "Response: " + resString);
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    private void navigateToNotes(JSONObject readNotesJson) {
        try {
            if (readNotesJson != null && readNotesJson.has("total") && !readNotesJson.getString("total").equalsIgnoreCase("0")) {
                Intent notesIntent = new Intent(this, NotesActivity.class);
                AnnotationsRequest annotationsRequest = new AnnotationsRequest(
                        resId,
                        bookId,
                        "",
                        readNotesJson.toString(),
                        true
                );
                notesIntent.putExtra("extra_annotations_request", annotationsRequest);
                startActivity(notesIntent);
            } else {
//                        Toast.makeText(this, "No notes available.", Toast.LENGTH_SHORT).show();
                customSnackBar.showActionSnackBar("No highlights available.", "OK", CustomSnackBar.LENGTH_INDEFINITE, () -> customSnackBar.dismiss());
            }
        } catch (JSONException e) {
            Log.e(TAG, e.getMessage());
        }
    }

    private void setupAttachmentFeature(RelativeLayout attachmentContainer, ImageButton removeAttachmentButton) {
        // Initially hide the attachment container
        attachmentContainer.setVisibility(View.GONE);

        // Handle the visibility of the attachment
        if (imgData != null && !imgData.isEmpty()) {
            attachmentContainer.setVisibility(View.VISIBLE);
        } else {
            attachmentContainer.setVisibility(View.GONE);
        }

        if (imgUrl != null) {
            Glide.with(this)
                    .load(Uri.parse(imgUrl))
                    .placeholder(R.drawable.ic_image) // Optional placeholder
                    .error(R.drawable.ic_image)             // Optional error image
                    .into(attachmentIcon);
        } else {
            attachmentIcon.setImageResource(R.drawable.ic_image); // Fallback if URI is null
        }

        // Handle remove attachment button click
        removeAttachmentButton.setOnClickListener(v -> {
            // Clear the Base64 image string
            imgData = "";
            DataHolder.INSTANCE.setLargeData("");
            attachmentContainer.setVisibility(View.GONE);
            // Optionally, notify the user
            Toast.makeText(this, "Attachment removed", Toast.LENGTH_SHORT).show();
            deleteSnip(imgUrl);
        });

        // Show popup with the image when the attachment icon is clicked
        if (imgUrl != null && !imgUrl.isEmpty()) {
            attachmentIcon.setOnClickListener(v -> showImagePopup(imgUrl));
        }
    }

    private void deleteSnip(String imgUrl) {
        // Delete the full-page screenshot
        try {
            File fdelete = new File(Objects.requireNonNull(Uri.parse(imgUrl).getPath()));
            if (fdelete.exists()) {
                fdelete.delete();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void showImagePopup(String imageUrl) {
        // Create a Dialog for the popup
        Dialog dialog = new Dialog(this);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setContentView(R.layout.dialog_image_attachment_popup);

        // Get the ImageView from the dialog layout
        ImageView popupImageView = dialog.findViewById(R.id.popupImageView);

        // Load the image into the popup ImageView
        Glide.with(this)
                .load(Uri.parse(imageUrl))
                .placeholder(R.drawable.ic_image) // Optional placeholder
                .error(R.drawable.ic_image)       // Optional error image
                .into(popupImageView);

        // Set the dialog to close when clicking outside
        dialog.setCanceledOnTouchOutside(true);

        // Show the dialog
        dialog.show();
    }


    private void getDefaultPrompts() {
        showHideLoader(true);

        if (bookType.equalsIgnoreCase("gptbook")) {
            new BookGPT().getDefaultPrompts(resId, new WSCallback() {
                @Override
                public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                    showHideLoader(false);
                    toggleEmptyChatView();

                    try {
                        promptList = processPromptsFromJson(jsonObject);
                    }
                    catch (Exception e) {
                        Log.e(TAG, e.getMessage());
                        promptList = new ArrayList<>();
                        customSnackBar.show("No prompts available. Please contact customer support.", CustomSnackBar.LENGTH_SHORT);
                    }
                    // Check if default prompts have qna or msqs
                    for (GPTPrompts prompt : promptList) {
                        if (prompt.getPromptType().equalsIgnoreCase("qna")) {
                            containsQNA = true;
                        }
                        if (prompt.getPromptType().equalsIgnoreCase("mcqs")
                                || prompt.getPromptType().equalsIgnoreCase("mcq")) {
                            containsMCQS = true;
                        }
                    }
                    // Add test prompt if qna or mcqs are present
                    if (containsMCQS || containsQNA) {
                        //Create new prompt type
                        GPTPrompts prompt = new GPTPrompts();
                        prompt.setPromptType("test");
                        prompt.setPromptLabel("Give Test");
                        prompt.setLocalIconPath(R.drawable.give_test);
                        promptList.add(prompt);
                    }

                    // Add doubts & Highlights prompt always
                    GPTPrompts highlightsPrompt = new GPTPrompts();
                    highlightsPrompt.setPromptType("Highlights");
                    highlightsPrompt.setPromptLabel("Highlights & Notes");
                    highlightsPrompt.setLocalIconPath(R.drawable.highlight);
                    promptList.add(highlightsPrompt);

                    setupRecyclerView();
                }

                @Override
                public void onWSResultFailed(String resString, int responseCode) {
                    showHideLoader(false);
                    // Add doubts & Highlights prompt always
                    GPTPrompts highlightsPrompt = new GPTPrompts();
                    highlightsPrompt.setPromptType("highlights");
                    highlightsPrompt.setPromptLabel("Highlights & Notes");
                    highlightsPrompt.setLocalIconPath(R.drawable.highlight);
                    promptList = new ArrayList<>();
                    promptList.add(highlightsPrompt);

                    setupRecyclerView();
                }
            });
        }
        else {
            // Add doubts & Highlights prompt always
            showHideLoader(false);
            GPTPrompts highlightsPrompt = new GPTPrompts();
            highlightsPrompt.setPromptType("Highlights");
            highlightsPrompt.setPromptLabel("Highlights & Notes");
            highlightsPrompt.setLocalIconPath(R.drawable.highlight);
            promptList = new ArrayList<>();
            promptList.add(highlightsPrompt);

            setupRecyclerView();
        }
    }

    private ArrayList<GPTPrompts> processPromptsFromJson(JSONObject jsonObject) {
        // Create Gson instance
        Gson gson = new Gson();

        // Parse the entire JSON object
        Type listType = new TypeToken<ArrayList<GPTPrompts>>() {}.getType();

        // Extract the "gpts" and "defaultPrompts" arrays
        ArrayList<GPTPrompts> gptsList = new ArrayList<>();
        ArrayList<GPTPrompts> defaultPromptsList = new ArrayList<>();

        try {
            if (jsonObject.has("gpts")) {
                gptsList = gson.fromJson(jsonObject.get("gpts").toString(), listType);
            }
            if (jsonObject.has("defaultPrompts")) {
                defaultPromptsList = gson.fromJson(jsonObject.get("defaultPrompts").toString(), listType);
            }
        } catch (Exception e) {
            // Log error if JSON parsing fails (optional)
            e.printStackTrace();
        }

        // Handle null or empty lists
        if (gptsList == null) {
            gptsList = new ArrayList<>();
        }
        if (defaultPromptsList == null) {
            defaultPromptsList = new ArrayList<>();
        }

        // Combine both lists into one
        ArrayList<GPTPrompts> combinedList = new ArrayList<>(gptsList);
        combinedList.addAll(defaultPromptsList);

        // Remove duplicates based on 'promptType'
        LinkedHashMap<String, GPTPrompts> uniquePromptsMap = new LinkedHashMap<>();
        for (GPTPrompts prompt : combinedList) {
            // Use 'promptType' as the unique key
            if (!uniquePromptsMap.containsKey(prompt.getPromptType())) {
                uniquePromptsMap.put(prompt.getPromptType(), prompt);
            }
        }

        // Return the values as an ArrayList
        return new ArrayList<>(uniquePromptsMap.values());
    }

    private void fetchDefaultResponse(String promptType) {
        new BookGPT().getDefaultResponse(resId, promptType, new WSCallback() {
            @Override
            public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                if (jsonObject.optString("hasResource").equalsIgnoreCase("true")) {
                    if (promptType.equalsIgnoreCase("summary")) {
                        String response = jsonObject.optString("answer");
                        replaceLoaderWithAIResponse(response, null, "", true);
                    } else if (promptType.equalsIgnoreCase("qna")) {
                        showCreateQuestionsDialog(jsonObject.optString("resId"), "", promptType);
                    } else {
                        if (promptType.equalsIgnoreCase("mcqs") || promptType.equalsIgnoreCase("mcq")) {
                            showCreateQuestionsDialog(jsonObject.optString("resId"), jsonObject.optString("quizId"), promptType);
                        } else if (promptType.equalsIgnoreCase("flashcards")) {
                            showCreateFlashcardsDialog(jsonObject.optString("resId"));
                        }
                        else {
                            String response = jsonObject.optString("answer");
                            replaceLoaderWithAIResponse(response, null, "", true);
                        }
                    }
                }
                else {
                    showChatLoader();
                    new BookGPT().setupChapter(chapterId, new WSCallback() {
                        @Override
                        public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                            if (jsonObject.optString("status").equalsIgnoreCase("uploaded")) {
                                new BookGPT().createContent(jsonObject.optString("namespace"), promptType, resId, chapterId, new WSCallback() {
                                    @Override
                                    public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                                        if (jsonObject.optString("response").equalsIgnoreCase("added")) {
                                            fetchDefaultResponse(promptType);
                                        }
                                        else {
                                            String response = "K.ai is not available for this chapter. Please contact support.";
                                            replaceLoaderWithAIResponse(response, null, "", true);
                                        }
                                    }

                                    @Override
                                    public void onWSResultFailed(String resString, int responseCode) {
                                        String response = "K.ai is not available for this chapter. Please contact support.";
                                        replaceLoaderWithAIResponse(response, null, "", true);
                                    }
                                });
                            }
                            else {
                                String response = "K.ai is not available for this chapter. Please contact support.";
                                replaceLoaderWithAIResponse(response, null, "", true);
                            }

                        }

                        @Override
                        public void onWSResultFailed(String resString, int responseCode) {
                            String response = "K.ai is not available for this chapter. Please contact support.";
                            replaceLoaderWithAIResponse(response, null, "", true);
                        }
                    });
                }
            }

            @Override
            public void onWSResultFailed(String resString, int responseCode) {
                replaceLoaderWithAIResponse("K.ai is not available for this chapter. Please contact support", null, "", true);
                Log.d(TAG, "onWSResultFailed: " + resString);
            }
        });
    }

    private void getNotesAndAnnotations() {
        InternetConnectionChecker inc = new InternetConnectionChecker();
        Boolean connected = inc.isNetworkConnected(getBaseContext());
        if (connected) {
            new BookGPT().getAnnotations(resId, bookId, "", new WSCallback() {

                @Override
                public void onWSResultSuccess(JSONObject respJson, int responseCode) {
                    stopApiTimer();
                    JSONObject readNotesJson = null;
                    try {
                        if (respJson != null) {
                            if (respJson.has("total") && !respJson.getString("total").equalsIgnoreCase("0")) {
                                JSONArray notesArray = respJson.getJSONArray("rows");
                                if (notesArray.length() > 0) {
                                    readNotesJson = respJson;
                                }
                            } else {
                                readNotesJson = new JSONObject();
                            }
                        } else {
                            JSONArray notesArray = new JSONArray();
                            WonderPublishDataSource dataSource = new WonderPublishDataSource(GPTChatActivity.this, USER_DATABASE_NAME);
                            dataSource.open();
                            List<JSONObject> localAnnotationList = dataSource.getAnnotationActionByResId(resId);
                            dataSource.close();
                            for (int i = 0; i < localAnnotationList.size(); i++) {
                                notesArray.put(localAnnotationList.get(i));
                            }
                            readNotesJson = new JSONObject();
                            readNotesJson.put("rows", notesArray);
                            readNotesJson.put("total", notesArray.length());
                        }
                    } catch (JSONException e) {
                        readNotesJson = new JSONObject();
                        Log.e(TAG, e.getMessage());
                    }
                    navigateToNotes(readNotesJson);

                }

                @Override
                public void onWSResultFailed(String resString, int responseCode) {
                    navigateToNotes(new JSONObject());
                }
            });
        } else {
            WonderPublishDataSource dataSource = new WonderPublishDataSource(GPTChatActivity.this, USER_DATABASE_NAME);
            dataSource.open();
            String jsonString = dataSource.getNotesAnnotationByResId(resId);
            JSONObject readNotesJson = null;
            if (jsonString != null) {
                try {
                    readNotesJson = new JSONObject(jsonString);
                } catch (JSONException e) {
                    Log.e(TAG, e.getMessage());
                    readNotesJson = new JSONObject();
                }
            }
            else {
                readNotesJson = new JSONObject();
            }
            dataSource.close();
            navigateToNotes(readNotesJson);
        }
    }

    private void setupRecyclerView() {
        promptAdapter = new PromptAdapter(this, promptList, promptSpinner);
        promptAdapter.setOnSpinnerItemSelectedListener(new OnSpinnerItemSelectedListener<List<GPTPrompts>>() {
            @Override
            public void onItemSelected(int i, @Nullable List<GPTPrompts> gptPrompts, int i1, List<GPTPrompts> t1) {
                promptSpinner.showOrDismiss();
                GPTPrompts prompt = t1.get(i1);
                //togglePromptLayout();
                String promptLabel;
                if (prompt.getPromptType().equalsIgnoreCase("doubts")) {
                    messageInputLayout.setVisibility(View.VISIBLE);
                } else if (prompt.getPromptType().equalsIgnoreCase("highlights")) {
                    getNotesAndAnnotations();
                } else {
                    if (prompt.getPromptType().equalsIgnoreCase("mcqs")) {
                        promptLabel = " Generate MCQs";
                    } else {
                        promptLabel = prompt.getPromptLabel();
                    }
                    ChatMessage userMessage = new ChatMessage(promptLabel, true, null, true, false);
                    messages.add(userMessage);
                    chatAdapter.notifyItemInserted(messages.size() - 1);
                    toggleEmptyChatView();
                    scrollToNewMessage();
                    if (prompt.getPromptType().equalsIgnoreCase("test")) {
                        showCreateQuestionsDialog(resId, "", prompt.getPromptType());
                    }
                    else {
                        fetchDefaultResponse(prompt.getPromptType());
                    }

                }

            }
        });
        //promptRecyclerView.setAdapter(promptAdapter);
        promptSpinner.setSpinnerAdapter(promptAdapter);
        promptSpinner.setLifecycleOwner(this);
        //promptSpinner.selectItemByIndex(0);
        if (highLightedDoubt.isEmpty()) {
            togglePromptLayout();
        }
        showHideLoader(false);
    }

    private void toggleEmptyChatView() {
        if (messages.isEmpty()) {
            emptyChatView.setVisibility(View.VISIBLE);
            if (isResOption)
                emptyChatTxt.setText("No history for this MCQ. Choose from Explain MCQ, Create Similar MCQs and Give Hint or Ask a doubt to get started.");
        }
        else {
            emptyChatView.setVisibility(View.GONE);
        }
    }

    private void setChatAdapter() {
        messages = new ArrayList<>();
        chatAdapter = new GPTChatAdapter(messages, new GPTChatAdapter.OnItemClickListener() {
            @Override
            public void onItemClick() {
                // Positive feedback
                Toast.makeText(GPTChatActivity.this, "Thank you for your feedback.", Toast.LENGTH_SHORT).show();
                new BookGPT().sendPositiveFeedback("", new WSCallback() {
                    @Override
                    public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {

                    }

                    @Override
                    public void onWSResultFailed(String resString, int responseCode) {

                    }
                });
            }
        }, new GPTChatAdapter.OnItemClickListener() {
            @Override
            public void onItemClick() {
                // Negative feedback
                // Show feedback dialog
                showFeedbackDialog();
            }
        }, new GPTChatAdapter.OnItemClickListener() {
            @Override
            public void onItemClick() {
                // Share
                showHideLoader(true);
                createAndOpenPDF(messages.get(messages.size() - 1), messages.get(messages.size() - 2),
                        bookTitle, chapterTitle, GPTChatActivity.this);
                showHideLoader(false);
            }
        }, new GPTChatAdapter.onButtonClickListener() {
            @Override
            public void onButtonClick(String btnType, int adapterPosition) {
                boolean isPositionalReplaceRequired;

                if (adapterPosition < messages.size() - 1) {
                    isPositionalReplaceRequired = true;
                } else {
                    isPositionalReplaceRequired = false;
                }

                switch (btnType) {
                    case "Show MCQs":
                        List<ChatExtra> extras = new ArrayList<>();
                        extras.add(new ChatExtra("button", "Play with AI", ""));
                        extras.add(new ChatExtra("button", "Practice", ""));
                        extras.add(new ChatExtra("button", "Test", ""));
                        if (isPositionalReplaceRequired) {
                            replaceMessageAtPosition(adapterPosition, formattedMCQ, extras, "", false);
                        } else {
                            replaceLoaderWithAIResponse(formattedMCQ, extras, "", false);
                        }
                        break;
                    case "Play with AI":
                        handleMCQ(resName, mcqObject, bookId, Integer.parseInt(chapterId), resId, false, "play", selectedChapter);
                        break;
                    case "Practice":
                        handleMCQ(resName, mcqObject, bookId, Integer.parseInt(chapterId), resId, false, "practice", selectedChapter);
                        break;
                    case "Test":
                        handleMCQ(resName, mcqObject, bookId, Integer.parseInt(chapterId), resId, false, "test", selectedChapter);
                        break;
                    case "Show Flashcards":
                        List<ChatExtra> extras2 = new ArrayList<>();
                        extras2.add(new ChatExtra("button", "Open Flashcards", ""));
                        if (isPositionalReplaceRequired) {
                            replaceMessageAtPosition(adapterPosition, formattedFlashcards, extras2, "", false);
                        } else {
                            replaceLoaderWithAIResponse(formattedFlashcards, extras2, "", false);
                        }
                        break;
                    case "Open Flashcards":
                        openFlashCard(flashCardResId, noOfFlashcards);
                        break;
                    case "Show QnA":
                        if (isPositionalReplaceRequired) {
                            replaceMessageAtPosition(adapterPosition, formattedQna, null, "", false);
                        } else {
                            replaceLoaderWithAIResponse(formattedQna, null, "", false);
                        }
                        break;
                    case "Show Answers":
                        showAnswers = !showAnswers;
                        List<MCQ> mcqList = MCQJSONParser.parseMCQList(qnaRawString);
                        mcqList = getRandomMCQSubset(GPTChatActivity.this, mcqList, Integer.parseInt(finalNumberOfMCQs));
                        formattedMCQ = MCQFormatter.formatMCQs(mcqList, showAnswers);
                        List<QA> qnaList = QAJSONParser.parseQnA(qnaRawString);
                        qnaList = getRandomQASubset(GPTChatActivity.this, qnaList, Integer.parseInt(finalNumberOfQuestions));
                        formattedQna = QAFormatter.formatQnA(qnaList, showAnswers);
                        List<ChatExtra> extras3 = new ArrayList<>();
                        extras3.add(new ChatExtra("button", "Reset Test", ""));
                        String formattedTest = formattedMCQ + formattedQna;

                        if (isPositionalReplaceRequired) {
                            replaceMessageAtPosition(adapterPosition, formattedTest, extras3, "", false);
                        } else {
                            replaceLoaderWithAIResponse(formattedTest, extras3, "", false);
                        }
                        break;
                    case "Reset Test":
                        showAnswers = !showAnswers;
                        List<MCQ> mcqList2 = MCQJSONParser.parseMCQList(qnaRawString);
                        mcqList2 = getRandomMCQSubset(GPTChatActivity.this, mcqList2, Integer.parseInt(finalNumberOfMCQs));
                        formattedMCQ = MCQFormatter.formatMCQs(mcqList2, showAnswers);
                        List<QA> qnaList2 = QAJSONParser.parseQnA(qnaRawString);
                        qnaList2 = getRandomQASubset(GPTChatActivity.this, qnaList2, Integer.parseInt(finalNumberOfQuestions));
                        formattedQna = QAFormatter.formatQnA(qnaList2, showAnswers);
                        List<ChatExtra> extras4 = new ArrayList<>();
                        extras4.add(new ChatExtra("button", "Show Answers", ""));
                        String formattedTest2 = formattedMCQ + formattedQna;

                        if (isPositionalReplaceRequired) {
                            replaceMessageAtPosition(adapterPosition, formattedTest2, extras4, "", false);
                        } else {
                            replaceLoaderWithAIResponse(formattedTest2, extras4, "", false);
                        }
                        break;
                    default:
                        break;

                }

            }
        }, new GPTChatAdapter.OnReadAloudClickedListener() {
            @Override
            public void onItemClick(String text) {
                /*// Read the text aloud
                if (bottomSheet == null) {
                    bottomSheet = prepareBottomSheet();
                }
                onSpeak(text);*/
            }
        }, new GPTChatAdapter.OnItemClickListener() {
            @Override
            public void onItemClick() {}
        });
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(chatAdapter);
    }

    private void showHideLoader(boolean value) {
        if (value) {
            gptLoaderLayout.setVisibility(View.VISIBLE);
        }
        else {
            gptLoaderLayout.setVisibility(View.GONE);
        }
    }

    private void showChatLoader() {
        ChatMessage loaderMessage = new ChatMessage("", false, null, true, false);
        loaderMessage.setLoader(true); // Mark as loader
        messages.add(loaderMessage);
        chatAdapter.notifyItemInserted(messages.size() - 1);
        toggleEmptyChatView();
        scrollToNewMessage();
    }

    private void replaceLoaderWithAIResponse(String response, List<ChatExtra> extras, String s, boolean showTypingEffect) {
        // Remove the loader item
        if (!messages.get(messages.size() - 1).isUser()) {
            messages.remove(messages.size() - 1);
            chatAdapter.notifyItemRemoved(messages.size() - 1);
        }

        /*if (extras == null) {
            if (bottomSheet == null) {
                bottomSheet = prepareBottomSheet();
            }
            onSpeak(response);
        }*/

        ChatMessage aiMessage = new ChatMessage(response, false, extras, true, showTypingEffect);
        aiMessage.setGptId(s);
        messages.add(aiMessage);
        chatAdapter.notifyItemChanged(messages.size() - 2);
        toggleEmptyChatView();
        scrollToNewMessage();
    }

    private void onSpeak(String response) {
        //bottomSheet.speak(response);
    }

    private void replaceMessageAtPosition(int position, String response, List<ChatExtra> extras, String s, boolean showTypingEffect) {
        // Check if the position is valid
        if (position >= 0 && position < messages.size()) {
            // Remove the existing message at the specified position
            if (messages.get(messages.size() - 1).isLoader()) {
                messages.remove(messages.size() - 1);
                chatAdapter.notifyItemRemoved(messages.size() - 1);
            }

            messages.remove(position);
            chatAdapter.notifyItemRemoved(position);

            // Create a new AI message
            ChatMessage aiMessage = new ChatMessage(response, false, extras, true, showTypingEffect);
            aiMessage.setGptId(s);

            // Add the new message at the specified position
            messages.add(position, aiMessage);
            chatAdapter.notifyItemInserted(position);
            toggleEmptyChatView();

            // Optionally scroll to the position of the new message
            recyclerView.scrollToPosition(position);
        }
    }


    private void saveChat(String userPrompt, String aiResponse, String imgLink) {
        new BookGPT().saveChat(resId, aiResponse, userPrompt,
                Wonderslate.getInstance().getSharedPrefs().getUserId(), imgLink, mcqQuizId, systemPrompt, new WSCallback() {
                    @Override
                    public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                        Gson gson = new Gson();
                        GptLogResponse response = gson.fromJson(jsonObject.toString(), GptLogResponse.class);

                        freeTokenCount = response.getFreeTokenCount();
                        paidTokenCount = response.getPaidTokenCount();
                        updateTokenCount();
                    }

                    @Override
                    public void onWSResultFailed(String resString, int responseCode) {
                        Log.d(TAG, "onWSResultFailed: " + resString);
                    }
                });

    }

    private void showFeedbackDialog() {
        final Dialog dialog = new Dialog(this);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setContentView(R.layout.gpt_feedback_dialog);
        // Set the width of the dialog window to MATCH_PARENT
        Window window = dialog.getWindow();
        if (window != null) {
            window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        }
        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        Button btnIncorrectResponse = dialog.findViewById(R.id.btnIncorrectResponse);
        Button btnIrrelevantResponse = dialog.findViewById(R.id.btnIrrelevantResponse);
        Button btnUnclearResponse = dialog.findViewById(R.id.btnUnclearResponse);
        Button btnNotAddressIntent = dialog.findViewById(R.id.btnNotAddressIntent);
        Button btnInappropriateContent = dialog.findViewById(R.id.btnInappropriateContent);
        Button btnOther = dialog.findViewById(R.id.btnOther);
        EditText etFeedback = dialog.findViewById(R.id.etFeedback);
        Button btnSubmit = dialog.findViewById(R.id.submitButton);

        View.OnClickListener feedbackClickListener = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Button clickedButton = (Button) v;
                String feedback = clickedButton.getText().toString();
                etFeedback.setText(feedback);
            }
        };

        btnIncorrectResponse.setOnClickListener(feedbackClickListener);
        btnIrrelevantResponse.setOnClickListener(feedbackClickListener);
        btnUnclearResponse.setOnClickListener(feedbackClickListener);
        btnNotAddressIntent.setOnClickListener(feedbackClickListener);
        btnInappropriateContent.setOnClickListener(feedbackClickListener);
        btnOther.setOnClickListener(feedbackClickListener);

        btnSubmit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                hideKeyboard();
                String feedback = etFeedback.getText().toString().trim();
                if (!feedback.isEmpty()) {
                    Toast.makeText(GPTChatActivity.this, "Feedback Submitted: " + feedback, Toast.LENGTH_SHORT).show();
                    dialog.dismiss();
                } else {
                    Toast.makeText(GPTChatActivity.this, "Please provide feedback before submitting.", Toast.LENGTH_SHORT).show();
                }
            }
        });

        dialog.show();
    }

    private void showCreateQuestionsDialog(String resId, String quizId, String promptType) {
        hideKeyboard();
        final Dialog dialog = new Dialog(this);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setContentView(R.layout.mcq_qna_gpt_dialog);
        // Set the width of the dialog window to MATCH_PARENT
        Window window = dialog.getWindow();
        if (window != null) {
            window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        }
        dialog.setCanceledOnTouchOutside(false);
        Objects.requireNonNull(dialog.getWindow()).setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        EditText etNumberOfQuestions = dialog.findViewById(R.id.etNumberOfQuestions);
        EditText etMCQs = dialog.findViewById(R.id.etNumberOfMCQs);
        LinearLayout mcqLayout = dialog.findViewById(R.id.mcqLayout);
        LinearLayout qnaLayout = dialog.findViewById(R.id.questionLayout);

        if (promptType.equalsIgnoreCase("mcqs") || promptType.equalsIgnoreCase("mcq")) {
            mcqLayout.setVisibility(View.VISIBLE);
            qnaLayout.setVisibility(View.GONE);
        } else if (promptType.equalsIgnoreCase("qna")) {
            mcqLayout.setVisibility(View.GONE);
            qnaLayout.setVisibility(View.VISIBLE);
        }
        else {
            if (containsMCQS && containsQNA) {
                mcqLayout.setVisibility(View.VISIBLE);
                qnaLayout.setVisibility(View.VISIBLE);
            }
            else if (containsMCQS && !containsQNA) {
                mcqLayout.setVisibility(View.VISIBLE);
                qnaLayout.setVisibility(View.GONE);
            }
            else {
                mcqLayout.setVisibility(View.GONE);
                qnaLayout.setVisibility(View.VISIBLE);
            }
        }
        Button btnCreateQuestions = dialog.findViewById(R.id.btnCreateQuestions);

        btnCreateQuestions.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                hideKeyboard();
                showChatLoader();
                if (promptType.equalsIgnoreCase("mcqs") || promptType.equalsIgnoreCase("mcq")) {
                    String numberOfMCQs = etMCQs.getText().toString().trim();
                    if (!numberOfMCQs.isEmpty()) {
                        finalNumberOfMCQs = numberOfMCQs;
                        finalNumberOfQuestions = "";
                        new BookGPT().getMCQ(resId, quizId, numberOfMCQs, new WSCallback() {
                            @Override
                            public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                                parseAndCreateMCQJsonObject(jsonObject);
                                List<MCQ> mcqList = MCQJSONParser.parseMCQList(jsonObject.toString());
                                mcqList = getRandomMCQSubset(GPTChatActivity.this, mcqList, Integer.parseInt(finalNumberOfMCQs));
                                formattedMCQ = MCQFormatter.formatMCQList(mcqList);
                                List<ChatExtra> extras = new ArrayList<>();
                                extras.add(new ChatExtra("button", "Show MCQs", ""));
                                extras.add(new ChatExtra("button", "Play with AI", ""));
                                extras.add(new ChatExtra("button", "Practice", ""));
                                extras.add(new ChatExtra("button", "Test", ""));
                                replaceLoaderWithAIResponse("You are all set! Dive into your MCQs.", extras, "", false);
                            }

                            @Override
                            public void onWSResultFailed(String resString, int responseCode) {
                                replaceLoaderWithAIResponse("K.ai is not available. Please try later.", null, "", true);
                            }
                        });
                    } else {
                        Toast.makeText(GPTChatActivity.this, "Please enter the number of MCQs.", Toast.LENGTH_SHORT).show();
                    }
                    dialog.dismiss();
                } else if (promptType.equalsIgnoreCase("qna")) {
                    String numberOfQuestions = etNumberOfQuestions.getText().toString().trim();
                    if (!numberOfQuestions.isEmpty()) {
                        finalNumberOfQuestions = numberOfQuestions;
                        finalNumberOfMCQs = "";
                        new BookGPT().getQNA(resId, numberOfQuestions, new WSCallback() {
                            @Override
                            public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                                List<QA> qaList = QAJSONParser.parseQAList(jsonObject.toString());
                                qaList = getRandomQASubset(GPTChatActivity.this, qaList, Integer.parseInt(finalNumberOfQuestions));
                                formattedQna = QAFormatter.formatQAList(qaList);
                                List<ChatExtra> extras = new ArrayList<>();
                                extras.add(new ChatExtra("button", "Show Questions & Answers", ""));
                                replaceLoaderWithAIResponse("You are all set! Dive into your Questions & Answers.", extras, "", false);
                            }

                            @Override
                            public void onWSResultFailed(String resString, int responseCode) {
                                replaceLoaderWithAIResponse("K.ai is not available. Please try later.", null, "", false);
                            }
                        });
                    } else {
                        Toast.makeText(GPTChatActivity.this, "Please enter the number of questions.", Toast.LENGTH_SHORT).show();
                    }
                    dialog.dismiss();
                }
                else {
                    String numberOfMCQs = etMCQs.getText().toString().trim();
                    String numberOfQuestions = etNumberOfQuestions.getText().toString().trim();

                    if (containsMCQS && containsQNA) {
                        numberOfMCQs = etMCQs.getText().toString().trim();
                        numberOfQuestions = etNumberOfQuestions.getText().toString().trim();

                        if (numberOfMCQs.isEmpty() || numberOfQuestions.isEmpty()) {
                            replaceLoaderWithAIResponse("Please enter the number of MCQs and Questions.", null, "", true);
                            dialog.dismiss();
                            return;
                        }
                    }
                    else if (containsMCQS && !containsQNA) {
                        numberOfMCQs = etMCQs.getText().toString().trim();
                        numberOfQuestions = "";

                        if (numberOfMCQs.isEmpty()) {
                            replaceLoaderWithAIResponse("Please enter the number of MCQs.", null, "", true);
                            dialog.dismiss();
                            return;
                        }
                    }
                    else {
                        numberOfQuestions = etNumberOfQuestions.getText().toString().trim();
                        numberOfMCQs = "";

                        if (numberOfQuestions.isEmpty()) {
                            replaceLoaderWithAIResponse("Please enter the number of Questions.", null, "", true);
                            dialog.dismiss();
                            return;
                        }
                    }

                    finalNumberOfQuestions = numberOfQuestions;
                    finalNumberOfMCQs = numberOfMCQs;
                    new BookGPT().getTest(resId, numberOfQuestions, numberOfMCQs, new WSCallback() {
                        @Override
                        public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                            qnaRawString = jsonObject.toString();
                            parseAndCreateMCQJsonObject(jsonObject);
                            List<MCQ> mcqList = MCQJSONParser.parseMCQList(jsonObject.toString());
                            mcqList = getRandomMCQSubset(GPTChatActivity.this, mcqList, Integer.parseInt(finalNumberOfMCQs));
                            formattedMCQ = MCQFormatter.formatMCQs(mcqList, showAnswers);
                            List<QA> qnaList = QAJSONParser.parseQnA(jsonObject.toString());
                            qnaList = getRandomQASubset(GPTChatActivity.this, qnaList, Integer.parseInt(finalNumberOfQuestions));
                            formattedQna = QAFormatter.formatQnA(qnaList, showAnswers);
                            List<ChatExtra> extras = new ArrayList<>();
                            extras.add(new ChatExtra("button", "Show Answers", ""));
                            String formattedTest = formattedMCQ + formattedQna;
                            replaceLoaderWithAIResponse(formattedTest, extras, "", false);
                        }

                        @Override
                        public void onWSResultFailed(String resString, int responseCode) {
                            replaceLoaderWithAIResponse("K.ai is not available. Please try later.", null, "", true);
                        }
                    });
                    dialog.dismiss();
                }
            }
        });

        dialog.show();
    }

    public static List<MCQ> getRandomMCQSubset(Context context, List<MCQ> mcqList, int number) {
        // Check if the list is null or empty
        if (mcqList == null || mcqList.isEmpty()) {
            Toast.makeText(context, "No questions found", Toast.LENGTH_SHORT).show();
            return new ArrayList<>(); // Return an empty list
        }

        // Check if the number is within valid range
        if (number <= 0 || number > mcqList.size()) {
            Toast.makeText(context, "Selected number of questions is more than available", Toast.LENGTH_SHORT).show();
            return new ArrayList<>(); // Return an empty list
        }

        // Create a new list from the original to avoid modifying it
        List<MCQ> shuffledList = new ArrayList<>(mcqList);

        // Shuffle the list
        //Collections.shuffle(shuffledList, new Random());

        // Return the first 'number' elements
        return shuffledList.subList(0, number);
    }

    public static List<QA> getRandomQASubset(Context context, List<QA> mcqList, int number) {
        // Check if the list is null or empty
        if (mcqList == null || mcqList.isEmpty()) {
            Toast.makeText(context, "No questions found", Toast.LENGTH_SHORT).show();
            return new ArrayList<>(); // Return an empty list
        }

        // Check if the number is within valid range
        if (number <= 0 || number > mcqList.size()) {
            Toast.makeText(context, "Selected number of questions is more than available", Toast.LENGTH_SHORT).show();
            return new ArrayList<>(); // Return an empty list
        }

        // Create a new list from the original to avoid modifying it
        List<QA> shuffledList = new ArrayList<>(mcqList);

        // Shuffle the list
        //Collections.shuffle(shuffledList, new Random());

        // Return the first 'number' elements
        return shuffledList.subList(0, number);
    }

    private void parseAndCreateMCQJsonObject(JSONObject jsonObject) {
        try {
            JSONArray mcqArray = new JSONArray(jsonObject.optString("results"));
            mcqObject = new JSONObject();
            mcqObject.put("results", mcqArray);
            mcqObject.put("status", jsonObject.optString("status"));
            mcqObject.put("isPassage", jsonObject.optString("isPassage"));
            mcqObject.put("passage", jsonObject.optString("passage"));
            mcqObject.put("description", jsonObject.optString("description"));
            mcqObject.put("resourceName", jsonObject.optString("resourceName"));
            mcqObject.put("chaptersList", jsonObject.optString("chaptersList"));
            mcqObject.put("language1", jsonObject.optString("language1"));
            mcqObject.put("language2", jsonObject.optString("language2"));
            mcqObject.put("examSubject", jsonObject.optString("examSubject"));
            mcqObject.put("examMstId", jsonObject.optString("examMst"));
            mcqObject.put("examDtl", jsonObject.optString("examDtl"));
            mcqObject.put("testSeries", jsonObject.optString("testSeries"));
            mcqObject.put("quizRecorder", jsonObject.optString("quizRecorder"));
            mcqObject.put("testEndDate", jsonObject.optString("testEndDate"));
            mcqObject.put("chapterName", jsonObject.optString("chapterName"));
            mcqObject.put("challengerName", jsonObject.optString("challengerName"));
            mcqObject.put("challengerPlace", jsonObject.optString("challengerPlace"));
            mcqObject.put("resId", resId);
            mcqObject.put("bookgpt", true);
        } catch (JSONException e) {
            e.printStackTrace();
            mcqObject = new JSONObject();
        }
    }

    private void handleMCQ(String activityType, JSONObject jsonData, String bookId, int chapterId, String resId,
                           boolean shopView, String quizMode, int selectedChapter) {
        if (quizMode.equalsIgnoreCase("practice") || quizMode.equalsIgnoreCase("test")) {
            Intent intent = new Intent(GPTChatActivity.this, QuizActivity.class);
            intent.putExtra("quizId", resId);
            intent.putExtra("resId", resId);
            intent.putExtra("previous", "yes");
            intent.putExtra("next", "yes");
            intent.putExtra("isFromShop", "disabled");
            intent.putExtra("selectedFormat", "practice".equals(quizMode) ? "practice" : "testSeries");
            intent.putExtra("realDailyTestDtlId", "");
            intent.putExtra("isGPTQuiz", true);
            int finalTimeOut = Integer.parseInt("15");
            finalTimeOut = finalTimeOut * 1000;
            intent.putExtra("language", "");
            intent.putExtra("sound", false);
            intent.putExtra("timeValue", String.valueOf(finalTimeOut));
            intent.putExtra("darkMode", false);
            intent.putExtra("quizData", mcqObject.optString("results"));
            intent.putExtra("language1", jsonData.optString("language1"));
            intent.putExtra("language2", jsonData.optString("language2"));
            intent.putExtra("resourceName", jsonData.optString("resourceName"));
            startActivity(intent);
        }
        else {
            navigateToPlay(jsonData);
        }
    }

    private void navigateToPlay(JSONObject jsonData) {
        Intent intent = new Intent(GPTChatActivity.this, QuizActivity.class);

        int finalTimeOut = Integer.parseInt("15");
        finalTimeOut = finalTimeOut * 1000;
        intent.putExtra("language", "");
        intent.putExtra("sound", false);
        intent.putExtra("timeValue", String.valueOf(finalTimeOut));
        intent.putExtra("darkMode", false);

        if (jsonData != null) {
            try {
                jsonData.put("resourceName", jsonData.optString("resourceName"));
            } catch (JSONException e) {
                e.printStackTrace();
            }
            intent.putExtra("quizData", mcqObject.optString("results"));
            intent.putExtra("selectedDate", jsonData.optString("date"));
            intent.putExtra("challengerName", jsonData.optString("challengerName"));
            intent.putExtra("challengerPlace", jsonData.optString("challengerPlace"));
            intent.putExtra("realDailyTestDtlId", jsonData.optString("realDailyTestDtlId"));
            intent.putExtra("language1", jsonData.optString("language1"));
            intent.putExtra("language2", jsonData.optString("language2"));
            intent.putExtra("resourceName", jsonData.optString("resourceName"));
            intent.putExtra("testMode", jsonData.optString("testMode"));
            intent.putExtra("previous", jsonData.optString("previous"));
            intent.putExtra("next", jsonData.optString("next"));
            intent.putExtra("resId", jsonData.optString("resId"));
            intent.putExtra("learn", !jsonData.optString("learn").isEmpty() &&
                    !jsonData.optString("learn").equalsIgnoreCase("null"));
            intent.putExtra("start", jsonData.optString("start"));
            intent.putExtra("end", jsonData.optString("end"));
            intent.putExtra("isFromShop", true);
            intent.putExtra("isGPTQuiz", true);
            intent.putExtra("selectedFormat", "");

        }

        if (Wonderslate.getInstance().getSharedPrefs().getUsermobile() != null && !Wonderslate.getInstance().getSharedPrefs().getUsermobile().isEmpty()) {
            intent.putExtra("userId", Wonderslate.getInstance().getSiteID() + "_" + Wonderslate.getInstance().getSharedPrefs().getUsermobile());
        } else {
            intent.putExtra("userId", Wonderslate.getInstance().getSiteID() + "_" + Wonderslate.getInstance().getSharedPrefs().getUseremail());
        }
        intent.putExtra("siteId", Wonderslate.getInstance().getSiteID());
        intent.putExtra("baseUrl", Wonderslate.SERVICE);
        intent.putExtra("xauth", Wonderslate.getInstance().getSharedPrefs().getAccessToken());

        startActivity(intent);
    }

    private void showCreateFlashcardsDialog(String resId) {
        hideKeyboard();
        final Dialog dialog = new Dialog(this);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setContentView(R.layout.gpt_flashcard_dialog);
        // Set the width of the dialog window to MATCH_PARENT
        Window window = dialog.getWindow();
        if (window != null) {
            window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        }
        dialog.setCanceledOnTouchOutside(false);
        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        EditText etNumberOfFlashcards = dialog.findViewById(R.id.etNumberOfFlashcards);
        Button btnCreateFlashcards = dialog.findViewById(R.id.btnCreateFlashcards);

        btnCreateFlashcards.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                hideKeyboard();
                showChatLoader();
                String numberOfFlashcards = etNumberOfFlashcards.getText().toString().trim();
                if (!numberOfFlashcards.isEmpty()) {
                    new BookGPT().getFlashCards(resId, "GPT Flash Card", numberOfFlashcards, new WSCallback() {
                        @Override
                        public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                            noOfFlashcards = numberOfFlashcards;
                            dialog.dismiss();
                            flashCardResId = jsonObject.optString("resId");
                            List<FlashCard> keyValueList = FlashcardJSONParser.parseKeyValueList(jsonObject.toString());
                            formattedFlashcards = FlashcardFormatter.formatKeyValueList(keyValueList);
                            List<ChatExtra> extras = new ArrayList<>();
                            extras.add(new ChatExtra("button", "Show", ""));
                            extras.add(new ChatExtra("button", "Open", ""));
                            replaceLoaderWithAIResponse("You are all set! Dive into your Flashcards.", extras, "", false);
                        }

                        @Override
                        public void onWSResultFailed(String resString, int responseCode) {
                            replaceLoaderWithAIResponse("K.ai is not available. Please try later.", null, "", true);
                            dialog.dismiss();
                        }
                    });
                } else {
                    Toast.makeText(GPTChatActivity.this, "Please enter the number of flashcards.", Toast.LENGTH_SHORT).show();
                }
            }
        });

        dialog.show();
    }

    public void openFlashCard(String resId, String noOfQuestions) {
        String flashcardUrl = "resources/displayFlashCards" + "?resId=" + resId + "&name=GPT Flashcards" + "&noOfQuestions=" + noOfQuestions;
        String resLink = Wonderslate.SERVICE4 + flashcardUrl + "&tokenId=" + WonderPubSharedPrefs.getInstance(GPTChatActivity.this).getAccessToken() + "&appType=android";

        Resource resource = new Resource();
        resource.setResLink(resLink);
        resource.setResName("GPT Flashcards");
        Intent flashCardIntent = new Intent(GPTChatActivity.this, WeblinkAct.class);
        flashCardIntent.putExtra("argResData", resource);
        startActivity(flashCardIntent);
    }

    private void showUnlockFullAccessDialog(boolean buyBook) {
        final Dialog dialog = new Dialog(this);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setContentView(R.layout.dialog_unlock_full_gpt_access);
        dialog.setCanceledOnTouchOutside(false);
        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        TextView rechargeMessage = dialog.findViewById(R.id.tvRechargeMessage);
        Button rechargeButton = dialog.findViewById(R.id.rechargeButton);
        if (buyBook) {
            rechargeMessage.setText("Your question deserves a powerful answer! Get the full experience by purchasing the book now!");
            rechargeButton.setText("Buy Now");
        }
        else {
            rechargeMessage.setText("Oops! Looks like you’re out of tokens. Recharge now to keep the conversation going.");
            rechargeButton.setText("Recharge Now");
        }

        rechargeButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (buyBook) {
                    finish();
                }
                else {
                    Intent rechargeIntent = new Intent(GPTChatActivity.this, GPTRechargeActivity.class);
                    startActivity(rechargeIntent);
                    finish();
                }
                dialog.dismiss();
            }
        });

        dialog.show();
    }

    private void getChatHistory() {
        if (!isResOption)
            mcqQuizId = "";
        new BookGPT().getChat(resId, Wonderslate.getInstance().getSharedPrefs().getUsermobile(), mcqQuizId, new WSCallback() {
            @Override
            public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                Gson gson = new Gson();
                GptLogsResponse response = gson.fromJson(jsonObject.toString(), GptLogsResponse.class);

                // Access the parsed data
                List<GptLog> gptLogs = response.getGptLogs();
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    gptLogs = gptLogs.stream()
                            .collect(Collectors.collectingAndThen(Collectors.toList(), list -> {
                                Collections.reverse(list);
                                return list;
                            }));
                }
                else {
                    // Reverse the list directly using Collections.reverse()
                    Collections.reverse(gptLogs);
                }
                freeTokenCount = response.getFreeTokenCount();
                paidTokenCount = response.getPaidTokenCount();
                updateTokenCount();

                for (GptLog log : gptLogs) {
                    if (isResOption) {
                        if (log.getQuizObjId().equalsIgnoreCase(mcqQuizId)) {
                            if (log.getSystemPrompt() != null && !log.getSystemPrompt().isEmpty()) {
                                List<ChatExtra> extras = new ArrayList<>();
                                if (log.getImgLink() != null && !log.getImgLink().isEmpty()) {
                                    String url = Wonderslate.SERVICE + "/funlearn/downloadEpubImage?source=" + log.getImgLink();
                                    extras.add(new ChatExtra("image", "image", url));
                                }
                                ChatMessage userMessage = new ChatMessage(log.getSystemPrompt(), true, extras, false, false);
                                messages.add(userMessage);
                            } else if (log.getUserPrompt() != null && !log.getUserPrompt().isEmpty()) {
                                List<ChatExtra> extras = new ArrayList<>();
                                if (log.getImgLink() != null && !log.getImgLink().isEmpty()) {
                                    String url = Wonderslate.SERVICE + "/funlearn/downloadEpubImage?source=" + log.getImgLink();
                                    extras.add(new ChatExtra("image", "image", url));
                                }
                                ChatMessage userMessage = new ChatMessage(log.getUserPrompt(), true, extras, false, false);
                                messages.add(userMessage);
                            }
                            if (log.getResponse() != null && !log.getResponse().isEmpty()) {
                                ChatMessage userMessage = new ChatMessage(log.getResponse(), false, null, false, false);
                                messages.add(userMessage);
                            }
                        }
                    }
                    else {
                        if (log.getUserPrompt() != null && !log.getUserPrompt().isEmpty()) {
                            List<ChatExtra> extras = new ArrayList<>();
                            if (log.getImgLink() != null && !log.getImgLink().isEmpty()) {
                                String url = Wonderslate.SERVICE + "/funlearn/downloadEpubImage?source=" + log.getImgLink();
                                extras.add(new ChatExtra("image", "image", url));
                            }
                            ChatMessage userMessage = new ChatMessage(log.getUserPrompt(), true, extras, false, false);
                            messages.add(userMessage);
                        }
                        if (log.getResponse() != null && !log.getResponse().isEmpty()) {
                            ChatMessage userMessage = new ChatMessage(log.getResponse(), false, null, false, false);
                            messages.add(userMessage);
                        }
                    }
                }
                chatAdapter.notifyItemInserted(messages.size() - 1);
                scrollToNewMessage();
                toggleEmptyChatView();

                if (isResOption && !resOptionType.equalsIgnoreCase("history")) {
                    onMCQGPTInteraction();
                    showHideLoader(false);
                }
                else if (isResOption && resOptionType.equalsIgnoreCase("history")) {
                    showHideLoader(false);
                }
            }

            @Override
            public void onWSResultFailed(String resString, int responseCode) {
                freeTokenCount = 0;
                paidTokenCount = 0;
                updateTokenCount();
                Log.d(TAG, "onWSResultFailed: " + resString);
                toggleEmptyChatView();
            }
        });
    }

    private void onMCQGPTInteraction() {
        List<ChatExtra> extras = new ArrayList<>();
        if (resOptionType.equalsIgnoreCase("hint")) {
            systemPrompt = "Give Hint";
        }
        else if (resOptionType.equalsIgnoreCase("explain")) {
            systemPrompt = "Explain MCQ";
        }
        else {
            systemPrompt = "Create Similar MCQs";
        }
        ChatMessage userMessage = new ChatMessage(systemPrompt, true, extras, true, false);
        messages.add(userMessage);
        chatAdapter.notifyItemInserted(messages.size() - 1);
        toggleEmptyChatView();
        scrollToNewMessage();
        messageInput.setText("");
        showChatLoader();

        new BookGPT().quizGPTInteractions(mcqQuizId, chatHistoryArray, true, resOptionQuery, resOptionType, "", new WSCallback() {
            @Override
            public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                imgData = "";
                DataHolder.INSTANCE.setLargeData("");
                deleteSnip(imgUrl);
                imgUrl = "";
                setupAttachmentFeature(attachmentContainer, removeAttachment);
                JSONObject chatHistoryObject = new JSONObject();
                try {
                    if (resOptionType.equalsIgnoreCase("hint")) {
                        chatHistoryObject.put("user", "Give Hint");
                    }
                    else if (resOptionType.equalsIgnoreCase("explain")) {
                        chatHistoryObject.put("user", "Explain MCQ");
                    }
                    else {
                        chatHistoryObject.put("user", "Create Similar MCQs");
                    }
                    chatHistoryObject.put("ai", jsonObject.optString("answer"));
                    chatHistoryArray.put(chatHistoryObject);
                    saveChat(resOptionQuery, jsonObject.optString("answer"), jsonObject.optString("imgLink"));
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                String response = jsonObject.optString("answer");
                replaceLoaderWithAIResponse(response, null, "", true);
                if (!systemPrompt.equalsIgnoreCase("Create Similar MCQs"))
                    storeMCQGPTInteraction(resId, response, resOptionType);
                showHideLoader(false);
            }

            @Override
            public void onWSResultFailed(String resString, int responseCode) {
                imgData = "";
                DataHolder.INSTANCE.setLargeData("");
                setupAttachmentFeature(attachmentContainer, removeAttachment);
                replaceLoaderWithAIResponse("K.ai is not available for this chapter. Please contact support.", null, "", true);
            }
        });
    }

    private void storeMCQGPTInteraction(String resId, String response, String resOptionType) {
        new BookGPT().storeQuizGPTContents(resId, response, resOptionType, new WSCallback() {
            @Override
            public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {}

            @Override
            public void onWSResultFailed(String resString, int responseCode) {}
        });
    }

    private void togglePromptLayout() {}


    private void updateTokenCount() {
        /*WSUserRegistration.getNotifications(new WSCallback() {
            @Override
            public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                String bookIdsString = jsonObject.optString("bookIds");
                if (!bookIdsString.isEmpty()) {
                    List<String> bookIdsList = Arrays.asList(bookIdsString.split(","));
                    if (!bookIdsList.contains(bookId)) {
                        tvBuyBook.setVisibility(View.VISIBLE);
                        tvDoubtsRecharge.setVisibility(View.GONE);
                        lltTokens.setVisibility(View.GONE);
                    }
                    else {
                        tvDoubtsRecharge.setVisibility(View.VISIBLE);
                        tvBuyBook.setVisibility(View.GONE);
                        lltTokens.setVisibility(View.VISIBLE);
                        flDoubtsRecharge.setVisibility(View.VISIBLE);
                        if (WonderPublishApplication.getInstance().getSharedPrefs().isGPTAdmin()) {
                            freeTokenCount = freeTokenCount + 10; //Extra tokens for GPT Admin
                        }
                        tvFreeTokenCount.setText(String.format("Free: %s", String.valueOf(freeTokenCount)));
                        tvPaidTokenCount.setText(String.format("Paid: %s", String.valueOf(paidTokenCount)));
                    }
                }
            }

            @Override
            public void onWSResultFailed(String resString, int responseCode) {
                tvBuyBook.setVisibility(View.VISIBLE);
                tvDoubtsRecharge.setVisibility(View.GONE);
                lltTokens.setVisibility(View.GONE);
            }
        });*/
    }

    public void createAndOpenPDF(ChatMessage aiMessage, ChatMessage userMessage, String bookTitle, String chapterTitle, Context context) {
        showHideLoader(true);
        try {
            // Prepare file name
            String fileName = "iBookGPT_" + bookTitle + "_" + chapterTitle + ".pdf";

            // Create a ContentValues object to hold metadata about the file
            ContentValues contentValues = new ContentValues();
            contentValues.put(MediaStore.MediaColumns.DISPLAY_NAME, fileName);
            contentValues.put(MediaStore.MediaColumns.MIME_TYPE, "application/pdf");
            contentValues.put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_DOCUMENTS);

            // Get the URI where the file will be saved
            Uri pdfUri = context.getContentResolver().insert(MediaStore.Files.getContentUri("external"), contentValues);

            if (pdfUri == null) {
                throw new IOException("Failed to create PDF file");
            }

            // Create an output stream to write the PDF content
            OutputStream outputStream = context.getContentResolver().openOutputStream(pdfUri);

            if (outputStream == null) {
                throw new IOException("Failed to open output stream");
            }

            // Initialize a PdfWriter with the output stream
            PdfWriter writer = new PdfWriter(outputStream);

            // Initialize a PdfDocument with the writer
            PdfDocument pdfDoc = new PdfDocument(writer);

            // Initialize a Document (the layout object for the PDF)
            Document document = new Document(pdfDoc, PageSize.A4);
            document.setMargins(40, 40, 40, 40); // Set margins similar to the reference PDF

            // Add clickable URL
            /*Text link = new Text("Visit us at: " + context.getResources().getString(R.string.appStoreLink))
                    .setFontColor(ColorConstants.BLUE)
                    .setFontSize(15)
                    .setUnderline()
                    .setAction(com.itextpdf.kernel.pdf.action.PdfAction.createURI("http://bit.ly/App_Invite"));
            document.add(new Paragraph(link).setTextAlignment(TextAlignment.RIGHT));*/

            // Add the iBookGPT logo
            Drawable drawable = ResourcesCompat.getDrawable(context.getResources(), R.drawable.ibookgpt_logo_light, null);
            if (drawable != null) {
                Bitmap bitmap;

                if (drawable instanceof BitmapDrawable) {
                    bitmap = ((BitmapDrawable) drawable).getBitmap();
                } else {
                    bitmap = Bitmap.createBitmap(drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight(), Bitmap.Config.ARGB_8888);
                    Canvas canvas = new Canvas(bitmap);
                    drawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
                    drawable.draw(canvas);
                }

                ByteArrayOutputStream stream = new ByteArrayOutputStream();
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, stream);
                ImageData imageData = ImageDataFactory.create(stream.toByteArray());
                Image logo = new Image(imageData).scaleToFit(200, 100).setFixedPosition(40, 760);
                document.add(logo);
            } else {
                Log.e("PDF Creation", "Logo drawable not found");
            }

            // Add the book and chapter title
            document.add(new Paragraph("Book: " + bookTitle)
                    .setBold()
                    .setFontSize(25)
                    .setMarginTop(80)); // Ensure space from the logo
            document.add(new Paragraph("Chapter: " + chapterTitle)
                    .setBold()
                    .setFontSize(25)
                    .setMarginBottom(20));

            // Separator line
            document.add(new Paragraph("---------------------------------------------------------------------------------------"));
            document.add(new Paragraph("\n"));

            // Add user message
            document.add(new Paragraph("User:").setBold().setFontSize(25));
            document.add(new Paragraph(userMessage.getText()).setFontSize(25));

            // Add AI response
            document.add(new Paragraph("\niBookGPT:").setBold().setFontSize(25));
            document.add(new Paragraph(aiMessage.getText()).setFontSize(25));
            document.add(new AreaBreak());

            Paragraph paragraph = new Paragraph("iBookGPT")
                    .setFont(PdfFontFactory.createFont(StandardFonts.HELVETICA_BOLD))
                    .setFontSize(100).setOpacity(0.5f);

            if (pdfDoc.getNumberOfPages() > 1) {
                for (int i = 1; i <= pdfDoc.getNumberOfPages(); i++) {
                    PdfPage pdfPage = pdfDoc.getPage(i);
                    Rectangle pageSize = pdfPage.getPageSizeWithRotation();

                    // When "true": in case the page has a rotation, then new content will be automatically rotated in the
                    // opposite direction. On the rotated page this would look as if new content ignores page rotation.
                    pdfPage.setIgnorePageRotationForContent(true);

                    float x = (pageSize.getLeft() + pageSize.getRight()) / 2;
                    float y = (pageSize.getTop() + pageSize.getBottom()) / 2;
                    document.showTextAligned(paragraph, x, y, i, TextAlignment.CENTER, VerticalAlignment.TOP, 0);
                    document.showTextAligned(new Paragraph(i + " of " + pdfDoc.getNumberOfPages()), 30, 40, TextAlignment.LEFT);
                    String dateTime = new SimpleDateFormat("dd/MM/yy, hh:mm a", Locale.getDefault()).format(new Date());
                    document.showTextAligned(new Paragraph(dateTime), pdfDoc.getDefaultPageSize().getWidth() - 80, 40, TextAlignment.RIGHT);
                }

            }
            else {
                PdfPage page = pdfDoc.getPage(1); // Get the page safely
                if (page != null) {
                    PdfCanvas canvasNew = new PdfCanvas(page);

                    // Add watermark
                    canvasNew.saveState();
                    canvasNew.setFillColor(ColorConstants.LIGHT_GRAY);
                    PdfExtGState gs2 = new PdfExtGState();
                    gs2.setFillOpacity(0.3f);
                    canvasNew.setExtGState(gs2);
                    PdfFont font = PdfFontFactory.createFont(StandardFonts.HELVETICA_BOLD);
                    canvasNew.setFontAndSize(font, 100);

                    // Center the watermark
                    float xCenter = pdfDoc.getDefaultPageSize().getWidth() / 2;
                    float yCenter = pdfDoc.getDefaultPageSize().getHeight() / 2;

                    // Rotate and place the watermark
                    AffineTransform transform = AffineTransform.getRotateInstance(Math.toRadians(45), xCenter, yCenter);
                    canvasNew.concatMatrix(transform);
                    canvasNew.beginText();
                    canvasNew.moveText(xCenter - 150, yCenter); // Adjust for better centering
                    canvasNew.showText("iBookGPT");
                    canvasNew.endText();
                    canvasNew.restoreState();

                    // Add footer with date and page number
                    canvasNew.beginText();
                    canvasNew.setFontAndSize(font, 17);
                    canvasNew.moveText(30, 40); // Left-aligned at the bottom
                    canvasNew.showText(1 + " of " + pdfDoc.getNumberOfPages());

                    // Add the date to the right side of the footer
                    String dateTime = new SimpleDateFormat("dd/MM/yy, hh:mm a", Locale.getDefault()).format(new Date());
                    canvasNew.moveText(pdfDoc.getDefaultPageSize().getWidth() - 80, 40);
                    canvasNew.showText(dateTime);
                    canvasNew.endText();
                }
            }

            document.close();

            // Open the PDF
            Intent pdfIntent = new Intent(Intent.ACTION_VIEW);
            pdfIntent.setDataAndType(pdfUri, "application/pdf");
            pdfIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);

            showHideLoader(false);

            // Check if any PDF reader is installed or offer to share
            Intent chooser = Intent.createChooser(pdfIntent, "Open or Share");
            if (pdfIntent.resolveActivity(context.getPackageManager()) != null) {
                context.startActivity(chooser);
            } else {
                Toast.makeText(context, "Please install a PDF Reader to use this feature", Toast.LENGTH_LONG).show();
            }

        } catch (IOException e) {
            showHideLoader(false);
            e.printStackTrace();
            Toast.makeText(context, "Failed to write the PDF file: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }
}