package com.wonderslate.prepjoy.ui.resource_input

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.wonderslate.prepjoy.databinding.ActivityResourceInputBinding
import com.wonderslate.prepjoy.ui.dashboard.DashBoardActivity

class ResourceInputActivity : AppCompatActivity() {

    private lateinit var binding: ActivityResourceInputBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityResourceInputBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupViews()
        setupClickListeners()
    }

    private fun setupViews() {
        // Simple setup without toolbar conflicts
        // The title is already set in the layout
    }

    private fun setupClickListeners() {
        binding.btnLearn.setOnClickListener {
            handleButtonClick("Learn")
        }

        binding.btnPractice.setOnClickListener {
            handleButtonClick("Practice")
        }

        binding.btnTest.setOnClickListener {
            handleButtonClick("Test")
        }

        binding.btnPlay.setOnClickListener {
            handleButtonClick("Play")
        }
    }

    private fun handleButtonClick(action: String) {
        val resourceId = binding.etResourceId.text.toString().trim()
        
        if (resourceId.isEmpty()) {
            Toast.makeText(this, "Please enter a Resource ID", Toast.LENGTH_SHORT).show()
            return
        }

        // For now, show a toast with the action and resource ID
        // You can implement the actual logic here later
        Toast.makeText(this, "$action action with Resource ID: $resourceId", Toast.LENGTH_SHORT).show()
        
        // Navigate to dashboard for now (you can change this later)
        val intent = Intent(this, DashBoardActivity::class.java)
        intent.putExtra("resourceId", resourceId)
        intent.putExtra("action", action)
        startActivity(intent)
    }

    override fun onBackPressed() {
        // Prevent going back to login screen
        // Instead, minimize the app
        moveTaskToBack(true)
    }
}
