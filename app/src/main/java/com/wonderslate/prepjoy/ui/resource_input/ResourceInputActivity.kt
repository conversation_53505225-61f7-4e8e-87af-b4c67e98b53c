package com.wonderslate.prepjoy.ui.resource_input

import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.data.network.WSAPIManager
import com.wonderslate.data.network.VolleyHelper
import com.wonderslate.prepjoy.BuildConfig
import com.wonderslate.prepjoy.databinding.ActivityResourceInputBinding
import com.wonderslate.prepjoy.ui.login.LoginActivity
import com.wonderslate.prepjoy.ui.quiz_instruction.QuizInstructions
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject

class ResourceInputActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "ResourceInputActivity"
    }

    private lateinit var binding: ActivityResourceInputBinding
    private var currentResourceId: String = ""
    private var currentQuizMode: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityResourceInputBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        setupClickListeners()
    }

    private fun setupViews() {
        // Simple setup without toolbar conflicts
        // The title is already set in the layout
    }

    private fun setupClickListeners() {
        binding.btnLearn.setOnClickListener {
            handleButtonClick("Learn")
        }

        binding.btnPractice.setOnClickListener {
            handleButtonClick("Practice")
        }

        binding.btnTest.setOnClickListener {
            handleButtonClick("Test")
        }

        binding.btnPlay.setOnClickListener {
            handleButtonClick("Play")
        }

        binding.btnLogout.setOnClickListener {
            logoutDialog()
        }
    }

    private fun handleButtonClick(action: String) {
        val resourceId = binding.etResourceId.text.toString().trim()

        Log.d(TAG, "=== BUTTON CLICKED ===")
        Log.d(TAG, "Action: $action")
        Log.d(TAG, "Resource ID: $resourceId")

        if (resourceId.isEmpty()) {
            Log.w(TAG, "Resource ID is empty")
            Toast.makeText(this, "Please enter a Resource ID", Toast.LENGTH_SHORT).show()
            return
        }

        // Store current values for navigation
        currentResourceId = resourceId
        currentQuizMode = getQuizModeForAction(action)

        Log.d(TAG, "Quiz Mode: $currentQuizMode")
        Log.d(TAG, "Calling api/newQuizQA API with resourceId: $resourceId")

        // Show loading and call the correct API (api/newQuizQA)
        showLoading(true)
        callNewQuizQAAPI(resourceId)
    }



    private fun getQuizModeForAction(action: String): String {
        val quizMode = when (action.lowercase()) {
            "learn" -> "learn"  // Learn mode sends "learn" as quiz type
            "practice" -> "practice"
            "test" -> "testSeries"
            "play" -> ""  // Play mode uses empty string (default)
            else -> ""
        }
        Log.d(TAG, "getQuizModeForAction: $action -> $quizMode")
        return quizMode
    }

    private fun callNewQuizQAAPI(resourceId: String) {
        val params = HashMap<String, String>()
        params["siteId"] = BuildConfig.SITE_ID
        params["resId"] = resourceId

        Log.d(TAG, "=== API CALL ===")
        Log.d(TAG, "API Parameters:")
        Log.d(TAG, "  siteId: ${BuildConfig.SITE_ID}")
        Log.d(TAG, "  resId: $resourceId")

        // Use the correct API endpoint: api/newQuizQA (SERVICE_CREATE_QUIZ_V2)
        val url = WSAPIManager.getInstance().getServiceURL(WSAPIManager.SERVICE_CREATE_QUIZ_V2, params)
        Log.d(TAG, "API URL: $url")

        VolleyHelper.getInstance(this).sendRequestToApi(
            com.android.volley.Request.Method.GET,
            url,
            null,
            object : VolleyHelper.VolleyCallback {
                override fun onSuccessResponse(result: String?, responseCode: Int, error: Boolean) {
                    Log.d(TAG, "=== API RESPONSE ===")
                    Log.d(TAG, "Response Code: $responseCode")
                    Log.d(TAG, "Error: $error")
                    Log.d(TAG, "Result: $result")

                    runOnUiThread {
                        showLoading(false)
                        if (responseCode == 200 && !error && result != null) {
                            try {
                                val jsonResponse = JSONObject(result)
                                Log.d(TAG, "Parsed JSON Response: $jsonResponse")
                                navigateToQuizInstructions(jsonResponse)
                            } catch (e: Exception) {
                                Log.e(TAG, "Error parsing JSON response", e)
                                e.printStackTrace()
                                Toast.makeText(this@ResourceInputActivity, "Error processing quiz data", Toast.LENGTH_SHORT).show()
                            }
                        } else {
                            Log.w(TAG, "API call failed - responseCode: $responseCode, error: $error")
                            Toast.makeText(this@ResourceInputActivity, "Failed to load quiz data. Please try again.", Toast.LENGTH_SHORT).show()
                        }
                    }
                }

                override fun onErrorResponse(error: String?, responseCode: Int) {
                    Log.e(TAG, "=== API ERROR ===")
                    Log.e(TAG, "Error Response Code: $responseCode")
                    Log.e(TAG, "Error Message: $error")

                    runOnUiThread {
                        showLoading(false)
                        Toast.makeText(this@ResourceInputActivity, "Failed to load quiz data. Please try again.", Toast.LENGTH_SHORT).show()
                    }
                }
            }
        )
    }

    // Following ChaptersListAct pattern after getting API response
    private fun navigateToQuizInstructions(jsonResponse: JSONObject) {
        try {
            Log.d(TAG, "=== NAVIGATION CHECK ===")
            Log.d(TAG, "JSON Response: $jsonResponse")
            Log.d(TAG, "Has 'results': ${jsonResponse.has("results")}")

            // Check if we have valid quiz data - the API returns "results" field with quiz data
            val resultsString = jsonResponse.optString("results", "")
            Log.d(TAG, "Results string: '$resultsString'")

            if (resultsString.isNotEmpty() && !resultsString.equals("[]") && !resultsString.equals("null")) {
                Log.d(TAG, "Valid quiz data found, navigating to QuizInstructions")

                // Verify QuizInstructions class is accessible
                try {
                    val clazz = QuizInstructions::class.java
                    Log.d(TAG, "QuizInstructions class found: ${clazz.name}")
                } catch (e: Exception) {
                    Log.e(TAG, "QuizInstructions class not found", e)
                    Toast.makeText(this, "QuizInstructions class not found", Toast.LENGTH_LONG).show()
                    return
                }

                // QuizInstructions expects the complete JSON object, not just the results string
                // We need to format it like the HomeViewModel does (line 536: multiQuizData = data.data.toString())
                val completeQuizData = jsonResponse.toString()

                // QuizInstructions will handle database caching when it processes the quiz data

                // Follow the exact same pattern as ChaptersListAct but with quiz data
                Log.d(TAG, "Creating intent for QuizInstructions...")
                val intent = Intent(this, QuizInstructions::class.java)
                Log.d(TAG, "Intent created successfully")

                Log.d(TAG, "Adding intent extras...")
                intent.putExtra("quizId", currentResourceId)
                intent.putExtra("language1", jsonResponse.optString("language1", "English"))
                intent.putExtra("language2", jsonResponse.optString("language2", ""))
                intent.putExtra("selectedFormat", currentQuizMode)
                intent.putExtra("isFromShop", true)
                intent.putExtra("quizData", completeQuizData) // Pass the complete JSON object like QuizInstructions expects
                intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
                Log.d(TAG, "All intent extras added successfully")

                Log.d(TAG, "Intent extras:")
                Log.d(TAG, "  quizId: $currentResourceId")
                Log.d(TAG, "  language1: ${jsonResponse.optString("language1", "English")}")
                Log.d(TAG, "  language2: ${jsonResponse.optString("language2", "")}")
                Log.d(TAG, "  selectedFormat: $currentQuizMode")
                Log.d(TAG, "  isFromShop: true")
                Log.d(TAG, "  quizData length: ${completeQuizData.length}")

                Log.d(TAG, "About to start QuizInstructions activity...")
                try {
                    // Test if we can resolve the activity
                    val resolveInfo = packageManager.resolveActivity(intent, 0)
                    if (resolveInfo != null) {
                        Log.d(TAG, "QuizInstructions activity can be resolved: ${resolveInfo.activityInfo.name}")
                    } else {
                        Log.e(TAG, "QuizInstructions activity cannot be resolved!")
                        Toast.makeText(this, "QuizInstructions activity not found in manifest", Toast.LENGTH_LONG).show()
                        return
                    }

                    startActivity(intent)
                    Log.d(TAG, "QuizInstructions activity started successfully")
                } catch (e: Exception) {
                    Log.e(TAG, "Error starting QuizInstructions activity", e)
                    e.printStackTrace()
                    Toast.makeText(this, "Error opening quiz: ${e.message}", Toast.LENGTH_LONG).show()
                }
            } else {
                Log.w(TAG, "No valid quiz data - results field is empty or null")
                Toast.makeText(this, "No quiz data available for this Resource ID", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in navigateToQuizInstructions", e)
            e.printStackTrace()
            Toast.makeText(this, "Error processing quiz data", Toast.LENGTH_SHORT).show()
        }
    }

    private fun showLoading(show: Boolean) {
        if (show) {
            binding.btnLearn.isEnabled = false
            binding.btnPractice.isEnabled = false
            binding.btnTest.isEnabled = false
            binding.btnPlay.isEnabled = false
            binding.btnLogout.isEnabled = false
            binding.etResourceId.isEnabled = false
        } else {
            binding.btnLearn.isEnabled = true
            binding.btnPractice.isEnabled = true
            binding.btnTest.isEnabled = true
            binding.btnPlay.isEnabled = true
            binding.btnLogout.isEnabled = true
            binding.etResourceId.isEnabled = true
        }
    }



    override fun onPause() {
        super.onPause()
        Log.d(TAG, "ResourceInputActivity onPause() called")
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "ResourceInputActivity onResume() called")
    }

    override fun onBackPressed() {
        // Prevent going back to login screen
        // Instead, minimize the app
        moveTaskToBack(true)
    }

    // Logout functionality - same as DashBoardActivity
    private fun logoutDialog() {
        val builder = AlertDialog.Builder(this)
        builder.setTitle("Logout")
        builder.setMessage("Are you sure you want to logout?")
        builder.setPositiveButton("Yes") { _: DialogInterface, _: Int ->
            performLogout()
        }
        builder.setNegativeButton("No") { dialog: DialogInterface, _: Int ->
            dialog.dismiss()
        }
        builder.show()
    }

    private fun performLogout() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // Clear user data from SharedPreferences using available methods
                val prefs = WonderPubSharedPrefs.getInstance(this@ResourceInputActivity)

                // Set user as logged out
                prefs.setisUserLoggedIn(false)

                // Clear access token
                prefs.clearAccessToken()

                // Clear all shared preferences (same as DashBoardActivity logout)
                prefs.clearAllSharePref()

                Log.d(TAG, "User data cleared successfully")

                withContext(Dispatchers.Main) {
                    // Navigate to login screen
                    val intent = Intent(this@ResourceInputActivity, LoginActivity::class.java)
                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                    startActivity(intent)
                    finish()
                    Log.d(TAG, "Navigated to LoginActivity")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error during logout", e)
                withContext(Dispatchers.Main) {
                    Toast.makeText(this@ResourceInputActivity, "Logout failed. Please try again.", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
}
