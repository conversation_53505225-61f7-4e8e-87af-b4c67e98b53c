package com.wonderslate.prepjoy.ui.resource_input

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.wonderslate.prepjoy.databinding.ActivityResourceInputBinding
import com.wonderslate.prepjoy.ui.quiz_instruction.QuizInstructions

class ResourceInputActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "ResourceInputActivity"
    }

    private lateinit var binding: ActivityResourceInputBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityResourceInputBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        setupClickListeners()
    }

    private fun setupViews() {
        // Simple setup without toolbar conflicts
        // The title is already set in the layout
    }

    private fun setupClickListeners() {
        binding.btnLearn.setOnClickListener {
            handleButtonClick("Learn")
        }

        binding.btnPractice.setOnClickListener {
            handleButtonClick("Practice")
        }

        binding.btnTest.setOnClickListener {
            handleButtonClick("Test")
        }

        binding.btnPlay.setOnClickListener {
            handleButtonClick("Play")
        }
    }

    private fun handleButtonClick(action: String) {
        val resourceId = binding.etResourceId.text.toString().trim()

        Log.d(TAG, "=== BUTTON CLICKED ===")
        Log.d(TAG, "Action: $action")
        Log.d(TAG, "Resource ID: $resourceId")

        if (resourceId.isEmpty()) {
            Log.w(TAG, "Resource ID is empty")
            Toast.makeText(this, "Please enter a Resource ID", Toast.LENGTH_SHORT).show()
            return
        }

        Log.d(TAG, "Navigating directly to QuizInstructions like ChaptersListAct")

        // Navigate directly to QuizInstructions following ChaptersListAct pattern
        navigateToQuizInstructions(resourceId, action)
    }



    // Following the exact same pattern as ChaptersListAct.onQuizAction
    private fun navigateToQuizInstructions(resourceId: String, action: String) {
        Log.d(TAG, "=== NAVIGATION TO QUIZ INSTRUCTIONS ===")
        Log.d(TAG, "Resource ID: $resourceId")
        Log.d(TAG, "Action: $action")

        val intent = Intent(this, QuizInstructions::class.java)
        intent.putExtra("quizId", resourceId)
        intent.putExtra("isFromShop", true)
        intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)

        when (action.lowercase()) {
            "practice" -> {
                intent.putExtra("selectedFormat", "practice")
                Log.d(TAG, "Starting Practice mode")
                startActivity(intent)
            }
            "learn", "play" -> {
                intent.putExtra("selectedFormat", "")
                Log.d(TAG, "Starting Play/Learn mode")
                startActivity(intent)
            }
            "test" -> {
                intent.putExtra("selectedFormat", "testSeries")
                Log.d(TAG, "Starting Test mode")
                startActivity(intent)
            }
            else -> {
                Toast.makeText(this, "Problem while opening quiz. Please try again", Toast.LENGTH_SHORT).show()
                Log.e(TAG, "Unknown action: $action")
            }
        }
    }



    override fun onBackPressed() {
        // Prevent going back to login screen
        // Instead, minimize the app
        moveTaskToBack(true)
    }
}
