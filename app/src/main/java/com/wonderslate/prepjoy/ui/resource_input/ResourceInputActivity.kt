package com.wonderslate.prepjoy.ui.resource_input

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.wonderslate.data.network.WSAPIManager
import com.wonderslate.data.network.VolleyHelper
import com.wonderslate.prepjoy.BuildConfig
import com.wonderslate.prepjoy.databinding.ActivityResourceInputBinding
import com.wonderslate.prepjoy.ui.quiz_instruction.QuizInstructions
import org.json.JSONObject

class ResourceInputActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "ResourceInputActivity"
    }

    private lateinit var binding: ActivityResourceInputBinding
    private var currentResourceId: String = ""
    private var currentQuizMode: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityResourceInputBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        setupClickListeners()
    }

    private fun setupViews() {
        // Simple setup without toolbar conflicts
        // The title is already set in the layout
    }

    private fun setupClickListeners() {
        binding.btnLearn.setOnClickListener {
            handleButtonClick("Learn")
        }

        binding.btnPractice.setOnClickListener {
            handleButtonClick("Practice")
        }

        binding.btnTest.setOnClickListener {
            handleButtonClick("Test")
        }

        binding.btnPlay.setOnClickListener {
            handleButtonClick("Play")
        }
    }

    private fun handleButtonClick(action: String) {
        val resourceId = binding.etResourceId.text.toString().trim()

        Log.d(TAG, "=== BUTTON CLICKED ===")
        Log.d(TAG, "Action: $action")
        Log.d(TAG, "Resource ID: $resourceId")

        if (resourceId.isEmpty()) {
            Log.w(TAG, "Resource ID is empty")
            Toast.makeText(this, "Please enter a Resource ID", Toast.LENGTH_SHORT).show()
            return
        }

        // Store current values for navigation
        currentResourceId = resourceId
        currentQuizMode = getQuizModeForAction(action)

        Log.d(TAG, "Quiz Mode: $currentQuizMode")
        Log.d(TAG, "Calling API with resourceId: $resourceId")

        // Show loading and call API
        showLoading(true)
        callNewQuizQAAPI(resourceId)
    }

    private fun getQuizModeForAction(action: String): String {
        return when (action.lowercase()) {
            "learn" -> ""  // Learn mode uses empty string (default play mode)
            "practice" -> "practice"
            "test" -> "testSeries"
            "play" -> ""  // Play mode uses empty string (default)
            else -> ""
        }
    }

    private fun callNewQuizQAAPI(resourceId: String) {
        val params = HashMap<String, String>()
        params["siteId"] = BuildConfig.SITE_ID
        params["resId"] = resourceId

        Log.d(TAG, "=== API CALL ===")
        Log.d(TAG, "API Parameters:")
        Log.d(TAG, "  siteId: ${BuildConfig.SITE_ID}")
        Log.d(TAG, "  resId: $resourceId")

        val url = WSAPIManager.getInstance().getServiceURL(WSAPIManager.SERVICE_CREATE_QUIZ_V2, params)
        Log.d(TAG, "API URL: $url")

        VolleyHelper.getInstance(this).sendRequestToApi(
            com.android.volley.Request.Method.GET,
            url,
            null,
            object : VolleyHelper.VolleyCallback {
                override fun onSuccessResponse(result: String?, responseCode: Int, error: Boolean) {
                    Log.d(TAG, "=== API RESPONSE ===")
                    Log.d(TAG, "Response Code: $responseCode")
                    Log.d(TAG, "Error: $error")
                    Log.d(TAG, "Result: $result")

                    runOnUiThread {
                        showLoading(false)
                        if (responseCode == 200 && !error && result != null) {
                            try {
                                val jsonResponse = JSONObject(result)
                                Log.d(TAG, "Parsed JSON Response: $jsonResponse")
                                navigateToQuiz(jsonResponse)
                            } catch (e: Exception) {
                                Log.e(TAG, "Error parsing JSON response", e)
                                e.printStackTrace()
                                Toast.makeText(this@ResourceInputActivity, "Error processing quiz data", Toast.LENGTH_SHORT).show()
                            }
                        } else {
                            Log.w(TAG, "API call failed - responseCode: $responseCode, error: $error")
                            Toast.makeText(this@ResourceInputActivity, "Failed to load quiz data. Please try again.", Toast.LENGTH_SHORT).show()
                        }
                    }
                }

                override fun onErrorResponse(error: String?, responseCode: Int) {
                    Log.e(TAG, "=== API ERROR ===")
                    Log.e(TAG, "Error Response Code: $responseCode")
                    Log.e(TAG, "Error Message: $error")

                    runOnUiThread {
                        showLoading(false)
                        Toast.makeText(this@ResourceInputActivity, "Failed to load quiz data. Please try again.", Toast.LENGTH_SHORT).show()
                    }
                }
            }
        )
    }

    private fun navigateToQuiz(jsonResponse: JSONObject) {
        try {
            Log.d(TAG, "=== NAVIGATION CHECK ===")
            Log.d(TAG, "JSON Response: $jsonResponse")
            Log.d(TAG, "Has 'results': ${jsonResponse.has("results")}")

            // Check if we have valid quiz data - the API returns "results" field with quiz data
            val resultsString = jsonResponse.optString("results", "")
            Log.d(TAG, "Results string: '$resultsString'")

            if (resultsString.isNotEmpty() && !resultsString.equals("[]") && !resultsString.equals("null")) {
                Log.d(TAG, "Valid quiz data found, navigating to QuizInstructions")

                val intent = Intent(this, QuizInstructions::class.java).apply {
                    putExtra("quizId", currentResourceId)
                    putExtra("language1", jsonResponse.optString("language1", "English"))
                    putExtra("language2", jsonResponse.optString("language2", ""))
                    putExtra("selectedFormat", currentQuizMode)
                    putExtra("isFromShop", true)
                    putExtra("isGPTQuiz", false)
                    putExtra("quizData", resultsString) // Pass the quiz data
                    addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
                }

                Log.d(TAG, "Intent extras:")
                Log.d(TAG, "  quizId: $currentResourceId")
                Log.d(TAG, "  language1: ${jsonResponse.optString("language1", "English")}")
                Log.d(TAG, "  language2: ${jsonResponse.optString("language2", "")}")
                Log.d(TAG, "  selectedFormat: $currentQuizMode")
                Log.d(TAG, "  isFromShop: true")
                Log.d(TAG, "  isGPTQuiz: false")
                Log.d(TAG, "  quizData length: ${resultsString.length}")

                startActivity(intent)
            } else {
                Log.w(TAG, "No valid quiz data - results field is empty or null")
                Toast.makeText(this, "No quiz data available for this Resource ID", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in navigateToQuiz", e)
            e.printStackTrace()
            Toast.makeText(this, "Error processing quiz data", Toast.LENGTH_SHORT).show()
        }
    }

    private fun showLoading(show: Boolean) {
        if (show) {
            binding.btnLearn.isEnabled = false
            binding.btnPractice.isEnabled = false
            binding.btnTest.isEnabled = false
            binding.btnPlay.isEnabled = false
            binding.etResourceId.isEnabled = false
        } else {
            binding.btnLearn.isEnabled = true
            binding.btnPractice.isEnabled = true
            binding.btnTest.isEnabled = true
            binding.btnPlay.isEnabled = true
            binding.etResourceId.isEnabled = true
        }
    }

    override fun onBackPressed() {
        // Prevent going back to login screen
        // Instead, minimize the app
        moveTaskToBack(true)
    }
}
