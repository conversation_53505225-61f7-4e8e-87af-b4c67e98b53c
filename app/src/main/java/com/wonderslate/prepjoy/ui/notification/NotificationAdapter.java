package com.wonderslate.prepjoy.ui.notification;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.wonderslate.prepjoy.R;
import com.wonderslate.prepjoy.Utils.GenerateDates;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class NotificationAdapter extends RecyclerView.Adapter<NotificationAdapter.NotificationHolder>{
    private final JSONArray array;
    private final NotificationActivity notificationActivity;

    public NotificationAdapter(JSONArray array, NotificationActivity notificationActivity) {
        this.array = array;
        this.notificationActivity = notificationActivity;
    }

    @NonNull
    @Override
    public NotificationHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_notification, parent, false);
        return new NotificationHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull NotificationHolder holder, int position) {
        try {
            JSONObject object = array.getJSONObject(position);

            holder.textTitle.setText(object.getString("title"));
            holder.textDescription.setText(object.getString("description"));
            if (!object.optString("sentTime").isEmpty()) {
                holder.textSentTime.setText(GenerateDates.INSTANCE.getHumanReadableTime(Long.parseLong(object.optString("sentTime"))));
            }
            else {
                holder.textSentTime.setText("");
            }
            holder.notificationItemHolder.setOnClickListener(v -> notificationActivity.openNotificationDetails(object));

        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public int getItemCount() {
        return array.length();
    }

    static class NotificationHolder extends RecyclerView.ViewHolder {
        private final TextView textTitle;
        private final TextView textDescription;
        private final TextView textSentTime;
        private final RelativeLayout notificationItemHolder;

        public NotificationHolder(@NonNull View itemView) {
            super(itemView);
            textTitle = itemView.findViewById(R.id.textTitle);
            textDescription = itemView.findViewById(R.id.textDescription);
            textSentTime = itemView.findViewById(R.id.notificationSentTime);
            notificationItemHolder = itemView.findViewById(R.id.notificationItemHolder);
        }
    }
}
