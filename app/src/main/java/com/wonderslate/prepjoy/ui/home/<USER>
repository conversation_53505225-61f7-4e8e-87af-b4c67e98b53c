package com.wonderslate.prepjoy.ui.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.databinding.BottomSheetSelectTestBinding

class HomeBottomSheet(val listener: QuizOptionsListener): BottomSheetDialogFragment() {

    private var _binding: BottomSheetSelectTestBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = BottomSheetSelectTestBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.buttonPlay.setOnClickListener {
            listener.onOptionSelected("play", "")
            dismiss()
        }
        binding.buttonPractice.setOnClickListener {
            listener.onOptionSelected("practice", "")
            dismiss()
        }
        binding.buttonTest.setOnClickListener {
            listener.onOptionSelected("test", "")
            dismiss()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}