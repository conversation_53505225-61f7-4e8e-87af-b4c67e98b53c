package com.wonderslate.prepjoy.ui.home

import android.app.Activity
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.facebook.shimmer.ShimmerFrameLayout
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.domain.entities.home.HomeOptions
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.ui.BaseActivity
import com.ws.commons.enums.HomeOption

/**
 * Recycler view adapter to display the cards on the home screen
 * @param options list of HomeOptions
 * @param listener implementation of Home Option Listener
 */
class HomeOptionsAdapter(private val options: List<HomeOptions>,
                         private val listener: HOListener):
    RecyclerView.Adapter<HomeOptionsAdapter.HomeOptionsItemViewHolder>() {


    inner class HomeOptionsItemViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val container: View = itemView.findViewById(R.id.item_home_options_container)
        val title: TextView = itemView.findViewById(R.id.item_home_options_title)
        val subTitle: TextView = itemView.findViewById(R.id.item_home_options_sub_title)
        val shimmerLayout: ShimmerFrameLayout = itemView.findViewById(R.id.item_home_options_new_shimmer)
        val shimmerText: TextView = itemView.findViewById(R.id.item_home_options_text_view)
        val image: ImageView = itemView.findViewById(R.id.item_home_options_image)
    }

    private fun onOptionClicked(option: HomeOptions, context: Context) {
        val dailyTest = WonderPubSharedPrefs.getInstance(context).dailyTestABTestValue.ifEmpty {
            context.getString(R.string.home_daily_tests)
        }
        val type: HomeOption = when (option.title) {
            context.getString(R.string.home_current_affairs) -> {
                HomeOption.CURRENT_AFFAIRS
            }
            dailyTest -> {
                HomeOption.DAILY_TEST
            }
            context.getString(R.string.home_ebooks) -> {
                HomeOption.EBOOKS
            }
            context.getString(R.string.home_cafe) -> {
                HomeOption.CAFE
            }
            context.getString(R.string.home_news) -> {
                HomeOption.NEWS
            }
            context.getString(R.string.home_jobs) -> {
                HomeOption.JOB_NOTIFICATION
            }
            else -> HomeOption.BOOK
        }
        listener.onHomeOptionClicked(option, type)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): HomeOptionsItemViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return HomeOptionsItemViewHolder(inflater.inflate(R.layout.item_home_options, parent, false))
    }

    override fun onBindViewHolder(holder: HomeOptionsItemViewHolder, position: Int) {
        val option = options[position]
        val context = holder.container.context
        holder.container.setOnClickListener {
            hideKeyboard(context as Activity)
            onOptionClicked(option, context)

        }
        val dailyTest = WonderPubSharedPrefs.getInstance(context).dailyTestABTestValue.ifEmpty {
            context.getString(R.string.home_daily_tests)
        }
        val resId = when(option.title) {
            "Physics" -> R.drawable.physics
            "Chemistry" -> R.drawable.chemistry
            "Mathematics" -> R.drawable.mathematics
            "Biology" -> R.drawable.biology
             dailyTest -> R.drawable.home_daily_test_image
            context.getString(R.string.home_ebooks) -> R.drawable.home_ebooks_image
            context.getString(R.string.home_cafe) -> R.drawable.home_discussion_image
            context.getString(R.string.home_current_affairs) -> R.drawable.home_current_affaies
            context.getString(R.string.home_news) -> R.drawable.ic_news_icon
            context.getString(R.string.home_jobs) -> R.drawable.ic_job_icon
            else -> -1
        }
        holder.image.visibility = View.GONE
        if (resId != -1) {
            holder.image.visibility = View.VISIBLE
            Glide.with(holder.image).load(resId).into(holder.image)
        }
        if (option.title.equals("Daily Games", true)) {
            holder.title.text = "Online Tests"
        }
        else {
            holder.title.text = option.title
        }
        option.subTitle?.let { holder.subTitle.text = it }
        holder.subTitle.visibility = View.GONE
        option.isNew?.let {
            if (it) {
                try {
                    holder.shimmerLayout.startShimmer()
                } catch (e: Exception) {
                    // Fallback if shimmer method doesn't exist
                    holder.shimmerText.visibility = View.VISIBLE
                }
            } else {
                holder.shimmerText.visibility = View.GONE
            }
        }
    }

    override fun getItemCount(): Int {
        return options.size
    }
    fun hideKeyboard(activity: Activity) {
        val imm = activity.getSystemService(BaseActivity.INPUT_METHOD_SERVICE) as InputMethodManager
        var view = activity.currentFocus
        if (view == null) {
            view = View(activity)
        }
        imm.hideSoftInputFromWindow(view.windowToken, 0)
    }
}