package com.wonderslate.prepjoy.ui.home

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import com.wonderslate.commons.Data
import com.wonderslate.commons.Result
import com.wonderslate.commons.Status
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.data.repository.DashBoardRepository
import com.wonderslate.domain.entities.*
import com.wonderslate.data.repository.HomeViewRepository
import com.wonderslate.domain.entities.UpdateUserProfileData
import com.wonderslate.domain.entities.UserRankData
import com.wonderslate.domain.entities.multiquiz.CFMultiQuizInput
import com.wonderslate.domain.entities.multiquiz.QuizResponse
import com.wonderslate.domain.usecase.analytics.*
import com.wonderslate.domain.usecase.currentaffairs.*
import com.wonderslate.domain.usecase.log.LogResourceActivityUseCase
import com.wonderslate.domain.usecase.otp.SendOtpUseCase
import com.wonderslate.domain.usecase.signup.UpdateUserProfileUseCase
import com.wonderslate.domain.usecase.user_details.ClearAllCachedDataUseCase
import com.wonderslate.domain.usecase.user_details.OnBoardingUseCase
import com.wonderslate.domain.usecase.user_details.UserSelectedCategoryUseCase
import com.wonderslate.prepjoy.ui.BaseViewModel
import com.ws.commons.exceptions.NoInternetException
import com.ws.commons.interfaces.EventLogger
import com.ws.commons.models.Event
import com.ws.commons.models.ShopBookData
import com.ws.database.room.entity.Preference
import com.ws.shop.data.models.ShopBooksRequest
import com.ws.shop.data.models.SiteBooks
import com.ws.shop.data.repositories.ShopBooksRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.LocalDateTime
import org.json.JSONObject

class HomeViewModel(
    private val currentAffairsStartEndDatesUseCase: CurrentAffairsStartEndDatesUseCase,
    private val QuizUseCase: CurrentAffairsQuizUseCase,
    private val multiQuizUseCase: CFWeeklyQuizUseCase,
    private val QuestionAnswersUseCase: QuizQuestionAnswersUseCase,
    private val currentAffairsUserDetailsUseCase: CurrentAffairsUserDetailsUseCase,
    private val userRankUseCase: CurrentAffairsUserRankUseCase,
    private val weeklyWinnersRankUseCase: CurrentAffairsUserRankUseCase,
    private val updateuserCase: UpdateUserProfileUseCase,
    private var checkForReadingMaterialCardUseCase: CheckForReadingMaterialCardUseCase,
    private var sendFirebaseTokenToServerUseCase: SendFirebaseTokenToServerUseCase,
    private val clearAllCachedDataUseCase: ClearAllCachedDataUseCase,
    private val shopBooksRepo : ShopBooksRepository,
    private val eventLogger: EventLogger,
    private val dashBoardRepo: DashBoardRepository,
    private val quizAttemptedUseCase: AnalyticsQuizzesAttemptedUseCase,
    private val lastQuizUseCase: AnalyticsUsersLastQuizUseCase,
    private val historyForAllQuiz: AnalyticsUsersHistoryForAllQuizzesUseCase,
    private val historyForAQuiz: AnalyticsUsersHistoryForAQuizUseCase,
    private val getAllQuizDetails: AnalyticsFullQuizDetailsUseCase,
    private val logResourceActivityUseCase: LogResourceActivityUseCase,
    private val homeViewRepository: HomeViewRepository

) : BaseViewModel() {
    private val mutableStartEndDates: MutableLiveData<Data<JSONObject>> = MutableLiveData()
    private val mutableQuiz: MutableLiveData<Data<JSONObject>> = MutableLiveData()
    private val mutableQuizQuestionAnswers: MutableLiveData<Data<JSONObject>> = MutableLiveData()
    private val mutableCurrentFragmentPosition: MutableLiveData<Int> = MutableLiveData()
    private val mutableCurrentAffairsUserDetails : MutableLiveData<Data<JSONObject>> = MutableLiveData()
    private var mutableUpdateUser: MutableLiveData<Data<JSONObject>> = MutableLiveData()
    private val mutableUserRank : MutableLiveData<Data<JSONObject>> = MutableLiveData()
    private val mutableWeeklyWinnersRank : MutableLiveData<Data<JSONObject>> = MutableLiveData()
    private val mutableCheckForCard: MutableLiveData<Data<JSONObject>> = MutableLiveData()
    private val mutableSendTokenToServer: MutableLiveData<Data<JSONObject>> = MutableLiveData()
    private val mutableMultiQuizResponse: MutableLiveData<Data<QuizResponse>> = MutableLiveData()
    private val mutableSiteBooks: MutableLiveData<Data<List<SiteBooks>>> = MutableLiveData()
    private val mutableAccessCode : MutableLiveData<Data<JSONObject>> = MutableLiveData()
    private val mutableChallengerCode : MutableLiveData<Data<JSONObject>> = MutableLiveData()
    private val mutableChallengerCode2: MutableLiveData<Data<JSONObject>> = MutableLiveData()

    private val mutableQuizAttempted : MutableLiveData<Data<JSONObject>> = MutableLiveData()
    private val mutableLastQuiz : MutableLiveData<Data<JSONObject>> = MutableLiveData()
    private val mutableHistoryForAllQuiz : MutableLiveData<Data<JSONObject>> = MutableLiveData()
    private val mutableHistoryForAQuiz : MutableLiveData<Data<JSONObject>> = MutableLiveData()
    private val mutableAllQuizDetails : MutableLiveData<Data<JSONObject>> = MutableLiveData()
    private var mutableResActivityLog: MutableLiveData<Data<JSONObject>> = MutableLiveData()

    val sentTokenToServer : LiveData<Data<JSONObject>>
        get() {
            return mutableSendTokenToServer
        }

    val accessCode: LiveData<Data<JSONObject>>
    get() { return mutableAccessCode}

    val challengerCode: LiveData<Data<JSONObject>>
    get() { return mutableChallengerCode }

    val challengerCode2: LiveData<Data<JSONObject>>
    get() { return mutableChallengerCode2}

    val checkForCard : LiveData<Data<JSONObject>>
        get() {
            return mutableCheckForCard
        }

    val userRank : LiveData<Data<JSONObject>>
        get() {
            return mutableUserRank
        }

    val weeklyWinnersRank : LiveData<Data<JSONObject>>
        get() {
            return mutableWeeklyWinnersRank
        }

    val currentAffairsUserDetails : LiveData<Data<JSONObject>>
        get() {
            return mutableCurrentAffairsUserDetails
        }

    val quizAttemptedDetails : LiveData<Data<JSONObject>>
        get() {
            return mutableQuizAttempted
        }

    val lastQuizDetails : LiveData<Data<JSONObject>>
        get() {
            return mutableLastQuiz
        }

    val historyForAllQuizDetails : LiveData<Data<JSONObject>>
        get() {
            return mutableHistoryForAllQuiz
        }

    val historyForAQuizDetails : LiveData<Data<JSONObject>>
        get() {
            return mutableHistoryForAQuiz
        }

    val getAllFullQuizDetails : LiveData<Data<JSONObject>>
        get() {
            return mutableAllQuizDetails
        }



    val currentFragmentPosition : LiveData<Int>
    get() {
        return mutableCurrentFragmentPosition
    }

    val startEndDatesResponse: LiveData<Data<JSONObject>>
        get() {
            return mutableStartEndDates
        }

    val userDetailsResponse : LiveData<Data<JSONObject>>
        get() {
            return mutableUpdateUser
        }

    val quizResponse: LiveData<Data<JSONObject>>
        get() {
            return mutableQuiz
        }

    val quizQuestionAnswers: LiveData<Data<JSONObject>>
        get() {
            return mutableQuizQuestionAnswers
        }

    val resLogResponse: LiveData<Data<JSONObject>>
        get() {
            return mutableResActivityLog
        }

    val multiQuizResponse: LiveData<Data<QuizResponse>>
    get() {
        return mutableMultiQuizResponse
    }
    val siteBooks: LiveData<Data<List<SiteBooks>>>
    get() { return mutableSiteBooks }

    fun getCurrentFragmentPosition(position: Int) {
        mutableCurrentFragmentPosition.value = position
    }

    private val _latestBooks = MutableStateFlow<com.ws.commons.Data<List<ShopBookData>>>(com.ws.commons.Data(responseType = com.ws.commons.Status.LOADING))
    val latestBooks = _latestBooks.asStateFlow()

    fun getShopBooks(booksRequest: ShopBooksRequest) = viewModelScope.launch(Dispatchers.IO) {
        _latestBooks.emit(
                com.ws.commons.Data(
                        responseType = com.ws.commons.Status.LOADING,
                        data = _latestBooks.value.data
                )
        )
        when(val result = shopBooksRepo.getLatestBooksList(booksRequest)) {
            is com.ws.commons.Result.Success -> {
                //Update books list
                val books = if(booksRequest.pageNo == 0) {
                    arrayListOf()
                } else {
                    _latestBooks.value.data?.toMutableList() ?: arrayListOf()
                }
                books.addAll(result.data.books)
                _latestBooks.emit(
                        com.ws.commons.Data(
                                responseType = com.ws.commons.Status.SUCCESSFUL,
                                data = books
                        )
                )
                val gson = Gson()
                val jsonList: String = gson.toJson(result.data.bookTags)
                insertPreferenceData(jsonList,"1")
            }

            is com.ws.commons.Result.Failure -> {
                _latestBooks.emit(
                        com.ws.commons.Data(
                                responseType = if (result.exception is NoInternetException) com.ws.commons.Status.HTTP_UNAVAILABLE else com.ws.commons.Status.ERROR,
                                data = _latestBooks.value.data,
                                error = result.exception
                        )
                )
            }

        }
    }

    private val _magazines = MutableStateFlow<com.ws.commons.Data<List<ShopBookData>>>(com.ws.commons.Data(responseType = com.ws.commons.Status.LOADING))
    val magazines = _magazines.asStateFlow()

    fun getMagazines(booksRequest: ShopBooksRequest) = viewModelScope.launch(Dispatchers.IO) {
        _magazines.emit(
            com.ws.commons.Data(
                responseType = com.ws.commons.Status.LOADING,
                data = _magazines.value.data
            )
        )
        when(val result = shopBooksRepo.getLatestBooksList(booksRequest)) {
            is com.ws.commons.Result.Success -> {
                //Update books list
                val books = if(booksRequest.pageNo == 0) {
                    arrayListOf()
                } else {
                    _magazines.value.data?.toMutableList() ?: arrayListOf()
                }
                books.addAll(result.data.books)
                _magazines.emit(
                    com.ws.commons.Data(
                        responseType = com.ws.commons.Status.SUCCESSFUL,
                        data = books
                    )
                )
                val gson = Gson()
                val jsonList: String = gson.toJson(result.data.bookTags)
                insertPreferenceData(jsonList,"1")
            }

            is com.ws.commons.Result.Failure -> {
                _magazines.emit(
                    com.ws.commons.Data(
                        responseType = if (result.exception is NoInternetException) com.ws.commons.Status.HTTP_UNAVAILABLE else com.ws.commons.Status.ERROR,
                        data = _magazines.value.data,
                        error = result.exception
                    )
                )
            }

        }
    }

    private val _compExamBooks = MutableStateFlow<com.ws.commons.Data<List<ShopBookData>>>(com.ws.commons.Data(responseType = com.ws.commons.Status.LOADING))
    val compExamBooks = _compExamBooks.asStateFlow()

    fun getCompExamBooks(booksRequest: ShopBooksRequest) = viewModelScope.launch(Dispatchers.IO) {
        _compExamBooks.emit(
            com.ws.commons.Data(
                responseType = com.ws.commons.Status.LOADING,
                data = _compExamBooks.value.data
            )
        )
        when(val result = shopBooksRepo.getLatestBooksList(booksRequest)) {
            is com.ws.commons.Result.Success -> {
                //Update books list
                val books = if(booksRequest.pageNo == 0) {
                    arrayListOf()
                } else {
                    _compExamBooks.value.data?.toMutableList() ?: arrayListOf()
                }
                books.addAll(result.data.books)
                _compExamBooks.emit(
                    com.ws.commons.Data(
                        responseType = com.ws.commons.Status.SUCCESSFUL,
                        data = books
                    )
                )
                val gson = Gson()
                val jsonList: String = gson.toJson(result.data.bookTags)
                insertPreferenceData(jsonList,"1")
            }

            is com.ws.commons.Result.Failure -> {
                _compExamBooks.emit(
                    com.ws.commons.Data(
                        responseType = if (result.exception is NoInternetException) com.ws.commons.Status.HTTP_UNAVAILABLE else com.ws.commons.Status.ERROR,
                        data = _compExamBooks.value.data,
                        error = result.exception
                    )
                )
            }

        }
    }

    fun fetchSiteLevelBooks() = viewModelScope.launch(Dispatchers.IO) {
        when(val result = shopBooksRepo.getSiteLevelBooks()) {
            is com.ws.commons.Result.Success -> {
                mutableSiteBooks.postValue(Data(responseType = Status.SUCCESSFUL,
                    data = result.data.books))
            }
            is com.ws.commons.Result.Failure -> {
                 val resType = if (result.exception is NoInternetException) {
                     Status.HTTP_UNAVAILABLE
                 } else {
                     Status.ERROR
                 }
                mutableSiteBooks.postValue(Data(responseType = resType, error = result.exception))
            }
        }
    }

    fun getStartEndDates() = launch {
        try {
            val datesResponse = withContext(Dispatchers.IO) {
                currentAffairsStartEndDatesUseCase.execute("")
            }
            when (datesResponse) {
                is Result.Success -> {
                    mutableStartEndDates.value =
                        Data(responseType = Status.SUCCESSFUL, data = datesResponse.data)
                }
                is Result.Failure -> {
                    mutableStartEndDates.value =
                        Data(responseType = Status.ERROR, error = datesResponse.exception)
                }
            }
        } catch (exception: Exception) {
            mutableStartEndDates.value = Data(responseType = Status.ERROR, error = exception)

        }
    }

    fun getQuiz(QuizData: String) = launch {
        try {
            val QuizResponse = withContext(Dispatchers.IO) {
                QuizUseCase.execute(QuizData)
            }
            when (QuizResponse) {
                is Result.Success -> {
                    mutableQuiz.value =
                        Data(responseType = Status.SUCCESSFUL, data = QuizResponse.data)
                }
                is Result.Failure -> {
                    mutableQuiz.value =
                        Data(responseType = Status.ERROR, error = QuizResponse.exception)
                }
            }
        } catch (exception: Exception) {
            mutableQuiz.value = Data(responseType = Status.ERROR, error = exception)

        }
    }

    fun fetchWeeklyQuiz(input: CFMultiQuizInput) = launch {
        try {
            val response = withContext(Dispatchers.IO) {
                multiQuizUseCase.execute(input)
            }
            when(response) {
                is Result.Success -> {
                    mutableMultiQuizResponse.value = Data(responseType = Status.SUCCESSFUL,
                        data = response.data)
                }
                is Result.Failure -> {
                    mutableMultiQuizResponse.value = Data(responseType = Status.ERROR,
                        error = response.exception)
                }
            }
        } catch (e: Exception) {
            mutableMultiQuizResponse.value = Data(responseType = Status.ERROR, error = e)
        }

    }

    fun updateUserData(signupData: UpdateUserProfileData) = launch {
        try {
            val UpdateUserResponse = withContext(Dispatchers.IO) {
                updateuserCase.execute(signupData)
            }

            when (UpdateUserResponse) {
                is Result.Success -> {
                    mutableUpdateUser.value =
                        Data(responseType = Status.SUCCESSFUL, data = UpdateUserResponse.data)
                }

                is Result.Failure -> {
                    mutableUpdateUser.value =
                        Data(responseType = Status.ERROR, error = UpdateUserResponse.exception)
                }
            }
        } catch (exception: Exception) {
            mutableUpdateUser.value = Data(responseType = Status.ERROR, error = exception)

        }
    }


    fun getQuizQuestionsAnswers(resId: String) = launch {
        try {
            val response = withContext(Dispatchers.IO) {
                QuestionAnswersUseCase.execute(resId)
            }
            when (response) {
                is Result.Success -> {
                    mutableQuizQuestionAnswers.value =
                            Data(responseType = Status.SUCCESSFUL, data = response.data)
                }
                is Result.Failure -> {
                    mutableQuizQuestionAnswers.value =
                            Data(responseType = Status.ERROR, error = response.exception)
                }
            }
        } catch (exception: Exception) {
            mutableQuizQuestionAnswers.value = Data(responseType = Status.ERROR, error = exception)

        }
    }

    fun getCurrentAffairsUserDetails(siteId: String) = launch {
        try {
            val currentAffairsUserDetailsResponse = withContext(Dispatchers.IO) {
                currentAffairsUserDetailsUseCase.execute(siteId)
            }
            when (currentAffairsUserDetailsResponse) {
                is Result.Success -> {
                    mutableCurrentAffairsUserDetails.value =
                        Data(responseType = Status.SUCCESSFUL, data = currentAffairsUserDetailsResponse.data)
                }
                is Result.Failure -> {
                    mutableCurrentAffairsUserDetails.value =
                        Data(responseType = Status.ERROR, error = currentAffairsUserDetailsResponse.exception)
                }
            }
        } catch (exception: Exception) {
            mutableCurrentAffairsUserDetails.value = Data(responseType = Status.ERROR, error = exception)

        }
    }

    fun addUserLoginLog(wonderPubSharedPrefs: WonderPubSharedPrefs) = launch {
        try {
            val response = withContext(Dispatchers.IO) {
                homeViewRepository.addUserLoginLog()
            }
            when(response) {
                is Result.Success -> {
                    Log.e("Log user success", response.data.toString())
                    wonderPubSharedPrefs.lastLogDate = LocalDateTime.now().toString()
                }
                is Result.Failure -> {
                    response.exception.message?.let { Log.e("Error in log user", it) }
                }
            }
        } catch (e: Exception) {
            e.message?.let { FirebaseCrashlytics.getInstance().log(it) }
        }
    }

    fun resetUserRank() {
        mutableUserRank.value = Data(responseType = Status.SUCCESSFUL, data = null)
    }
    fun resetWeeklyWinnersRank() {
        mutableWeeklyWinnersRank.value = Data(responseType = Status.SUCCESSFUL, data = null)
    }
    fun getUserRank(userRankData: UserRankData) = launch {
        try {
            mutableUserRank.value = Data(responseType = Status.LOADING)
            val userRankResponse = withContext(Dispatchers.IO) {
                userRankUseCase.execute(userRankData)
            }
            when (userRankResponse) {
                is Result.Success -> {
                    mutableUserRank.value =
                        Data(responseType = Status.SUCCESSFUL, data = userRankResponse.data)
                }
                is Result.Failure -> {
                    mutableUserRank.value =
                        Data(responseType = Status.ERROR, error = userRankResponse.exception)
                }
            }
        } catch (exception: Exception) {
            mutableUserRank.value = Data(responseType = Status.ERROR, error = exception)

        }
    }

    fun getWeeklyContestRank(userRankData: UserRankData) = launch {
        try {
            mutableWeeklyWinnersRank.value = Data(responseType = Status.LOADING)
            val weeklyWinnersRankResponse = withContext(Dispatchers.IO) {
                weeklyWinnersRankUseCase.execute(userRankData)
            }
            when (weeklyWinnersRankResponse) {
                is Result.Success -> {
                    mutableWeeklyWinnersRank.value =
                        Data(responseType = Status.SUCCESSFUL, data = weeklyWinnersRankResponse.data)
                }
                is Result.Failure -> {
                    mutableWeeklyWinnersRank.value =
                        Data(responseType = Status.ERROR, error = weeklyWinnersRankResponse.exception)
                }
            }
        } catch (exception: Exception) {
            mutableWeeklyWinnersRank.value = Data(responseType = Status.ERROR, error = exception)

        }
    }

    fun getCheckForCard(date: String) = launch {
        try {
            val checkForCard = withContext(Dispatchers.IO) {
                checkForReadingMaterialCardUseCase.execute(date)
            }
            when (checkForCard) {
                is Result.Success -> {
                    mutableCheckForCard.value =
                        Data(responseType = Status.SUCCESSFUL, data = checkForCard.data)
                }
                is Result.Failure -> {
                    mutableCheckForCard.value =
                        Data(responseType = Status.ERROR, error = checkForCard.exception)
                }
            }
        } catch (exception: Exception) {
            mutableCheckForCard.value = Data(responseType = Status.ERROR, error = exception)

        }
    }

    fun sendFirebaseTokenToServer(token: String) = launch {
        try {
            val sendTokenToServer = withContext(Dispatchers.IO) {
                sendFirebaseTokenToServerUseCase.execute(token)
            }
            when (sendTokenToServer) {
                is Result.Success -> {
                    mutableSendTokenToServer.value =
                            Data(responseType = Status.SUCCESSFUL, data = sendTokenToServer.data)
                }
                is Result.Failure -> {
                    mutableSendTokenToServer.value =
                            Data(responseType = Status.ERROR, error = sendTokenToServer.exception)
                }
            }
        } catch (exception: Exception) {
            mutableSendTokenToServer.value = Data(responseType = Status.ERROR, error = exception)

        }
    }

     fun accessCode(connectionCode: String,
                    quizId: String?,
                    quizType: String,
                    lastUser: String,
                    challengerName: String,
                    bookId: String? = null) = launch {
        try {
            val response = withContext(Dispatchers.IO) {
                homeViewRepository.fetchAccessCode(connectionCode,
                                                   quizId,
                                                   quizType,
                                                   lastUser,
                                                   challengerName,
                                                   bookId)
            }
            when(response){
                is Result.Success -> {
                    mutableAccessCode.value = Data(responseType = Status.SUCCESSFUL,
                        data = response.data)
                }
                is Result.Failure -> {
                    mutableAccessCode.value = Data(responseType = Status.ERROR,
                        error = response.exception)
                }
            }
        }catch (e: Exception) {
            mutableAccessCode.value = Data(responseType = Status.ERROR,
                error = e)
        }
    }

    fun challengerCode(challengerCode: String,
                       option: Int? = 1,
                       bookId: String? = null) = launch {
        try {
            if (option == 2) {
                mutableChallengerCode2.value = Data(responseType = Status.LOADING)
            } else {
                mutableChallengerCode.value = Data(responseType = Status.LOADING)
            }
            val response = withContext(Dispatchers.IO) {
                homeViewRepository.fetchConnectionCode(challengerCode)
            }
            when(response){
                is Result.Success -> {
                    if (option == 2) {
                        mutableChallengerCode2.value = Data(responseType = Status.SUCCESSFUL,
                            data = response.data)
                    } else {
                        val emitData = response.data
                        bookId?.let { emitData.put("bookId", it) }
                        mutableChallengerCode.value = Data(responseType = Status.SUCCESSFUL,
                            data = emitData)
                    }

                }
                is Result.Failure -> {
                    if (option == 2) {
                        mutableChallengerCode2.value = Data(responseType = Status.ERROR,
                            error = response.exception)
                    } else {
                        mutableChallengerCode.value = Data(responseType = Status.ERROR,
                            error = response.exception)
                    }
                }
            }
        } catch (e: Exception) {
            if (option == 2) {
                mutableChallengerCode2.value = Data(responseType = Status.ERROR,
                    error = e)
            } else {
                mutableChallengerCode.value = Data(responseType = Status.ERROR,
                    error = e)
            }
        }
    }

    fun declineCode(id: String, user: String) = launch {
        withContext(Dispatchers.IO) {
            homeViewRepository.declineCode(id, user)
        }
    }

    fun logEvent(event: Event) {
        eventLogger.logEvent(event)
    }


    suspend fun clearDB() {
        clearAllCachedDataUseCase.execute(Unit)
    }

    suspend fun getPreferenceData(resId: String): Preference? {
        return dashBoardRepo.getCachedPreferenceData(resId)
    }

    suspend fun insertPreferenceData(prefrenceObj: String,resId:String ) {
        return dashBoardRepo.insertIntoPreferenceEntity(prefrenceObj,resId)
    }

    fun getQuizAttempted() = launch {
        try {
            val quizAttemptedResponse = withContext(Dispatchers.IO) {
                quizAttemptedUseCase.execute()
            }
            when (quizAttemptedResponse) {
                is Result.Success -> {
                    mutableQuizAttempted.value =
                        Data(responseType = Status.SUCCESSFUL, data = quizAttemptedResponse.data)
                }
                is Result.Failure -> {
                    mutableQuizAttempted.value =
                        Data(responseType = Status.ERROR, error = quizAttemptedResponse.exception)
                }
            }
        } catch (exception: Exception) {
            mutableQuizAttempted.value = Data(responseType = Status.ERROR, error = exception)

        }
    }

    fun getLastQuiz() = launch {
        try {
            val lastquizResponse = withContext(Dispatchers.IO) {
                lastQuizUseCase.execute()
            }
            when (lastquizResponse) {
                is Result.Success -> {
                    mutableLastQuiz.value =
                        Data(responseType = Status.SUCCESSFUL, data = lastquizResponse.data)
                }
                is Result.Failure -> {
                    mutableLastQuiz.value =
                        Data(responseType = Status.ERROR, error = lastquizResponse.exception)
                }
            }
        } catch (exception: Exception) {
            mutableLastQuiz.value = Data(responseType = Status.ERROR, error = exception)

        }
    }

    fun getHistoryForAllQuiz(data : UserHistoryforAllQuiz) = launch {
        try {
            val historyForAllQuizResponse = withContext(Dispatchers.IO) {
                historyForAllQuiz.execute(data)
            }
            when (historyForAllQuizResponse) {
                is Result.Success -> {
                    mutableHistoryForAllQuiz.value =
                        Data(responseType = Status.SUCCESSFUL, data = historyForAllQuizResponse.data)
                }
                is Result.Failure -> {
                    mutableHistoryForAllQuiz.value =
                        Data(responseType = Status.ERROR, error = historyForAllQuizResponse.exception)
                }
            }
        } catch (exception: Exception) {
            mutableHistoryForAllQuiz.value = Data(responseType = Status.ERROR, error = exception)

        }
    }

    fun getHistoryForAQuiz(data : UserHistoryForQuizData) = launch {
        try {
            val historyForAQuizResponse = withContext(Dispatchers.IO) {
                historyForAQuiz.execute(data)
            }
            when (historyForAQuizResponse) {
                is Result.Success -> {
                    mutableHistoryForAQuiz.value =
                        Data(responseType = Status.SUCCESSFUL, data = historyForAQuizResponse.data)
                }
                is Result.Failure -> {
                    mutableHistoryForAQuiz.value =
                        Data(responseType = Status.ERROR, error = historyForAQuizResponse.exception)
                }
            }
        } catch (exception: Exception) {
            mutableHistoryForAQuiz.value = Data(responseType = Status.ERROR, error = exception)
        }
    }


    fun getFullQuizDetails(data : QuizFullDetails) = launch {
        try {
            val AllQuizResponseDetails = withContext(Dispatchers.IO) {
                getAllQuizDetails.execute(data)
            }
            when (AllQuizResponseDetails) {
                is Result.Success -> {
                    mutableAllQuizDetails.value =
                        Data(responseType = Status.SUCCESSFUL, data = AllQuizResponseDetails.data)
                }
                is Result.Failure -> {
                    mutableAllQuizDetails.value =
                        Data(responseType = Status.ERROR, error = AllQuizResponseDetails.exception)
                }
            }
        } catch (exception: Exception) {
            mutableAllQuizDetails.value = Data(responseType = Status.ERROR, error = exception)

        }
    }

    fun logResourceOpen(resLogData: ResourceLogData) = launch {
        try {
            val resLogResponse = withContext(Dispatchers.IO) {
                logResourceActivityUseCase.execute(resLogData)
            }

            when (resLogResponse) {
                is Result.Success -> {
                    mutableResActivityLog.value =
                        Data(responseType = Status.SUCCESSFUL, data = resLogResponse.data)
                }

                is Result.Failure -> {
                    mutableResActivityLog.value =
                        Data(responseType = Status.ERROR, error = resLogResponse.exception)
                }
            }
        } catch (exception: Exception) {
            mutableResActivityLog.value =
                Data(responseType = Status.ERROR, error = exception)
        }
    }
}