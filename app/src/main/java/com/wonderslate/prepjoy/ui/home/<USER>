package com.wonderslate.prepjoy.ui.home;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.firebase.analytics.FirebaseAnalytics;
import com.wonderslate.data.preferences.WonderPubSharedPrefs;
import com.wonderslate.domain.entities.multiquiz.CFMultiQuizInput;
import com.wonderslate.prepjoy.BuildConfig;
import com.wonderslate.prepjoy.R;
import com.wonderslate.prepjoy.Utils.FirebaseAnalyticsUtils;
import com.wonderslate.prepjoy.Utils.FlavourHelper;
import com.wonderslate.prepjoy.Utils.Flavours;
import com.wonderslate.prepjoy.Utils.Utils;
import com.wonderslate.prepjoy.ui.reading_material.ReadingMaterialActivity;
import com.wonderslate.prepjoy.ui.videos.VideoActivity;

import java.time.LocalDate;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class HomeSwipingAdapter extends RecyclerView.Adapter<HomeSwipingAdapter.HomeSwipingHolder> {
    private final List<String> date;
    private final boolean[] arr;
    private final String lastElement;
    private final CFListener listener;
    private DailyTestListener dailyTestListener;
    private String mode = "";
    private Context mContext;

    HomeSwipingAdapter(List<String> date, CFListener listener,
                       boolean[] arr, String last,Context pContext) {
        this.date = date;
        this.arr = arr;
        this.lastElement = last;
        this.listener = listener;
        this.mContext = pContext;
    }

    @NonNull
    @Override
    public HomeSwipingHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new HomeSwipingHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.layout_swipe_card, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull HomeSwipingHolder holder, @SuppressLint("RecyclerView") int position) {
        switch (mode) {
            case "Daily":
                inflateDailyView(holder, position);
                break;
            case "Weekly":
                inflateWeeklyView(holder, position);
                break;
            case "Monthly":
                inflateMonthlyQuiz(holder, position);
                break;
            case "Tests":
                inflateDailyTests(holder, position);
                break;
        }

      /* holder.lltparent.setOnClickListener (view -> {
           homeFragment.getQuiz(date.get(position));
       });*/
    }

    private void inflateDailyTests(HomeSwipingHolder holder, int position) {
        String testDate = date.get(position);
        holder.textDate.setText(testDate);
        if (null != dailyTestListener) {
            holder.option1.setText("Play");
            holder.option1.setOnClickListener(view -> dailyTestListener.
                    onTestOptionSelected(testDate, "play"));
            holder.option3.setText("Practice");
            holder.option3.setOnClickListener(view -> dailyTestListener.
                    onTestOptionSelected(testDate, "practice"));
            holder.option2.setText("Test");
            holder.option2.setOnClickListener(view -> dailyTestListener.
                    onTestOptionSelected(testDate, "learn"));
            holder.option4.setVisibility(View.GONE);
            holder.option6.setText(R.string.history_daily_test_title);
            holder.option6.setVisibility(View.VISIBLE);
            holder.option6.setOnClickListener(view -> dailyTestListener.
                    onTestOptionSelected(testDate, "history"));
        }
    }

    private void inflateWeeklyView(HomeSwipingHolder holder, int position) {
        String dates = date.get(position);
        WonderPubSharedPrefs prefs = WonderPubSharedPrefs.getInstance(mContext);
        String abTestValue = prefs.getQuizABTestValue();
        StringBuilder inputDate = new StringBuilder();
        String date1 = dates.split("/")[0];
        String date2 = dates.split("/")[1];
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MMM-yyyy");
        SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat dateFormat2 = new SimpleDateFormat("dd-MM-yyyy");
        String cardText = null;
        try {
            cardText = dateFormat.format(dateFormat1.parse(date1)) + "\n" +
                    dateFormat.format(dateFormat1.parse(date2));
             inputDate.append(dateFormat2.format(dateFormat1.parse(date1)));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        holder.textDate.setText(cardText);
        holder.option1.setText(String.format("%d MCQs "+ abTestValue, 25));
        holder.option3.setText(String.format("%d MCQs "+ abTestValue, 50));
        holder.option2.setText(String.format("%d MCQs "+ abTestValue, 75));
        holder.option5.setVisibility(View.VISIBLE);
        Resources resources = holder.option2.getResources();
        boolean isEnabled = resources.getBoolean(R.bool.show_weekly_75_questions);
        int visibility = View.GONE;
        if (isEnabled) {
            visibility = View.VISIBLE;
        }
        holder.option2.setVisibility(visibility);
        holder.option1.setOnClickListener(v -> {
            CFMultiQuizInput input = new CFMultiQuizInput(inputDate.toString(),
                    "25", "7");
            listener.onMultiQuizSelected(input);
            new FirebaseAnalyticsUtils(mContext).logQuestionsABTestingEvent(abTestValue);

        });
        holder.option3.setOnClickListener(v -> {
            CFMultiQuizInput input = new CFMultiQuizInput(inputDate.toString(),
                    "50", "7");
            listener.onMultiQuizSelected(input);
            new FirebaseAnalyticsUtils(mContext).logQuestionsABTestingEvent(abTestValue);

        });
        holder.option2.setOnClickListener(v -> {
            CFMultiQuizInput input = new CFMultiQuizInput(inputDate.toString(),
                    "75", "7");
            listener.onMultiQuizSelected(input);
            new FirebaseAnalyticsUtils(mContext).logQuestionsABTestingEvent(abTestValue);

        });
        holder.option5.setOnClickListener(v ->{
            Intent intent = new Intent(mContext, ReadingMaterialActivity.class);
            intent.putExtra("inputDate", inputDate.toString());
            intent.putExtra("readMode", "Weekly");
            intent.putExtra("viewFrom", "weekly-quiz");
            mContext.startActivity(intent);
        });
    }

    private void inflateMonthlyQuiz(HomeSwipingHolder holder, int position) {
        final String month = date.get(position);
        SimpleDateFormat format = new SimpleDateFormat("MMM-yyyy");
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy");
        Calendar calendar = Calendar.getInstance();
        WonderPubSharedPrefs prefs = WonderPubSharedPrefs.getInstance(mContext);
        String abTestValue = prefs.getQuizABTestValue();
        try {
            calendar.setTime(dateFormat.parse(month));
            holder.textDate.setText(format.format(calendar.getTime()));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        holder.option1.setText(String.format("%d MCQs "+ abTestValue, 25));
        holder.option3.setText(String.format("%d MCQs "+ abTestValue, 50));
        holder.option2.setText(String.format("%d MCQs "+ abTestValue, 75));
        holder.option4.setVisibility(View.VISIBLE);
        holder.option4.setText(String.format("%d MCQs "+ abTestValue, 100));
        holder.option1.setOnClickListener(v -> {
            CFMultiQuizInput input = new CFMultiQuizInput(month, "25", "30");
            listener.onMultiQuizSelected(input);
            new FirebaseAnalyticsUtils(mContext).logQuestionsABTestingEvent(abTestValue);

        });
        holder.option2.setOnClickListener(v -> {
            CFMultiQuizInput input = new CFMultiQuizInput(month, "75", "30");
            listener.onMultiQuizSelected(input);
            new FirebaseAnalyticsUtils(mContext).logQuestionsABTestingEvent(abTestValue);

        });
        holder.option3.setOnClickListener(v -> {
            CFMultiQuizInput input = new CFMultiQuizInput(month, "50", "30");
            listener.onMultiQuizSelected(input);
            new FirebaseAnalyticsUtils(mContext).logQuestionsABTestingEvent(abTestValue);

        });
        holder.option4.setOnClickListener(v -> {
            CFMultiQuizInput input = new CFMultiQuizInput(month, "100", "30");
            listener.onMultiQuizSelected(input);
            new FirebaseAnalyticsUtils(mContext).logQuestionsABTestingEvent(abTestValue);

        });
    }

    private void inflateDailyView(HomeSwipingHolder holder, int position) {
        holder.textDate.setText(date.get(position));
        WonderPubSharedPrefs prefs = WonderPubSharedPrefs.getInstance(mContext);
        String abTestValue = prefs.getQuizABTestValue();
       if (BuildConfig.FLAVOR.equalsIgnoreCase(Flavours.KARNATAKA.getFlavour()))
        {
            holder.option1.setText("10 MCQs "+ abTestValue);
        }
       else if(BuildConfig.FLAVOR.equalsIgnoreCase(Flavours.CURRENT_AFFAIRS.getFlavour()))
       {
           holder.option1.setText("15 MCQs "+WonderPubSharedPrefs.getInstance(mContext).getQuizABTestValue());
       }
        holder.option1.setOnClickListener(view -> {
                     listener.onDailyQuizSelected(date.get(position), "play");
            new FirebaseAnalyticsUtils(mContext).logQuestionsABTestingEvent(abTestValue);
        });

        holder.option2.setOnClickListener(view -> listener.onDailyQuizSelected(date.get(position), "watch"));

        holder.option3.setOnClickListener(view -> {
            listener.onDailyQuizSelected(date.get(position), "read");
            });
        if (FlavourHelper.INSTANCE.isCurrentAffairsAudioEnabled()) {
            holder.option6.setText(R.string.audio_page_title);
            holder.option6.setOnClickListener(view -> listener.
                    onDailyQuizSelected(date.get(position), "audio"));
        }
        else {
            holder.option6.setText(R.string.history_daily_test_title);
            holder.option6.setOnClickListener(view -> listener.
                    onDailyQuizSelected(date.get(position), "history"));
        }
        boolean showReadWatch = holder.option1.getResources().getBoolean(R.bool.show_read_watch);
        if (!showReadWatch) {
            holder.option2.setVisibility(View.GONE);
            holder.option3.setVisibility(View.GONE);
        } else {
            if ((arr[0] || arr[1] || arr[2] || arr[3])) {
                if (date.get(position).equalsIgnoreCase(lastElement)) {
                    if (arr[0]) {
                        holder.option1.setVisibility(View.VISIBLE);
                    } else {
                        holder.option1.setVisibility(View.GONE);
                    }

                    if (arr[1]) {
                        holder.option3.setVisibility(View.VISIBLE);
                    } else {
                        holder.option3.setVisibility(View.GONE);
                    }


                    if (arr[2]) {
                        holder.option2.setVisibility(View.VISIBLE);
                    } else {
                        holder.option2.setVisibility(View.GONE);
                    }

                    if (arr[3]) {
                        holder.option6.setVisibility(View.VISIBLE);
                    } else {
                        holder.option6.setVisibility(View.GONE);
                    }

                    if (!(arr[0] || arr[1] || arr[2])) {
                        holder.rlcard.setVisibility(View.GONE);
                    } else {
                        holder.rlcard.setVisibility(View.VISIBLE);
                    }
                } else {
                    holder.option1.setVisibility(View.VISIBLE);
                    holder.option3.setVisibility(View.VISIBLE);
                    holder.option2.setVisibility(View.VISIBLE);
                    holder.option6.setVisibility(View.VISIBLE);
                    holder.rlcard.setVisibility(View.VISIBLE);
                }
            }
        }
    }

    @Override
    public int getItemCount() {
        int size = date.size();
        if (mode.equalsIgnoreCase("Daily")) {
            if (!(arr[0] || arr[1] || arr[2])) {
                size = date.size() - 1;
            }
        }
       return size;
    }

    @Override
    public long getItemId(int position) {
        return super.getItemId(position);
    }

    @Override
    public int getItemViewType(int position) {
        return super.getItemViewType(position);
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public void setDailyTestListener(DailyTestListener dailyTestListener) {
        this.dailyTestListener = dailyTestListener;
    }

    static class HomeSwipingHolder extends RecyclerView.ViewHolder {
        private final Button option1;
        private final Button option2;
        private final Button option3;
        private final Button option4;
        private final Button option5;
        private final Button option6;
        private final TextView textDate;
        private RelativeLayout rlcard;

        public HomeSwipingHolder(@NonNull View itemView) {
            super(itemView);

            option1 = itemView.findViewById(R.id.btnOption1);
            option2 = itemView.findViewById(R.id.btnOption3);
            option3 = itemView.findViewById(R.id.btnOption2);
            option4 = itemView.findViewById(R.id.btnOption4);
            option5 = itemView.findViewById(R.id.btnOption5);
            option6 = itemView.findViewById(R.id.btnOption6);
            textDate = itemView.findViewById(R.id.txtDateVal);
            rlcard = itemView.findViewById(R.id.rlcard);
        }
    }
}
