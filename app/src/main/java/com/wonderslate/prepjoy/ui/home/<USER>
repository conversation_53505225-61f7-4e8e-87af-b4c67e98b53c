package com.wonderslate.prepjoy.ui.home

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Bundle
import android.text.Html
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.ViewPager
import androidx.viewpager2.widget.CompositePageTransformer
import androidx.viewpager2.widget.MarginPageTransformer
import androidx.viewpager2.widget.ViewPager2
import com.airbnb.lottie.LottieAnimationView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.facebook.shimmer.ShimmerFrameLayout
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.tabs.TabLayout
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.ktx.Firebase
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wang.avi.AVLoadingIndicatorView
import com.wonderslate.android.module.rssmanager.Channel
import com.wonderslate.android.module.rssmanager.RSS
import com.wonderslate.android.module.rssmanager.RssReader
import com.wonderslate.commons.Data
import com.wonderslate.commons.Status
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.domain.entities.QuizFullDetails
import com.wonderslate.domain.entities.home.HomeOptions
import com.wonderslate.domain.entities.multiquiz.CFMultiQuizInput
import com.wonderslate.domain.entities.multiquiz.QuizResponse
import com.wonderslate.domain.entities.tests.DTQuizInput
import com.wonderslate.domain.entities.tests.DailyTestResponse
import com.wonderslate.prepjoy.BuildConfig
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.Utils.CrashlyticsLogger
import com.wonderslate.prepjoy.Utils.CustomViews.BadgesDialog
import com.wonderslate.prepjoy.Utils.CustomViews.MedalsDialog
import com.wonderslate.prepjoy.Utils.FirebaseAnalyticsUtils
import com.wonderslate.prepjoy.Utils.FlavourHelper
import com.wonderslate.prepjoy.Utils.Flavours
import com.wonderslate.prepjoy.Utils.GenerateDates
import com.wonderslate.prepjoy.Utils.Utils
import com.wonderslate.prepjoy.Views.Adapters.DailyTestsAdapter
import com.wonderslate.prepjoy.news.NewsLanguage
import com.wonderslate.prepjoy.news.NewsListActivity
import com.wonderslate.prepjoy.news.NewsPreferenceActivity
import com.wonderslate.prepjoy.news.NewsSource
import com.wonderslate.prepjoy.news.TemporaryDataHolder
import com.wonderslate.prepjoy.news.UserPrefModel
import com.wonderslate.prepjoy.ui.audio.AudioPlayerActivity
import com.wonderslate.prepjoy.ui.chapters_list.ChaptersListAct
import com.wonderslate.prepjoy.ui.chapters_list.HistoryForQuiz
import com.wonderslate.prepjoy.ui.dashboard.DashBoardActivity
import com.wonderslate.prepjoy.ui.login.LoginActivity
import com.wonderslate.prepjoy.ui.profile.ProfileActivity
import com.wonderslate.prepjoy.ui.quiz.QuizActivity
import com.wonderslate.prepjoy.ui.quiz_instruction.QuizInstructions
import com.wonderslate.prepjoy.ui.reading_material.ReadingMaterialActivity
import com.wonderslate.prepjoy.ui.tests.DailyTestViewModel
import com.wonderslate.prepjoy.ui.videos.VideoActivity
import com.ws.chapter_list.data.models.ChaptersListConfig
import com.ws.commons.enums.HomeOption
import com.ws.commons.extensions.joinList
import com.ws.commons.models.ShopBookData
import com.ws.core_ui.extensions.collectLatestWithLifecycle
import com.ws.core_ui.extensions.hideKeyboard
import com.ws.core_ui.extensions.hideView
import com.ws.core_ui.extensions.isTablet
import com.ws.core_ui.extensions.showToast
import com.ws.core_ui.extensions.showView
import com.ws.core_ui.extensions.startActivityWithAnim
import com.ws.core_ui.extensions.visibility
import com.ws.shop.data.models.ShopBooksRequest
import com.ws.shop.data.models.SiteBooks
import com.ws.shop.ui.LatestBooksAdapter
import io.github.douglasjunior.androidSimpleTooltip.SimpleTooltip
import com.wonderslate.prepjoy.databinding.FragmentHomeBinding
import com.wonderslate.prepjoy.databinding.FragmentDashboardBinding
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.LocalDateTime
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import java.lang.reflect.Type
import java.text.DateFormat
import java.text.SimpleDateFormat
import java.util.Collections
import java.util.Date
import java.util.Locale
import kotlin.math.abs


class HomeFragment : Fragment(), DailyTestListener, CFListener, QuizOptionsListener, HOListener,
    RssReader.RssCallback {
    var dates: MutableList<String> = ArrayList()
    var tabLayout: TabLayout? = null
    private var viewPager: ViewPager? = null
    private var rootview: View? = null
    private var myContext: FragmentActivity? = null
    private var _binding: FragmentHomeBinding? = null
    private val binding get() = _binding!!
    private val viewModel: HomeViewModel by sharedViewModel()
    private val dailyTestViewModel: DailyTestViewModel by sharedViewModel()
    private lateinit var rlProfile: RelativeLayout
    private lateinit var llProfile: LinearLayout
    private lateinit var lltPoints : LinearLayout
    private lateinit var lltMedals : LinearLayout
    private lateinit var viewPager2: ViewPager2
    private lateinit var dailyTestsList: RecyclerView
    private lateinit var progressLoader: AVLoadingIndicatorView
    private  lateinit var aviLoader :AVLoadingIndicatorView
    private lateinit var textView: TextView
    private lateinit var comingSoonAnimView: LottieAnimationView
    private lateinit var txtShowMore: TextView
    private lateinit var imageView: ImageView
    private lateinit var textBadge: TextView
    private lateinit var textLifeTimePoints: TextView
    private lateinit var textLifeTimeMedals: TextView
    private lateinit var mainshimmer :ShimmerFrameLayout
    private lateinit var  lltLastQuiz :LinearLayout
    private lateinit var imageProfile: ImageView
    private lateinit var textName: TextView
    private lateinit var textUserName: TextView
    private lateinit var gameCode:EditText
    private lateinit var codeContainer:RelativeLayout

    private lateinit var txtQuiztitle: TextView
    private lateinit var txtScore: TextView
    private lateinit var txtQuizStatus: TextView
    private lateinit var infojoinGame:Button
    private lateinit var btnJoinGame: Button
    private lateinit var txtNoLastQuiz:TextView
    private lateinit var lltLastQuizData :LinearLayout
    private lateinit var tvLearn: TextView
    private lateinit var tvCommunity: TextView


    private lateinit var dashBoardView: LinearLayout
    private var calledFrom = "-1111"
    private var isDayTabEnable: Boolean = true
    private var isCardShowForCurrentData: Boolean = true
    private var multiQuizInput: CFMultiQuizInput? = null
    private lateinit var jsonObject: JSONObject
    var arrayForCurr = booleanArrayOf(false, false, false, false)
    var selectedDate = ""
    var selectedTestId = ""
    var selectedTestMode = ""
    var selectedTestDate = ""
    var multiQuizIntent = Intent()
    private lateinit var selectedPlayDate: String
    private lateinit var selectedQuizMode: String
    private var mListener: OnHomeFrgmentFragInteraction? = null
    private val latestBooksAdapter by inject<LatestBooksAdapter>()
    private val wonderPubSharedPrefs = WonderPubSharedPrefs.getInstance(context)
    private lateinit var firebaseAnalytics: FirebaseAnalytics
    private var lastquizId :String = ""
    private var lastQuizRecId :String =""
    private var multiQuizData: String? = null
    private var tabPos :Int = 0

    private var rssReader: RssReader? = null

    override fun onAttach(activity: Activity) {
        myContext = activity as FragmentActivity
        super.onAttach(activity)
        if (activity is OnHomeFrgmentFragInteraction) {
            mListener = activity
        } else {
            throw RuntimeException(context.toString()
                    + " must implement OnFragmentInteractionListener")
        }
    }

    override fun onCreateView(
            inflater: LayoutInflater,
            container: ViewGroup?,
            savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        rootview = binding.root
        try {
            initViews()
            initTabView()
            initObserver()
            binding.lvCenter?.smoothToShow()
            getdata()
            initLatestBooksRecycler()
            initShopBooks()
            initStatistics()
            getPreferenceData()
            validateLastLogin()
            setProfile()
            firebaseAnalytics = Firebase.analytics
            val bundle = Bundle()
            bundle.putString(FirebaseAnalyticsUtils.ACTIVITY_NAME, "HomeFragment")
            bundle.putString(FirebaseAnalyticsUtils.LOCATION, "HomeFragment onCreateView")
            firebaseAnalytics.logEvent(FirebaseAnalyticsUtils.ACTION, bundle)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return rootview
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    private fun initShopBooks() {
        viewModel.getShopBooks(buildRequest())
    }

    private fun buildRequest(): ShopBooksRequest {
        val level = wonderPubSharedPrefs.sharedPrefsUserLevelPref
        val syllabus = if (wonderPubSharedPrefs.sharedPrefsUserSyllabusPref != null && wonderPubSharedPrefs.sharedPrefsUserSyllabusPref.size > 0
            && wonderPubSharedPrefs.sharedPrefsUserSyllabusPref[0] != null) {
            wonderPubSharedPrefs.sharedPrefsUserSyllabusPref.joinList()
        } else {
            ""
        }
        /*val syllabus = wonderPubSharedPrefs.sharedPrefsUserSyllabusPref.joinList()*/
        val grade = if (wonderPubSharedPrefs.sharedPrefsUserGradePref != null && wonderPubSharedPrefs.sharedPrefsUserGradePref.size > 0
            && wonderPubSharedPrefs.sharedPrefsUserGradePref[0] != null) {
            wonderPubSharedPrefs.sharedPrefsUserGradePref.joinList()
        }
        else {
            ""
        }
        return ShopBooksRequest(level, syllabus, grade, mcqBook = false)
    }

    //region Init
    private fun initViews() {
        try {
            mainshimmer = binding.itemHomeOptionsNewShimmer
            //mainshimmer.startShimmerAnimation()
            lltLastQuiz = binding.lltLastQuiz
            lltLastQuiz.setOnClickListener{
                aviLoader.smoothToShow()
               // viewModel.getQuizQuestionsAnswers(lastquizId)
                viewModel.getFullQuizDetails(QuizFullDetails(lastQuizRecId))
            }

            tabLayout = binding.tabLayout
            viewPager = null // No viewPager in layout, only viewPager1
            viewPager2 = binding.viewPager1
            rlProfile = binding.rrltUserProfile
            llProfile = binding.lltUserPerformance
            aviLoader = binding.aviLoader
            progressLoader = binding.progressLoader
            textView = binding.textComingSoon
            comingSoonAnimView = binding.animView
            imageView = binding.imgPcommanderIcon
            textBadge = binding.textBadge
            textLifeTimePoints = binding.textLifeTimePoints
            textLifeTimeMedals = binding.textLifeTimeMedals
            imageProfile = binding.userImageView
            textName = binding.txtUserProfilename
            textUserName = binding.txtUsername
            dailyTestsList = binding.dailyTestsRecyclerview
            lltPoints = binding.lltPoints
            lltMedals = binding.lltMedals
            txtShowMore = binding.txtshowMore
            tvLearn = binding.tvLearn
            tvCommunity = binding.tvCommunity
            rlProfile.setOnClickListener {
                val intent = Intent(context, ProfileActivity::class.java)
                this.startActivity(intent)
            }
            lltPoints.setOnClickListener {
                context?.let { it1 -> BadgesDialog().showDialog(it1) }
            }
            lltMedals.setOnClickListener {
                context?.let { it1 -> MedalsDialog().showDialog(it1) }
            }

            txtQuiztitle = binding.txtQuiztitle
            txtScore = binding.txtScore
            txtQuizStatus = binding.txtQuizStatus
            initDashBoard()
            infojoinGame = binding.userStatistics.infojoinGame
            val infoText = getString(R.string.challenge_info)
            infojoinGame.setOnClickListener {
                SimpleTooltip.Builder(myContext)
                    .anchorView(it)
                    .text(Html.fromHtml("<small>$infoText</small>"))
                    .textColor(Color.WHITE)
                    .gravity(Gravity.BOTTOM)
                    .cornerRadius(5F)
                    .dismissOnInsideTouch(true)
                    .dismissOnOutsideTouch(true)
                    .animated(true)
                    .transparentOverlay(true)
                    .build()
                    .show()
            }

            txtNoLastQuiz = binding.txtNoLastQuiz
            lltLastQuizData = binding.lltLastQuizData
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun initTabView() {
        tabLayout!!.addTab(tabLayout!!.newTab().setText(myContext!!.resources.getString(R.string.daily)))
        tabLayout!!.addTab(tabLayout!!.newTab().setText(myContext!!.resources.getString(R.string.weekly)))
        tabLayout!!.addTab(tabLayout!!.newTab().setText(myContext!!.resources.getString(R.string.monthly)))
        tabLayout!!.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                when (tab.position) {
                    0 -> {
                        textView.visibility = View.GONE
                        viewPager2.visibility = View.VISIBLE
                        isDayTabEnable = true
                        updateSwipeTabs()
                        val bundle = Bundle()
                        bundle.putString(FirebaseAnalyticsUtils.ACTIVITY_NAME, "HomeFragment")
                        bundle.putString(FirebaseAnalyticsUtils.ACTION, "HomeFragment daily")
                        firebaseAnalytics.logEvent(FirebaseAnalyticsUtils.LOCATION, bundle)

                    }
                    1 -> {
                        textView.visibility = View.GONE
                        viewPager2.visibility = View.VISIBLE
                        isDayTabEnable = false
                        updateWeeklyTabs()
                        val bundle = Bundle()
                        bundle.putString(FirebaseAnalyticsUtils.ACTIVITY_NAME, "HomeFragment")
                        bundle.putString(FirebaseAnalyticsUtils.ACTION, "HomeFragment weekly")
                        firebaseAnalytics.logEvent(FirebaseAnalyticsUtils.LOCATION, bundle)
                    }
                    2 -> {
                        textView.visibility = View.GONE
                        viewPager2.visibility = View.VISIBLE
                        isDayTabEnable = false
                        progressLoader.hide()
                        updateMonthlyTabs()
                        val bundle = Bundle()
                        bundle.putString(FirebaseAnalyticsUtils.ACTIVITY_NAME, "HomeFragment")
                        bundle.putString(FirebaseAnalyticsUtils.ACTION, "HomeFragment monthly")
                        firebaseAnalytics.logEvent(FirebaseAnalyticsUtils.LOCATION, bundle)
                    }
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {
            }

            override fun onTabReselected(tab: TabLayout.Tab) {
            }
        })
    }

    private fun initStatistics() {
        val view = binding.userStatistics.root
        codeContainer = binding.userStatistics.codeContainer
        gameCode = binding.userStatistics.homeGameCode
        btnJoinGame = binding.userStatistics.joinGame
        btnJoinGame.setOnClickListener {
            hideKeyboard()
            if (gameCode.text?.isNotBlank() == true) {
               loadGame(gameCode.text.toString())
            } else {
                Snackbar.make(rootview!!, "Please enter a game code", Snackbar.LENGTH_SHORT).show()
            }
        }
        if (FlavourHelper.isGameCodeEnabled()) {
            view.visibility = View.VISIBLE
            codeContainer.visibility = View.VISIBLE
        }
        else {
            view.visibility = View.GONE
            codeContainer.visibility = View.GONE
        }
    }

    private fun loadGame(code: String) {
        viewModel.challengerCode(code, 2)
        aviLoader.smoothToShow()
    }

    private fun observeLoadGame(): Observer<Data<JSONObject>> {
        return Observer<Data<JSONObject>> { data ->
            aviLoader.smoothToHide()
            when (data.responseType) {
                Status.SUCCESSFUL -> {
                    data.data?.let { jsonObject1 ->
                        if (validateGameCode(jsonObject1)) {
                            val joinGameIntent = Intent(context, QuizActivity::class.java).also {
                                it.putExtra("joinGame", true)
                                val stringResponse = data.data.toString()
                                it.putExtra("data", stringResponse)
                            }
                            startActivity(joinGameIntent)
                            gameCode.setText("")
                        } else if (jsonObject1.optString("status").lowercase() != "success") {
                            Snackbar.make(rootview!!, "Please enter a valid code",
                                Snackbar.LENGTH_SHORT).show()
                            btnJoinGame.setBackgroundResource(R.drawable.button_shape_default)
                        } else if (jsonObject1.has("hasAccess")
                            && !jsonObject1.optBoolean("hasAccess")) {
                            Snackbar.make(rootview!!, "Access denied for the code",
                                Snackbar.LENGTH_SHORT).show()
                            btnJoinGame.setBackgroundResource(R.drawable.button_shape_default)
                        }
                    }
                }
                Status.ERROR, Status.HTTP_UNAVAILABLE ->{
                    btnJoinGame.setBackgroundResource(R.drawable.button_shape_default)
                    netWorkError()}
                Status.LOADING -> {}
            }
        }
    }

    private fun initDashBoard() {
        dashBoardView = binding.lltDashboard
        val back = binding.dashboardBack
        tabLayout?.visibility = View.GONE
        dailyTestsList.layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
        back.setOnClickListener {
            navigateBack()
        }
        txtShowMore.setOnClickListener {
            mListener?.onEBooksClicked("Show more - Game books")
        }
        prepareOptions()
        if (FlavourHelper.isSiteLevelBooksEnabled()) {
            viewModel.fetchSiteLevelBooks()
            observeSiteLevelBooks()
        }
    }

    private fun prepareOptions() {
        val items1 = getOptions1()
        val items2 = getOptions2()
        populateOptions1(items1)
        populateOptions2(items2)
    }

    private fun populateOptions1(items: List<HomeOptions>) {
        val recyclerView: RecyclerView? = rootview?.findViewById(R.id.fragment_home_options_recycler)
        val adapter = HomeOptionsAdapter(items, this)
        recyclerView?.adapter = adapter
        recyclerView?.layoutManager = GridLayoutManager(requireActivity(), 3)
        val space = resources.getDimensionPixelSize(com.intuit.sdp.R.dimen._3sdp)
        recyclerView?.addItemDecoration(SpacesItemDecoration(space))
    }

    private fun populateOptions2(items: List<HomeOptions>) {
        val recyclerView: RecyclerView? = rootview?.findViewById(R.id.fragment_home_options2_recycler)
        val adapter = HomeOptionsAdapter(items, this)
        recyclerView?.adapter = adapter
        recyclerView?.layoutManager = GridLayoutManager(requireActivity(), 3)
        val space = resources.getDimensionPixelSize(com.intuit.sdp.R.dimen._3sdp)
        recyclerView?.addItemDecoration(SpacesItemDecoration(space))
    }

    private fun getOptions1(books: List<SiteBooks>? = null): List<HomeOptions> {
        val options = arrayListOf<HomeOptions>()
        val optionCurrentAffairs = HomeOptions(getString(R.string.home_current_affairs),
            getString(R.string.home_current_affairs_sub_title))
        val abTestingValue = wonderPubSharedPrefs.dailyTestABTestValue.ifEmpty {
            getString(R.string.home_daily_tests)
        }
        val optionDailyTest = HomeOptions(abTestingValue,
            getString(R.string.daily_test_subtitle))
        val optionEbook = HomeOptions(getString(R.string.home_ebooks), "Smart Game\nbooks")
        if (FlavourHelper.isCurrentAffairsEnabled()) {
            options.add(optionCurrentAffairs)
        }

        if (FlavourHelper.isOnlineTestEnabled()) {
            options.add(optionDailyTest)
        }
        options.add(optionEbook)
        books?.forEach {
           val subtitle = when(it.bookName) {
                "Physics" -> R.string.home_physics_subtitle
                "Chemistry" -> R.string.home_chemistry_subtitle
                "Mathematics" -> R.string.home_mathematics_subtitle
                "Biology" -> R.string.home_biology_subtitle
                else -> -1
            }
            if (subtitle != -1) {
                options.add(HomeOptions(title = it.bookName,
                                        id = it.bookId.toInt(),
                                        subTitle = getString(subtitle)))
            } else {
                options.add(HomeOptions(title = it.bookName, id = it.bookId.toInt()))
            }
        }
        return options
    }

    private fun getOptions2(books: List<SiteBooks>? = null): List<HomeOptions> {
        val options = arrayListOf<HomeOptions>()
        val optionCafe = HomeOptions(getString(R.string.home_cafe), getString(R.string.home_cafe_sub_title))
        val optionsNews = HomeOptions(getString(R.string.home_news), getString(R.string.home_news_sub_title))
        val optionsJobNotification = HomeOptions(getString(R.string.home_jobs), "")
        books?.forEach {
            val subtitle = when(it.bookName) {
                "Physics" -> R.string.home_physics_subtitle
                "Chemistry" -> R.string.home_chemistry_subtitle
                "Mathematics" -> R.string.home_mathematics_subtitle
                "Biology" -> R.string.home_biology_subtitle
                else -> -1
            }
            if (subtitle != -1) {
                options.add(HomeOptions(title = it.bookName,
                    id = it.bookId.toInt(),
                    subTitle = getString(subtitle)))
            } else {
                options.add(HomeOptions(title = it.bookName, id = it.bookId.toInt()))
            }
        }
        if (FlavourHelper.isDiscussEnabled()) {
            options.add(optionCafe)
        }
        if (FlavourHelper.isNewsEnabled()) {
            options.add(optionsNews)
        }
        if (FlavourHelper.isJobNotificationEnabled()) {
            options.add(optionsJobNotification)
        }
        if (options.size == 0) {
            tvCommunity.visibility = View.GONE
        }
        else {
            tvCommunity.visibility = View.VISIBLE
        }
        return options
    }

    //endregion

    //region Options Navigation
    fun navigateBack() {
        if (this::dailyTestsList.isInitialized) {
            if (tabLayout?.isShown == true || dailyTestsList.isShown) {
                displayDashBoard()
                (requireActivity() as DashBoardActivity).isDailyTestOpen(false)

            } else {
                dailyTestViewModel.retrieveDailyTests()
                viewPager2.visibility = View.GONE
                dailyTestsList.visibility = View.VISIBLE
                binding.dashboardTitle.text = "Online Tests"
            }
        }
    }

    fun displayDashBoard() {
        if (null != binding.dashboardToolbar) {
            binding.dashboardToolbar.visibility = View.GONE
            dashBoardView.visibility = View.VISIBLE
            tabLayout?.visibility = View.GONE
            viewPager2.visibility = View.GONE
            binding.rrltUserProfile.visibility = View.VISIBLE
            binding.lltUserPerformance.visibility = View.VISIBLE
            dailyTestsList.visibility = View.GONE
            comingSoonAnimView.visibility = View.GONE
        }
    }

    /**
     * Navigates user to QuizActivity to display results of the last attempted quiz
     */
    private fun navigateToQuizStats() {
        val quizIntent = Intent(context, QuizActivity::class.java).also { it.putExtra("stats", true) }
        startActivity(quizIntent)
    }

    private fun navigateDailyTest() {
        binding.rrltUserProfile.visibility = View.GONE
        binding.lltUserPerformance.visibility = View.GONE
        dashBoardView.visibility = View.GONE
        binding.dashboardToolbar.visibility = View.VISIBLE
        binding.dashboardTitle.text = "Online Tests"
        comingSoonAnimView.visibility = View.GONE
        if (wonderPubSharedPrefs.userPreferredDailyTest.isEmpty()) {
            dailyTestViewModel.retrieveDailyTests()
        }
        else {
            val gson = Gson()
            val testJson = wonderPubSharedPrefs.userPreferredDailyTest
            val test = gson.fromJson(testJson, DailyTestResponse::class.java)
            onTestSelected(test)
        }
        progressLoader.smoothToShow()
        context?.let {
            FirebaseAnalyticsUtils(it).logDailyTestABTestingEvent(wonderPubSharedPrefs.dailyTestABTestValue)
        }
        (requireActivity() as DashBoardActivity).isDailyTestOpen(true)
    }

    private fun navigateNews() {
        if (WonderPubSharedPrefs.getInstance(context).newsLangPref.isNotEmpty() && WonderPubSharedPrefs.getInstance(context).newsSource.isNotEmpty())
        {
            val serializedSourceObject: String = WonderPubSharedPrefs.getInstance(context).newsSource
            val serializedLangObject: String = WonderPubSharedPrefs.getInstance(context).newsLangPref
            val gson = Gson()
            val type: Type = object : TypeToken<List<NewsSource?>?>() {}.type
            var newsSourceList: List<NewsSource> = gson.fromJson(serializedSourceObject, type)

            val tempSelectedNewsSource: List<NewsSource> =
                ArrayList(newsSourceList)

            newsSourceList = ArrayList<NewsSource>()

            for (newsSource in tempSelectedNewsSource) {
                if (newsSource.isChecked) {
                    newsSourceList.add(newsSource)
                }
            }

            val gson2 = Gson()
            val type2: Type = object : TypeToken<List<NewsLanguage?>?>() {}.type
            var newsLangList: List<NewsLanguage> = gson2.fromJson(serializedLangObject, type2)

            val tempSelectedNewsLang: List<NewsLanguage> =
                ArrayList(newsLangList)

            newsLangList = ArrayList<NewsLanguage>()

            for (newsLang in tempSelectedNewsLang) {
                if (newsLang.isChecked) {
                    newsLangList.add(newsLang)
                }
            }

            var userPrefModel: UserPrefModel? = null
            if (userPrefModel == null) {
                userPrefModel = UserPrefModel()
            }

            userPrefModel.userNewsLanguage = newsLangList
            userPrefModel.userNewsSource = newsSourceList
            TemporaryDataHolder.setUserPrefModel(userPrefModel)
            val feedDetailsIntent = Intent(context, NewsListActivity::class.java)
            startActivity(feedDetailsIntent)

        } else {
            val handleSharedFeedIntent = Intent(activity, NewsPreferenceActivity::class.java)
            startActivity(handleSharedFeedIntent)
        }
    }

    private fun navigateJobs() {
        val url = "https://prepjoy.com/job-notifications"
        val i = Intent(Intent.ACTION_VIEW)
        i.data = Uri.parse(url)
        startActivity(i)
    }

    fun refreshHomePage()
    {
        try {
            tabLayout?.visibility = View.VISIBLE
            viewPager2.visibility = View.VISIBLE
            dailyTestsList.visibility = View.GONE
            dashBoardView.visibility = View.GONE
            binding.rrltUserProfile.visibility = View.GONE
            binding.lltUserPerformance.visibility = View.GONE
            binding.dashboardToolbar.visibility = View.VISIBLE
            binding.dashboardTitle.text = getString(R.string.home_current_affairs)
            progressLoader.smoothToShow()
            viewModel.getStartEndDates()
            (requireActivity() as DashBoardActivity).isDailyTestOpen(true)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    //endregion

    //region Observers
    private fun initObserver() {

        // Inflate the layout for this fragment
        rssReader = RssReader(this)
        viewModel.startEndDatesResponse.observe(viewLifecycleOwner, ::startEndDatesResponse)
        viewModel.currentAffairsUserDetails.observe(viewLifecycleOwner, ::currentAffairsUserDetails)
        viewModel.quizResponse.observe(viewLifecycleOwner, ::quizResponse)
        viewModel.currentFragmentPosition.observe(viewLifecycleOwner, ::currentFragmentPosition)
        viewModel.multiQuizResponse.observe(viewLifecycleOwner, ::multiQuizResponse)
        viewModel.checkForCard.observe(viewLifecycleOwner, ::checkForCard)
        dailyTestViewModel.dailyTests.observe(viewLifecycleOwner, ::observeDailyTests)
        dailyTestViewModel.dailyDates.observe(viewLifecycleOwner, ::observeDailyTestDates)
        dailyTestViewModel.testQuizResponse.observe(viewLifecycleOwner, ::dailyTestQuizResponse)
        viewModel.challengerCode2.observe(viewLifecycleOwner, observeLoadGame())
//        viewModel.quizAttemptedDetails.observe(viewLifecycleOwner, ::quizAttemptedDetails)
//        viewModel.lastQuizDetails.observe(viewLifecycleOwner, ::lastQuizDetails)
//        viewModel.historyForAllQuizDetails.observe(viewLifecycleOwner, ::historyForAllQuizDetails)
//        viewModel.historyForAQuizDetails.observe(viewLifecycleOwner, ::historyForAQuizDetails)
//        viewModel.getAllFullQuizDetails.observe(viewLifecycleOwner, ::getAllDetailsForQuiz)
        viewModel.quizQuestionAnswers.observe(viewLifecycleOwner, ::quizQuestionAnswersResponse)

    }

    private fun quizAttemptedDetails(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
            }
            Status.SUCCESSFUL -> {
                data.data?.let {
                    Log.e("Data",":"+data.data)
                }
            }
            Status.HTTP_UNAVAILABLE -> {
            }
            Status.ERROR -> {
            }
        }
    }

    private fun lastQuizDetails(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
            }
            Status.SUCCESSFUL -> {
                data.data?.let {
                    Log.e("Data",":"+data.data)
                   val res =  it.getString("lastQuizDetails")
                    if (res.equals("no records")) {
                        mainshimmer.visibility = View.GONE
                        lltLastQuiz.visibility = View.VISIBLE
                        lltLastQuizData.visibility =View.GONE
                        txtNoLastQuiz.visibility = View.VISIBLE
                        lltLastQuiz.isEnabled = false

                    }
                    else
                    {
                        mainshimmer.visibility = View.GONE
                        lltLastQuiz.visibility = View.VISIBLE
                        lltLastQuiz.isEnabled = true
                        val dataArray = JSONArray(res)
                        val jsonObject = dataArray.getJSONObject(0)
                        txtQuiztitle.text = jsonObject.getString("quizName")
                        txtScore.text = jsonObject.getString("points")
                        if(jsonObject.has("matchStatus")&& !jsonObject.isNull("matchStatus")){
                            if(jsonObject.getString("matchStatus").equals("win"))
                                txtQuizStatus.text = "Won"
                            else
                                txtQuizStatus.text = jsonObject.getString("matchStatus")
                        }
                        else
                        {
                            if(jsonObject.has("quizType")) {
                                if (jsonObject.getString("quizType").equals("practice")) {
                                    txtQuizStatus.text = "Practice"
                                } else if (jsonObject.getString("quizType").contains("test")) {
                                    txtQuizStatus.text = "Test"
                                }
                                else
                                {
                                    txtQuizStatus.text = "--"
                                }
                            }
                            else
                                txtQuizStatus.text = "--"
                        }
                        lastQuizRecId = jsonObject.getInt("quizRecId").toString()
                        lastquizId = jsonObject.getInt("resId").toString()
                    }
                }
            }
            Status.HTTP_UNAVAILABLE -> {
                mainshimmer.visibility = View.GONE
                lltLastQuiz.visibility = View.VISIBLE
                lltLastQuizData.visibility =View.GONE
                txtNoLastQuiz.visibility = View.VISIBLE
                lltLastQuiz.isEnabled = false
                txtNoLastQuiz.text = "Some thing went wrong.\nPlease try again later."
            }
            Status.ERROR -> {
                mainshimmer.visibility = View.GONE
                lltLastQuiz.visibility = View.VISIBLE
                lltLastQuizData.visibility =View.GONE
                txtNoLastQuiz.visibility = View.VISIBLE
                lltLastQuiz.isEnabled = false
                txtNoLastQuiz.text = "Some thing went wrong.\nPlease try again later."
            }
        }
    }

    private fun historyForAllQuizDetails(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
            }
            Status.SUCCESSFUL -> {
                data.data?.let {
                    Log.e("Data",":"+data.data)
                }
            }
            Status.HTTP_UNAVAILABLE -> {
            }
            Status.ERROR -> {
            }
        }
    }

    private fun historyForAQuizDetails(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
            }
            Status.SUCCESSFUL -> {
                data.data?.let {
                    Log.e("Data",":"+data.data)
                }
            }
            Status.HTTP_UNAVAILABLE -> {
            }
            Status.ERROR -> {
            }
        }
    }
    private fun getAllDetailsForQuiz(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
            }
            Status.SUCCESSFUL -> {
                data.data?.let {
                    try {
                        if(tabPos ==0) {
                            aviLoader?.smoothToHide()
                            wonderPubSharedPrefs.userLastQuizResults = data.data.toString()
                            navigateToQuizStats()
                        }

                    } catch (e: JSONException) {
                        e.printStackTrace()
                    }
                }
            }
            Status.HTTP_UNAVAILABLE -> {

                try {
                    aviLoader?.smoothToHide()
                } catch (e: Exception) {
                    aviLoader?.smoothToHide()
                    Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), myContext)
                    e.printStackTrace()
                }
            }

            Status.ERROR -> {
                try {
                    aviLoader?.smoothToHide()
                    if (data.error.toString().contains("401")) {
                        WonderPubSharedPrefs.getInstance(myContext).clearAllSharePref()
                        val intent = Intent(myContext, LoginActivity::class.java)
                        startActivity(intent)
                    }
                } catch (e: Exception) {
                    aviLoader?.smoothToHide()
                    e.printStackTrace()
                }
            }
        }
    }

    private fun quizQuestionAnswersResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
            }
            Status.SUCCESSFUL -> {
                data.data?.let {
                    try {
                       // progressLoader?.smoothToHide()
                        wonderPubSharedPrefs.userLastQuizData = data.data.toString()
                        viewModel.getFullQuizDetails(QuizFullDetails(lastQuizRecId))

                    } catch (e: JSONException) {
                        e.printStackTrace()
                    }
                }
            }
            Status.HTTP_UNAVAILABLE -> {

                try {
                    progressLoader?.smoothToHide()
                } catch (e: Exception) {
                    progressLoader?.smoothToHide()
                    Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), myContext)
                    e.printStackTrace()
                }
            }

            Status.ERROR -> {
                try {
                    progressLoader?.smoothToHide()
                    if (data.error.toString().contains("401")) {
                        WonderPubSharedPrefs.getInstance(myContext).clearAllSharePref()
                        val intent = Intent(myContext, LoginActivity::class.java)
                        startActivity(intent)
                    }
                } catch (e: Exception) {
                    progressLoader?.smoothToHide()
                    e.printStackTrace()
                }
            }
        }
    }

    private fun observeDailyTestDates(data: Data<JSONObject>) {
        when(data.responseType) {
            Status.LOADING -> {
                progressLoader.smoothToShow()
            }
            Status.HTTP_UNAVAILABLE, Status.ERROR -> {
                netWorkError()
            }
            Status.SUCCESSFUL -> {
                data.data?.let {
                    updateDailyTests(it)
                }
            }
        }
    }

    private fun observeDailyTests(data: Data<List<DailyTestResponse>>) {
        when(data.responseType) {
            Status.LOADING -> {
                progressLoader.smoothToShow()
            }
            Status.ERROR, Status.HTTP_UNAVAILABLE -> {
                netWorkError()
            }
            Status.SUCCESSFUL -> {
                progressLoader.smoothToHide()
                if (data.data?.isEmpty() == true) {
                    comingSoonAnimView.visibility = View.VISIBLE
                }
                else {
                    data.data?.let {
                        val adapter = DailyTestsAdapter(it, this)
                        dailyTestsList.adapter = adapter
                        dailyTestsList.visibility = View.VISIBLE
                    }
                }
            }
        }
    }

    private fun observeSiteLevelBooks() {
        viewModel.siteBooks.observe(viewLifecycleOwner) { siteBooksData ->
            when(siteBooksData.responseType) {
                Status.SUCCESSFUL -> {
                    populateOptions1(getOptions1(siteBooksData.data))
                    populateOptions2(getOptions2(siteBooksData.data))
                }
                else -> {

                }
            }
        }
    }

    private fun netWorkError() {
        if (Utils.isNetworkAvailable(context)) {
            Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), myContext)
        } else {
            Utils.showTopSnackBar(resources.getString(R.string.internet_connection_offline_text), myContext)
        }
        progressLoader.smoothToHide()
    }

    private fun checkForCard(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
                //progressLoader.smoothToHide()
            }
            Status.SUCCESSFUL -> {
                progressLoader.hide()
                data.data?.let {
                    data.data?.let {
                        isCardShowForCurrentData = parseDataForCard(data.data!!)
                        // if (isDateThere)

                        if (isDayTabEnable)
                            updateSwipeTabs()
                    }
                }
            }
            Status.HTTP_UNAVAILABLE -> {
                progressLoader.hide()
                if (Utils.isNetworkAvailable(context)) {
                    Utils.showTopSnackBar(resources.getString(R.string.server_under_maintenance), myContext)
                } else {
                    Utils.showTopSnackBar(resources.getString(R.string.internet_connection_offline_text), myContext)
                }
                if (isDayTabEnable)
                    updateSwipeTabs()
            }
            Status.ERROR -> {
                progressLoader.smoothToHide()
                if (Utils.isNetworkAvailable(context)) {
                    Utils.showTopSnackBar(resources.getString(R.string.some_thing_went_wrong), myContext)
                } else {
                    Utils.showTopSnackBar(resources.getString(R.string.internet_connection_offline_text), myContext)
                }
                if (isDayTabEnable)
                    updateSwipeTabs()
            }
        }
    }
    //endregion
    private fun parseDataForCard(jsonObject: JSONObject): Boolean {
        arrayForCurr = booleanArrayOf(false, false, false, false)
        var isShow = false
        try {
            val readingMaterials = jsonObject.optString("readingMaterials")
            val quizResId = jsonObject.optString("quizResId")
            val videos = jsonObject.optString("videos")
            val audios = jsonObject.optString("audios")
            arrayForCurr[0] = !quizResId.equals("-1", ignoreCase = true)
            arrayForCurr[1] = !readingMaterials.equals("[]", ignoreCase = true)
            arrayForCurr[2] = !videos.equals("[]", ignoreCase = true)
            arrayForCurr[3] = !audios.equals("[]", ignoreCase = true)
            if (!(readingMaterials.isEmpty() || quizResId.isEmpty() || videos.isEmpty())) {
                isShow = true
            }
        } catch (e: Exception) {
            Log.e("sdfsdfsdfsdfsdfsd", e.message.toString())
        }
        return isShow
    }

    private fun validateGameCode(data: JSONObject): Boolean {
        return data.has("status") && data.getString("status").
            lowercase(Locale.getDefault()) == "success" && data.has("hasAccess")
                && data.optBoolean("hasAccess")
    }

    private fun currentFragmentPosition(int: Int) {
       setProfile()
        if (!Utils.isNetworkAvailable(context)) {
            progressLoader.hide()
        }
        tabPos = int

    }

    override fun onResume() {
        super.onResume()
        //viewModel.getCurrentAffairsUserDetails(BuildConfig.SITE_ID)
        val visibility = (!BuildConfig.FLAVOR.equals(Flavours.NEET.flavour, ignoreCase = true))
        (requireActivity() as DashBoardActivity).showOrHideUserPreference(visibility)
        setProfile()
        initStatistics()
        initShopBooks()
    }

    private fun startEndDatesResponse(data: Data<JSONObject>) {
        // swipeToRefresh.isRefreshing = false
        when (data.responseType) {
            Status.LOADING -> {
                progressLoader.smoothToShow()
            }
            Status.SUCCESSFUL -> {
                data.data?.let {
                    val v = data.data!!.getString("latestDate").split("-")
                    val date = v[2] + "-" + v[1] + "-" + v[0]
                    jsonObject = data.data!!

                    viewModel.getCheckForCard(date)
                    progressLoader.smoothToShow()

                }
            }
            Status.HTTP_UNAVAILABLE -> {
                progressLoader.hide()
            }

            Status.ERROR -> {
                progressLoader.hide()
                if (FlavourHelper.companyName() != "Wonderslate") {
                    textView.visibility = View.GONE
                    viewPager2.visibility = View.GONE
                    comingSoonAnimView.visibility = View.VISIBLE
                }
                else {
                    viewPager2.visibility = View.GONE
                    textView.visibility = View.VISIBLE
                    textView.text = getString(R.string.no_record)
                }
            }
        }
    }

    private fun currentAffairsUserDetails(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
            }
            Status.SUCCESSFUL -> {

                data.data?.let {
                    updateUserPoints(data.data!!)
                }
            }
            Status.HTTP_UNAVAILABLE, Status.ERROR -> {
            }
        }
    }

    private fun updateUserPoints(jsonObject: JSONObject) {
        try {
            val s = jsonObject.getString("details")

            val arr = s.split(",").toTypedArray()

            if (s.contains("totalPoints")) {
                for (i in arr.indices) {
                    if (arr[i].contains("totalPoints")) {
                        wonderPubSharedPrefs.totalPoints =
                                arr[i].split(":")[1]
                        break
                    }
                }
            }

            if (s.contains("currentBadge")) {
                for (i in arr.indices) {
                    if (arr[i].contains("currentBadge")) {
                        wonderPubSharedPrefs.totalBades =
                                arr[i].split(":")[1]
                        break
                    }
                }
            }

            if (s.contains("totalMedals")) {
                for (i in arr.indices) {
                    if (arr[i].contains("totalMedals")) {
                        wonderPubSharedPrefs.totalMedals =
                                arr[i].split(":")[1]
                        break
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        setProfile()
    }

    private fun dailyTestQuizResponse(data: Data<QuizResponse>) {
        when (data.responseType) {
            Status.LOADING -> {
                progressLoader.smoothToShow()
            }
            Status.SUCCESSFUL -> {
                progressLoader.smoothToHide()
                data.data?.let { loadDailyTestQuiz(it) }
            }
            Status.ERROR -> {
                netWorkError()
                progressLoader.smoothToHide()
            }
            Status.HTTP_UNAVAILABLE -> {
                netWorkError()
            }
        }
    }

    private fun multiQuizResponse(data: Data<QuizResponse>) {
        when (data.responseType) {
            Status.LOADING -> {
                progressLoader.smoothToShow()
            }
            Status.SUCCESSFUL -> {
                progressLoader.smoothToHide()
                data.data?.let { loadMultiQuiz(it) }
            }
            Status.ERROR -> {
                netWorkError()
            }
            Status.HTTP_UNAVAILABLE -> {
               netWorkError()
            }
        }
    }

    private fun quizResponse(data: Data<JSONObject>) {
        when (data.responseType) {
            Status.LOADING -> {
                //progressLoader.smoothToHide()
            }
            Status.SUCCESSFUL -> {
                progressLoader.hide()
                data.data?.let {
                    Log.e("Quizadadassd", "" + data)
                    data.data?.let {
                        if (!calledFrom.equals("-1111", ignoreCase = true)) {
                            loadQuizData(it)
                            calledFrom = "-1111"
                        }
                    }
                }
            }
            Status.HTTP_UNAVAILABLE, Status.ERROR -> netWorkError()
        }
    }

    private fun loadMultiQuiz(output: QuizResponse) {
        if (output.result.isBlank()) {
            Utils.showBottomSnackBar(resources.getString(R.string.no_quiz_data_available_week),
                    myContext)
        } else {
            multiQuizIntent.putExtra("quizData", output.result)
            multiQuizIntent.putExtra("language1", output.language1)
            multiQuizIntent.putExtra("language2", output.language2)
            multiQuizIntent.putExtra("challengerName", output.challengerName)
            multiQuizIntent.putExtra("challengerPlace", output.challengerPlace)
            multiQuizIntent.putExtra("realDailyTestDtlId", output.realDailyTestDtlId)
            context?.startActivity(multiQuizIntent)
        }
    }

    private fun loadDailyTestQuiz(output: QuizResponse) {
        if (output.result.isBlank()) {
            Utils.showBottomSnackBar(resources.getString(R.string.no_quiz_data_available_week),
                myContext)
        }
        else if(selectedTestMode.equals("history"))
        {
            val intent = Intent(context, HistoryForQuiz::class.java)
            intent.putExtra("quizId", output.realDailyTestDtlId)
            intent.putExtra("resName", "Daily test")
            intent.putExtra("quizType", "dailyTests")

            intent.putExtra("quizData", output.result)
            intent.putExtra("language1", output.language1)
            intent.putExtra("language2", output.language2)
            intent.putExtra("challengerName", output.challengerName)
            intent.putExtra("challengerPlace", output.challengerPlace)
            intent.putExtra("realDailyTestDtlId", output.realDailyTestDtlId)
            intent.putExtra("testId", selectedTestId)
            intent.putExtra("testMode", selectedTestMode)
            intent.putExtra("testDate", selectedTestDate)


            startActivity(intent)
        }
        else{
            val intent = Intent(context, QuizInstructions::class.java)
            intent.putExtra("quizData", output.result)
            intent.putExtra("language1", output.language1)
            intent.putExtra("language2", output.language2)
            intent.putExtra("challengerName", output.challengerName)
            intent.putExtra("challengerPlace", output.challengerPlace)
            intent.putExtra("realDailyTestDtlId", output.realDailyTestDtlId)
            intent.putExtra("testId", selectedTestId)
            intent.putExtra("testMode", selectedTestMode)
            intent.putExtra("testDate", selectedTestDate)
            context?.startActivity(intent)
        }
    }

    private fun loadQuizData(jsonObject: JSONObject) {
        val jObj = JSONObject(jsonObject.toString())
        if (jObj.getString("resId").equals("-1")) {
            Utils.showBottomSnackBar(resources.getString(R.string.no_quiz_data_available), myContext)
        }
        else if(selectedQuizMode.equals("history"))
        {
            val intent = Intent(context, HistoryForQuiz::class.java)
            intent.putExtra("quizId", jObj.getString("resId"))
            intent.putExtra("resName", "Current Affairs")
            intent.putExtra("quizType", "currentAffairs")
            startActivity(intent)
        }
        else {
            val intent = Intent(context, QuizInstructions::class.java)
            intent.putExtra("quizId", jObj.getString("resId"))
            intent.putExtra("language1", jObj.getString("language1"))
            intent.putExtra("language2", jObj.getString("language2"))
            intent.putExtra("selectedDate", selectedDate)
            intent.putExtra("selectedFormat", selectedQuizMode)
            intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
            context?.startActivity(intent)
        }
    }

    fun updateWeeklyTabs() {
        if (this::jsonObject.isInitialized && jsonObject.has("latestDate")) {
            val startDate = jsonObject.optString("latestDate")
            val endDate = jsonObject.optString("startingDate")
            val weeks = GenerateDates.generateBetweenWeeks(startDate, endDate)
            Collections.sort(weeks)
            if (weeks.isNotEmpty()) {
                val homeSwipingAdapter = HomeSwipingAdapter(weeks,this, arrayForCurr, weeks[weeks.size - 1],context)
                homeSwipingAdapter.setMode("Weekly")
                initAdapter(homeSwipingAdapter)
            } else {
                viewPager2.visibility = View.GONE
                textView.visibility = View.VISIBLE
                textView.text = getString(R.string.no_record)
            }

        }
        else {
            textView.visibility = View.GONE
            viewPager2.visibility = View.GONE
            comingSoonAnimView.visibility = View.VISIBLE
        }
    }

    fun updateMonthlyTabs() {
        if (this::jsonObject.isInitialized && jsonObject.has("latestDate")) {
            val startDate = jsonObject.optString("latestDate")
            val endDate = jsonObject.optString("startingDate")
            val months = GenerateDates.generateBetweenMonths(endDate, startDate)
            if (months.isNotEmpty()) {
                val adapter = HomeSwipingAdapter(months,this, arrayForCurr, months[months.size - 1],context)
                adapter.setMode("Monthly")
                initAdapter(adapter)
            } else {
                viewPager2.visibility = View.GONE
                textView.visibility = View.VISIBLE
                textView.text = getString(R.string.no_record)
            }
        }
        else {
            textView.visibility = View.GONE
            viewPager2.visibility = View.GONE
            comingSoonAnimView.visibility = View.VISIBLE
        }
    }

    private fun updateSwipeTabs() {
        if (this::jsonObject.isInitialized && jsonObject.has("latestDate")) {
            val startDate = jsonObject.optString("latestDate")
            val endDate = jsonObject.optString("startingDate")
            wonderPubSharedPrefs.setDatesResponse(jsonObject)
            val dates: List<String> = GenerateDates.generateBetweenDates(endDate, startDate)
            val homeSwipingAdapter = HomeSwipingAdapter(dates,this, arrayForCurr, dates[dates.size - 1],context)
            homeSwipingAdapter.setMode("Daily")
            initAdapter(homeSwipingAdapter)
        }
        else {
            textView.visibility = View.GONE
            viewPager2.visibility = View.GONE
            comingSoonAnimView.visibility = View.VISIBLE
        }
    }

    private fun initAdapter(adapter: HomeSwipingAdapter) {
        viewPager2.adapter = adapter
        viewPager2.clipToPadding = false
        viewPager2.clipChildren = false
        viewPager2.offscreenPageLimit = 3
        viewPager2.getChildAt(0).overScrollMode = RecyclerView.OVER_SCROLL_NEVER
        val compositePageTransformer = CompositePageTransformer()
        compositePageTransformer.addTransformer(MarginPageTransformer(40))
        compositePageTransformer.addTransformer { page, position ->
            val r = 1 - abs(position)
            page.scaleY = .85f + r * .15f
        }
        if (!dashBoardView.isShown) {
            viewPager2.visibility = View.VISIBLE
        }
        viewPager2.setPageTransformer(compositePageTransformer)
        viewPager2.currentItem = adapter.itemCount
        progressLoader.smoothToHide()

        viewPager2.adapter = adapter
        viewPager2.clipToPadding = false
        viewPager2.clipChildren = false
        viewPager2.offscreenPageLimit = 3
        viewPager2.getChildAt(0).overScrollMode = RecyclerView.OVER_SCROLL_NEVER
        val compositePageTransformer1 = CompositePageTransformer()
        compositePageTransformer1.addTransformer(MarginPageTransformer(40))
        compositePageTransformer1.addTransformer { page, position ->
            val r = 1 - abs(position)
            page.scaleY = .85f + r * .15f
        }
        if (!dashBoardView.isShown) {
            viewPager2.visibility = View.VISIBLE
        }
        viewPager2.setPageTransformer(compositePageTransformer1)
        viewPager2.currentItem = adapter.itemCount
        progressLoader.smoothToHide()


    }

    private fun updateDailyTests(response: JSONObject) {
        if (response.has("startingDate")) {
            val startDate = response.optString("latestDate")
            val endDate = response.optString("startingDate")
            wonderPubSharedPrefs.testDatesResponse = response.toString()
            val dates: List<String> = GenerateDates.generateBetweenDates(endDate, startDate)
            val homeSwipingAdapter = HomeSwipingAdapter(dates,this, arrayForCurr, dates[dates.size - 1],context)
            homeSwipingAdapter.setMode("Tests")
            homeSwipingAdapter.setDailyTestListener(this)
            initAdapter(homeSwipingAdapter)
        }
    }

    private fun processQuizOnDate() {
        if (this::selectedPlayDate.isInitialized && selectedPlayDate != "") {
            val inputFormat: DateFormat = SimpleDateFormat("dd-MMM-yyyy", Locale.ENGLISH)
            val outputFormat: DateFormat = SimpleDateFormat("dd-MM-yyyy", Locale.ENGLISH)
            val date: Date = inputFormat.parse(selectedPlayDate)
            val outputDateStr: String = outputFormat.format(date)
            if (Utils.isNetworkAvailable(context)) {
                progressLoader.smoothToShow()
            }
            viewModel.getQuiz(outputDateStr)
            calledFrom = selectedPlayDate
            selectedDate = outputDateStr
        } else if (null != multiQuizInput) {
            multiQuizInput?.let {
                if (Utils.isNetworkAvailable(context)) {
                    progressLoader.smoothToShow()
                }
                getMultiQuiz(it)
            }
        }
    }

    private fun historyOptionsForDate(pdate: String) {
        try {
            selectedPlayDate = pdate
            multiQuizInput = null
            selectedQuizMode = "history"
            processQuizOnDate()
        } catch (e: Exception) {
            CrashlyticsLogger.logError(WonderPubSharedPrefs.getInstance(context).usermobile, "home fragment historyOptionsForDate", e)
        }
    }

    private fun audioOptionsForDate(pdate: String) {
        try {
            val intent = Intent(context, AudioPlayerActivity::class.java)
            intent.putExtra("inputDate", pdate)
            intent.putExtra("viewFrom", "daily-quiz")
            this.startActivity(intent)
        } catch (e: Exception) {
            CrashlyticsLogger.logError(WonderPubSharedPrefs.getInstance(context).usermobile, "home fragment audioOptionsForDate", e)
        }
    }

    fun showQuizOptionsForDate(pdate: String) {
        try {
            selectedPlayDate = pdate
            multiQuizInput = null
            val options = HomeBottomSheet(this)
            options.show(childFragmentManager, "Options")
        } catch (e: Exception) {
            CrashlyticsLogger.logError(WonderPubSharedPrefs.getInstance(context).usermobile, "home fragment showQuizOptionsForDate", e)
        }
    }

    private fun showMultiQuizOptionsForDate(input: CFMultiQuizInput) {
        try {
            selectedPlayDate = ""
            multiQuizInput = input
            val options = HomeBottomSheet(this)
            options.show(childFragmentManager, "Options")
        } catch (e: Exception) {
            CrashlyticsLogger.logError(WonderPubSharedPrefs.getInstance(context).usermobile, "home fragment showMultiQuizOptionsForDate", e)
        }
    }

    private fun getMultiQuiz(input: CFMultiQuizInput) {
        val bundle = Bundle()
        val intentKey: String
        val activityName: String
        val selectedMultiQuiz: String
        val quizType: String
        if (input.noOfDays == "7") {
            activityName = "HomeFragment Weekly Play"
            intentKey = "selectedWeek"
            selectedMultiQuiz = input.dateOfInput
            quizType = "weekly"
        } else {
            activityName = "HomeFragment Monthly Play"
            intentKey = "selectedMonth"
            selectedMultiQuiz = input.dateOfInput
            quizType = "monthly"
        }
        bundle.putString(FirebaseAnalyticsUtils.ACTIVITY_NAME, activityName)
        bundle.putString(FirebaseAnalyticsUtils.LOCATION, "HomeFragment Adapter play")
        firebaseAnalytics.logEvent(FirebaseAnalyticsUtils.ACTION, bundle)
        multiQuizIntent = Intent(context, QuizInstructions::class.java)
        multiQuizIntent.putExtra(intentKey, selectedMultiQuiz)
        multiQuizIntent.putExtra("quizType", quizType)
        multiQuizIntent.putExtra("selectedFormat", selectedQuizMode)
        multiQuizIntent.putExtra("noOfQuestions", input.noOfQuestions)
        multiQuizIntent.putExtra("noOfDays", input.noOfDays)
        multiQuizIntent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
        multiQuizIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
        viewModel.fetchWeeklyQuiz(input)
    }

    private fun getTestFor(testDate: String) {
        val formatter = SimpleDateFormat("dd-MM-yyyy")
        val parser = SimpleDateFormat("dd-MMM-yyyy")
        val input = DTQuizInput(testId = selectedTestId, dateInput = formatter.
        format(parser.parse(testDate)))
        selectedTestDate = input.dateInput
        progressLoader.smoothToShow()
        dailyTestViewModel.retrieveTest(input)
    }

    private fun setProfile() {
        val url: String = wonderPubSharedPrefs.userImage
        val check: Boolean = "null" in url
        if (check) {
            imageProfile!!.setImageResource(R.drawable.prepjoy_full_icon)
        } else {

            Glide.with(this)
                    .load(wonderPubSharedPrefs.userImage)
                    .diskCacheStrategy(DiskCacheStrategy.NONE)
                    .skipMemoryCache(true)
                    .dontAnimate()
                    .into(object : CustomTarget<Drawable>() {
                        override fun onResourceReady(resource: Drawable, transition: Transition<in Drawable>?) {
                            imageProfile.setImageDrawable(resource)
                        }

                        override fun onLoadCleared(placeholder: Drawable?) {
                        }


                    })
        }
        textName.text = wonderPubSharedPrefs.username
        textUserName.text = wonderPubSharedPrefs.totalBades
        textLifeTimePoints.text = wonderPubSharedPrefs.totalPoints
        textLifeTimeMedals.text = wonderPubSharedPrefs.totalMedals
        val home = HomeBadge(context, imageView, textBadge, wonderPubSharedPrefs.totalBades)
        home.getImageIcon(context, imageView, textBadge, wonderPubSharedPrefs.totalBades)
    }

    override fun onTestSelected(test: DailyTestResponse) {
        val gson = Gson()
        val testJson = gson.toJson(test)
        wonderPubSharedPrefs.userPreferredDailyTest = testJson
        if (test.testId != null) {
            dailyTestViewModel.retrieveDates(test.testId)
            progressLoader.smoothToShow()
            selectedTestId = test.testId
            binding.dashboardTitle.text = test.testName
            dailyTestsList.visibility = View.GONE
            tabLayout?.visibility = View.INVISIBLE
        }
        else {
            Snackbar.make(requireView(), "This test is not available. Please try later.", Snackbar.LENGTH_LONG)
                .show()
        }
    }

    override fun onTestOptionSelected(dateInput: String, operation: String) {
        when(operation) {
            "play" -> selectedTestMode = ""
            /*"practice" -> selectedTestMode = "dailyTests"
            "learn" -> selectedTestMode = "dailyTests"*/
            "practice" -> selectedTestMode = "practice"
            "learn" -> selectedTestMode = "testSeries"
            "history" -> selectedTestMode = "history"
        }
        getTestFor(dateInput)
    }

    override fun onMultiQuizSelected(input: CFMultiQuizInput) {
        showMultiQuizOptionsForDate(input)
    }

    override fun onDailyQuizSelected(inputDate: String, operation: String) {
        when(operation) {
            "read" -> {
                val bundle = Bundle()
                bundle.putString(FirebaseAnalyticsUtils.ACTIVITY_NAME, "HomeFragment adapter read")
                bundle.putString(FirebaseAnalyticsUtils.LOCATION, "HomeFragment Adapter read")
                firebaseAnalytics.logEvent(FirebaseAnalyticsUtils.ACTION, bundle)
                if (Utils.isNetworkAvailable(context)) {
                    val intent = Intent(context, ReadingMaterialActivity::class.java)
                    intent.putExtra("inputDate", inputDate)
                    intent.putExtra("viewFrom", "daily-quiz")
                    startActivity(intent)
                } else {
                    Toast.makeText(
                        context,
                        R.string.internet_connection_offline_text,
                        Toast.LENGTH_LONG
                    ).show()
                }
            }

            "watch" -> {
                val bundle = Bundle()
                bundle.putString(FirebaseAnalyticsUtils.ACTIVITY_NAME, "HomeFragment watch")
                bundle.putString(FirebaseAnalyticsUtils.LOCATION, "HomeFragment Adapter watch")
                firebaseAnalytics.logEvent(FirebaseAnalyticsUtils.ACTION, bundle)

                if (Utils.isNetworkAvailable(context)) {
                    val intent = Intent(context, VideoActivity::class.java)
                    intent.putExtra("inputDate", inputDate)
                    intent.putExtra("viewFrom", "daily-quiz")
                    startActivity(intent)
                } else {
                    Toast.makeText(
                        context,
                        R.string.internet_connection_offline_text,
                        Toast.LENGTH_LONG
                    ).show()
                }
            }

            "play" -> {
                val bundle = Bundle()
                bundle.putString(FirebaseAnalyticsUtils.ACTIVITY_NAME, "HomeFragment play")
                bundle.putString(FirebaseAnalyticsUtils.LOCATION, "HomeFragment Adapter play")
                firebaseAnalytics.logEvent(FirebaseAnalyticsUtils.ACTION, bundle)
                showQuizOptionsForDate(inputDate)
            }

            "history" -> {
                val bundle = Bundle()
                bundle.putString(FirebaseAnalyticsUtils.ACTIVITY_NAME, "HomeFragment history")
                bundle.putString(FirebaseAnalyticsUtils.LOCATION, "HomeFragment Adapter history")
                firebaseAnalytics.logEvent(FirebaseAnalyticsUtils.ACTION, bundle)
                historyOptionsForDate(inputDate)
            }

            "audio" -> {
                val bundle = Bundle()
                bundle.putString(FirebaseAnalyticsUtils.ACTIVITY_NAME, "HomeFragment audio")
                bundle.putString(FirebaseAnalyticsUtils.LOCATION, "HomeFragment Adapter audio")
                firebaseAnalytics.logEvent(FirebaseAnalyticsUtils.ACTION, bundle)
                audioOptionsForDate(inputDate)
            }
        }
    }
    fun refreshCards(resType: String?) {
        if (isAdded && isVisible && !dashBoardView.isShown) {
            viewModel.getStartEndDates()
        }
    }

    override fun onOptionSelected(option: String, quizName: String) {
        when(option) {
            "play" -> selectedQuizMode = ""
            "practice" -> selectedQuizMode = "practice"
            "test" -> selectedQuizMode = "testSeries"
            "history" -> selectedQuizMode = "history"
        }
        processQuizOnDate()
    }
    interface OnHomeFrgmentFragInteraction {
        fun onEBooksClicked(from: String)
        fun onCafeClick()
        fun onBookItemClicked(bookData: ShopBookData, from: String)
    }

    private fun getdata()
    {
        viewModel.latestBooks.collectLatestWithLifecycle(this) { data ->
            when(data.responseType) {
                com.ws.commons.Status.LOADING -> {
                    binding.lvCenter?.smoothToShow()
                    binding.noDatalayout?.hideView()
                    binding.linearNoData?.hideView()
                }

                com.ws.commons.Status.SUCCESSFUL -> {
                    try {
                        // shimmerBooks?.hideView()
                        txtShowMore.visibility = View.VISIBLE
                        binding.lvCenter?.smoothToHide()
                        // shimmerBooks?.hideView()
                     //   swipeToRefresh?.isRefreshing = false

                        val books = data.data ?: arrayListOf()
                        latestBooksAdapter.update(books,true)
                        latestBooksAdapter.toggleSeeMoreOverlay(false)
                        binding.linearNoData?.visibility(books.isEmpty())
                    }
                    catch (e:Exception)
                    {
                        e.printStackTrace()
                    }
                }

                com.ws.commons.Status.ERROR, com.ws.commons.Status.HTTP_UNAVAILABLE -> {
                  // shimmerBooks?.hideView()
                    binding.lvCenter?.smoothToHide()
                  //  swipeToRefresh?.isRefreshing = false

                    // Show error message if adapter is empty
                    if(latestBooksAdapter.itemCount == 0) {
                        if(data.responseType == com.ws.commons.Status.HTTP_UNAVAILABLE) {
                            binding.noDatalayout?.showView()
                        } else {
                            binding.linearNoData?.showView()
                        }
                    } else
                        showToast("Problem while getting books. Please try again.")
                }
                else -> {}
            }
        }
    }

    private fun initLatestBooksRecycler() {
         rootview!!.findViewById<RecyclerView>(R.id.recycleBooks)?.apply {
             //Show 3 books in row if app is running in a tablet else 2 books

             val gridLayoutManager = GridLayoutManager(requireContext(), if(isTablet()) 3 else 2)
             layoutManager = gridLayoutManager
             adapter = latestBooksAdapter
        }

        latestBooksAdapter.onBookClickListener = {
            mListener?.onBookItemClicked(it, "Online Test Series")
        }

        latestBooksAdapter.onSeeMoreClickListener = {
            mListener?.onEBooksClicked("Show more - Game books")
        }
    }

    override fun onHomeOptionClicked(option: HomeOptions, type: HomeOption) {
        when(type) {
            HomeOption.CURRENT_AFFAIRS -> {
                refreshHomePage()
            }
            HomeOption.DAILY_TEST -> {
                navigateDailyTest()
            }
            HomeOption.EBOOKS -> {
                mListener?.onEBooksClicked("Dashboard - Ebooks card")
            }
            HomeOption.CAFE -> {
                mListener?.onCafeClick()
            }
            HomeOption.BOOK -> {
                activity?.let {
                    it.startActivityWithAnim(ChaptersListAct.createIntent(it, ChaptersListConfig(
                        bookId = option.id!!,
                        bookName = option.title,
                        isPreview = false,
                        isFromShop = false
                    )
                    ))
                }
            }
            HomeOption.NEWS ->{
                navigateNews()
            }
            HomeOption.JOB_NOTIFICATION ->{
                navigateJobs()
            }
        }
    }
    private fun getPreferenceData()
    {
        try
        {
            CoroutineScope(Dispatchers.IO).launch {
                val cachedData = viewModel.getPreferenceData("1")
                withContext(Dispatchers.Main) {
                    if (cachedData != null) {
                        wonderPubSharedPrefs.setPrefrenceValue(cachedData.jsonData)
                    }
                }
            }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }
    }

    private fun validateLastLogin() {
        val lastLogin = wonderPubSharedPrefs.lastLogDate
        val currentTime = LocalDateTime.now()
        try {
            val shouldLogUser = lastLogin.isEmpty() ||
                    LocalDateTime.parse(lastLogin).toLocalDate().
                    isBefore(currentTime.toLocalDate())
            if (shouldLogUser) {
                viewModel.addUserLoginLog(wonderPubSharedPrefs)
            }
        } catch (e: Exception) {
            e.message?.let { CrashlyticsLogger.logError(WonderPubSharedPrefs.getInstance(context).usermobile, it, e) }
        }

    }

    override fun rssFeedsLoaded(rssList: MutableList<RSS>?) {
        val rssItems: MutableList<Channel.Item> = ArrayList()
        for (rss in rssList!!) {
            rssItems.addAll(rss.channel.items)
        }

        rssItems.sortWith { item: Channel.Item, t1: Channel.Item ->
            try {
                val localDateTime1 =
                    LocalDateTime.parse(t1.pubDate)
                val localDateTime2 =
                    LocalDateTime.parse(item.pubDate)
                localDateTime1.compareTo(localDateTime2)
            } catch (exception: IllegalArgumentException) {
                0
            }
        }

        TemporaryDataHolder.setMergedItemList(rssItems)
        val feedDetailsIntent = Intent(context, NewsListActivity::class.java)
        //feedDetailsIntent.putExtra(NewsConstants.SELECTED_FEED_PUB_KEY, WonderPubSharedPrefs.getInstance(context).newsSource)
        startActivity(feedDetailsIntent)
    }

    override fun unableToReadRssFeeds(errorMessage: String?) {
        Snackbar.make(requireView(), "Error: $errorMessage", Snackbar.LENGTH_LONG)
            .show()
    }
}