package com.wonderslate.prepjoy.ui.resources.reading

import android.annotation.SuppressLint
import android.content.ActivityNotFoundException
import android.content.Intent
import android.content.res.Configuration
import android.content.res.Configuration.ORIENTATION_LANDSCAPE
import android.content.res.Configuration.ORIENTATION_PORTRAIT
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.Rect
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.text.TextUtils
import android.util.Base64
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.PixelCopy
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.webkit.MimeTypeMap
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.SeekBar
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.canhub.cropper.CropImageContract
import com.canhub.cropper.CropImageContractOptions
import com.canhub.cropper.CropImageOptions
import com.canhub.cropper.CropImageView
import com.downloader.Error
import com.downloader.OnDownloadListener
import com.downloader.PRDownloader
import com.downloader.Priority
import com.downloader.Status
import com.skydoves.powerspinner.OnSpinnerItemSelectedListener
import com.wonderslate.data.interfaces.WSCallback
import com.wonderslate.data.models.MCQ
import com.wonderslate.data.network.Wonderslate
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.data.repository.BookGPT
import com.wonderslate.prepjoy.Utils.MCQFormatter
import com.wonderslate.prepjoy.Utils.MCQLanguageParser
import com.wonderslate.prepjoy.Views.Adapters.MCQAdapter
import com.wonderslate.prepjoy.ui.quiz.QuizActivity
import com.wonderslate.prepjoy.ui.resources.reading.utils.ReadUnzipHelper
import com.ws.chapter_list.data.models.ChapterResources
import com.ws.commons.extensions.byteToMB
import com.ws.commons.extensions.toDecoded
import com.ws.commons.interfaces.EncryptionKeysProvider
import com.ws.commons.interfaces.FlavorConfig
import com.ws.commons.interfaces.TokenProvider
import com.ws.commons.interfaces.UpdateNotificationManager
import com.ws.commons.models.NetworkConfig
import com.ws.core_ui.base.BaseFragmentWithListener
import com.ws.core_ui.extensions.collectLatestWithLifecycle
import com.ws.core_ui.extensions.createPath
import com.ws.core_ui.extensions.decrypt
import com.ws.core_ui.extensions.encrypt
import com.ws.core_ui.extensions.guessExtension
import com.ws.core_ui.extensions.guessFileName
import com.ws.core_ui.extensions.hideView
import com.ws.core_ui.extensions.readString
import com.ws.core_ui.extensions.showToast
import com.ws.core_ui.extensions.showView
import com.ws.core_ui.extensions.toDecode
import com.ws.core_ui.extensions.toEncodedBase64String
import com.ws.core_ui.extensions.visibility
import com.ws.core_ui.utils.TempConfig
import com.ws.database.room.entity.Chapter
import com.ws.database.room.entity.ReadData
import com.ws.database.room.entity.Resource
import com.ws.resources.R
import com.ws.resources.data.enums.ReadType
import com.ws.resources.data.interfaces.ReadingImageUpdateStatusProvider
import com.ws.resources.data.models.AnnotationsRequest
import com.ws.resources.data.models.ReadingFragConfig
import com.ws.resources.ui.changeColor
import com.ws.resources.ui.configure
import com.ws.resources.ui.configureForCaching
import com.ws.resources.ui.configureForSecurePdf
import com.ws.resources.ui.handleUrlLoading
import com.ws.resources.ui.reading.NotesBottomSheetFragment
import com.ws.resources.ui.reading.ReadingFragViewModel
import com.ws.resources.ui.reading.utils.Encryption
import com.ws.resources.ui.reading.utils.ReadJSInterface
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import org.jsoup.Jsoup
import org.koin.android.ext.android.inject
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream
import java.io.UnsupportedEncodingException
import com.wonderslate.prepjoy.databinding.LayoutReadingFragBinding


class ReadingFrag: BaseFragmentWithListener<LayoutReadingFragBinding, ReadingFrag.OnReadingFragInteractionListener>(), MCQAdapter.OnMCQActionListener {

    private var readingFragConfig: ReadingFragConfig? = null

    private lateinit var chapterList: ArrayList<ChapterResources>

    private var currentReadType = ReadType.UNSPECIFIED

    private val viewModel by inject<ReadingFragViewModel>()

    private val flavorConfig by inject<FlavorConfig>()

    private val tokenProvider by inject<TokenProvider>()

    private val updateNotificationManager by inject<UpdateNotificationManager>()

    private val encryptionKeysProvider by inject<EncryptionKeysProvider>()

    private val readingImageUpdateStatusManager by inject<ReadingImageUpdateStatusProvider>()

    private lateinit var resource: Resource

    private lateinit var chapter: Chapter

    private val networkConfig: NetworkConfig by inject()

    private val readUnzipHelper: ReadUnzipHelper by lazy {
        ReadUnzipHelper()
    }

    private var decryptedPdfFile: File? = null
    private var decryptedPdfFileUri: Uri? = null

    private var epubBase64String: String = ""

    private var isWebViewLoaded = false

    private var renderHighlightString = ""
    private var readNotesJson = JSONObject()

    private var inWebSearchMode = false
    private var inFlashCardMode = false
    private var webLink: String = ""
    private var flCardQuote: String = ""
    private var tempScreenshotFile: File? = null
    var imgData: String = ""
    private var imgUrl: String = ""
    private var cropImageLauncher: ActivityResultLauncher<CropImageContractOptions>? = null
    private val SNIPPED_IMAGE: String = ".CroppedImages"
    private lateinit var mcqAdapter: MCQAdapter
    private var language1MCQs: List<MCQ> = mutableListOf()
    private var language2MCQs: List<MCQ> = mutableListOf()
    private var formattedMCQLang1: List<String> = mutableListOf()
    private var formattedMCQLang2: List<String> = mutableListOf()
    private var lang1: String = ""
    private var lang2: String = ""
    private var mcqObject: JSONObject? = null
    private var selectedResOptionLang: String = ""
    private var quizId: Int? = null

    override fun inflateViewBinding(
            inflater: LayoutInflater,
            container: ViewGroup?
    ): com.wonderslate.prepjoy.databinding.LayoutReadingFragBinding {
        return LayoutReadingFragBinding.inflate(inflater, container, false)
    }

    override fun initArguments(bundle: Bundle?) {
        readingFragConfig = bundle?.getSerializable(ARG_READING_CONFIG) as? ReadingFragConfig
        chapterList = ((bundle?.getSerializable(ARG_CHAPTERS_LIST) as? ArrayList<ChapterResources>)!!)
    }

    override fun initView() {
        setupClicks()
        setupTextFormatterUI()
        initObservers()
        logReadingActivity()
    }

    private fun logReadingActivity() {

    }

    override fun load() {

        when (Build.VERSION.SDK_INT) {
            in 1..28 -> listener?.checkForStoragePermission()
            else -> {
                onStoragePermissionGranted()
            }
        }
    }

    private fun loadNewResource() {
        readingFragConfig?.let { config ->
            fragScope?.launch {
                var chap = viewModel.findCachedChapter(resource.topicId)

                if (chap == null && deepLinkChapter != null) {
                    chap = deepLinkChapter
                }

                if(chap == null) {
                    showToast(OPEN_ERROR_MSG)
                    listener?.onBackBtnClicked()
                    return@launch
                }

                // Init chapter
                chapter = chap

                if(tokenProvider.isLoggedIn() && !config.isPreviewMode) {
                    viewModel.getAnnotations(AnnotationsRequest(resource.id, chapter.bookId, resource.ebupChapterLink ?: ""))
                }

                updateReadType()

                configureWebViews()

                binding?.wvReading?.settings?.apply {
                    loadWithOverviewMode = resource.isEbook()
                    useWideViewPort = resource.isEbook()
                    setSupportZoom(true)
                }

                setupToolbar()

                binding?.lvCenter?.visibility = View.VISIBLE

                isWebViewLoaded = false

                getGPTResOptions()

            }
        }
    }

    fun onStoragePermissionGranted() {
        if (!readingFragConfig?.bookType.equals("bookgpt") && !readingFragConfig?.bookType.equals("ebookwithai")) {
            readingFragConfig?.let { config ->
                fragScope?.launch {
                    var res = viewModel.findCachedResource(config.resId)

                    if (res == null && deepLinkResource != null) {
                        res = deepLinkResource
                    }

                    if(res == null) {
                        showToast(OPEN_ERROR_MSG)
                        listener?.onBackBtnClicked()
                        return@launch
                    }

                    // Init resource
                    resource = res

                    var chap = viewModel.findCachedChapter(resource.topicId)

                    if (chap == null && deepLinkChapter != null) {
                        chap = deepLinkChapter
                    }

                    if(chap == null) {
                        showToast(OPEN_ERROR_MSG)
                        listener?.onBackBtnClicked()
                        return@launch
                    }

                    // Init chapter
                    chapter = chap

                    if(tokenProvider.isLoggedIn() && !config.isPreviewMode) {
                        viewModel.getAnnotations(AnnotationsRequest(resource.id, chapter.bookId, resource.ebupChapterLink ?: ""))
                    }

                    updateReadType()

                    configureWebViews()

                    binding?.wvReading?.settings?.apply {
                        loadWithOverviewMode = resource.isEbook()
                        useWideViewPort = resource.isEbook()
                        setSupportZoom(true)
                    }

                    setupToolbar()

                    binding?.lvCenter?.visibility = View.VISIBLE

                    startLoadingReading()

                }
            }
        }

    }

    private fun setupToolbar() {
        binding?.tvResourceName?.text = if(inWebSearchMode) "Web search" else resource.resName.toDecoded()
        binding?.btnTextFormatter?.hideView()
        if (!readingFragConfig?.bookType.equals("bookgpt") && !readingFragConfig?.bookType.equals("ebookwithai")) {
            binding?.btnNotes?.visibility(readingFragConfig?.isPreviewMode != true && !inWebSearchMode)
        }
        else {
            binding?.tvResourceName?.visibility = View.GONE
            binding?.btnTextFormatter?.visibility = View.GONE
            binding?.btnNotes?.visibility = View.GONE
            binding?.btnRefresh?.visibility = View.GONE
            //binding?.bookGptBtn?.visibility = View.VISIBLE
            binding?.genericResourceChooser?.visibility = View.VISIBLE
            //binding?.btnSnip?.visibility = View.VISIBLE
        }

    }

    private fun setupClicks() {
        binding?.btnBack?.setOnClickListener {
            listener?.onBackBtnClicked()
        }

        binding?.btnSnip?.setOnClickListener {
            captureScreenshotWithSecureFlag()
        }


        // Initialize cropImageLauncher
        cropImageLauncher = registerForActivityResult(CropImageContract()) { result ->
            if (result.isSuccessful) {
                // Use the cropped image URI
                val croppedImageUri: Uri? = result.uriContent
                if (croppedImageUri != null) {
                    saveCroppedImage(croppedImageUri)
                }
            } else {
                // Handle errors
                val error: Exception? = result.error
                error?.let {
                    it.printStackTrace()
                    Toast.makeText(requireContext(), it.message, Toast.LENGTH_SHORT).show()
                }
            }
        }

        if (!readingFragConfig?.bookType.equals("bookgpt") && !readingFragConfig?.bookType.equals("ebookwithai")) {
            binding?.btnNotes?.visibility = View.VISIBLE
        }
        else {
            binding?.bookGptBtn?.visibility = View.GONE
        }

        binding?.btnNotes?.setOnClickListener {
            listener?.onOpenNotesList(AnnotationsRequest(
                    resource.id,
                    chapter.bookId,
                    resource.ebupChapterLink ?: "",
                    forceUpdate = viewModel.isConnected() && currentReadType == ReadType.SECURE_PDF
            ))
        }

        binding?.btnTextFormatter?.setOnClickListener {}

        binding?.wvReading?.setOnLongClickListener {
            return@setOnLongClickListener readingFragConfig?.isPreviewMode == true
        }

        binding?.wvReadingPdf?.setOnLongClickListener {
            binding?.wvReadingPdf?.evaluateJavascript("checkConnection(" + viewModel.isConnected() + ")", null)
            return@setOnLongClickListener readingFragConfig?.isPreviewMode == true
        }

        binding?.layoutTextFormatter?.textSizeIncrease?.setOnClickListener {}

        binding?.layoutTextFormatter?.textSizeDecrease?.setOnClickListener {}

        binding?.layoutTextFormatter?.darkbackbtn?.setOnClickListener {
            binding?.layoutTextFormatter?.markBlack?.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorAccent))
            binding?.layoutTextFormatter?.markWhite?.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.light_grey))
        }

        binding?.layoutTextFormatter?.whitebackbtn?.setOnClickListener {
            binding?.layoutTextFormatter?.markBlack?.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.light_grey))
            binding?.layoutTextFormatter?.markWhite?.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.colorAccent))
        }

        if (!readingFragConfig?.bookType.equals("bookgpt") && !readingFragConfig?.bookType.equals("ebookwithai")) {
            binding?.bookGptBtn?.visibility = View.GONE
        }
        else {
            binding?.tvResourceName?.visibility = View.GONE
            binding?.btnTextFormatter?.visibility = View.GONE
            binding?.btnNotes?.visibility = View.GONE
            binding?.btnRefresh?.visibility = View.GONE
            //binding?.bookGptBtn?.visibility = View.VISIBLE
            binding?.genericResourceChooser?.visibility = View.VISIBLE
            //binding?.btnSnip?.visibility = View.VISIBLE
        }

        binding?.bookGptBtn?.setOnClickListener {
            listener?.onOpenGPTChat(resource, chapter, "", imgData, imgUrl)
            clearSnipData()
        }

        if (readingFragConfig?.bookType.equals("bookgpt") || readingFragConfig?.bookType.equals("ebookwithai")) {
            setupGenericReader()
        }
    }

    private fun captureScreenshotWithSecureFlag() {
        // Enable screenshots
        toggleScreenshots(true)

        // Add a short delay to ensure FLAG_SECURE is cleared before capturing
        Handler(Looper.getMainLooper()).postDelayed({
            // Perform the screenshot capture (this method should capture the full page or specific area)
            captureFullPageScreenshot()

            // Disable screenshots again after capturing
            Handler(Looper.getMainLooper()).postDelayed({ toggleScreenshots(false) }, 500)
        }, 500)
    }

    private fun captureFullPageScreenshot() {
        val webView = binding?.wvReadingPdf

        // Ensure WebView is not null
        if (webView == null) {
            Toast.makeText(requireContext(), "WebView not available", Toast.LENGTH_SHORT).show()
            return
        }

        // Measure WebView content size
        val webViewWidth = webView.width
        val webViewHeight = (webView.contentHeight * webView.scale).toInt()

        // Create bitmap for the full content
        val fullBitmap = Bitmap.createBitmap(webViewWidth, webViewHeight, Bitmap.Config.ARGB_8888)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // Get WebView position on the screen
            val location = IntArray(2)
            webView.getLocationOnScreen(location)
            val webViewX = location[0]
            val webViewY = location[1]

            // Define the Rect to exclude system bars
            val rect = Rect(
                webViewX,
                webViewY,
                webViewX + webViewWidth,
                webViewY + webView.height
            )

            try {
                PixelCopy.request(
                    requireActivity().window,
                    rect,
                    fullBitmap,
                    { result ->
                        if (result == PixelCopy.SUCCESS) {
                            // Save the bitmap temporarily or pass it to the crop tool
                            val tempFile = saveBitmapToTempFile(fullBitmap)
                            if (tempFile != null) {
                                launchCropTool(tempFile)
                            }
                        } else {
                            Toast.makeText(requireContext(), "Failed to capture full page screenshot.", Toast.LENGTH_SHORT).show()
                        }
                    },
                    Handler(Looper.getMainLooper())
                )
            } catch (e: Exception) {
                e.printStackTrace()
                Toast.makeText(requireContext(), "Error using PixelCopy", Toast.LENGTH_SHORT).show()
            }
        } else {
            Toast.makeText(requireContext(), "PixelCopy requires Android O or higher.", Toast.LENGTH_SHORT).show()
        }
    }


    private fun saveBitmapToTempFile(bitmap: Bitmap): File? {
        try {
            val storageDir = File(
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES),
                "Screenshots"
            )
            if (!storageDir.exists()) storageDir.mkdirs()

            tempScreenshotFile =
                File(storageDir, "full_page_screenshot_" + System.currentTimeMillis() + ".jpg")
            val fos = FileOutputStream(tempScreenshotFile)
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos)
            fos.flush()
            fos.close()

            return tempScreenshotFile
        } catch (e: IOException) {
            e.printStackTrace()
            Toast.makeText(context, "Failed to save screenshot", Toast.LENGTH_SHORT).show()
        }
        return null
    }

    private fun launchCropTool(imageFile: File) {
        val sourceUri = Uri.fromFile(imageFile)

        // Launch the crop image activity with the given options
        cropImageLauncher?.launch(
            CropImageContractOptions(
                sourceUri,
                getCropImageOptions()
            )
        )
    }

    private fun getCropImageOptions(): CropImageOptions {
        val options = CropImageOptions()
        // Enable guidelines
        options.guidelines = CropImageView.Guidelines.OFF

        // Enable zoom
        options.autoZoomEnabled = true

        // Customize the crop area
        options.borderLineColor = Color.RED
        options.borderLineThickness = 3F

        // Customize the toolbar
        options.activityMenuIconColor = Color.WHITE
        options.toolbarBackButtonColor = Color.WHITE
        options.cropMenuCropButtonIcon = R.drawable.okay

        // Output settings
        options.outputCompressFormat = Bitmap.CompressFormat.PNG

        // Disable camera and gallery sources
        options.imageSourceIncludeCamera = false
        options.imageSourceIncludeGallery = false

        // Disable rotation and flipping
        options.allowFlipping = false
        options.allowRotation = false
        options.allowCounterRotation = false

        return options
    }

    private fun toggleScreenshots(enable: Boolean) {
        if (enable) {
            // Enable screenshots by clearing FLAG_SECURE
            requireActivity().window.clearFlags(WindowManager.LayoutParams.FLAG_SECURE)
        } else {
            // Disable screenshots by setting FLAG_SECURE
            requireActivity().window.setFlags(
                WindowManager.LayoutParams.FLAG_SECURE,
                WindowManager.LayoutParams.FLAG_SECURE
            )
        }
    }

    private fun saveCroppedImage(croppedImageUri: Uri) {
        try {
            // Directory to save the cropped image
            val storageDir = File(requireContext().getExternalFilesDir(null)?.absolutePath + SNIPPED_IMAGE)
            if (!storageDir.exists()) storageDir.mkdirs()

            // File to store the cropped image
            val croppedFile = File(storageDir, "cropped_image_${System.currentTimeMillis()}.png")

            // Open input and output streams to copy data
            val inputStream: InputStream? = requireContext().contentResolver.openInputStream(croppedImageUri)
            val outputStream: OutputStream = FileOutputStream(croppedFile)
            val buffer = ByteArray(1024)
            var bytesRead: Int

            // Write the data to the cropped file
            while (inputStream?.read(buffer).also { bytesRead = it ?: -1 } != -1) {
                outputStream.write(buffer, 0, bytesRead)
            }

            inputStream?.close()
            outputStream.close()

            // Delete the full-page screenshot, if it exists
            tempScreenshotFile?.let {
                if (it.exists()) {
                    it.delete()
                }
            }

            // Update the image URL and log it
            imgUrl = Uri.fromFile(croppedFile).toString()

            // Convert the cropped file to Base64
            imgData = convertFileUriToBase64(Uri.fromFile(croppedFile)).toString()

            // Handle success or failure
            listener?.onOpenGPTChat(resource, chapter, "", imgData, imgUrl)
            clearSnipData()
        } catch (e: IOException) {
            e.printStackTrace()
            Toast.makeText(requireContext(), "Error saving cropped image", Toast.LENGTH_SHORT).show()
        }
    }


    private fun convertFileUriToBase64(fileUri: Uri): String? {
        try {
            // Open an InputStream from the URI
            val inputStream: InputStream = requireContext().contentResolver.openInputStream(fileUri)
                ?: return null // Handle null case

            // Read the InputStream into a byte array
            val outputStream = ByteArrayOutputStream()
            val buffer = ByteArray(1024)
            var bytesRead: Int
            while ((inputStream.read(buffer).also { bytesRead = it }) != -1) {
                outputStream.write(buffer, 0, bytesRead)
            }

            val byteArray = outputStream.toByteArray()

            // Encode the byte array into Base64
            val base64String = Base64.encodeToString(byteArray, Base64.NO_WRAP)

            inputStream.close()

            // Format as a Base64 data URL for an image
            return "data:image/png;base64,$base64String"
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            return null // Handle errors
        }
    }

    private fun setupGenericReader() {
        if (chapterList.isNotEmpty()) {
            val chapterNamesList: MutableList<String> = java.util.ArrayList()
            var previewChapterName = ""
            for (i in chapterList.indices) {
                chapterNamesList.add(chapterList[i].chapterName)
                if (chapterList[i].isPreview) {
                    previewChapterName = chapterList[i].chapterName
                }
            }

            val genericResourceSelectionAdapter = GenericResourceSelectionAdapter(
                context,
                chapterNamesList,
                binding?.genericResourceChooser,
                false,
                previewChapterName,
                this@ReadingFrag
            )

            genericResourceSelectionAdapter.onSpinnerItemSelectedListener =
                OnSpinnerItemSelectedListener<List<String>> { _, _, i1, t1 ->
                    val selectedChapterName = t1[0]
                    if (chapterList[i1].chapterName == selectedChapterName) {
                        resource = chapterList[i1].resources[0]
                        loadNewResource()
                    }
                }

            binding?.genericResourceChooser?.setSpinnerAdapter(genericResourceSelectionAdapter)
            binding?.genericResourceChooser?.selectItemByIndex(0)
        }
    }

    private fun initObservers() {
        viewModel.annotations.collectLatestWithLifecycle(this) {
            readNotesJson = it
            updateHighlightString()
            updateWebViewWithHighlights()
        }
    }
    fun showBuyBookMsg() {
        binding?.genericResourceChooser?.dismiss()
        Toast.makeText(context, "Please buy this book to read more", Toast.LENGTH_SHORT).show()
    }

    private fun getGPTResOptions() {
        BookGPT().getGPTResOptions(chapter.bookId, object : WSCallback {
            override fun onWSResultSuccess(jsonObject: JSONObject, responseCode: Int) {
                try {
                    configureContentViews(jsonObject)
                } catch (e: JSONException) {
                    e.printStackTrace()
                }
            }

            override fun onWSResultFailed(resString: String, responseCode: Int) {
                Log.d(TAG, "Response: $resString")
            }
        })

    }

    private fun configureContentViews(jsonObject: JSONObject) {
        if (jsonObject.optString("showPdf").equals("true", true)) {
            binding?.bookGptBtn?.visibility = View.VISIBLE
            binding?.btnSnip?.visibility = View.VISIBLE
            binding?.resourceLayout?.visibility = View.GONE

            CoroutineScope(Dispatchers.Main).launch {
                startLoadingReading()
            }
        }
        else if (jsonObject.optString("showMcq").equals("true", true)) {
            binding?.alertDialogLayout?.hideView()
            binding?.resourceLayout?.visibility = View.VISIBLE
            BookGPT().getTest(resource.id, "", "", object : WSCallback {
                override fun onWSResultSuccess(jsonObject: JSONObject, responseCode: Int) {
                    binding?.lvCenter?.visibility = View.GONE
                    try {
                        // Setup MCQ Data
                        binding?.recyclerMcqs?.layoutManager = LinearLayoutManager(requireContext())

                        val languagesObj = jsonObject.getJSONObject("mcqLanguages")

                        lang1 = languagesObj.getString("language1")
                        lang2 = languagesObj.getString("language2")
                        parseAndCreateMCQJsonObject(jsonObject)

                        val parsedMCQs = MCQLanguageParser.parseLanguageMCQs(jsonObject.toString(), lang1, lang2)
                        language1MCQs = parsedMCQs.language1MCQs
                        language2MCQs = parsedMCQs.language2MCQs

                        // Load Language1 by default
                        if ((language1MCQs as MutableList<MCQ>?)!!.isNotEmpty()) {

                            formattedMCQLang1 = MCQFormatter.formatMCQResource(language1MCQs)
                            formattedMCQLang2 = MCQFormatter.formatMCQResource(language2MCQs)

                            binding?.resourceTitle?.text = "Multiple Choice Question (${language1MCQs.size})"
                            // Initialize RecyclerView
                            mcqAdapter = MCQAdapter(requireContext(), formattedMCQLang1, this@ReadingFrag)
                            binding?.recyclerMcqs?.adapter = mcqAdapter
                        } else {
                            Toast.makeText(requireContext(), "No MCQs found!", Toast.LENGTH_SHORT).show()
                        }

                        // Set up Spinner
                        val spinnerAdapter = ArrayAdapter(requireContext(), R.layout.spinner_item, listOf(lang1, lang2))
                        spinnerAdapter.setDropDownViewResource(R.layout.spinner_dropdown_item)
                        binding?.spinnerLanguage?.adapter = spinnerAdapter

                        binding?.spinnerLanguage?.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
                            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                                selectedResOptionLang = if (position == 0) lang1 else lang2
                                mcqAdapter.updateList(if (position == 0) formattedMCQLang1 else formattedMCQLang2)
                            }

                            override fun onNothingSelected(parent: AdapterView<*>?) {}
                        }

                        binding?.mcqOptions?.visibility = View.VISIBLE
                        binding?.mcqPlayTxt?.setOnClickListener {
                            mcqObject = replaceKeysInJson(mcqObject!!)
                            handleMCQ(
                                mcqObject!!,
                                resource.id,
                                "play"
                            )

                        }
                        binding?.mcqPracticeTxt?.setOnClickListener {
                            mcqObject = replaceKeysInJson(mcqObject!!)
                            handleMCQ(
                                mcqObject!!,
                                resource.id,
                                "practice"
                            )

                        }
                        binding?.mcqTestTxt?.setOnClickListener {
                            mcqObject = replaceKeysInJson(mcqObject!!)
                            handleMCQ(
                                mcqObject!!,
                                resource.id,
                                "test"
                            )

                        }

                        if (lang1.isEmpty() || lang2.isEmpty()) {
                            binding?.spinnerLanguage?.visibility = View.GONE
                            selectedResOptionLang = lang1.ifEmpty { lang2 }
                        }
                        else {
                            binding?.spinnerLanguage?.visibility = View.VISIBLE
                        }
                        binding?.mcqBtnsLayout?.visibility = View.VISIBLE

                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }

                override fun onWSResultFailed(resString: String, responseCode: Int) {
                    binding?.lvCenter?.visibility = View.GONE
                    Log.d(TAG, "Response: $resString")
                }
            })

        }
        else if (jsonObject.optString("showSnapshot").equals("true", true)) {

        }
        else if (jsonObject.optString("showQa").equals("true", true)) {

        }
        else if (jsonObject.optString("showNotification").equals("yes", true)) {
            binding?.alertDialogLayout?.hideView()
            binding?.bookGptBtn?.visibility = View.VISIBLE
            binding?.btnSnip?.visibility = View.VISIBLE
            binding?.resourceLayout?.visibility = View.GONE

            CoroutineScope(Dispatchers.Main).launch {
                startLoadingReading()
            }
        }
        else {
            binding?.alertDialogLayout?.hideView()
            binding?.bookGptBtn?.visibility = View.VISIBLE
            binding?.btnSnip?.visibility = View.VISIBLE
            binding?.resourceLayout?.visibility = View.GONE

            CoroutineScope(Dispatchers.Main).launch {
                startLoadingReading()
            }
        }
    }

    private fun parseAndCreateMCQJsonObject(jsonObject: JSONObject) {
        try {
            val mcqArray = JSONArray(jsonObject.optString("mcqs"))
            mcqObject = JSONObject()
            mcqObject?.put("results", mcqArray)
            mcqObject?.put("status", jsonObject.optString("status"))
            mcqObject?.put("isPassage", jsonObject.optString("isPassage"))
            mcqObject?.put("passage", jsonObject.optString("passage"))
            mcqObject?.put("description", jsonObject.optString("description"))
            mcqObject?.put("resourceName", jsonObject.optString("resourceName"))
            mcqObject?.put("chaptersList", jsonObject.optString("chaptersList"))
            mcqObject?.put("language1", lang1)
            mcqObject?.put("language2", lang2)
            mcqObject?.put("examSubject", jsonObject.optString("examSubject"))
            mcqObject?.put("examMstId", jsonObject.optString("examMst"))
            mcqObject?.put("examDtl", jsonObject.optString("examDtl"))
            mcqObject?.put("testSeries", jsonObject.optString("testSeries"))
            mcqObject?.put("quizRecorder", jsonObject.optString("quizRecorder"))
            mcqObject?.put("testEndDate", jsonObject.optString("testEndDate"))
            mcqObject?.put("chapterName", jsonObject.optString("chapterName"))
            mcqObject?.put("challengerName", jsonObject.optString("challengerName"))
            mcqObject?.put(
                "challengerPlace",
                jsonObject.optString("challengerPlace")
            )
            mcqObject?.put("resId", resource.id)
            mcqObject?.put("bookgpt", true)
            mcqObject?.put("gptTestSeries", true)
            mcqObject?.put("quizId", resource.id)
        } catch (e: JSONException) {
            e.printStackTrace()
            mcqObject = JSONObject()
        }
    }

    private fun handleMCQ(
        jsonData: JSONObject,
        resId: String,
        quizMode: String
    ) {
        if (quizMode.equals("practice", ignoreCase = true) || quizMode.equals("test", ignoreCase = true)) {
            val intent = Intent(requireActivity(), QuizActivity::class.java).apply {
                putExtra("quizId", resId)
                putExtra("resId", resId)
                putExtra("previous", "yes")
                putExtra("next", "yes")
                putExtra("isFromShop", "disabled")
                putExtra("selectedFormat", if (quizMode.equals("practice", ignoreCase = true)) "practice" else "testSeries")
                putExtra("realDailyTestDtlId", "")
                putExtra("isGPTQuiz", true)
                putExtra("language", selectedResOptionLang)
                putExtra("sound", false)
                putExtra("timeValue", (15 * 1000).toString()) // 15 seconds timeout
                putExtra("darkMode", false)
                putExtra("quizData", mcqObject?.optString("results"))
                putExtra("language1", lang1)
                putExtra("language2", lang2)
                putExtra("resourceName", jsonData.optString("resourceName"))
            }
            startActivity(intent)
        } else {
            navigateToPlay(jsonData)
        }
    }

    fun replaceKeysInJson(jsonObject: JSONObject): JSONObject {
        val jsonString = jsonObject.toString()

        // Replace "question" with "ps"
        var modifiedJsonString = jsonString.replace("\"question\"", "\"ps\"")

        // Replace "option1", "option2", etc., with "op1", "op2", etc.
        modifiedJsonString = modifiedJsonString.replace(Regex("\"option(\\d+)\""), "\"op$1\"")

        // Replace "answer1", "answer2", etc., with "ans1", "ans2", etc.
        modifiedJsonString = modifiedJsonString.replace(Regex("\"answer(\\d+)\""), "\"ans$1\"")

        return JSONObject(modifiedJsonString)
    }


    private fun navigateToPlay(jsonData: JSONObject?) {
        val intent = Intent(requireActivity(), QuizActivity::class.java).apply {
            putExtra("language", selectedResOptionLang)
            putExtra("sound", false)
            putExtra("timeValue", (15 * 1000).toString()) // 15 seconds timeout
            putExtra("darkMode", false)

            jsonData?.let {
                try {
                    it.put("resourceName", it.optString("resourceName"))
                } catch (e: JSONException) {
                    e.printStackTrace()
                }
                putExtra("quizData", mcqObject?.optString("results"))
                putExtra("selectedDate", it.optString("date"))
                putExtra("challengerName", it.optString("challengerName"))
                putExtra("challengerPlace", it.optString("challengerPlace"))
                putExtra("realDailyTestDtlId", it.optString("realDailyTestDtlId"))
                putExtra("language1", it.optString("language1"))
                putExtra("language2", it.optString("language2"))
                putExtra("resourceName", it.optString("resourceName"))
                putExtra("testMode", it.optString("testMode"))
                putExtra("previous", it.optString("previous"))
                putExtra("next", it.optString("next"))
                putExtra("resId", it.optString("resId"))
                putExtra("learn", it.optString("learn").isNotEmpty() && it.optString("learn") != "null")
                putExtra("start", it.optString("start"))
                putExtra("end", it.optString("end"))
                putExtra("isFromShop", true)
                putExtra("isGPTQuiz", true)
                putExtra("selectedFormat", "")
            }

            val userId = with(Wonderslate.getInstance().sharedPrefs) {
                if (!usermobile.isNullOrEmpty()) {
                    "${Wonderslate.getInstance().siteID}_$usermobile"
                } else {
                    "${Wonderslate.getInstance().siteID}_$useremail"
                }
            }
            putExtra("userId", userId)
            putExtra("siteId", Wonderslate.getInstance().siteID)
            putExtra("baseUrl", Wonderslate.SERVICE)
            putExtra("xauth", Wonderslate.getInstance().sharedPrefs.accessToken)
        }
        startActivity(intent)
    }



    @SuppressLint("ClickableViewAccessibility")
    private fun configureWebViews() {

        binding?.wvReadingPdf?.visibility(isSecurePdfOrEPub())
        binding?.wvReading?.visibility(!isSecurePdfOrEPub())

        binding?.wvReading?.apply {
            configure(
                    enableZoomControls = currentReadType != ReadType.E_PUB,
                    isDebugMode = flavorConfig.isDebugMode
            ) {
                if (it == 100 && !isWebViewLoaded) {
                    isWebViewLoaded = true
                    binding?.lvCenter?.hide()
                }
            }
            configureForCaching(activity = requireActivity(), isConnected = viewModel.isConnected())
            changeColor(ContextCompat.getColor(requireContext(), R.color.white))

            val readInterface = createReadJSInterface(this)
            readInterface.connectInterface()

            webViewClient = object: WebViewClient() {
                override fun onPageFinished(view: WebView?, url: String?) {
                    configure(
                            enableZoomControls = currentReadType != ReadType.E_PUB,
                            isDebugMode = flavorConfig.isDebugMode
                    )
                }
            }
        }

        binding?.wvReadingPdf?.apply {
            configureForSecurePdf {
                if (it == 100 && !isWebViewLoaded) {
                    try {
                        when(currentReadType) {
                            ReadType.E_PUB -> {
                                val chapterLinkData = resource.ebupChapterLink?.split("#")?.get(0) ?: ""
                                if (WonderPubSharedPrefs.getInstance(context).isBookInLibrary == true) {
                                    evaluateJavascript("displayEpub('$epubBase64String', 0, \"English\", '$renderHighlightString', '$chapterLinkData', 'false')", null)
                                }
                                else {
                                    evaluateJavascript("displayEpub('$epubBase64String', 0, \"English\", '$renderHighlightString', '$chapterLinkData', 'true')", null)
                                }
                            }

                            ReadType.SECURE_PDF -> {
                                val pageValue: Int = getLastReadPDFPage(resource.id)
                                evaluateJavascript(
                                        "displayPdfWithHighlight('$decryptedPdfFileUri','$readNotesJson','$pageValue')",
                                        null
                                )
                                evaluateJavascript("checkConnection(" + viewModel.isConnected() + ")", null)
                            }
                            else -> {}
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "configureWebViews: ", e)
                    }
                    isWebViewLoaded = true
                    binding?.lvCenter?.hide()
                }
            }

            val readInterface = createReadJSInterface(this)
            readInterface.connectInterface()

            handleUrlLoading()
        }

        binding?.wvSearch?.apply {
            configure(
                    enableZoomControls = true,
                    isDebugMode = flavorConfig.isDebugMode
            ) {
                if(it == 100) {
                    binding?.lvCenter?.hide()
                }
            }

            configureForCaching(requireActivity(), viewModel.isConnected())

            changeColor(ContextCompat.getColor(requireContext(), R.color.white))

            val readInterface = createReadJSInterface(this)
            readInterface.connectInterface()

            handleUrlLoading()
        }

        binding?.wvFlashCard?.apply {
            configure(
                    enableZoomControls = true,
                    isDebugMode = flavorConfig.isDebugMode
            ) {
                if(it == 100) {
                    evaluateJavascript("createNewSet('$flCardQuote')", null)
                    binding?.lvCenter?.hide()
                }
            }

            configureForCaching(requireActivity(), viewModel.isConnected())

            changeColor(ContextCompat.getColor(requireContext(), R.color.white))

            val readInterface = createReadJSInterface(this)
            readInterface.connectInterface()

            handleUrlLoading()
        }

    }

    private fun getLastReadPDFPage(urlId: String): Int {
        val lastReadPageMap = stringToHashMap(WonderPubSharedPrefs.getInstance(context).sharedPrefsLastReadPage)
        return if (lastReadPageMap.isEmpty()) {
            0
        } else {
            try {
                lastReadPageMap[resource.id]!!
            } catch (e: java.lang.Exception) {
                0
            }
        }
    }

    fun stringToHashMap(str: String): java.util.HashMap<String, Int> {
        val hashMap = java.util.HashMap<String, Int>()
        if (!str.isEmpty()) {
            // Split the input string by a delimiter (e.g., comma)
            val keyValuePairs = str.split(",".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
            for (pair in keyValuePairs) {
                val keyValue = pair.split(":".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                if (keyValue.size == 2) {
                    val key = keyValue[0].trim { it <= ' ' }
                    val value = keyValue[1].trim { it <= ' ' }.toInt()
                    hashMap[key] = value
                }
            }
        }
        return hashMap
    }

    fun hashMapToString(hashMap: HashMap<String, Int>): String? {
        val sb = java.lang.StringBuilder()
        if (!hashMap.isEmpty()) {
            for (key in hashMap.keys) {
                val value = hashMap[key]!!
                sb.append(key).append(":").append(value).append(",")
            }

            // Remove the trailing comma
            if (sb.length > 0) {
                sb.deleteCharAt(sb.length - 1)
            }
        }
        return sb.toString()
    }


    private fun createReadJSInterface(webView: WebView?): ReadJSInterface {
        return ReadJSInterface(webView, object: ReadJSInterface.ReadingCallbacks{
            override fun onAnnotationCall(annotationString: String?, action: String?) {
                var jsonObject: JSONObject? = null
                if (annotationString?.isNotEmpty() == true) try {
                    if (currentReadType == ReadType.E_PUB) {
                        jsonObject = JSONObject(annotationString)
                        val dataObject: JSONObject = jsonObject
                        //{"type":"POST","dataType":"json","data":"{\"quote\":\"founded\",\"ranges\":[{\"start\":\"\/p[3]\",\"startOffset\":9,\"end\":\"\/p[3]\",
                        // \"endOffset\":16}],\"uri\":\"0\",\"bookId\":\"${params.bookId}\"}","contentType":"application\/json; charset=utf-8"}
                        if (dataObject.has("quote") && dataObject.getString("quote").isNotEmpty()) {
                            val localId = viewModel.getAnnotationLocalId(dataObject.getString("ranges"))
                            if (localId.isNotEmpty()) {
                                jsonObject.put("id", localId)
                            }
                            handleAnnotation(jsonObject, action ?: "")
                        }
                    } else {
                        jsonObject = JSONObject(annotationString)
                        if (jsonObject.has("quote") && jsonObject.getString("quote").isNotEmpty()) {
                            val localId = viewModel.getAnnotationLocalId(jsonObject.getString("ranges"))
                            if (localId.isNotEmpty()) {
                                jsonObject.put("id", localId)
                            }

                            handleAnnotation(jsonObject, action ?: "")
                        }
                    }
                } catch (e: JSONException) {
                    Log.e(TAG, "onAnnotationCall: ", e)
                }
            }

            override fun onScrolledBottom() {
                //TODO("Not yet implemented")
            }

            override fun onWebSearchTap(query: String?) {
                (if (query?.isNotEmpty() == true) query else "").let {
                    listener?.onOpenGPTChat(resource, chapter,
                        it,
                        imgData,
                        imgUrl
                    )
                    clearSnipData()
                }
                //search(query)
            }

            override fun onAnnotationTap(state: Boolean) {
                //TODO("Not yet implemented")
                Log.e(TAG, "onAnnotationCall: state - $state")
            }

            override fun onScrollDown() {
                activity?.runOnUiThread{
                    Handler(Looper.getMainLooper()).postDelayed({
                        if (resources.configuration.orientation == ORIENTATION_LANDSCAPE) {
                            binding?.clHeader?.visibility = View.VISIBLE
                        }
                    }, 1000)
                }
            }

            override fun onScrollUp() {
                activity?.runOnUiThread{
                    Handler(Looper.getMainLooper()).postDelayed({
                        if (resources.configuration.orientation == ORIENTATION_LANDSCAPE) {
                            binding?.clHeader?.visibility = View.GONE
                        }
                    }, 1000)
                }
            }

            override fun savePDFPage(value: Int) {
                //Store pdf page in shared prefs
                val lastReadPageMap: HashMap<String, Int> = stringToHashMap(WonderPubSharedPrefs.getInstance(context).sharedPrefsLastReadPage)
                lastReadPageMap[resource.id] = value
                WonderPubSharedPrefs.getInstance(context).sharedPrefsLastReadPage = hashMapToString(lastReadPageMap)
            }

            override fun onFlashcardTap(quote: String) {
                activity?.runOnUiThread {
                    Handler(Looper.getMainLooper()).post {
                        flCardQuote = quote
                        openFlashCardWebView("resources/createFlashCards")
                    }
                }
            }

            override fun onFlashCardAdded() {
                activity?.runOnUiThread {
                    Handler(Looper.getMainLooper()).post {
                        onBackPressed()
                    }
                }
            }

        })
    }

    fun clearSnipData() {
        imgData = ""
        imgUrl = ""
    }

    fun openFlashCardWebView(url: String) {
        binding?.wvFlashCard?.let {
            inFlashCardMode = true

            binding?.lvCenter?.show()

            webLink = Wonderslate.SERVICE + url + "?tokenId=" + WonderPubSharedPrefs.getInstance(context).accessToken + "&appType=android&" +
                    "chapterId=" + resource.topicId + "&prepjoyApp=true" + "&siteId=" + Wonderslate.getInstance().siteID

            setupToolbar()

            it.showView()
            it.loadUrl(webLink)
            binding?.wvReading?.hideView()
            binding?.wvReadingPdf?.hideView()
            binding?.wvSearch?.hideView()
            it.settings.mixedContentMode = WebSettings.MIXED_CONTENT_COMPATIBILITY_MODE
        }

    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        if (newConfig.orientation == ORIENTATION_PORTRAIT) {
            binding?.clHeader?.visibility = View.VISIBLE
        }
    }

    private fun handleAnnotation(annotateJson: JSONObject, action: String) {
        fragScope?.launch {
            when(action) {
                "notesNew", "notesEdit" -> {
                    //{"quote":"Around","ranges":[{"start":"\/h2[1]","startOffset":15,"end":"\/h2[1]","endOffset":21}]}
                    val listener: NotesBottomSheetFragment.OnNoteEditListener = object : NotesBottomSheetFragment.OnNoteEditListener {
                        override fun onNew(note: String) {
                            try {
                                annotateJson.put("text", note)
                                handleAnnotation(annotateJson, "create")
                            } catch (e: JSONException) {
                                Log.e(TAG, "handleAnnotation: ", e)
                            }
                        }

                        override fun onUpdate(note: String) {
                            try {
                                annotateJson.put("text", note)
                                handleAnnotation(annotateJson, "update")
                            } catch (e: JSONException) {
                                Log.e(TAG, "handleAnnotation: ", e)
                            }
                        }

                        override fun onDismiss() {
                            updateWebViewWithHighlights()
                        }
                    }

                    NotesBottomSheetFragment.showWithAnnotation(
                            fManager = childFragmentManager,
                            selected = annotateJson,
                            listener = listener
                    )
                }

                "create" -> {
                    if(viewModel.isConnected()) {
                        binding?.lvCenter?.visibility = View.VISIBLE
                        val isSuccess = viewModel.createAnnotation(AnnotationsRequest(
                                resId = resource.id,
                                bookId = chapter.bookId,
                                epubChapterLink = resource.ebupChapterLink ?: "",
                                annotationJsonString = annotateJson.toString()
                        ))
                        if(isSuccess) {
                            showToast(ANNOTATION_UPDATE_SUCCESS)
                        } else {
                            showToast(PROBLEM_UPDATING_ANNOTATION)
                        }
                        binding?.lvCenter?.visibility = View.GONE
                    } else {
                        showToast("Device need to be online for highlight and notes feature")
                    }
                }

                "update" -> {
                    if(viewModel.isConnected()) {
                        binding?.lvCenter?.visibility = View.VISIBLE
                        val isSuccess = viewModel.updateAnnotation(AnnotationsRequest(
                                resId = resource.id,
                                bookId = chapter.bookId,
                                epubChapterLink = resource.ebupChapterLink ?: "",
                                annotationJsonString = annotateJson.toString()
                        ))
                        if(isSuccess) {
                            showToast(ANNOTATION_UPDATE_SUCCESS)
                        } else {
                            showToast(PROBLEM_UPDATING_ANNOTATION)
                        }
                        binding?.lvCenter?.visibility = View.GONE
                    } else {
                        showToast("Device need to be online for highlight and notes feature")
                    }
                }

                "pdfTextUpdate" -> {
                    if(viewModel.isConnected()) {
                        binding?.lvCenter?.visibility = View.VISIBLE
                        val isSuccess = viewModel.updateAnnotation(AnnotationsRequest(
                                resId = resource.id,
                                bookId = chapter.bookId,
                                epubChapterLink = resource.ebupChapterLink ?: "",
                                annotationJsonString = annotateJson.toString()
                        ))
                        if(isSuccess) {
                            showToast(ANNOTATION_UPDATE_SUCCESS)
                        } else {
                            showToast(PROBLEM_UPDATING_ANNOTATION)
                        }
                        binding?.lvCenter?.visibility = View.GONE
                    } else {
                        showToast("Device need to be online for highlight and notes feature")
                    }
                }

                "delete" -> {
                    if(!annotateJson.has("id"))
                        return@launch

                    if(viewModel.isConnected()) {
                        binding?.lvCenter?.visibility = View.VISIBLE
                        val isSuccess = viewModel.deleteAnnotation(AnnotationsRequest(
                                resId = resource.id,
                                bookId = chapter.bookId,
                                epubChapterLink = resource.ebupChapterLink ?: "",
                                annotationJsonString = annotateJson.toString()
                        ))
                        if(isSuccess) {
                            showToast(ANNOTATION_UPDATE_SUCCESS)
                        } else {
                            showToast(PROBLEM_UPDATING_ANNOTATION)
                        }
                        binding?.lvCenter?.visibility = View.GONE
                    } else {
                        showToast("Device need to be online for highlight and notes feature")
                    }
                }
            }
        }
    }

    private fun search(query: String?) = fragScope?.launch(Dispatchers.Main) {
        binding?.wvSearch?.let {
            if(it.isVisible)
                return@launch

            inWebSearchMode = true

            setupToolbar()

            it.showView()
            binding?.wvReading?.hideView()
            binding?.wvReadingPdf?.hideView()
            it.settings.mixedContentMode = WebSettings.MIXED_CONTENT_COMPATIBILITY_MODE
            it.loadUrl("https://www.google.com/search?q=$query")
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setupTextFormatterUI() {

        binding?.layoutTextFormatter?.hideView()

        binding?.layoutTextFormatter?.let { formatter ->

            formatter.brightnessSeekBar.apply {
                max = 100
                try {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        setProgress(
                                Settings.System.getInt(
                                        requireActivity().contentResolver,
                                        Settings.System.SCREEN_BRIGHTNESS
                                ), true
                        )
                    } else {
                        progress = Settings.System.getInt(
                                requireActivity().contentResolver,
                                Settings.System.SCREEN_BRIGHTNESS
                        )
                    }
                } catch (e: Settings.SettingNotFoundException) {
                    Log.e(TAG, "setupTextFormatterUI: ", e)
                }
                jumpDrawablesToCurrentState()
                incrementProgressBy(5)

                setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
                    override fun onProgressChanged(seekBar: SeekBar, progress: Int, b: Boolean) {
                        val backLightValue = progress.toFloat() / 100
                        val layoutParams: WindowManager.LayoutParams = requireActivity().window.attributes
                        layoutParams.screenBrightness = backLightValue
                        requireActivity().window.attributes = layoutParams
                    }

                    override fun onStartTrackingTouch(seekBar: SeekBar) {}
                    override fun onStopTrackingTouch(seekBar: SeekBar) {}
                })
            }
        }

        binding?.wvReading?.setOnTouchListener { view, motionEvent ->
            //TODO("Use to hide formatting UI and help detect if user interaction")
            when(motionEvent.action) {
                MotionEvent.ACTION_MOVE -> {
                    binding?.layoutTextFormatter?.hideView()
                }
            }
            return@setOnTouchListener false
        }
    }

    private suspend fun startLoadingReading() {
        val isReadUpdated = updateNotificationManager.isResourceUpdated(resource.id)
        when(currentReadType) {

            ReadType.ZIPPED_PDF -> {
                // Start downloading reading zip or load from cached if available
                loadZippedPdf(isReadUpdated)
            }

            ReadType.E_PUB -> {
                // Start downloading epub or load from cached if available
                loadEpub(isReadUpdated)
            }

            ReadType.SECURE_PDF, ReadType.NON_SECURE_PDF -> {
                // NON_SECURE_PDF: If cached PDF is available then load or
                // SECURE_PDF: Start downloading and load or load from cached
                loadPdf(isReadUpdated)
            }

            else -> {
                showToast(OPEN_ERROR_MSG)
                listener?.onBackBtnClicked()
            }
        }
    }

    /**
     *  Downloads, process, cache and load reading zip
     *
     *  @param isUpdated if true zip will be re-downloaded
     */
    private suspend fun loadZippedPdf(isUpdated: Boolean = false) {
        val downloadUrl = viewModel.getZipDownloadUrl(
                isLoggedIn = tokenProvider.isLoggedIn(),
                isPreviewMode = readingFragConfig?.isPreviewMode ?: true,
                resource = resource
        )

        val resLink = resource.resLink.replace(" ", "").trim().toDecoded()
        val fileName = resLink.guessFileName()

        val path = "${resource.id}_${resLink}"

        val unZipStoragePath = createPath(UNZIPPED_EPUB_FILES, path)
        val unZippedFile = File(unZipStoragePath)

        if(isUpdated) {
            if(unZippedFile.exists())
                unZippedFile.deleteRecursively()
            readingImageUpdateStatusManager.setSVGUpdated(resource.id, false)
            readingImageUpdateStatusManager.setImageReplaced(resource.id, false)
            viewModel.deleteReadData(resource.id)
            updateNotificationManager.onResourceUpdated(resource.id)
        }

        val zipStoragePath = createPath(DOWNLOADED_FILES, path)
        val zippedFile = File(zipStoragePath)

        val cachedData: ReadData? = viewModel.getCachedReadData(resource.id)

        fun show(htmlContent: String) {
            binding?.wvReading?.loadDataWithBaseURL("file:///android_asset/fonts/", htmlContent, "text/html",
                    "UTF-8", "about:blank")
        }

        fun extractReadZipAndLoad() = fragScope?.launch {
            val extractData = readUnzipHelper.unzip(zipStoragePath, unZipStoragePath)
            val htmlContent = withContext(Dispatchers.IO) {
                var html = File(extractData.htmlFilePath).readString()
                html = html.replaceImageExtension(extractData.imageLocation)
                html = html.removeExtraCharacters()
                html = html.replaceImagePaths(extractData.imageLocation)
                html = html.replaceFontPaths(extractData.fontsLocation)
                html = html.replaceStylesPaths(extractData.stylesLocations)
                html = html.replaceTable()
                html
            }

            binding?.alertDialogLayout?.hideView()

            // start loading
            show(htmlContent.offlineKatexConfig(renderHighlightString).replaceHeader())

            // encrypt htmlContent then store
            val encryptedHtmlContent = htmlContent.encryptOrEmpty()
            viewModel.cacheReadData(ReadData(resource.id, chapter.bookId, encryptedHtmlContent))

            try {
                // Delete extra files after extraction process
                withContext(Dispatchers.IO) {
                    zippedFile.deleteRecursively()
                    File(extractData.htmlFilePath).delete()
                }
            } catch (e: Exception) {
                Log.e(TAG, "extractReadZipAndLoad: ", e)
            }
        }

        when {

            cachedData != null && !TextUtils.isEmpty(cachedData.data) -> {
                val decryptedHtmlContent = cachedData.data.decryptOrEmpty()
                show(decryptedHtmlContent.offlineKatexConfig(renderHighlightString).replaceHeader())
            }

            zippedFile.exists() -> {
                extractReadZipAndLoad()
            }
            else -> {
                if(unZippedFile.exists())
                    unZippedFile.deleteRecursively()

                download(downloadUrl, zipStoragePath.replace(fileName, ""), fileName) {
                    // Download completed so start extraction process
                    if (zippedFile.exists()) {
                        extractReadZipAndLoad()
                    }
                    else {
                        Toast.makeText(context, "Problem while getting reading material. Please try again later.", Toast.LENGTH_SHORT).show()
                        binding?.alertDialogLayout?.hideView()
                    }
                }
            }
        }

        binding?.lvCenter?.hide()

    }

    /**
     *  Downloads, process, cache and load PDFs (Secure/Non secure)
     *
     *  @param isUpdated if true pdf will be re-downloaded
     */
    private fun loadPdf(isUpdated: Boolean) {
        val downloadUrl = viewModel.getPDFDownloadUrl(resource.id)

        val resLink = resource.resLink.replace(" ", "").trim().toDecoded()
        val fileName = resLink.guessFileName()

        val path = "${resource.id}_${resLink}"

        val encryptedPdfPath = createPath(UNZIPPED_EPUB_FILES, path)
        val encryptedPdf = File(encryptedPdfPath)

        if(isUpdated && encryptedPdf.exists()) {
            encryptedPdf.delete()
            updateNotificationManager.onResourceUpdated(resource.id)
        }

        val pdfDownloadPath = createPath(DOWNLOADED_FILES, path)
        val pdfFile = File(pdfDownloadPath)

        fun getDecryptedFileUri(): Uri? {
            val decryptedFileName: String = StringBuilder(fileName).insert(fileName.length - 4, "_dcy").toString()
            decryptedPdfFile = File(encryptedPdfPath.replace(fileName, decryptedFileName))

            if (!decryptedPdfFile!!.exists()) {
                Log.e("ReadingFrag", "Decrypted file does ot exist")
            }

            // Decrypt the `encryptedPdf` and write to `decryptedPdfFile`
            encryptedPdf.decrypt(encryptionKeysProvider.getReadPDFEncryptionKey(), decryptedPdfFile!!)

            return FileProvider.getUriForFile(requireContext(), requireActivity().application.packageName + ".provider", decryptedPdfFile!!)
        }

        fun show() {
            decryptedPdfFileUri = getDecryptedFileUri()

            if(currentReadType == ReadType.SECURE_PDF) {
                if (!readingFragConfig?.bookType.equals("bookgpt") && !readingFragConfig?.bookType.equals("ebookwithai")) {
                    binding?.wvReadingPdf?.loadUrl("file:///android_asset/pdf_library/web/viewer.html?bookLang=${TempConfig.isEnglishBook}&bookType=ebook")
                }
                else {
                    binding?.wvReadingPdf?.loadUrl("file:///android_asset/pdf_library/web/viewer.html?bookLang=${TempConfig.isEnglishBook}&bookType=gpt")
                }
            } else {
                // open out of app
                try {
                    val type = MimeTypeMap.getSingleton().getMimeTypeFromExtension(resLink.guessExtension(fallback = "pdf"))
                    val intent = Intent()
                    intent.action = Intent.ACTION_VIEW
                    intent.setDataAndType(decryptedPdfFileUri, type)
                    intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    startActivity(intent)
                    listener?.onBackBtnClicked()
                } catch (e: ActivityNotFoundException) {
                    showToast("Please install a PDF Reader before opening this reading material")
                    listener?.onBackBtnClicked()
                }
            }
        }

        fun encryptPdfAndLoad() = fragScope?.launch {
            // encrypt file and delete original file
            pdfFile.encrypt(encryptionKeysProvider.getReadPDFEncryptionKey(), encryptedPdf)
            pdfFile.delete()

            // Load the Pdf
            show()

            binding?.alertDialogLayout?.hideView()
        }

        when {
            encryptedPdf.exists() -> {
                show()
            }

            else -> {
                download(downloadUrl, pdfDownloadPath.replace(fileName, ""), fileName) {
                    // Download completed so start encryption process
                    encryptPdfAndLoad()
                }
            }
        }
    }

    private fun clearWebView() {
        if (binding?.wvReadingPdf != null) {
            binding?.wvReadingPdf?.tag = null
            binding?.wvReadingPdf?.clearHistory()
            binding?.wvReadingPdf?.removeAllViews()
            binding?.wvReadingPdf?.loadUrl("about:blank")
            binding?.wvReadingPdf?.clearFormData()
        }
    }

    /**
     *  Downloads, process, cache and load EPUB
     *
     *  @param isUpdated if true epub will be re-downloaded
     */
    private suspend fun loadEpub(isUpdated: Boolean) {
        val downloadUrl = viewModel.getZipDownloadUrl(
                isLoggedIn = tokenProvider.isLoggedIn(),
                isPreviewMode = readingFragConfig?.isPreviewMode ?: true,
                resource = resource
        )

        val resLink = resource.resLink.replace(" ", "").trim().toDecoded()
        val fileName = resLink.guessFileName()

        val path = "${resource.id}_${resLink}"

        val isSingleEpub = !resource.ebupChapterLink.isNullOrBlank()

        if(isUpdated) {
            if(isSingleEpub)
                viewModel.deleteReadDataByBookId(chapter.bookId)
            else
                viewModel.deleteReadData(resource.id)
            updateNotificationManager.onResourceUpdated(resource.id)
        }

        val zipStoragePath = createPath(DOWNLOADED_FILES, path)
        val zippedFile = File(zipStoragePath)

        val cachedData: ReadData? = if(isSingleEpub)
            viewModel.getCachedReadDataByBookId(chapter.bookId)
        else
            viewModel.getCachedReadData(resource.id)

        fun show() {
            binding?.wvReadingPdf?.loadDataWithBaseURL("null", epubHtmlTemplate, "text/html", "UTF-8", "about:blank")
        }

        fun extract() = fragScope?.launch {
            epubBase64String = zippedFile.toEncodedBase64String()
            zippedFile.delete()
            val encryptedEpubFile: File = File(zipStoragePath.replace(fileName, ""), fileName)
            val stream: FileOutputStream = withContext(Dispatchers.IO) {
                FileOutputStream(encryptedEpubFile)
            }
            stream.use { stream ->
                stream.write(epubBase64String.toByteArray())
            }

            try {
                viewModel.cacheReadData(ReadData(resource.id, chapter.bookId, encryptedEpubFile.absolutePath))
            } catch (e: Exception) {
                Log.e(TAG, "extract: ", e)
            }
            show()
        }

        when {
            cachedData != null && !TextUtils.isEmpty(cachedData.data) -> {
                val epubFilePath = cachedData.data

                val savedEpubFile = File(epubFilePath)

                val bytes = ByteArray(savedEpubFile.length().toInt())

                val `in`: FileInputStream = withContext(Dispatchers.IO) {
                    FileInputStream(savedEpubFile)
                }
                try {
                    withContext(Dispatchers.IO) {
                        `in`.read(bytes)
                    }
                } finally {
                    withContext(Dispatchers.IO) {
                        `in`.close()
                    }
                }

                epubBase64String = String(bytes)
                show()
            }

            else -> {
                download(downloadUrl, zipStoragePath.replace(fileName, ""), fileName) {
                    Log.e(TAG, "Inside load epub")
                    binding?.alertDialogLayout?.hideView()
                    extract()
                }
            }
        }

    }

    /**
     *  Downloads a file to a given download path
     *
     *  @param downloadUrl url of the file
     *  @param downloadPath will be used for storing downloaded file
     *  @param fileName name of the file
     *  @param onDownloadCompleted gives a callback once download is completed
     */
    private fun download(downloadUrl: String, downloadPath: String, fileName: String, onDownloadCompleted: () -> Unit) {
        // TODO:
        //  - Check if previous download status by using downloadId
        //  - If prev download is not completed then try resume else start new

        binding?.downloadMsgTxt?.text = "Downloading ${resource.resName}".toDecoded()
        binding?.downloadPercent?.text = "0%"
        binding?.alertDialogLayout?.showView()

        var lastPercentage = 0
        var downloadId = 0
        downloadId = PRDownloader.download(downloadUrl, downloadPath, fileName)
                .setHeader("X-Auth-Token", tokenProvider.getToken())
                .setPriority(Priority.HIGH)
                .build()
                .setOnProgressListener {
                    // update progress
                    try {
                        val percentage = (it.currentBytes * 100).div(it.totalBytes).toInt()
                        if(lastPercentage != percentage) {
                            lastPercentage = percentage
                            binding?.downloadPercent?.text = "${percentage}%"
                            binding?.progressDialogBar?.progress = lastPercentage
                            Log.i(TAG, "startZipDownload: ${percentage}% (${it.currentBytes.byteToMB()} MB/${it.totalBytes.byteToMB()} MB)")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "startZipDownload: ", e)
                    }
                }
                .start(object : OnDownloadListener {
                    override fun onDownloadComplete() {

                        if(isDetached || !isAdded)
                            return

                        binding?.downloadPercent?.text = "Processing..."

                        Log.e(TAG, "download status: " + PRDownloader.getStatus(downloadId))

                        if (PRDownloader.getStatus(downloadId) == Status.COMPLETED) {
                            PRDownloader.cancel(downloadId)
                        }

                        onDownloadCompleted()

                    }

                    override fun onError(error: Error?) {
                        if (PRDownloader.getStatus(downloadId) != Status.COMPLETED &&
                                PRDownloader.getStatus(downloadId) != Status.UNKNOWN) {
                            Log.e(TAG, "onError: ${error?.responseCode}")
                            Log.e(TAG, "downloadStatus: ${PRDownloader.getStatus(downloadId)}")
                            showToast("Problem while downloading reading material. Please try again")
                            listener?.onBackBtnClicked()
                        }
                    }
                })

        // TODO: store the downloadId in Local storage for later resuming the download
    }

    private fun updateReadType() {
        try {
            when {
                "pdf".equals(resource.fileType, true) -> {
                    currentReadType = if(isSecurePDF(resource.resLink, "no")) ReadType.SECURE_PDF else ReadType.NON_SECURE_PDF
                }
                "epub".equals(resource.fileType, false) || ".epub".contains(resource.resLink) -> {
                    currentReadType = ReadType.E_PUB
                    activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
                }
                else -> currentReadType = ReadType.ZIPPED_PDF
            }
        } catch (e: Exception) {
            Log.e(TAG, "updateReadType: ", e)
        }
    }

    private fun isSecurePDF(resLink: String, videoPlayer: String) =
            (resLink.contains(".pdf") || resLink.contains(".PDF")) && "no".equals(videoPlayer, true)


    private fun updateHighlightString() {
        try {
            renderHighlightString = when (currentReadType) {
                ReadType.E_PUB -> {
                    try {
                        readNotesJson.toString().replace("\\/", "/")
                    } catch (e: NullPointerException) {
                        Log.e(TAG, "updateHighlightString: ", e)
                        "{\"rows\":[]}"
                    }
                }
                else -> {
                    try {
                        readNotesJson.optJSONArray("rows")?.toString()?.replace("\\/", "/") ?: "[]"
                    } catch (e: NullPointerException) {
                        Log.e(TAG, "updateHighlightString: ", e)
                        "[]"
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "updateHighlightString: ", e)
        }
    }

    private fun updateWebViewWithHighlights() {
        if(isWebViewLoaded)
            binding?.wvReadingPdf?.evaluateJavascript("loadAnnotationsFromJavaData(0,'$renderHighlightString')", null)
    }


    // ****** Extension helper functions for Zipped PDFs *****

    private fun String.replaceImagePaths(imageLocation: String): String {
        var content = this
        if(content.contains("data=\"") && !readingImageUpdateStatusManager.isSVGUpdated(resource.id)) {
            if(imageLocation.isNotEmpty()) {
                content = readUnzipHelper.replaceSVG(content, imageLocation, resource, chapter)
                readingImageUpdateStatusManager.setSVGUpdated(resource.id, true)
            }
        }

        val isPreviewMode = readingFragConfig?.isPreviewMode ?: true

        if (content.contains("<img") && content.contains("/funlearn/") && !readingImageUpdateStatusManager.isImageReplaced(resource.id)) {
            content = content.replace("/funlearn/", networkConfig.service + "funlearn/")
        } else if (content.contains("../Images/") && !readingImageUpdateStatusManager.isImageReplaced(resource.id)) {
            if (imageLocation.isNotEmpty()) {
                content = content.replace("../Images/", imageLocation + "Images/")
            } else {
                //Get Images Directly Nothing to download
                val directImagePath: String = if (isPreviewMode) {
                    networkConfig.service1 + "/funlearn/downloadEpubImage?source=upload/resources/" + resource.id + "/extract/OEBPS/"
                } else {
                    networkConfig.service1 + "funlearn/downloadEpubImage?source=" +
                            "upload/books/" + chapter.bookId + "/chapters/" + chapter.chapterId + "/" + resource.id + "/extract/OEBPS/"
                }
                content = content.replace("../Images/".toRegex(), directImagePath + "Images/")
            }
        } else if (content.contains("Images/") && !readingImageUpdateStatusManager.isImageReplaced(resource.id)) {
            if (imageLocation.isNotEmpty()) {
                content = content.replace("Images/", imageLocation)
            } else {
                //Get Images Directly Nothing to download
                val directImagePath = if (isPreviewMode) {
                    networkConfig.service1 + "/funlearn/downloadEpubImage?source=upload/resources/" + resource.id + "/extract/OEBPS/"
                } else {
                    networkConfig.service1 + "funlearn/downloadEpubImage?source=" +
                            "upload/books/" + chapter.bookId + "/chapters/" + chapter.chapterId + "/" + resource.id + "/extract/OEBPS/"
                }
                content = content.replace("Images/".toRegex(), directImagePath + "Images/")
            }
        } else if (content.contains("images/") && !readingImageUpdateStatusManager.isImageReplaced(resource.id)) {
            if (imageLocation.isNotEmpty()) {
                content = content.replace("images/".toRegex(), imageLocation)
            } else {

                //Get Images Directly Nothing to download
                val directImagePath = if (isPreviewMode) {
                    networkConfig.service1 + "/funlearn/downloadEpubImage?source=upload/resources/" + resource.id + "/extract/OEBPS/"
                } else {
                    networkConfig.service1 + "funlearn/downloadEpubImage?source=" +
                            "upload/books/" + chapter.bookId + "/chapters/" + chapter.chapterId + "/" + resource.id + "/extract/OEBPS/"
                }
                content = content.replace("images/".toRegex(), directImagePath + "images/")
            }
        }

        return content
    }

    private fun String.replaceHeader(): String {
        return replace("</head>", "<style>\n" +
                "@font-face { font-family: 'Work Sans Regular'; src: url('file:///android_asset/fonts/WorkSans-Regular.ttf');}" +
                "@font-face { font-family: 'Work Sans Medium'; src: url('file:///android_asset/fonts/WorkSans-Medium.ttf');}" +
                "@font-face { font-family: 'Work Sans Light'; src: url('file:///android_asset/fonts/WorkSans-Light.ttf');}" +
                "@font-face { font-family: 'Work Sans Bold'; src: url('file:///android_asset/fonts/WorkSans-Bold.ttf');}" +
                "@font-face { font-family: 'Work Sans SemiBold'; src: url('file:///android_asset/fonts/WorkSans-SemiBold.ttf');}" +
                "html, body {\n" +
                "  font-family: 'Work Sans Light' !important; \n" +
                "  font-size: 16px;\n" +
                "  line-height: 24px;\n" +
                "  letter-spacing: 0.01em;\n" +
                "  color: #1B2733;\n" +
                "  font-weight: 300;\n" +
                "  overflow: auto;\n" +
                "  overflow-x: hidden;\n" +
                "  -webkit-overflow-scrolling: touch;\n" +
                "}" +
                ".table-responsive {\n" +
                "   display: block;\n" +
                "   width: 100%;\n" +
                "   overflow-x: auto;\n" +
                "   -webkit-overflow-scrolling: touch;\n" +
                "   -ms-overflow-style: -ms-autohiding-scrollbar;\n" +
                "}" +
                "body a, p, span, ul, li {\n" +
                /*"  font-weight: 300 !important;\n" +*/
                "}" +
                ".blackTheme {\n" +
                "color:black !important;\n" +
                "}\n" +

                ".whiteTheme {\n" +
                "color:white !important;\n" +
                "}\n" +
                ".whiteTheme p span{\n" +
                "color:white !important;\n" +
                "}\n" +
                "#htmlreadingcontent img {\n" +
                "   max-width: 100%;\n" +
                "   height: auto;\n" +
                "   border: 0; }\n" +
                " #htmlreadingcontent h1 {\n" +
                "   font-family: “Work Sans”, sans-serif;\n" +
                "   font-size: 30px !important;\n" +
                "   line-height: 36px !important;\n" +
                "   letter-spacing: 0.04em !important;\n" +
                "   color: #1B2733 !important; }\n" +
                " #htmlreadingcontent h2 {\n" +
                "   font-family: “Work Sans”, sans-serif;\n" +
                "   font-size: 24px !important;\n" +
                "   line-height: 30px !important;\n" +
                "   letter-spacing: 0.02em !important;\n" +
                "   color: #1B2733 !important; }\n" +
                " #htmlreadingcontent h3 {\n" +
                "   font-family: “Work Sans”, sans-serif;\n" +
                "   font-size: 18px !important;\n" +
                "   line-height: 28px !important;\n" +
                "   letter-spacing: 0.01em !important;\n" +
                "   color: #1B2733 !important; }\n" +
                " #htmlreadingcontent p, #htmlreadingcontent span {\n" +
                "   font-family: “Work Sans”, sans-serif; }\n" +
                " #htmlreadingcontent span.annotator-hlh {\n" +
                "   font-size: inherit !important;\n" +
                "   font-family: inherit; }\n" +
                " #htmlreadingcontent span.annotator-hl {\n" +
                "   font-size: inherit !important;\n" +
                "   font-family: inherit; }\n" +
                "\n" +
                "#htmlreadingcontent table {\n" +
                "   width: 100% !important;\n" +
                "    border-collapse: collapse;\n" +
                "}\n" +
                "#htmlreadingcontent table td p {\n" +
                "text-align:unset !important;\n" +
                "}\n" +
                " </style>")
    }

    private fun String.replaceFontPaths(fontsLocation: String): String {
        var content = this
        if (content.contains("fonts/") && fontsLocation.isNotEmpty()) {
            content = content.replace(
                    "fonts/",
                    fontsLocation
            )
        }
        return content
    }

    private fun String.replaceStylesPaths(stylesLocation: String): String {
        var content = this
        if (content.contains("../Styles/") && stylesLocation.isNotEmpty()) {
            content = content.replace(
                    "../Styles/",
                    stylesLocation
            )
        }
        return content
    }

    private fun String.replaceTable(): String {
        var content = this
        if (content.contains("<table")) {
            content = content.replace("<table", "<div class=table-responsive><table")
            content = content.replace("</table>", "</table></div>")
        }
        return content
    }

    private fun String.replaceImageExtension(imageLocation: String): String {
        var html = this

        if (html.contains("data=\"")) {
            if (imageLocation.isNotEmpty()) {
                html = readUnzipHelper.replaceSVG(html, imageLocation, resource, chapter)
                readingImageUpdateStatusManager.setSVGUpdated(
                        resource.id,
                        true
                )
            } else {
                readingImageUpdateStatusManager.setSVGUpdated(
                        resource.id,
                        false
                )
            }
        }

        if (html.contains("../Images/")) {
            if (imageLocation.isNotEmpty()) {
                html = html.replace(
                        "../Images/".toRegex(),
                        imageLocation
                )
                val htmlDoc = Jsoup.parse(html)
                val imgElements = htmlDoc.getElementsByTag("img")
                for (i in imgElements.indices) {
                    //get img path from img src tag
                    var imgPath = imgElements[i].attr("src")
                    try {
                        imgPath = imgPath.toDecode()
                    } catch (e: UnsupportedEncodingException) {
                        Log.e(TAG, "getHtmlFromFile: ", e)
                    }
                    //check if that path exists or not
                    try {
                        imgPath = imgPath.toDecode()
                    } catch (e: UnsupportedEncodingException) {
                        Log.e(TAG, "getHtmlFromFile: ", e)
                    }
                    //check if that path exists or not
                    val file = File(imgPath)
                    if (file.exists()) {
                        imgElements[i].attr("src", imgPath)
                    } else {
                        //replace the path accordingly
                        if (imgPath.contains(".jpg")) {
                            imgPath = imgPath.replace(".jpg", ".png")
                        } else if (imgPath.contains(".png")) {
                            imgPath = imgPath.replace(".png", ".jpg")
                        }
                        imgElements[i].attr("src", imgPath)
                    }
                }
                html = htmlDoc.html()
                readingImageUpdateStatusManager.setImageReplaced(
                        resource.id,
                        true
                )
            } else {
                readingImageUpdateStatusManager.setImageReplaced(
                        resource.id,
                        false
                )
            }
        } else if (html.contains("Images/")) {
            if (imageLocation.isNotEmpty()) {
                html = html.replace(
                        "Images/".toRegex(),
                        imageLocation
                )
                val htmlDoc = Jsoup.parse(html)
                val imgElements = htmlDoc.getElementsByTag("img")
                for (i in imgElements.indices) {
                    //get img path from img src tag
                    var imgPath = imgElements.attr("src")
                    //check if that path exists or not
                    val file = File(imgPath)
                    if (file.exists()) {
                        //do nothing
                    } else {
                        //replace the path accordingly
                        if (imgPath.contains(".jpg")) {
                            imgPath = imgPath.replace(".jpg", ".png")
                        } else if (imgPath.contains(".png")) {
                            imgPath = imgPath.replace(".png", ".jpg")
                        }
                        imgElements.attr("src", imgPath)
                    }
                }
                html = htmlDoc.html()
                readingImageUpdateStatusManager.setImageReplaced(
                        resource.id,
                        true
                )
            } else {
                readingImageUpdateStatusManager.setImageReplaced(
                        resource.id,
                        false
                )
            }
        } else if (html.contains("images/")) {
            if (imageLocation.isNotEmpty()) {
                html = html.replace(
                        "images/".toRegex(),
                        imageLocation
                )
                val htmlDoc = Jsoup.parse(html)
                val imgElements = htmlDoc.getElementsByTag("img")
                for (i in imgElements.indices) {
                    //get img path from img src tag
                    var imgPath = imgElements.attr("src")
                    //check if that path exists or not
                    val file = File(imgPath)
                    if (file.exists()) {
                        //do nothing
                    } else {
                        //replace the path accordingly
                        if (imgPath.contains(".jpg")) {
                            imgPath = imgPath.replace(".jpg", ".png")
                        } else if (imgPath.contains(".png")) {
                            imgPath = imgPath.replace(".png", ".jpg")
                        }
                        imgElements.attr("src", imgPath)
                    }
                }
                html = htmlDoc.html()
                readingImageUpdateStatusManager.setImageReplaced(
                        resource.id,
                        true
                )
            } else {
                readingImageUpdateStatusManager.setImageReplaced(
                        resource.id,
                        false
                )
            }
        }
        return html
    }

    private fun String.removeExtraCharacters(): String {
        return this.replace("\uF097", "")
                .replace("\uF0DC", "")
    }

    private fun String.encryptOrEmpty(): String {
        return getEncryptionHelper()?.encryptOrNull(this) ?: ""
    }

    private fun String.decryptOrEmpty(): String {
        return getEncryptionHelper()?.decryptOrNull(this) ?: ""
    }

    private fun getEncryptionHelper(): Encryption? {
        val encryptionKey = encryptionKeysProvider.getReadEncryptionKey()
        return Encryption.getDefault(
                encryptionKey,
                getString(R.string.salt),
                ByteArray(16)
        )
    }

    fun onBackPressed(): Boolean {
        binding?.wvSearch?.let {
            if(inWebSearchMode) {
                inWebSearchMode = false
                setupToolbar()
                it.hideView()
                it.loadUrl("about:blank")
                binding?.wvReadingPdf?.visibility(isSecurePdfOrEPub())
                binding?.wvReading?.visibility(!isSecurePdfOrEPub())
                return true
            }
        }
        binding?.wvFlashCard?.let {
            if (inFlashCardMode) {
                inFlashCardMode = false
                setupToolbar()
                it.hideView()
                it.loadUrl("about:blank")
                binding?.wvReadingPdf?.visibility(isSecurePdfOrEPub())
                binding?.wvReading?.visibility(!isSecurePdfOrEPub())
                binding?.wvSearch?.hideView()
                return true
            }
        }
        return false
    }

    private fun isSecurePdfOrEPub(): Boolean {
        return currentReadType == ReadType.SECURE_PDF || currentReadType == ReadType.E_PUB
    }

    override fun onPause() {
        storeLastReadPDFPage()
        super.onPause()
        if(currentReadType == ReadType.SECURE_PDF)
            decryptedPdfFile?.delete()
    }

    override fun onDestroyView() {
        storeLastReadPDFPage()
        binding?.wvReading?.destroy()
        binding?.wvReadingPdf?.destroy()
        binding?.wvSearch?.destroy()
        binding?.wvFlashCard?.destroy()
        super.onDestroyView()
    }

    private fun storeLastReadPDFPage() {
        //Get Last Read Page
        if (currentReadType == ReadType.SECURE_PDF) {
            if (binding?.wvReadingPdf != null) {
                binding?.wvReadingPdf?.evaluateJavascript("sendCurrentPageNo()", null)
            }
        }
    }


    interface  OnReadingFragInteractionListener {
        fun onBackBtnClicked()

        fun checkForStoragePermission()

        fun onWebSearch(resource: Resource, term: String)

        fun onOpenNotesList(annotationsRequest: AnnotationsRequest)

        fun onOpenGPTChat(resource: Resource, chapter: Chapter, doubts: String, imgData: String, imgUrl: String)

        fun onOpenGPTMCQChat(
            resource: Resource,
            chapter: Chapter,
            doubts: String,
            imgData: String,
            imgUrl: String,
            resOptionType: String,
            lang: String,
            query: String,
            quizId: Int
        )
    }

    companion object {

        private const val TAG = "ReadingFrag"

        const val ARG_READING_CONFIG = "argReadingConfig"
        const val ARG_CHAPTERS_LIST = "chapterList"

        private const val DOWNLOADED_FILES = "/.BooksMojo/"
        private const val UNZIPPED_EPUB_FILES = "/.Books2Mojo/"

        private const val OPEN_ERROR_MSG = "Problem while opening this reading material. Please try again"
        private const val PROBLEM_UPDATING_ANNOTATION = "Problem while updating annotation. Please try again"
        private const val ANNOTATION_UPDATE_SUCCESS = "Annotation updated successfully"

        private var deepLinkResource: Resource? = null
        private var deepLinkChapter: Chapter? = null

        fun newInstance(
            readingFragConfig: ReadingFragConfig,
            readingResource: Resource?,
            resourceChapter: Chapter?,
            chaptersList: ArrayList<ChapterResources>
        ) = ReadingFrag().also {
            it.arguments = bundleOf(
                ARG_READING_CONFIG to readingFragConfig,
                ARG_CHAPTERS_LIST to chaptersList
            )
            deepLinkResource = readingResource
            deepLinkChapter = resourceChapter
        }
    }

    override fun onGiveHintClicked(mcqText: String?, position: Int) {
        val query = "MCQ\n$mcqText"
        quizId = if (selectedResOptionLang.equals(lang1, true)) {
            language1MCQs[position].id
        } else {
            language2MCQs[position].id
        }
        listener?.onOpenGPTMCQChat(resource, chapter, "", "", "", "hint", selectedResOptionLang, query, quizId!!)
    }

    override fun onExplainMCQClicked(mcqText: String?, position: Int) {
        val query = "MCQ\n$mcqText"
        quizId = if (selectedResOptionLang.equals(lang1, true)) {
            language1MCQs[position].id
        } else {
            language2MCQs[position].id
        }
        listener?.onOpenGPTMCQChat(resource, chapter, "", "", "", "explain", selectedResOptionLang, query, quizId!!)
    }

    override fun onCreateSimilarMCQsClicked(mcqText: String?, position: Int) {
        val query = "MCQ\n$mcqText"
        quizId = if (selectedResOptionLang.equals(lang1, true)) {
            language1MCQs[position].id
        } else {
            language2MCQs[position].id
        }
        listener?.onOpenGPTMCQChat(resource, chapter, "", "", "", "similarMCQ", selectedResOptionLang, query, quizId!!)
    }

    override fun onShowHistoryClicked(mcqText: String?, position: Int) {
        val query = "MCQ\n$mcqText"
        quizId = if (selectedResOptionLang.equals(lang1, true)) {
            language1MCQs[position].id
        } else {
            language2MCQs[position].id
        }
        listener?.onOpenGPTMCQChat(resource, chapter, "", "", "", "history", selectedResOptionLang, query, quizId!!)
    }

}
