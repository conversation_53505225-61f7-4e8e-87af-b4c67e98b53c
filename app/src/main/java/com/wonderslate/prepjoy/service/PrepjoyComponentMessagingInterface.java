package com.wonderslate.prepjoy.service;

public class PrepjoyComponentMessagingInterface {
    // List of actions
    public static final String BROADCAST_ACTION_READ_DATABASE = "readDatabase";
    public static final String BROADCAST_ACTION_LIBRARY_SETUP = "librarySetup";
    public static final String BROADCAST_ACTION_GET_BOOK_LIST = "getBookList";
    public static final String BROADCAST_ACTION_GET_BOOK = "getBook";
    public static final String BROADCAST_ACTION_CHAPTER_BOOK = "getChapter";
    public static final String BROADCAST_ACTION_GET_QUIZ = "getQuiz";
    public static final String BROADCAST_ACTION_GET_BOOK_DETAILS = "bookDetails";
    public static final String BROADCAST_ACTION_GET_TEST_BOOK_LIST = "testBookList";
    public static final String BROADCAST_ACTION_GET_TEST_CHAPTER_LIST = "testChapList";
    public static final String BROADCAST_ACTION_GET_TEST_QUIZ_TYPES = "testQuizTypes";
    public static final String BROADCAST_ACTION_GET_TEST_QUIZ_LEVEL = "testQuizLevels";
    public static final String BROADCAST_ACTION_GET_TEST_NUM_QUES = "testNumQues";
    public static final String BROADCAST_ACTION_CREATE_QUIZ = "testCreate";
    public static final String BROADCAST_ACTION_GET_CATAGORIES = "getCategories";
    public static final String BROADCAST_ACTION_PAYMENT = "appPurchase";
    public static final String BROADCAST_ACTION_PAYMENT_CHECK = "bookPurchased";
    public static final String BROADCAST_ACTION_USERNAME_CHECK = "usernameCheck";
    public static final String BROADCAST_ACTION_SERVICE_LOGIN = "serviceLogin";
    public static final String BROADCAST_ACTION_SERVICE_REGISTRATION = "serviceRegistration";
    public static final String BROADCAST_ACTION_SERVICE_USERNAME_EXISTS = "serviceUsernameExists";
    public static final String BROADCAST_ACTION_SERVICE_PROMO_CODE_CHECK = "servicepromoCodeCheck";
    public static final String BROADCAST_ACTION_SERVICE_USER_PROFILE_DETAILS = "serviceUserProfileDetails";
    public static final String BROADCAST_ACTION_SERVICE_CHANGE_PASSWORD = "serviceChangePassword";
    public static final String BROADCAST_ACTION_SERVICE_UPDATE_USER_PROFILE = "serviceUpdateUserProfile";
    public static final String BROADCAST_ACTION_SERVICE_SEARCH_SUGGESTION = "serviceSearchSuggestion";

    public static final String BROADCAST_ACTION_DOWNLOAD_FILE = "fileDownload";

    public static final String BROADCAST_ACTION_NOTIFICATION_RECEIVE = "notificationReceiver";
    public static final String BROADCAST_ACTION_NOTIFICATION_DISMISS = "notificationDismiss";
    public static final String BROADCAST_ACTION_NOTIFICATION_DELETE = "notificationDelete";
    public static final String BROADCAST_NOTIFICATION_INDEPENDENT_RES = "notificationIndRes";
    public static final String BROADCAST_RESULT_STATUS = "status";
    public static final String BROADCAST_RESULT_BOOK_ID = "bookId";
    public static final String BROADCAST_RESULT_CHAPTER_ID = "chapterId";
    public static final String BROADCAST_RESULT_QUIZ_ID = "quizId";
    public static final String BROADCAST_RESULT_JSON_DATA = "jsonData";
    public static final String BROADCAST_RESULT_REFRESH_UI = "updateUi";
    public static final String ERROR_STATUS_KEY = "errorResponse";
    public static final String ERROR_TIME_OUT_MSG = "Oops! Connection timed out. Please try again.";
    public static final String ERROR_INTERNAL_SERVER = "Oops! Something went wrong.";

    // Result status
    public static final Integer RESULT_STATUS_SUCCESS = 0;
    public static final Integer RESULT_STATUS_FAILED = -2;
}
