package com.wonderslate.prepjoy.service

import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Intent
import android.media.RingtoneManager
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.lifecycle.Lifecycle
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.android.gms.tasks.Task
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.wonderslate.data.helper.ConstantsHelper
import com.wonderslate.data.network.WSAPIManager
import com.wonderslate.data.network.Wonderslate
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.domain.usecase.user_details.ClearAllCachedDataUseCase
import com.wonderslate.prepjoy.BuildConfig
import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.Utils.CrashlyticsLogger
import com.wonderslate.prepjoy.ui.dashboard.DashBoardActivity
// import com.wonderslate.prepjoy.ui.groupwall.GroupWallCommentAndReplayActivity
import com.wonderslate.prepjoy.ui.login.LoginActivity
import com.wonderslate.prepjoy.ui.notification.NotificationDetailsActivity
import com.wonderslate.prepjoy.ui.notification.NotificationUtil
import com.wonderslate.prepjoy.ui.quiz.QuizActivity
import kotlinx.coroutines.*
import java.time.LocalDateTime
import java.time.Duration
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import java.io.UnsupportedEncodingException
import java.net.URLEncoder
import java.util.concurrent.atomic.AtomicInteger

class PrepJoyFirebaseMessagingService :
        FirebaseMessagingService(), KoinComponent {
    private val TAG: String = "PrepJoyFirebaseMessage"
    private val LIVE_NOTIFICATION_TOPIC = "Topic_All_User"
    private val DEBUG_NOTIFICATION_TOPIC = "Topic_Debug_User"
    private var tokenFirebase = "nil"
    private val NOTIFICATION_INTENT_REQUEST_CODE = 258

    private val clearAllCachedDataUseCase: ClearAllCachedDataUseCase by inject()


    override fun onMessageReceived(message: RemoteMessage) {
        // super.onMessageReceived(message)
        //Check for Notification Type message or Data Type message
        if (message.notification != null) {
            message.notification!!.body?.let { message.notification!!.title?.let { it1 -> sendWSNotification(it, it1, message.sentTime) } }
        } else {
            if (!onlineGameRequest(message.data.toString())) {
                sendWSDataNotification(message.data.toString(), message.sentTime)
            }
        }
    }

    private fun onlineGameRequest(data: String): Boolean {
        var result = false
        try {
            val json = JSONObject(data)
            val isGameNotification = json.has("userType") &&
                    json.optString("userType") == "singleuser" &&
                    (json.has("userName") || json.has("challengerName"))
            if (isGameNotification) {
                result = true
                val title = json.optString("title")
                val intent = if (title == "declined to play") {
                    Intent("decline_game").also {
                        it.putExtra("userName", json.optString("body"))
                    }
                } else {
                    Intent("online_game").also {
                        it.putExtra("accessCode", title)
                        it.putExtra("challenger", json.optString("challengerName"))
                        if (json.has("bookId")) {
                            it.putExtra("bookId", json.optString("bookId"))
                        }
                    }
                }
                val isAppStopped = null != Wonderslate.APP_STATE &&
                        Wonderslate.APP_STATE == Lifecycle.Event.ON_STOP
                val isQuizActivity = Wonderslate.aClass.simpleName == QuizActivity::class.simpleName
                val isDashBoard = Wonderslate.aClass.simpleName == DashBoardActivity::class.simpleName
                if ((isAppStopped || isQuizActivity) &&
                    title != "declined to play") {
                    val sharedPrefs = WonderPubSharedPrefs.getInstance(applicationContext)
                    if (json.has("bookId") && json.optString("bookId") != "null") {
                        sharedPrefs.sharedPrefsUserChallengerBookId = json.optString("bookId")
                    }
                    if (json.has("challengerName")) {
                        if (json.has("bookId") && json.optString("bookId") != "null") {
                            notifyGameCode(title,
                                json.optString("challengerName"),
                                json.optString("bookId"))
                        } else {
                            notifyGameCode(title, json.optString("challengerName"))
                        }
                    } else {
                        notifyGameCode(title)
                    }
                } else if (isDashBoard || isQuizActivity) {
                    LocalBroadcastManager.getInstance(baseContext).sendBroadcast(intent)
                }
            }
        }
        catch (e:Exception)
        {
            val message = "Exception for notification generation of online gaming => ${e.message}"
            CrashlyticsLogger.logError(WonderPubSharedPrefs.getInstance(applicationContext).usermobile, message, e)
        }
        return result
    }

    private fun notifyGameCode(code: String, challenger: String? = null, bookId: String? = null) {
        val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        val builder = NotificationUtil.playQuizRequest(applicationContext, code, challenger)
        val quizIntent = Intent(baseContext, DashBoardActivity::class.java).apply {
            putExtra("isAccess", true)
            putExtra("code", code)
            putExtra("challenger", challenger)
            bookId?.let { putExtra("bookId", it) }
            addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
        }
        val id = System.currentTimeMillis().toInt()
        val pendingIntent = PendingIntent.getActivity(this,
            id, quizIntent, PendingIntent.FLAG_IMMUTABLE)
        builder.setContentIntent(pendingIntent)
        notificationManager.notify(id, builder.build())
    }

    private fun sendWSDataNotification(message: String, sentTime: Long) {
        var message = message
        val messageObject: JSONObject
        var bigPicName: String?
        var title: String
        var body: String
        val id: String
        val nSentTime: String
        val deepLink: String?
        val resourceLink: String = "funlearn/showImage?"
        var groupId: String = ""
        var postId: String = ""
        try {
            val updateType: String
            message = message.replace(" ", "_")
            message = message.replace(",_", ", ")
            messageObject = JSONObject(message)
            Log.d(TAG, "old Notification$messageObject")

            message = messageObject.optString("type")

            if (messageObject.has("messageType") && !message.contains("channel")) {
                updateType = messageObject.getString("messageType")
                when (updateType) {
                    "syllabusListChanged" -> {
                            /* dataSource.storeJSONData(WonderPublishDBContract.DATA_UPDATE_NOTIFICATION + DATA_UPDATE_STORE + sentTime,
                                messageObject.toString())
                        //Notify app that new notification came
                        Log.d("WSMessagingService", "Broadcasting message")
                        val messagingIntent = Intent("syllabusListChanged")
                        LocalBroadcastManager.getInstance(this).sendBroadcast(messagingIntent)*/
                    }
                    "resourceUpdated", "quizUpdated", "readingMaterialUpdated" -> {

                        //check if it is for Independent Resource
                        if (updateType == "resourceUpdated" && messageObject.has("resType")) {
                            sendIndependentResBroadcast(messageObject.optString("resType"))
                        }

                        // TODO: store in DB instead of shared prefs
                        try {
                            val sharedPrefs = WonderPubSharedPrefs.getInstance(this)
                            val notifications = JSONArray(sharedPrefs.updateNotifications)
                            notifications.put(messageObject.toString())
                            sharedPrefs.updateNotifications = notifications.toString()
                        } catch (e: Exception) {
                            Log.e(TAG, "sendWSDataNotification: ", e)
                        }
                    }
                    "new_message" -> {
                        /* messageObject.put("sentTime", LocalDate(sentTime).toString("dd-MM-yyyy")
                            + " " + LocalTime(sentTime).toString("HH:mm"))
                    val intent = Intent("CHAT_MESSAGE")
                    val gson = Gson()
                    val type = object : TypeToken<NotificationModel?>() {}.type
                    val notificationModel: NotificationModel = gson.fromJson(messageObject.toString(), type)
                    intent.putExtra("addMessage", notificationModel)
                    sendOrderedBroadcast(intent, null)*/
                    }
                    /* "delete_message" -> {
                    messageObject.put("sentTime", LocalTime(sentTime).toString("HH:mm"))
                    val deleteIntent = Intent("CHAT_MESSAGE")
                    val gsonDelete = Gson()
                    val typeModel = object : TypeToken<NotificationModel?>() {}.type
                    val modelNotification: NotificationModel = gsonDelete.fromJson(messageObject.toString(), typeModel)
                    deleteIntent.putExtra("deleteMessage", modelNotification)
                    sendOrderedBroadcast(deleteIntent, null)
                }*/
                    "Logout" -> if (!WonderPubSharedPrefs.getInstance(this@PrepJoyFirebaseMessagingService).accessToken.equals("nil")
                            && !WonderPubSharedPrefs.getInstance(this@PrepJoyFirebaseMessagingService).accessToken
                                    .equals(messageObject.optString("authenticationTokenString"))) {
                    CoroutineScope(Dispatchers.Main).launch {
                        WonderPubSharedPrefs.getInstance(this@PrepJoyFirebaseMessagingService).clearAllSharePref()
                        clearAllCachedDataUseCase.execute(Unit)
                        val forceSignOutIntent = Intent(this@PrepJoyFirebaseMessagingService, LoginActivity::class.java)
                        forceSignOutIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        forceSignOutIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        startActivity(forceSignOutIntent)
                    }
                }
                    "Practice" -> {
                        Log.e("Silent for practice", messageObject.toString())
                        validateReminder()
                    }

                }
            } else {
                messageObject.put("sentTime", sentTime.toString() + "")
                messageObject.put("isRead", "true")
                title = messageObject.optString("title").replace("_", " ")
                body = messageObject.optString("body").replace("_", " ")
                nSentTime = sentTime.toString() + ""
                messageObject.put("title", title)
                messageObject.put("body", body)
                bigPicName = URLEncoder.encode(messageObject.optString("imageUrl"), "UTF-8")
                id = messageObject.optString("id")
                deepLink = messageObject.optString("link")

                if (title.equals("New Post Created", true)) {
                    title = messageObject.optString("description").replace("_", " ")
                    groupId = messageObject.optString("groupId").replace("_", " ")
                    postId = messageObject.optString("postId").replace("_", " ")
                    if (messageObject.optString("postImage").isNotEmpty() && !messageObject.optString("postImage").equals("null", true)) {
                        bigPicName = URLEncoder.encode(messageObject.optString("postImage"), "UTF-8")
                    }
                }

                if (bigPicName != null && bigPicName.isNotEmpty() && !bigPicName.equals("null", ignoreCase = true)) {
                    val notifImageUrl: String = if (groupId.isNotEmpty() && postId.isNotEmpty()) {
                        WSAPIManager.SERVICE3 + "groups/showGroupPostImage?id=" + postId + "&fileName=" + bigPicName
                    } else {
                        String.format(WSAPIManager.SERVICE4.toString() + resourceLink + "id=%s&fileName=%s&imgType=notifications", id, bigPicName)
                    }
                    val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
                    val intent: Intent = if (groupId.isNotEmpty() && postId.isNotEmpty()) {
                        // GroupWallCommentAndReplayActivity temporarily disabled for demo
                        Intent(this, NotificationDetailsActivity::class.java)
                    } else {
                        Intent(this, NotificationDetailsActivity::class.java)
                    }
                    if (groupId.isNotEmpty() && postId.isNotEmpty()) {
                        intent.putExtra("POST_NOTIFICATION", true)
                        intent.putExtra("postId", postId)
                        intent.putExtra("groupId", groupId)
                    }
                    if (notifImageUrl.contains("showGroupPostImage")) {
                        bigPicName = notifImageUrl
                    }
                    intent.putExtra("notifDetails", getNotificationModel(body, title, id, bigPicName, deepLink, nSentTime).toString())
                    intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
                    val notificationBuilder = NotificationUtil.buildBigPictureNotification(baseContext, title, body, notifImageUrl)
                    val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        PendingIntent.getActivity(this,
                            NOTIFICATION_INTENT_REQUEST_CODE, intent, PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT)
                    } else {
                        PendingIntent.getActivity(this,
                            NOTIFICATION_INTENT_REQUEST_CODE, intent, PendingIntent.FLAG_UPDATE_CURRENT)
                    }
                    notificationBuilder.setContentIntent(pendingIntent)
                    val displayNotification = if (!resources.getBoolean(R.bool.display_current_affairs_notification)) {
                        title != getString(R.string.current_affairs_notification_text)
                    } else {
                        true
                    }
                    if (displayNotification) {
                        notificationManager.notify(getId(), notificationBuilder.build())
                    }
                } else {
                    val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
                    val intent: Intent = if (groupId.isNotEmpty() && postId.isNotEmpty()) {
                        // GroupWallCommentAndReplayActivity temporarily disabled for demo
                        Intent(this, NotificationDetailsActivity::class.java)
                    } else {
                        Intent(this, NotificationDetailsActivity::class.java)
                    }
                    if (groupId.isNotEmpty() && postId.isNotEmpty()) {
                        intent.putExtra("POST_NOTIFICATION", true)
                        intent.putExtra("postId", postId)
                        intent.putExtra("groupId", groupId)
                    }
                    intent.putExtra("notifDetails", getNotificationModel(body, title, id, bigPicName, deepLink, nSentTime).toString())
                    intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
                    val notificationBuilder = NotificationUtil.buildNotification(baseContext, title, body)

                    val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        PendingIntent.getActivity(this,
                            NOTIFICATION_INTENT_REQUEST_CODE, intent, PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT)
                    } else {
                        PendingIntent.getActivity(this,
                            NOTIFICATION_INTENT_REQUEST_CODE, intent, PendingIntent.FLAG_UPDATE_CURRENT)
                    }
                    notificationBuilder.setContentIntent(pendingIntent)
                    val displayNotification = if (!resources.getBoolean(R.bool.display_current_affairs_notification)) {
                        title != getString(R.string.current_affairs_notification_text)
                    } else {
                        true
                    }
                    if (displayNotification) {
                        notificationManager.notify(getId(), notificationBuilder.build())
                    }
                }
                insertData(body, title, id, bigPicName, deepLink, nSentTime)
            }
        } catch (e: JSONException) {
            Log.e(TAG, e.message!!)
        } catch (e: UnsupportedEncodingException) {
            Log.e(TAG, e.message!!)
        }
    }

    private fun sendWSNotification(notificationMessage: String, notificationTitle: String, sentTime: Long) {
        //On click of notification it redirect to this Activity
        val startCurrentIntent = Intent(applicationContext, DashBoardActivity::class.java)
        startCurrentIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK or
                Intent.FLAG_ACTIVITY_REORDER_TO_FRONT or Intent.FLAG_ACTIVITY_SINGLE_TOP)
        startCurrentIntent.putExtra("context", "navigate_to_current_affairs")
        val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            PendingIntent.getActivity(this,
                NOTIFICATION_INTENT_REQUEST_CODE, startCurrentIntent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE)
        } else {
            PendingIntent.getActivity(this,
                NOTIFICATION_INTENT_REQUEST_CODE, startCurrentIntent, PendingIntent.FLAG_UPDATE_CURRENT)
        }
        val soundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        val notificationBuilder: NotificationCompat.Builder = NotificationCompat.Builder(this, packageName)
                .setSmallIcon(R.drawable.prepjoy_icon_light)
                .setContentText(notificationMessage)
                .setContentTitle(notificationTitle)
                .setAutoCancel(true)
                .setColor(resources.getColor(R.color.white))
                .setSound(soundUri)
                .setContentIntent(pendingIntent)
        val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(getId(), notificationBuilder.build())
        insertData(notificationMessage, notificationTitle, "", "", "", sentTime.toString() + "")
    }

    private fun getId(): Int {
        val c = AtomicInteger(0)
        return c.incrementAndGet()
    }

    /**
     * Validates if the app was not used for the day and sends a notification to user
     */
    private fun validateReminder() {
        val prefs = WonderPubSharedPrefs.getInstance(applicationContext)
        val lastLogin = prefs.userLastUsageTimeStamp
        if (lastLogin.isNotEmpty()) {
            val lastLoginTime = LocalDateTime.parse(lastLogin)
            val now = LocalDateTime.now()
            val hours = Duration.between(lastLoginTime, now).toHours()
            if (hours >= 19) {
                val user = prefs.username
                val title = getString(R.string.app_usage_reminder_title) + " " + user
                sendWSNotification(getString(R.string.app_usage_reminder_body),
                    title,
                    0L)
            }
        }
    }

    /**
     * Called if the FCM registration token is updated. This may occur if the security of
     * the previous token had been compromised. Note that this is called when the
     * FCM registration token is initially generated so this is where you would retrieve the token.
     */
    override fun onNewToken(token: String) {
        Log.d(TAG, "Refreshed token: $token")

        tokenFirebase = token
        subscribeToTopic()


        // If you want to send messages to this application instance or
        // manage this apps subscriptions on the server side, send the
        // FCM registration token to your app server.
        WonderPubSharedPrefs.getInstance(this@PrepJoyFirebaseMessagingService).firebaseToken = token
    }


    private fun subscribeToTopic() {
        //Subscribe to topic depending on Live & Debug
        if (BuildConfig.DEBUG) {
            FirebaseMessaging.getInstance().subscribeToTopic(DEBUG_NOTIFICATION_TOPIC).addOnCompleteListener { task: Task<Void?> ->
                Log.d(TAG, task.toString())
                if (task.isSuccessful) {
                    WonderPubSharedPrefs.getInstance(this@PrepJoyFirebaseMessagingService).subscribedToTopic(true)
                } else {
                    WonderPubSharedPrefs.getInstance(this@PrepJoyFirebaseMessagingService).subscribedToTopic(false)
                }
            }
        } else {
            FirebaseMessaging.getInstance().subscribeToTopic(LIVE_NOTIFICATION_TOPIC).addOnCompleteListener { task: Task<Void?> ->
                Log.d(TAG, task.toString())
                if (task.isSuccessful) {
                    WonderPubSharedPrefs.getInstance(this@PrepJoyFirebaseMessagingService).subscribedToTopic(true)
                } else {
                    WonderPubSharedPrefs.getInstance(this@PrepJoyFirebaseMessagingService).subscribedToTopic(false)
                }
            }
        }
    }

    private fun insertData(description: String, title: String, id: String, imgUrl: String?, link: String?, nSentTime: String) {
        val count = WonderPubSharedPrefs.getInstance(this)
                .firebaseNotificationCount

        WonderPubSharedPrefs.getInstance(this).firebaseNotificationCount = count + 1

        val s = WonderPubSharedPrefs.getInstance(this).firebaseNotification

        val arr = JSONArray(s)

        val obj = JSONObject()

        obj.put("title", title)
        obj.put("description", description)
        obj.put("sentTime", nSentTime)
        obj.put("imgUrl", imgUrl)
        obj.put("id", id)
        obj.put("link", link)

        arr.put(obj)

        WonderPubSharedPrefs.getInstance(this).firebaseNotification = arr.toString()
        sendMessage(count + 1)
    }

    private fun getNotificationModel(description: String, title: String, id: String, imgUrl: String?, link: String?, nSentTime: String): JSONObject {
        val obj = JSONObject()

        obj.put("title", title)
        obj.put("description", description)
        obj.put("sentTime", nSentTime)
        obj.put("imgUrl", imgUrl)
        obj.put("id", id)
        obj.put("link", link)

        return obj
    }

    private fun sendMessage(count: Int) {
        Log.d("sender", "Broadcasting message")
        val intent = Intent("notification_count")
        // You can also include some extra data.
        intent.putExtra("count", count)
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent)
    }

    private fun sendIndependentResBroadcast(resType: String) {
        val intent: Intent = Intent(ConstantsHelper.BROADCAST_ACTION_NOTIFICATION_RECEIVE)
        intent.putExtra("resType", resType)
        sendBroadcast(intent)
    }

    private fun sendCafeCount(description: String, title: String)
    {
        val count = WonderPubSharedPrefs.getInstance(this).cafeNotificationCount
        WonderPubSharedPrefs.getInstance(this).cafeNotificationCount = count + 1

        val totalCount = count +1

        val intent = Intent("cafe_notification_count")
        // You can also include some extra data.
        intent.putExtra("cafe_count", totalCount)
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent)
    }

}