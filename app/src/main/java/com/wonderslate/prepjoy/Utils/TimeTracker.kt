package com.wonderslate.prepjoy.Utils

import android.util.Log

class TimeTracker {
    private var startTimeMillis: Long = 0
    private var endTimeMillis: Long = 0

    fun trackTime(value: <PERSON>olean, ttContext: String) {
        if (value) {
            startTimeMillis = System.currentTimeMillis()
            Log.d("TimeTracker", "Time Tracking Started: $ttContext")
        } else {
            endTimeMillis = System.currentTimeMillis()
            val timeDifferenceMillis = endTimeMillis - startTimeMillis
            val timeDifferenceSeconds = timeDifferenceMillis / 1000.0 // Convert milliseconds to seconds
            Log.d("TimeTracker", "Time Tracking Stopped: $ttContext seconds")
            Log.d("TimeTracker", "Time difference: $timeDifferenceSeconds seconds")
            startTimeMillis = 0
            endTimeMillis = 0
        }
    }
}
