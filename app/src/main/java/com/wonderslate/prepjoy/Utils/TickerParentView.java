package com.wonderslate.prepjoy.Utils;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.RelativeLayout;

public class TickerParentView extends RelativeLayout {
    public TickerParentView(Context context) {
        super(context);
    }

    public TickerParentView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public TickerParentView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }


    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev){
        return true;
    }
}
