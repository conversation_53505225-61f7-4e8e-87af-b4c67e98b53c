package com.wonderslate.prepjoy.Utils;

import com.wonderslate.data.models.MCQ;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class MCQJ<PERSON>NParser {

    public static List<MCQ> parseMCQList(String jsonResponse) {
        List<MCQ> mcqList = new ArrayList<>();

        JSONArray jsonArray = null;

        try {
            JSONObject jsonObject = new JSONObject(jsonResponse);
            if (jsonObject.has("mcqs")) {
                jsonArray = new JSONArray(jsonObject.optString("mcqs"));
            } else if (jsonObject.has("results")) {
                jsonArray = new JSONArray(jsonObject.optString("results"));
            } else {
                jsonArray = new JSONArray(jsonResponse);
            }

            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject mcqObject = jsonArray.getJSONObject(i);

                MCQ mcq = new MCQ();
                mcq.setId(mcqObject.getInt("id"));
                if (mcqObject.has("question")) {
                    mcq.setPs(mcqObject.getString("question"));
                    mcq.setOp1(mcqObject.getString("option1"));
                    mcq.setOp2(mcqObject.getString("option2"));
                    mcq.setOp3(mcqObject.getString("option3"));
                    mcq.setOp4(mcqObject.getString("option4"));

                    // Determine the correct answer
                    if (mcqObject.getString("answer1") != null && !mcqObject.getString("answer1").isEmpty()
                            && !mcqObject.getString("answer1").equals("null")
                            && mcqObject.getString("answer1").equals("Yes")) {
                        mcq.setCorrectAnswer("A");
                    } else if (mcqObject.getString("answer2") != null && !mcqObject.getString("answer2").isEmpty()
                            && !mcqObject.getString("answer2").equals("null")
                            && mcqObject.getString("answer2").equals("Yes")) {
                        mcq.setCorrectAnswer("B");
                    } else if (mcqObject.getString("answer3") != null && !mcqObject.getString("answer3").isEmpty()
                            && !mcqObject.getString("answer3").equals("null")
                            && mcqObject.getString("answer3").equals("Yes")) {
                        mcq.setCorrectAnswer("C");
                    } else if (mcqObject.getString("answer4") != null && !mcqObject.getString("answer4").isEmpty()
                            && !mcqObject.getString("answer4").equals("null")
                            && mcqObject.getString("answer4").equals("Yes")) {
                        mcq.setCorrectAnswer("D");
                    }
                } else {
                    mcq.setPs(mcqObject.getString("ps"));
                    mcq.setOp1(mcqObject.getString("op1"));
                    mcq.setOp2(mcqObject.getString("op2"));
                    mcq.setOp3(mcqObject.getString("op3"));
                    mcq.setOp4(mcqObject.getString("op4"));

                    // Determine the correct answer
                    if (mcqObject.has("ans1")) {
                        mcq.setCorrectAnswer("A");
                    } else if (mcqObject.has("ans2")) {
                        mcq.setCorrectAnswer("B");
                    } else if (mcqObject.has("ans3")) {
                        mcq.setCorrectAnswer("C");
                    } else if (mcqObject.has("ans4")) {
                        mcq.setCorrectAnswer("D");
                    }
                }
                mcq.setAnswerDescription(mcqObject.getString("answerDescription"));
                mcq.setDifficultyLevel(mcqObject.getString("difficultyLevel"));

                mcqList.add(mcq);
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }

        return mcqList;
    }
}

