package com.wonderslate.prepjoy.Utils;

import android.app.Activity;
import android.app.Dialog;
import android.content.Intent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.wonderslate.prepjoy.R;
import com.wonderslate.prepjoy.ui.dashboard.DashBoardActivity;
import com.wonderslate.prepjoy.ui.shoppingcart.ShoppingCartIcon;

public class CartDialog {

    private static final String TAG = "CartDialog";

    public static final String ADDED_SUCCESS = "This eBook has been added to your cart";
    public static final String ALREADY_ADDED = "This eBook is already present in your cart";
    public static final String ALREADY_PURCHASED = "This eBook is already purchased";
    public static final String SURE_TO_DELETE = "Are you sure you want to remove this?";

    private static final String GO_TO_MY_EBOOKS = "Go to My Books";
    private static final String REMOVE_EBOOK = "Remove";
    private static final String CANCEL = "Cancel";

    private ImageView ivClose;
    private TextView tvDesc;
    private Button btnGotoCart, btnBrowseMore;


    public void showDialog(Activity activity, String message) {
        showDialog(activity, message, null);
    }
    public void showDialog(Activity activity, String message, OnCartPositiveActionListener listener) {

        if (activity == null || activity.isFinishing() || activity.isDestroyed())
            return;

        final Dialog dialog = new BottomSheetDialog(activity);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
        dialog.setCancelable(true);
        dialog.setCanceledOnTouchOutside(false);
        dialog.setContentView(R.layout.cart_dialog);
        dialog.getWindow().findViewById(R.id.design_bottom_sheet).setBackgroundResource(android.R.color.transparent);

        //Initialize views
        tvDesc = dialog.findViewById(R.id.tvDesc);
        btnGotoCart = dialog.findViewById(R.id.btnGotoCart);
        btnBrowseMore = dialog.findViewById(R.id.btnBrowseMore);
        ivClose = dialog.findViewById(R.id.ivClose);

        btnBrowseMore.setText(R.string.continue_browsing);

        tvDesc.setText(message);

        switch (message) {
            case ALREADY_PURCHASED:
                btnBrowseMore.setVisibility(View.GONE);
                btnGotoCart.setText(GO_TO_MY_EBOOKS);
                btnGotoCart.setOnClickListener(v -> {
                    Intent intent = new Intent(activity, DashBoardActivity.class);
                    //intent.putExtra("context", "navigate_to_library");
                    intent.putExtra("context", "navigate_to_ebook");
                    activity.startActivity(intent);
                    activity.overridePendingTransition(R.anim.slide_from_right, R.anim.slide_to_left);
                    dialog.dismiss();
                });
                break;

            case SURE_TO_DELETE:
                btnBrowseMore.setText(CANCEL);
                btnGotoCart.setText(REMOVE_EBOOK);
                btnGotoCart.setOnClickListener(v -> {
                    if(listener != null)
                        listener.onPositiveButtonClicked(dialog);
                });
                break;

            case ALREADY_ADDED:
            case ADDED_SUCCESS:
                btnGotoCart.setOnClickListener(v -> {
                    new ShoppingCartIcon(activity, null).openCart();
                    dialog.dismiss();
                });
                break;
        }

        View.OnClickListener dismissListener = v -> {
            dialog.dismiss();
        };
        btnBrowseMore.setOnClickListener(dismissListener);
        ivClose.setOnClickListener(dismissListener);

        dialog.show();
    }

    public interface OnCartPositiveActionListener {
        void onPositiveButtonClicked(Dialog dialog);
    }

}