package com.wonderslate.prepjoy.Utils;

import android.app.Activity;
import android.app.Dialog;
import android.graphics.Paint;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.core.content.res.ResourcesCompat;

import com.airbnb.lottie.LottieAnimationView;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.wonderslate.prepjoy.R;
import com.wonderslate.prepjoy.Utils.CustomViews.WSTextView;

import org.json.JSONObject;

public class PriceOptionDialog {
    private static final String TAG = "PriceDialog";

    private TextView tveBookListPrice, tveBookOfferPrice, tvTestSeriesListPrice, tvTestSeriesOfferPrice,
            tveBookDiscount, tvTestSeriesDiscount, addToCartResultTxt;
    private WSTextView tvAddToCart;
    private Button goToCartBtn, continueBrowsingBtn;
    private RelativeLayout testSeriesPriceHolder, eBookPriceHolder;
    private LinearLayout priceParent, addToCardResult, addToCartPostResult;
    private LottieAnimationView addToCartSuccessAnim, addToCartFailureAnim, addToCartLoadingAnim;

    private String message = "", eBookPurchaseType = "";


    public void showDialog(Activity activity /*BookInfoModel bookInfoModel*/) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed())
            return;

        final Dialog dialog = new BottomSheetDialog(activity);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
        dialog.setCancelable(true);
        dialog.setCanceledOnTouchOutside(true);
        dialog.setContentView(R.layout.price_option_dialog_layout);
        // dialog.getWindow().findViewById(R.id.design_bottom_sheet).setBackgroundResource(android.R.color.transparent);

        //Initialize views
        tveBookListPrice = dialog.findViewById(R.id.textView_badge_paid);
        tveBookOfferPrice = dialog.findViewById(R.id.textView_badge_free);
        tvTestSeriesListPrice = dialog.findViewById(R.id.textView_badge_paid_test_series);
        tvTestSeriesOfferPrice = dialog.findViewById(R.id.textView_badge_free_test_series);
        tveBookDiscount = dialog.findViewById(R.id.textView_discount);
        tvTestSeriesDiscount = dialog.findViewById(R.id.textView_discount_test_series);
        tvAddToCart = dialog.findViewById(R.id.textView_buy_add_to_lib);
        if (tvAddToCart != null) {
            tvAddToCart.setClickable(false);
        }
        testSeriesPriceHolder = dialog.findViewById(R.id.test_series_price_holder);
        eBookPriceHolder = dialog.findViewById(R.id.eBook_price_holder);
        priceParent = dialog.findViewById(R.id.price_parent);
        addToCardResult = dialog.findViewById(R.id.add_to_cart_result);
        addToCartPostResult = dialog.findViewById(R.id.add_to_cart_post_result);
        addToCartSuccessAnim = dialog.findViewById(R.id.add_to_cart_success_lottie);
        addToCartFailureAnim = dialog.findViewById(R.id.add_to_cart_failure_lottie);
        addToCartLoadingAnim = dialog.findViewById(R.id.add_to_cart_loading_lottie);
        addToCartResultTxt = dialog.findViewById(R.id.add_to_cart_result_txt);
        goToCartBtn = dialog.findViewById(R.id.btnGotoCart);
        continueBrowsingBtn = dialog.findViewById(R.id.btnBrowseMore);

        View.OnClickListener addToCartListener = v -> {
            if (eBookPurchaseType.isEmpty()) {
                Toast.makeText(activity, "Please select an option to purchase.", Toast.LENGTH_SHORT).show();
            }
            else {
                priceParent.setVisibility(View.GONE);
                showLoadingAnim();
                //addBookToCart(bookInfoModel.getId(), eBookPurchaseType, activity);
            }
        };

        testSeriesPriceHolder.setOnClickListener(v -> {
            tvAddToCart.setOnClickListener(addToCartListener);
            tvAddToCart.setBackground(ResourcesCompat.getDrawable(activity.getResources(),
                    R.drawable.button_background_filled, null));
            tvAddToCart.setClickable(true);
            testSeriesPriceHolder.setBackground(ResourcesCompat.getDrawable(activity.getResources(),
                    R.drawable.test_series_price_bg, null));
            eBookPriceHolder.setBackgroundColor(ResourcesCompat.getColor(activity.getResources(),
                    R.color.white, null));
            eBookPurchaseType = "testSeries";
        });

        eBookPriceHolder.setOnClickListener(v -> {
            tvAddToCart.setOnClickListener(addToCartListener);
            tvAddToCart.setBackground(ResourcesCompat.getDrawable(activity.getResources(),
                    R.drawable.button_background_filled, null));
            tvAddToCart.setClickable(true);
            eBookPriceHolder.setBackground(ResourcesCompat.getDrawable(activity.getResources(),
                    R.drawable.test_series_price_bg, null));
            testSeriesPriceHolder.setBackgroundColor(ResourcesCompat.getColor(activity.getResources(),
                    R.color.white, null));
            eBookPurchaseType = "eBook";
        });

        continueBrowsingBtn.setOnClickListener(v -> dialog.dismiss());

        /*goToCartBtn.setOnClickListener(v -> {
            if (goToCartBtn.getText().toString().equalsIgnoreCase("Go to Cart")) {
                new ShoppingCartIcon(activity, null).openCart();
            }
            if (goToCartBtn.getText().toString().equalsIgnoreCase("Go to Library")) {
                ((WSLandingActivity) activity).moveToLibrary();
            }
            dialog.dismiss();
        });*/

        /*tveBookListPrice.setText(String.format("₹%s", getParsedPrice(bookInfoModel.getListPrice())));
        tveBookListPrice.setPaintFlags(tveBookListPrice.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);

        tveBookOfferPrice.setText(String.format("₹%s", getParsedPrice(bookInfoModel.getOfferPrice())));

        if (getDiscount(bookInfoModel.getListPrice(), bookInfoModel.getOfferPrice()).isEmpty()) {
            tveBookDiscount.setVisibility(View.GONE);
        }
        else {
            tveBookDiscount.setVisibility(View.VISIBLE);
            tveBookDiscount.setText(String.format("%s off", getDiscount(bookInfoModel.getListPrice(), bookInfoModel.getOfferPrice())));
        }

        tvTestSeriesListPrice.setText(String.format("₹%s", getParsedPrice(bookInfoModel.getTestsListprice())));
        tvTestSeriesListPrice.setPaintFlags(tvTestSeriesListPrice.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);

        tvTestSeriesOfferPrice.setText(String.format("₹%s", getParsedPrice(bookInfoModel.getTestsPrice())));

        if (getDiscount(bookInfoModel.getTestsListprice(), bookInfoModel.getTestsPrice()).isEmpty()) {
            tvTestSeriesDiscount.setVisibility(View.GONE);
        }
        else {
            tvTestSeriesDiscount.setVisibility(View.VISIBLE);
            tvTestSeriesDiscount.setText(String.format("%s off", getDiscount(bookInfoModel.getTestsListprice(), bookInfoModel.getTestsPrice())));
        }*/

        dialog.show();
    }

    private String getParsedPrice(String price) {
        if (price.contains(".0") || price.contains(".00")) {
            price = price.substring(0, price.indexOf("."));
        }
        return price;
    }

    private String getDiscount(String listPrice, String offerPrice) {
        if (listPrice == null || offerPrice == null) {
            return "";
        }
        if (listPrice.isEmpty() && offerPrice.isEmpty()) {
            return "";
        }
        else if (!listPrice.isEmpty() && offerPrice.isEmpty()) {
            return "";
        }
        else if (listPrice.isEmpty()) {
            return "";
        }
        else {
            float discount;
            float listPriceInt = Float.parseFloat(listPrice);
            float offerPriceInt = Float.parseFloat(offerPrice);
            if (listPriceInt > 100) {
                discount = listPriceInt - offerPriceInt;
                return "Rs " + (int) discount;
            }
            else {
                discount = (((listPriceInt - offerPriceInt) / listPriceInt) * 100);
                int finalDiscount = Math.round(discount);
                if (finalDiscount == 0) {
                    return "";
                }
                else {
                    return finalDiscount + "%";
                }
            }
        }
    }

    private void showSuccessAnim(String message) {
        priceParent.setVisibility(View.GONE);
        addToCartLoadingAnim.setVisibility(View.GONE);
        addToCardResult.setVisibility(View.VISIBLE);
        addToCartFailureAnim.setVisibility(View.GONE);
        addToCartSuccessAnim.setVisibility(View.VISIBLE);
        addToCartPostResult.setVisibility(View.VISIBLE);
        addToCartResultTxt.setText(message);
    }

    private void showLoadingAnim() {
        priceParent.setVisibility(View.GONE);
        addToCardResult.setVisibility(View.VISIBLE);
        addToCartFailureAnim.setVisibility(View.GONE);
        addToCartSuccessAnim.setVisibility(View.GONE);
        addToCartLoadingAnim.setVisibility(View.VISIBLE);
        addToCartPostResult.setVisibility(View.GONE);
        addToCartResultTxt.setText("Adding to Cart...");
    }

    private void showFailedAnim() {
        priceParent.setVisibility(View.GONE);
        addToCartLoadingAnim.setVisibility(View.GONE);
        addToCardResult.setVisibility(View.VISIBLE);
        addToCartSuccessAnim.setVisibility(View.GONE);
        addToCartFailureAnim.setVisibility(View.VISIBLE);
        addToCartPostResult.setVisibility(View.GONE);
        addToCartResultTxt.setText("There was a problem while adding to cart. Please try again later");
    }

    /*private void addBookToCart(String bookId, String bookType, Activity activity) {
        WSBookStore bookStore = new WSBookStore();
        bookStore.addBookToCart(bookId, bookType, new WSResponseCallback<JSONObject>() {
            @Override
            public void onWSResultSuccess(JSONObject response, int responseCode) {
                String status = response.optString("status");
                if ("OK".equalsIgnoreCase(status)) {
                    int count = WonderPublishApplication.getCartCount();
                    WonderPublishApplication.setCartCount(count + 1);

                    if (eBookPurchaseType.equalsIgnoreCase("eBook")) {
                        message = "eBook has been successfully added to your cart";
                    }
                    else {
                        message = "Test Series has been successfully added to your cart";
                    }
                }
                if ("Already exist".equalsIgnoreCase(status)) {
                    if (eBookPurchaseType.equalsIgnoreCase("eBook")) {
                        message = "This eBook is already in your cart";
                    }
                    else {
                        message = "This Test Series is already in your cart";
                    }
                }
                if ("Book already purchased".equalsIgnoreCase(status)) {
                    if (eBookPurchaseType.equalsIgnoreCase("eBook")) {
                        message = "You have already purchased this eBook";
                    }
                    else {
                        message = "You have already purchased this Test Series";
                    }

                    goToCartBtn.setText("Go to Library");
                }

                if (activity instanceof WSLandingActivity) {
                    ((WSLandingActivity) activity).updateShoppingCartIcon();
                }
                else if (activity instanceof ChaptersListActivity) {
                    ((ChaptersListActivity) activity).updateShoppingCartIcon();
                }

                showSuccessAnim(message);
            }

            @Override
            public void onWSResultFailed(String resString, int responseCode) {
                showFailedAnim();
            }
        });
    }*/
}
