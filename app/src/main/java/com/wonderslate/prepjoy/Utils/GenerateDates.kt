package com.wonderslate.prepjoy.Utils


import android.os.Build
import android.text.format.DateUtils
import java.time.LocalDate
import java.time.LocalDateTime
import org.json.JSONObject
import java.text.ParseException
import java.text.SimpleDateFormat
import java.time.DayOfWeek
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters
import java.util.*
import kotlin.math.abs


object GenerateDates{

    private val dMyFormatter = SimpleDateFormat("dd-MM-yyyy")
    private val yMdFormatter = SimpleDateFormat("yyyy-MM-dd")

    fun generateBetweenDates(dateString1: String, dateString2: String): List<String> {
        val dates: ArrayList<String> = ArrayList<String>()
        var date1: Date? = null
        var date2: Date? = null
        try {
            date1 = yMdFormatter.parse(dateString1)
            date2 = yMdFormatter.parse(dateString2)
        } catch (e: ParseException) {
            e.printStackTrace()
        }
        val cal1: Calendar = Calendar.getInstance()
        cal1.setTime(date1)
        val cal2: Calendar = Calendar.getInstance()
        cal2.setTime(date2)
        while (!cal1.after(cal2)) {
            //dates.add(cal1.getTime())
            val dateFormat = SimpleDateFormat("dd-MMM-yyyy")
            val formattedDate = dateFormat.format(cal1.getTime())
            dates.add(formattedDate)
            cal1.add(Calendar.DATE, 1)
        }
        return dates
    }


    fun generateBetweenWeeks(startDate: String, endDate: String): List<String> {
        val weeks = ArrayList<String>()
        val start = DateTime(startDate)
        val end = DateTime(endDate)
        val totalWeeks = abs(Weeks.weeksBetween(start, end).weeks)
        val from = start.toLocalDateTime()
        for (i in 0..totalWeeks) {
            val startOfLastWeek = from.minusWeeks(i).dayOfWeek().withMinimumValue().minusDays(1)
            val endOfLastWeek = startOfLastWeek.plusDays(6)
            val weekStart = startOfLastWeek.toString().split("T")[0]
            val weekEnd = endOfLastWeek.toString().split("T")[0]
            weeks.add("$weekStart/$weekEnd")
        }
        weeks.removeAt(0)
        return weeks
    }

    fun generateBetweenMonths(startDate: String, endDate: String): List<String> {
        val months = ArrayList<String>()
        val monthsFrom = yMdFormatter.parse(startDate)
        val monthsTo = yMdFormatter.parse(endDate)
        val startCalendar = Calendar.getInstance().also { it.time = monthsFrom }
        while (startCalendar.time.time <= monthsTo.time) {
            val month = LocalDate(startCalendar.time).withDayOfMonth(1)
            months.add(dMyFormatter.format(month.toDate()))
            startCalendar.add(Calendar.MONTH, 1)
        }
        //months.removeAt(0)
        return months
    }

    fun findPreviousWeek(week: String): String {
        val currentWeek = LocalDateTime(dMyFormatter.parse(week))
        val previousWeek = currentWeek.minusWeeks(0).dayOfWeek().withMinimumValue().minusDays(1)
        val res = dMyFormatter.format(yMdFormatter.parse(previousWeek.toString().split("T")[0]))
        return res
    }

    fun findNextWeek(week: String): String {
        val currentWeek = LocalDateTime(dMyFormatter.parse(week))
        val nextWeek = currentWeek.plusWeeks(2).dayOfWeek().withMinimumValue().minusDays(1)
        val res = dMyFormatter.format(yMdFormatter.parse(nextWeek.toString().split("T")[0]))
        return res
    }

    fun findNextMonth(month: String): String {
        val currentMonth = LocalDateTime(dMyFormatter.parse(month))
        val nextWeek = currentMonth.plusMonths(1).dayOfMonth().withMinimumValue()
        return dMyFormatter.format(yMdFormatter.parse(nextWeek.toString().split("T")[0]))
    }

    fun findPreviousMonth(month: String): String {
        val currentMonth = LocalDateTime(dMyFormatter.parse(month))
        val previousMonth = currentMonth.minusMonths(1).dayOfMonth().withMinimumValue()
        return dMyFormatter.format(yMdFormatter.parse(previousMonth.toString().split("T")[0]))
    }

    fun findDay(currentDate: String, sequence: Int): String {
        var nextDate = ""
        val c: Calendar = Calendar.getInstance()
        c.setTime(dMyFormatter.parse(currentDate))
        if (sequence == 1) {
            c.add(Calendar.DATE, -1)
            nextDate = dMyFormatter.format(c.getTime())
        } else if (sequence == 2) {
            c.add(Calendar.DATE, 1)
            nextDate = dMyFormatter.format(c.getTime())
        }
        return nextDate
    }

    //region Validation
    fun validateMonth(selectedMonth: String, startAndEndDate: String): String {
        var previosVal = ""
        var nextVal = ""
        if (startAndEndDate.isNotBlank()) {
            val dateJson = JSONObject(startAndEndDate)
            val start = dateJson.getString("latestDate")
            val end = dateJson.getString("startingDate")
            val months = generateBetweenMonths(end, start)
            if (months.isNotEmpty()) {
                for (i in 0..months.size) {
                    if (i < months.size && months[i].contains(selectedMonth)) {
                        when (i) {
                            0 -> {
                                previosVal = "no"
                                nextVal = "yes"
                            }
                            months.size - 1 -> {
                                previosVal = "yes"
                                nextVal = "no"
                            }
                            else -> {
                                previosVal = "yes"
                                nextVal = "yes"
                            }
                        }
                    }
                }
            }
        }
        return "$previosVal-$nextVal"
    }

    fun validateWeek(selectedWeek: String, startAndEndDate: String): String {
        var previosVal = ""
        var nextVal = ""
        if (startAndEndDate.isNotBlank()) {
            val dateJson = JSONObject(startAndEndDate)
            val formatter = SimpleDateFormat("yyyy-MM-dd")
            val parser = SimpleDateFormat("dd-MM-yyyy")
            val selected = formatter.format(parser.parse(selectedWeek))
            val start = dateJson.getString("latestDate")
            val end = dateJson.getString("startingDate")
            val weeks = generateBetweenWeeks(start, end)
            if (weeks.isNotEmpty()) {
                for (i in 0..weeks.size) {
                    if (i < weeks.size && weeks[i].contains(selected)) {
                        when (i) {
                            0 -> {
                                previosVal = "yes"
                                nextVal = "no"
                            }
                            weeks.size - 1 -> {
                                previosVal = "no"
                                nextVal = "yes"
                            }
                            else -> {
                                previosVal = "yes"
                                nextVal = "yes"
                            }
                        }
                    }
                }
            }
        }
        return "$previosVal-$nextVal"
    }

    fun checkifDateExist(selectedDate: String, startAndEndDate: String): String {
        var previosVal = ""
        var nextVal = ""
        try {
            if (startAndEndDate != null) {
                val jsonObj = JSONObject(startAndEndDate)
                val startDate = jsonObj.optString("latestDate")
                val endDate = jsonObj.optString("startingDate")

                try {
                    val start: Date = SimpleDateFormat("dd-MM-yyyy", Locale.ENGLISH).parse(selectedDate)
                    val end: Date = SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH).parse(endDate)
                    when {
                        start > end -> {
                            // println("start is after end")
                            previosVal = "yes"
                        }
                        start < end -> {
                            previosVal = "no"
                            // println("start is before end")
                        }
                        start.compareTo(end) == 0 -> {
                            previosVal = "no"
                            println("start is equal to end")
                        }
                    }
                } catch (e: android.net.ParseException) {
                    e.printStackTrace()
                }

                try {
                    val start: Date = SimpleDateFormat("dd-MM-yyyy", Locale.ENGLISH).parse(selectedDate)
                    val end: Date = SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH).parse(startDate)
                    when {
                        start > end -> {
                            nextVal = "no"
                        }
                        start < end -> {
                            nextVal = "yes"
                        }
                        start.compareTo(end) == 0 -> {
                            nextVal = "no"
                        }
                    }
                } catch (e: android.net.ParseException) {
                    e.printStackTrace()
                }

            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return "$previosVal-$nextVal"
    }
    //endregion

    fun getHumanReadableTime(time: Long): String {
        var humanReadableTime = DateUtils.getRelativeTimeSpanString(
            Date(time).time,
            Calendar.getInstance().timeInMillis,
            DateUtils.MINUTE_IN_MILLIS
        ) as String
        if (humanReadableTime.contains("1 Jan 1970", true)) {
            humanReadableTime = DateUtils.getRelativeTimeSpanString(
                Date(System.currentTimeMillis()).time,
                Calendar.getInstance().timeInMillis,
                DateUtils.MINUTE_IN_MILLIS
            ) as String
        }
        return humanReadableTime
    }

    //For quiz contest
    //Get the date of previous Saturday
    //Quiz starts on Sunday to Saturday -- midnight to 12 am
    fun getPreviousSaturday(): String {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            java.time.LocalDate.now().with(TemporalAdjusters.previous(DayOfWeek.SATURDAY)).toString()
        } else {
            org.threeten.bp.LocalDate.now().with(org.threeten.bp.temporal.TemporalAdjusters.previous(org.threeten.bp.DayOfWeek.SATURDAY)).toString()
        }
    }
}

