package com.wonderslate.prepjoy.Utils;

import com.wonderslate.data.models.QA;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class QAJSONParser {

    public static List<QA> parseQAList(String jsonResponse) {
        List<QA> qaList = new ArrayList<>();

        try {
            JSONObject jsonObject = new JSONObject(jsonResponse);
            JSONArray jsonArray = jsonObject.getJSONArray("qaList");

            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject qaObject = jsonArray.getJSONObject(i);

                QA qa = new QA();
                qa.setId(qaObject.getInt("id"));
                qa.setQuestion(qaObject.getString("question"));
                qa.setAnswer(qaObject.getString("answer"));
                qa.setDifficultyLevel(qaObject.getString("difficultyLevel"));

                qaList.add(qa);
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }

        return qaList;
    }

    public static List<QA> parseQnA(String jsonResponse) {
        List<QA> qnaList = new ArrayList<>();

        try {
            JSONObject jsonObject = new JSONObject(jsonResponse);
            JSONArray jsonArray = jsonObject.getJSONArray("qna");

            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject qnaObject = jsonArray.getJSONObject(i);

                QA qna = new QA();
                qna.setId(qnaObject.getInt("id"));
                qna.setQuestion(qnaObject.getString("question"));
                qna.setAnswer(qnaObject.getString("answer"));
                qna.setDifficultyLevel(qnaObject.getString("difficultyLevel"));

                qnaList.add(qna);
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }

        return qnaList;
    }
}

