package com.wonderslate.prepjoy.news;

import static java.net.HttpURLConnection.HTTP_OK;

import android.util.Log;

import com.android.volley.Request;
import com.wonderslate.data.interfaces.WSCallback;
import com.wonderslate.data.network.VolleyHelper;
import com.wonderslate.data.network.WSAPIManager;
import com.wonderslate.data.network.Wonderslate;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;

public class NewsService {

    public void getNewsPreferenceList(final WSCallback wsCallback) {
        try {
            String url =  WSAPIManager.getInstance().getServiceURL(WSAPIManager.SERVICE_GET_AVAILABLE_NEWS_PREFERENCE);
            VolleyHelper.getInstance(Wonderslate.getInstance().getContext()).sendRequestToApi(Request.Method.GET, url, null, new VolleyHelper.VolleyCallback() {
                @Override
                public void onSuccessResponse(String result, int responseCode, boolean error) {
                    if (responseCode == HTTP_OK) {
                        try {
                            wsCallback.onWSResultSuccess(new JSONObject(result), responseCode);
                        } catch (JSONException e) {
                            Log.e("NewsService", e.getMessage());
                        }
                    }
                }

                @Override
                public void onErrorResponse(String result, int responseCode) {
                    wsCallback.onWSResultFailed(result, responseCode);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void updateUserNewsPrefs(String selectedLanguages, String selectedSources, final WSCallback wsCallback) {
        try {
            HashMap<String,String> params = new HashMap<>();
            params.put("languages", selectedLanguages);
            params.put("newsSourceIds", selectedSources);
            String url =  WSAPIManager.getInstance().getServiceURL(WSAPIManager.SERVICE_UPDATE_USER_NEWS_PREFERENCE, params);
            VolleyHelper.getInstance(Wonderslate.getInstance().getContext()).sendRequestToApi(Request.Method.GET, url, null, new VolleyHelper.VolleyCallback() {
                @Override
                public void onSuccessResponse(String result, int responseCode, boolean error) {
                    if (responseCode == HTTP_OK) {
                        try {
                            wsCallback.onWSResultSuccess(new JSONObject(result), responseCode);
                        } catch (JSONException e) {
                            Log.e("NewsService", e.getMessage());
                        }
                    }
                }

                @Override
                public void onErrorResponse(String result, int responseCode) {
                    wsCallback.onWSResultFailed(result, responseCode);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void updateNewsLangPrefs(String selectedLangs, final WSCallback wsCallback) {
        try {
            HashMap<String,String> params = new HashMap<>();
            params.put("languages", selectedLangs);
            String url =  WSAPIManager.getInstance().getServiceURL(WSAPIManager.SERVICE_ADD_NEWS_LANGUAGE_PREFERENCE, params);
            VolleyHelper.getInstance(Wonderslate.getInstance().getContext()).sendRequestToApi(Request.Method.GET, url, null, new VolleyHelper.VolleyCallback() {
                @Override
                public void onSuccessResponse(String result, int responseCode, boolean error) {
                    if (responseCode == HTTP_OK) {
                        try {
                            wsCallback.onWSResultSuccess(new JSONObject(result), responseCode);
                        } catch (JSONException e) {
                            Log.e("NewsService", e.getMessage());
                        }
                    }
                }

                @Override
                public void onErrorResponse(String result, int responseCode) {
                    wsCallback.onWSResultFailed(result, responseCode);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void updateNewsSourcePrefs(String selectedSource, final WSCallback wsCallback) {
        try {
            HashMap<String,String> params = new HashMap<>();
            params.put("newsSourceIds", selectedSource);
            String url =  WSAPIManager.getInstance().getServiceURL(WSAPIManager.SERVICE_ADD_NEWS_SOURCE_PREFERENCE, params);
            VolleyHelper.getInstance(Wonderslate.getInstance().getContext()).sendRequestToApi(Request.Method.POST, url, null, new VolleyHelper.VolleyCallback() {
                @Override
                public void onSuccessResponse(String result, int responseCode, boolean error) {
                    if (responseCode == HTTP_OK) {
                        try {
                            wsCallback.onWSResultSuccess(new JSONObject(result), responseCode);
                        } catch (JSONException e) {
                            Log.e("NewsService", e.getMessage());
                        }
                    }
                }

                @Override
                public void onErrorResponse(String result, int responseCode) {
                    wsCallback.onWSResultFailed(result, responseCode);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
