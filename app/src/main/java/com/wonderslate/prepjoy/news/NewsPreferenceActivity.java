package com.wonderslate.prepjoy.news;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;

import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.wonderslate.prepjoy.R;

public class NewsPreferenceActivity extends AppCompatActivity {
    String navMode =  "";
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_news_preference);
        init();
    }
    private void init() {
        NewsPreferenceFragment newsPreferenceFragment = NewsPreferenceFragment.newInstance("", "");
        getSupportFragmentManager().beginTransaction().replace(R.id.news_preference, newsPreferenceFragment).commit();
        if (Build.VERSION.SDK_INT >= 23) {
            Window window = this.getWindow();
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setNavigationBarColor(ContextCompat.getColor(this, R.color.primary_bg_dark));
            getWindow().setStatusBarColor(ContextCompat.getColor(this, R.color.primary_bg_dark));
            View decorView = getWindow().getDecorView();
            decorView.setSystemUiVisibility(decorView.getSystemUiVisibility() & ~View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING);
        }
        if( getIntent().hasExtra("Mode"))
            navMode = getIntent().getStringExtra("Mode");

    }
    @Override
    public boolean onSupportNavigateUp() {
        finish();
        return false;
    }

    @Override
    public void onBackPressed() {
        if (navMode.equalsIgnoreCase("change")) {
            Intent feedDetailsIntent = new Intent(this, NewsListActivity.class);
            startActivity(feedDetailsIntent);
            finish();
        }
        else
        {
            super.onBackPressed();
            finish();
        }
    }
}