package com.wonderslate.prepjoy.news;

import static com.wonderslate.prepjoy.news.NewsConstants.SELECTED_FEED_PUB_KEY;
import static com.wonderslate.prepjoy.news.NewsConstants.SELECTED_FEED_PUB_KEY_NAME;
import static com.wonderslate.prepjoy.news.NewsConstants.SELECTED_FEED_PUB_KEY_POSITION;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.os.AsyncTask;
import android.os.Bundle;

import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.wang.avi.AVLoadingIndicatorView;
import com.wonderslate.android.module.rssmanager.Channel;
import com.wonderslate.android.module.rssmanager.RSS;
import com.wonderslate.android.module.rssmanager.RssReader;
import com.google.android.material.snackbar.Snackbar;
import com.wonderslate.prepjoy.R;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * A simple {@link Fragment} subclass.
 * Use the {@link PaperWiseFrag#newInstance} factory method to
 * create an instance of this fragment.
 */
public class PaperWiseFrag extends Fragment implements PaperWiseNewsAdapter.ItemClickListener, RSSHandlerCallback {

    // TODO: Rename parameter arguments, choose names that match
    // the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
    private static final String ARG_PARAM1 = "param1";
    private static final String ARG_PARAM2 = "param2";

    // TODO: Rename and change types of parameters
    private String mParam1;
    private String mParam2;

    View view;

    List<NewsSource> feedList;
    RecyclerView recyclerView;
    AVLoadingIndicatorView mergedNewsLoader;
    static int selectedNewsPubPosition;
    List<NewsSource> selectedNews = new ArrayList<>();
    static NewsListActivity newsListActivity;


    public PaperWiseFrag() {
        // Required empty public constructor
    }

    /**
     * Use this factory method to create a new instance of
     * this fragment using the provided parameters.
     *
     * @param param1 Parameter 1.
     * @param param2 Parameter 2.
     * @return A new instance of fragment PaperWiseFrag.
     */
    // TODO: Rename and change types and number of parameters
    public static PaperWiseFrag newInstance(String param1, String param2) {
        PaperWiseFrag fragment = new PaperWiseFrag();
        Bundle args = new Bundle();
        args.putString(ARG_PARAM1, param1);
        args.putString(ARG_PARAM2, param2);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            mParam1 = getArguments().getString(ARG_PARAM1);
            mParam2 = getArguments().getString(ARG_PARAM2);
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        view = inflater.inflate(R.layout.fragment_paper_wise, container, false);
        feedList = NewsListActivity.getSelectedNewsSource();
        recyclerView = view.findViewById(R.id.paper_wise_list);
        mergedNewsLoader = view.findViewById(R.id.mergedNewsLoader);
        RecyclerView.LayoutManager layoutManagerSource = new GridLayoutManager(getContext(), 2);
        recyclerView.setLayoutManager(layoutManagerSource);

        PaperWiseNewsAdapter newsSourcePrefAdapter = new PaperWiseNewsAdapter(getActivity(), feedList);
        newsSourcePrefAdapter.setClickListener(this);
        recyclerView.setAdapter(newsSourcePrefAdapter);
        return view;
    }

    @Override
    public void onItemClick(View view, int position) {
        selectedNews.clear();
        selectedNewsPubPosition = position;
        selectedNews.add(feedList.get(position));
        new getRSSData().executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR) ;

    }

    public void callRssHandler(List<NewsSource> userSelectedNewsSource) {
        Snackbar.make(view, "Fetching & preparing news items for you...", Snackbar.LENGTH_LONG)
                .show();

        //Call New Handler here
        new RSSHandler().handleRSSFeeds(userSelectedNewsSource, this);
    }

    @Override
    public void onSuccess(List<Channel.Item> items) {
        mergedNewsLoader.smoothToHide();
        TemporaryDataHolder.setRssItemList(items);
        Intent feedDetailsIntent = new Intent(getContext(), PubFeedDetails.class);
        feedDetailsIntent.putExtra(SELECTED_FEED_PUB_KEY, feedList.get(selectedNewsPubPosition).getRssFeed());
        feedDetailsIntent.putExtra(SELECTED_FEED_PUB_KEY_NAME, feedList.get(selectedNewsPubPosition).getName());
        feedDetailsIntent.putExtra(SELECTED_FEED_PUB_KEY_POSITION, selectedNewsPubPosition);
        startActivity(feedDetailsIntent);
    }

    @Override
    public void onFailure(String message) {
        try {
            CustomDialogGenerator customDialogGenerator = new CustomDialogGenerator(getActivity()).prepareNewsErrorDialog(getString(R.string.prepjoy_news_title),
                    String.format(getString(R.string.news_error_feed_error), feedList.get(selectedNewsPubPosition).getName()), new CustomDialogActionListener() {
                @Override
                public void onPositiveAction(Dialog dialog) {
                    Intent openPrefsIntent = new Intent(getActivity(), NewsPreferenceActivity.class);
                    openPrefsIntent.putExtra("Mode","change");
                    startActivity(openPrefsIntent);
                    dialog.dismiss();
                    getActivity().finish();
                }

                @Override
                public void onNegativeAction(Dialog dialog) {
                    dialog.dismiss();
                    getActivity().finish();
                }
            });
            customDialogGenerator.generateNewsErrorDialog();
        }
        catch (Exception ex) {
            Snackbar.make(view, "No news available at this moment. Please try changing your news preference.", Snackbar.LENGTH_INDEFINITE)
                    .setAction("OK", new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            Intent openPrefsIntent = new Intent(getActivity(), NewsPreferenceActivity.class);
                            openPrefsIntent.putExtra("Mode","change");
                            startActivity(openPrefsIntent);
                            getActivity().finish();
                        }
                    }).show();
        }
    }

    public class getRSSData extends AsyncTask<Void,Void,Void>
    {
        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            mergedNewsLoader.smoothToShow();
        }

        @Override
        protected Void doInBackground(Void... voids) {
            callRssHandler(selectedNews);
            return null;
        }

        @Override
        protected void onPostExecute(Void unused) {

        }
    }
}