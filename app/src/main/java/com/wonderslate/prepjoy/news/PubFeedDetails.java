package com.wonderslate.prepjoy.news;

import static androidx.browser.customtabs.CustomTabsIntent.SHARE_STATE_OFF;
import static androidx.browser.customtabs.CustomTabsService.ACTION_CUSTOM_TABS_CONNECTION;
import static com.wonderslate.prepjoy.news.NewsConstants.SELECTED_FEED_PUB_KEY;
import static com.wonderslate.prepjoy.news.NewsConstants.SELECTED_FEED_PUB_KEY_NAME;
import static com.wonderslate.prepjoy.news.NewsConstants.SELECTED_FEED_PUB_KEY_POSITION;
import static com.wonderslate.prepjoy.news.NewsConstants.SELECTED_PUB_FEED_URL_KEY;

import androidx.appcompat.app.AppCompatActivity;
import androidx.browser.customtabs.CustomTabColorSchemeParams;
import androidx.browser.customtabs.CustomTabsIntent;
import androidx.cardview.widget.CardView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.Color;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.RelativeLayout;

import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.wonderslate.android.module.rssmanager.Channel;
import com.google.android.material.snackbar.Snackbar;
import com.wonderslate.prepjoy.R;

import java.util.ArrayList;
import java.util.List;

public class PubFeedDetails extends AppCompatActivity implements PubFeedListAdapter.ItemClickListener, SwipeRefreshLayout.OnRefreshListener, RSSHandlerCallback, CustomListScrollListener {
    private String selectedPublisher;
    public String selectedPublisherName;
    List<Channel.Item> feedList;
    PubFeedListAdapter pubFeedListAdapter;
    RecyclerView recyclerView;
    CardView btnBack, settingsNewsPreference;
    SwipeRefreshLayout mSwipeRefreshLayout;
    int userSelectedNewsProvider;
    FloatingActionButton scrollToTop;
    RelativeLayout rrltBack;

    public PubFeedDetails() {}

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_pub_feed_details);

        selectedPublisher = getIntent().getStringExtra(SELECTED_FEED_PUB_KEY);
        selectedPublisherName = getIntent().getStringExtra(SELECTED_FEED_PUB_KEY_NAME);
        userSelectedNewsProvider = getIntent().getIntExtra(SELECTED_FEED_PUB_KEY_POSITION, 0);

        rrltBack = findViewById(R.id.rrltBack);
        settingsNewsPreference = findViewById(R.id.btnNewsPref);
        mSwipeRefreshLayout = findViewById(R.id.paperwise_feed_swipe_refresh);
        scrollToTop = findViewById(R.id.scroll_to_top_paper_wise);
        scrollToTop.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                recyclerView.getLayoutManager().scrollToPosition(0);
            }
        });

        scrollToTop.setVisibility(View.GONE);
        settingsNewsPreference.setOnClickListener(view -> {
            //Open the preference screen
            Intent openPrefsIntent = new Intent(PubFeedDetails.this, NewsPreferenceActivity.class);
            startActivity(openPrefsIntent);
            finish();
        });
        rrltBack.setOnClickListener(v -> {
            onBackPressed();
            finish();
        });

        mSwipeRefreshLayout.setOnRefreshListener(this);
        mSwipeRefreshLayout.setColorSchemeResources(R.color.colorPrimary,
                android.R.color.holo_green_dark,
                android.R.color.holo_orange_dark,
                android.R.color.holo_blue_dark);

        initFeedList();
        if (Build.VERSION.SDK_INT >= 23) {
            Window window = this.getWindow();
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setNavigationBarColor(ContextCompat.getColor(this, R.color.primary_bg_dark));
            getWindow().setStatusBarColor(ContextCompat.getColor(this, R.color.primary_bg_dark));
            View decorView = getWindow().getDecorView();
            decorView.setSystemUiVisibility(decorView.getSystemUiVisibility() & ~View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING);
        }
    }

    private void initFeedList() {
        feedList = new ArrayList<>();

        // set up the RecyclerView
        feedList = TemporaryDataHolder.getRssItemList();
        recyclerView = findViewById(R.id.feed_list);
        RecyclerView.LayoutManager layoutManager = new LinearLayoutManager(this);
        recyclerView.setLayoutManager(layoutManager);
        DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(recyclerView.getContext(), LinearLayoutManager.VERTICAL);
        recyclerView.addItemDecoration(dividerItemDecoration);

        pubFeedListAdapter = new PubFeedListAdapter(this, selectedPublisherName, this);
        pubFeedListAdapter.setClickListener(PubFeedDetails.this);
        pubFeedListAdapter.updateData(feedList);
        recyclerView.setAdapter(pubFeedListAdapter);
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // Inflate the menu; this adds items to the action bar if it is present.
        //getMenuInflater().inflate(R.menu.menu_feed_list, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Handle action bar item clicks here. The action bar will
        // automatically handle clicks on the Home/Up button, so long
        // as you specify a parent activity in AndroidManifest.xml.
        int id = item.getItemId();

        //noinspection SimplifiableIfStatement
        if (id == android.R.id.home) {
            finish();
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

    @Override
    public boolean onSupportNavigateUp() {
        return super.onSupportNavigateUp();
    }

    @Override
    public void onItemClick(View view, int position) {
        Snackbar.make(view, "Fetching feed details. Please wait.", Snackbar.LENGTH_LONG)
                .show();

        ArrayList<ResolveInfo> resolveInfos = getCustomTabsPackages(PubFeedDetails.this);

        //Helper.getReadableDates("");

        if (resolveInfos.size() > 0) {
            CustomTabsIntent.Builder builder = new CustomTabsIntent.Builder();
            int colorInt = Color.parseColor("#04001D");
            CustomTabColorSchemeParams defaultColors = new CustomTabColorSchemeParams.Builder()
                    .setToolbarColor(colorInt)
                    .build();
            builder.setDefaultColorSchemeParams(defaultColors);
            builder.setShowTitle(false);
            builder.setShareState(SHARE_STATE_OFF);
            builder.setUrlBarHidingEnabled(true);

            CustomTabsIntent customTabsIntent = builder.build();
            customTabsIntent.launchUrl(this, Uri.parse(feedList.get(position).getLink().trim()));
        }
        else {
            Intent feedDetailsIntent = new Intent(PubFeedDetails.this, FeedDetailsWebViewActivity.class);
            feedDetailsIntent.putExtra(SELECTED_PUB_FEED_URL_KEY, feedList.get(position).getLink().trim());
            feedDetailsIntent.putExtra(SELECTED_FEED_PUB_KEY, selectedPublisher);
            startActivity(feedDetailsIntent);
        }
    }

    public static ArrayList<ResolveInfo> getCustomTabsPackages(Context context) {
        PackageManager pm = context.getPackageManager();
        // Get default VIEW intent handler.
        Intent activityIntent = new Intent()
                .setAction(Intent.ACTION_VIEW)
                .addCategory(Intent.CATEGORY_BROWSABLE)
                .setData(Uri.fromParts("https", "", null));

        // Get all apps that can handle VIEW intents.
        List<ResolveInfo> resolvedActivityList = pm.queryIntentActivities(activityIntent, 0);
        ArrayList<ResolveInfo> packagesSupportingCustomTabs = new ArrayList<>();
        for (ResolveInfo info : resolvedActivityList) {
            Intent serviceIntent = new Intent();
            serviceIntent.setAction(ACTION_CUSTOM_TABS_CONNECTION);
            serviceIntent.setPackage(info.activityInfo.packageName);
            // Check if this package also resolves the Custom Tabs service.
            if (pm.resolveService(serviceIntent, 0) != null) {
                packagesSupportingCustomTabs.add(info);
            }
        }
        return packagesSupportingCustomTabs;
    }

    /**
     * Called when a swipe gesture triggers a refresh.
     */
    @Override
    public void onRefresh() {
        mSwipeRefreshLayout.setRefreshing(true);
        // Fetching data from server
        callRssHandler(true);
    }

    public void callRssHandler(boolean pageRefresh) {
        if (!pageRefresh) {
            Snackbar.make(recyclerView, "Fetching & preparing news items for you...", Snackbar.LENGTH_LONG)
                    .show();
        }

        List<NewsSource> userPrefsNewsSource = new ArrayList<>();
        for (NewsSource newsSource : TemporaryDataHolder.getUserPrefModel().getUserNewsSource()) {
            if (newsSource.isChecked()) {
                userPrefsNewsSource.add(newsSource);
            }
        }

        List<NewsSource> selectedNews = new ArrayList<>();
        selectedNews.add(userPrefsNewsSource.get(userSelectedNewsProvider));

        //Call New Handler here
        new RSSHandler().handleRSSFeeds(selectedNews, this);
    }

    @Override
    public void onSuccess(List<Channel.Item> items) {
        TemporaryDataHolder.setRssItemList(items);
        feedList.clear();
        feedList.addAll(items);
        pubFeedListAdapter.updateData(feedList);
        mSwipeRefreshLayout.setRefreshing(false);
    }

    @Override
    public void onFailure(String message) {
        mSwipeRefreshLayout.setRefreshing(false);
        try {
            CustomDialogGenerator customDialogGenerator = new CustomDialogGenerator(PubFeedDetails.this).prepareNewsErrorDialog(getString(R.string.prepjoy_news_title),
                    String.format(getString(R.string.news_error_feed_error), selectedPublisherName), new CustomDialogActionListener() {
                @Override
                public void onPositiveAction(Dialog dialog) {
                    Intent openPrefsIntent = new Intent(PubFeedDetails.this, NewsPreferenceActivity.class);
                    openPrefsIntent.putExtra("Mode","change");
                    startActivity(openPrefsIntent);
                    dialog.dismiss();
                    finish();
                }

                @Override
                public void onNegativeAction(Dialog dialog) {
                    dialog.dismiss();
                    finish();
                }
            });
            customDialogGenerator.generateNewsErrorDialog();
        }
        catch (Exception ex) {
            Snackbar.make(recyclerView, "No news available at this moment. Please try changing your news preference.", Snackbar.LENGTH_INDEFINITE)
                    .setAction("OK", new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            Intent openPrefsIntent = new Intent(PubFeedDetails.this, NewsPreferenceActivity.class);
                            openPrefsIntent.putExtra("Mode","change");
                            startActivity(openPrefsIntent);
                            finish();
                        }
                    }).show();
        }
    }

    @Override
    public void onListBottom() {
    }

    @Override
    public void onListTop() {

    }

    @Override
    public void showCustomView() {
        if (scrollToTop != null) {
            scrollToTop.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void hideCustomView() {
        if (scrollToTop != null) {
            scrollToTop.setVisibility(View.GONE);
        }
    }

    @Override
    public void callNextPage() {
    }
}