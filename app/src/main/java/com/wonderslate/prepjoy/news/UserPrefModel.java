package com.wonderslate.prepjoy.news;

import java.util.List;

public class UserPrefModel {
   List<NewsLanguage> userNewsLanguage;
   List<NewsSource> userNewsSource;

   public List<NewsLanguage> getUserNewsLanguage() {
      return userNewsLanguage;
   }

   public void setUserNewsLanguage(List<NewsLanguage> userNewsLanguage) {
      this.userNewsLanguage = userNewsLanguage;
   }

   public List<NewsSource> getUserNewsSource() {
      return userNewsSource;
   }

   public void setUserNewsSource(List<NewsSource> userNewsSource) {
      this.userNewsSource = userNewsSource;
   }
}
