package com.wonderslate.prepjoy.news;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

public class NewsPagerAdapter extends FragmentPagerAdapter {
   Context context;
   int totalTabs;
   public NewsPagerAdapter(Context c, FragmentManager fm, int totalTabs) {
      super(fm);
      context = c;
      this.totalTabs = totalTabs;
   }
   @NonNull
   @Override
   public Fragment getItem(int position) {
      switch (position) {
         case 0:
            return MergedNewsFrag.newInstance(context, "");
         case 1:
            return PaperWiseFrag.newInstance("", "");
         default:
            return null;
      }
   }
   @Override
   public int getCount() {
      return totalTabs;
   }
}
