package com.wonderslate.prepjoy.news;

import java.time.LocalDateTime;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class DateUtil {
   String formatRfc2822 = "EEE, dd MMM yyyy HH:mm:ss Z";
   String formatRfc2822v2 = "EEE, dd MMM yyyy hh:mm:ss Z";
   String formatRfc2822v3 = "EEE, dd MMM yyyy hh:mm:ssZ";
   String formatRfc2822v4 = "EEE, dd MMM yyyy hh:mm:ssZZZZ";
   String formatISO = "yyyy-MM-dd'T'HH:mm:ss'Z'";
   String formatLocal = "dd-MM-yyyy hh:mm:ss a";

   public String getCurrDate() {
      return new SimpleDateFormat("dd-MM-yyyy hh:mm:ss a", Locale.ENGLISH).format(new Date());
   }

   //converts rss publish date into a readable format
   public String getDate(String pubDate) throws ParseException {
      Date date = getDateObj(pubDate);
      return new SimpleDateFormat(formatLocal, Locale.ENGLISH).format(date);
   }

   //get Date object from pub date
   public Date getDateObj(String pubDate) throws ParseException {
      SimpleDateFormat pubDateFormat = new SimpleDateFormat(formatRfc2822, Locale.ENGLISH); //rss spec
      Date date = null;
      try {
         date = pubDateFormat.parse(pubDate);
      } catch (ParseException e) {
         try {
            pubDateFormat = new SimpleDateFormat(formatLocal, Locale.ENGLISH); //fallback
            date = pubDateFormat.parse(pubDate);
         }
         catch (ParseException ex) {
            try {
               pubDateFormat = new SimpleDateFormat(formatISO, Locale.ENGLISH);
               date = pubDateFormat.parse(pubDate);
            }
            catch (ParseException exception) {
               try {
                  DateTime dateTimeUTC = new DateTime(pubDate);
                  date = dateTimeUTC.toDate();
               }
               catch (Exception parseException) {
                  try {
                     pubDateFormat = new SimpleDateFormat(formatRfc2822v2, Locale.ENGLISH);
                     date = pubDateFormat.parse(pubDate);
                  }
                  catch (Exception excep) {
                     try {
                        pubDateFormat = new SimpleDateFormat(formatRfc2822v3, Locale.ENGLISH);
                        date = pubDateFormat.parse(pubDate);
                     }
                     catch (Exception excpe) {
                        pubDateFormat = new SimpleDateFormat(formatRfc2822v4, Locale.ENGLISH);
                        date = pubDateFormat.parse(pubDate);
                     }
                  }
               }
            }
         }
      }
      return date;
   }
}
