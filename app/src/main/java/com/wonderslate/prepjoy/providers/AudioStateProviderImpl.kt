package com.wonderslate.prepjoy.providers

// import com.wonderslate.prepjoy.ui.videos.AudioPlayerService
import com.ws.commons.interfaces.AudioPlayerStateProvider

class AudioStateProviderImpl :AudioPlayerStateProvider{
    override fun audioState(): <PERSON><PERSON><PERSON> {
       // AudioPlayerService functionality temporarily disabled for demo
       return false
    }

    override fun audioResId(): String? {
        // AudioPlayerService functionality temporarily disabled for demo
        return null
    }
}