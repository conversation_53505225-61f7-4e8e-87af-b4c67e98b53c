package com.wonderslate.prepjoy.providers

import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.ws.resources.data.interfaces.ReadingImageUpdateStatusProvider

class ReadingImageUpdateStatusProviderImpl(
    private val sharedPrefs: WonderPubSharedPrefs
): ReadingImageUpdateStatusProvider {
    override fun isSVGUpdated(resId: String): <PERSON><PERSON><PERSON> {
        return sharedPrefs.isSVGUpdated(resId)
    }

    override fun setSVGUpdated(resId: String, updated: Boolean) {
        sharedPrefs.setSVGUpdated(resId, updated)
    }

    override fun isImageReplaced(resId: String): Bo<PERSON><PERSON> {
        return sharedPrefs.isImageReplaced(resId)
    }

    override fun setImageReplaced(resId: String, updated: Boolean) {
        sharedPrefs.setImageReplaced(resId, updated)
    }
}