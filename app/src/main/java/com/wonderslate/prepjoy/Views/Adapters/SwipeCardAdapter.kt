package com.wonderslate.prepjoy.Views.Adapters

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import androidx.fragment.app.FragmentActivity
import androidx.viewpager.widget.PagerAdapter

import com.wonderslate.prepjoy.R
import com.wonderslate.prepjoy.ui.home.HomeFragment
import com.wonderslate.prepjoy.ui.reading_material.ReadingMaterialActivity
import com.wonderslate.prepjoy.ui.videos.VideoActivity
// Removed synthetic imports - using findViewById instead
import java.text.SimpleDateFormat
import java.util.*

class SwipeCardAdapter(dates: List<String>, myContext: FragmentActivity?,fragment:HomeFragment ) : PagerAdapter() {

    private var startEndDates: List<String>? = dates
    private var pfragment: HomeFragment = fragment
    var context: Context? = myContext

    private var layoutInflater: LayoutInflater? = LayoutInflater.from(myContext)


    override fun getCount(): Int {
        return startEndDates!!.size
    }

    override fun isViewFromObject(view: View, `object`: Any): Boolean {
        return view == `object`
    }

    override fun destroyItem(container: ViewGroup, position: Int, `object`: Any) {
        container.removeView(`object` as View)
    }

    override fun instantiateItem(container: ViewGroup, position: Int): Any {
        val view: View = layoutInflater!!.inflate(R.layout.layout_swipe_card, container, false)

        val textview = view.findViewById<View>(R.id.txtDateVal) as TextView
        val txtTodayQuiz = view.findViewById(R.id.txtTodayQuiz) as TextView
        val btnRead = view.findViewById(R.id.btnOption2) as Button
        val btnOption1 = view.findViewById(R.id.btnOption1) as Button
        val btnOption3 = view.findViewById(R.id.btnOption3) as Button

        txtTodayQuiz.text = dateTitle(startEndDates!![position])
        textview.text = (startEndDates!![position])
        container.addView(view)
        view.setOnClickListener {
            pfragment.showQuizOptionsForDate("23-09-2021")
        }
        btnOption1.setOnClickListener{

            pfragment.showQuizOptionsForDate("23-09-2021")

        }

        btnOption3.setOnClickListener{
            val intent = Intent(context, VideoActivity::class.java)
            intent.putExtra("inputDate", startEndDates!![position])
            context?.startActivity(intent)
        }

        btnRead.setOnClickListener {
            val intent = Intent(context, ReadingMaterialActivity::class.java)
            intent.putExtra("inputDate", startEndDates!![position])
            context?.startActivity(intent)
        }
        return view
    }

    @SuppressLint("SimpleDateFormat")
    private fun dateTitle(date: String): String {
        val sdf = SimpleDateFormat("dd-MMM-yyyy")

        return if (sdf.format(Date()).equals(date)) {
            "Today's quiz"
        } else "Quiz For"
    }
}