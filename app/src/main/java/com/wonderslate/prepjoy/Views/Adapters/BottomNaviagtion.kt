package com.wonderslate.prepjoy.Views.Adapters

import android.content.Context;
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import com.wonderslate.prepjoy.ui.home.HomeFragment
import com.wonderslate.prepjoy.ui.leaderboard.LeaderboardFragment
import com.wonderslate.prepjoy.ui.profile.ProfileFragment

class BottomNaviagtion(private val myContext: Context, fm: FragmentManager, internal var totalTabs: Int) : FragmentPagerAdapter(fm) {

    // this is for fragment tabs
    override fun getItem(position: Int): Fragment {
        when (position) {
            0 -> {
                //  val homeFragment: HomeFragment = HomeFragment()
                return HomeFragment()
            }
            1 -> {
                return LeaderboardFragment()
            }
            2 -> {
                // val movieFragment = MovieFragment()
                return ProfileFragment()
            }
            else -> return HomeFragment()
        }
    }

    // this counts total number of tabs
    override fun getCount(): Int {
        return totalTabs
    }
}
