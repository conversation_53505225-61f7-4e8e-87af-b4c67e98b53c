package com.wonderslate.prepjoy.Views.Adapters;

import android.content.Context;
import android.text.Spanned;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.wonderslate.prepjoy.R;
import com.wonderslate.prepjoy.Utils.MarkwonUtil;

import java.util.List;

import io.noties.markwon.Markwon;

public class MCQAdapter extends RecyclerView.Adapter<MCQAdapter.MCQViewHolder> {
    private final Context context;
    private List<String> mcqList;
    private static Markwon markwon;
    private final OnMCQActionListener mcqActionListener;

    // Interface for handling button clicks with position
    public interface OnMCQActionListener {
        void onGiveHintClicked(String mcqText, int position);
        void onExplainMCQClicked(String mcqText, int position);
        void onCreateSimilarMCQsClicked(String mcqText, int position);
        void onShowHistoryClicked(String mcqText, int position);
    }

    public MCQAdapter(Context context, List<String> mcqList, OnMCQActionListener listener) {
        this.context = context;
        this.mcqList = mcqList;
        this.mcqActionListener = listener;
    }

    @NonNull
    @Override
    public MCQViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_mcq, parent, false);
        return new MCQViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull MCQViewHolder holder, int position) {
        String mcq = mcqList.get(position);
        Log.d("MCQAdapter", "MCQ: " + mcq.replaceAll("https", "ht*tp").replaceAll("www", "ww*w"));
        Spanned markdown = MarkwonUtil.preprocessAndRenderNotations(markwon, mcq);
        MarkwonUtil.renderMarkdown(markwon, markdown, holder.txtQuestion);

        // Pass position to button listeners
        holder.bind(mcqActionListener, position);
    }

    @Override
    public int getItemCount() {
        return mcqList.size();
    }

    public void updateList(List<String> newList) {
        mcqList = newList;
        notifyDataSetChanged();
    }

    public static class MCQViewHolder extends RecyclerView.ViewHolder {
        TextView txtQuestion;
        Button btnGiveHint, btnExplainMCQ, btnCreateSimilar, btnShowHistory;

        public MCQViewHolder(@NonNull View itemView) {
            super(itemView);
            txtQuestion = itemView.findViewById(R.id.txtQuestion);
            markwon = MarkwonUtil.createMarkwonInstance(itemView.getContext(), txtQuestion.getTextSize());

            // Buttons
            btnGiveHint = itemView.findViewById(R.id.btnGiveHint);
            btnExplainMCQ = itemView.findViewById(R.id.btnExplainMCQ);
            btnCreateSimilar = itemView.findViewById(R.id.btnCreateSimilar);
            btnShowHistory = itemView.findViewById(R.id.btnShowHistory);
        }

        // Bind position for button click listeners
        public void bind(OnMCQActionListener listener, int position) {
            btnGiveHint.setOnClickListener(v ->
                    listener.onGiveHintClicked(txtQuestion.getText().toString(), position));

            btnExplainMCQ.setOnClickListener(v ->
                    listener.onExplainMCQClicked(txtQuestion.getText().toString(), position));

            btnCreateSimilar.setOnClickListener(v ->
                    listener.onCreateSimilarMCQsClicked(txtQuestion.getText().toString(), position));

            btnShowHistory.setOnClickListener(v ->
                    listener.onShowHistoryClicked(txtQuestion.getText().toString(), position));
        }
    }
}
