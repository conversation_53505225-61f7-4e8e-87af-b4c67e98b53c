package com.wonderslate.prepjoy.Views.Activity;


import static com.wonderslate.prepjoy.Utils.Utils.disableScreenShot;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.RemoteException;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.airbnb.lottie.LottieAnimationView;
import com.android.installreferrer.api.InstallReferrerClient;
import com.android.installreferrer.api.InstallReferrerStateListener;
import com.android.installreferrer.api.ReferrerDetails;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.firebase.dynamiclinks.FirebaseDynamicLinks;
import com.google.firebase.dynamiclinks.PendingDynamicLinkData;
import com.wonderslate.data.helper.ServerTimeHelper;
import com.wonderslate.data.interfaces.WSCallback;
import com.wonderslate.data.network.OpenUtils;
import com.wonderslate.data.network.ServerTimeCallBack;
import com.wonderslate.data.preferences.WonderPubSharedPrefs;
import com.wonderslate.prepjoy.R;
import com.wonderslate.prepjoy.Utils.InternetConnectionChecker;
import com.wonderslate.prepjoy.ui.chapters_list.ChaptersListAct;
import com.wonderslate.prepjoy.ui.dashboard.DashBoardActivity;
import com.wonderslate.prepjoy.ui.login.LoginActivity;
import com.wonderslate.prepjoy.ui.new_book_details.BookDetailsAct;
import com.wonderslate.prepjoy.ui.quiz_instruction.QuizInstructions;
import com.ws.book_details.data.models.BookDetails;
import com.ws.chapter_list.data.models.ChaptersListConfig;
import com.ws.resources.data.models.ReadingFragConfig;
import com.ws.resources.ui.reading.ReadingActivity;

import java.time.LocalDateTime;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class SplashActivity extends AppCompatActivity {

    private FrameLayout layoutServerIssue;
    private TextView textViewServerMesssge;
    private LottieAnimationView lottieAnimationView;
    private Button buttonClose, buttonOffline;
    private static final String AFFILIATION_KEY = "affiliation";
    private WonderPubSharedPrefs sharedPrefs;
    private InstallReferrerClient referrerClient;
    private String referrerUrl;
    private FirebaseAnalytics analytics;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_splash);
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }
        //check servers are working
        sharedPrefs = WonderPubSharedPrefs.getInstance(getApplicationContext());
        analytics = FirebaseAnalytics.getInstance(getApplicationContext());
        layoutServerIssue = findViewById(R.id.layout_server_issue);
        textViewServerMesssge = findViewById(R.id.textView_server_message);
        buttonClose = findViewById(R.id.button_close);
        buttonOffline = findViewById(R.id.button_offline);
        lottieAnimationView = findViewById(R.id.lottie_view);
        buttonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });

        buttonOffline.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                layoutServerIssue.setVisibility(View.GONE);
               // requestPermission();
                initScreens();
            }
        });
        checkInstallReferrer();
        InternetConnectionChecker internetConnectionChecker = new InternetConnectionChecker();
        if (internetConnectionChecker.isNetworkConnected(this)) {
            checkServers();
        }
        else
        {
            initScreens();
        }
        disableScreenShot(this);
    }

    private void initScreens()
    {
        validateLink();
    }

    //region Deep Link Validation
    private void validateLink() {
        FirebaseDynamicLinks.getInstance().
                getDynamicLink(getIntent()).
                addOnSuccessListener(this, this::onDynamicLinkSuccess).
                addOnFailureListener(this, this::errorHandler);
    }

    private void onDynamicLinkSuccess (final PendingDynamicLinkData deepLink) {
        if (deepLink != null && deepLink.getLink() != null) {
            redirectToDeepLinkLoader(deepLink.getLink());
        }
        else {
            redirectToScreen();
        }
    }

    private void findAffiliationCode(final PendingDynamicLinkData deepLink) {
        if (verifyCampaign(deepLink)) {
            if (verifyContent(deepLink.getLink())) {
                final String content = deepLink.getLink().getQueryParameter("utm_content");
                ServerTimeHelper.getNewInstance().getServerTime(new ServerTimeCallBack() {
                    @Override
                    public void onSuccess(@NonNull LocalDateTime time) {
                        sharedPrefs.setUserAffiliationTimeStamp(time.plusDays(7).toString());
                        sharedPrefs.setUserAffiliationContent(content);
                        Bundle bundle = new Bundle();
                        bundle.putString("SERVER_TIME_SUCCESS", time.toString());
                        bundle.putString("AFFILIATION_CODE", content);
                        analytics.logEvent("AFFILIATION_STORE_SUCCESS", bundle);
                    }

                    @Override
                    public void onFailed(String resString, int responseCode) {
                        Bundle bundle = new Bundle();
                        bundle.putString("SERVER_TIME_ERROR", resString);
                        bundle.putString("AFFILIATION_CODE", content);
                        analytics.logEvent("AFFILIATION_STORE_ERROR", bundle);
                    }
                });
            }
        }
        redirectToScreen();
    }

    private void errorHandler(Exception e) {
         redirectToScreen();
    }

    private boolean verifyCampaign(PendingDynamicLinkData linkData) {
        boolean result = false;
        if (null != linkData && null != linkData.getLink()) {
            Uri link = linkData.getLink();
            String linkString = null != link ? link.toString() : "";
            if (!linkString.isEmpty() && !linkString.equalsIgnoreCase("null")) {
                if (link.getQueryParameterNames().contains("utm_campaign")) {
                    String campaign = link.getQueryParameter("utm_campaign");
                    if (null != campaign &&
                            !campaign.isEmpty() &&
                            !campaign.equalsIgnoreCase("null") &&
                            campaign.equalsIgnoreCase(AFFILIATION_KEY)) {
                        result = true;
                    }
                }
            }
        }
        return result;
    }

    private boolean verifyContent(@NonNull Uri link) {
        boolean result = false;
        String utmContent = link.getQueryParameter("utm_content");
        if (null != utmContent &&
                !utmContent.isEmpty() &&
                !utmContent.equalsIgnoreCase("null")) {
            result = true;
        }
        return result;
    }
    //endregion

    private void redirectToDeepLinkLoader(Uri link) {
        Intent deepLinkIntent = new Intent(SplashActivity.this, DeepLinkReceiverActivity.class);
        deepLinkIntent.putExtra("deepLink", link.toString());
        startActivity(deepLinkIntent);
        finish();
    }

    private void redirectToScreen()
    {
        try {
            Intent recieveSharedLinkIntent = getIntent();
            String recieveSharedLinkAction = recieveSharedLinkIntent.getAction();
            if (sharedPrefs.getUserId().isEmpty()) {
                Intent login = new Intent(SplashActivity.this, LoginActivity.class);
                startActivity(login);
                finish();
            } else if (Intent.ACTION_SEND.equals(recieveSharedLinkAction) ) {
                if (getIntent().getExtras().containsKey("bookId"))
                {
                    int pBookid = getIntent().getExtras().getInt("bookId");
                    if(pBookid != 0)
                    {
                        startActivity(ChaptersListAct.createIntent(
                                this,
                                new ChaptersListConfig(
                                        pBookid,getIntent().getExtras().getBoolean("hasPreview"),
                                        "",
                                        "",
                                        false,true, ""
                                )
                        ));
                        overridePendingTransition(R.anim.slide_from_right, R.anim.slide_to_left);
                        finish();
                    }
                    else
                    {
                        navigateToDashboard();
                    }
                }
                else
                {
                    navigateToDashboard();
                }
            }
            else
            {
                navigateToDashboard();
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    private void navigateToDashboard() {
        Intent dashboard = new Intent(SplashActivity.this, DashBoardActivity.class);
        startActivity(dashboard);
        overridePendingTransition(R.anim.slide_from_right, R.anim.slide_to_left);
        finish();
    }
    //Check servers are working fine - control server
    private void checkServers() {
        try {
                final OpenUtils checkAppVersion = new OpenUtils();
                checkAppVersion.checkServersWorking(new WSCallback() {
                    @Override
                    public void onWSResultSuccess(JSONObject jsonObject, int responseCode) {
                        if (jsonObject != null && jsonObject.has("shouldContinue") && jsonObject.optString("shouldContinue") != null
                                && !jsonObject.optString("shouldContinue").equalsIgnoreCase("null")) {
                            if (jsonObject.optString("shouldContinue").equalsIgnoreCase("yes")) {
                                layoutServerIssue.setVisibility(View.GONE);
                               // requestPermission();
                                initScreens();
                            } else {
                                lottieAnimationView.setAnimation(R.raw.lottie_server);
                                layoutServerIssue.setVisibility(View.VISIBLE);
                                if (jsonObject.has("message") && jsonObject.optString("message") != null && !jsonObject.optString("message").equalsIgnoreCase("null") && !jsonObject.optString("message").isEmpty()) {
                                    textViewServerMesssge.setText("" + jsonObject.optString("message"));
                                } else {
                                    textViewServerMesssge.setText("We're facing technical difficulties. We'll be back soon. Sorry fro the inconvenience.");
                                }
                            }
                        } else {

                            layoutServerIssue.setVisibility(View.GONE);
                            initScreens();
                           // lottieAnimationView.setAnimation(R.raw.lottie_server);
                          //  layoutServerIssue.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onWSResultFailed(String resString, int responseCode) {
                       // lottieAnimationView.setAnimation(R.raw.lottie_server);
                       // layoutServerIssue.setVisibility(View.VISIBLE);
                        layoutServerIssue.setVisibility(View.GONE);
                        initScreens();
                    }
                });

        } catch (Exception e) {
            e.printStackTrace();
            layoutServerIssue.setVisibility(View.GONE);
            initScreens();
        }
    }

    private void checkInstallReferrer() {
        try {
            referrerClient = InstallReferrerClient.newBuilder(this).build();
            referrerClient.startConnection(new InstallReferrerStateListener() {
                @Override
                public void onInstallReferrerSetupFinished(int responseCode) {
                    switch (responseCode) {
                        case InstallReferrerClient.InstallReferrerResponse.OK:
                            // Connection established.
                            ReferrerDetails response = null;
                            try {
                                response = referrerClient.getInstallReferrer();
                                referrerUrl = response.getInstallReferrer();
                                String recreatedUTMUrl = "https://play.google.com/store/apps/details?" + referrerUrl;
                                if (Uri.parse(recreatedUTMUrl).getQueryParameter("utm_campaign") != null && !Uri.parse(recreatedUTMUrl).getQueryParameter("utm_campaign").equalsIgnoreCase("null") && !Uri.parse(recreatedUTMUrl).getQueryParameter("utm_campaign").isEmpty() && Uri.parse(recreatedUTMUrl).getQueryParameter("utm_campaign").equalsIgnoreCase("affiliation")) {
                                    //For Affiliation
                                    if (Uri.parse(recreatedUTMUrl).getQueryParameter("utm_content") != null &&
                                            !Uri.parse(recreatedUTMUrl).getQueryParameter("utm_content").equalsIgnoreCase("null") &&
                                            !Uri.parse(recreatedUTMUrl).getQueryParameter("utm_content").isEmpty()) {
                                        final String content = Uri.parse(recreatedUTMUrl).getQueryParameter("utm_content");
                                        ServerTimeHelper.getNewInstance().getServerTime(new ServerTimeCallBack() {
                                            @Override
                                            public void onSuccess(@NonNull LocalDateTime time) {
                                                sharedPrefs.setUserAffiliationTimeStamp(time.plusDays(7).toString());
                                                sharedPrefs.setUserAffiliationContent(content);
                                                Bundle bundle = new Bundle();
                                                bundle.putString("SERVER_TIME_SUCCESS", time.toString());
                                                bundle.putString("AFFILIATION_CODE", content);
                                                analytics.logEvent("AFFILIATION_STORE_SUCCESS_PLAY_STORE", bundle);
                                            }

                                            @Override
                                            public void onFailed(String resString, int responseCode) {
                                                Bundle bundle = new Bundle();
                                                bundle.putString("SERVER_TIME_ERROR", resString);
                                                bundle.putString("AFFILIATION_CODE", content);
                                                analytics.logEvent("AFFILIATION_STORE_ERROR_PLAY_STORE", bundle);
                                            }
                                        });
                                    }
                                }
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }
                            referrerClient.endConnection();

                            //Send to firebase
                            if (referrerUrl != null) {
                                Bundle bundle = new Bundle();
                                bundle.putString(FirebaseAnalytics.Param.SOURCE, "Source: " + Uri.parse(referrerUrl).getQueryParameter("utm_source"));
                                bundle.putString(FirebaseAnalytics.Param.MEDIUM, "Medium: " + Uri.parse(referrerUrl).getQueryParameter("utm_medium"));
                                bundle.putString(FirebaseAnalytics.Param.CAMPAIGN, "Campaign: " + Uri.parse(referrerUrl).getQueryParameter("utm_campaign"));
                                analytics.logEvent(FirebaseAnalytics.Event.CAMPAIGN_DETAILS, bundle);

                                Bundle bundle2 = new Bundle();
                                bundle2.putString(FirebaseAnalytics.Param.ITEM_ID, "Source: " + Uri.parse(referrerUrl).getQueryParameter("utm_source"));
                                bundle2.putString(FirebaseAnalytics.Param.ITEM_ID, "Medium: " + Uri.parse(referrerUrl).getQueryParameter("utm_medium"));
                                bundle2.putString(FirebaseAnalytics.Param.CONTENT_TYPE, "analytics");
                                analytics.logEvent("Campaigns", bundle);
                            }
                            break;
                        case InstallReferrerClient.InstallReferrerResponse.FEATURE_NOT_SUPPORTED:
                        case InstallReferrerClient.InstallReferrerResponse.SERVICE_UNAVAILABLE:
                            // Connection couldn't be established.
                            // API not available on the current Play Store app.
                            referrerClient.endConnection();
                            break;
                    }
                }

                @Override
                public void onInstallReferrerServiceDisconnected() {
                    // Try to restart the connection on the next request to
                    // Google Play by calling the startConnection() method.
                    referrerClient.endConnection();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}