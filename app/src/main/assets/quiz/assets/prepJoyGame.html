<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>PrepJoy</title>
    <meta name="theme-color" content="#2EBAC6"/>
    <meta name="author" content="P.R">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;400;500&display=swap"
          rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Righteous&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css"  crossorigin="anonymous" referrerpolicy="no-referrer" />

    <link rel="stylesheet"
          href="file:///android_asset/quiz/assets/stylesheets/prepJoy/bootstrap.min.css"/>
    <link rel="stylesheet"
          href="file:///android_asset/quiz/assets/stylesheets/prepJoy/animate.css"/>
    <link rel="stylesheet"
          href="file:///android_asset/quiz/assets/stylesheets/prepJoy/jquery-ui.css">
    <link rel="stylesheet" href="file:///android_asset/quiz/assets/stylesheets/prepJoy/prepJoy.css">
    <link rel="stylesheet" id="whTheme">
    <script src="file:///android_asset/quiz/assets/javascripts/jquery.min.js"></script>
    <script src="file:///android_asset/quiz/assets/javascripts/jquery-ui.js"></script>
    <script src="file:///android_asset/quiz/assets/javascripts/popper.min.js"></script>
    <script src="file:///android_asset/quiz/assets/javascripts/bootstrap.min.js"></script>
    <script src="file:///android_asset/quiz/assets/javascripts/swiper.js"></script>
</head>

<body>
<img src="file:///android_asset/quiz/assets/images/prepjoy/loader.gif" class="loader-submit"/>
<div class="sidebar d-none d-lg-block">
    <nav class="main-nav">
        <ul class="main-menu">
            <li class="menu-item active">
                <a href="#" class="menu-a" target="_blank">
                    <div class="menu-txt-hld">
                        <img src="/assets/prepjoy/prepjoy.jpg" class="prep-start img-responsive">
                    </div>
                </a>
            </li>
            <li class="menu-item active">
                <a href="#" class="menu-a" target="_blank">
                    <div class="menu-txt-hld">
                        <i class="material-icons">home</i>
                        <span class="menu-txt">Home</span>
                    </div>
                </a>
            </li>
            <li class="menu-item">
                <a href="#" class="menu-a" target="_blank">
                    <div class="menu-txt-hld">
                        <i class="material-icons">play_arrow</i>
                        <span class="menu-txt">Play</span>
                    </div>
                </a>
            </li>
            <li class="menu-item">
                <a href="#" class="menu-a" target="_blank">
                    <div class="menu-txt-hld">
                        <i class="material-icons">quiz</i>
                        <span class="menu-txt">Practice</span>
                    </div>
                </a>
            </li>
            <li class="menu-item">
                <a href="#" class="menu-a" target="_blank">
                    <div class="menu-txt-hld">
                        <i class="material-icons">assignment_late</i>
                        <span class="menu-txt">Test</span>
                    </div>
                </a>
            </li>
            <li class="menu-item">
                <a href="#" class="menu-a" target="_blank">
                    <div class="menu-txt-hld">
                        <i class="material-icons">leaderboard</i>
                        <span class="menu-txt">Leaderboard</span>
                    </div>
                </a>
            </li>
        </ul>
    </nav>
</div>
<div class="main-page ">

    <div class="container main-page__header">
        <div class="d-flex justify-content-between" style="margin-top:10px;">
            <i class="fa-solid fa-arrow-left-long" onclick=backToHome()></i>
        </div>
        <h2 class="mt-4 mcq-name">Group 18-Practice Set</h2>
    </div>
    <div class="container main-page__content">
        <div class="header_wrap">
            <div class="main-page__content-header">
                <h4><span  class="mr-2 total_questions">10</span>Questions</h4>
            </div>
            <div class="main-page__content-points">
                <div class="main-page__content-points__one points_card separator-1">
                    <p class="points_card-point">+1</p>
                    <p class="points_card-des">Correct</p>
                </div>
                <div class="main-page__content-points__two points_card separator-1">
                    <p class="points_card-point">0</p>
                    <p class="points_card-des">Incorrect</p>
                </div>
                <div class="main-page__content-points__three points_card">
                    <p class="points_card-point">0</p>
                    <p class="points_card-des">Skip</p>
                </div>
            </div>
            <div class="main-page__content-separator"></div>

            <div class="main-page__content-questionsRange">
                <div class="main-page__content-header">
                    <h4>Settings</h4>
                </div>
                <div class="main-page__content-questionsRange__language d-flex align-items-center ">
                    <p>Language</p>
                    <div class="radioBtns_wrapper d-flex align-items-center" id="languageRadioBtn" style="gap:10px;">
                    </div>
                </div>
                <div class="main-page__content-questionsRange__range d-flex align-items-center">
                    <p>Questions</p>
                    <div class="radioBtns_wrapper d-flex align-items-center" style="gap:10px;">
                        <div>
                            <input type="radio" id="all" name="questionsRange" value="allQuestion" checked>
                            <label for="all">All</label>
                        </div>
                        <div>
                            <input type="radio" id="limited" name="questionsRange" value="limitedQuestion">
                            <label for="limited">Select</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="d-none">
                <div class="customize-header lang-module d-flex align-items-center justify-content-between mt-3">
                    <select class="lang form-control" id="languageSelect" onchange="langSelect(this)">
                        <option value="">Select Language</option>
                        <option value="english">English</option>
                        <option class="option-hindi" value="hindi">Hindi</option>
                        <option class="option-kannada" value="kannada">Kannada</option>
                    </select>
                    <select class="questionsRangeOption form-control" id="questionsRangeOption">
                        <option value="">Select Questions Range</option>
                        <option value="allQuestion">Take All Questions</option>
                        <option value="limitedQuestion">Take limited Questions</option>
                    </select>
                </div>
                <div class="customize-header time-module mt-4 d-flex align-items-center justify-content-between">
                    <label>Time/Question:</label>
                    <select class="time-box form-control" id="timeSelect" onchange="timeSelect(this)">
                        <option value="15">15 seconds</option>
                        <option value="30">30 seconds</option>
                        <option value="45">45 seconds</option>
                        <option value="60">60 seconds</option>
                        <option value="75">75 seconds</option>
                        <option value="90">90 seconds</option>
                        <option value="105">105 seconds</option>
                        <option value="120">120 seconds</option>
                    </select>
                </div>
            </div>
            <div class="clock-wrapper">
                <div class="clock-header">
                    <div class="d-flex justify-content-between">
                        <h4>Change Time</h4>
                        <i class="material-icons" onclick="closeTimer()">close</i>
                    </div>
                </div>
                <div class="clock-explanation">
                    <div class="form-check">
                        <input class="form-check-input d-none" type="radio" name="flexRadioDefault"
                               value="15" id="flexRadioDefault1" checked>
                        <label class="form-check-label" for="flexRadioDefault1">
                            15 sec
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input d-none" type="radio" name="flexRadioDefault"
                               value="30" id="flexRadioDefault2">
                        <label class="form-check-label" for="flexRadioDefault2">
                            30 sec
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input d-none" type="radio" name="flexRadioDefault"
                               value="60" id="flexRadioDefault3">
                        <label class="form-check-label" for="flexRadioDefault3">
                            60 sec
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input d-none" type="radio" name="flexRadioDefault"
                               value="90" id="flexRadioDefault4">
                        <label class="form-check-label" for="flexRadioDefault4">
                            90 sec
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input d-none" type="radio" name="flexRadioDefault"
                               value="120" id="flexRadioDefault5">
                        <label class="form-check-label" for="flexRadioDefault5">
                            120 sec
                        </label>
                    </div>
                </div>
            </div>
            <!-- Selecting Question Range   -->
            <div class="quiz__rangeSelection" style="display:none;">
                <div class="quiz__rangeSelection-rangeInputs mb-5">
                    <input type="number" id="questionRangeInput" placeholder="Enter number of questions">
                    <div class="d-flex align-items-center justify-content-between mt-3">
                        <input type="number" class="questionRangeFromToInput" id="questionRangeFromInput" placeholder="From">
                        <input type="number" class="questionRangeFromToInput" id="questionRangeToInput" placeholder="To">
                    </div>
                    <p class="rangeInputs__errorMsg" style="display:none;">Please enter valid range</p>
                </div>
            </div>

            <div class="inital-btn mt-4 text-center">
                <button class="btn btn-practices" onclick="validateRangeToStart()">Start</button>
            </div>
        </div>

        <div>
            <div class="main-page__sectionDetails" style="display: none">
                <div class="main-page__sectionDetails-description">
                    <p>No. of Sections : <span id="sectionNo"></span></p>
                    <p class="d-none sectotalTimeCount">Total Time : <span id="sectionTotalTime">10 mintues</span> minutes</p>
                </div>
                <table class="table table-bordered ">
                    <thead>
                    <tr>
                        <th scope="col">Section Name</th>
                        <th scope="col">No. of Question</th>
                        <th scope="col"><i class="fa-solid fa-check"></i></th>
                        <th scope="col"><i class="fa-solid fa-x"></i></th>
                        <th scope="col">Time (min)</th>
                    </tr>
                    </thead>
                    <tbody id="sectionQuizDetails">
                    </tbody>
                </table>
            </div>
        </div>
    </div>

</div>

<div class="container-lg practice-container d-none">
    <div class="row">
        <div class="col-12 col-lg-2 preview-quiz">
            <div class="d-flex justify-content-between p-4">
                <div><h4><i class="material-icons">toc</i> Test Preview</h4></div>
                <i class="material-icons" onclick="closePreview()">close</i>

            </div>
            <div id="total-que-tab">
                <div class="num-wrapper">
                    <div class="num-text">1</div>
                    <div id="attempt-status">Not Attempted</div>
                </div>
            </div>
        </div>

        <div class="col-lg-10 practice-quiz-wrapper">
            <div class="app-header mt-lg-4">
                <div class="d-flex align-items-center justify-content-between justify-content-lg-center">
                    <div class="main-timer d-none">
                        <div class="time-wrapper">
                            <p id="test_timer"></p>
                        </div>

                    </div>
                    <i class="material-icons" onclick="showPreview()">toc</i>
                    <div class="d-none align-items-center svg-timer" id="testTimerSection">
                        <div class="normal-time">
                            <svg id="time-progress" width="35" height="35" viewBox="0 0 200 200" style="transform: rotate(-90deg)">
                                <circle cx="100" cy="100" r="90" stroke="#e0e0e0" stroke-width="20" fill="none"></circle>
                                <circle cx="100" cy="100" r="90" stroke="red" stroke-width="20" fill="none" stroke-dasharray="565.48" stroke-dashoffset="565.48"></circle>
                            </svg>

                            <button class="play d-none" id="pause" data-setter=""></button>
                        </div>
                        <div class="sectiontime-wrapper ml-3">
                            <span class="c display-remain-time">00.00</span>
                        </div>
                    </div>
                    <div class="header-buttons-wrapper">
                        <button class="btn btn-submit-quiz d-none" id="submitQuizBtn" onclick="submitFromModal()" title="Submit Quiz">
                            <i class="material-icons">send</i>
                            Submit
                        </button>
                        <i class="material-icons" onclick=submitFromModal()>close</i>
                    </div>
                </div>
                <div class="d-flex align-items-center justify-content-between">
                    <h4 class="quizTypeName practice-title">Test</h4>
                    <select class="section-selection" id="sectionSelect" style="display:none;" onchange="sectionChange()">

                    </select>
                </div>
            </div>
            <!--Game Profile-->
            <div class="gamer-profile d-none">
                <div class="circle-wrapper">
                    <div class="roundbg">
                        <img src="file:///android_asset/quiz/assets/images/prepjoy/pointer.svg">
                    </div>
                </div>
                <div class="col-12 playerbg">
                    <div class="container">
                        <div class="media">
                            <img src=""
                                 class="mr-3 rounded-circle user-profile-image" alt="" width="50px"
                                 height="50px">
                            <div class="media-body">
                                <h5 class="mt-0 username playerName">MS Dhoni</h5>
                                <p>Novice</p>
                                <p id="userPlace">Bangalore, Karnataka</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="meridian">
                </div>
                <div class="col-12 botbg botselect">
                    <div class="container">
                        <div class="media botProfile d-none">
                            <div class="media-body">
                                <h5 class="mt-0 username challengerName">Virat Kohli</h5>
                                <p>Novice</p>
                                <p id="botPlace">Level 1</p>
                            </div>
                            <img src=""
                                 class="ml-3 bot-profile-img rounded-circle" alt="" width="50px"
                                 height="50px">
                        </div>
                        <h4 class="text-center locate">Locating the user...</h4>
                        <div class="bot-wrapper">
                            <div class="bot-anim-wrapper d-none">
                                <div class="bot-anim"></div>
                                <div class="bot-anim"></div>
                                <div class="bot-anim"></div>
                                <div class="bot-anim"></div>
                                <div class="bot-anim"></div>
                                <div class="bot-anim"></div>
                                <div class="bot-anim"></div>
                                <div class="bot-anim"></div>
                                <div class="bot-anim"></div>

                            </div>
                            <div class="img-bot"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="no-counter d-none">
                <div id="no-counter"></div>
            </div>


            <!--Players Profile top bar-->
            <div class="quiz-profile container-fluid d-none">

                <div class="media">
                    <img src="/assets/prepjoy/profsample.jpeg"
                         class="mr-3 rounded-circle user-profile-image" alt="" width="35px"
                         height="35px">
                    <div class="media-body">
                        <h5 class="mt-0 username playerName">MS Dhoni</h5>
                        <p class="score" id="playerScore">0 pts</p>
                    </div>
                </div>
                <div class="main-timer">
                    <h4>Time Left</h4>
                    <div class="time-wrapper">
                        <div id="app">

                        </div>
                        <p id="countdown" class="timer_sec">15</p>
                    </div>

                </div>
                <div class="media">
                    <div class="media-body">
                        <h5 class="mt-0 username challengerName">Virat Kohli</h5>
                        <p class="score" id="botScores">0 pts</p>
                    </div>
                    <img src="/assets/prepjoy/profsample.jpeg"
                         class="ml-3 rounded-circle bot-profile-img" alt="" width="35px"
                         height="35px">
                </div>


            </div>

            <div class="quizes d-none">
                <!--Success Progressbar-->
                <div class="progress progress-bar-vertical player1">
                    <div id='player1' class="progress-bar progress-bar-success active"
                         role="progressbar" aria-valuenow="100" aria-valuemin="0"
                         aria-valuemax="100" style="height: 0%;">
                        <span class="sr-only">60% Complete</span>
                    </div>
                </div>
                <!--Question and Answers-->
                <div class="container swiper-container">
                    <p class="audioChange" onclick="audioChange()"><i class="material-icons">volume_up</i>
                    </p>

                    <div class="question-wrapper">
                        <div class="d-none d-lg-flex justify-content-between que-show">
                            <div class='question-no'></div>
                        </div>
                        <div class="favQuestionStar">

                        </div>
                        <div class="directions d-none">

                            <p id="direction" class="more">
                                Lorem Ipsum is simply dummy text of  the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.
                            </p>


                        </div>
                        <p class="que_text">What is the manchester of South India?</p>

                        <button class="btn btn-review"><i class="material-icons">push_pin</i> Mark
                            for Review
                        </button>
                        <button class="btn show-explanation m-3" onclick="showDescription()">Show
                            Explanation
                        </button>
                    </div>
                    <div class="que-options-wrapper mt-4">
                        <div class="que-options">

                        </div>
                    </div>
                </div>

                <!--Success Progressbar-->
                <div class="progress progress-bar-vertical player2">
                    <div id='player2' class="progress-bar progress-bar-success active"
                         role="progressbar" aria-valuenow="100" aria-valuemin="0"
                         aria-valuemax="100" style="height: 0%;">
                        <span class="sr-only">60% Complete</span>
                    </div>
                </div>

            </div>

            <!--User win status Dialog box-->
            <div class="modal fade" id="winnerModal" tabindex="-1" role="dialog"
                 data-backdrop="static" data-keyboard="false" aria-labelledby="winnerModal"
                 aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-dialog-zoom modal-sm"
                     role="document">
                    <div class="modal-content">
                        <div class="modal-header d-none">
                            <button type="button" class="close" data-dismiss="modal"
                                    aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div id="winner">
                            </div>
                            <h1 class="user-status">You Won</h1>
                            <div id="submitQuiz"></div>
                        </div>
                        <div class="modal-footer">

                        </div>
                    </div>
                </div>
            </div>
            <div class="modal fade" id="medalModal" tabindex="-1" role="dialog"
                 data-backdrop="static" data-keyboard="false" aria-labelledby="winnerModal"
                 aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-dialog-zoom modal-sm"
                     role="document">
                    <div class="modal-content">

                        <div class="modal-body medal-wrappers">
                            <div>
                                <div id="medal-upload"></div>
                                <h1>Congratulations</h1>
                                <p id="win-message">You've won 8 Consecutive times</p>
                                <div id="medal-user" class="medal-user">
                                </div>
                                <p id="medal-name">Gold Medal</p>
                                <button class="btn btn-answer mt-3" data-dismiss="modal">Back to
                                    Scoreboard
                                </button>
                            </div>

                        </div>

                    </div>
                </div>
            </div>
            <div class="modal fade" id="rankModal" tabindex="-1" role="dialog"
                 data-backdrop="static" data-keyboard="false" aria-labelledby="winnerModal"
                 aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-dialog-zoom modal-sm"
                     role="document">
                    <div class="modal-content">

                        <div class="modal-body medal-wrappers">
                            <div>
                                <div id="rank-upload"></div>
                                <h1>Congratulations</h1>
                                <p id="rank-message">You have got 1st Rank</p>
                                <div id="rank-cup" class=''>
                                </div>
                                <button class="btn btn-answer mt-3" data-dismiss="modal">Back to
                                    Scoreboard
                                </button>
                            </div>

                        </div>

                    </div>
                </div>
            </div>

            <div class="practice-result d-none">
                <div class="container">
                    <div class="d-flex result-header align-items-center">
                        <h2><i class="material-icons" onclick="backToHome()">
                            arrow_left
                        </i></h2>
                        <p class="d-flex justify-content-center align-items-center w-100"><span class="result">Results</span></p>
                        <div class="sectionSelectDropDown d-none">
                            <select name="sectionSelectDropDown" id="sectionSelectDropDown" onchange="resultSectionChange()">

                            </select>
                        </div>
                        <p class="d-none" id="sharebtn" onclick='showShareIcon()'><i class="material-icons">share</i> Share</p>
                    </div>
                    <h1 id="user-status" class="pb-2">
                        You Won!
                    </h1>
                    <div class="result__wrapper-card">
                        <h3 id="wsResultPointsCount"></h3>
                        <h6 id="wsSectionResultPointsCount"></h6>
                        <div class="score-media">

                            <div class="result-points">

                                <div class="d-flex align-items-center justify-content-center score-wrap">
                                    <p id="score" class="updateScore">123</p> / <p id="noofque"></p>
                                </div>
                            </div>
                            <div class="sperator-pipe"></div>
                            <div >
                                <ul class="score-media_lists">
                                    <li><span class="score-media_lists-text">Correct</span><span>:</span><span id="right">10</span></li>
                                    <li><span class="score-media_lists-text">InCorrect</span><span>:</span><span id="wrong">20</span></li>
                                    <li><span class="score-media_lists-text">Skipped</span><span>:</span><span id="skipped">1</span></li>
                                </ul>
                            </div>
                        </div>
                        <div class="next-match text-center" style="margin-top:25px">
                            <button class="btn btn-answer" id="practice-summary" onclick="showSummary()">
                                Summary / Details <i class="fa-sharp fa-solid fa-arrow-right"></i>
                            </button>
                        </div>
                        <div class="button-results">
                            <h2 class="button-results__title">Play</h2>
                            <button class="btn btn-prev btn-playagain" onclick="showAction('previousMatch')" id="prevMatch">
                                <i class="fa-solid fa-backward-step"></i>
                                <p class="button-results__label">Previous</p>
                            </button>
                            <button class="btn btn-playagain" onclick="showAction('rematch')">
                                <i class="fa-solid fa-rotate-right"></i>
                                <p class="button-results__label">Rematch</p>
                            </button>
                            <button class="btn btn-next btn-playagain" id="nextMatch" onclick="showAction('nextMatch')">
                                <i class="fa-solid fa-forward-step"></i>
                                <p class="button-results__label">Next</p>
                            </button>
                        </div>
                        <div id="lottie"></div>
                    </div>
                </div>
            </div>

            <div class="action-wrapper">

                <div class="d-flex justify-content-between">
                    <i class="material-icons close-buttons"  onclick="closeButtons()">close</i>
                </div>
                <div class="action-explanation" >
                    <div class="button-results d-flex align-items-center flex-column" style="gap:1rem">
                        <button id='btn-play' class="btn btn-playagain bottom__sheet-buttons" onclick="playAgain()">
                            <img src="file:///android_asset/quiz/assets/images/prepjoy/ic_play.png" class="bottom__sheet-buttons-img" alt="">
                            Play
                        </button>
                        <button id='btn-practice' class="btn  btn-playagain bottom__sheet-buttons" onclick="previousQuiz()">
                            <img src="file:///android_asset/quiz/assets/images/prepjoy/is_practice.png" class="bottom__sheet-buttons-img" alt="">
                            Practice
                        </button>
                        <button id='btn-test' class="btn  btn-playagain bottom__sheet-buttons" onclick="nextQuiz()">
                            <img src="file:///android_asset/quiz/assets/images/prepjoy/ic_test.png" class="bottom__sheet-buttons-img" alt="">
                            Test
                        </button>
                    </div>
                </div>
            </div>

            <div class="app-footer">
                <div>
                    <button class="btn" id='btn-prev' onclick="prevQue()">Previous</button>
                    <div class='question-no'></div>
                    <button class="btn" id="btn-next" onclick="nextQue()">Next</button>
                </div>
            </div>
            <div class="explanation-wrapper">
                <div class="explanation-header">
                    <div class="d-flex justify-content-between">
                        <h4>Correct Answer:</h4>
                        <i class="material-icons" onclick="closeExplanation()">close</i>
                    </div>
                    <p class="correct-answer" id="correct-answer">test</p>
                </div>
                <div class="answer-explanation">
                    <h4>Explanation:</h4>
                    <div id="explanation"></div>
                </div>
            </div>

        </div>
        <div class="d-none d-lg-block col-lg-1 h-full">
            <input type="checkbox" checked data-toggle="toggle" id='toggle-theme' data-on="<i class='material-icons'>light_mode</i>" data-off="<i class='material-icons'>nightlight_round</i>" data-size="sm">
        </div>
    </div>
</div>
<div class='modal fade' id='prep-report-question'>
    <div class='modal-dialog modal-dialog-centered modal-dialog-zoom modal-sm'>
        <div class='modal-content prep-report-modal'>
            <div class='modal-header'>
                <p class='modal-title d-flex align-items-center prep-report-icon'>Report the
                    question <i class='ml-1 material-icons'>error</i></p>
                <button type='button' class='close' data-dismiss='modal'>&times;</button>
            </div>
            <div class='modal-body d-flex flex-column'>
                <div class="chkbox mt-1">
                    <input type="checkbox" class="form-check-input" id="spellingMistake">
                    <label class="form-check-label tick" for="spellingMistake">
                        <span></span>Spelling Mistake
                    </label>
                </div>
                <div class="chkbox mt-1">
                    <input type="checkbox" class="form-check-input" id="directionNotGiven">
                    <label class="form-check-label tick" for="directionNotGiven">
                        <span></span>Direction not given
                    </label>
                </div>
                <div class="chkbox mt-1">
                    <input type="checkbox" class="form-check-input" id="imageNotVisible">
                    <label class="form-check-label tick" for="imageNotVisible">
                        <span></span>Graph / Image not visible
                    </label>
                </div>

                <div class="chkbox mt-1">
                    <input type="checkbox" class="form-check-input" id="incompleQuestion">
                    <label class="form-check-label tick" for="incompleQuestion">
                        <span></span>Incomplete question
                    </label>
                </div>
                <div class="chkbox mt-1">
                    <input type="checkbox" class="form-check-input" id="otherIssues">
                    <label class="form-check-label tick" for="otherIssues">
                        <span></span>Other Issues
                    </label>
                </div>

                <div class="form-group mt-3 textbox">
                    <textarea class="form-control" id="moreInformation"
                              placeholder="Let us know"></textarea>
                </div>
            </div>
            <div class='modal-footer d-flex justify-content-center'>
                <button type='button' class='btn btn-submit btn-answer reportBtn'
                        onclick="reportSubmit()">Submit
                </button>
            </div>
        </div>
    </div>
</div>
<div class='modal fade' id='review-modal' data-keyboard="false" data-backdrop="static">
    <div class='modal-dialog modal-dialog-centered modal-dialog-zoom modal-sm'>
        <div class='modal-content prep-report-modal'>
            <div class='modal-header'>
                <h2>Test Summary</h2>
                <!--                <button type='button' class='close' data-dismiss='modal'>&times;</button>-->
            </div>
            <div class='modal-body d-flex align-items-center'>
                <div class="d-flex justify-content-around align-items-center w-100 mt-2">
                    <div>
                        <div class="box green">
                            <p id='noOfAnswered'></p>
                        </div>

                        <p class="text-quote">Answered</p>
                    </div>
                    <div class="review-wrap">
                        <div class="box yellow">
                            <p id='noOfreviewed'></p>
                        </div>
                        <p class="text-quote">Marked for Review</p>
                    </div>
                    <div>
                        <div class="box blue">
                            <p id='noOfSkipped'></p>
                        </div>
                        <p class="text-quote">Skipped</p>
                    </div>
                </div>


            </div>
            <div class='modal-footer'>
                <button type="button" class="btn btn-default mr-3" data-dismiss="modal">Cancel</button>
                <button type='button' class='btn btn-submit mb-0'
                        onclick="submitFromModal()">Submit
                </button>
            </div>
        </div>
    </div>
</div>


<div class='modal fade' id='questionInfoModal' data-keyboard="false" data-backdrop="static">
    <div class='modal-dialog modal-dialog-centered modal-dialog-zoom modal-sm'>
        <div class='modal-content prep-report-modal'>
            <div class='modal-body d-flex align-items-center' style="padding:1.4rem">
                <div class="d-flex justify-content-around align-items-center w-100 mt-2">
                    <ul class="questionInfoModal__lists">
                        <li>1 point will be awarded for each correct answer.</li>
                        <li>0 points will be deducted for each in-correct and skipped answer.</li>
                    </ul>
                </div>
            </div>
            <div class='modal-footer infoModalCloseBtn'>
                <button type="button" class="btn btn-default mr-3" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="continue-test" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">

            <!-- Modal body -->
            <div class="modal-body" id="temp-mod">
                <div class="content-wrapper">
                    <h4 class="timeup text-center">Time's Up!</h4>
                    <p id="completed-subject" class="text-center"> The time to complete this section has ended.</p>
                    <p class="comingup text-center">COMING UP - <span id="comingup-subject"></span></p>
                </div>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer">
                <button onclick="javascript: nextSection();" data-dismiss="modal" class="btn btn-continue bg-danger text-white">Continue</button>
            </div>

        </div>
    </div>
</div>
<div class="modal fade" id="force-submit-test" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <!-- Modal body -->
            <div class="modal-body text-center" id="submitTest">
                <h1 class="submit">Submit</h1>
                <p>Your test will be submitted now. </p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer">
                <button onclick="javascript:submitFromModal();" data-dismiss="modal" class="btn submit bg-danger text-white">SUBMIT</button>

            </div>

        </div>
    </div>
</div>
<div class="modal fade" id="secChangeAlert" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <!-- Modal body -->
            <div class="modal-body text-center">
                <p id="secChangeAlertTxt"></p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer">
                <button data-dismiss="modal" class="btn bg-danger text-white">Okay</button>
            </div>

        </div>
    </div>
</div>
<script src="file:///android_asset/quiz/assets/javascripts/timer.js"></script>
<script>


    var option_tag='';
    var currentUsername='';
    var myQuizId='';
    var resourcesId='';
    var questions=[];
    var challengerName='';
    var challengerPlace='';
    var botName='';
    var source='android';
    // var time_line = document.querySelector(".time_line");
    var timeCount = document.querySelector(".timer_sec");
    var option_list = document.querySelector(".que-options");
    var que_count = 0;
    var que_numb = 1;
    var timeValue =  1500;//Change time here will change in all places(All answer progressbar logic wil work fine);
    var questionTime=timeValue/100;
    var userScore = 0;
    var botScore=0;
    var counterLine;
    var widthValue = 0;
    var counter;
    var globalTimer;
    var checkbotAnswer;
    var botAnswerTime;
    var chatBotAnswerTime;
    var myCounter;
    var botAnswerFirst=false;
    var userAnswerFirst=false;
    var userAnswers=[];
    var botAnswer;
    var qaObj={
        userAnswers:[],
    };
    var userTotalTime=0;
    var botTotalTime='';
    var correctOption=-1;
    var que_answer;
    var userId='';
    var qIndex;
    var newTime;
    var userImage='';
    var language='';//for web
    var userPlace='';
    var submitUrl='';
    var sound;
    var countdown;
    var span;
    var timeObj;
    var tt;

    var quizMode=''; //Practice

    var baseUrl = 'https://www.wonderslate.com/';
    var serviceURL = 'https://www.wonderslate.com/';
    var previous='yes';
    var next='no';
    var practicePrevious=false;
    var monthly='no';
    var weekly='no';
    var quizType='';
    var noOfQuestions='';
    var testId='';
    var dailyTest=false;
    var tokenId='';
    var resourceDate='';
    var fromShop='';
    var siteId = '';
    var realDailyTestDtlId = '';
    var reviewedQ=false;
    var sectionPresent=false;
    var sectionDtl;
    var sectionMst;
    var sectionLength;
    var sectionTotalTime;
    var subjectsList=[];
    var sectionSelectValue;
    var sectionQuestions=[];
    var secTimerPresent=false;
    var exitQuiz=true;
    var storingInitialData;
    var qSubject;
    var isBotAnswered = false;
    var bookgpt = false;
    var gptTestSeries = false;

    // Submit button visibility logic
    function updateSubmitButtonVisibility() {
        var submitBtn = document.getElementById('submitQuizBtn');
        if (!submitBtn) return;

        // Show submit button only for Practice and Test modes (not Play)
        var shouldShowButton = (quizMode === 'practice' || quizMode === 'testSeries' || quizMode === 'test');

        if (shouldShowButton) {
            // Show button from first question until second-to-last question
            // Hide on last question (when finish button appears)
            var isLastQuestion = (que_count >= questions.length - 1);

            if (isLastQuestion) {
                submitBtn.classList.add('d-none');
                console.log('Submit button hidden - last question');
            } else {
                submitBtn.classList.remove('d-none');
                console.log('Submit button shown - question ' + (que_count + 1) + ' of ' + questions.length);
            }
        } else {
            // Hide button for Play mode
            submitBtn.classList.add('d-none');
            console.log('Submit button hidden - Play mode');
        }
    }

function changeTestTime(time){
    timeValue=time*100;
    questionTime=timeValue/100;
    TIME_LIMIT = timeValue/100;
    $('#countdown').text(questionTime);
    if(timeValue>6000){
        $('#countdown').text('00.00');
    }
}

function receiveData(data) {
    if(source=='ios') {
        data = JSON.stringify(data);
        data = JSON.parse(data);
        fromShop=data.isFromShop;
        siteId=data.siteId;
        if(fromShop=='disabled'){
           $('.button-results').addClass('d-none');
        }
        weekly=data.weekly;
        monthly=data.monthly;
        testId=data.testId;

        if(testId !='' && testId!='undefined' && testId!=undefined){
            dailyTest=true;
        }


         if(data.resourceDate !=''){
            resourceDate=data.resourceDate;
            if(weekly=='yes'){
               $('.practice-title').text('Current Affairs');
               $('.mcq-name').text('Current Affairs for the week '+' '+resourceDate);
            }else if(monthly=='yes'){
               $('.practice-title').text('Current Affairs');
               $('.mcq-name').text('Current Affairs for the month '+' '+resourceDate);
            }else{
               $('.practice-title').text('Online Test '+' '+resourceDate);
               $('.mcq-name').text('Online Test '+' '+resourceDate);
            }
         }else{
              $('.mcq-name').text(data.quizData.resourceName);
              if(data.quizData.resourceName.includes("Current Affairs")){
                $('.practice-title').text('Current Affairs');
            }
            else{
                $('.practice-title').text(data.quizData.resourceName);
            }
         }
              if(data.userImage !=''){
                  userImage = data.userImage;
              }
              else{
                  userImage = 'profiledefault.png'
              }
              if(quizMode ==''){
              timeValue=data.timeValue;
              timeValue=Number(timeValue);
              changeTestTime(timeValue);
              }
    }else{
        data=data;
        console.log(data);
        console.log(data.bookgpt);
        bookgpt = data.bookgpt;
        gptTestSeries = data.gptTestSeries;
        fromShop=data.isFromShop;
        if(fromShop=='disabled'){
            $('.button-results').addClass('d-none');
        }
        if(quizMode==''){
            timeValue=data.timeValue;
            changeTestTime(timeValue);
        }

         weekly=data.weekly;
         monthly=data.monthly;
         testId=data.testId;
         siteId = data.siteId;
         realDailyTestDtlId = data.quizData.realDailyTestDtlId;

        if(testId !='' && testId !='undefined' && testId !=undefined){
              dailyTest=true;
        }
        if(data.resourceDate !=''){
             resourceDate=data.resourceDate;
             if(weekly=='yes'){
                 $('.practice-title').text('Current Affairs');
                 $('.mcq-name').text('Current Affairs for the week '+' '+resourceDate);
             }else if(monthly=='yes'){
                   $('.practice-title').text('Current Affairs');
                   $('.mcq-name').text('Current Affairs for the month '+' '+resourceDate);
             }else{
                  $('.practice-title').text('Online Test '+' '+resourceDate);
                  $('.mcq-name').text('Online Test '+' '+resourceDate);
             }
        }else{
            $('.mcq-name').text(data.quizData.resourceName);
            if(data.quizData.resourceName.includes("Current Affairs")){
                $('.practice-title').text('Current Affairs');
            }
            else{
                $('.practice-title').text(data.quizData.resourceName);
            }
        }
    }

    currentUsername = data.userName;
    userPlace= data.city+','+data.state;

    $('#userPlace').text(userPlace);
    language=data.language;

    if(language==''){
        $('.lang-module').removeClass('d-flex').addClass('d-none');
    }
    sound=data.sound;
    next=data.next;
    previous=data.previous;
    quizMode=data.quizType;

    if(language==''){
        $('.custom-check.lang').hide();
    }else{
        $('.custom-check.lang').show();
    }

    if(sound=='off'){
        $('.audioChange i').text('volume_off');
        setSound=true;
    }
    else{
        $('.audioChange i').text('volume_up');
        setSound=false;
    }
    if(data.userImage !=''){
        userImage = data.userImage;
    }
    else{
        if(source=='android') {
            userImage = 'file:///android_asset/quiz/assets/images/prepjoy/default_icon.png';
        }
        else{
            userImage = 'profiledefault.png';
        }
    }
    $('.playerName').text(currentUsername);
    userId=data.userId;
    myQuizId = data.quizData.results[0].quizId;

    if(gptTestSeries){
        myQuizId = data.quizId
    }
    resourcesId = data.resId;

    if(quizMode==''){
    $('.que-show').removeClass('d-none');
        quizMode='';
        quizType='play';
    }
    else if(quizMode=='practice'){
        quizMode='practice';
        quizType='practice';
    }
    else if(quizMode=='testSeries'){
        quizMode='testSeries';
        quizType='test';
        console.log("quizType",quizType)
    }

 initializeQuizResults(data);
    if(quizMode!=''){

        data=data.quizData;

        if(data.language1=='Hindi'){
            $('.option-kannada').hide();
        }
        else if(data.language1=='Kannada'){
             $('.option-hindi').hide();
        }
        else if(data.language2=='Hindi'){
             $('.option-kannada').hide();
        }
         else if(data.language2=='Kannada'){
             $('.option-hindi').hide();
        }
    }

    if(quizMode!=''){
        constructLanguageBtn(data.language1,data.language2);
    }
}


function shuffle(o){
    for(var j, x, i = o.length; i; j = Math.floor(Math.random() * i), x = o[--i], o[i] = o[j], o[j] = x);

    return o;
}


    //App will come here directly
    function initializeQuizResults(data) {

        if (source != 'web') {
            if(quizMode==''){
                $('.practice-container').removeClass('d-none');
            }

            if(source=='android'){
                submitUrl = data.baseUrl;
                serviceURL = data.serviceURL;
                weekly=data.weekly;
                monthly=data.monthly;
                tokenId=data.xauth;
                resourceDate=data.resourceDate;

                if (data.nextchallengerPlace != '' && data.nextChallenger != '') {
                    challengerName = data.nextChallenger;
                    challengerPlace = data.nextchallengerPlace;
                } else {
                    challengerName = data.quizData.challengerName;
                    challengerPlace = data.quizData.challengerPlace;
                }

                data = data.quizData;

                 questions = data.results;
            }
            else{
            challengerPlace = data.challengerPlace;
            var data = data.quizData;
            challengerName = data.challengerName;
                challengerName = data.challengerName;
                challengerPlace = data.challengerPlace;
                 tokenId = data.xauth;

            questions = data.results;
            }

        } else {
            questions = data.results;
            challengerName = data.challengerName;
            challengerPlace = data.challengerPlace;
        }
        storingInitialData = data
        storingQaForWeb = data.results
        console.log(data)
        if (sectionPresent){
            questions.sort(arrangeSection);
        }
        if(data.examDtl!=""  && data.examDtl!=null){
            $("#sectionSelect").show();
            $('.main-page__content-header,.main-page__content-points,.main-page__content-questionsRange__range').addClass('d-none').removeClass('d-flex');
            if(quizType=='practice'||quizType=='test')
                $("#noOfQuestionsOption").hide();
            sectionQuizUI(data);
        }
        noOfQuestions=questions.length;

        if(quizMode=='testSeries'){
            $('.btn-review').css('display','flex');
        }
        botName = challengerName + "-" + challengerPlace;
        $('.challengerName').text(challengerName);
        $('#botPlace').text(challengerPlace);
        $(".user-profile-image").attr({"src": userImage});

        var botImage = randomIntFromInterval(1, 20);//Randomise image
        if(source !='web'){
            if(quizMode==''){
                if(source=='android') {
                    $(".bot-profile-img").attr({"src": 'file:///android_asset/quiz/assets/images/prepjoy/' + botImage + '.jpg'});
                }else{

                    $(".bot-profile-img").attr({ "src": +botImage+'.jpg' });
                }
            }
        }

        if((quizMode !='practice') && (quizMode !='testSeries')){
            $('.app-header,.app-footer').hide();
        }
        if(quizMode=='') {
            //$('.main-page').hide();
            $('.main-page').addClass('d-none').removeClass('d-flex');
            startQuiz();
        }
        else if(quizMode=='test'){
            //$('.main-page').show();
            $('.main-page').removeClass('d-none').addClass('d-flex');
            $('.mcq-type').html('<p>Test</p>');
            $('.total-que').html(questions.length + '<br/><span>Questions</span>');
            $('.total_questions').text(questions.length );

        }
        else if((quizMode =='practice')||(quizMode =='testSeries') || (quizMode =='test')){
            document.getElementById("whTheme").setAttribute("href","file:///android_asset/quiz/assets/stylesheets/prepJoy/whTheme.css")
            document.querySelector(".loader-submit").setAttribute("src","file:///android_asset/quiz/assets/images/prepjoy/loader.gif")
            document.querySelector(".loader-submit").classList.add("loader-submit-new")
            $('.total-que').html(questions.length + '<br/><span>Questions</span>');
            if(bookgpt===true){
                $('.main-page__content-questionsRange').removeClass('d-flex').addClass('d-none');
                $('.main-page__content-header').addClass('gptheader');
                $('.header_wrap').addClass('gptHeaderWrap');
            }
            //$('.main-page').show();
            $('.main-page').removeClass('d-none').addClass('d-flex');
            $('.main-page__content-points').removeClass('d-flex').addClass('d-none');
            $('.main-page__content-separator').removeClass('d-flex').addClass('d-none');
            $('.custom-check:last-child').hide();
            $('.time-module').removeClass('d-flex').addClass('d-none');
            $('.total_questions').text(questions.length );
        }
    }

    function sectionQuizUI(data){
        sectionDtl = data.examDtl;
        sectionMst = data.examMst;
        sectionLength = sectionMst.noOfSections;
        sectionTotalTime = sectionMst.totalTime;
        if (sectionMst.examInstructions !="" && sectionMst.examInstructions!=null){
            if(quizType!='play')  $("#inst-sec").show();
            document.getElementById("instructionText").innerHTML = sectionMst.examInstructions;
        }
        if(quizType!='play')
            $(".main-page__sectionDetails").show();

        $("#sectionNo").text(sectionLength);
        $("#sectionTotalTime").text(sectionTotalTime);
        $(".sectotalTimeCount").removeClass('d-none')

        var optionHtml="";
        var sectionDetailsTable="";
        if (sectionDtl!="" && sectionDtl.length>0 && sectionDtl!=""){
            sectionPresent=true;
            secpresent = true;
            for(var s=0;s<sectionDtl.length;s++){
                subjectsList.push(sectionDtl[s].subject);
                sectionDetailsTable += "<tr>"+
                    " <th scope='col'>"+sectionDtl[s].subject+"</th>"+
                    "<th scope='col'>"+sectionDtl[s].noOfQuestions+"</th>"+
                    "<th scope='col'>"+sectionDtl[s].rightAnswerMarks+"</th>"+
                    "<th scope='col'>"+sectionDtl[s].wrongAnswerMarks+"</th>"+
                    "<th scope='col'>"+sectionDtl[s].totalTime+"</th>"+
                    "</tr>";
            }

            document.getElementById("sectionQuizDetails").innerHTML = sectionDetailsTable;
        }
        console.log(quizType,quizMode)
        if(quizType=='test') {
        console.log("yes it is test",$("#testTimerSection"))
            //   $('.quizes').removeClass('d-flex').addClass('d-none');
            $("#testTimerSection").removeClass('d-none').addClass('d-flex').attr('style','gap:12px')
            if (sectionMst != null && !sectionMst == "") {
                examMst = sectionMst;
                if (secpresent && sectionDtl[0].totalTime != null && !sectionDtl[0].totalTime == "") {
                    changeWholeTime(sectionDtl[0].totalTime * 60);
                    secTimerPresent = true;
                } else {
                    if (sectionMst.totalTime != null && !sectionMst.totalTime == "") {
                        changeWholeTime(sectionMst.totalTime * 60);
                    }
                }
            }
        }
    }

    function sectionChange(){
        sectionSelectValue = document.getElementById("sectionSelect").value;
        sectionQuestions=[];
        for(var q=0;q<storingQaForWeb.length;q++){
            if (storingQaForWeb[q].subject == sectionSelectValue){
                sectionQuestions.push(storingQaForWeb[q]);
            }
        }
        var indexVal = questions.map(i => i.subject).indexOf(sectionSelectValue);
        $("#sectionDisplayName").text(sectionSelectValue);

        goToQue(indexVal+1);
        displayQueNumbers();
        checkQuestionState();
        var sectionTime = sectionDtl[document.getElementById("sectionSelect").selectedIndex].totalTime;
        if(secTimerPresent) {
            if (sectionTime == undefined) {
                forceSubmitTest();
            } else {
                changeWholeTime(sectionTime * 60);
                startTimerForTest();
            }
        }
    }
    function checkQuestionState(){
        var qansobj = qaObj.userAnswers;
        var markList=[];

        var mergedSubjects = qansobj.map(subject => {
            var otherSubject = sectionQuestions.find(element => element.id === subject.id)
            return  { ...subject, ...otherSubject }
        })

        for(var p=0;p<mergedSubjects.length;p++){
            if (mergedSubjects[p].subject == sectionSelectValue){
                markList.push(mergedSubjects[p]);
            }
        }
        markList.map(mark=>{
            if (mark.userOption!='-1'){
                document.getElementById('que-'+(mark.queIndex+1)).classList.add('answered');
            }
           if (mark.reviewedQ && mark.userOption !='-1'){
                document.getElementById('que-'+(mark.queIndex+1)).classList.add('pinMark');
                document.getElementById('que-'+(mark.queIndex+1)).classList.add('reviewed','markedOrange');
                $('#que-' + (mark.queIndex+1) ).find('.num-text').append('<i class="material-icons" style="color:#FF4141 ">push_pin</i>');
            }else if (mark.reviewedQ) {
                document.getElementById('que-'+(mark.queIndex+1)).classList.add('pinMark');
                document.getElementById('que-'+(mark.queIndex+1)).classList.add('reviewed');
                $('#que-' + (mark.queIndex+1) ).find('.num-text').append('<i class="material-icons">push_pin</i>');
            }
        })

    }
function intersect(a, b) {
        return a.filter(Set.prototype.has, new Set(b));
    }
function addSubjectDropDown(){
        var subjectArrList = [];
        var optionHtml="";

        for (var r=0;r<questions.length;r++){
            for(var t=0;t<subjectsList.length;t++){
                if (questions[r].subject == subjectsList[t]){
                    subjectArrList.push(questions[r].subject)
                }
            }
        }
        finalList = intersect(subjectArrList, subjectsList);
        finalList = finalList.filter(function (value, index, array) {
            return array.indexOf(value) === index;
        });

        for(var v=0;v<finalList.length;v++){
            optionHtml+="<option value='"+finalList[v]+"' data-sub='"+finalList[v]+"'>"+finalList[v]+"</option>";
        }

        document.getElementById("sectionSelect").innerHTML = optionHtml;
        sectionSelectValue = document.getElementById("sectionSelect").value;
        console.log(document.getElementById("sectionSelect").value)
    }
    //Start Quiz
    function startQuiz(){
        console.log(questions);
        addSubjectDropDown();
        console.log(sectionSelectValue,storingQaForWeb,questions)
        for(var q=0;q<storingQaForWeb.length;q++){
            if (storingQaForWeb[q].subject == sectionSelectValue){
                sectionQuestions.push(storingQaForWeb[q]);
            }
        }
        console.log(sectionQuestions)
        if((quizMode !='practice') && (quizMode !='testSeries')) {
          $('.preview-quiz').removeClass('d-lg-block').addClass('d-none');
            $('.practice-quiz-wrapper').css('margin','0 auto');
            $('.title-header').removeClass('d-lg-block').addClass('d-none');
            if (source != 'web') {
                userShowAudio();
            }
            if(quizMode=='test'){
                $('.quiz-profile .media').hide();
                $('.quiz-profile').addClass('justify-content-center');
               $('.progress-bar-vertical').addClass('d-none');
                setTimeout(showCounter, 1000);
            }
            else {

                $('.gamer-profile').addClass('d-block').removeClass('d-none');  //hide Profiles
                $('.playerbg .media ').addClass('animate__animated animate__fadeInLeft');//Player 1 slide animation
                $('.bot-wrapper .img-bot').addClass('animate__animated animate__fadeInUp');//Bot Animation
                setTimeout(slideBots, 1500);
                if (source != 'web') {
                    slidingAudio();
                }
                setTimeout(showUser, 4000);
                setTimeout(showCounter, 5500);
            }
        }
        else if((quizMode =='practice')||(quizMode =='testSeries')) {
            displayObjects();
            quizDetails();
            displayQueNumbers();
            // Initialize submit button visibility for practice/test modes
            setTimeout(updateSubmitButtonVisibility, 500);
        }
        if(quizType=='test'){
            if(sectionMst!=null&&!sectionMst==""){
                examMst=sectionMst;
                if((secpresent&&sectionDtl[0].totalTime!=null&&!sectionDtl[0].totalTime=="")||(sectionMst.totalTime!=null&&!sectionMst.totalTime=="")) {
                    startTimerForTest();
                    if(secpresent&&sectionDtl[0].totalTime!=null&&!sectionDtl[0].totalTime==""){
                        //disable subject selection
                        document.getElementById("sectionSelect").disabled=true;
                    }
                }
            }
        }
    }

    function displayQueNumbers(){
        var htmlStr='';
        if (sectionPresent){
            $("#sectionDisplayName").text(sectionSelectValue);
            var index = questions.findIndex(item => item.subject === sectionSelectValue);
            var qList;
            var td = [];
            var lastIndex;
            for(var t=0;t<questions.length;t++){
                if (questions[t].subject == qaObj.userAnswers[t].subject){
                    td.push(qaObj.userAnswers[t].subject);
                }
            }
            lastIndex = td.lastIndexOf(sectionSelectValue)+1;

            qList = range(index+1,lastIndex);

            console.log("qList",qList,td)
            for(var i=0;i<qList.length;i++){
                qIndex=i;
                htmlStr+='<div class="num-wrapper"  id="que-'+qList[i]+'" onclick="goToQue('+qList[i]+')">'+
                    '<div class="num-text-div"></div>'+
                    '<div id="attempt-status">Not Attempted</div>'+
                    '</div>'+
                    '</div>';
                    document.getElementById('total-que-tab').innerHTML=htmlStr;
            }

            var allQDIV = document.querySelectorAll('.num-text-div');
            for(var a=0;a<allQDIV.length;a++){
                allQDIV[a].innerHTML =  '<div class="num-text">'+(a+1)+'</div>';
            }
        }else{
            for(var i=1;i<questions.length+1;i++){
                qIndex=i;
                htmlStr+='<div class="num-wrapper" id="que-'+i+'" onclick="goToQue('+i+')">'+
                            '<div class="num-text">'+i+'</div>'+
                                '<div id="attempt-status">Not Attempted</div>'+
                            '</div>'+
                        '</div>';
                document.getElementById('total-que-tab').innerHTML=htmlStr;
            }
        }
       displayObjects();
    }

    function displayObjects(){
        for(var i=0;i<questions.length;i++) {
            qIndex=i;
            que_id=questions[i].id;
            if (sectionPresent){
                qSubject = questions[i].subject;
            }
            if(questions[i].ans4 =='Yes'){
                correctOption = 4;
            }
            else if(questions[i].ans3 =='Yes') {
                correctOption = 3;
            }
            else if(questions[i].ans2 =='Yes'){
                correctOption = 2;
            }
            else if(questions[i].ans1 =='Yes'){
                correctOption = 1;
            }
            else{
                    correctOption = 5;

            }
            userAnswersObj();
            testTimerObj();
        }
    }

    function goToQue(index){
        que_count=index-1;
        $('.show-explanation').hide();

        if((quizMode =='practice')||(quizMode =='testSeries')){
            if(qaObj.userAnswers.length!=que_count) { //set state only if not available
                setState(que_count);
            }
        }
        $('#btn-next').text('Next').removeClass('next-btn').attr('onclick','nextQue()');
        if(que_count===(questions.length - 1)){
            if((quizMode =='practice')||(quizMode =='testSeries')){
                $('#btn-next').text('Finish').addClass('next-btn').attr('onclick','finish()');
            }
        }
        closeExplanation();
        quizDetails();
        closePreview();
        // Update submit button visibility when jumping to a question
        setTimeout(updateSubmitButtonVisibility, 100);

    }
    //Explanation Show
    function showExplanation(index) {

        var answerOption1=replaceSymbols(questions[index].op1);
        answerOption1=checkLanguageAndImage(answerOption1);

        var answerOption2=replaceSymbols(questions[index].op2);
        answerOption2=checkLanguageAndImage(answerOption2);

        var answerOption3=replaceSymbols(questions[index].op3);
        answerOption3=checkLanguageAndImage(answerOption3);

        var answerOption4=replaceSymbols(questions[index].op4);
        answerOption4=checkLanguageAndImage(answerOption4);
        var answerOption5=replaceSymbols(questions[index].op5);
        answerOption5=checkLanguageAndImage(answerOption5);

        if(questions[index].answerDescription != ''){
        var explanation=replaceSymbols(questions[index].answerDescription);
        explanation=checkLanguageAndImage(explanation);
        }
        if(questions[index].ans1=='Yes'){
            que_answer=answerOption1;

        }
        else if(questions[index].ans2=='Yes'){
            que_answer=answerOption2;

        }
        else if(questions[index].ans3=='Yes'){
            que_answer=answerOption3;

        }
        else if(questions[index].ans4=='Yes'){
            que_answer=answerOption4;
        }
         else if(questions[index].ans5=='Yes'){
            que_answer=answerOption5;
        }


        if (quizMode == 'practice') {
            if (questions[index].answerDescription != '' && questions[index].answerDescription != 'null' && questions[index].answerDescription != null) {
                $('.show-explanation').show();
                    explanation=replaceImageUrlForApps(explanation);


                $('#explanation').html(explanation);
                que_answer=replaceImageUrlForApps(que_answer);
                        $('#correct-answer').html(que_answer);

            } else {
                $('.show-explanation').hide();
            }

        }

    }



    //Save User Answers and Bot Answers in Object
    var checkObj=false;
    var checkObj1=false;
    var botTime='';
     var timerArr=[];
     function arrangeSection( a, b ) {
        if ( a.subject.toLowerCase() < b.subject.toLowerCase()){
            return -1;
        }
        if ( a.subject.toLowerCase() > b.subject.toLowerCase()){
            return 1;
        }
        return 0;
    }
    function userAnswersObj(){
        if((quizMode =='practice')||(quizMode =='testSeries')){
            botTime=0;
        }
        else{
            botTime=(timeValue/100)-chatBotAnswerTime;
        }

        botTotalTime=Number(botTotalTime)+botTime;
        var tempAnswerObj=qaObj.userAnswers;


        for(var i=0;i<tempAnswerObj.length;i++){
            if(tempAnswerObj[i].queIndex==qIndex){
                checkObj=true;
                //alert('sss');
            }
            if(qIndex==tempAnswerObj.length){
                checkObj1=true;
                //alert('sss1');
            }
        }
        if(!checkObj) { //remove duplicates and update if obj if not available
            qaObj.userAnswers.push(
                {
                    id: que_id,
                    queIndex:qIndex,
                    correctOption: correctOption,
                    userOption: -1,
                    botTime: botTime,
                    userTime: 0,
                    subject:qSubject,
                    botAnswer: botAnswer,
                    reviewedQ:reviewedQ
                }
            );
        }
    }

    const range = (min, max) => {
        const arr = Array(max - min + 1)
            .fill(0)
            .map((_, i) => i + min);
        return arr;
    }

var checkObjs=false;
    var  checkObjs1=false;
    function testTimerObj(){
        var timerArray=timerArr;
        for(var i=0;i<timerArray.length;i++){
            if(timerArray[i].queIndex==qIndex){
                checkObjs=true;
            }
            if(qIndex==timerArray.length){
                checkObjs1=true;

            }
        }
        if(!checkObjs) {
            timerArr.push({
                queIndex: qIndex,
                queTime: 0
            })
        }

    }

//Shift to Next Que when timer Ends
    function nextQue(){
    isBotAnswered = false;
        if (secTimerPresent&&que_id==sectionQuestions[sectionQuestions.length-1].id && !(que_count===(questions.length - 1))){
            $("#secChangeAlertTxt").html("The next section will unlock when the time for this section is over.")
            $("#secChangeAlert").modal("show")
        }else{
            clockPause();
            clockReset();
            if(quizMode!='') {

                timerObj = timerArr[que_count];
                storeTimer = (timerObj.queTime) + (Number($('#test_timer').text()));
                sendTimer = {queTime: storeTimer};
                Object.entries(sendTimer).forEach(([key, value]) => {
                    timerObj[key] = value
                });

            }
            practicePrevious=false;
            $('.quizes').removeClass("animate__animated animate__bounceInRight animate__bounceInLeft");
            $('#countdown').text(questionTime);
            botAnswerFirst=false;
            userAnswerFirst=false;
            if(que_count < questions.length - 1){
                que_count++;
                que_numb++;
                quizDetails();
                // Update submit button visibility after navigation
                setTimeout(updateSubmitButtonVisibility, 100);
            }
            else{
                if(quizMode==''){

                    gameResult();
                }
                else{
                    submitAnswers();
                }
            }

            if (sectionPresent) {

                if (que_id == sectionQuestions[sectionQuestions.length - 1].id && !(que_count === (questions.length - 1))) {
                    sectionQuestions = [];
                    var index = subjectsList.findIndex(item => item === sectionSelectValue);
                    sectionSelectValue = subjectsList[index + 1];
                    $("#sectionSelect").val(sectionSelectValue);
                    $("#sectionDisplayName").text(sectionSelectValue);
                    for (var q = 0; q < storingQaForWeb.length; q++) {
                        if (storingQaForWeb[q].subject == sectionSelectValue) {
                            sectionQuestions.push(storingQaForWeb[q]);
                        }
                    }
                    displayQueNumbers();
                    checkQuestionState();

                    var sectionTime = sectionDtl[document.getElementById("sectionSelect").selectedIndex].totalTime;
                    if (sectionTime == undefined) {
                        // do nothing
                    } else {
                        changeWholeTime(sectionTime * 60);
                        startTimerForTest();
                    }
                }

            }else{

               if(que_count===(questions.length - 1)){
                   if((quizMode =='practice')||(quizMode =='testSeries')){
                       $('#btn-next').text('Finish').addClass('next-btn').attr('onclick','finish()');
                   }
                }
            }
            resetUIforNext();
            $('.show-explanation').hide();
            if((quizMode =='practice')||(quizMode =='testSeries')){

                if(qaObj.userAnswers.length!=que_count) { //set state only if not available
                    setState(que_count);
                }
            }

            $('.btn-review').removeClass().addClass('btn btn-review');
            if($('#que-'+(que_count+1)).hasClass('reviewed')){
                $('.btn-review').addClass('marked');
            }
            else{
                $('.btn-review').removeClass('marked');
            }
        }
    }
    $.expr[':'].textEquals = $.expr.createPseudo(function(arg) {
    return function( elem ) {
        return $(elem).text().match("^" + arg + "$");
    };
});
    function finish(){

        var number_of_reviewed =  $('#total-que-tab').children('.reviewed').length;
         var number_of_answered= $('#total-que-tab .num-wrapper #attempt-status:textEquals("Attempted")').length;
        var number_of_skipped= $('#total-que-tab .num-wrapper #attempt-status:textEquals("Not Attempted")').length;
            $('#noOfreviewed').html(number_of_reviewed);
                $('#noOfSkipped').html(number_of_skipped);
                $('#noOfAnswered').html(number_of_answered);
                 if(quizMode=='practice'){
                    $('.review-wrap').hide();
                 }
                   $('#review-modal').modal('show');

    }


    function setState(que_count){

        var saveState=qaObj.userAnswers[que_count];
        var correctOption=saveState.correctOption;
        var userOption=saveState.userOption;
        var explanationState=questions[que_count].answerDescription;

        if($('#que-'+(que_count+1)).hasClass('reviewed')){
                $('.btn-review').addClass('marked');
            }
            else{
                $('.btn-review').removeClass('marked');
            }
        if(userOption !=-1) {
            if(quizMode=='practice') {
                if (explanationState != '' && explanationState !=null && explanationState !='null') {
                    showExplanation(que_count);
                    $('.show-explanation').show();
                } else {
                    $('.show-explanation').hide();
                }
            }
            if (correctOption == userOption) {
                setTimeout(function () {
                    if(quizMode=='practice') {
                        setOption(correctOption, null);
                    }
                    else{
                        setOption(correctOption, userOption);
                    }
                }, 100);
            }
            else{
                setTimeout(function () {
                    setOption(correctOption,userOption);
                }, 100);
            }
        }

    }

    function prevQue(){
        if(secTimerPresent&&que_id==sectionQuestions[0].id) {
            $("#secChangeAlertTxt").html("Cannot go to previous section")
            $("#secChangeAlert").modal("show")
        }else{
            clockPause();
            clockReset();
            if(quizMode!='') {
                timerObj = timerArr[que_count];

                if (que_count == (questions.length - 1)) {
                    $('#btn-next').attr('onclick', 'nextQue()').text('Next').removeClass('next-btn');
                }

                storeTimer = Number($('#test_timer').text());
                sendTimer = {queTime: storeTimer};
                Object.entries(sendTimer).forEach(([key, value]) => {
                    timerObj[key] = value
                });
            }
            practicePrevious=true;

            if(que_count==(questions.length - 1)){
                $('#btn-next').attr('onclick','nextQue()').text('Next').removeClass('next-btn');
            }
            if (sectionPresent) {
                if (que_id == sectionQuestions[0].id) {
                    var index = subjectsList.findIndex(item => item === sectionSelectValue);
                    sectionSelectValue = subjectsList[index - 1];

                    $("#sectionSelect").val(sectionSelectValue);
                    $("#sectionDisplayName").text(sectionSelectValue);
                    sectionQuestions = [];
                    for (var q = 0; q < storingQaForWeb.length; q++) {
                        if (storingQaForWeb[q].subject == sectionSelectValue) {
                            sectionQuestions.push(storingQaForWeb[q]);
                        }
                    }
                    displayQueNumbers();
                    checkQuestionState();
                }
            }else{

            }
            if(que_count !=0) {
                $('.quizes').removeClass("animate__animated animate__bounceInRight animate__bounceInLeft");
                que_count--;
                que_numb--;
                quizDetails();
                $('.show-explanation').hide();
                setState(que_count);
                // Update submit button visibility after navigation
                setTimeout(updateSubmitButtonVisibility, 100);
            }
            if($('#que-'+(que_count+1)).hasClass('reviewed')){
                $('.btn-review').addClass('marked');
            }
            else{
                $('.btn-review').removeClass('marked');
            }
        }
    }


   function setOption(correctOption,userOption){
     if(quizMode =='practice' || quizMode =='') {
       if(showCorrectAnswerState){


             $('.option').eq(correctOption - 1).addClass('option correct animate__bounceIn');
              showCorrectAnswerState=false;

      } else{

        $('.option').eq(correctOption - 1).addClass('correct');
        }
        disableOptions();
        if (userOption != null) {
            $('.option').eq(userOption - 1).addClass('incorrect');
        }
    }
    else if(quizMode=='testSeries'){
        $('.show-explanation').hide();
        if (userOption != null) {
            $('.option').eq(userOption - 1).addClass('incorrect clicked');
        }
    }


}


    //hide and animation when move to next ques

    function resetUIforNext(){
        $('#app,#countdown').show();
        $('.question-wrapper').removeClass("animate__animated animate__fadeInDown");
        $('.progress-bar-vertical.player1').removeClass('progress-correct progress-incorrect');
        $('.progress-bar-vertical.player2').removeClass('progress-correct progress-incorrect');
        $('#player1,#player2').css({'height':'0%','background': ''});
    }

//Show timer and profile and start quiz.
    function quizDetails(){
        if(source!='web'){
        if(quizMode ==''){
            $('.audioChange').show();
        }
        else{
            $('.audioChange').hide();
        }
        }
        $('.directions').addClass('d-none');
        if((quizMode !='practice') && (quizMode !='testSeries')) {
            $('.quiz-profile').removeClass('d-none').addClass('d-flex');
            $('.no-counter').addClass('d-none');
        }
        else if((quizMode =='practice')||(quizMode =='testSeries')){
         if($(window).width() <768) {
                $('.question-wrapper>.question-no').addClass('d-none');
            }
            $('.question-wrapper>.question-no').addClass('d-none');
            if(que_count==0){
                $('#btn-prev').prop("disabled", true);
            }
            else{
                $('#btn-prev').prop("disabled", false);
            }
        }
        if(quizMode=='testSeries'){
            $('.question-wrapper').addClass('test-series');
        }

        setTimeout(displayQuestions);
    }

//Display Questions
    function displayQuestions(){

        $('.quizes').removeClass('d-none').addClass('d-flex');
        botDetails();
        if(source!='web'){
            queAudio();
        }
        showQuestions(que_count);

        // Update submit button visibility when displaying questions
        updateSubmitButtonVisibility();

        if((quizMode !='practice') && (quizMode !='testSeries')) {
            setTimeout(answerAnimation,1000);
            setTimeout(removeAnimation,2000);
            setTimeout(startTimer, 2000);
            setTimeout(startTimers, 2000);
            setTimeout(addOptionSelectAttr,2000);
        }
        else if(quizMode =='testSeries' || quizMode =='practice'){
            $('.que-options').show();
            if(practicePrevious){
                $('.quizes').addClass("animate__animated animate__bounceInLeft");
            }
            else{
                $('.quizes').addClass("animate__animated animate__bounceInRight");
            }
            setTimeout(startTestTimer);
            setTimeout(addOptionSelectAttr,0);
        }
        else{
            $('.que-options').show();
            if(practicePrevious){
                $('.quizes').addClass("animate__animated animate__bounceInLeft");
            }
            else{
                $('.quizes').addClass("animate__animated animate__bounceInRight");
            }
            setTimeout(addOptionSelectAttr,0);
        }
        if((userScore-botScore)>=2){

            botRun(1,chatBotAnswerTime);
        }
        else{

            botRun(botAnswer,chatBotAnswerTime);
        }
        addMathjax();
    }



function removeAnimation(){
 $('.que-options .option:first-child').removeClass("animate__animated animate__fadeInDown");
    $('.que-options .option:nth-child(2)').removeClass("animate__animated animate__fadeInLeft");
    $('.que-options .option:nth-child(3)').removeClass("animate__animated animate__fadeInRight");
    $('.que-options .option:last-child').removeClass("animate__animated animate__fadeInUp");
    }

    //Gives bot's answer and time randomly
    function botDetails(){
        var botAnswers=[0,1];
        botAnswer=botAnswers[Math.floor(Math.random() * botAnswers.length) | 0];//Randomise Bot Answer 0=false,1=true

        var timerBot=(timeValue/100)-2;  //End time for bot answer
        chatBotAnswerTime = randomIntFromInterval(3,timerBot);//Randomise when bot to answer

    }

var que_id;
    //Show Que and Answer one by one

    function showQuestions(index){
        qIndex=index;
        que_id=questions[index].id;
        $('.que-options').hide();
        $('.question-wrapper').hide();

        var que_no=$(".question-no");
        //Que-no
        que_no.text((Number(index)+1)+' of '+questions.length);
        //que-text
        var que_text = document.querySelector(".que_text");
        var btn_review = document.querySelector(".btn-review");
        var que_tag = replaceSymbols(questions[index].ps);
        var que_direction= document.querySelector("#direction");
        var directions=questions[index].directions;
        var favBtn = document.querySelector('.favQuestionStar');

        que_tag=checkLanguageAndImage(que_tag);

        var answerOption1=replaceSymbols(questions[index].op1);
        answerOption1=checkLanguageAndImage(answerOption1);

        var answerOption2=replaceSymbols(questions[index].op2);
        answerOption2=checkLanguageAndImage(answerOption2);

        var answerOption3=replaceSymbols(questions[index].op3);
        answerOption3=checkLanguageAndImage(answerOption3);

        var answerOption4=replaceSymbols(questions[index].op4);
        answerOption4=checkLanguageAndImage(answerOption4);
        var answerOption5=replaceSymbols(questions[index].op5);
        answerOption5=checkLanguageAndImage(answerOption5);

        if (source != 'web'){
               que_tag=replaceImageUrlForApps(que_tag);
                answerOption1 = replaceImageUrlForApps(answerOption1);
                answerOption2 = replaceImageUrlForApps(answerOption2);
                answerOption3 = replaceImageUrlForApps(answerOption3);
                answerOption4 = replaceImageUrlForApps(answerOption4);
                 answerOption5 = replaceImageUrlForApps(answerOption5);
        }

        if(questions[index].ans1=='Yes'){
            que_answer=answerOption1;
            correctOption=1;
        }
        else if(questions[index].ans2=='Yes'){
            que_answer=answerOption2;
            correctOption=2;
        }
        else if(questions[index].ans3=='Yes'){
            que_answer=answerOption3;
            correctOption=3;
        }
        else if(questions[index].ans4=='Yes'){
            que_answer=answerOption4;
            correctOption=4;
        }
        else{
            que_answer=questions[index].op5;
            correctOption=5;
        }


        answerUI(answerOption1,answerOption2,answerOption3,answerOption4,answerOption5);




        que_text.innerHTML = que_tag;
        if(directions !=null) {
            directions=replaceSymbols(directions);
            directions=checkLanguageAndImage(directions);
            directions=replaceImageUrlForApps(directions);
            que_direction.innerHTML = directions;
             $('.directions').removeClass('d-none');
        }
       $('.btn-review').addClass('btn-'+(index+1)).attr('onclick','markForReview('+index+')');
        option_list.innerHTML = option_tag;
        userAnswersObj();
        $('.question-wrapper').show();
        if((quizMode !='practice') && (quizMode !='testSeries')) {
            $('.question-wrapper').addClass("animate__animated animate__fadeInDown");
        }

        let favHeader = ""
        if(quizType=='test' || quizMode =='testSeries'){
            favHeader += "<div style='margin=left:10px;'>"
            if (questions[index].marks && questions[index].marks!=="" && questions[index].marks!=null && questions[index].marks!=" ") {
                favHeader +="<span style='color: green !important;'><i class='fa-solid fa-check' style='color: green !important;'></i> +" + questions[index].marks + "</span> ";
            }
            if (questions[index].negativeMarks && questions[index].negativeMarks!=="" && questions[index].negativeMarks!=null && questions[index].negativeMarks!=" "){
                favHeader += "<span> | </span>" +
                    "<span style='color: red !important;'><i class='fa-solid fa-xmark' style='color: red !important;'></i> -"+questions[index].negativeMarks+"</span>";
            }
            favHeader +="</div>";
            favBtn.innerHTML = favHeader;
        }

        if (sectionPresent){
            if (que_id==sectionQuestions[sectionQuestions.length-1].id && que_count!=(questions.length - 1)){
                $('#btn-next').text('Next Section');
            }else if(que_count===(questions.length - 1)){
                $('#subNewBtn-1').removeClass('d-block').addClass('d-none');
                $('#subNewBtn-2').removeClass('d-lg-block');
                $('#btn-next').text('Finish').attr('onclick','finish()');
            }
            else{
                $('#btn-next').text('Next');
            }

            if ((sectionQuestions[0].id == que_id )&& que_count!=0){
                $('#btn-prev').text('Previous Section');
            }else{
                $('#btn-prev').text('Previous');
                document.getElementById("btn-prev").disabled=false;
            }
        }
        showText();
    }


    function answerUI(answerOption1,answerOption2,answerOption3,answerOption4,answerOption5){
         option_tag = '<div class="option" data-ans="1"><span>'+ answerOption1.trim() +'</span></div>'
            + '<div class="option" data-ans="2"><span>'+ answerOption2.trim() +'</span></div>';
              if(answerOption3 !=''){
            option_tag += '<div class="option" data-ans="3"><span>'+ answerOption3.trim() +'</span></div>';
            }
             if(answerOption4 !=''){
             option_tag += '<div class="option" data-ans="4"><span>'+ answerOption4.trim() +'</span></div>';
            }
            if(answerOption5 !=''){
            option_tag+= '<div class="option" data-ans="5"><span>'+ answerOption5.trim() +'</span></div>';
            }

        return option_tag;
    }

    //Replace image url for apps
   function replaceImageUrlForApps(answerOptions){
        var myAnswerOption ='';

            if (answerOptions.indexOf('src') != -1){
            answerOptions = answerOptions.split('/funlearn/').join(serviceURL +'funlearn/');

                 myAnswerOption = answerOptions;

            }
            else{
                myAnswerOption=answerOptions;
            }

            return myAnswerOption;
        }
    //check lang and image in response

    function checkLanguageAndImage(item){
      if(language=='hindi') {
         var img='';
              if(item.match(/<img/)) {
                img= item.substr(item.indexOf("<"));
              }
         item = item.substr(0,item.indexOf("~~"));
         item = item + img;

      }else if(language=='kannada') {
         var img='';
              if(item.match(/<img/)) {
                img= item.substr(item.indexOf("<"));
              }
         item = item.substr(0,item.indexOf("~~"));
         item = item + img;

      }else if(language=='english') {
         item = item.substr(item.indexOf("~~")+2);
         item =item.split('<span class="math-tex">').join('');
         item =item.split('</span>').join('');
       }else{
         item = item;
         item =item.split('<span class="math-tex">').join('');
         item =item.split('</span>').join('');
      }
      if(item.indexOf('</table>') > -1){
        item=item.split("<table").join("<div class='table-responsive'><table class='table'");

        item=item.split('</table>').join('</table></div>');
      }

      return item;
 }

    //Check and replace symbols.

    function replaceSymbols(item){
      var replacedItem='';
      if(item !=null){
         replacedItem=item;
      }
      return replacedItem;
    }

    //Adding option select attributes

    function addOptionSelectAttr(){
        var option = option_list.querySelectorAll(".option");

        // set onclick attribute to all available options
        for(var i=0; i < option.length; i++){
            option[i].setAttribute("onclick", "optionSelected(this)");
        }

    }



    // Start question timer
    function startTimer(){
       span = document.getElementById('countdown');
       countdown = new Countdown(span, questionTime);
       countdown.start();
  }


function startTestTimer(){
      clockStart();
  }

    //User Answer Select
    var answerObj;
    var correcAns = '';
    var sendUserAnswer;
    var selectUserAnswerIndex='';
    var sendTimer;
    var timerObj;
    var storeTimer;
    function optionSelected(answer){
      $('.option').removeClass('correct incorrect clicked');
<!--       var userAns =  answer.innerHTML; //getting user selected option-->
<!--        userAns = userAns.replace('<span>','');-->
<!--        userAns = userAns.replace('</span>','');-->
<!--        userAns = userAns.trim();-->
        //var userAns = answer.textContent;

        var userAns = $(answer).attr("data-ans")

         answerObj=qaObj.userAnswers[que_count];
         timerObj=timerArr[que_count];
        correcAns = ''; //getting correct answer from array
         selectUserAnswerIndex='';
        if(questions[que_count].ans1=='Yes'){
            checkLangauge();
            correctOption=1;
        }
        else if(questions[que_count].ans2=='Yes'){
            checkLangauge();
            correctOption=2;
        }
        else if(questions[que_count].ans3=='Yes'){
            checkLangauge();
            correctOption=3;
        }
        else if(questions[que_count].ans4=='Yes'){
            checkLangauge();
            correctOption=4;
        }
        else{
            checkLangauge();
            correctOption=5;
        }

        if(quizMode!='') {
            storeTimer = (Number($('#test_timer').text()));
            sendTimer = {queTime: storeTimer};


            $.each(sendTimer,function(key,value){
                timerObj[key] = value;
            });//reassign values to particular id of answerobj

        }
        if(quizMode!=='testSeries') {
            disableOptions();//disable once user selects answer
        }

        if((quizMode =='') || (quizMode =='test')){
            userPlay(newTime,userAns,correctOption,'',answer);//User play logic
        }
        if(quizMode =='test'){
            $('.progress-bar-vertical').addClass('d-none');
        }
        else if(quizMode =='testSeries'){
            $('.progress-bar-vertical').addClass('d-none');
            userPlay(0,userAns,correctOption,storeTimer,answer);
        }
        else if((quizMode =='practice')){
            $('.progress-bar-vertical').addClass('d-none');
            $('#btn-next').attr('onclick','');
            $('#btn-prev').attr('onclick','');
            userPlay(0,userAns,correctOption,storeTimer,answer);
        }
        if((quizMode =='practice') || (quizMode =='testSeries')) {
            showExplanation(que_count);
            checkAttempted(que_count + 1);
        }


    }


    function checkAttempted(que_count){
        $("#que-"+que_count).find('#attempt-status').text('Attempted');
        $("#que-"+que_count).css('border','1px solid #2980b9');
    }


    function checkLangauge(){
        if(language=='hindi') {
            correcAns = correcAns.substr(0, correcAns.indexOf("~~"));
        }
        else if(language=='kannada') {
            correcAns = correcAns.substr(0, correcAns.indexOf("~~"));
        }
        else if(language=='english') {
            correcAns = correcAns.substr(correcAns.indexOf("~~") + 2);
        }

        else{
            correcAns=questions[que_count].op1.trim();
        }
    }


//Disable Options
    function disableOptions(){
        var allOptions = option_list.children.length; //getting all option items
        for(i=0; i < allOptions; i++){
            $(option_list.children[i]).attr("onclick", "");

        }
    }
    function randomIntFromInterval(min, max) { // min and max included
        return Math.floor(Math.random() * (max - min + 1) + min)
    }
var showCorrectAnswerState=false;
    //Show Right Answers once bot and user answered
function showCorrectAnswer(){
    var allOptions = option_list.children.length; //getting all option items

    showCorrectAnswerState=true;
    for(i=0; i < allOptions; i++){

        var tempAnswer =  option_list.children[i].innerHTML; //getting user selected option
        tempAnswer = tempAnswer.replace('<span>','');
        tempAnswer = tempAnswer.replace('</span>','');

        tempAnswer=tempAnswer.trim();
        que_answer=que_answer.trim();
        que_answer=que_answer.trim();

         if(showCorrectAnswerState){
                  setOption(correctOption, null);
                  }

        if((quizMode =='practice')||(quizMode =='testSeries')){

            if(que_count===(questions.length - 1)){
                $('#btn-next').attr('onclick','finish()');
            }
            else {
                $('#btn-next').attr('onclick', 'nextQue()');
            }
            $('#btn-prev').attr('onclick','prevQue()');
        }
    }
    for(i=0; i < allOptions; i++){
        option_list.children[i].classList.add("disabled"); //once user select an option then disabled all options
    }
}
function submitFromModal(){
testSubmitted=true;
     $('.quizes').removeClass('d-flex').addClass('d-none');
                $('.app-header,.app-footer').hide();
                $('#review-modal').modal('hide');
                submitAnswers();
    }

function closeExplanation(){
    $('.explanation-wrapper').addClass('animate__animated animate__bounceOutDown');
    setTimeout(resetExplanation,1000);
}
function resetExplanation(){
    $('.explanation-wrapper').hide().removeClass('animate__animated animate__bounceOutDown');
}
    //Clear Timer and timer progress immediately
    function clearImmediate(){
        $('#app,#countdown').hide();
        $('#countdown').text('00');
        clearInterval(timeObj.timer); //clear counter
        clearTimerBar();
        document.getElementById("base-timer-path-remaining").setAttribute("stroke-dasharray", '283');
    }

    //mark for review
    function markForReview(index){
        index=Number(index);

        $('.btn-'+(index+1)).toggleClass('marked');
        if($('.btn-'+(index+1)).hasClass('marked')) {
            $('#que-' + (index + 1)).addClass('reviewed').find('.num-text').html((index + 1) + ' ' + '<i class="material-icons">push_pin</i>');
            qaObj.userAnswers[index].reviewedQ=true;
        }
        else{
            $('#que-' + (index + 1)).removeClass('reviewed').find('.num-text').html((index + 1));
            qaObj.userAnswers[index].reviewedQ=false;
        }
    }

    //Submit Answer To server
    function submitAnswers(){

        if(quizMode!=''){
            $('.loader-submit').show();
        }
         $('.preview-quiz').removeClass('d-lg-block').addClass('d-none');
        $('.practice-quiz-wrapper').css('margin','0 auto');
        $('.title-header').removeClass('d-lg-block').addClass('d-none');

        var correctArr=[];
       var answerByUser=qaObj;
    var qaAnswers=answerByUser.userAnswers;
        for(var i=0;i<questions.length;i++) {

                if (qaAnswers[i].userOption === qaAnswers[i].correctOption) {
                    correctArr.push(qaAnswers[i].userOption);

            }
      }
      userScore=correctArr.length;

        $('#noofque').text(questions.length);

        if(quizMode ==''){
         $('.updateScore').text(userScore);

         }
        else{
        userScore=correctArr.length;
        $('.updateScore').text(correctArr.length);

        }

        if(fromShop=='disabled'){
            $('.button-results').addClass('d-none');
        }
         if(quizMode =='') {
            if(dailyTest){
                if(realDailyTestDtlId!=null|| realDailyTestDtlId!=''||realDailyTestDtlId!='null'){
                    var quizDetails = {
                    matchStatus: userWin,
                    userTime: userTotalTime,
                    botTime: botTotalTime,
                    userPoints: userScore,
                    botPoints: botScore,
                    userName: userId,
                    botName: botName,
                    language:language,
                    siteId: siteId,
                    source: source,
                    quizType:quizType,
                    noOfQuestions:noOfQuestions,
                    dailyTestDtlId:testId,
                    realDailyTestDtlId:realDailyTestDtlId
                };
            }else{
                var quizDetails = {
                    matchStatus: userWin,
                    userTime: userTotalTime,
                    botTime: botTotalTime,
                    userPoints: userScore,
                    botPoints: botScore,
                    userName: userId,
                    botName: botName,
                    language:language,
                    siteId: siteId,
                    source: source,
                    quizType:quizType,
                    noOfQuestions:noOfQuestions,
                    dailyTestDtlId:testId,
                };
            }
        } else if ((weekly == 'yes') || (monthly == 'yes')) {
        var quizDetails = {
            matchStatus: userWin,
            userTime: userTotalTime,
            botTime: botTotalTime,
            userPoints: userScore,
            botPoints: botScore,
            userName: userId,
            botName: botName,
            language: language,
            siteId: siteId,
            source: source,
            quizType:quizType,
            noOfQuestions:noOfQuestions
        };
    } else {
        var quizDetails = {
            resId: resourcesId,
            quizId: myQuizId,
            matchStatus: userWin,
            userTime: userTotalTime,
            botTime: botTotalTime,
            userPoints: userScore,
            botPoints: botScore,
            userName: userId,
            botName: botName,
            language: language,
            siteId: siteId,
            source: source,
            quizType:quizType,
            noOfQuestions:noOfQuestions
        };
    }
}else if(quizMode!=''){
        if(dailyTest){
            if(realDailyTestDtlId!=null|| realDailyTestDtlId!=''||realDailyTestDtlId!='null'){
                var quizDetails = {
                    matchStatus: userWin,
                    userTime: userTotalTime,
                    botTime: botTotalTime,
                    userPoints: userScore,
                    botPoints: botScore,
                    userName: userId,
                    botName: botName,
                    language:language,
                    siteId: siteId,
                    source: source,
                    quizType:quizType,
                    noOfQuestions:noOfQuestions,
                    dailyTestDtlId:testId,
                    realDailyTestDtlId:realDailyTestDtlId,
                };
            }else{
                var quizDetails = {
                    matchStatus: userWin,
                    userTime: userTotalTime,
                    botTime: botTotalTime,
                    userPoints: userScore,
                    botPoints: botScore,
                    userName: userId,
                    botName: botName,
                    language:language,
                    siteId: siteId,
                    source: source,
                    quizType:quizType,
                    noOfQuestions:noOfQuestions,
                    dailyTestDtlId:testId,
                }
            }
        } else if ((weekly == 'yes') || (monthly == 'yes')) {
            var quizDetails = {
                matchStatus: userWin,
                userTime: userTotalTime,
                botTime: botTotalTime,
                userPoints: userScore,
                botPoints: botScore,
                userName: userId,
                botName: botName,
                language: language,
                siteId: siteId,
                source: source,
                quizType:quizType,
                noOfQuestions:noOfQuestions
            };
        }
        else{
            var quizDetails = {
                resId: resourcesId,
                quizId: myQuizId,
                matchStatus: userWin,
                userTime: userTotalTime,
                botTime: botTotalTime,
                userPoints: userScore,
                botPoints: botScore,
                userName: userId,
                botName: botName,
                language: language,
                siteId: siteId,
                source: source,
                quizType:quizType,
                noOfQuestions:noOfQuestions
            };
        }
    }
        qaObj = Object.assign(qaObj,quizDetails);
        qaObj=toString(qaObj);

       if(source!='web'){
           submitCallback(qaObj);
           if(previous=='no'){
               $('.btn-prev').prop('disabled', true);
           }
           if(next=='no'){
               $('.btn-next').prop('disabled', true);
           }
       }
      else {

            var saveData = $.ajax({
                type: 'POST',
                url: "/prepjoy/quizSubmit",
                contentType: "application/json",
                dataType: "json",
                data: qaObj,
                success: function (response) {
                    if (response.status == 'success') {
                        $('.loader-submit').hide();
                        if(quizMode =='') {
                            $('#winnerModal').modal('hide');
                            currentBadge(response);
                            $('.resultWrapper').removeClass('d-none');
                        }
                        else if(quizMode =='practice' || quizMode =='test' || quizMode =='testSeries'){
                           $('.quizes,.quiz-profile').removeClass('d-flex').addClass('d-none');
                            $('.practice-result').removeClass('d-none').addClass('d-flex');
                            $('.nav-tabs a[href="#all"]').tab('show');

                            currentBadge(response);
                        }
                        if(quizMode =='test'){
                            $('#practice-summary').text('Test Summary');
                        }
                    }
                }
            });
        }
    }




//convert Json Obj to Strings
    function toString(o) {
        Object.keys(o).forEach(function(k){
            if (typeof o[k] === 'object') {
                return toString(o[k]);
            }

            o[k] = '' + o[k];
        });

        return JSON.stringify(o);
    }


    //show Preview

    function showPreview(){
         $('.preview-quiz').show().addClass('animate__animated animate__bounceInUp').removeClass('animate__animated animate__bounceInUp',{duration:500});
         $('.practice-quiz-wrapper').addClass('d-none');
         $('#review-modal').modal('hide');

    }
    function closePreview(){
        $('.preview-quiz').addClass('animate__animated animate__bounceOutDown').hide().removeClass('animate__animated animate__bounceOutDown',{duration:500});
        $('.practice-quiz-wrapper').removeClass('d-none');
    }

//lang select

    function langSelect(){
        var lang = document.getElementById("languageSelect");
        if(lang.value == "english"){
           language='english';
        }else{
            language='hindi';
        }
    }

    //timeSelect

    function timeSelect(){
        var time = document.getElementById("timeSelect");
        changeTestTime(time.value);
    }


    function onStart(){
        //$('.main-page').hide();
        $('.main-page').addClass('d-none').removeClass('d-flex');
        if((quizMode=='practice') ||(quizMode=='testSeries')){
            $('.practice-container').removeClass('d-none');
        }
        startQuiz();
    }


    //Formulas
    function addMathjax(){

        MathJax = { mml: { forceReparse: true } }
        // $('head').append('<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML" >');
        $('head').append('<script src="https://polyfill.io/v3/polyfill.min.js?features=es6">')
        $('head').append('<script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js">')
    }

    if(quizMode=='practice') {
        $('.swiper-container').on('swiped-left', function (e) {
            nextQue();
        });
        $('.swiper-container').on('swiped-right', function (e) {
            prevQue();
        });
        $('.action-wrapper').on('swiped-down', function (e) {
            $('.action-wrapper').hide();
        });
    }




    //showing share b no we shoukl
    function showShareButton(){
        $('#sharebtn').removeClass('invisible');
    }

    //callback for shareicon showing
    function showShareIcon(){
        if(source=='android'){
            JSInterface.sharingModal();
        }
        else if(source=='ios'){
            webkit.messageHandlers.sharingModal.postMessage('share button');
        }
    }

    //callback for showing rating modal
    function showRating(){
        if(source=='android'){
            JSInterface.ratingModal();
        }
        else if(source=='ios'){
            webkit.messageHandlers.ratingModal.postMessage('rating modal');
        }
    }
//Report question
    var questionId;
    var reportObj;
    var questionReportObj;

    function showReportModal(questionNo){
        questionId = questionNo;
        $('#prep-report-question').modal('show');
    }

    function reportSubmit(){

        var issuesSelected="";
        if(document.getElementById("spellingMistake").checked){
            issuesSelected="Spelling mistake";
        }
        if(document.getElementById("directionNotGiven").checked){
            issuesSelected+=(issuesSelected==""?"":",")+"Directions not given";
        }
        if(document.getElementById("imageNotVisible").checked){
            issuesSelected+=(issuesSelected==""?"":",")+"Image not visible";
        }
        if(document.getElementById("incompleQuestion").checked){
            issuesSelected+=(issuesSelected==""?"":",")+"Incomplete questions";
        }
        if(document.getElementById("otherIssues").checked){
            issuesSelected+=(issuesSelected==""?"":",")+"Other issues";
        }

        var moreInformation = document.getElementById("moreInformation").value;

        questionReportObj={
            selectedIssue:issuesSelected,
            questionIssueId:questionId,
            issuetext:moreInformation
        }
        questionReportObj=JSON.stringify(questionReportObj);
         reportQuiz(questionReportObj);
            $('#prep-report-question').modal('hide');

    }


    //callback for reporting quiz questions
    function clearModal(){
        $('#moreInformation').val('');
        $("#spellingMistake,#directionNotGiven,#imageNotVisible, #incompleQuestion,#otherIssues").prop('checked', false);

    }
    function reportQuiz(reports){

        $('#prep-report-question').modal('hide');
        if(source=='android'){
            JSInterface.reportQuizIssue(reports);
        }
        else if(source=='ios'){
            webkit.messageHandlers.reportQuizIssue.postMessage(reports);
        }
    }


    function showAction(data){
        if(source=='android') {
         JSInterface.resizeWebview(false);
        }else {
         webkit.messageHandlers.resizeWebview.postMessage(false);
        }
        $('.action-wrapper').show();

        if(data=='rematch'){
            $('#btn-play').attr('onclick','playAgain()');
            $('#btn-practice').attr('onclick','practiceQuiz()');
            $('#btn-test').attr('onclick','testQuiz()');
        }
        else if(data=='previousMatch'){
            $('#btn-play').attr('onclick','previousQuiz()');
            $('#btn-practice').attr('onclick','practicePreviousQuiz()');
            $('#btn-test').attr('onclick','testPreviousQuiz()');
        }
        else if(data=='nextMatch'){
            $('#btn-play').attr('onclick','nextQuiz()');
            $('#btn-practice').attr('onclick','practiceNextQuiz()');
            $('#btn-test').attr('onclick','testNextQuiz()');
        }
    }


function showSummary(){
    showResultSummary(qaObj,questions)
}
function showResultSummary(qaObj,questions){
    if(source=='android'){
        JSInterface.openQuizSummary(qaObj,JSON.stringify(questions));
    }else if(source=='ios'){
        webkit.messageHandlers.openQuizSummary.postMessage(qaObj,questions);
    }
}

function practiceQuiz(){
    if (source == 'android') {
        JSInterface.practiceAgainQuiz();
    } else if (source == 'ios') {
        webkit.messageHandlers.practiceAgainQuiz.postMessage('');
    }
}
function testQuiz(){
    if (source == 'android') {
        JSInterface.testAgainQuiz();
    } else if (source == 'ios') {
        webkit.messageHandlers.testAgainQuiz.postMessage('');
    }
}
function practicePreviousQuiz(){
    if (source == 'android') {
        JSInterface.practicePreviousQuiz();
    } else if (source == 'ios') {
        webkit.messageHandlers.practicePreviousQuiz.postMessage('');
    }
}
function testPreviousQuiz(){
    if (source == 'android') {
        JSInterface.testPreviousQuiz();
    } else if (source == 'ios') {
        webkit.messageHandlers.testPreviousQuiz.postMessage('');
    }
}
function practiceNextQuiz(){
    if (source == 'android') {
        JSInterface.practiceNextQuiz();
    } else if (source == 'ios') {
        webkit.messageHandlers.practiceNextQuiz.postMessage('');
    }
}
function testNextQuiz(){
    if (source == 'android') {
        JSInterface.testNextQuiz();
    } else if (source == 'ios') {
        webkit.messageHandlers.testNextQuiz.postMessage('');
    }
}
function playAgain(){
    if(source=='android'){
        JSInterface.restartGame(nextChallenger,nextchallengerPlace);
    }
    else if(source=='ios'){
        webkit.messageHandlers.restartGame.postMessage('');
    }
}
function previousQuiz(){
    if(source!='web'){
        if(source =='android'){
            JSInterface.prevQuiz();
        }
        else{
            webkit.messageHandlers.prevQuiz.postMessage('');
        }
    }
}
function nextQuiz(){
    if(source!='web'){
        if(source =='android'){
            JSInterface.nextQuiz();
        }
        else{
            webkit.messageHandlers.nextQuiz.postMessage('');
        }
    }
}

function getHistory(historyData){
console.log(historyData);
    var qaAnswersList = historyData;
    var qaQuestionsList = historyData.results;
    delete qaAnswersList.results;
    qaAnswersList.analytics = true;
    quizMode = qaAnswersList.quizType;
    $('#user-status').hide();

    qaObj = JSON.stringify(qaAnswersList);
    questions = qaQuestionsList;
    $('#noofque').text(questions.length);

    $('.practice-container').removeClass('d-none');
    $('.app-header,.app-footer').hide();
    $('body').css('overflow-y','auto');
    $('.quizes,.quiz-profile').removeClass('d-flex').addClass('d-none');
    $('.practice-result').removeClass('d-none').addClass('d-flex');
    $('.nav-tabs a[href="#all"]').tab('show');
    $('.badges,.balance-points').hide();
    $('.button-results').hide();
    if(source=='android') {
       JSInterface.resizeWebview(true);
    }else {
       webkit.messageHandlers.resizeWebview.postMessage(true);
    }
    openSummary(qaAnswersList,qaQuestionsList);
}
function closeButtons(){
    $('.action-wrapper').hide();
    if(source=='android') {
     JSInterface.resizeWebview(true);
    }else {
     webkit.messageHandlers.resizeWebview.postMessage(true);
    }
}
  $(document).on('hide.bs.modal','#prep-report-question', function () {
        $('.form-check-input').prop('checked',false);
        $('#moreInformation').val('');

    });
    function showText() {
        // Configure/customize these variables.
        var showChar = 100;  // How many characters are shown by default
        var ellipsestext = "...";
        var moretext = "Show less";
        var lesstext = "Show more";


        $('.more').each(function() {
            var content = $(this).html();

            if(content.length > showChar) {

                var c = content.substr(0, showChar);
                var h = content.substr(showChar, content.length - showChar);

                var html = c + '<span class="moreellipses">' + ellipsestext+ '</span><span class="morecontent"><span>' + h + '</span><a href="" class="morelink">' + moretext + '</a></span>';

                $(this).html(html);
            }

        });

        $(".morelink").click(function(){
            if($(this).hasClass("less")) {
                $(this).removeClass("less");
                $(this).html(moretext);
            } else {
                $(this).addClass("less");
                $(this).html(lesstext);
            }
            $(this).parent().prev().toggle();
            $(this).prev().toggle();
            return false;
        });
    }


    //SELECTING QUESTIONS RANGE UI CHANGES

    var quizRangeSelection = document.querySelector(".quiz__rangeSelection");
    var questionRangeInput = document.getElementById('questionRangeInput');
    var questionRangeFromInput = document.getElementById('questionRangeFromInput');
    var questionRangeToInput = document.getElementById('questionRangeToInput');

    var defaultRange = true;
    var limitedRange = false;
    var startingIndex=0;
    var endingIndex=0;

    function constructLanguageBtn(language1,language2){
        var rHtml = "";
        console.log(language1,language2);
        if(language1 != undefined && language2 != undefined){
            if(language1 !="" && language2!=""){
                var languageSortedList = [language1,language2].sort();
                language1 = languageSortedList[0];
                language2 = languageSortedList[1];
            }

            if(language1 != "" || language1 != null){
                rHtml += "<div>\n"+
                    "<input type='radio' id="+language1.toLowerCase()+" name='language' value="+language1.toLowerCase()+" checked>\n"+
                    "<label for="+language1.toLowerCase()+">"+language1+"</label>\n"+
                "</div>\n";
            }
            if(language2 != "" && language2 !=null){
                rHtml += "<div>\n"+
                    "<input type='radio' id="+language2.toLowerCase()+" name='language' value="+language2.toLowerCase()+">\n"+
                    "<label for="+language2.toLowerCase()+">"+language2+"</label>\n"+
                "</div>";
            }
            document.getElementById('languageRadioBtn').innerHTML = rHtml;

            if(language2 !=""){
                language=language1.toLowerCase();
            }else{
                language = "";
            }

            document.querySelectorAll("input[name='language']").forEach((input) => {
            input.addEventListener('change', function(e){
                if(e.target.value == "english"){
                    language='english';
                }else if(e.target.value == "hindi"){
                    language='hindi';
                }else if(e.target.value == "kannada"){
                    language='kannada';
                }
            });
        });
        }else{
            document.querySelector('.main-page__content-questionsRange__language').classList.remove('d-flex');
            document.querySelector('.main-page__content-questionsRange__language').classList.add('d-none');
        }
    }

    document.querySelectorAll("input[name='language']").forEach((input) => {
        input.addEventListener('change', function(e){
            console.log(e.target.value);
            if(e.target.value == "english"){
                language='english';
            }else if(e.target.value == "hindi"){
                language='hindi';
            }else if(e.target.value == "kannada"){
                language='kannada';
            }
        });
    });
    document.querySelectorAll("input[name='questionsRange']").forEach((input) => {
        input.addEventListener('change', function(e){
            if(e.target.value == "allQuestion" || e.target.value == ""){
                defaultRange = true;
                limitedRange = false;
                quizRangeSelection.style.display = "none";
                document.querySelector('.inital-btn').style.height = '50%';
            }else if(e.target.value == "limitedQuestion"){
                defaultRange = false;
                limitedRange = true;
                quizRangeSelection.style.display = "block";
                document.querySelector('.inital-btn').style.height = '30%';
            }
        });
    });


    questionRangeInput.addEventListener("keyup",function(e){
        if(e.target.value.length > 0){
            questionRangeFromInput.setAttribute("disabled","disabled");
            questionRangeToInput.setAttribute("disabled","disabled");
        }else if(e.target.value.length == 0){
            questionRangeFromInput.removeAttribute("disabled");
            questionRangeToInput.removeAttribute("disabled");
        }
    })
    questionRangeFromInput.addEventListener("keyup",function(e){
        if(e.target.value.length > 0){
            questionRangeInput.setAttribute("disabled","disabled");
            questionRangeInput.value="";
        }else if(e.target.value.length == 0){
            questionRangeInput.removeAttribute("disabled");
        }
    })
    questionRangeToInput.addEventListener("keyup",function(e){
        if(e.target.value.length > 0){
            questionRangeInput.setAttribute("disabled","disabled");
            questionRangeInput.value="";
        }else if(e.target.value.length == 0){
            questionRangeInput.removeAttribute("disabled");
        }
    })


    function validateRangeToStart(){
        if(limitedRange){
            if(questionRangeInput.value !="" && questionRangeFromInput.value=="" && questionRangeToInput.value==""){
                endingIndex=parseInt(questionRangeInput.value);
                if((endingIndex > questions.length) || endingIndex<=0){
                    showErrorMsg();
                }else{
                    questions = questions.slice(startingIndex,endingIndex);
                    onStart();
                }
            }else if(questionRangeInput.value =="" && questionRangeFromInput.value!="" && questionRangeToInput.value!=""){
                startingIndex = parseInt(questionRangeFromInput.value);
                endingIndex = parseInt(questionRangeToInput.value);
                if((startingIndex < questions.length &&  endingIndex <= questions.length) && (startingIndex>0 && endingIndex>0) && (endingIndex > startingIndex)){
                    startingIndex== 1 ? startingIndex = 0:startingIndex = (startingIndex-1);
                    questions = questions.slice(startingIndex,endingIndex);
                    onStart();
                }else{
                    showErrorMsg();
                }
            }else if(questionRangeInput.value =="" && questionRangeFromInput.value!="" && questionRangeToInput.value==""){
                showErrorMsg();
            }else if(questionRangeInput.value =="" && questionRangeFromInput.value=="" && questionRangeToInput.value!=""){
                showErrorMsg();
            }else if(questionRangeInput.value =="" && questionRangeFromInput.value=="" && questionRangeToInput.value==""){
                showErrorMsg();
            }
        }else if(defaultRange){
            onStart();
        }
    }

    function showErrorMsg(){
        document.querySelector('.rangeInputs__errorMsg').style.display='flex';
        setTimeout(function(){
            document.querySelector('.rangeInputs__errorMsg').style.display='none';
        },1600)
    }

    function openInfoModal(){
        $('#questionInfoModal').modal('show');
    }

    function continueTest() {
        $('#continue-test').modal('show');
    }
    function submitTest() {
        answeredUnanswered();
        $('#submit-test').modal('show');
    }
    function forceSubmitTest() {
        $('#force-submit-test').modal('show');
    }

    function nextSection() {
        document.getElementById("sectionSelect").selectedIndex += 1;
        sectionChange();
    }
let latestTimePassed = 0;
function pauseAndSaveQuiz(){
    var remainingTime = countdown.pause();
    const timeRem = {
        remainingTime:remainingTime,
        questionNo:Number(que_count)+1
    }
    console.log('Paused timing data:', JSON.stringify(timeRem));
    console.log('timePassed',timePassed);
    latestTimePassed = timePassed
    onTimesUp()
}

function resumeTest(){
    timePassed = latestTimePassed;
    countdown.resume()
    startTimers()
}
</script>
</div>
<script src="file:///android_asset/quiz/assets/javascripts/quizTimer.js"></script>
<script src="file:///android_asset/quiz/assets/javascripts/lottie.min.js"></script>
<script src="file:///android_asset/quiz/assets/javascripts/userPlay.js"></script>
<script src="file:///android_asset/quiz/assets/javascripts/botPlay.js"></script>
<script src="file:///android_asset/quiz/assets/javascripts/resultSummary.js"></script>
<script src="file:///android_asset/quiz/assets/javascripts/analyticsResult.js"></script>
<script src="file:///android_asset/quiz/assets/javascripts/animation.js"></script>
<script src="file:///android_asset/quiz/assets/javascripts/quizResult.js"></script>
<script src="file:///android_asset/quiz/assets/javascripts/appCallback.js"></script>
<script src="file:///android_asset/quiz/assets/javascripts/prepjoyTimer.js"></script>


</body>
</html>