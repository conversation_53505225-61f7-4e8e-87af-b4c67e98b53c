var anim;
var animdata;
//Game Rsult Caluclate
var userWin='';
function gameResult(){
    if(source!='web') {
        stopAudios();
    }
    $('#winnerModal').modal('show');
     animdata ={
        container: document.getElementById("winner"),
        renderer: "svg",
        loop: 1,
        autoplay: true,
        path: ""
    };
    if(userScore>botScore){
        $('#user-status, .user-status').text('You won');
        userWin='win';
        if(source!='web'){
            userWinsImage();
            winAudios();
        }
        else {
            animdata.path = '/assets/prepjoy/win.json';
        }
    }
    else if(userScore==botScore){
        $('#user-status,.user-status').text('Oh! its  Tie').addClass('red');
        userWin='Draw';
        if(source!='web'){
           gameTieImage();
            tieAudios();
        }
        else {
            animdata.path = '/assets/prepjoy/tie.json';
        }
    }
    else{
        $('#user-status,.user-status').text('You Lose').addClass('red');
        userWin='Lose';
        if(source!='web'){
          gameLoseImage();
            loseAudios();
        }
        else {
            animdata.path = '/assets/prepjoy/lose.json';
        }
    }
    anim = bodymovin.loadAnimation(animdata);
    $("#winnerModal").on('shown.bs.modal', function(){
        $('.quizes,.quiz-profile').addClass('d-none').removeClass('d-flex');
//        setTimeout(submitAnswers,1000);
    });
    setTimeout(submitAnswers,2000);
}
//Current Badge
var userTotalPoints;
var maxValueSlider=0;
var nextChallenger;
var nextchallengerPlace;
function currentBadge(data) {
    var nextBadge = '';
    var balancePoints;
    var hunter = 0;
    var warrior = 100;
    var gladiator = 300;
    var knight = 800;
    var ninja = 2000;
    // var commander=3999;
    var samurai = 4000;
    var ultimate = 8000;
    var badgeDetails = data.prepJoyUserDetails;
    nextChallenger = data.challengerName;
    nextchallengerPlace = data.challengerPlace;
    badgeDetails = badgeDetails.split(",").reduce(function (obj, str, index) {
        var strParts = str.split(":");
        if (strParts[0] && strParts[1]) { //<-- Make sure the key & value are not undefined
            obj[strParts[0].replace(/\s+/g, '')] = strParts[1].trim(); //<-- Get rid of extra spaces at beginning of value strings
        }
        return obj;
    }, {});
    var newBadge = data.newBadge;
    var newMedal = data.newMedal;
    var totalPoints = badgeDetails.totalPoints;
    var currentBadge = badgeDetails.currentBadge;
    var totalMedals = badgeDetails.totalMedals;
        var newRanks='';
       if(badgeDetails.newRanks) {
           newRanks = badgeDetails.newRanks;
           showRank(newRanks);
       }
    userTotalPoints = Number(totalPoints);
    if (newBadge != null) {
        //$('#user-status').hide();
        $('.badge-screen').show();
        showBadge(newBadge);
    }
    if (newMedal != null) {
        $('#medalModal').modal('show');
        if (newMedal == 'gold') {
            $('#win-message').text('You’ve won 8 Consecutive times.');
            $('#medal-user').addClass('gold');
            $('#medal-name').text('Gold Medal');
        } else if (newMedal == 'silver') {
            $('#win-message').text('You’ve won 5 Consecutive times.');
            $('#medal-user').addClass('silver');
            $('#medal-name').text('Silver Medal');
        } else if (newMedal == 'bronze') {
            $('#win-message').text('You’ve won 3 Consecutive times.');
            $('#medal-user').addClass('bronze');
            $('#medal-name').text('Bronze Medal');
        }
        var anim;
        var animdata = {
            container: document.getElementById("medal-upload"),
            renderer: "svg",
            loop: 1,
            autoplay: true,
            path: ""
        };
        animdata.path = 'https://assets6.lottiefiles.com/packages/lf20_mntsz64q.json';
        anim = bodymovin.loadAnimation(animdata);
    }
    if (currentBadge == 'pHunter') {
        $('.currentBadgePoints').text(hunter + ' pts');
        $('.nextBadgePoints').text(warrior + ' pts');
        nextBadge = 'pWarrior';
        maxValueSlider = warrior;
        balancePoints = warrior - userTotalPoints;
    } else if (currentBadge == 'pWarrior') {
        $('.currentBadgePoints').text(warrior + ' pts');
        $('.nextBadgePoints').text(gladiator + ' pts');
        nextBadge = 'pGladiator';
        maxValueSlider = gladiator;
        balancePoints = gladiator - userTotalPoints;
    } else if (currentBadge == 'pGladiator') {
        $('.currentBadgePoints').text(gladiator + ' pts');
        $('.nextBadgePoints').text(knight + ' pts');
        nextBadge = 'pKnight';
        maxValueSlider = knight;
        balancePoints = knight - userTotalPoints;
    } else if (currentBadge == 'pKnight') {
        $('.currentBadgePoints').text(knight + ' pts');
        $('.nextBadgePoints').text(ninja + ' pts');
        nextBadge = 'pNinja';
        maxValueSlider = ninja;
        balancePoints = ninja - userTotalPoints;
    } else if (currentBadge == 'pNinja') {
        $('.currentBadgePoints').text(ninja + ' pts');
        $('.nextBadgePoints').text(samurai + ' pts');
        nextBadge = 'pSamurai';
        maxValueSlider = samurai;
        balancePoints = samurai - userTotalPoints;
    } else if (currentBadge == 'pSamurai') {
        $('.currentBadgePoints').text(samurai + ' pts');
        $('.nextBadgePoints').text(ultimate + ' pts');
        nextBadge = 'pUltimate';
        maxValueSlider = ultimate;
        balancePoints = ultimate - userTotalPoints;
    } else if (currentBadge == 'pUltimate') {
        $('.currentBadgePoints').text(ultimate + '+ pts');
        $('.row.badges').removeClass('justify-content-between').addClass('justify-content-center');
        $('.row.badges > div:last-child').hide();
        maxValueSlider = ultimate;
        balancePoints = 0;
    }
    $('.currentBadge').text(currentBadge);
    $('.nextBadge').text(nextBadge);
    scoreProgress();
    if (currentBadge != 'pUltimate') {
        $('.balance-points').html("<p>You need " + balancePoints + ' pts' + " more to become " + nextBadge + "</p>");
    } else {
        $('.balance-points').html("<p>You are an PRO now.</p>");
    }
    showAllAnswers();
}
///Progress bar of user badge and score on result page
function scoreProgress() {
    var handle = $( ".custom-handle" );
    $( ".slider" ).slider({
        orientation: "horizontal",
        range: "min",
        max: maxValueSlider,
        value: userTotalPoints,
        disabled:true,
        create: function() {
            var value=$( this ).slider( "value" );
            handle.html("<p>You're here</p>"+"<p>"+value+" pts</p>");
        },
        slide: function( event, ui ) {
            handle.html("<p>You're here</p>"+"<p>"+ui.value+" pts</p>");
        }
    });
    $('.slider').removeClass('ui-state-disabled');
}
//If user Earns Badge come here
function showBadge(newBadge){
    if(newBadge=='pHunter'){
        $('.badge').addClass('hunter');
        $('#badgetype').text('pHunter');
    }
    else if(newBadge=='pWarrior'){
        $('.badge').addClass('warrior');
        $('#badgetype').text('pWarrior');
    }
    else if(newBadge=='pGladiator'){
        $('.badge').addClass('commander');
        $('#badgetype').text('pGladiator');
    }
    else if(newBadge=='pKnight'){
        $('.badge').addClass('knight');
        $('#badgetype').text('pKnight');
    }
    else if(newBadge=='pNinja'){
        $('.badge').addClass('ninja');
        $('#badgetype').text('pNinja');
    }
    else if(newBadge=='pSamurai'){
        $('.badge').addClass('samurai');
        $('#badgetype').text('pSamurai');
    }
    else if(newBadge=='pUltimate'){
        $('.badge').addClass('ultimate');
        $('#badgetype').text('pUltimate');
    }
    var anim;
    var animdata ={
        container: document.getElementById("lottie"),
        renderer: "svg",
        loop: 1,
        autoplay: true,
        path: "https://assets10.lottiefiles.com/packages/lf20_i6sqnxav.json"
    };
    anim = bodymovin.loadAnimation(animdata);
    anim.addEventListener('complete',stopAnim);
    function stopAnim(){
        $('#lottie').hide();
    }
}
//if user earns rank
function showRank(rank){
    $('#rankModal').modal('show');
    if((rank=='1') || (rank==1)) {
        $('#rank-message').text('You have got ' + rank + 'st' + ' rank');
    }
    else if(rank=='2' || rank==2){
        $('#rank-message').text('You have got ' + rank + 'nd' + ' rank');
    }
    else if(rank=='3' || rank==3){
        $('#rank-message').text('You have got ' + rank + 'rd' + ' rank');
    }
    else{
        $('#rank-message').text('You have got ' + rank + 'th' + ' rank');
    }
    var anim;
    var animdata ={
        container: document.getElementById("rank-cup"),
        renderer: "svg",
        loop: 1,
        autoplay: true,
        path: "https://assets3.lottiefiles.com/packages/lf20_r7bfvyke.json"
    };
    anim = bodymovin.loadAnimation(animdata);
}