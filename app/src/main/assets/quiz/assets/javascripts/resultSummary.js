var mergedUserAnswers;
var language="";
var quizMode="";
var userScore=0;
var source='android';
var serviceBaseUrl = 'https://www.wonderslate.com/';

function openSummary(qaObject,questions){
    console.log(qaObject,questions);
    quizMode=qaObject.quizType;
    language=qaObject.language;
    serviceBaseUrl = qaObject.serviceURL;
    var list = questions.map(t1 => ({...t1, ...qaObject.userAnswers.find(t2 => t2.id == t1.id)}))
    mergedUserAnswers = list;
     if((quizMode =='practice')||(quizMode =='testSeries') || (quizMode =='test')){
            document.getElementById("whTheme").setAttribute("href","file:///android_asset/quiz/assets/stylesheets/prepJoy/whTheme.css")
        }
    if(quizMode=='testSeries' || quizMode=='test'){

        var markedListElem = document.getElementById('markedListElem');

        if(markedListElem !=null){
            document.getElementById('markedListElem').style.display="block";
        }
    }
    showAllAnswers(mergedUserAnswers);
}


function showAllAnswers(mergedUserAnswers){
    var htmlStr='';
    var correctArr=[];
    var inCorrectArr=[];
    var skipArr=[];
    $('.nav-tabs a[href="#all"]').tab('show');
//    htmlStr +="<div class='container'>";
        for(var i=0;i<mergedUserAnswers.length;i++) {
            var ques = mergedUserAnswers[i].ps;
            ques=replaceSymbols(ques);
            ques=checkLanguageAndImage(ques);
            ques=replaceImageUrlForApps(ques);

            var option1 = mergedUserAnswers[i].op1;
            option1=replaceSymbols(option1);
            option1=checkLanguageAndImage(option1);
            option1=replaceImageUrlForApps(option1);

            var option2 = mergedUserAnswers[i].op2;
            option2=replaceSymbols(option2);
            option2=checkLanguageAndImage(option2);
            option2=replaceImageUrlForApps(option2);

            var option3 = mergedUserAnswers[i].op3;
            option3=replaceSymbols(option3);
            option3=checkLanguageAndImage(option3);
            option3=replaceImageUrlForApps(option3);

            var option4 = mergedUserAnswers[i].op4;
            option4=replaceSymbols(option4);
            option4=checkLanguageAndImage(option4);
            option4=replaceImageUrlForApps(option4);


            if(mergedUserAnswers[i].op5){
              var option5=mergedUserAnswers[i].op5;
              option5=replaceSymbols(option5);
              option5=checkLanguageAndImage(option5);
              option5=replaceImageUrlForApps(option5);
            }else{
              var option5='';
            }

           var explanation='';

           if(mergedUserAnswers[i].answerDescription != ''){
             explanation=replaceSymbols(mergedUserAnswers[i].answerDescription);
             explanation=checkLanguageAndImage(explanation);
             explanation=replaceImageUrlForApps(explanation);
           }

    htmlStr += "<div class='mcq-answers'>" +
                    "<a href='javascript:showReportModal("+(mergedUserAnswers[i].id)+")'class='text-white reporticon d-flex justify-content-end'><i class='material-icons'>error</i></a>"+
                    "<div id='que-no'>Q" + (i + 1) + ".</div>" +
                    "<div class='question-wrapper'>" ;
                        if(mergedUserAnswers[i].directions !=null) {
                            htmlStr +="<div class='directions'>" +
                                     "   <p id='direction' class='more'>" + mergedUserAnswers[i].directions + "</p>" +
                                     "</div>";
                        }
            htmlStr +="<p class='que_text'>" + ques + "</p>" +
                   "</div>" +
                   "<div class='que-options-wrapper mt-4'>" +
                        "<div class='que-options' id='que-" + i + "'>" +
                            "<div id=ans-" + i + "' class='option-qa'>";
                            if (mergedUserAnswers[i].ans1 == 'Yes') {
                                htmlStr += '<div class="option correct">';
                            } else if ((mergedUserAnswers[i].ans1 != 'Yes') && (mergedUserAnswers[i].userOption == '1')) {
                                htmlStr += '<div class="option incorrect">';
                            } else {
                                htmlStr += '<div class="option">';
                            }
                    htmlStr += '<span>' + option1 + '</span>' +
                            '</div>' +
                        "</div>" +
                    "<div id=ans-" + i + "' class='option-qa'>";
                        if (mergedUserAnswers[i].ans2 == 'Yes') {
                            htmlStr += '<div class="option correct">';
                        } else if ((mergedUserAnswers[i].ans2 != 'Yes') && (mergedUserAnswers[i].userOption == '2')) {
                            htmlStr += '<div class="option incorrect">';
                        } else {
                            htmlStr += '<div class="option">';
                    }
                    htmlStr += '<span>' + option2 + '</span></div>' +
                        "</div>" +
                    "<div id=ans-" + i + "' class='option-qa'>";

                        if (mergedUserAnswers[i].ans3 == 'Yes') {
                            htmlStr += '<div class="option correct">';
                        } else if ((mergedUserAnswers[i].ans3 != 'Yes') && (mergedUserAnswers[i].userOption == '3')) {
                            htmlStr += '<div class="option incorrect">';
                        } else {
                            htmlStr += '<div class="option">';
                        }
                    htmlStr += '<span>' + option3 + '</span>' +
                        '</div>' +
                    "</div>";

        htmlStr += "<div id=ans-" + i + "' class='option-qa'>";
            if (mergedUserAnswers[i].ans4 == 'Yes') {
                htmlStr += '<div class="option correct">';
            } else if ((mergedUserAnswers[i].ans4 != 'Yes') && (mergedUserAnswers[i].userOption == '4')) {
                htmlStr += '<div class="option incorrect">';
            } else {
                htmlStr += '<div class="option">';
            }

            htmlStr += '<span>' + option4 + '</span>'+
            '</div>' +
            "</div>" ;

            if(mergedUserAnswers[i].ans5 !=null && mergedUserAnswers[i].ans5 !=''){
              "<div id=ans-" + i + "' class='option-qa'>";
                    if (mergedUserAnswers[i].ans5 == 'Yes') {
                        htmlStr += '<div class="option correct">';
                    } else if ((mergedUserAnswers[i].ans5 != 'Yes') && (mergedUserAnswers[i].userOption == '5')) {
                        htmlStr += '<div class="option incorrect">';
                    } else {
                        htmlStr += '<div class="option">';
                    }
                    htmlStr += '<span>' + option5 + '</span>'+
                    '</div>' +
                    "</div>" ;
                    }
            if((explanation !=null)&&(explanation !='')){
                htmlStr +=  '<div class="mt-4 explanation-wrappers"><h3 class="head-text">Explanation:</h3><p class="explanation-text">' + explanation + '</p></div>';
            }
           htmlStr +=  "</div>" +
                    "</div>" +
                    "</div>" +
                "</div>";

        if (quizMode != '') {
            if (mergedUserAnswers[i].userOption == mergedUserAnswers[i].correctOption) {
                correctArr.push(mergedUserAnswers[i].userOption);
            }
            if (mergedUserAnswers[i].userOption == '-1') {
                skipArr.push(mergedUserAnswers[i].userOption);
            }
            if (mergedUserAnswers[i].userOption != '-1' && mergedUserAnswers[i].userOption != mergedUserAnswers[i].correctOption) {
                inCorrectArr.push(mergedUserAnswers[i].userOption);
            }
        }
    }
    htmlStr += "</div>";
        $('#right').text(correctArr.length);
        $('#wrong').text(inCorrectArr.length);
        $('#skipped').text(skipArr.length);
        $('.updateScore').text(correctArr.length);
        $('#noofque').text(mergedUserAnswers.length);
         document.getElementById('all').innerHTML = htmlStr;
        addMathjax();
}

    //mathjax script adding
function addMathjax(){
    MathJax = { mml: { forceReparse: true } }
    // $('head').append('<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML" >');
    $('head').append('<script src="https://polyfill.io/v3/polyfill.min.js?features=es6">')
    $('head').append('<script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js">')
}
$("#show-answer").click(function() {
    $('.answer-wrapper').show();
    addMathjax()
});


function correctAnswers(){
    displayTabAnswers('correct');
}
function incorrectAnswers(){
    displayTabAnswers('incorrect');
}
function skippedAnswers(){
    displayTabAnswers('skipped');
}
function markedForReview(){
    displayTabAnswers('markedForReview');
}

function displayTabAnswers(tabType){
var analytics = false;
    var htmlStr='';
    htmlStr +="<div class='container'>";

    if(analytics){
        for(var i=0;i<questions.length;i++) {
                var ques = questions[i].ps;
                 var directions=questions[i].directions;
                            ques=replaceSymbols(ques);
                            ques=checkLanguageAndImage(ques);
                     ques=replaceImageUrlForApps(ques);
                     var option1 = questions[i].op1;
                             option1=replaceSymbols(option1);
                             option1=checkLanguageAndImage(option1);
                      option1=replaceImageUrlForApps(option1);
                           var option2 = questions[i].op2;
                                   option2=replaceSymbols(option2);
                                   option2=checkLanguageAndImage(option2);
                            option2=replaceImageUrlForApps(option2);
                              var option3 = questions[i].op3;
                                           option3=replaceSymbols(option3);
                                           option3=checkLanguageAndImage(option3);
                                    option3=replaceImageUrlForApps(option3);
                            var option4 = questions[i].op4;
                                                   option4=replaceSymbols(option4);
                                                   option4=checkLanguageAndImage(option4);
                                            option4=replaceImageUrlForApps(option4);
                            if(questions[i].op5){
                                      var option5=questions[i].op5;
                                         option5=replaceSymbols(option5);
                                          option5=checkLanguageAndImage(option5);
                                          option5=replaceImageUrlForApps(option5);
                                      }
                                      else{
                                      var option5='';
                                      }
                                      var explanation='';
                            if(questions[i].answerDescription != ''){
                                     explanation=replaceSymbols(questions[i].answerDescription);
                                    explanation=checkLanguageAndImage(explanation);
                                     explanation=replaceImageUrlForApps(explanation);
                                    }
                                    if(tabType=='incorrect'){
            if((questions[i].userOption !=-'1') && (questions[i].correctOption!=questions[i].userOption)){
            htmlStr+=answerTemplateAnalytics(questions[i],ques,i,option1,option2,option3,option4,option5,directions,explanation);
            }
            document.getElementById('incorrectAnswers').innerHTML = htmlStr;
            if($('#incorrectAnswers .mcq-answers').length ==0){
            document.getElementById('incorrectAnswers').innerHTML = '<h4 class="validate-answers">No InCorrect Answers</h4>';
                }
        }
         else if(tabType=='correct'){
        if(questions[i].correctOption==questions[i].userOption) {
            htmlStr += answerTemplateAnalytics(questions[i], ques, i, option1, option2, option3, option4,option5,directions,explanation);
        }
            document.getElementById('correctAnswers').innerHTML = htmlStr;
            if($('#correctAnswers .mcq-answers').length ==0){
            document.getElementById('correctAnswers').innerHTML = '<h4 class="validate-answers">No Correct Answers</h4>';
        }
        }
        else if(tabType=='skipped'){
            if(questions[i].userOption=='-1'){
            htmlStr+=answerTemplateAnalytics(questions[i],ques,i,option1,option2,option3,option4,option5,directions,explanation);
        }
            document.getElementById('skippedAnswers').innerHTML = htmlStr;
            if($('#skippedAnswers .mcq-answers').length ==0){
            document.getElementById('skippedAnswers').innerHTML = '<h4 class="validate-answers">No Skipped Answers</h4>';
        }
        }
        }
    }else{
       for(var i=0;i<mergedUserAnswers.length;i++) {
            var ques = mergedUserAnswers[i].ps;
            var directions=mergedUserAnswers[i].directions;
            ques=replaceSymbols(ques);
            ques=checkLanguageAndImage(ques);
            ques=replaceImageUrlForApps(ques);

            var option1 = mergedUserAnswers[i].op1;
            option1=replaceSymbols(option1);
            option1=checkLanguageAndImage(option1);
            option1=replaceImageUrlForApps(option1);

            var option2 = mergedUserAnswers[i].op2;
            option2=replaceSymbols(option2);
            option2=checkLanguageAndImage(option2);
            option2=replaceImageUrlForApps(option2);

            var option3 = mergedUserAnswers[i].op3;
            option3=replaceSymbols(option3);
            option3=checkLanguageAndImage(option3);
            option3=replaceImageUrlForApps(option3);

            var option4 = mergedUserAnswers[i].op4;
            option4=replaceSymbols(option4);
            option4=checkLanguageAndImage(option4);
            option4=replaceImageUrlForApps(option4);

            if(mergedUserAnswers[i].op5){
              var option5=mergedUserAnswers[i].op5;
              option5=replaceSymbols(option5);
              option5=checkLanguageAndImage(option5);
              option5=replaceImageUrlForApps(option5);
            }else{
              var option5='';
            }

            var explanation='';
            if(mergedUserAnswers[i].answerDescription != ''){
                explanation=replaceSymbols(mergedUserAnswers[i].answerDescription);
                explanation=checkLanguageAndImage(explanation);
                explanation=replaceImageUrlForApps(explanation);
            }

            if(tabType=='incorrect'){
                if((mergedUserAnswers[i].userOption !=-'1') && (mergedUserAnswers[i].correctOption!=mergedUserAnswers[i].userOption)){
                    htmlStr+=answerTemplate(mergedUserAnswers[i],ques,i,option1,option2,option3,option4,option5,mergedUserAnswers[i],directions,explanation);
                }
                document.getElementById('incorrectAnswers').innerHTML = htmlStr;
                if($('#incorrectAnswers .mcq-answers').length ==0){
                    document.getElementById('incorrectAnswers').innerHTML = '<h4 class="validate-answers">No InCorrect Answers</h4>';
                }
            }else if(tabType=='correct'){
                if(mergedUserAnswers[i].correctOption==mergedUserAnswers[i].userOption) {
                    htmlStr += answerTemplate(mergedUserAnswers[i], ques, i, option1, option2, option3, option4,option5, mergedUserAnswers[i],directions,explanation);
                }
                document.getElementById('correctAnswers').innerHTML = htmlStr;
                if($('#correctAnswers .mcq-answers').length ==0){
                    document.getElementById('correctAnswers').innerHTML = '<h4 class="validate-answers">No Correct Answers</h4>';
                }
            }else if(tabType=='skipped'){
                if(mergedUserAnswers[i].userOption=='-1'){
                    htmlStr+=answerTemplate(mergedUserAnswers[i],ques,i,option1,option2,option3,option4,option5,mergedUserAnswers[i],directions,explanation);
                }
                document.getElementById('skippedAnswers').innerHTML = htmlStr;
                if($('#skippedAnswers .mcq-answers').length ==0){
                    document.getElementById('skippedAnswers').innerHTML = '<h4 class="validate-answers">No Skipped Answers</h4>';
                }
            }else if(tabType=='markedForReview'){
                if(mergedUserAnswers[i].reviewedQ=="true"){
                    htmlStr+=answerTemplate(mergedUserAnswers[i],ques,i,option1,option2,option3,option4,option5,mergedUserAnswers[i],directions,explanation);
                }
                document.getElementById('markedForReview').innerHTML = htmlStr;
                if($('#markedForReview .mcq-answers').length ==0){
                    document.getElementById('markedForReview').innerHTML = '<h4 class="validate-answers">No question has been marked for review</h4>';
                }
            }
        }
    }
  addMathjax();
}

function answerTemplate(questions,ques,index,option1,option2,option3,option4,option5,qaAnswers,directions,explanation){
    var htmlStr='';
    htmlStr += "<div class='mcq-answers'>" +
                    "<div id='que-no'>Q" + (index + 1) + ".</div>" +
                        "<div class='question-wrapper'>" ;
                        if(directions !=null) {
                            htmlStr +="<div class=\"directions\">\n" +
                                "        <p id=\"direction\" class='more'>" + directions + "</p>\n" +
                                "       </div>";
                        }
                            htmlStr += "<p class='que_text'>" + ques + "</p>" +
                        "</div>" +

                        "<div class='que-options-wrapper mt-4'>" +
                            "<div class='que-options' id='que-" + index + "'>" +
                                "<div id=ans-" + index + "' class='option-qa'>";
                                    if (questions.ans1 == 'Yes') {
                                        htmlStr += '<div class="option correct">';
                                    }else if ((questions.ans1 != 'Yes') && (qaAnswers.userOption == '1')) {
                                        htmlStr += '<div class="option incorrect">';
                                    }else {
                                        htmlStr += '<div class="option">';
                                    }
                                    htmlStr += '<span>' + option1 + '</span>' +
                                '</div>' +
                            "</div>" +
                        "<div id=ans-" + index + "' class='option-qa'>";
                            if (questions.ans2 == 'Yes') {
                                htmlStr += '<div class="option correct">';
                            }else if ((questions.ans2 != 'Yes') && (qaAnswers.userOption == '2')) {
                                htmlStr += '<div class="option incorrect">';
                            } else {
                                htmlStr += '<div class="option">';
                            }
                            htmlStr += '<span>' + option2 + '</span></div>' +
                        "</div>" +

                        "<div id=ans-" + index + "' class='option-qa'>";
                            if (questions.ans3 == 'Yes') {
                                htmlStr += '<div class="option correct">';
                            }else if ((questions.ans3 != 'Yes') && (qaAnswers.userOption == '3')) {
                                htmlStr += '<div class="option incorrect">';
                            }else {
                                htmlStr += '<div class="option">';
                            }
                            htmlStr += '<span>' + option3 + '</span>' +
                        '</div>' +
                     "</div>";
                    htmlStr += "<div id=ans-" + index + "' class='option-qa'>";
                        if (questions.ans4 == 'Yes') {
                            htmlStr += '<div class="option correct">';
                        }else if ((questions.ans4 != 'Yes') && (qaAnswers.userOption == '4')) {
                            htmlStr += '<div class="option incorrect">';
                        }else {
                            htmlStr += '<div class="option">';
                        }
                        htmlStr += '<span>' + option4 + '</span></div>' +
                    "</div>" ;
                    if((option5 !=null)&&(option5 !='')){
                        htmlStr += "<div id=ans-" + index + "' class='option-qa'>";
                            if (questions.ans5 == 'Yes') {
                                htmlStr += '<div class="option correct">';
                            }else if ((questions.ans5 != 'Yes') && (qaAnswers.userOption == '5')) {
                                htmlStr += '<div class="option incorrect">';
                            }else {
                                htmlStr += '<div class="option">';
                            }
                            htmlStr += '<span>' + option5 + '</span></div>' +
                        "</div>" ;
                    }
                     if((explanation !=null)&&(explanation !='')){
                         htmlStr +=  '<div class="mt-4 explanation-wrappers"><h3 class="head-text">Explanation:</h3><p class="explanation-text">' + explanation + '</p></div>';
                     }
                htmlStr +="</div>" +
                "</div>" +
            "</div>";
    return htmlStr;
}

function showReportModal(questionNo){
    questionId = questionNo;
    $('#prep-report-question').modal('show');
}
function reportSubmit(){

    var issuesSelected="";
    if(document.getElementById("spellingMistake").checked){
        issuesSelected="Spelling mistake";
    }
    if(document.getElementById("directionNotGiven").checked){
        issuesSelected+=(issuesSelected==""?"":",")+"Directions not given";
    }
    if(document.getElementById("imageNotVisible").checked){
        issuesSelected+=(issuesSelected==""?"":",")+"Image not visible";
    }
    if(document.getElementById("incompleQuestion").checked){
        issuesSelected+=(issuesSelected==""?"":",")+"Incomplete questions";
    }
    if(document.getElementById("otherIssues").checked){
        issuesSelected+=(issuesSelected==""?"":",")+"Other issues";
    }

    var moreInformation = document.getElementById("moreInformation").value;

    questionReportObj={
        selectedIssue:issuesSelected,
        questionIssueId:questionId,
        issuetext:moreInformation
    }
    questionReportObj=JSON.stringify(questionReportObj);
     reportQuiz(questionReportObj);
        $('#prep-report-question').modal('hide');

}
function reportQuiz(reports){
    $('#prep-report-question').modal('hide');
    if(source=='android'){
        JSInterface.reportQuizIssue(reports);
    }
    else if(source=='ios'){
        webkit.messageHandlers.reportQuizIssue.postMessage(reports);
    }
}
 //callback for reporting quiz questions
function clearModal(){
    $('#moreInformation').val('');
    $("#spellingMistake,#directionNotGiven,#imageNotVisible, #incompleQuestion,#otherIssues").prop('checked', false);

}

function summaryBack(){
    if(source=='android'){
        JSInterface.summaryBack();
    }
    else if(source=='ios'){
        webkit.messageHandlers.summaryBack.postMessage();
    }
}


function showDescription(){
    $('.explanation-wrapper').show().addClass('animate__animated animate__bounceInUp');
    addMathjax();
}