function Countdown(elem, seconds) {
    timeObj = {};
    timeObj.elem = elem;
    timeObj.totalTime = seconds * 100; // Convert seconds to hundredths of a second
    timeObj.remainingTime = timeObj.totalTime; // Initialize remaining time
    timeObj.timer = null;
    timeObj.paused = false;

    timeObj.count = function() {
        var now = +new Date();
        var elapsed = Math.floor((now - timeObj.startTime) / 10); // Calculate elapsed time in hundredths of a second
        var timeLeft = timeObj.remainingTime - elapsed; // Calculate remaining time

        if (timeLeft <= 0) {
            timeObj.elem.innerHTML = '00:00';
            showCorrectAnswer();
            setTimeout(function() {
                nextQue();
            }, 1000);
            clearInterval(timeObj.timer);
        } else {
            var mi = Math.floor(timeLeft / (60 * 100));
            var ss = Math.floor((timeLeft - mi * 60 * 100) / 100);
            var ms = timeLeft - Math.floor(timeLeft / 100) * 100;
            newTime = timeObj.fillZero(ss) + "." + timeObj.fillZero(ms);
            newTime = Number(newTime);
            globalTimer = newTime;
            botPlay(globalTimer, chatBotAnswerTime); // Goes for bot playing
            if (timeObj.totalTime > 6000) {
                timeObj.elem.innerHTML = timeObj.fillZero(mi) + ":" + timeObj.fillZero(ss);
            } else {
                timeObj.elem.innerHTML = timeObj.fillZero(ss);
            }
        }
    };

    timeObj.start = function() {
        if (!timeObj.timer && !timeObj.paused) {
            timeObj.startTime = +new Date();
            timeObj.timer = setInterval(timeObj.count, 10); // Update interval to 10ms for better accuracy
        } else if (!timeObj.timer && timeObj.paused) {
            timeObj.startTime = +new Date();
            timeObj.timer = setInterval(timeObj.count, 10); // Update interval to 10ms for better accuracy
        }
    };

    timeObj.pause = function() {
        if (timeObj.timer) {
            clearInterval(timeObj.timer);
            var now = +new Date();
            var elapsed = Math.floor((now - timeObj.startTime) / 10); // Calculate elapsed time in hundredths of a second
            timeObj.remainingTime -= elapsed; // Update remaining time
            timeObj.paused = true;
            timeObj.timer = null;
        }
        return timeObj.remainingTime / 100; // Return remaining time in seconds
    };

    timeObj.resume = function() {
        if (!timeObj.timer) {
            timeObj.start();
        }
    };

    timeObj.stop = function() {
        if (timeObj.timer) {
            clearInterval(timeObj.timer);
            timeObj.timer = null;
        }
    };

    timeObj.fillZero = function(num) {
        return num < 10 ? '0' + num : num;
    };

    return timeObj;
}

var startTime, interval;
function stop(){
    clearInterval(interval);
}
function updateDisplay(currentTime){
   document.getElementById('test_timer').innerHTML=(currentTime / 1000).toFixed(2);
}
var testInterval;
function clockStart() {
        startTime = Date.now();
    testInterval = setInterval(function(){
            updateDisplay(Date.now() - startTime);
        },100);
    }
    function clockReset() {
        clearInterval(testInterval);
    }
function clockPause() {
        clearInterval(testInterval);
    }
   function clockResume() {
       clockStart();
    }
function clockRestart() {
    clockReset();
    clockStart();
 }