var image_medal='';
var image_won='';
var image_lose='';
var image_tie='';
var image_badge='';
var setSound;
var resqaObj;
function playAgain(){
    if(source=='android'){
        JSInterface.restartGame(nextChallenger,nextchallengerPlace);
    }
    else if(source=='ios'){
        webkit.messageHandlers.restartGame.postMessage('');
    }
}
function backToHome(){
    if(source=='android'){
        JSInterface.backToHome();
    }
    else if(source=='ios'){
        webkit.messageHandlers.backToHome.postMessage('');
    }
}
function audioChange(){
if(quizMode==''){
    $('.audioChange i').text(($('.audioChange i').text() == 'volume_up') ? 'volume_off' : 'volume_up');
    if(source=='android'){
        JSInterface.audioChange();
    }
    else if(source=='ios'){
        webkit.messageHandlers.audioChange.postMessage('');
    }
    }
}
function audioChange() {
if(quizMode==''){
    $('.audioChange i').text(($('.audioChange i').text() == 'volume_up') ? 'volume_off' : 'volume_up');
    if ($('.audioChange i').text() == 'volume_up') {
        setSound = true;
    } else {
        setSound = false;
    }
    if(source=='android'){
        JSInterface.audioChange(setSound);
    }
    else if(source=='ios'){
        webkit.messageHandlers.audioChange.postMessage(setSound);
    }
    }
}
        function userShowAudio(){
        if(quizMode==''){
    if(source=='android'){
        JSInterface.userAudio();
    }
    else if(source=='ios'){
        webkit.messageHandlers.userAudio.postMessage('');
    }
    }
}
function slidingAudio() {
if(quizMode==''){
    if (source == 'android') {
        JSInterface.slideBotAudio();
    } else if (source == 'ios') {
        webkit.messageHandlers.slideBotAudio.postMessage('');
    }
    }
}
function battleAudios(){
if(quizMode==''){
    if(source=='android'){
        JSInterface.battleAudio();
    }
    else if(source=='ios'){
        webkit.messageHandlers.battleAudio.postMessage('');
    }
    }
}
function queAudio() {
if(quizMode==''){
    if (source == 'android') {
        JSInterface.questionAudio();
    } else if (source == 'ios') {
        webkit.messageHandlers.questionAudio.postMessage('');
    }
    }
}
function botCorrectAudio() {
if(quizMode==''){
    if (source == 'android') {
        JSInterface.botCorrect();
    } else if (source == 'ios') {
        webkit.messageHandlers.botCorrect.postMessage('');
    }
    }
}
function botIncorrectAudio(){
if(quizMode==''){
    if(source=='android'){
        JSInterface.botIncorrect();
    }
    else if(source=='ios'){
        webkit.messageHandlers.botIncorrect.postMessage('');
    }
    }
}
function timeAudio(){
if(quizMode==''){
    if(source=='android'){
        JSInterface.timerAudio();
    }
    else if(source=='ios'){
        webkit.messageHandlers.timerAudio.postMessage('');
    }
    }
}
function answerCorrectAudio(){
if(quizMode==''){
    if(source=='android'){
        JSInterface.answerCorrect();
    }
    else if(source=='ios'){
        webkit.messageHandlers.answerCorrect.postMessage('');
    }
    }
}
function answerInCorrectAudio(){
if(quizMode==''){
    if(source=='android'){
        JSInterface.answerIncorrect();
    }
    else if(source=='ios'){
        webkit.messageHandlers.answerIncorrect.postMessage('');
    }
    }
}
function submitCallback(qaObj){
    if(source=='android') {
        JSInterface.checkConnection();
    }
    else {
        webkit.messageHandlers.submitQaData.postMessage(qaObj);
    }
}
function submitFromAndroid(){
console.log("bot total time :"+qaObj,JSON.stringify(qaObj))
    var saveData = $.ajax({
        type: 'POST',
        crossdomain: true,
        url:submitUrl,
        contentType: "application/json",
        dataType: "json",
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Content-Type':'application/json',
            'Access-Control-Allow-Methods': 'GET, DELETE, HEAD, OPTIONS',
            'X-Auth-Token': tokenId,
            'Content-Encoding': 'gzip'
        },
        data: qaObj,
        success:function (response){
            if (response.status == 'success') {
            storeRecId(response,qaObj);
            $('.loader-submit').hide();
            if(bookgpt===true){
               $("#prevMatch").addClass("d-none");
               $("#nextMatch").addClass("d-none");
            }

                if(quizMode =='') {
                    $('#winnerModal').modal('hide');
                   $('.quizes,.quiz-profile').removeClass('d-flex').addClass('d-none');
                   $('.practice-result').removeClass('d-none').addClass('d-flex');
                   $('.nav-tabs a[href="#all"]').tab('show');

                    console.log(qaObj)
                    computeDistributedScore(qaObj);
                   currentBadge(response);

                }
                else if(quizMode =='practice' || quizMode =='test' || quizMode =='testSeries'){
                    $('.quizes,.quiz-profile').removeClass('d-flex').addClass('d-none');
                    $('.practice-result').removeClass('d-none').addClass('d-flex');
                    $('.nav-tabs a[href="#all"]').tab('show');
                    $('#user-status').hide();
                    if(source=='android') {
                       JSInterface.resizeWebview(true);
                    }else {
                       webkit.messageHandlers.resizeWebview.postMessage(true);
                    }
                    resqaObj = qaObj;
                    sectionListConstruct()
                    computeDistributedScore(qaObj);
                    currentBadge(response);

                }
                if(quizMode =='test'){
                    $('#practice-summary').text('Test Summary');
                }
            }
        }
    });
}

var testSubmitted=false;
var secWiseCorrect;
var secWiseInCorrect;
var secWiseSkipped;
var secWiseAttempted;
var totalSectionQ=[];
var secWiseTotal;
var resultSectionSelectValue;
var mergedUserAnswers;
var secpresent=false;
var sectionList;
var mergedList;
var resCrtArr=[];
var resIncArr=[];
var resSkipArr=[];
var score=0;
var showScore = false;
var secScore = 0;
var htmlStr='';
var correctArr=[];
var inCorrectArr=[];
var skipArr=[];
var secCrtAns=[];
var secInAns=[];
var secSpAns=[];
var attemptedQs=[];
var ttCount=[];
var crtGA=[];
var incGA=[];
var skpGA=[];
function  computeDistributedScore(qaObj){
    var sectionResultQuestions=[]
    var allQuestions = questions;
    var sectionCorrectMarks=-1;
    var sectionIncorrectMarks=0;

    var answersList = JSON.parse(qaObj).userAnswers;
    var correctCount = 0;
    var inCorrectCount = 0;
    var skippedCount = 0;
    mergedList = allQuestions.map(t1 => ({...t1, ...answersList.find(t2 => t2.id == t1.id)}))
    allQuestions = mergedList
    answersList.map(item=>{
        if(item.correctOption == item.userOption){
            correctCount++;
        }
        if(item.correctOption != item.userOption && item.userOption != -1){
            inCorrectCount++;
        }
        if(item.userOption == -1){
            skippedCount++;
        }
    })
    secCrtAns=[]
        secInAns=[]
        secSpAns=[]
        attemptedQs=[]
        ttCount=[]
        crtGA=[]
        incGA=[]
        skpGA=[]
        secScore=0
        score=0
    if (storingInitialData.examDtl ==null){
        //do nothing

    }else{
         if (storingInitialData.examDtl != "" && storingInitialData.examDtl.length > 0 && storingInitialData.examDtl != "" && storingInitialData.examDtl !=null ) {

             secpresent=true;

             resultSectionSelectValue   = document.getElementById("sectionSelectDropDown").value;
             console.log(resultSectionSelectValue)
             for (var m=0;m<allQuestions.length;m++){
                 if (allQuestions[m].subject == resultSectionSelectValue){
                     sectionResultQuestions.push(allQuestions[m])
                 }

             }
             var examDtl = storingInitialData.examDtl;
             for(var s=0;s<examDtl.length;s++){
                 //add correct and incorrect marks to each question

                 for (var m=0;m<allQuestions.length;m++){
                     if (allQuestions[m].subject ==examDtl[s].subject){
                         if (typeof examDtl[s].rightAnswerMarks !== 'undefined'&&examDtl[s].rightAnswerMarks!=null&&examDtl[s].rightAnswerMarks!="null") {
                             allQuestions[m].marks = Number(examDtl[s].rightAnswerMarks);
                             if (typeof examDtl[s].wrongAnswerMarks !== 'undefined'&&examDtl[s].wrongAnswerMarks!=null&&examDtl[s].wrongAnswerMarks!="null") {
                                 allQuestions[m].negativeMarks = Number(examDtl[s].wrongAnswerMarks);
                             }
                         }
                     }
                 }
                 if(examDtl[s].subject==resultSectionSelectValue){
                     if (typeof examDtl[s].rightAnswerMarks !== 'undefined'&&examDtl[s].rightAnswerMarks!=null&&examDtl[s].rightAnswerMarks!="null") {
                        sectionCorrectMarks = Number(examDtl[s].rightAnswerMarks);
                         if (typeof examDtl[s].wrongAnswerMarks !== 'undefined'&&examDtl[s].wrongAnswerMarks!=null&&examDtl[s].wrongAnswerMarks!="null") {
                             sectionIncorrectMarks = Number(examDtl[s].wrongAnswerMarks);
                         }
                     }
                 }
             }
         }
     }
     
     console.log(sectionCorrectMarks,sectionIncorrectMarks)
     for(var i=0;i<mergedList.length;i++){
        if (typeof mergedList[i].marks !== 'undefined'&&mergedList[i].marks!=null&&mergedList[i].marks!="null") {
            showScore = true;
        }
        if (mergedList[i].userOption == mergedList[i].correctOption) {
            correctArr.push(mergedList[i].userOption);
            if (typeof mergedList[i].marks !== 'undefined'&&mergedList[i].marks!=null&&mergedList[i].marks!="null") {
                score =Number(score)+Number(mergedList[i].marks);
            }else{
                score =correctArr.length;
            }
        }
        if (mergedList[i].userOption == '-1') {
            skipArr.push(mergedList[i].userOption);
        }
        if (mergedList[i].userOption != '-1' && mergedList[i].userOption != mergedList[i].correctOption) {
            inCorrectArr.push(mergedList[i].userOption);
            if (typeof mergedList[i].negativeMarks !== 'undefined'&&mergedList[i].negativeMarks!=null&&mergedList[i].negativeMarks!="null") {
                score =Number(score)-Number(mergedList[i].negativeMarks);
            }
        }
        
        if (secpresent){
            if ((allQuestions[i].userOption == allQuestions[i].correctOption) && (allQuestions[i].subject == resultSectionSelectValue)) {
                secCrtAns.push(mergedList[i].subject);
                if(sectionCorrectMarks!=-1){
                    secScore =Number(secScore)+Number(sectionCorrectMarks);
                }
                else if (typeof allQuestions[i].marks !== 'undefined'&&allQuestions[i].marks!=null&&allQuestions[i].marks!="null") {
                    secScore =Number(secScore)+Number(allQuestions[i].marks);
                }
            }
            if ((allQuestions[i].userOption == '-1') && (allQuestions[i].subject == resultSectionSelectValue) ) {
                secSpAns.push(mergedList[i].subject);
            }
            if ((allQuestions[i].userOption != '-1' && allQuestions[i].userOption != allQuestions[i].correctOption) && (allQuestions[i].subject == resultSectionSelectValue)) {
                secInAns.push(allQuestions[i].subject);
                //first preference for section marks, if not then individual
                if(sectionCorrectMarks!=-1){
                    secScore =Number(secScore)-Number(sectionIncorrectMarks);
                }
                else if (typeof allQuestions[i].negativeMarks !== 'undefined'&&allQuestions[i].negativeMarks!=null&&allQuestions[i].negativeMarks!="null") {
                    secScore =Number(secScore)-Number(allQuestions[i].negativeMarks);
                }
            }
            if ((allQuestions[i].userOption != '-1') && (allQuestions[i].subject == resultSectionSelectValue)){
                attemptedQs.push(allQuestions[i].subject);
            }
            if (allQuestions[i].subject == resultSectionSelectValue){
                ttCount.push(allQuestions[i].subject);
            }

            if (allQuestions[i].subject == resultSectionSelectValue){
                if (allQuestions[i].userOption == allQuestions[i].correctOption) {
                    crtGA.push(allQuestions[i].userOption);
                }
                if (allQuestions[i].userOption == '-1') {
                    skpGA.push(allQuestions[i].userOption);
                }
                if (allQuestions[i].userOption != '-1' && allQuestions[i].userOption != allQuestions[i].correctOption) {
                    incGA.push(allQuestions[i].userOption);
                }
            }
        }
     }

     secWiseCorrect = secCrtAns;
     secWiseInCorrect = secInAns;
     secWiseSkipped = secSpAns;
     secWiseAttempted = attemptedQs;
     secWiseTotal = ttCount;
     skipperScore = skipArr.length;
     wrongScore = inCorrectArr.length;
     correctScore = correctArr.length;
     if(secpresent) {
        $('#right').text(secWiseCorrect.length);
         $('#wrong').text(secWiseInCorrect.length);
         $('#skipped').text(secWiseSkipped.length);
         $('.updateScore').text((score%1==0?score.toFixed(0):score.toFixed(2)));
     }else{
        $('#right').text(correctCount);
         $('#wrong').text(inCorrectCount);
         $('#skipped').text(skippedCount);
         $('.updateScore').text((score%1==0?score.toFixed(0):score.toFixed(2)));
     }
    if(showScore){
        document.getElementById('wsResultPointsCount').innerHTML = "<span class='titleSplit'>Total Marks </span><span class='titleColon'>: </span><span>" + (score%1==0?score.toFixed(0):score.toFixed(2))  + "</span>";
        if (secpresent) {
            document.getElementById('wsSectionResultPointsCount').innerHTML = "<span class='titleSplit'>Section Marks </span><span class='titleColon'>: </span><span>" + (secScore%1==0?secScore.toFixed(0):secScore.toFixed(2))  + "</span>";
        }
    }else {
        if (secpresent) {
            document.getElementById('wsResultPointsCount').innerHTML = "Points <span>" + (score%1==0?score.toFixed(0):score.toFixed(2)) + "/" + sectionList.length + "</span>";
        } else {
            document.getElementById('wsResultPointsCount').innerHTML = "Points <span>" + correctScore + "/" + mergedList.length + "</span>";
        }
    }
    console.log("answersList",answersList)
    if(source=='android') {
     JSInterface.resizeWebview(true);
    }else {
     webkit.messageHandlers.resizeWebview.postMessage(true);
    }
}
function sectionListConstruct(){
    var sectionListHtml = "";
    if (storingInitialData.examDtl!=null && storingInitialData.examDtl!=""){
        document.querySelector('.sectionSelectDropDown').classList.remove('d-none')
        sectionList = storingInitialData.examDtl;
        for (var i=0;i<sectionList.length;i++){
            sectionListHtml+="<option value='"+sectionList[i].subject+"'>"+sectionList[i].subject+"</option>";
        }
        document.getElementById('sectionSelectDropDown').innerHTML = sectionListHtml;
        resultSectionSelectValue = sectionList[0].subject;
    }

}
function resultSectionChange(){
    resultSectionSelectValue = document.getElementById("sectionSelectDropDown").value;
    computeDistributedScore(resqaObj)
}
function freq(nums) {
    return nums.reduce((acc, curr) => {
        acc[curr] = -~acc[curr];
        return acc;
    }, {});
}

function replaceKeys(object) {
    Object.keys(object).forEach(function (key) {
        var newKey = key.replaceAll(" ","");
        if (object[key] && typeof object[key] === 'object') {
            replaceKeys(object[key]);
        }
        if (key !== newKey) {
            object[newKey] = object[key];
            delete object[key];
        }
    });
}
//ios only
function recieveScore(data){
    $('.loader-submit').hide();
    if(quizMode =='') {
        $('#winnerModal').modal('hide');
        currentBadge(data);
        $('.resultWrapper').removeClass('d-none');
    }
    else if(quizMode =='practice' || quizMode =='test' || quizMode =='testSeries'){
        console.log(data);
        $('.quizes,.quiz-profile').removeClass('d-flex').addClass('d-none');
        $('.practice-result').removeClass('d-none').addClass('d-flex');
        $('.nav-tabs a[href="#all"]').tab('show');
        showAllAnswers();
        currentBadge(data);
    }
    if(quizMode =='test'){
        $('#practice-summary').text('Test Summary');
    }
}
//ios only
function imageJsonSave(){
    image_won='https://assets1.lottiefiles.com/packages/lf20_sonmjuir.json';
    image_lose='https://assets5.lottiefiles.com/private_files/lf30_ovxvpeuq.json';
    image_tie='https://assets5.lottiefiles.com/packages/lf20_pthbdqkh.json';
    image_medal='https://assets6.lottiefiles.com/packages/lf20_mntsz64q.json';
    image_badge='https://assets10.lottiefiles.com/packages/lf20_i6sqnxav.json';
}
function botImages(){
if(quizMode==''){
    if(source=='android') {
        $(".bot-profile-img").attr({"src": 'file:///android_asset/quiz/assets/images/prepjoy/' + botImage + '.jpg'});
    }
    else{
        $(".bot-profile-img").attr({ "src": +botImage+'.jpg' });
    }
    }
}
function stopAudios() {
if(quizMode==''){
    if (source == 'android') {
        JSInterface.stopAudio();
    } else if (source == 'ios') {
        webkit.messageHandlers.stopAudio.postMessage('');
    }
    }
}
function winAudios() {
if(quizMode==''){
    if (source == 'android') {
        JSInterface.winAudio();
    } else if (source == 'ios') {
        webkit.messageHandlers.winAudio.postMessage('');
    }
    }
}
function tieAudios() {
if(quizMode==''){
    if (source == 'android') {
        JSInterface.tieAudio();
    } else if (source == 'ios') {
        webkit.messageHandlers.tieAudio.postMessage('');
    }
    }
}
function loseAudios() {
if(quizMode==''){
    if (source == 'android') {
        JSInterface.loseAudio();
    } else if (source == 'ios') {
        webkit.messageHandlers.loseAudio.postMessage('');
    }
    }
}
function userWinsImage() {
if(quizMode==''){
    if (source == 'ios') {
        animdata.path = image_won;
    } else if (source == 'android') {
        animdata.path = 'file:///android_asset/quiz/assets/images/prepjoy/win.json';
    }
    }
}
function gameTieImage() {
if(quizMode==''){
    if (source == 'ios') {
        animdata.path = image_tie;
    } else if (source == 'android') {
        animdata.path = 'file:///android_asset/quiz/assets/images/prepjoy/tie.json';
    }
    }
}
function gameLoseImage() {
if(quizMode==''){
    if (source == 'ios') {
        animdata.path = image_lose;
    } else if (source == 'android') {
        animdata.path = 'file:///android_asset/quiz/assets/images/prepjoy/lose.json';
    }
    }
}
function shareQuiz(){
if(quizMode==''){
    if (source == 'android') {
        JSInterface.shareQuiz();
    } else if (source == 'ios') {
        webkit.messageHandlers.shareQuiz.postMessage('');
    }
    }
}

function storeRecId(response,qaObj){
    qaObj = JSON.parse(qaObj)
    qaObj.quiRecId = response.quiRecId
    console.log(qaObj,qaObj.quiRecId);
    if(source=='android') {
    console.log(JSON.stringify(qaObj))
        JSInterface.storeRecId(JSON.stringify(qaObj));
    }
    else {
        webkit.messageHandlers.storeRecId.postMessage(qaObj);
    }
}

function playRematch(){
    console.log("playRematch()")
    conn.send({rematch:true});
    $("#alertsModal").modal("show");

}

function playPreviousMatch(){
    console.log("playPreviousMatch()")
    conn.send({playPreviousMatch:true});
    callbackForRematch();
    $("#alertsModal").modal("show");
}

function playNextMatch(){
   conn.send({playNextMatch:true});
   callbackForRematch();
   $("#alertsModal").modal("show");
}

function callbackForRematch() {
if (source == 'android') {
        JSInterface.rematchNew();
    } else if (source == 'ios') {
        webkit.messageHandlers.rematchNew.postMessage('');
    }
}