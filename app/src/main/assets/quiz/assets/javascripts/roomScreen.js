var usrImg;
var rematchAcceptText=false;

function initialize(data) {

   console.log("Initial data :" +JSON.stringify(data));
   console.log("User Credentials :"+data.userCreds);

   siteId = data.userCreds.siteId;
   currentUsername = data.userCreds.userName
   userId = siteId +"_"+data.userCreds.userId
   quizAvailable = data.quizAvailable;
   onlineChallenge = data.onlineChallenge;
   testId=data.testId;
   gameEbook = data.gameEbook;
   language = data.language;
   sound=data.sound;
   usrImg=data.userImage;


   if(sound=='off'){
     $('.audioChange i').text('volume_off');
       setSound=true;
   }else{
       $('.audioChange i').text('volume_up');
       setSound=false;
   }

   if(testId !='' && testId !='undefined' && testId !=undefined){
       dailyTest=true;
   }
   if(onlineChallenge){
       $('.room-header').hide();
   }
   if(gameEbook){
       $('.button-results').addClass('d-none');
   }

   console.log("quizAvailable"+quizAvailable);

   if(!quizAvailable){
      //data.quizData = "";
      callPreviousMatch(quizAvailable);
   }else if(data.reMatch){
      console.log(quizAvailable,previousMatch);
      console.log("inside else if :"+data);
      $('#roomScreenDiv').show(); //showing initial screen
      $('.resultWrapper').addClass('d-none'); //hiding results screen
      if(!rematchAcceptText){
        $('#connectionStatus').text("You accepted "+realUserName+"'s challenge "); //changing connection text
      }else if(rematchAcceptText){
        $('#connectionStatus').text(realUserName +" has accepted your challenge"); //changing connection text
      }
      $('.action-wrapper').hide();
      $('#sharebtn').addClass('invisible');
      $('.bot-anim-wrapper ').removeClass('animate__animated animate__fadeInLeft');
      userScore = 0;
      botScore = 0;
      userTotalTime=0;
      botTotalTime=0;
      que_count=0;
      userAnswers=[];
      qaObj = {
           userAnswers: []
      };
      checkObj = false;
      questionTime=timeValue/100;
      checkObj1=false;
      botTime='';
      timerArr=[];
      var readyGo = document.getElementById("no-counter");
      readyGo.innerHTML='';
      document.getElementById("winner").innerHTML="";
      $('.user-status').text('').removeClass('red');
      $('#playerScore').text(userScore + ' pts');
      $('#botScores').text(botScore + ' pts');
      if(nextMatchPlay){
        conn.send({quizAvailable:true,nextMatchPlayScreen:true});
        $('#nextMatchModal').modal('hide');
      }else{
       conn.send({quizAvailable:true});
      }
         $('#previousMatchModal').modal('hide');
      }

   if(data.userImage !=''){
      userImage = data.userImage;
   }else{
      userImage = 'file:///android_asset/quiz/assets/images/prepjoy/default_icon.png'
   }

   $('.playerName').text(currentUsername);

    var myDetails = {
        currentUsername: currentUsername,
        userId: userId,
        connect: true,
        userImage:usrImg
      };

    if(!data.reMatch){
      peer = new Peer({ debug: 1});
    }

    peer.on("open", function (id) {
        console.log("connection is opened");

        if (peer.id === null) {
          console.log("Received null id");
          peer.id = lastPeerId;
        } else {
          lastPeerId = peer.id;
        }

        console.log("Generated ID: " + peer.id);
        myConnectionCode = peer.id;

        if(source=='android'){
          JSInterface.getGameCode(myConnectionCode);
        }else if(source=='ios'){
          webkit.messageHandlers.getGameCode.postMessage(myConnectionCode);
        }
    });

    peer.on("connection", function (c) {
          console.log("checking single connection");
          // Allow only a single connection
          if (conn && conn.open) {
            c.on("open", function () {
              c.send({ singleConnection: true });
              // setTimeout(function() { c.close(); }, 500);
            });
            return;
          }

          conn = c;
          console.log("Connected to: " + conn.peer);


          conn.on("close", function () {
          console.log("close")
             $('#connectionLostModal').modal('show');
             $('#rematchModal').modal('hide');
             $('#previousMatchModal').modal('hide');
             $('#nextMatchModal').modal('hide');
             internetLost=true;
             stoppingGame();
             peer.destroy();
          });

          conn.on("data", function (data) {
            console.log("I received :"+JSON.stringify(data));
            botAnswer = data.userSelectedOption;
            botTime += data.userRefTime;

            if (data.connect) {
              joinerDetails = data;
              console.log(data.currentUsername + " has joined the game");
              conn.send(myDetails);

              if(data.botImage!=""){
                botImage= data.botImage;
              }else{
                botImage='file:///android_asset/quiz/assets/images/prepjoy/profiledefault.png'
              }

              $('.realUsr-img').attr({"src": botImage});
              $('.realUsr-img').addClass('animate__animated animate__zoomInUp');
              $('.disUsrImg').show();
              $("#connectionStatus").text(data.currentUsername + " has joined the game!");
              realUserName = data.currentUsername;
              opponentId = data.userId;
              $(".startGameBtn").removeAttr("disabled");
            }

            if(data.userOptionVal == 0){
               $('.progress-bar-vertical.player2').addClass('progress-incorrect');
               $('#player2').css({
                 'height': data.userTime,
                 'background': '#FF4141'
               });
            }else if (data.userOptionVal == 1){
               $('.progress-bar-vertical.player2').addClass('progress-correct');
               $('#player2').css({
                  'height': data.userTime,
                  'background': '#42B538'
               });

               console.log(botScore);
               botScore += 1;
               $('#botScores').text(botScore + ' pts');
            }

            botTotalTime = botTime;

            if(data.peerAnswered){
                peerAnswered=true;
                    if(userAnswered && peerAnswered){
                        console.log("GO TO NEXT QUESTION-2");
                        if(quizMode=='') {
                           setTimeout(showCorrectAnswer, 2000);
                        }
                        countdown.stop(); //clear counter
                        clearTimerBar();
                        $('#app,#countdown').hide();
                        document.getElementById("base-timer-path-remaining").setAttribute("stroke-dasharray", '283');
                        setTimeout(nextQue, 4000);
                    }
            }

            if (data.rematch){
              reMatch=true;
              previousMatch=false;
              $('#rematchText').text(challengerName + " invites you for a game.");
              $('#rematchModal').modal('show');
              $('#sharebtn').addClass('invisible');
            }
            if (data.playPreviousMatch){
              previousMatch=true;
              $('#previousMatchModalText').text(challengerName + " invites you for a game.");
              $('#previousMatchModal').modal('show');
              $('#sharebtn').addClass('invisible');
            }


            if(data.playNextMatch){
              nextMatchPlay = true;
              if(nextMatchPlay){
                $('#nextMatchModalText').text(challengerName + " invites you for a game.");
                $('#nextMatchModal').modal('show');
                $('#sharebtn').addClass('invisible');
              }
            }

            if (data.rematchAccepted){
              $('#roomScreenDiv').show(); //showing initial screen
              $('.resultWrapper').addClass('d-none'); //hiding results screen
              $('#connectionStatus').text(realUserName +" has accepted your challenge"); //changing connection text
              $('.action-wrapper').hide();
              $('.bot-anim-wrapper ').removeClass('animate__animated animate__fadeInLeft');
              $("#alertsModal").modal("hide");
              //rematch();

               if(source=='android'){
                   console.log("callback is given");
                   JSInterface.realUserRematch();
               }else if(source=='ios'){
                   webkit.messageHandlers.realUserRematch.postMessage('');
               }


              rematch=true;
              rematchGotAcc=true;
              userScore = 0;
              botScore = 0;
              userTotalTime=0;
              botTotalTime=0;
              que_count=0;
              userAnswers=[];
              qaObj = {
                  userAnswers: []
              };
              checkObj = false;
              questionTime=timeValue/100;
              checkObj1=false;
              botTime='';
              timerArr=[];
              var readyGo = document.getElementById("no-counter");
              readyGo.innerHTML='';
              document.getElementById("winner").innerHTML="";
              $('.user-status').text('').removeClass('red');
              $('#playerScore').text(userScore + ' pts');
              $('#botScores').text(botScore + ' pts');
              $('.practice-result').removeClass("d-flex").addClass("d-none");
              if(source=='android') {
                       JSInterface.resizeWebview(false);
                      }else {
                       webkit.messageHandlers.resizeWebview.postMessage(false);
                      }

              }else if(data.rematchAccepted == false){
                $('#denyMatchText').text(realUserName + " denied your challenge");
                $('#denyMatchModal').modal('show');
                $("#alertsModal").modal("hide");
                $(".closeModal").on("click",function(){
                  $('#denyMatchModal').modal('hide');
                })
            }


            if(data.previousMatchAccepted){
              var options = {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
              };
              console.log(storingInitialData,JSON.stringify(storingInitialData))
              var todaysDate = new Date().toLocaleDateString('en-US', options)
              todaysDate = todaysDate.replaceAll("/","-");
              $("#alertsModal").modal("hide");

              previousMatch=true;
              rematchAcceptText=true;
              if(source=='android'){
                console.log("callback is given");
                JSInterface.getPreviousMatch(todaysDate);
              }else if(source=='ios'){
                webkit.messageHandlers.getPreviousMatch.postMessage('');
              }
              if(source=='android') {
                       JSInterface.resizeWebview(false);
                      }else {
                       webkit.messageHandlers.resizeWebview.postMessage(false);
                      }
                      $('.practice-result').removeClass("d-flex").addClass("d-none");
                                    if(source=='android') {
                                             JSInterface.resizeWebview(false);
                                            }else {
                                             webkit.messageHandlers.resizeWebview.postMessage(false);
                                            }
            }else if(data.previousMatchAccepted==false){
               $('#denyMatchText').text(realUserName + " denied your challenge");
               $('#denyMatchModal').modal('show');
               $("#alertsModal").modal("hide");
               $(".closeModal").on("click",function(){
                  $('#denyMatchModal').modal('hide');
               })
            }


            if(data.nextMatchAccepted){
              var options = {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
              };
              var todaysDate = new Date().toLocaleDateString('en-US', options)
              todaysDate = todaysDate.replaceAll("/","-");
              $("#alertsModal").modal("hide");

              nextMatchPlay=true;
              rematchAcceptText=true;
              if(source=='android'){
                console.log("callback is given");
                JSInterface.getNextMatch(todaysDate);
              }else if(source=='ios'){
                webkit.messageHandlers.getNextMatch.postMessage('');
              }
              if(source=='android') {
                       JSInterface.resizeWebview(false);
                      }else {
                       webkit.messageHandlers.resizeWebview.postMessage(false);
                      }
                      $('.practice-result').removeClass("d-flex").addClass("d-none");
                                    if(source=='android') {
                                             JSInterface.resizeWebview(false);
                                            }else {
                                             webkit.messageHandlers.resizeWebview.postMessage(false);
                                            }
            }else if(data.nextMatchAccepted==false){
              $('#denyMatchText').text(realUserName + " denied your challenge");
              $('#denyMatchModal').modal('show');
              $("#alertsModal").modal("hide");
              $(".closeModal").on("click",function(){
                $('#denyMatchModal').modal('hide');
              })
            }

            if(data.appMinimized){
                 $("#connectionLostModal").modal("show");
            }
          });

    });


    $('#rematchBtnAccept').on('click',function (){
              conn.send({rematchAccepted:true});

              $('#roomScreenDiv').show(); //showing initial screen
              $('.resultWrapper').addClass('d-none'); //hiding results screen
              $('#connectionStatus').text("You accepted "+ realUserName + "'s challenge");
              $('.action-wrapper').hide();
              $('#rematchModal').modal('hide');
              $('.bot-anim-wrapper ').removeClass('animate__animated animate__fadeInLeft');
             $('#sharebtn').addClass('invisible');
              //rematch();
                if(source=='android'){
                   console.log("callback is given");
                   JSInterface.rematchNew();
               }else if(source=='ios'){
                   webkit.messageHandlers.rematchNew.postMessage('');
               }
              rematch=true;
              rematchAcceptText=true;
              userScore = 0;
              botScore = 0;
              userTotalTime=0;
              botTotalTime=0;
              que_count=0;
              userAnswers=[];
              qaObj = {
                  userAnswers: []
              };
              checkObj = false;
              questionTime=timeValue/100;
              checkObj1=false;
              botTime='';
              timerArr=[];
              var readyGo = document.getElementById("no-counter");
              readyGo.innerHTML='';
              document.getElementById("winner").innerHTML="";
              $('.user-status').text('').removeClass('red');
              $('#playerScore').text(userScore + ' pts');
              $('#botScores').text(botScore + ' pts');
              $('.practice-result').removeClass("d-flex").addClass("d-none");
              $('.action-wrapper').hide();
              if(source=='android') {
                       JSInterface.resizeWebview(false);
                      }else {
                       webkit.messageHandlers.resizeWebview.postMessage(false);
                      }
    })

    $('.rematchBtnDenied').on('click',function (){
              conn.send({rematchAccepted:false});
              $('#rematchModal').modal('hide');
              $('#sharebtn').addClass('invisible');
    })

    $("#previousAccepted").on("click",function(){
             conn.send({previousMatchAccepted:true});

             var todaysDate = new Date().toLocaleDateString('en-US', todaysDate)
             todaysDate = todaysDate.replaceAll("/","-");

             previousMatch=true;
             rematchAcceptText=true;

             if(source=='android'){
               console.log("callback is given");
               JSInterface.getPreviousMatch(todaysDate);
             }else if(source=='ios'){
               webkit.messageHandlers.getPreviousMatch.postMessage('');
             }
             $('.practice-result').removeClass("d-flex").addClass("d-none");
             $('.action-wrapper').hide();
             if(source=='android') {
                      JSInterface.resizeWebview(false);
                     }else {
                      webkit.messageHandlers.resizeWebview.postMessage(false);
                     }
    });

    $('.previousDenied').on('click',function (){
              conn.send({previousMatchAccepted:false});
              $('#previousMatchModal').modal('hide');
              $('#sharebtn').addClass('invisible');
    })

    $("#nextAccepted").on("click",function(){
             conn.send({nextMatchAccepted:true});

             var todaysDate = new Date().toLocaleDateString('en-US', todaysDate)
             todaysDate = todaysDate.replaceAll("/","-");

             previousMatch=true;
             nextMatchPlay=true;
             rematchAcceptText=true;

              if(source=='android'){
                console.log("callback is given");
                JSInterface.getNextMatch(todaysDate);
              }else if(source=='ios'){
                webkit.messageHandlers.getNextMatch.postMessage('');
              }
              $('.practice-result').removeClass("d-flex").addClass("d-none");
              $('.action-wrapper').hide();
              if(source=='android') {
               JSInterface.resizeWebview(false);
              }else {
               webkit.messageHandlers.resizeWebview.postMessage(false);
              }
    });

    $('.nextDenied').on('click',function (){
        conn.send({previousMatchAccepted:false});
        $('#nextMatchModal').modal('hide');
        $('#sharebtn').addClass('invisible');
    })


    peer.on("error",function(err){
        conn.send("lost internet1");
        if(err.type=="network"){
        errorType=err.type;
            $('#internetLostModal').modal('show');
            conn.send("lost internet1");
        }
    })
    peer.on("disconnected", function () {
        if(!errorType=="network"){
            $("#connectionLostModal").modal('show')
            $("#connectionText").text("You lost your connection.");
        }
    });

    storingInitialData = data;
}

$('#startGameBtn').on('click',function (){
    if(conn.peerConnection.iceConnectionState != 'disconnected'){
      if(previousMatch && quizAvailable){
        conn.send({previousStart:true})
      }
       initializeQuizResults(storingInitialData);

       if(quizMode==''){
          $('.que-show').removeClass('d-none');
           quizMode='';
           quizType='play';
       }

       if(source=='android'){
          JSInterface.storingOpponentData(realUserName,opponentId );
       }else if(source=='ios'){
          webkit.messageHandlers.storingOpponentData.postMessage(realUserName,opponentId );
       }
    }else if(conn.peerConnection.iceConnectionState == 'disconnected'){
      $('#connectionLostModal').modal('show');
      $('#connectionText').text("Your opponent lost the connection.");
    }
})


function gameCode(data){
    console.log(data);
    if(!onlineChallenge){
     $('.room-code__sec').show();
    }else{
      $('#accessloader').hide();
    }
    $('#accessloader').hide();
    myId = data.challengeCode;
    accessCode.innerHTML = data.challengeCode;
    gameAccessCode = data.challengeCode
    userChallengeId = data.userChallengeId;
    console.log(myConnectionCode);
}

function stoppingGame(){
   $('#connectionLostModal').modal('show');
   stop();
   resetUIforNext();
   clearImmediate();
   document.getElementById("base-timer-path-remaining").setAttribute("stroke-dasharray", '283');
}

function quizInitialization(data){
  console.log("quizInitialization :"+JSON.stringify(data));
  console.log(conn,joinerDetails);

  var userDetails={
     currentUsername:currentUsername,
     userId:userId,
  }

  questions = data.quizData;
  console.log(questions);
  console.log(data.reMatch);
}

function shareGameCode(){
  if(source=='android'){
    JSInterface.shareGameCode(gameAccessCode);
  }else if(source=='ios'){
     webkit.messageHandlers.backToHome.postMessage('');
  }else {
     console.log("Share code")
  }
}

$('.connectionlost').on('click',function (){
    if(source=='android'){
      JSInterface.lostConnection();
    }else if(source=='ios'){
      webkit.messageHandlers.lostConnection().postMessage('');
    }
})

function callPreviousMatch(quizAvailable){
  console.log(quizAvailable);
  if(quizAvailable){
      console.log(quizAvailable);
      $('#roomScreenDiv').show(); //showing initial screen
      $('.resultWrapper').addClass('d-none'); //hiding results screen
      $('#connectionStatus').text(realUserName +" has accepted your challenge"); //changing connection text
      $('.action-wrapper').hide();
      $('.bot-anim-wrapper ').removeClass('animate__animated animate__fadeInLeft');
      userScore = 0;
      botScore = 0;
      userTotalTime=0;
      botTotalTime=0;
      que_count=0;
      userAnswers=[];
      qaObj = {
        userAnswers: []
      };
      checkObj = false;
      questionTime=timeValue/100;
      checkObj1=false;
      botTime='';
      timerArr=[];
      var readyGo = document.getElementById("no-counter");
      readyGo.innerHTML='';
      document.getElementById("winner").innerHTML="";
      $('.user-status').text('').removeClass('red');
      $('#playerScore').text(userScore + ' pts');
      $('#botScores').text(botScore + ' pts');
      $('.practice-result').removeClass("d-flex").addClass("d-none");
      conn.send({quizAvailable:true});
      $('#previousMatchModal').modal('hide');
      $('.practice-result').removeClass("d-flex").addClass("d-none");

      if(source=='android') {
       JSInterface.resizeWebview(false);
      }else {
       webkit.messageHandlers.resizeWebview.postMessage(false);
      }
    }else{
      console.log(quizAvailable);
      $('#denyMatchText').text("This quiz not available, sorry.");
      $("#denyMatchModal").modal("show");
      $('#nextMatchModal').modal('hide');
      $('#previousMatchModal').modal('hide');
      $("#alertsModal").modal("hide");
      $(".closeModal").on("click",function(){
        $('#denyMatchModal').modal('hide');
         conn.send({quizAvailable:false});
         $('#previousMatchModal').modal('hide');
         $('#nextMatchModal').modal('hide');
      })
    }
}
$(".internerLost").on("click",function(){
$("#internetLostModal").modal("hide");
})
$(".closeModal").on("click",function (){
   $("#alertsModal").modal("hide");
})
$(".alertClose").on("click",function (){
   $("#alertsModal").modal("hide");
})

function appMinimized(){
    conn.send({appMinimized:true});

    $("#connectionLostModal").modal('show')
    $("#connectionText").text("You lost your connection.");

}
