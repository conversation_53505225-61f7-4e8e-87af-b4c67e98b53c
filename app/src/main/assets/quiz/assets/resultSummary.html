<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>PrepJoy</title>
    <meta name="theme-color" content="#2EBAC6"/>
    <meta name="author" content="P.R">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;400;500&display=swap"
          rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Righteous&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css"  crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet"
          href="file:///android_asset/quiz/assets/stylesheets/prepJoy/bootstrap.min.css"/>
    <link rel="stylesheet"
          href="file:///android_asset/quiz/assets/stylesheets/prepJoy/animate.css"/>
    <link rel="stylesheet"
          href="file:///android_asset/quiz/assets/stylesheets/prepJoy/jquery-ui.css">
    <link rel="stylesheet" href="file:///android_asset/quiz/assets/stylesheets/prepJoy/prepJoy.css">
    <link rel="stylesheet" id="whTheme">
    <script src="file:///android_asset/quiz/assets/javascripts/jquery.min.js"></script>
    <script src="file:///android_asset/quiz/assets/javascripts/jquery-ui.js"></script>
    <script src="file:///android_asset/quiz/assets/javascripts/popper.min.js"></script>
    <script src="file:///android_asset/quiz/assets/javascripts/bootstrap.min.js"></script>
    <script src="file:///android_asset/quiz/assets/javascripts/swiper.js"></script>
    <style>
         body{
            overflow-y:scroll !important;
        }
    </style>
</head>

<body>

<div class="container" id="qa-answer">

</div>
<div class="tab-wrapper">
    <div class="result__header-wrapper">
        <div class="d-flex" style="margin-top:24px">
            <button class="back__btn" onclick="summaryBack()"><i class="fa-solid fa-arrow-left"></i></button>
            <p class="d-flex text-white justify-content-center align-items-center w-100"><span class="result">Summary / Details</span></p>
        </div>
        <ul class="nav nav-tabs">
            <li class="active"><a data-toggle="tab" href="#all">All</a></li>
            <li onclick="correctAnswers()"><a data-toggle="tab" href="#correctAnswers">Correct</a></li>
            <li onclick="incorrectAnswers()"><a data-toggle="tab" href="#incorrectAnswers">Incorrect</a></li>
            <li onclick="skippedAnswers()"><a data-toggle="tab" href="#skippedAnswers">Skipped</a></li>
            <li onclick="markedForReview()" style="display:none" id="markedListElem"><a data-toggle="tab" href="#markedForReview">Marked for Review</a></li>
        </ul>
    </div>

    <div class="tab-content">
        <div id="all" class="tab-pane fade in active">

        </div>
        <div id="correctAnswers" class="tab-pane fade">

        </div>
        <div id="incorrectAnswers" class="tab-pane fade">

        </div>
        <div id="skippedAnswers" class="tab-pane fade">

        </div>
        <div id="markedForReview" class="tab-pane fade">

        </div>
    </div>
</div>

<div class='modal fade' id='prep-report-question'>
    <div class='modal-dialog modal-dialog-centered modal-dialog-zoom modal-sm'>
        <div class='modal-content prep-report-modal'>
            <div class='modal-header'>
                <p class='modal-title d-flex align-items-center prep-report-icon'>Report the
                    question <i class='ml-1 material-icons'>error</i></p>
                <button type='button' class='close' data-dismiss='modal'>&times;</button>
            </div>
            <div class='modal-body d-flex flex-column'>
                <div class="chkbox mt-1">
                    <input type="checkbox" class="form-check-input" id="spellingMistake">
                    <label class="form-check-label tick" for="spellingMistake">
                        <span></span>Spelling Mistake
                    </label>
                </div>
                <div class="chkbox mt-1">
                    <input type="checkbox" class="form-check-input" id="directionNotGiven">
                    <label class="form-check-label tick" for="directionNotGiven">
                        <span></span>Direction not given
                    </label>
                </div>
                <div class="chkbox mt-1">
                    <input type="checkbox" class="form-check-input" id="imageNotVisible">
                    <label class="form-check-label tick" for="imageNotVisible">
                        <span></span>Graph / Image not visible
                    </label>
                </div>

                <div class="chkbox mt-1">
                    <input type="checkbox" class="form-check-input" id="incompleQuestion">
                    <label class="form-check-label tick" for="incompleQuestion">
                        <span></span>Incomplete question
                    </label>
                </div>
                <div class="chkbox mt-1">
                    <input type="checkbox" class="form-check-input" id="otherIssues">
                    <label class="form-check-label tick" for="otherIssues">
                        <span></span>Other Issues
                    </label>
                </div>

                <div class="form-group mt-3 textbox">
                    <textarea class="form-control" id="moreInformation"
                              placeholder="Let us know"></textarea>
                </div>
            </div>
            <div class='modal-footer d-flex justify-content-center'>
                <button type='button' class='btn btn-submit rpBtnSub reportBtn'
                        onclick="reportSubmit()">Submit
                </button>
            </div>
        </div>
    </div>
</div>

<script>


//check lang and image in response

    function checkLanguageAndImage(item){

      if(language=='hindi') {
         var img='';
              if(item.match(/<img/)) {
                img= item.substr(item.indexOf("<"));
              }
         item = item.substr(0,item.indexOf("~~"));
         item = item + img;

      }else if(language=='kannada') {
         var img='';
              if(item.match(/<img/)) {
                img= item.substr(item.indexOf("<"));
              }
         item = item.substr(0,item.indexOf("~~"));
         item = item + img;

      }else if(language=='english') {
         item = item.substr(item.indexOf("~~")+2);
         item =item.split('<span class="math-tex">').join('');
         item =item.split('</span>').join('');
       }else{
         item = item;
         item =item.split('<span class="math-tex">').join('');
         item =item.split('</span>').join('');
      }
      if(item.indexOf('</table>') > -1){
        item=item.split("<table").join("<div class='table-responsive'><table class='table'");

        item=item.split('</table>').join('</table></div>');
      }

      return item;
 }

    //Check and replace symbols.

    function replaceSymbols(item){
      var replacedItem='';
      if(item !=null){
         replacedItem=item;
      }
      return replacedItem;
    }


    //Replace image url for apps
   function replaceImageUrlForApps(answerOptions){
        var myAnswerOption ='';

            if (answerOptions.indexOf('src') != -1){
            answerOptions = answerOptions.split('/funlearn/').join(serviceBaseUrl +'funlearn/');

                 myAnswerOption = answerOptions;

            }
            else{
                myAnswerOption=answerOptions;
            }

            return myAnswerOption;
        }


</script>
<script src="file:///android_asset/quiz/assets/javascripts/resultSummary.js"></script>
</body>
</html>