html,
body {
  margin: 0;
  padding: 0;
  height: 100vh;
  font-family: 'Poppins', sans-serif;
}
html {
  height: 100vh;
}
body {
  height: 100%;
}
h1,
h2,
h3,
h4,
label,
input,
p,
pre,
span {
  font-family: 'Poppins', sans-serif;
}
p {
  margin-bottom: 0;
}
body {
  background: #060029;
  min-height: 100vh;
  overflow-y: hidden;
  overflow-x:hidden;
}
.btn.focus, .btn:focus{
box-shadow:none !important;
}

/* Header Submit Button Styles */
.header-buttons-wrapper {
  display: flex;
  align-items: center;
  gap: 15px;
}

.btn-submit-quiz {
  background: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.btn-submit-quiz:hover {
  background: #218838;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.btn-submit-quiz:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(40, 167, 69, 0.3);
}

.btn-submit-quiz i {
  font-size: 16px;
}

.btn-submit-quiz.d-none {
  display: none !important;
}
.play-wrapper {
  height: 100vh;
}
.btn-play {
  padding: 0.5rem 2rem;
  background: #DA0101;
  color: #fff;
}
.instruction p {
  color: #fff;
}
.instruction {
  min-height: 100vh;
}
.button-wrapper {
  position: absolute;
  bottom: 0;
  width: 100%;
  display: flex;
  background: #213247;
}
.button-wrapper button {
  width: 50%;
  color: #fff;
  border-radius: 0;
  border: none;
}
.button-wrapper button:focus {
  outline: none;
}
.button-wrapper button:first-child {
  border-right: 1px solid rgba(237, 237, 237, 0.2);
}
.button-main {
  padding: 16px 42px;
  box-shadow: 0px 0px 12px -2px rgba(0, 0, 0, 0.5);
  line-height: 1.25;
  background: none;
  text-decoration: none;
  color: white;
  font-size: 16px;
  letter-spacing: 0.08em;
  text-transform: uppercase;
  position: relative;
  transition: background-color 0.6s ease;
  overflow: hidden;
}
.button-main:after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  top: 50%;
  left: 50%;
  top: var(--mouse-y);
  left: var(--mouse-x);
  transform-style: flat;
  transform: translate3d(-50%, -50%, 0);
  background: rgba(255, 255, 255, 0.1);
  border-radius: 100%;
  transition: width 0.3s ease, height 0.3s ease;
}
.button-main:focus,
.button-main:hover {
  background: #16212f;
}
.button-main:active:after {
  width: 300px;
  height: 300px;
}
.gamer-profile {
  min-height: 100vh;
}
.gamer-profile .media-body p {
  color: #fff;
}
.gamer-profile .media-body h5 {
  color: #fff;
}
.gamer-profile .media {
  height: 50vh;
}
.gamer-profile .media img {
  border: 3px solid #fff;
}
.gamer-profile .media h5,
.gamer-profile .media p {
  padding: 0;
  margin: 0;
}
.gamer-profile .media p {
  font-size: 10px;
}
.gamer-profile .media:first-child {
  padding: 6rem 2rem;
}
.gamer-profile .media.botProfile:last-child {
  border: none;
  padding: 6rem 2rem;
}
.gamer-profile .media.botProfile:last-child h5,
.gamer-profile .media.botProfile:last-child p {
  text-align: right;
}
.border-bot {
  border-top: 3px solid #fff;
}
.playerbg {
  background: url("file:///android_asset/quiz/assets/images/prepjoy/playerbg.png") center no-repeat;
  background-size: cover;
  background-color: #E83500;
}
.bot-anim-wrapper {
  display: flex;
}
.bot-anim {
  background: url("file:///android_asset/quiz/assets/images/prepjoy/bot-anim.svg") center no-repeat;
  height: 50px;
  width: 50px;
  animation: mymove 4s infinite;
  position: relative;
}
@keyframes mymove {
  0% {
    left: -100px;
    top: 0px;
  }
  25% {
    left: 300px;
    top: 0px;
  }
  50% {
    left: -100px;
    top: 0px;
  }
  75% {
    left: 300px;
    top: 0px;
  }
  100% {
    left: -100px;
    top: 0px;
  }
}
.botbg {
  background: url("file:///android_asset/quiz/assets/images/prepjoy/botbg.png") center no-repeat;
  background-size: cover;
}
.roundbg {
  background: url("file:///android_asset/quiz/assets/images/prepjoy/roundbg.svg") center no-repeat;
  background-size: contain;
  width: 80px;
  height: 100%;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
.roundbg img {
  width: 45px;
}
.quiz-details {
  height: 85vh;
}
.ready {
  font-size: 20px;
  color: #ddd;
}
.title {
  font-size: 20px;
  color: #fff;
  font-weight: 400;
  margin-bottom: 0;
  margin-top: 10px;
}
.subject {
  font-weight: normal;
  color: rgba(221, 221, 221, 0.8);
  font-size: 14px;
  margin-top: 5px;
}
.que-no {
  font-size: 14px;
  color: #fff;
}
.quiz-profile {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-bottom: 0.5rem;
  padding-top: 1rem;
}
.quiz-profile .media img {
  border: 2px solid #fff;
}
.quiz-profile .username {
  font-size: 12px;
  color: #fff;
  margin-bottom: 0;
}
.quiz-profile .score {
  font-size: 12px;
  color: #f39c12;
}
.main-timer {
  text-align: center;
}
.main-timer h4 {
  font-size: 10px;
  text-transform: uppercase;
  color: #2980b9;
  margin-bottom: 0px;
}
.main-timer p {
  font-size: 16px;
  color: #3498db;
  font-weight: 500;
}
.progress {
  position: absolute;
  top: 0;
  width: 100%;
  border-radius: 0;
  height: 0.25rem;
}
.progress .progress-bar {
  background-color: #3498db;
}
.quizes {

  position: relative;
}
.quizes .container {
  height: calc(100vh - 110px);
  overflow-y: auto;
  overflow-x: hidden;
}
.question-wrapper {
margin-top:10px;
  z-index: 999;
  background: #060029;
}
.question-wrapper.test-series {
  border-bottom-left-radius: 30px;
  border-bottom-right-radius: 30px;
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait), only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .question-wrapper.test-series {
    top: 0;
    padding-top: 15px;
    background: #282342;
    margin: 0 -15px;
    border-top: 1px solid rgba(221, 221, 221, 0.2);
    padding-bottom: 0;
  }
  .question-wrapper.test-series .que_text {
      padding: 10px;
      max-height: 200px;
      overflow-y: auto;
    }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .question-wrapper {
    top: 10px;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait) {
  .question-wrapper {
    top: 25px;
  }
}
.question-wrapper p {
  color: #fff;
  font-size: 16px;

}
.question-wrapper img {
  border-radius: 4px;
  padding: 5px 20px;
  margin-top: 1rem;
  width: auto;
  max-height: 100px;
  object-fit: contain;
}
.que-options-wrapper {
  padding: 0 10px;
}
.que-options-wrapper .que-options {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}
.que-options-wrapper .que-options .option {
  padding: 5px 0.5rem;
  width: 100%;
  min-height: 65px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 1rem;
  margin-bottom: 1rem;
  box-shadow: inset 0px 0px 5px rgba(0, 0, 0, 0.08);
  border-radius: 10px;
}
.que-options-wrapper .que-options .option span {

}
.que-options-wrapper .que-options .option span img {
  max-width: 100px;
  max-height: 100px;
}
.que-options-wrapper .que-options .option.disabled {
  pointer-events: none;
}
.que-options-wrapper .que-options .option.correct {
  background: #42B538;
}
.que-options-wrapper .que-options .option.correct.clicked {
  background: #2980b9;
}
.que-options-wrapper .que-options .option.incorrect {
  background: #FF4141;
}
.que-options-wrapper .que-options .option.incorrect.clicked {
  background: #2980b9;
}
.que-options-wrapper .que-options .option:last-child {
  margin-bottom: 1.5rem;
}
.time_line-wrapper {
  background: #E3E3E3;
  box-shadow: inset 0px 2px 4px rgba(0, 0, 0, 0.25);
  border-radius: 10px;
  height: 7px;
  margin: 1rem 2rem;
  display: none;
}
.time_line {
  background: linear-gradient(90.34deg, #E83500 -2.11%, #FFD465 110.36%);
  border-radius: 10px;
  height: 7px;
  width: 0;
}
.total_que span {
  color: #fff;
}
.total_que span p {
  color: #fff;
}
.meridian {
  height: 8px;
  width: 100%;
  background: #fff;
  border-radius: 1px;
}
.circle-wrapper {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 999;
}
.botselect {
  background: #060029;
  height: 45vh;
  display: flex;
  align-items: center;
}
.img-bot {
  background: url("file:///android_asset/quiz/assets/images/prepjoy/userstart.svg") center no-repeat;
  width: 60px;
  height: 60px;
  position: absolute;
  left: 40%;
  top: 0;
  border: 3px solid #fff;
  border-radius: 50px;
}
.bot-wrapper {
  position: relative;
}
.progress-bar-vertical {
  width: 8px;
  height: 90%;
  display: flex;
  align-items: flex-end;
  position: absolute;
  bottom: 0;
  top: initial;
  border-radius: 4px ;
  background: #060029;
}
.progress-bar-vertical.player1 {
  left: 0;
}
.progress-bar-vertical.player2 {
  right: 0;
}
.progress-bar-vertical.progress-correct {
  background: rgba(66, 181, 56, 0.5);
}
.progress-bar-vertical.progress-incorrect {
  background: rgba(218, 1, 1, 0.5);
}
.progress-bar-vertical .progress-bar {
  width: 100%;
  height: 0;
  -webkit-transition: height 0.6s ease;
  -o-transition: height 0.6s ease;
  transition: height 0.6s ease;
}
.base-timer {
  position: relative;
  width: 40px;
  height: 40px;
}
.base-timer__svg {
  transform: scaleX(-1);
}
.base-timer__circle {
  fill: none;
  stroke: none;
}
.base-timer__path-elapsed {
  stroke-width: 7px;
  stroke: grey;
}
.base-timer__path-remaining {
  stroke-width: 7px;
  stroke-linecap: round;
  transform: rotate(90deg);
  transform-origin: center;
  transition: 1s linear all;
  fill-rule: nonzero;
  stroke: currentColor;
}
.base-timer__path-remaining.green {
  color: #41b883;
}
.base-timer__path-remaining.orange {
  color: orange;
}
.base-timer__path-remaining.red {
  color: red;
}
.base-timer__label {
  position: absolute;
  width: 40px;
  height: 40px;
  top: 0;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  display: none;
}
.time-wrapper {
  width: 40px;
  height: 40px;
  position: relative;
  margin: 5px auto;
}
#countdown {
  position: absolute;
  width: 40px;
  height: 40px;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  display: flex;
  top: 0;
}
.resultWrapper,
.practice-result {
  overflow-y: auto;
  overflow-x:hidden
}
.resultWrapper .result-header h2,
.practice-result .result-header h2,
.resultWrapper .result-header p,
.practice-result .result-header p {
  color: #fff;
  padding: 16px 0;
  display: flex;
  align-items: center;
}
.resultWrapper .result-header h2,
.practice-result .result-header h2 {
  font-family: 'Righteous', cursive;
  font-size: 21px;
}
.resultWrapper .result-header h2 i,
.practice-result .result-header h2 i {
  font-size: 24px;
}
.resultWrapper .result-header p,
.practice-result .result-header p {
  font-size: 14px;
}
.resultWrapper .result-header p i,
.practice-result .result-header p i {
  font-size: 18px;
  margin-right: 5px;
}
.result {
  font-family: 'Righteous', cursive;
  font-size: 21px;
}
.prep-logo {
  width: 55px;
}
#user-status,
.user-status {
  color: #E83500;
  font-family: 'Righteous', cursive;
  font-size: 26px;
  text-align: center;
  text-transform: uppercase;
}
.score-media {
 display: flex;
 justify-content: space-around;
 align-items: center;
  color: #fff;
}
.result-points{
border:1px dashed #fff;
padding:10px;
width:100px;
border-radius:5px;
background: #060029;
}
.score-media ul{
color:#fff;
list-style:none;
font-weight:500;
margin-bottom:0;
padding-left:0;
}

.sperator-pipe{
border-right: 1px solid #fff;
width: 5px;
height: 50px;
opacity: 0.5;

}

.sperator-pipe-1{
width: 89%;
height: 1px;
background: #fff;
opacity: 0.3;
margin:28px auto 12px auto;
}

.score-media ul li span{
font-family:'Righteous', cursive !important;
}
.score-media_lists li{
width:125px;
display:flex;
}
.score-media_lists-text{
width:80px;
}
#right,
#wrong,
#skipped{
margin-left:10px;
}


.button-results__title{
font-size: 1rem;
color: #fff;
text-transform: uppercase;
margin-top:15px;
}


.button-results__label{
font-size: 10px;
opacity: 0.3;
}

.result__wrapper-card{
background: rgba(255,255,255,0.2);
padding: 1rem;
border-radius: 8px;
}
.fa-arrow-right{
margin-left:5px;
animation :rightArrow 2s cubic-bezier(0.25, 0.1, 0.25, 1) infinite
}

.score-media .updateScore {
  font-size: 20px;
  color: #fff;
  text-align: center;
  font-family: 'Righteous', cursive;
}
.score-media .score-text {
  font-size: 16px;
  color: #fff;
  text-align: center;
  font-family: 'Righteous', cursive;
}
.score-media #noofque {
  font-size: 20px;
  color: #fff;
  text-align: center;
  font-family: 'Righteous', cursive;
}
.score-media .score-wrap {
  color: #fff;
  font-size: 16px;
}
@media (min-width: 992px) {
  .container-lg {
    max-width: 800px;
    margin: 0 auto;
  }
}
.slider {
  margin-top: 3.5rem;
}
.ui-state-default,
.ui-widget-content .ui-state-default,
.ui-widget-header .ui-state-default,
.ui-button,
html .ui-button.ui-state-disabled:hover,
html .ui-button.ui-state-disabled:active {
  background: #E83500;
  border-radius: 50px;
  border: 3px solid #fff;
}
.ui-widget-content {
  background: #E3E3E3;
  box-shadow: inset 0px 2px 4px rgba(0, 0, 0, 0.25);
  border-radius: 10px;
}
.ui-slider-horizontal {
  height: 8px;
}
.ui-slider-horizontal .ui-slider-handle {
  top: -0.4em;
}
.ui-slider-range {
  background: linear-gradient(90.34deg, #FF6565 -2.11%, #FFD465 110.36%);
}
.ui-widget.ui-widget-content {
  border: none;
}
.badges div {
  margin-top: 10px;
}
.badges p {
  color: #fff;
  font-size: 10px;
}
.custom-handle p {
  color: #fff;
  font-size: 10px;
  white-space: nowrap;
  text-align: center;
  position: absolute;
  top: -35px;
  left: -40px;
  width: 100px;
}
.custom-handle p:nth-child(2) {
  margin-top: 15px;
}
.balance-points {
  background: #150F36;
  box-shadow: inset 0px 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: 10px;
  color: #fff;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 35px;
}
.btn-playagain {
  background: transparent;
  border-radius: 10px;
  font-family: 'Righteous', cursive;
  font-size: 24px;
  color: #fff;
  width: 95px;
  height: 48px;
  margin: -10px auto 10px auto;
}
.btn-play-reset {
  color: #F79420;
  border-color: #fd693c;
  box-shadow: 0px 1px 5px #eb410e;
  border: none;
  background: #fff;
}
.btn-answer {
  width: 100%;
  background: #eeeeee;
  color: #15002D
  color: #fff;
  border: 1px solid;
  font-family: 'Righteous', cursive;
  position:relative;
  z-index:999;
}

.rpBtnSub{
width: 100%;
background: #DA0101;
color: #fff !important;
font-family: 'Righteous', cursive !important;
}


.box {
  width: 20px;
  height: 20px;
  background: #fff;
}
.box.red {
  background: #E83500;
}
.box.green {
  background: #42B538;
}
.answer-indicator {
  display: flex;
  align-items: center;
  justify-content: space-around;
  border: 1px dashed rgba(255, 255, 255, 0.5);
  width: 100%;
  height: 48px;
  margin-top: 2rem;
  border-radius: 4px;
}
.answer-indicator > div {
  display: flex;
  align-items: center;
  justify-content: center;
}
.answer-indicator > div p {
  color: #fff;
  margin-left: 10px;
  font-size: 10px;
}
.share-indicator {
  display: flex;
  align-items: center;
  justify-content: space-around;
  border: 1px dashed rgba(255, 255, 255, 0.5);
  width: 100%;
  height: 48px;
  margin-top: 2rem;
  border-radius: 4px;
  height: auto;
  padding: 10px;
}
.share-indicator > div {
  display: flex;
  align-items: center;
  justify-content: center;
}
.share-indicator > div p {
  color: #fff;
  margin-left: 10px;
  font-size: 10px;
}
.share-indicator img {
  height: 40px;
  width: 40px;
  margin-right: 10px;
}
.share-indicator p {
  font-size: 14px;
}
.share-indicator div {
  display: block;
}
.share-indicator div p {
  font-size: 12px;
}
.share-indicator button {
  background: #2980b9;
  font-size: 14px;
  display: flex;
  align-items: center;
  margin-left: 10px;
  margin-top: 4px;
  color: #fff;
}
.share-indicator button i {
  font-size: 16px;
  margin-right: 5px;
}
#qa-answer {
  margin: 2rem -14px;
}
.mcq-answers {
  margin: 0 auto;
  padding: 2rem 24px;
  background: #150f36;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
}
.mcq-answers #que-no {
  font-weight: bold;
  font-size: 18px;
  color: #fff;
  text-align: center;
  margin-bottom: 1rem;
}
.mcq-answers .question-wrapper {
  background: none;
  position: static;
}
.mcq-answers .que-options-wrapper .que-options .option:last-child {
  margin-bottom: 0;
}
.answer-wrapper {
  display: none;
}
.answer-wrapper .box {
  margin-right: 10px;
  width: 15px;
  height: 15px;
  margin-top: 5px;
}
.share-wrapper {
  display: none;
  display: block;
}
.share-wrapper .box {
  margin-right: 10px;
  width: 15px;
  height: 15px;
  margin-top: 5px;
}
.option-qa {
  display: flex;
  align-items: center;
}
.option-qa .option span p {
  display: flex;
  flex-direction: column;
}
.badge {
  height: 70px;
  width: 70px;
  margin: 15px auto;
  margin-bottom: 0;
}
.badge.hunter {
  background: url("file:///android_asset/quiz/assets/images/prepjoy/hunter.svg") center no-repeat;
  background-size: contain;
}
.badge.warrior {
  background: url("file:///android_asset/quiz/assets/images/prepjoy/warrior.svg") center no-repeat;
  background-size: contain;
}
.badge.knight {
  background: url("file:///android_asset/quiz/assets/images/prepjoy/knight.svg") center no-repeat;
  background-size: contain;
}
.badge.ninja {
  background: url("file:///android_asset/quiz/assets/images/prepjoy/ninja.svg") center no-repeat;
  background-size: contain;
}
.badge.commander {
  background: url("file:///android_asset/quiz/assets/images/prepjoy/gladiator.svg") center no-repeat;
  background-size: contain;
}
.badge.gladiator {
  background: url("file:///android_asset/quiz/assets/images/prepjoy/gladiator.svg") center no-repeat;
  background-size: contain;
}
.badge.samurai {
  background: url("file:///android_asset/quiz/assets/images/prepjoy/supreme.svg") center no-repeat;
  background-size: contain;
}
.badge.ultimate {
  background: url("file:///android_asset/quiz/assets/images/prepjoy/ultimate.svg") center no-repeat;
  background-size: contain;
}
.badge-screen {
  margin-bottom: 1rem;
  display: none;
}
.badge-screen #congrats {
  color: #E83500;
  font-family: 'Righteous', cursive;
  font-size: 26px;
  text-transform: uppercase;
  text-align: center;
}
.badge-screen p {
  color: #fff;
  text-align: center;
  font-size: 14px;
}
#lottie {
  position: absolute;
  top: 0;
  left: 0;
}
.modal.fade .modal-dialog.modal-dialog-zoom {
  -webkit-transform: translate(0, 0) scale(0.5);
  transform: translate(0, 0) scale(0.5);
}
.modal.show .modal-dialog.modal-dialog-zoom {
  -webkit-transform: translate(0, 0) scale(1);
  transform: translate(0, 0) scale(1);
}
#winnerModal {
  overflow-y: hidden;
  height:auto;
}
#winnerModal .modal-content {
  background: #000;
}

#winnerModal .modal-content .modal-header {
  border: none;
}
#winnerModal .modal-content .modal-footer {
  border: none;
}
#winnerModal .user-status {
  color: #42B538;
}
#winnerModal .user-status.red {
  color: #E83500;
}
#winner {
  height: 100px;
}
.question-no {
  color: #2980b9;
  margin: 0.5rem 0;
  font-family: 'Righteous', cursive;
  font-size: 16px;
  text-align: center;
  font-weight: normal;
}
.medal-user {
  width: 200px;
  height: 110px;
  margin: 20px auto;
}
.medal-user.gold {
  background: url("file:///android_asset/quiz/assets/images/prepjoy/goldmedal.svg") center no-repeat;
  background-size: contain;
}
.medal-user.silver {
  background: url("file:///android_asset/quiz/assets/images/prepjoy/silvermedal.svg") center no-repeat;
  background-size: contain;
}
.medal-user.bronze {
  background: url("file:///android_asset/quiz/assets/images/prepjoy/bronzemedal.svg") center no-repeat;
  background-size: contain;
}
.medal-wrappers {
  text-align: center;
  background: #000;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.medal-wrappers h1 {
  color: #e83500;
  font-size: 28px;
  font-family: 'Righteous', cursive;
  padding-top: 15px;
}
.medal-wrappers p {
  font-size: 18px;
  color: #fff;
}
#medal-name {
  font-size: 18px;
  color: #fff;
}
#medalModal .modal-footer {
  border: none;
}
#medalModal .modal-dialog {
  height: 100%;
  margin: 0;
  border-radius: 0;
}
#medalModal .modal-content {
  border-radius: 0;
  border: none;
  height: 100%;
}
#rankModal .modal-footer {
  border: none;
}
#rankModal .modal-dialog {
  height: 100%;
  margin: 0;
  border-radius: 0;
}
#rankModal .modal-content {
  border-radius: 0;
  border: none;
  height: 100%;
}
#medal-upload {
  position: absolute;
  left: 0;
  top: 0;
}
@media only screen and (min-width: 769px) {
  .circle-wrapper {
    width: 47%;
  }
}
.locate {
  color: #fff;
  font-size: 18px;
  margin-bottom: 1rem;
}
.no-counter {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}
#no-counter {
  color: #fff;
}
#readyGo {
  position: relative;
}
@-webkit-keyframes count {
  0% {
    transform: scale(1.5);
  }
  100% {
    transform: scale(1);
  }
}
.nums {
  font-size: 5rem;
  height: auto;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  text-align: center;
}
.three {
  -webkit-animation: count 0.1s cubic-bezier(0.1, 0.1, 1, 1) 1;
  animation: count 0.1s cubic-bezier(0.1, 0.1, 1, 1) 1;
}
.two {
  -webkit-animation: count 0.1s cubic-bezier(0.1, 0.1, 1, 1) 1;
  animation: count 0.1s cubic-bezier(0.1, 0.1, 1, 1) 1;
}
.one {
  -webkit-animation: count 0.1s cubic-bezier(0.1, 0.1, 1, 1) 1;
  animation: count 0.1s cubic-bezier(0.1, 0.1, 1, 1) 1;
}
.quiz-profile .playerName {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 60px;
}
.audioChange {
  color: #fff;
  margin-right: 10px;
  position: relative;
  height: 25px;
  width: 20px;
  margin-left: auto;
}
.audioChange i {
  font-size: 15px;
  background: #E83500;
  border-radius: 50px;
  width: 25px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: right;
  position: absolute;
  right: 0;
}
.button-results {
  text-align: center;
}
.app-header {
  background: #201a40;
  padding: 0.5rem 1rem;
  box-shadow: 0 2px 5px #010102;
}
.app-header > div {
  padding: 0.5rem;
}
.app-header i {
  color: #fff;
}
.app-header h4 {
  color: #fff;
  font-size: 16px;
}
.app-footer {
  position: sticky;
  position: -webkit-sticky;
  bottom: 0;
  background: #201a40;
  padding: 0.5rem 2rem;
  width: 100%;
  box-shadow: 0px 0 2px #010102;
}
.app-footer > div {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.app-footer button {
  background: #060029;
  border: none;
  color: #fff;
  font-size: 16px;
  height: 40px;
  width: auto;
  border: 1px solid #010102;
  border-radius: 8px;
}
.app-footer button.next-btn {
  background: #0AAEF9;
}
.clock-wrapper,
.lang-wrapper {
  margin: 0 -1.5rem;
  width: auto !important;
}
.explanation-wrapper,
.clock-wrapper,
.lang-wrapper,
.action-wrapper {
  display: none;
  background: #201a40;
  min-height: 300px;
  position: sticky;
  bottom: 0;
  width: 100%;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
}
.explanation-wrapper .clock-explanation,
.clock-wrapper .clock-explanation,
.lang-wrapper .clock-explanation,
.action-wrapper .clock-explanation,
.explanation-wrapper .lang-explanation,
.clock-wrapper .lang-explanation,
.lang-wrapper .lang-explanation,
.action-wrapper .lang-explanation {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-evenly;
}
.explanation-wrapper .answer-explanation,
.clock-wrapper .answer-explanation,
.lang-wrapper .answer-explanation,
.action-wrapper .answer-explanation,
.explanation-wrapper .clock-explanation,
.clock-wrapper .clock-explanation,
.lang-wrapper .clock-explanation,
.action-wrapper .clock-explanation,
.explanation-wrapper .lang-explanation,
.clock-wrapper .lang-explanation,
.lang-wrapper .lang-explanation,
.action-wrapper .lang-explanation,
.explanation-wrapper .action-explanation,
.clock-wrapper .action-explanation,
.lang-wrapper .action-explanation, {
  padding: 0.5rem;
  max-height: 100vh;
  overflow-y: auto;
  padding-bottom: 6rem;
}
.explanation-wrapper .answer-explanation h4,
.clock-wrapper .answer-explanation h4,
.lang-wrapper .answer-explanation h4,
.action-wrapper .answer-explanation h4,
.explanation-wrapper .clock-explanation h4,
.clock-wrapper .clock-explanation h4,
.lang-wrapper .clock-explanation h4,
.action-wrapper .clock-explanation h4,
.explanation-wrapper .lang-explanation h4,
.clock-wrapper .lang-explanation h4,
.lang-wrapper .lang-explanation h4,
.action-wrapper .lang-explanation h4,
.explanation-wrapper .action-explanation h4,
.clock-wrapper .action-explanation h4,
.lang-wrapper .action-explanation h4,
.action-wrapper .action-explanation h4 {
  font-size: 14px;
  color: #fff;
}
.explanation-wrapper .answer-explanation #explanation,
.clock-wrapper .answer-explanation #explanation,
.lang-wrapper .answer-explanation #explanation,
.action-wrapper .answer-explanation #explanation,
.explanation-wrapper .clock-explanation #explanation,
.clock-wrapper .clock-explanation #explanation,
.lang-wrapper .clock-explanation #explanation,
.action-wrapper .clock-explanation #explanation,
.explanation-wrapper .lang-explanation #explanation,
.clock-wrapper .lang-explanation #explanation,
.lang-wrapper .lang-explanation #explanation,
.action-wrapper .lang-explanation #explanation,
.explanation-wrapper .action-explanation #explanation,
.clock-wrapper .action-explanation #explanation,
.lang-wrapper .action-explanation #explanation,
.action-wrapper .action-explanation #explanation {
  color: #fff;
  padding-bottom: 10px;
  font-size: 14px;
}
#explanation img{
    border-radius: 4px;
    padding: 5px 20px;
    margin-top: 1rem;
    width: auto;
    max-height: 100px;
    object-fit: contain;
    }
.explanation-wrapper .explanation-header,
.clock-wrapper .explanation-header,
.lang-wrapper .explanation-header,
.action-wrapper .explanation-header,
.explanation-wrapper .clock-header,
.clock-wrapper .clock-header,
.lang-wrapper .clock-header,
.action-wrapper .clock-header,
.explanation-wrapper .lang-header,
.clock-wrapper .lang-header,
.lang-wrapper .lang-header,
.action-wrapper .lang-header,
.explanation-wrapper .action-header,
.clock-wrapper .action-header,
.lang-wrapper .action-header,
.action-wrapper .action-header {
  background: #213247;
  min-height: 80px;
  padding: 1rem;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  box-shadow: 0 -10px 10px -10px #201a40;
}
.explanation-wrapper .explanation-header h4,
.clock-wrapper .explanation-header h4,
.lang-wrapper .explanation-header h4,
.action-wrapper .explanation-header h4,
.explanation-wrapper .clock-header h4,
.clock-wrapper .clock-header h4,
.lang-wrapper .clock-header h4,
.action-wrapper .clock-header h4,
.explanation-wrapper .lang-header h4,
.clock-wrapper .lang-header h4,
.lang-wrapper .lang-header h4,
.action-wrapper .lang-header h4,
.explanation-wrapper .action-header h4,
.clock-wrapper .action-header h4,
.lang-wrapper .action-header h4,
.action-wrapper .action-header h4 {
  color: #42B538;
  font-size: 16px;
}
.explanation-wrapper .explanation-header i,
.clock-wrapper .explanation-header i,
.lang-wrapper .explanation-header i,
.action-wrapper .explanation-header i,
.explanation-wrapper .clock-header i,
.clock-wrapper .clock-header i,
.lang-wrapper .clock-header i,
.action-wrapper .clock-header i,
.explanation-wrapper .lang-header i,
.clock-wrapper .lang-header i,
.lang-wrapper .lang-header i,
.action-wrapper .lang-header i,
.explanation-wrapper .action-header i,
.clock-wrapper .action-header i,
.lang-wrapper .action-header i,
.action-wrapper .action-header i {
  color: #fff;
  background: #201a40;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: CENTER;
  border-radius: 4px;
  font-size: 20px;
}
.explanation-wrapper .explanation-header p,
.clock-wrapper .explanation-header p,
.lang-wrapper .explanation-header p,
.action-wrapper .explanation-header p,
.explanation-wrapper .clock-header p,
.clock-wrapper .clock-header p,
.lang-wrapper .clock-header p,
.action-wrapper .clock-header p,
.explanation-wrapper .lang-header p,
.clock-wrapper .lang-header p,
.lang-wrapper .lang-header p,
.action-wrapper .lang-header p,
.explanation-wrapper .action-header p,
.clock-wrapper .action-header p,
.lang-wrapper .action-header p,
.action-wrapper .action-header p {
  color: #fff;
}
.show-explanation {
  background: #e1b12c;
  color: #fff;
  font-size: 14px;
  display: none;
}
.preview-quiz {
  height: 100%;
  background: #201a40;
  display: none;
  border-top-left-radius: 50px;
  border-top-right-radius: 50px;
  margin-top: 5px;
}
.preview-quiz h4 {
  color: #fff;
  display: flex;
  align-items: center;
  font-size: 18px;
}
.preview-quiz h4 i {
  font-size: 20px;
  margin-right: 10px;
}
.preview-quiz i {
  font-size: 20px;
  color: #fff;
}
.num-wrapper {
  background: #150F36;
  width: 100%;
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 10px;
}
.num-wrapper .num-text {
  background: #302a54;
  border: 1px solid #5a4848;
  font-size: 14px;
  height: 30px;
  width: 30px;
  text-align: center;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.num-wrapper .num-text i {
  position: absolute;
  right: -10px;
  top: -5px;
  color: #e1b12c;
}
.num-wrapper div {
  color: #fff;
}
#total-que-tab {
  padding: 1rem;
  height: calc(100vh - 78px);
  overflow-y: auto;
}

.result__header-wrapper{
position: sticky;
top: 0;
z-index: 999;
background:#060029;
}

.practice-result {
  overflow-x: hidden;
}
.practice-result .nav-tabs {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
  margin-left: -15px;
  margin-right: -15px;
  border-bottom: none;
  /*position: sticky;
  top: 0;
  z-index: 999; */
  box-shadow: 0 0 10px #999;
}
.practice-result .nav-tabs li {
  width: 25%;
}

.nav-tabs li a {
  color: #fff;
  display: block;
  width: 100%;
  text-align: center;
  padding: 10px 0;
  text-decoration: none;
}
 .nav-tabs li a.active {
  color: #fff;
  border-bottom: 3px solid #E83500;
}
.practice-result .validate-answers {
  text-align: center;
  margin-top: 4rem;
  color: #E83500;
  font-size: 16px;
}
.practice-result .tab-content {
  margin-left: -15px;
  margin-right: -15px;
}
.test-summary {
  padding: 10px;
}
.test-summary i {
  color: #fff;
  font-size: 20px;
  margin-right: 12px;
}
.test-summary h4 {
  color: #fff;
  font-size: 15px;
  margin: 0;
}
.test-summary p {
  color: #fff;
  font-size: 14px;
}
.box-button {
  margin-top: 1.5rem;
  border-radius: 4px;
  padding: 5px 8px;
}
.box-button.green {
  background: #05c46b;
  position: relative;
}
.box-button.green::after {
  content: '';
  color: #000;
  position: absolute;
  right: 0;
  top: 0;
  width: 32px;
  height: 32px;
  border-radius: 50%;
}
.box-button.red {
  background: #cb170c;
}
.box-button.yellow {
  background: #ffa801;
}
.main-page {
  display: none;
  flex-direction:column;
  min-height: 100vh;
  overflow-y: auto;
  background: #000;
}
.main-page h1,
.main-page h2,
.main-page h3,
.main-page h4,
.main-page p {
  color: #fff;
  font-size: 16px;
}
.main-page h1 span,
.main-page h2 span,
.main-page h3 span,
.main-page h4 span,
.main-page p span {
  font-size: 14px;
}
.main-page p.total-que {
  margin-top: 10px;
}
.main-page i {
  font-size: 24px;
  color: #fff;
}
.main-page .mcq-type {
  background: #201a40;
  padding: 5px 10px;
  border-radius: 4px;
  box-shadow: 0 0 0 2px #423e3e;
}
.main-page .mcq-name {
  font-size: 18px;
  color: #fff;
  font-weight: normal;
}
.main-page .set-box {
  width: 60px;
  height: 50px;
  background: rgba(242, 223, 223, 0.25);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  backdrop-filter: blur(13px);
  -webkit-backdrop-filter: blur(13px);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}
.instruction-wrapper {
  margin: 1.5rem -1.5rem;
  padding: 1.5rem;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
.instruction-wrapper .h4 {
  color: #fff;
  font-size: 15px;
}
.custom-check {
  width: 100px;
  height: 75px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 16px;
  background: #201a40;
  padding: 5px 10px;
  border-radius: 4px;
  box-shadow: 0 0 0 2px #423e3e;
}
input[type=checkbox]:checked + label {
  border: 1px solid #2980b9 !important;
}
input[type=radio]:checked + label {
  border: 1px solid #2980b9 !important;
  background: #2980b9 !important;
}
.btn-practices {
  background: #E83500;
  padding: 5px 10px;
  border-radius: 4px;
  box-shadow: 0 0 0 2px #423e3e;
  color: #fff;
  font-size: 16px;
  text-transform: uppercase;
  width: 200px;
  border: none;
  position:fixed;
  bottom:20px;
}
.customize-header select {
  width: 150px;
}
.customize-header label {
  color: #fff;
  font-size: 16px;
  margin-bottom: 0;
}
.loader-submit {
  position: absolute;
  top: 50%;
  right: 50%;
  transform: translate(50%, -50%);
  display: none;
  width: 200px;
}
.btn-review {
  display: none;
  background: #201a40;
  color: #fff;
  width: 100%;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
  border-bottom-left-radius: 30px;
  border-bottom-right-radius: 30px;
  font-size: 16px;
}
.btn-review.marked {
  color: #e1b12c;
}
.btn-review i {
  margin-right: 10px;
  font-size: 20px;
}
.btn-review:focus {
  outline: none !important;
  box-shadow: 0 0 0 0 rgba(0, 123, 255, 0) !important;
}
.reporticon {
  text-decoration: none;
}
.reporticon:focus {
  text-decoration: none;
}
#prep-report-question .prep-report-modal .chkbox {
  display: flex;
  align-items: center;
}
#prep-report-question .prep-report-modal label {
  display: flex;
  align-items: center;
  color: #000;
  cursor: pointer;
  position: relative;
}
#prep-report-question .prep-report-modal label span {
  display: inline-block;
  position: relative;
  background-color: transparent;
  width: 15px;
  height: 15px;
  transform-origin: center;
  border: 2px solid #fff;
  border-radius: 50%;
  vertical-align: -6px;
  margin-right: 10px;
  transition: background-color 150ms 200ms, transform 350ms cubic-bezier(0.78, -1.22, 0.17, 1.89);
}
#prep-report-question .prep-report-modal label span:before {
  content: "";
  width: 0px;
  height: 2px;
  border-radius: 2px;
  background: #000;
  position: absolute;
  transform: rotate(45deg);
  top: 6px;
  left: 2px;
  transition: width 50ms ease 50ms;
  transform-origin: 0% 0%;
}
#prep-report-question .prep-report-modal label span:after {
  content: "";
  width: 0px;
  height: 2px;
  border-radius: 2px;
  background: #fff;
  position: absolute;
  transform: rotate(305deg);
  top: 9px;
  left: 3px;
  transition: width 50ms ease;
  transform-origin: 0% 0%;
}
#prep-report-question .prep-report-modal .form-check-label {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  background: #201a40;
  padding: 5px 10px;
  border-radius: 4px;
  box-shadow: 0 0 0 2px #423e3e;
  margin-top: 1rem;
}
#prep-report-question .prep-report-modal input[type="checkbox"] {
  visibility: hidden;
}
#prep-report-question .prep-report-modal input[type="checkbox"]:checked + label {
  color: #DA0101;
}
#prep-report-question .prep-report-modal input[type="checkbox"]:checked + label span {
  background-color: #DA0101;
  transform: scale(1.05);
  border: 2px solid #DA0101;
}
#prep-report-question .prep-report-modal input[type="checkbox"]:checked + label span:after {
  width: 10px;
  background: #fff;
  transition: width 150ms ease 100ms;
}
#prep-report-question .prep-report-modal input[type="checkbox"]:checked + label span:before {
  width: 5px;
  background: #fff;
  transition: width 150ms ease 100ms;
}
#prep-report-question .prep-report-modal label,
#prep-report-question .prep-report-modal input,
#prep-report-question .prep-report-modal p,
#prep-report-question .prep-report-modal textarea {
  font-family: 'Righteous', cursive !important;
  font-size: 14px;
  color: #000;
  font-weight: lighter !important;
}
#prep-report-question .prep-report-modal .prep-report-icon i {
  font-size: 18px;
  color: #E83500;
}
#prep-report-question .prep-report-modal .form-control:focus {
  border: 1px solid #999;
  box-shadow: none;
}
.action-explanation .btn-playagain {
  width: 80%;
  background: #334356;
}
.close-buttons {
  text-align: right;
  color: #fff;
  float: right;
  width: 100%;
  padding: 10px;
  margin-bottom:18px;
}
.action-wrapper {
  z-index: 999;
}
.show-hide-text {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}
.show-hide-text a {
  text-transform: uppercase;
  order: 2;
  font-size: 14px;
}
.show-hide-text p {
  position: relative;
  overflow: hidden;
  max-height: 60px;
  line-height: 20px;
}
.show-hide-text p:after {
  content: "";
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100px;
  height: 20px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, white 100%);
}
@supports (-webkit-line-clamp: 3) {
  .show-hide-text p {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }
  .show-hide-text p:after {
    display: none;
  }
}
.show-less {
  display: none;
}
.show-less:target {
  display: block;
}
.show-less:target ~ p {
  display: block;
  max-height: 100%;
}
.show-less:target + a {
  display: none;
}
.wrapper p {
  font-size: 16px;
  line-height: 20px;
}
.morecontent span {
  display: none;
}
.morelink {
  display: block;
  font-size: 16px;
  text-decoration: underline;
}
.explanation-text{
color:#fff;
}
.tab-content table td{
color:#fff;
}
.explanation-wrappers{
color:#fff;
}
.head-text{
color:#fff;
font-size:16px;
}

#direction{
  font-weight: bold;
  font-size: 14px;
}

.explanation-text img{
max-width:320px;
}

.box{
width:45px;
height:45px;
display:flex;
align-items:center;
justify-content:center;
border-radius:4px;
margin:0 auto;
}
.box p{
color:#fff;
}
.box.yellow{
background:#e1b12c;
}
.box.green{
background:#27ae60;
}
.box.blue{
background:#8e44ad;
}
.text-quote{

    font-size: 10px;
    font-weight: bold;
    margin-top: 8px;
}
.sub-text{
font-size: 12px;
    color:#060029;
     font-weight: bold;

}
.goto-que{
font-size: 12px;
    background: #2980b9;
    color: #fff;
    display: flex;
    width: 150px;
    justify-content: space-around;
    margin: 5px auto;
}
.goto-que i{
font-size:16px;
}
#review-modal .modal-header h2{
font-size:16px;
color:#E83500;
margin-bottom:0;
}
#review-modal .modal-header{
justify-content:center;
}
#review-modal .modal-content{
min-height:250px;
}
#review-modal .btn-submit{
background:#DA0101;
color:#fff;
}

#review-modal button{
font-size:12px;
}
mjx-math{
white-space: pre-wrap;
}
.MJX-TEX{
white-space:pre-wrap!important
}
.option-qa .option span p{
display:block !important;
}
.que_text p{
overflow-x:scroll;
}

#qa-answer .question-wrapper p{
overflow-x:scroll;
}

.explanation-wrappers img{
width:100%;
object-fit:contain;
}

.explanation-wrappers {
max-width:300px;
}

.nav-tabs {
    display: grid;
    grid-template-columns: repeat(5,1fr);
    grid-gap: 7px;
    padding: 10px 0 0 0;
    overflow-x: scroll;
    margin-top: 2rem;
    border-bottom: none;
    position: sticky;
    top: 0;
    z-index: 999;
    box-shadow: 0 0 10px #999;
    box-shadow: 0 2px 2px rgb(255 255 255 / 20%);
}
.nav-tabs li {
    width: 100px;
}

.nav-tabs li a {
    display: block;
    width: 100%;
    text-align: center;
    padding: 10px 0;
    text-decoration: none;
    border-right: 0.3px solid #000;
    border-radius:2px;
}


.validate-answers {
    text-align: center;
    margin-top: 4rem;
    color: #E83500;
    font-size: 16px;
}
.tab-content {
    margin-left: -15px;
    margin-right: -15px;
}

.back__btn{
background:transparent;
border:none;
color:#fff;
width:40px;
}

.action-wrapper{
margin-top:260px;
padding-bottom:1rem;
min-height:260px !important;
}
.bottom__sheet-buttons{
border-radius:5px !important;
background:#454060;
font-size:16px;
border:1px solid #fff;
text-align:left;
display:flex;
align-items:center;
}

.bottom__sheet-buttons-img{
width:28px;
height:28px;
margin-right:10px;
}

.result__header-wrapper button{
margin-left:5px;
}
.result__header-wrapper div p{
margin-left:-6px;
}
button:focus{
outline:none!important;
}
/*
.explanation-wrappers p img{
width:100%;
object-fit:contain;
}*/
mjx-mtable{
width:fit-content !important;
}

table{
width:fit-content !important;
}

@keyframes rightArrow{
0%{
transform:translateX(0);
}
50%{
transform:translateX(5px);
}
100%{
transform:translateX(0);
}
}

/* NEW INSTRUCTION PAGE utilities*/
.inital-btn{
    height: 50%;
    display: flex;
    justify-content: center;
}
.inital-btn button{
margin-top:auto;
}
/* NEW INSTRUCTION PAGE */
.quiz__rangeSelection{
   margin-top:1.2rem;
}
.quiz__rangeSelection-buttons{
    display:flex;
    justify-content:space-between;
    margin-bottom:1rem;
}
.quiz__rangeSelection-button{
    color:#fff;
    border:1px solid;
    border-radius:5px;
    padding:5px 18px;
    background:transparent;
    transition:background 0.3s ease;
}
.quiz__rangeSelection-button.activeRange{
    background:#E83500 !important;
    border-color:#E83500 !important;
    transition:all 0.3s ease;
}

.quiz__rangeSelection-rangeInputs input{
    padding: 3px 10px;
    border: 1px solid rgba(255,255,255,0.7);
    background: transparent;
    border-radius: 3px;
    margin-top: 5px;
    outline: none;
    color:#000;
}
.rangeInputs__errorMsg{
    color:#E83500 !important;
}
.questionRangeFromToInput{
    width:140px;
}
.questionRangeFromToInput:disabled{
    border: 1px solid rgba(255,255,255,0.2) !important;
}
.quiz__rangeSelection-rangeInputs input:disabled{
    border: 1px solid rgba(255,255,255,0.2) !important;
}

.main-page__header{
    background: #060029;
    height: 180px;
}

.main-page__content{
    height: 100vh;
    padding: 1.5rem 1rem;
    border-top-left-radius: 30px;
    border-top-right-radius: 30px;
    background: #201a40;
    margin-top: -36px;
}

.main-page__content-header{
    padding: 10px 0 10px 0;
}

.main-page__content-header h4{
    display: flex;
    align-items: center;
    text-align:center;
}

.main-page__content-header p{
font-size:12px;
}

.main-page__content-separator{
    height: 1px;
    background: rgba(255,255,255,0.1);
    width: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    margin: 1rem auto 1rem auto;
    border-radius:100px;
}

.customize-header select{
    font-size:12px;
    padding-left:0.3rem;
    padding-right:0.3rem;
}

.infoModalCloseBtn {
    justify-content:center;
}

.infoModalCloseBtn  button{
    width: 200px;
    background: #E83500;
    color: #fff;
}

.questionInfoModal__lists{
    padding-left:10px;
}

.questionInfoModal__lists li{
    font-size:16px;
    margin-bottom:10px;
}

.questionInfoModal__lists li::marker{
    color:red;
    background:red;
}

.main-page__content-points{
    display:flex;
    justify-content:space-between;
    align-items:center;
}
.points_card p{
    font-size:12px;
}
.points_card .points_card-des{
    color:rgba(255,255,255,0.5);
}
.points_card{
    padding: 10px;
    width: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.separator-1{
    position:relative;
}
.separator-1::after{
    position: absolute;
    content: '';
    width: 1px;
    background: rgba(255,255,255,0.1);
    height: 30px;
    top: 10px;
    right: 0;
}
.main-page__content-questionsRange__language{
    margin-bottom:20px;
    margin-left:25px;
}
.main-page__content-questionsRange__range{
    margin-left:25px;
}
.main-page__content-questionsRange__language,
.main-page__content-questionsRange__range{
    font-size: 14px;
}

.main-page__content-questionsRange__language input[type=radio],
.main-page__content-questionsRange__range input[type=radio]{
    appearance: none;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    border: 2px solid #fff;
    transition: 0.1s all ease;
    margin-right: 5px;
    position: relative;
    top: 4px;
    color:#fff !important;
}
.main-page__content-questionsRange__language label,
.main-page__content-questionsRange__range label{
    color:#fff;
    margin-bottom:0;
    transition: 0.1s all ease;
}
.main-page__content-questionsRange__language input[type=radio]:checked,
.main-page__content-questionsRange__range input[type=radio]:checked{
    border: 4px solid #E83500 !important;
}

.main-page__content-questionsRange__language input[type=radio]:checked + label,
.main-page__content-questionsRange__range input[type=radio]:checked + label{
    border:none!important;
    background:transparent !important;
    color:#E83500 !important;
}
.main-page__content-questionsRange__language p,
.main-page__content-questionsRange__range p{
    width:130px;
    font-size:14px;
}

.main-page__sectionDetails{
overflow:scroll;
}
.main-page__sectionDetails .table{
color:#fff;
}
.section-selection{
width:150px;
}
.sectiontime-wrapper{
width:80px;
}
.display-remain-time{
color:#fff;
}
.quizTypeName{
margin-bottom:0!important;
}

#sectionSelect:disabled{
opacity: 0.9 !important;
background: rgba(255, 255, 255, 0.9);
}
#sectionSelectDropDown{
min-width:auto;
max-width:160px;
}
#wsResultPointsCount,
#wsSectionResultPointsCount{
color:#fff;
text-align:center;
}

.gptheader{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.gptHeaderWrap{
    height:50%;
}