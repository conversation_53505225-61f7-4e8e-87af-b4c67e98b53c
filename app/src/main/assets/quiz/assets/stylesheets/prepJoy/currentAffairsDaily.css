.loading-icon {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  background: rgba(68, 68, 68, 0.9);
  z-index: 9999;
  overflow: hidden;
}
.load-wrapper {
  position: absolute;
  top: 35%;
  width: 100%;
}
.loader-wrapper {
  width: 139px;
  height: 65px;
  background-color: #FFFFFF;
  position: relative;
  top: 50% !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.25);
  margin: 0 auto;
  border-radius: 4px;
}
.loader,
.loader:before,
.loader:after {
  border-radius: 50%;
  width: 2.5em;
  height: 2.5em;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation: load7 1.8s infinite ease-in-out;
  animation: load7 1.8s infinite ease-in-out;
}
.loader {
  color: #F05A2A;
  font-size: 9px;
  margin: 0 auto;
  position: relative;
  text-indent: -9999em;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}
.loader:before,
.loader:after {
  content: '';
  position: absolute;
  top: 0;
}
.loader:before {
  left: -3.5em;
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}
.loader:after {
  left: 3.5em;
}
@-webkit-keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}
@keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}
.button-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}
.slider {
  max-width: 1110px;
  margin: 0 auto;
}
.slick-list {
  margin: 0 -20px;
}
.slick-list.draggable {
  padding: 0 !important;
}
.slick-track {
  padding-top: 53px;
  padding-bottom: 53px;
}
.validation-msg {
  text-align: center;
}
.validation-msg h4 {
  color: #7F28A8;
  font-weight: 500;
  font-style: italic;
}
.validation-msg .btn-primary {
  border-radius: 10px;
  margin-top: 2%;
  border: 1px solid #9A309B !important;
  color: #9A309B !important;
  background: transparent !important;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
}
.slider-card {
  /*border:1px solid black;*/
  /*background: white;*/
  height: 170px;
}
.date-present h5 {
  font-family: Righteous;
  font-size: 36px;
  font-style: normal;
  font-weight: 400;
  line-height: 45px;
  letter-spacing: 0em;
  text-align: center;
  color: #e83500;
}
.slider-card.slick-slide {
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0.1;
  border-radius: 10px;
}
.slick-slide {
  text-align: center;
  transition: transform 0.3s ease-in-out;
}
.slick-slide.slick-current {
  transform: scale(1.35);
  position: relative;
  z-index: 1;
  opacity: 1;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
}
.mcqRead .button-wrapper {
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding-top: 15px;
  cursor: pointer;
  padding-bottom: 15px;
}
.col-10.first-block span {
  font-family: Poppins;
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: 30px;
  letter-spacing: 0em;
  text-align: left;
  color: #9A309B;
}
.col-10.first-block p {
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 21px;
  letter-spacing: 0em;
  text-align: left;
  color: black;
}
.row.heading .col-12 p {
  display: inline-block;
  font-family: Poppins;
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: 36px;
  letter-spacing: 0em;
  color: #9A309B;
}
.row.heading {
  padding: 2% 0.5%;
}
.slider-parent {
  margin-top: 3%;
}
.slider-parent p,
.readingMaterialParent p:first-child {
  font-family: Poppins;
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: 30px;
  letter-spacing: 0em;
  text-align: left;
  color: #9A309B;
  margin-bottom: 1%;
}
@media (max-width: 525px) {
  .row.heading .col-12 p {
    font-size: 14px;
  }
}
.col-2.second-block img {
  float: right !important;
}
.video-card {
  width: 100%;
  height: 100%;
}
/*youtube button css*/
.play {
  background: red;
  border-radius: 50% / 10%;
  color: #FFFFFF;
  font-size: 1em;
  /* change this to change size */
  height: 3em;
  /*margin: 20px auto;*/
  padding: 0;
  position: relative;
  text-align: center;
  text-indent: 0.1em;
  transition: all 150ms ease-out;
  width: 4em;
  cursor: pointer;
}
.slick-next:before,
.slick-prev:before {
  display: none !important;
}
button.slick-prev.slick-arrow,
.slick-next {
  width: 48px;
  height: 48px !important;
  border-radius: 50%;
  z-index: 100;
}
button.slick-prev.slick-arrow img {
  -webkit-transform: scaleX(-1);
  transform: scaleX(-1);
}
button.slick-prev.slick-arrow {
  background: white !important;
}
button.slick-next.slick-arrow {
  background: white !important;
}
.play:hover {
  background: darkorange;
}
.slick-next:before,
.slick-prev:before {
  display: none !important;
}
.play::before {
  background: inherit;
  border-radius: 5% / 50%;
  bottom: 9%;
  content: "";
  left: -5%;
  position: absolute;
  right: -5%;
  top: 9%;
}
.play-parent {
  position: absolute;
  top: 40%;
  margin-left: 41%;
}
@media (max-width: 575px) {
  .play-parent .play {
    width: 24px !important;
    height: 2rem;
  }
  .play::after {
    font-size: 4px !important;
  }
}
.slick-slide img {
  border-radius: 10px;
}
.slider-card.slick-slide .play-parent {
  display: none;
}
.slider-card.slick-slide.slick-current .play-parent {
  display: block;
}
.play::after {
  border-style: solid;
  border-width: 1em 0 1em 1.732em;
  border-color: transparent transparent transparent rgba(255, 255, 255, 0.75);
  content: ' ';
  font-size: 0.75em;
  height: 0;
  margin: -1em 0 0 -0.75em;
  top: 50%;
  position: absolute;
  width: 0;
}
p {
  margin-top: 4em;
  text-align: center;
}
button#youtubeclose span {
  color: black;
  float: right;
  margin-right: 2%;
}
.slider-parent h4,
.readingMaterialParent h4,
.row.first-row.mcqRead h4 {
  text-align: center;
  color: #9A309B;
  font-weight: 700;
  width: 100%;
}
.resource-set {
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding-top: 15px;
  padding-bottom: 15px;
  height: 100%;
}
.slider-video.slick-initialized.slick-slider {
  width: 95%;
  margin: auto;
}
.readingcard {
  width: 100%;
}
.readingcard img {
  display: flex;
  margin: auto;
}
.col-2.left-box img {
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.23);
  border-radius: 10px;
  object-fit: contain;
}
.col-10.right-box p {
  text-align: left;
}
@media (max-width: 575px) {
  .col-2.left-box img {
    height: 60px;
    width: 60px;
  }
}
.col-2.left-box {
  display: flex;
  justify-content: center;
  align-items: center;
}
.readingMaterialParent .col-10.right-box p:first-child {
  font-family: Poppins;
  font-size: 15px !important;
  font-style: normal;
  font-weight: 700;
  line-height: 15px;
  letter-spacing: -0.015em;
  text-align: left;
  color: black !important;
}
.button-wrap a {
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  cursor: pointer;
  padding: 5px 10px;
  display: inline-block;
  border: 1px solid #9A309B;
  color: #9A309B;
  text-decoration: none;
  width: 100px;
  text-align: center;
}
.button-wrap {
  margin-top: 4%;
}
.button-wrap a.next {
  float: right;
}
.toggleSwitch .switch {
  position: relative;
  display: inline-block;
  width: 30px;
  height: 15px;
}
.toggleSwitch .switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.toggleSwitch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}
.toggleSwitch .slider:before {
  position: absolute;
  content: "";
  height: 13px;
  width: 13px;
  left: 2px;
  bottom: 1px;
  background-color: #FFFFFF;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}
.toggleSwitch input:checked + .slider {
  background: radial-gradient(100% 400% at 0% 0%, #D13F7F 0%, #8028A8 100%);
}
.toggleSwitch input:focus + .slider {
  box-shadow: 0 0 1px #2F80ED;
}
.toggleSwitch input:checked + .slider:before {
  -webkit-transform: translateX(14px);
  -ms-transform: translateX(14px);
  transform: translateX(14px);
}
.toggleSwitch .slider.round {
  border-radius: 10px;
}
.toggleSwitch .slider.round:before {
  border-radius: 50%;
}
.toggleSwitch input:disabled + .slider {
  opacity: 0.3;
  cursor: not-allowed;
}
.public-text {
  margin-right: 10px;
  display: block;
  font-style: italic;
  font-size: 12px;
  color: #7F28A8;
}
.switch.toggleSwitch {
  position: absolute;
  right: 0%;
  top: 40%;
  display: flex;
  justify-content: center;
}
.row.heading {
  position: relative;
  width: 100%;
}
@media (max-width: 575px) {
  .col-12.col-md-6.col-lg-6.mt-3.pl-md-0.readingMaterialcard {
    padding: 0;
  }
  .row.heading {
    margin: 0;
  }
  .row.heading .col-12 {
    padding: 0;
  }
  .switch.toggleSwitch {
    top: 31%;
  }
  .button-wrap a {
    font-size: 10px;
    width: 80px !important;
  }
  .col-10.first-block span {
    font-size: 15px;
  }
  .slider-parent p,
  .readingMaterialParent p:first-child {
    font-size: 15px;
  }
  .validation-msg h4 {
    font-size: 13px;
  }
  .public-text {
    font-size: 10px;
  }
  .slider-video.slick-initialized.slick-slider {
    width: 90%;
    margin: auto;
  }
  .mcqRead .button-wrapper {
    padding-top: 5px;
    padding-bottom: 5px;
  }
}
.d-flex.flex-wrap.readingMaterialcards h5 {
  text-align: left;
  font-weight: 700;
}
.d-flex.justify-content-between.align-items-center.resource-set {
  padding: 10px;
}
.readingMaterialParent {
  margin-bottom: 5%;
}
.readingMaterialcards a {
  text-decoration: none;
  word-break: break-word;
}
.readingcard img {
  max-height: 250px;
  border-radius: 10px;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  margin-top: 10px;
}
.readingcard {
  padding: 10px;
}
.readingMaterialcards a strong {
  color: #9A309B;
}
.readingcard p {
  text-align: left;
  padding: 15px 0px;
}
@media (max-width: 575px) {
  .readingcard img {
    min-height: 200px !important;
  }
  .readingcard {
    padding-bottom: 10% !important;
  }
  .validation-msg .btn-primary {
    font-size: 10px;
  }
  .video-card {
    height: unset !important;
  }
  .slick-slide.slick-current {
    box-shadow: none !important;
  }
  .slick-track {
    padding: 0% 0% !important;
    margin-top: -15px !important;
    margin-bottom: -15px !important;
  }
  button.slick-prev.slick-arrow,
  .slick-next {
    width: 30px !important;
    height: 30px !important;
  }
}
.slick-slide.slick-current:focus {
  border: none !important;
  outline: none !important;
}
@media (min-width: 1024px) {
  button.slick-prev.slick-arrow {
    margin-left: 28px;
  }
}
.row.first-row.mcqRead {
  margin-bottom: 2%;
}
p.telegram-para.text-left {
  font-weight: 700;
}
