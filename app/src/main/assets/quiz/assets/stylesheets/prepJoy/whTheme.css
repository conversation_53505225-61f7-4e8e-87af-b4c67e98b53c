body{
    background: #fff;
}

.main-page__header,
.quizes .container,
.question-wrapper,
.progress-bar-vertical,
.result__header-wrapper,
.mcq-answers,
.question-wrapper.test-series{
    background: #fff;
}


.app-header,
.app-footer{
    box-shadow: 0 2px 5px rgba(255,255,255,0.2);
}

.main-page__header *,
.main-page__content-header *,
.main-page__content-points *,
.main-page__content-questionsRange__language *,
.main-page__content-questionsRange__range *,
.app-header *,
.question-wrapper *,
.preview-quiz *,
.practice-result *,
.result__header-wrapper *,
.mcq-answers *,
.main-page__sectionDetails *{
    color:#000 !important;
}
.main-page__content{
    background: #f5f5f5 !important;
}
.que-options-wrapper .que-options .option,
.app-header,
.app-footer,
.preview-quiz{
    background:#EFEFF1;
}
.main-page__content-questionsRange__language input[type=radio], .main-page__content-questionsRange__range input[type=radio]{
border-color:#000 !important;
}

.num-wrapper,
.result-points,
.nav-tabs,
.btn-review{
    background:#e3e3e3;
}
.num-wrapper .num-text{
    background:transparent
}

.nav-tabs li a{
    border-right: none;
}
.nav-tabs li {
    min-width: 153px;
}

.loader-submit-new{
width:30px;
}