* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
.room {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 90vh;
}
.room-header h4,
.room-header p {
  color: #fff;
}
.room-code__sec {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.room-code__sec .accessCodeDiv {
  margin: 40px auto 20px auto;
  color: #fff;
  border: 2px #9999;
  border-style: dashed solid;
  padding: 0.75rem;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.room-code__sec .accessCodeDiv h2 {
  margin-bottom: 0!important;
}
.room-code__sec .shareCodeBtn {
  border: 1px solid #DA0101;
  padding: 10px;
  border-radius: 5px;
  color: #DA0101;
  font-size: 12px;
}
.room #accessloader {
  margin-top: 10px;
}
.room-start__game {
  margin-top: auto;
  display: flex;
  justify-content: center;
}
.room-start__game .startGameBtn {
  display: block;
  padding: 0.5rem 1rem;
  background: #DA0101;
  border-radius: 5px;
  border: none;
  outline: none;
  color: #fff;
  width: 200px;
}
.room-start__game .startGameBtn:disabled {
  background: #ef6c6c;
}
.room-friendsList {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30%;
  flex-direction: column;
}
.room-friendsList h5 {
  color: #fff;
}
.joinRoom {
  display: flex;
  flex-direction: column;
  height: 55vh;
}
.joinRoom-header {
  color: #fff;
  text-align: center;
  margin-bottom: 40px;
}
.joinRoom-input__sec {
  display: flex;
}
.joinRoom-input__sec input {
  padding: 12px;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  outline: none;
  border: none;
  width: 100%;
  margin-bottom: 1px;
}
.joinRoom-input__sec input:focus-visible {
  outline: none;
  border: none;
}
.joinRoom-input__sec .joinNow {
  border: none;
  padding: 12px;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
  background: #DA0101;
}
.joinRoom-input__sec .joinNow:focus {
  outline: none;
}

.connectionlost,
#nextAccepted,
#previousAccepted,
.closeModal,
#rematchBtnAccept,
.alertClose,
.internerLost{
background:#DA0101 !important;
border:none !important;
}

#connectionLostModal .modal-content{
background:#150f36 !important;
color:#fff !important;
}
#connectionLostModal .modal-footer{
border-top:none!important;
}

#rematchModal .modal-content{
   background:#150f36 !important;
   color:#fff !important;
}

#rematchModal .modal-footer{
  border-top:none!important;
}

#denyMatchModal .modal-content{
background:#150f36 !important;
color:#fff !important;
}

#denyMatchModal .modal-footer{
  border-top:none!important;
}

#previousMatchModal .modal-content{
   background:#150f36 !important;
   color:#fff !important;
}

#previousMatchModal .modal-footer{
  border-top:none!important;
}

#nextMatchModal .modal-content{
   background:#150f36 !important;
   color:#fff !important;
}

#nextMatchModal .modal-footer{
  border-top:none!important;
}

#connectionBusyModal .modal-content{
   background:#150f36 !important;
   color:#fff !important;
}

#connectionBusyModal .modal-footer{
  border-top:none!important;
}

#alertsModal .modal-content{
   background:#150f36 !important;
   color:#fff !important;
}

#alertsModal .modal-footer{
  border-top:none!important;
}

#internetLostModal .modal-content{
   background:#150f36 !important;
   color:#fff !important;
}

#internetLostModal .modal-footer{
  border-top:none!important;
}
.realUsr-img{
  border: 2px solid #fff;
  padding: 2px;
}

.action-wrapper {
    min-height: 100px !important;
    height: auto !important;
}