.questionBlock {
  display: flex;
}
#parent ul {
  padding: 0;
}
#parent li {
  list-style: none;
  padding: 3px;
}
div#answer-list ul {
  width: 100%;
  max-width: 100%;
}
div#answer-list ul li {
  display: inline-block;
  width: auto;
  text-align: center;
  border: 1px solid ;
  width: 69px;
}
div#answer-list {
  display: block;
  width: 100%;
}
div#answer-list h6 {
  display: inline;
  margin: 0 !important;
}
div#answer-list span:after {
  content: ".";
}
.container h4 {
  padding: 10px;
}
div#description li h6 {
  margin: 0px !important;
  margin-right: 5px !important;
}
div#parent h4,
div#parent h5,
div#parent h6 {
  color: black;
}
#htmlContent table {
  width: auto !important;
}
.questionBlock h5 p,
.questionBlock h5 {
  color: #000000;
  font-weight: 700;
  font-size: 13px;
}
.questionBlock > p,
div#description ul li > p:nth-child(1) {
  margin-right: 2px;
  font-weight: bold;
}
.questionBlock > p:after,
div#description ul li > p:nth-child(1):after {
  content: ".";
}
#htmlContent span {
  font-family: inherit !important;
}
.questionBlock > p {
  width: 25px !important;
}
#htmlContent td {
  border: 1px solid black;
  padding: 6px;
}
.katex .base {
  display: inline !important;
}
.questionBlock img {
  max-width: 100%;
  width: auto;
  height: 100%;
}
div#answer-list ul li p {
  display: inline;
}
div#answer-list ul li p:after {
  content: ".";
}
.direction p {
  word-break: break-word;
}
div#book-read-material .container {
  padding: 0px 9px !important;
}
div#getdata li p:nth-child(2),
div#getdata li p:nth-child(1) {
  display: inline;
}
.questionBlock p {
  word-break: unset !important;
}
.directions-parent p {
  font-weight: normal;
  font-size: 13px;
}
#parent ul li {
  font-size: 13px !important;
}
.questionBlock h5 p {
  margin-bottom: 0 !important;
}
#parent ul {
  padding: 0;
  margin-bottom: 1rem !important;
}
.directions-parent p,
.directions-parent .table-responsive {
  font-style: italic;
}
.directions-parent p {
  margin-bottom: 0 !important;
  padding-bottom: 1%;
}
#parent ul li p {
  margin-right: 1%;
}
#htmlContent h1,
#htmlContent h2,
#htmlContent h3,
#htmlContent h4,
#htmlContent h5,
#htmlContent p,
#htmlContent span {
  color: #000;
  font-family: inherit;
  word-break: break-word;
  white-space: inherit;
}
@media only screen and (max-width: 767px) {
  .katex {
    font-size: 10px !important;
  }
  .katex .vlist > span > span {
    display: inline-flex !important;
  }
  .katex .col-align-c .mord {
    position: relative;
    margin-top: 1.4em !important;
  }
  .katex .mtable .vertical-separator {
    position: relative;
    top: 2em;
  }
  .katex .mtable .arraycolsep {
    position: relative;
    top: 2em;
  }
  .katex .mtable .col-align-l > .vlist-t {
    line-height: 1em !important;
  }
  .katex .col-align-l {
    position: relative;
    top: 2em;
  }
  .katex .col-align-l .mord:first-child {
    top: 0.2em !important;
    position: relative;
  }
}
.read-content .suggested-videos {
  width: 600px;
  margin: 0 auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .read-content .suggested-videos {
    width: 100%;
  }
}
.read-content .suggested-videos h6 {
  font-family: 'Poppins', sans-serif;
}
.read-content .suggested-videos .video-wrapper {
  margin: 1rem 0;
}
.read-content .suggested-videos .video-wrapper a {
  display: flex;
  color: #444444;
}
.read-content .suggested-videos .video-wrapper .video-img-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
  height: 45px;
}
.read-content .suggested-videos .video-wrapper .video-img-wrapper::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #00000066;
}
.read-content .suggested-videos .video-wrapper .video-img-wrapper img {
  width: 80px;
}
.read-content .suggested-videos .video-wrapper .video-img-wrapper i {
  position: absolute;
  color: red;
}
.read-content .suggested-videos .video-wrapper p {
  line-height: normal;
  margin-bottom: 0;
  font-size: 13px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .read-content .suggested-videos .video-wrapper p {
    font-size: 12px;
  }
}
#reader {
  position: fixed;
  top: 0;
  width: 100%;
  transform: translateX(100%);
  transition: all 0.5s ease;
}
.readerAnimation {
  transform: translateX(0) !important;
  transition: all 0.5s ease;
  background: #fff;
}
.readerAnimationClose {
  transform: translateX(100%) !important;
  transition: all 0.5s ease;
  background: #fff;
}
.epub_nav {
  background: #fff;
  height: 75px;
  display: flex;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 3;
  justify-content: space-between;
  padding: 0 2rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.epub_nav-backBtn {
  background: transparent;
  border: none;
}
.navBtns {
  margin-left: 10px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.navBtns i {
  font-size: 20px !important;
}
.epub_footer {
  width: 100%;
  background: #f4f4f4;
  padding: 10px;
  position: fixed;
  bottom: 0;
  z-index: 2;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.epubProgressSlider {
  height: 2px;
  width: 75%;
  margin: 0 auto;
  display: flex;
  margin-top: 10px;
}
.lazyLoader {
  background: #fff;
  height: 100vh;
  position: absolute;
  opacity: 0.9;
  z-index: 9999;
  top: 0;
  left: 0;
  right: 0;
}
.lazyLoader h2 {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 5rem;
}
.loaderHidden {
  display: none;
}
.anim {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
.epubDownloadBtn {
  background: #1F419B;
  color: #fff;
}
@media (max-width: 768px) {
  .epub_nav {
    padding: 10px;
  }
  .epubHighlightsBtn,
  .epubDownloadBtn {
    width: 100px;
    font-size: 10px;
    padding: 5px;
  }
  .epubHighlightsBtn i,
  .epubDownloadBtn i {
    font-size: 16px;
  }
  .navBtns {
    width: 32px;
    height: 32px;
  }
}
