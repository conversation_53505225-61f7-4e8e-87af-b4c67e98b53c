<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <data
                android:host="youtube.com"
                android:scheme="https" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <data
                android:host="twitter.com"
                android:scheme="https" />
        </intent>
    </queries>

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="28"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
    <uses-permission android:name="android.permission.ACCESS_DOWNLOAD_MANAGER" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission
        android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <application
        android:name=".Application"
        android:allowBackup="false"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:resizeableActivity="false"
        android:supportsRtl="true"
        android:theme="@style/Theme.PreopJoy"
        android:usesCleartextTraffic="true"
        tools:replace="android:icon,android:theme,android:allowBackup">
        <activity
            android:name=".Views.Activity.DeepLinkReceiverActivity"
            android:exported="true"
            android:launchMode="singleInstance"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme1">
            <intent-filter
                android:autoVerify="true"
                tools:targetApi="m">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="http"/>
                <data android:host="prepjoy.page.link"/>
                <data android:scheme="https"/>
                <data android:host="prepjoy.page.link"/>
                <data android:scheme="https"/>
                <data android:host="www.prepjoy.com"/>
                <data android:scheme="https"/>
                <data android:host="prepjoy.com"/>
                <data android:scheme="https"/>
                <data android:host="publish.prepjoy.com"/>
                <data android:scheme="https"/>
                <data android:host="qa.prepjoy.com"/>
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.ibookgpt.GPTChatActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"/>
        <activity
            android:name=".ui.ibookgpt.GPTRechargeActivity"
            android:exported="false"
            android:theme="@style/AppTheme.NoActionBar"/>
        <activity
            android:name=".ui.ibookgpt.NotesActivity"
            android:exported="false"
            android:theme="@style/AppTheme.NoActionBar"/>
        <activity
            android:name=".ui.resources.reading.ReadingActivity"
            android:exported="false"
            android:theme="@style/ModuleTheme"/>
        <activity
            android:name=".ui.resources.weblink.WeblinkAct"
            android:exported="false"
            android:theme="@style/ModuleTheme"/>
        <activity
            android:name=".ui.notification.NotificationDetailsActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <!-- Shopping cart functionality temporarily disabled for demo -->
        <!-- <activity
            android:name=".ui.shoppingcart.ActShoppingCart"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" /> -->
        <activity
            android:name=".ui.dashboard.GrantPermissionActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.chapters_list.HistoryForQuiz"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme1" />
        <activity
            android:name=".news.NewsListActivity"
            android:exported="false"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".news.NewsPreferenceActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".news.PubFeedDetails"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.groupwall.GroupWallActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".news.FeedDetailsWebViewActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.groupwall.GroupWallCommentAndReplayActivity"
            android:exported="false" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/providers_path" />
        </provider>

        <activity
            android:name=".ui.notification.NotificationActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.quiz_instruction.QuizInstructions"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme1"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.dashboard.UserPreferanceSelectionActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.settings.SettingsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme1" />
        <activity
            android:name=".ui.new_book_details.BookDetailsAct"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme1" />
        <activity
            android:name=".ui.quiz.QuizActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme1"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.quiz.ResultActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme1" />
        <activity
            android:name=".ui.videos.VideoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme1" />
        <activity
            android:name=".ui.reading_material.ReadingMaterialActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme1" />
        <activity
            android:name=".ui.resource_input.ResourceInputActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
        <activity
            android:name=".ui.dashboard.DashBoardActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme1"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name=".ui.profile.ProfileActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme1" />
        <activity
            android:name=".ui.signup.SignUpActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme1" />
        <activity
            android:name=".ui.login.LoginActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme1" />
        <activity
            android:name="com.soundcloud.android.crop.CropImageActivity"
            android:exported="true" />
        <activity
            android:name=".ui.videos.CustomMediaPlayer"
            android:configChanges="orientation|screenSize"
            android:exported="false"
            android:theme="@style/AppTheme1"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".ui.audio.AudioPlayerActivity"
            android:screenOrientation="portrait"
            android:exported="false"
            android:theme="@style/AppTheme1"
            android:launchMode="singleTop"/>
        <activity
            android:name=".Views.Activity.SplashActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme1">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.chapters_list.ChaptersListAct"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme1" />

        <activity
            android:name=".ui.report_issue.ReportIssueActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme1" />

        <receiver
            android:name=".Utils.InternetConnectionChecker"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_ACTION" />
            </intent-filter>
        </receiver>
        <receiver
            android:name=".ui.login.SmsBroadcastReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
            </intent-filter>
        </receiver>

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <service
            android:name=".service.PrepJoyFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <service android:name=".ui.videos.AudioPlayerService" />
        <!--
 Set custom default icon. This is used when no icon is set for incoming notification messages.
     See README(https://goo.gl/l4GJaQ) for more.
        -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/app_icon" />
        <!--
 Set color used with incoming notification messages. This is used when no color is set for the incoming
             notification message. See README(https://goo.gl/6BKBk7) for more.
        -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/primary_bg_dark" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="@string/com.crashlytics.android.build_id" /> <!-- RazorPay library has not added "exported" param so manually we need to include it -->
    </application>

</manifest>