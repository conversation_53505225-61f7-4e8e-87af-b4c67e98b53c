<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context="com.wonderslate.prepjoy.Views.Activity.SplashActivity">



        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgPrepJLogo"
            android:layout_width="200dp"
            android:layout_height="200dp"
            android:src="@drawable/app_icon"
            android:scaleType="centerCrop"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/splash_sub_title"
            android:textStyle="bold"
            android:textSize="@dimen/_12ssp"
            android:textColor="@color/primary_bg_dark"
            android:layout_marginTop="@dimen/_5sdp"
            app:layout_constraintEnd_toEndOf="@id/imgPrepJLogo"
            app:layout_constraintTop_toBottomOf="@+id/imgPrepJLogo"
            app:layout_constraintLeft_toLeftOf="parent"
            android:visibility="gone"
            />

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:srcCompat="@drawable/ws_logo_footer"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginBottom="25dp" />

        <com.varunest.loader.TheGlowingLoader
            android:layout_width="match_parent"
            android:layout_height="500dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:visibility="visible"
           />



        <FrameLayout
            android:id="@+id/layout_server_issue"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:visibility="gone">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:background="@color/white"
                    android:gravity="center"
                    android:orientation="vertical">

                        <com.airbnb.lottie.LottieAnimationView
                            android:id="@+id/lottie_view"
                            android:layout_width="300dp"
                            android:layout_height="300dp"
                            android:layout_centerHorizontal="true"
                            app:lottie_autoPlay="true"
                            app:lottie_loop="true"
                            app:lottie_rawRes="@raw/lottie_server" />

                        <TextView
                            android:id="@+id/textView_server_message"
                            android:layout_width="250dp"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/poppins_regular"
                            android:gravity="center"
                            android:text=""
                            android:textColor="@color/black"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:visibility="visible" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                                <Button
                                    android:id="@+id/button_close"
                                    android:layout_width="150dp"
                                    android:layout_height="40dp"
                                    android:layout_marginTop="10dp"
                                    android:layout_marginRight="10dp"
                                    android:background="@color/primary_bg_red"
                                    android:text="Close App"
                                    android:textAllCaps="false"
                                    android:textColor="@color/white"
                                    android:textSize="12sp"
                                    android:textStyle="bold" />

                                <Button
                                    android:id="@+id/button_offline"
                                    android:layout_width="150dp"
                                    android:layout_height="40dp"
                                    android:layout_marginLeft="10dp"
                                    android:layout_marginTop="10dp"
                                    android:background="@color/gray"
                                    android:text="Go Offline"
                                    android:textAllCaps="false"
                                    android:textColor="@color/white"
                                    android:textSize="12sp"
                                    android:textStyle="bold" />
                        </LinearLayout>
                </LinearLayout>

        </FrameLayout>



</androidx.constraintlayout.widget.ConstraintLayout>