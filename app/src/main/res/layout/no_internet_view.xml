<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android" android:layout_width="match_parent"
    android:orientation="vertical"
    android:id="@+id/noConnectionLayout"
    android:visibility="gone"
    android:layout_height="wrap_content">


    <TextView
        android:id="@+id/errorTxt"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingTop="5dp"
        android:paddingBottom="3dp"
        android:background="@color/primary_bg_red"
        android:text="@string/internet_connection_offline_text"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:typeface="serif" />

    <View
        android:layout_width="match_parent"
        android:layout_height="3dp"
        android:alpha="0.3"
        android:background="@drawable/top_shadow"/>
    </RelativeLayout>