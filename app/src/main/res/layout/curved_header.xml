<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ballon_header_root_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <RelativeLayout
        android:id="@+id/layout_back_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:focusable="true"
        android:foreground="?selectableItemBackground">

        <ImageView
            android:id="@+id/btnBack"
            android:layout_width="40dp"
            android:layout_height="30dp"
            android:layout_marginLeft="8dp"
            android:layout_marginTop="14dp"
            android:layout_marginBottom="10dp"
            android:padding="2dp"
            android:src="@drawable/ic_arrow_back_black"
            app:tint="@color/text_stike_throught" />

        <TextView
            android:id="@+id/textview_header_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:layout_marginTop="18dp"
            android:layout_toRightOf="@+id/btnBack"
            android:gravity="center"
            android:fontFamily="@font/poppins_bold"
            android:text="Back"
            android:textColor="@color/text_stike_throught"
            android:textSize="14dp" />

        <TextView
            android:id="@+id/pageTitleWhite"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/btnBack"
            android:layout_alignBottom="@+id/btnBack"
            android:layout_marginTop="0dp"
            android:layout_marginBottom="3dp"
            android:layout_toEndOf="@+id/btnBack"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:text="The Joy of Learning dfdfd  hsj hjb hdfzjh dfh "
            android:scrollHorizontally="true"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:maxEms="11"
            android:visibility="gone"
            app:autoSizeMaxTextSize="20sp"
            app:autoSizeMinTextSize="10sp" />

        <ImageView
            android:id="@+id/btnBack_hamburger"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="15dp"
            android:clickable="true"
            android:focusable="true"
            android:padding="8dp"
            android:src="@drawable/ic_hamburger"
            android:visibility="gone"
            app:tint="@color/ws_new_solid_color" />
    </RelativeLayout>

    <ImageView
        android:id="@+id/imgWsLogo"
        android:layout_width="90dp"
        android:layout_height="30dp"
        android:layout_alignParentRight="true"
        android:layout_marginTop="14dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:src="@drawable/ic_logo"
        android:visibility="visible" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/no_connection_lottie"
        android:layout_width="80dp"
        android:layout_height="50dp"
        android:layout_alignParentRight="true"
        android:layout_marginTop="10dp"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        android:visibility="gone"
        app:lottie_rawRes="@raw/no_connection_lottiejson" />

    <ImageView
        android:id="@+id/imgRefresh"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_alignParentRight="true"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:src="@drawable/ic_refresh"
        android:visibility="gone"
        app:tint="@color/white" />

    <RelativeLayout
        android:id="@+id/layout_timer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_toEndOf="@+id/imgWsLogo"
        android:background="@drawable/shadow_full_curved_layout_bg_lite"
        android:gravity="center|left"
        android:padding="10dp"
        android:visibility="gone">

        <com.ws.core_ui.custom_views.WSTextView
            android:id="@+id/txttimer"
            android:layout_width="100dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxWidth="220dp"
            android:maxLines="1"
            android:textColor="@color/black"
            android:textSize="11sp"
            android:textStyle="bold"
            app:autoSizeMaxTextSize="11sp"
            app:autoSizeMinTextSize="7sp"
            app:autoSizeTextType="uniform"
            app:ws_font_weight="bold"
            tools:text="00:00" />

        <ProgressBar
            android:id="@+id/view_progress_bar"
            style="@style/MyProgressBarTwo"
            android:layout_width="80dp"
            android:layout_height="6dp"
            android:layout_below="@+id/txttimer"
            android:layout_centerHorizontal="true"
            android:layout_gravity="center"
            android:layout_marginBottom="3dp"
            android:theme="@style/MyProgressBarTwo" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/lnHeaderTitleGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/layout_back_icon"
        android:layout_alignParentStart="true"
        android:layout_marginLeft="3dp"
        android:gravity="center_vertical">

        <com.ws.core_ui.custom_views.WSTextView
            android:id="@+id/pageTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:ellipsize="end"
            android:gravity="center"
            android:maxWidth="220dp"
            android:maxLines="1"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:textSize="20sp"
            android:textStyle="bold"
            app:autoSizeMaxTextSize="20sp"
            app:autoSizeMinTextSize="10sp"
            app:autoSizeTextType="uniform"
            app:enableGradient="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/frameLayout_notification"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:ws_font_weight="bold"
            tools:text="The Joy of Learning" />

        <FrameLayout
            android:id="@+id/frameLayout_notification"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@+id/pageTitle"
            android:clickable="true"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:id="@+id/lltNotification"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:clickable="false"
                android:gravity="center"
                android:padding="3dp">

                <Button
                    android:id="@+id/button_notification"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ic_notification_bell"
                    android:clickable="false"
                    android:gravity="center" />

            </LinearLayout>

            <TextView
                android:id="@+id/notification_count"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_gravity="right"
                android:layout_marginTop="4dp"
                android:layout_marginRight="4dp"
                android:background="@drawable/avatar_bg_circle"
                android:clickable="false"
                android:gravity="center"
                android:text="0"
                android:textColor="@color/black"
                android:textSize="10dp" />
        </FrameLayout>
    </RelativeLayout>

</RelativeLayout>