<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_bg_dark"
    android:padding="30dp"
    tools:context="com.wonderslate.prepjoy.ui.login.LoginActivity">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imgPrepJLogo"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:src="@drawable/app_icon"
        android:scaleType="fitCenter"
        android:adjustViewBounds="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/txtLoginDesc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="@dimen/_10ssp"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@+id/imgPrepJLogo"
        app:layout_constraintLeft_toLeftOf="parent"
        android:text="@string/sign_up_get_started_desc"/>


    <EditText
        android:id="@+id/edtName"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="10dp"
        android:hint="@string/sign_up_name"
        android:textColorHint="@color/gray"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:padding="10dp"
        android:background="@color/heighlight_bg_blue"
        app:layout_constraintTop_toBottomOf="@+id/txtLoginDesc"
        app:layout_constraintLeft_toLeftOf="parent"
        />

    <TextView
        android:id="@+id/txtvalidateName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/primary_bg_red"
        android:textSize="@dimen/_10ssp"
        android:layout_marginTop="10dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/edtName"
        app:layout_constraintLeft_toLeftOf="parent"
        android:text="@string/sign_up_enter_name"/>

    <EditText
        android:id="@+id/edtUsername"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="10dp"
        android:hint="@string/enter_your_mobile_number_short"
        android:textColorHint="@color/gray"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:padding="10dp"
        android:background="@color/heighlight_bg_blue"
        app:layout_constraintTop_toBottomOf="@+id/txtvalidateName"
        app:layout_constraintLeft_toLeftOf="parent"
        />

    <EditText
        android:id="@+id/edtPassword"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="10dp"
        android:hint="@string/password"
        android:textColorHint="@color/gray"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:padding="10dp"
        android:inputType="textPassword"
        android:background="@color/heighlight_bg_blue"
        app:layout_constraintTop_toBottomOf="@+id/edtUsername"
        app:layout_constraintLeft_toLeftOf="parent"
        />


    <androidx.appcompat.widget.AppCompatSpinner
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:id="@+id/spinnerstate"
        android:layout_marginTop="10dp"
        android:background="@color/heighlight_bg_blue"
        app:layout_constraintTop_toBottomOf="@+id/edtPassword"
        app:layout_constraintLeft_toLeftOf="parent" />


    <TextView
        android:id="@+id/txtvalidateState"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/primary_bg_red"
        android:textSize="@dimen/_10ssp"
        android:layout_marginTop="10dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/spinnerstate"
        app:layout_constraintLeft_toLeftOf="parent"
        android:text="@string/sign_up_select_state"/>


    <androidx.appcompat.widget.AppCompatSpinner
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:id="@+id/spinnercity"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:background="@color/heighlight_bg_blue"
        app:layout_constraintTop_toBottomOf="@+id/txtvalidateState"
        app:layout_constraintLeft_toLeftOf="parent" />




    <TextView
        android:id="@+id/txtvalidatecity"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/primary_bg_red"
        android:textSize="@dimen/_10ssp"
        android:layout_marginTop="10dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/spinnercity"
        app:layout_constraintLeft_toLeftOf="parent"
        android:text="@string/sign_up_select_district"/>

    <EditText
        android:id="@+id/edtSpinnerCountry"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="10dp"
        android:hint="Country"
        android:textColorHint="#7e7e7e"
        android:textSize="@dimen/_10ssp"
        android:padding="10dp"
        android:visibility="gone"
        android:background="@color/heighlight_bg_blue"
        app:layout_constraintTop_toBottomOf="@+id/txtvalidatecity"
        app:layout_constraintLeft_toLeftOf="parent"
        />


    <LinearLayout
        android:id="@+id/rrltCircleBtn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        app:layout_constraintTop_toBottomOf="@+id/edtSpinnerCountry"
        app:layout_constraintLeft_toLeftOf="parent">

        <Button
            android:id="@+id/btnSignup"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="20dp"
            android:background="@drawable/button_shape_default"
            android:gravity="center_horizontal|center_vertical"
            android:text="@string/sign_up_confirm"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="@dimen/_14ssp"
            android:theme="@style/PrimaryButton" />
    </LinearLayout>

    <com.wang.avi.AVLoadingIndicatorView
        android:id="@+id/avlSignUp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        style="@style/AVLoadingIndicatorView"
        android:visibility="invisible"
        app:indicatorName="BallBeatIndicator"
        app:indicatorColor="@color/primary_bg_red"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <RelativeLayout
        android:layout_width="match_parent"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_height="wrap_content">

        <include
            android:id="@+id/layoutInternet"
            layout="@layout/no_internet_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="5dp"/>


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_below="@id/layoutInternet"
            android:layout_marginTop="8dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/imgFooterLogo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:srcCompat="@drawable/ws_logo"
                android:layout_marginEnd="@dimen/_10sdp"
                />

            <TextView
                android:id="@+id/txtPoweredBy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/footer_powered_by"
                android:textColor="@color/white"
                android:textSize="@dimen/_10ssp"
                android:layout_toEndOf="@+id/imgFooterLogo"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/txtPoweredBy"
                android:layout_toEndOf="@+id/imgFooterLogo"
                android:text="@string/footer_wonderslate"
                android:textColor="@color/white"
                android:textSize="@dimen/_14ssp" />
        </RelativeLayout>
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>