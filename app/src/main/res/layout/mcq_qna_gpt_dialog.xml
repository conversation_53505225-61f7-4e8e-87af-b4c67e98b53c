<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="15dp"
    android:layout_margin="15dp"
    android:elevation="5dp"
    android:background="@drawable/gpt_drawable_dialog_back">

    <LinearLayout
        android:id="@+id/questionLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="5dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Number of Questions"
            android:textSize="16sp"
            android:textColor="@android:color/black"
            android:fontFamily="@font/poppins_bold"
            android:layout_marginBottom="12dp"/>

        <EditText
            android:id="@+id/etNumberOfQuestions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Enter number of questions"
            android:textColorHint="@color/app_gray"
            android:fontFamily="@font/poppins_medium"
            android:textSize="14sp"
            android:inputType="number"
            android:padding="12dp"
            android:background="@drawable/rounded_edit_text_back"
            android:layout_marginBottom="20dp"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/mcqLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="5dp"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Number of MCQs"
            android:textSize="16sp"
            android:textColor="@android:color/black"
            android:fontFamily="@font/poppins_bold"
            android:layout_marginBottom="12dp"/>

        <EditText
            android:id="@+id/etNumberOfMCQs"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Enter number of MCQs"
            android:textColorHint="@color/app_gray"
            android:fontFamily="@font/poppins_medium"
            android:textSize="14sp"
            android:inputType="number"
            android:padding="12dp"
            android:background="@drawable/rounded_edit_text_back"
            android:layout_marginBottom="20dp"/>

    </LinearLayout>

    <Button
        android:id="@+id/btnCreateQuestions"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Create"
        android:backgroundTint="@color/colorAccent"
        android:background="@drawable/button_background_accent_rounded"
        android:textColor="@android:color/white"
        android:fontFamily="@font/poppins_medium"
        android:textAllCaps="false"
        android:layout_marginBottom="8dp"/>
</LinearLayout>
