<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clickable="true"
    android:focusable="true"
    android:foreground="@drawable/normal_selectable_foreground"
    android:paddingStart="10dp"
    android:paddingEnd="10dp"
    android:id="@+id/chapterItem">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/chapterLock"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_lock_grey"
            android:layout_centerVertical="true"
            android:layout_marginEnd="10dp"/>

        <TextView
            android:id="@+id/chapterName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@id/chapterLock"
            android:fontFamily="@font/poppins_medium"
            android:padding="10dp"
            android:gravity="start"
            android:textColor="@color/colorBookNameTitle"
            android:textSize="14sp"
            android:maxLines="2"
            android:ellipsize="end"
            android:typeface="serif" />

    </RelativeLayout>
</FrameLayout>