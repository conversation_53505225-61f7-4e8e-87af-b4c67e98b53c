<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="20dp"
    android:elevation="4dp"
    android:gravity="center"
    android:background="@drawable/gpt_drawable_dialog_back">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Unlock Full Access!"
        android:fontFamily="@font/poppins_medium"
        android:textStyle="bold"
        android:textSize="20sp"
        android:textColor="@android:color/black"
        android:gravity="center" />

    <TextView
        android:id="@+id/tvRechargeMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@android:color/darker_gray"
        android:textSize="16sp"
        android:fontFamily="@font/poppins_medium"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:lineSpacingExtra="4dp"
        android:gravity="center"/>

    <Button
        android:id="@+id/rechargeButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/button_background_accent_rounded"
        android:backgroundTint="@color/colorAccent"
        android:textColor="@android:color/white"
        android:fontFamily="@font/poppins_medium"
        android:textSize="14sp"
        android:layout_marginTop="8dp"
        android:padding="12dp"
        android:elevation="3dp"
        android:gravity="center"/>
</LinearLayout>
