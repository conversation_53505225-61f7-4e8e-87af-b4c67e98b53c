<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:gravity="center"
    android:background="@drawable/gpt_drawable_dialog_back"
    android:layout_margin="15dp">

    <!-- Title Text -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Let's make it perfect together."
        android:textStyle="bold"
        android:textSize="16sp"
        android:gravity="center"
        android:fontFamily="@font/poppins_medium"
        android:textColor="@color/black" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="What can be improved in this answer?"
        android:textSize="14sp"
        android:gravity="center"
        android:fontFamily="@font/poppins_medium"
        android:layout_marginTop="8dp"
        android:textColor="@color/app_gray" />

    <!-- Feedback Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="16dp">

        <!-- Individual feedback button style -->
        <Button
            android:id="@+id/btnIncorrectResponse"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="INCORRECT RESPONSE"
            android:background="@drawable/button_background_filled"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:layout_marginBottom="10dp"
            android:padding="12dp"
            android:fontFamily="@font/poppins_medium" />

        <Button
            android:id="@+id/btnIrrelevantResponse"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="RESPONSE IS IRRELEVANT"
            android:background="@drawable/button_background_filled"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:layout_marginBottom="8dp"
            android:padding="12dp"
            android:fontFamily="@font/poppins_medium" />

        <Button
            android:id="@+id/btnUnclearResponse"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Response is unclear or confusing"
            android:background="@drawable/button_background_filled"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:layout_marginBottom="8dp"
            android:padding="12dp"
            android:fontFamily="@font/poppins_medium" />

        <Button
            android:id="@+id/btnNotAddressIntent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Response do not address my intent"
            android:background="@drawable/button_background_filled"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:layout_marginBottom="8dp"
            android:padding="12dp"
            android:fontFamily="@font/poppins_medium" />

        <Button
            android:id="@+id/btnInappropriateContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Response has inappropriate content"
            android:background="@drawable/button_background_filled"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:layout_marginBottom="8dp"
            android:padding="12dp"
            android:fontFamily="@font/poppins_medium" />

        <Button
            android:id="@+id/btnOther"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Others (Please specify)"
            android:background="@drawable/button_background_filled"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:layout_marginBottom="8dp"
            android:padding="12dp"
            android:fontFamily="@font/poppins_medium" />

    </LinearLayout>

    <!-- Feedback Input Field -->
    <EditText
        android:id="@+id/etFeedback"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="What can we do better? Your feedback..."
        android:inputType="textMultiLine"
        android:padding="12dp"
        android:layout_marginTop="16dp"
        android:textColor="@color/black"
        android:textColorHint="@color/app_gray"
        android:fontFamily="@font/poppins_medium"
        android:textSize="14sp"
        android:background="@drawable/rounded_edit_text_back"
        android:maxHeight="100dp"
        android:minHeight="40dp"
        android:scrollbars="vertical"
        android:overScrollMode="never" />

    <!-- Submit Button -->
    <Button
        android:id="@+id/submitButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="SUBMIT"
        android:background="@drawable/button_background_filled"
        android:textColor="@color/white"
        android:padding="12dp"
        android:textSize="16sp"
        android:layout_marginTop="16dp"
        android:fontFamily="@font/poppins_medium"
        android:gravity="center" />

</LinearLayout>