<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/light_white">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="5dp">

        <LinearLayout
            android:id="@+id/layout_user"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/relativeLayout_avatar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_gravity="center">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/avatar_bg_circle_white"
                    android:gravity="center">

                    <androidx.cardview.widget.CardView
                        android:layout_width="18dp"
                        android:layout_height="18dp"
                        android:layout_centerHorizontal="true"
                        android:elevation="12dp"
                        app:cardCornerRadius="60dp">

                        <ImageView
                            android:id="@+id/imageView_avatar"
                            android:layout_width="18dp"
                            android:layout_height="18dp"
                            android:scaleType="centerCrop"
                            app:srcCompat="@drawable/profile_icon" />
                    </androidx.cardview.widget.CardView>
                </RelativeLayout>

            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center|left"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textview_user"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_gravity="center|left"
                    android:layout_marginLeft="5dp"
                    android:text="User name"
                    android:textColor="@color/colorActionBarText"
                    android:textSize="10sp" />

                <TextView
                    android:id="@+id/textview_date_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_gravity="center"
                    android:layout_marginLeft="10dp"
                    android:text="02.09.2021"
                    android:textColor="@color/gray"
                    android:textSize="8sp" />
            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/textView_replay_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/layout_user"
            android:layout_marginLeft="40dp"
            android:layout_marginTop="5dp"
            android:layout_marginRight="10dp"
            android:autoLink="all"
            android:autoSizeMaxTextSize="12sp"
            android:autoSizeMinTextSize="10sp"
            android:ellipsize="end"
            android:fontFamily="@font/poppins_regular"
            android:text="My commend here"
            android:textColor="@color/black"
            android:textColorLink="@color/video_title_color_land"
            android:textSize="12sp" />
    </RelativeLayout>
</RelativeLayout>

