<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_bg_dark"
    android:padding="30dp"
    tools:context="com.wonderslate.prepjoy.ui.login.LoginActivity">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imgPrepJLogo"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_centerHorizontal="true"
        android:src="@drawable/app_icon"
        android:scaleType="centerCrop" />

    <TextView
        android:id="@+id/txtLogin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/imgPrepJLogo"
        android:layout_marginTop="10dp"
        android:text="@string/login_screen_login_text"
        android:textColor="@color/primary_bg_red"
        android:textSize="24sp"
        android:textStyle="bold"
        android:visibility="gone" />

    <TextView
        android:id="@+id/txtLoginDesc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/txtLogin"
        android:visibility="gone"
        android:layout_marginTop="10dp"
        android:text="@string/login_screen_login_text_desc"
        android:textColor="@color/white"
        android:textSize="10sp" />


    <LinearLayout
        android:id="@+id/rrltMobile"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/txtLoginDesc"
        android:layout_marginTop="10dp"
        android:background="@color/heighlight_bg_blue"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/edtMobileNumber"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_weight="2"
            android:background="@color/heighlight_bg_blue"
            android:hint="@string/enter_your_mobile_number"
            android:inputType="textEmailAddress"
            android:padding="10dp"
            android:textColor="@color/white"
            android:textColorHint="#7e7e7e"
            android:textSize="14sp" />

        <ImageView
            android:id="@+id/imgEditMobile"
            android:layout_width="50dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginRight="20dp"
            android:layout_weight="1"
            android:background="@android:drawable/ic_menu_edit"
            android:clickable="true"
            android:focusable="true"
            android:visibility="gone" />


    </LinearLayout>


    <TextView
        android:id="@+id/txtOtpSentMobile"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/rrltMobile"
        android:layout_marginTop="5dp"
        android:text="* Enter valid mobile number"
        android:textColor="@color/primary_bg_red"
        android:textSize="10sp"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/llPasswdOtp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_below="@+id/txtOtpSentMobile">

        <LinearLayout
            android:id="@+id/llOtp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <EditText
                android:id="@+id/edtOtp"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="10dp"
                android:background="@color/heighlight_bg_blue"
                android:hint="@string/otp"
                android:inputType="number"
                android:maxLength="6"
                android:padding="10dp"
                android:textColor="@color/white"
                android:textColorHint="@color/gray"
                android:textSize="14sp"
                android:visibility="visible"
                tools:visibility="visible"/>

            <LinearLayout
                android:id="@+id/rrltResendOtp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:visibility="visible"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/txtotpNotReceived"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/did_not_receive_otp"
                    android:textColor="@color/white"
                    android:textSize="10sp"
                    android:visibility="visible" />

                <TextView
                    android:id="@+id/txtResendOtp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:clickable="true"
                    android:enabled="false"
                    android:text="@string/resend_otp"
                    android:textColor="@color/primary_bg_red"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:visibility="visible" />


                <TextView
                    android:id="@+id/txtReSendOtpSeconds"
                    android:layout_width="@dimen/_15sdp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="00"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:visibility="visible" />


            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/llPasswd"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <EditText
                android:id="@+id/edtPassword"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="10dp"
                android:background="@color/heighlight_bg_blue"
                android:hint="@string/password"
                android:inputType="textPassword"
                android:padding="10dp"
                android:textColor="@color/white"
                android:textColorHint="@color/gray"
                android:textSize="14sp"
                android:visibility="visible" />

            <TextView
                android:id="@+id/txtForgotPassword"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_gravity="end"
                android:clickable="true"
                android:focusable="true"
                android:enabled="true"
                android:text="@string/forgot_passwd"
                android:textColor="@color/primary_bg_red"
                android:textSize="12sp"
                android:textStyle="bold"
                android:visibility="visible" />
        </LinearLayout>

    </LinearLayout>


    <ProgressBar
        android:id="@+id/progressbar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/edtOtp"
        android:backgroundTint="@color/primary_bg_red"
        android:indeterminateTint="@color/primary_bg_red"
        android:max="100"
        android:progress="45"
        android:progressTint="@color/primary_bg_red"
        android:visibility="gone" />


    <TextView
        android:id="@+id/txtValidateOTP"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/progressbar"
        android:layout_marginTop="5dp"
        android:text="* Enter valid OTP"
        android:textColor="@color/primary_bg_red"
        android:textSize="10sp"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/rrltCircleBtn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/llPasswdOtp"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <Button
            android:id="@+id/btnLogin"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="20dp"
            android:background="@drawable/button_shape_disabled"
            android:enabled="false"
            android:gravity="center_horizontal|center_vertical"
            android:text="@string/get_otp"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:theme="@style/PrimaryButton" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/llSignUp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_below="@id/rrltCircleBtn"
        android:gravity="center"
        android:padding="8dp"
        android:layout_marginTop="30dp">

        <TextView
            android:id="@+id/txtSignUp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="@string/sign_up_txt"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginEnd="10dp"
            android:visibility="visible" />

        <TextView
            android:id="@+id/txtSignUpBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="true"
            android:focusable="true"
            android:enabled="true"
            android:text="@string/sign_up_btn_text"
            android:textColor="@color/primary_bg_red"
            android:textSize="14sp"
            android:textStyle="bold"
            android:visibility="visible" />

    </LinearLayout>

    <RelativeLayout
        android:id="@+id/relativeLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:gravity="center">


        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgFooterLogo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="10dp"
            app:srcCompat="@drawable/ws_logo" />

        <TextView
            android:id="@+id/txtPoweredBy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@+id/imgFooterLogo"
            android:text="@string/footer_powered_by"
            android:textColor="@color/white"
            android:textSize="10sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/txtPoweredBy"
            android:layout_toEndOf="@+id/imgFooterLogo"
            android:text="@string/footer_wonderslate"
            android:textColor="@color/white"
            android:textSize="15sp" />
    </RelativeLayout>

    <include
        layout="@layout/no_internet_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/relativeLayout"
        android:layout_marginBottom="5dp" />

    <ProgressBar
        android:id="@+id/avlLogin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="invisible"
        android:indeterminateTint="@color/primary_bg_red" />

</RelativeLayout>