<?xml version="1.0" encoding="utf-8"?>
<ScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_bg_dark"
    android:fillViewport="true"
    android:fitsSystemWindows="true">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">



<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/primary_bg_dark"
    android:orientation="vertical"
    tools:context="com.wonderslate.prepjoy.ui.login.LoginActivity">


    <include
        android:id="@+id/header"
        layout="@layout/header_language_change"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <RelativeLayout
        android:id="@+id/rrltUserProfile"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:paddingTop="15dp">

        <RelativeLayout
            android:id="@+id/userImageContainerLayout"
            android:layout_width="180dp"
            android:layout_height="180dp"

            android:layout_centerInParent="true"
            android:layout_marginBottom="10dp">

            <FrameLayout
                android:id="@+id/userImageLayout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                android:background="@drawable/circle_background"
                android:clickable="true"
                android:elevation="5dp"
                android:foreground="@drawable/normal_selectable_foreground">

                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/userImageView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop" />
            </FrameLayout>

            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:layout_marginEnd="12dp"
                android:layout_marginRight="12dp"
                android:layout_marginBottom="12dp"
                android:background="@drawable/circle_bg_red"
                android:elevation="8dp">

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="18dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:backgroundTint="@color/white"
                    android:scaleType="fitXY"

                    app:srcCompat="@drawable/edit_icon_white"
                    app:tint="@color/white" />
            </FrameLayout>


        </RelativeLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:gravity="center"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/imgPcommanderIcon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:srcCompat="@drawable/commander_icon"/>


            <TextView
                android:id="@+id/textBadge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="pCommander"
                android:textColor="@color/white"
                android:textSize="12sp" />
        </LinearLayout>

    </RelativeLayout>


    <LinearLayout
        android:id="@+id/lltUserPerformance"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary_bg_dark"
        android:orientation="horizontal"
        android:padding="10dp">

        <LinearLayout
            android:id="@+id/lltPoints"
            android:layout_width="10dp"
            android:layout_height="60dp"
            android:layout_gravity="center"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="4dp"
            android:layout_weight="1"
            android:background="@drawable/card_view_bg"
            android:gravity="center"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textLifeTimePoints"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="true"
                    android:text="138"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/txtLifetime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="4dp"
                    android:clickable="true"
                    android:text="pts"
                    android:textColor="@color/white"
                    android:textSize="8sp" />
            </LinearLayout>


            <TextView
                android:id="@+id/txtMonth"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="@string/lifetime"
                android:textColor="@color/primary_bg_red"
                android:textSize="8sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/lltMedals"
            android:layout_width="10dp"
            android:layout_height="60dp"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="10dp"
            android:layout_weight="1"
            android:background="@drawable/card_view_bg"
            android:gravity="center"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textLifeTimeMedals"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="true"
                    android:text="138"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/imgMonthMedal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="4dp"
                    android:clickable="true"
                    android:textColor="@color/white"
                    android:textSize="8dp"
                    app:srcCompat="@drawable/medal_small_icon" />

            </LinearLayout>

            <TextView
                android:id="@+id/txtMonthMedals"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/lifetime"
                android:textColor="@color/primary_bg_red"
                android:textSize="8sp" />

        </LinearLayout>

    </LinearLayout>

    <EditText
        android:id="@+id/edtName"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="10dp"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:hint="Name"
        android:textColorHint="@color/gray"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:padding="10dp"
        android:singleLine="true"
        android:maxLines="1"
        android:nextFocusDown="@+id/spinnerstate"
        android:background="@color/heighlight_bg_blue" />

    <TextView
        android:id="@+id/txtvalidateName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/primary_bg_red"
        android:textSize="12sp"
        android:layout_marginTop="10dp"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:visibility="gone"
        android:text="Please enter name"/>


    <androidx.appcompat.widget.AppCompatSpinner
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:id="@+id/spinnerstate"
        android:layout_marginTop="10dp"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:nextFocusDown="@+id/spinnercity"
        android:background="@color/heighlight_bg_blue" />


    <TextView
        android:id="@+id/txtvalidateState"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/primary_bg_red"
        android:textSize="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"

        android:visibility="gone"
        android:text="Please select state"/>


    <androidx.appcompat.widget.AppCompatSpinner
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:id="@+id/spinnercity"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:nextFocusDown="@+id/btnupdateUser"
        android:background="@color/heighlight_bg_blue" />

    <TextView
        android:id="@+id/txtvalidatecity"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/primary_bg_red"
        android:textSize="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:visibility="gone"
        android:text="Please select district"/>


    <LinearLayout
        android:id="@+id/rrltCircleBtn"
        android:layout_marginTop="@dimen/_10sdp"
        android:layout_marginBottom="@dimen/_20sdp"
        android:layout_marginEnd="@dimen/_20sdp"
        android:layout_marginStart="@dimen/_20sdp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <Button
            android:id="@+id/btnupdateUser"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:background="@drawable/button_shape_default"
            android:gravity="center_horizontal|center_vertical"
            android:text="@string/save"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="@dimen/_10ssp"
            android:theme="@style/PrimaryButton"
            android:visibility="gone" />
    </LinearLayout>


</LinearLayout>

        <com.wang.avi.AVLoadingIndicatorView
            android:id="@+id/avlSignUp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/AVLoadingIndicatorView"
            android:visibility="invisible"
            android:layout_centerInParent="true"
            app:indicatorName="LineScalePulseOutRapidIndicator"
            app:indicatorColor="@color/primary_bg_red"/>
    </RelativeLayout>
</ScrollView>