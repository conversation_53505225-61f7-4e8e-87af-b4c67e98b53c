<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white"
    android:orientation="vertical"
    android:padding="16dp">

    <ImageView
        android:id="@+id/ivClose"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="6dp"
        android:foreground="?selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:src="@drawable/ic_close_black"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
        android:id="@+id/tvDesc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@color/ws_new_solid_color"
        android:typeface="normal"
        app:layout_constraintTop_toBottomOf="@+id/ivClose"
        android:layout_marginTop="16dp"
        tools:text="Some message"
        android:textSize="18sp"
        android:visibility="visible"
        app:ws_font_weight="bold" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/tvDesc"
        android:layout_marginTop="40dp">

        <Button
            android:id="@+id/btnBrowseMore"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="5dp"
            android:layout_marginBottom="16dp"
            android:layout_weight="1"
            android:background="@drawable/feedback_dialog_cancle_bg"
            android:elevation="8dp"
            android:gravity="center"
            android:textAllCaps="false"
            android:text="@string/continue_browsing"
            android:textColor="#8E8E8E"
            android:textSize="11sp" />

        <Button
            android:id="@+id/btnGotoCart"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:layout_marginBottom="16dp"
            android:layout_weight="1"
            android:background="@drawable/access_code_button"
            android:elevation="8dp"
            android:gravity="center"
            android:textAllCaps="false"
            android:text="@string/go_to_cart"
            android:textColor="@color/white"
            android:textSize="11sp" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>