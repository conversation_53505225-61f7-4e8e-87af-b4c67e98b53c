<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@color/primary_bg_dark">

    <androidx.cardview.widget.CardView
        android:id="@+id/btnBack"
        android:layout_width="@dimen/_32sdp"
        android:layout_height="@dimen/_32sdp"
        android:backgroundTint="@color/colorPrimary"
        app:cardCornerRadius="@dimen/_8sdp"
        android:foreground="?selectableItemBackground"
        android:layout_marginStart="@dimen/_10sdp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@drawable/ic_issue"
            android:contentDescription="Go back"/>
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/reportIssuePageTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Issue Reporter"
        android:textColor="@color/white"
        android:fontFamily="@font/font_medium"
        android:textSize="18sp"
        android:layout_toEndOf="@id/btnBack"
        android:padding="8dp"
        android:layout_marginStart="5dp"/>

    <LinearLayout
        android:id="@+id/crashDataLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/reportIssuePageTitle"
        android:orientation="vertical"
        android:paddingHorizontal="15dp">

        <TextView
            android:id="@+id/textViewHelpTxt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="We apologize for the inconvenience. An unexpected issue has occurred. Please help us make the experince better by reporting the issue. Thank you for your understanding."
            android:textColor="@color/white"
            android:fontFamily="@font/font_regular"
            android:textSize="14sp"
            android:layout_marginTop="15dp"/>

        <TextView
            android:id="@+id/textViewIssueTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Issue Details"
            android:textColor="@color/white"
            android:fontFamily="@font/font_medium"
            android:textSize="14sp"
            android:layout_marginTop="15dp"/>

        <TextView
            android:id="@+id/textViewUserId"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="User ID: "
            android:textColor="@color/white"
            android:fontFamily="@font/font_regular"
            android:textSize="14sp"
            android:layout_marginTop="15dp"/>

        <TextView
            android:id="@+id/textViewAppVersion"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="App Version: "
            android:textColor="@color/white"
            android:fontFamily="@font/font_regular"
            android:textSize="14sp"
            android:layout_marginTop="15dp"/>

        <TextView
            android:id="@+id/textViewAndroidVersion"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Android Version: "
            android:textColor="@color/white"
            android:fontFamily="@font/font_regular"
            android:textSize="14sp"
            android:layout_marginTop="20dp" />

        <TextView
            android:id="@+id/textViewPhoneModel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Phone Model/Make: "
            android:textColor="@color/white"
            android:fontFamily="@font/font_regular"
            android:textSize="14sp"
            android:layout_marginTop="15dp"/>


        <TextView
            android:id="@+id/textViewDateTime"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Date Time of Crash: "
            android:textColor="@color/white"
            android:fontFamily="@font/font_regular"
            android:textSize="14sp"
            android:layout_marginTop="15dp"/>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/textInputCommentsLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColorHint="@color/white"
            android:layout_marginTop="16dp">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/textInputComments"
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:hint="Additional Comments (Max 250 characters)"
                android:maxLines="7"
                android:inputType="textMultiLine"
                android:maxLength="250"
                android:textColor="@color/white"
                android:fontFamily="@font/font_regular"
                android:textSize="14sp"
                android:background="@color/colorPrimary"/>

        </com.google.android.material.textfield.TextInputLayout>

        <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
            android:id="@+id/buttonReportIssue"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="5dp"
            android:background="@drawable/buy_btn_enabled_back"
            android:elevation="8dp"
            android:gravity="center"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:text="Report Issue"
            android:fontFamily="@font/font_regular"
            android:paddingVertical="15dp"
            android:layout_marginTop="30dp"/>

    </LinearLayout>

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lottie_view"
        android:layout_width="300dp"
        android:layout_height="300dp"
        android:layout_gravity="center"
        android:visibility="gone"
        tools:visibility="gone"
        android:layout_centerInParent="true"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:lottie_rawRes="@raw/lottie_server" />

</RelativeLayout>
