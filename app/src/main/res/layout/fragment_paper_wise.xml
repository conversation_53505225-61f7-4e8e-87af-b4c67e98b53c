<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.wonderslate.prepjoy.news.PubFeedDetails"
    android:background="@color/primary_bg_dark">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/paper_wise_list"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.wang.avi.AVLoadingIndicatorView
        android:id="@+id/mergedNewsLoader"
        style="@style/AVLoadingIndicatorView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_gravity="center"
        android:visibility="gone"
        app:indicatorColor="@color/primary_bg_red"
        app:indicatorName="LineScalePulseOutRapidIndicator" />

</RelativeLayout>