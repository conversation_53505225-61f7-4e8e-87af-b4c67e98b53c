<?xml version="1.0" encoding="utf-8"?>
<androidx.swiperefreshlayout.widget.SwipeRefreshLayout
    android:id="@+id/swipeToRefresh"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_bg_dark">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

<RelativeLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:animateLayoutChanges="true">

    <RelativeLayout
        android:id="@+id/weekly_winner_parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="10dp"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="15dp"
        android:layout_marginStart="25dp"
        android:layout_marginEnd="25dp"
        android:layout_centerHorizontal="true"
        android:background="@drawable/bg_round_border"
        android:visibility="gone">
        <TextView
            android:id="@+id/weekly_winner_header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Previous Week Winners"
            android:textSize="14sp"
            android:textColor="@color/white"
            android:fontFamily="@font/poppins_medium"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="10dp"/>

        <Button
            android:layout_width="19dp"
            android:layout_height="19dp"
            android:layout_toEndOf="@id/weekly_winner_header"
            android:ems="2"
            android:layout_marginEnd="10dp"
            android:layout_marginStart="10dp"
            android:padding="@dimen/_5sdp"
            android:textColor="@color/white"
            android:background="@drawable/info_icon_bg"
            android:id="@+id/info_contest"
            android:elevation="5dp"/>

        <RelativeLayout
            android:id="@+id/previousWeekTopRanks"
            android:layout_width="wrap_content"
            android:layout_gravity="center"
            android:layout_centerHorizontal="true"
            android:gravity="center"
            android:layout_height="wrap_content"
            android:layout_marginTop="25dp">

            <RelativeLayout
                android:id="@+id/pwUser1Rank"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="35dp">

                <TextView
                    android:id="@+id/pwtxtRankVal1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:gravity="center"
                    android:text="#1"
                    android:textSize="10sp"
                    android:textColor="@color/white" />

                <ImageView
                    android:id="@+id/pwimgcrown"
                    android:layout_width="13dp"
                    android:layout_height="13dp"
                    android:layout_centerHorizontal="true"
                    android:gravity="center"
                    android:layout_marginBottom="3dp"
                    android:layout_below="@+id/pwtxtRankVal1"
                    android:src="@drawable/ic_crown"
                    android:visibility="gone"/>


                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/pwuserRankImage1"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_below="@+id/pwimgcrown"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/circle_background"
                    android:gravity="center"
                    android:padding="@dimen/_2sdp"
                    android:scaleType="centerCrop"
                    android:src="@drawable/avatar_bg_circle" />

                <TextView
                    android:id="@+id/pwtxtUserName1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/pwuserRankImage1"
                    android:layout_alignStart="@id/pwuserRankImage1"
                    android:layout_alignEnd="@id/pwuserRankImage1"
                    android:layout_centerHorizontal="true"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="1"
                    android:text=""
                    android:textColor="@color/white"
                    android:textSize="8sp"
                    android:textStyle="bold" />


                <TextView
                    android:id="@+id/pwtxtUserState1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/pwtxtUserName1"
                    android:layout_alignStart="@id/pwuserRankImage1"
                    android:layout_alignEnd="@id/pwuserRankImage1"
                    android:layout_centerHorizontal="true"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="1"
                    android:text=""
                    android:textColor="@color/white"
                    android:textSize="8sp" />


                <TextView
                    android:id="@+id/pwtxtUserPoints1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/pwtxtUserState1"
                    android:layout_centerHorizontal="true"
                    android:gravity="center"
                    android:text=""
                    android:textSize="10sp"
                    android:textColor="@color/white"
                    android:textStyle="bold"/>


            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/pwUser2Rank"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="35dp"
                android:layout_toEndOf="@id/pwUser1Rank"
                android:layout_alignBaseline="@id/pwUser2Rank"
                android:layout_alignParentBottom="true">


                <TextView
                    android:id="@+id/pwtxtRankVal2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:gravity="center"
                    android:layout_centerHorizontal="true"
                    android:text="#2"
                    android:textSize="10sp"/>

                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/pwuserRankImage2"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_below="@+id/pwtxtRankVal2"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/circle_background"
                    android:gravity="center"
                    android:padding="@dimen/_2sdp"
                    android:scaleType="centerCrop"
                    android:src="@drawable/avatar_bg_circle" />

                <TextView
                    android:id="@+id/pwtxtUserName2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/pwuserRankImage2"
                    android:layout_alignStart="@id/pwuserRankImage2"
                    android:layout_alignEnd="@id/pwuserRankImage2"
                    android:layout_centerHorizontal="true"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="1"
                    android:text=""
                    android:textColor="@color/white"
                    android:textSize="8sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/pwtxtUserState2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/pwtxtUserName2"
                    android:layout_alignStart="@id/pwuserRankImage2"
                    android:layout_alignEnd="@id/pwuserRankImage2"
                    android:layout_centerHorizontal="true"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="1"
                    android:text=""
                    android:textColor="@color/white"
                    android:textSize="8sp" />


                <TextView
                    android:id="@+id/pwtxtUserPoints2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/pwtxtUserState2"
                    android:layout_centerHorizontal="true"
                    android:gravity="center"
                    android:text=""
                    android:textSize="10sp"
                    android:textColor="@color/white"
                    android:textStyle="bold"/>


            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/pwUser3Rank"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBaseline="@id/pwUser2Rank"
                android:layout_alignParentBottom="true"
                android:layout_toEndOf="@+id/pwUser2Rank">


                <TextView
                    android:id="@+id/pwtxtRankVal3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:gravity="center"
                    android:text="#3"
                    android:textSize="10sp"
                    android:textColor="@color/white" />

                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/pwuserRankImage3"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_below="@+id/pwtxtRankVal3"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/circle_background"
                    android:gravity="center"
                    android:padding="@dimen/_2sdp"
                    android:scaleType="centerCrop"
                    android:src="@drawable/avatar_bg_circle" />

                <TextView
                    android:id="@+id/pwtxtUserName3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/pwuserRankImage3"
                    android:layout_alignStart="@id/pwuserRankImage3"
                    android:layout_alignEnd="@id/pwuserRankImage3"
                    android:layout_centerHorizontal="true"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="1"
                    android:text=""
                    android:textColor="@color/white"
                    android:textSize="8sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/pwtxtUserState3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/pwtxtUserName3"
                    android:layout_alignStart="@id/pwuserRankImage3"
                    android:layout_alignEnd="@id/pwuserRankImage3"
                    android:layout_centerHorizontal="true"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="1"
                    android:text=""
                    android:textColor="@color/white"
                    android:textSize="8sp" />


                <TextView
                    android:id="@+id/pwtxtUserPoints3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/pwtxtUserState3"
                    android:layout_centerHorizontal="true"
                    android:gravity="center"
                    android:text=""
                    android:textSize="10sp"
                    android:textColor="@color/white"
                    android:textStyle="bold"/>


            </RelativeLayout>

        </RelativeLayout>
    </RelativeLayout>


<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_below="@id/weekly_winner_parent"
    android:id="@+id/container_details"
    android:background="@color/primary_bg_dark"
    android:orientation="vertical"
    tools:context="com.wonderslate.prepjoy.ui.login.LoginActivity"
    >

    <RelativeLayout
        android:id="@+id/rrltUserProfile"
        android:layout_width="match_parent"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:paddingTop="15dp"
        android:visibility="gone"
        android:layout_height="wrap_content">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_toLeftOf="@id/linearBadge">


        <FrameLayout
            android:id="@+id/userImageLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/circle_background"
            android:foreground="@drawable/normal_selectable_foreground">

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/userImageView"
                android:layout_width="50dp"
                android:src="@drawable/avatar_bg_circle"
                android:layout_height="50dp"
                android:scaleType="centerCrop" />
        </FrameLayout>

        <TextView
            android:id="@+id/txtUserProfilename"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="10dp"
            android:layout_toEndOf="@id/userImageLayout"
            android:text="Tony Mark"
            android:textColor="@color/white"
            android:textSize="24sp"
            android:textStyle="bold"
            android:maxLines="2"/>

        <TextView
            android:id="@+id/txtUsername"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@id/userImageLayout"
            android:layout_below="@+id/txtUserProfilename"
            android:text="tonymark12345"
            android:layout_marginStart="7dp"
            android:gravity="center"
            android:textColor="@color/primary_bg_red"
            android:textSize="12sp" />

        </RelativeLayout>

        <LinearLayout
            android:id="@+id/linearBadge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginStart="8dp"
            android:layout_alignParentEnd="true">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/imgPcommanderIcon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:srcCompat="@drawable/commander_icon" />


            <TextView
                android:id="@+id/textBadge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="pCommander"
                android:textColor="@color/white"
                android:textSize="12sp"/>
        </LinearLayout>

    </RelativeLayout>

    <LinearLayout
        android:visibility="gone"
        android:id="@+id/lltUserPerformance"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="10dp"
        android:background="@color/primary_bg_dark"
        android:layout_marginTop="8dp">

        <LinearLayout
            android:id="@+id/lltPoints"
            android:layout_width="10dp"
            android:layout_height="60dp"
            android:layout_weight="1"
            android:gravity="center"
            android:layout_gravity="center"
            android:background="@drawable/card_view_bg"
            android:layout_marginEnd="4dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_gravity="center"
                android:gravity="center">

                <TextView
                    android:id="@+id/textLifeTimePoints"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="true"
                    android:text="138"
                    android:textColor="@color/white"
                    android:textSize="17sp"
                    android:textStyle="bold" />
                <TextView
                    android:id="@+id/txtLifetime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="true"
                    android:text="pts"
                    android:layout_marginLeft="4dp"
                    android:textColor="@color/white"
                    android:textSize="8sp" />
            </LinearLayout>


            <TextView
                android:id="@+id/txtMonth"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/lifetime"
                android:gravity="center"
                android:layout_gravity="center"
                android:textColor="@color/primary_bg_red"
                android:textSize="8sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/lltMedals"
            android:layout_width="10dp"
            android:layout_height="60dp"
            android:layout_weight="1"
            android:gravity="center"
            android:layout_marginStart="4dp"
            android:background="@drawable/card_view_bg"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_gravity="center"
                android:gravity="center">

                <TextView
                    android:id="@+id/textLifeTimeMedals"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="true"
                    android:text="138"
                    android:textColor="@color/white"
                    android:textSize="17sp"
                    android:textStyle="bold" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/imgMonthMedal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="true"
                    android:layout_marginLeft="4dp"
                    app:srcCompat="@drawable/medal_small_icon"
                    android:textColor="@color/white"
                    android:textSize="8dp" />

            </LinearLayout>

            <TextView
                android:id="@+id/txtMonthMedals"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/lifetime"
                android:gravity="center"
                android:textColor="@color/primary_bg_red"
                android:textSize="8sp" />

        </LinearLayout>

    </LinearLayout>

    <TextView
        android:id="@+id/textNoRankRecord"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/no_record"
        android:layout_marginTop="80dp"
        android:textColor="@color/white"
        android:gravity="center"
        android:textSize="18sp"
        android:textStyle="bold"
        android:visibility="gone"
        android:layout_centerInParent="true"/>

    <RelativeLayout
        android:id="@+id/rrltTopRanks"
        android:layout_width="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:layout_height="wrap_content">

        <RelativeLayout
            android:id="@+id/rrltUser2Rank"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="-20dp"
            android:layout_alignBaseline="@id/rrltUser2Rank"
            android:layout_alignParentBottom="true">


            <TextView
                android:id="@+id/txtRankVal2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:gravity="center"
                android:layout_centerHorizontal="true"
                android:text="2"/>

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/userRankImage2"
                android:layout_width="@dimen/_70sdp"
                android:layout_height="@dimen/_70sdp"
                android:layout_below="@+id/txtRankVal2"
                android:layout_centerHorizontal="true"
                android:background="@drawable/circle_background"
                android:gravity="center"
                android:padding="@dimen/_2sdp"
                android:scaleType="centerCrop"
                android:src="@drawable/avatar_bg_circle" />

            <TextView
                android:id="@+id/txtUserName2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_below="@+id/userRankImage2"
                android:layout_alignStart="@id/userRankImage2"
                android:layout_alignEnd="@id/userRankImage2"
                android:layout_centerHorizontal="true"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"
                android:paddingHorizontal="5dp"
                android:text=""
                android:textColor="@color/white"
                android:textSize="@dimen/_8sdp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/txtUserState2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_below="@+id/txtUserName2"
                android:layout_alignStart="@id/userRankImage2"
                android:layout_alignEnd="@id/userRankImage2"
                android:layout_centerHorizontal="true"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"
                android:paddingHorizontal="5dp"
                android:text=""
                android:textColor="@color/white"
                android:textSize="@dimen/_8sdp" />


            <TextView
                android:id="@+id/txtUserPoints2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/txtUserState2"
                android:layout_centerHorizontal="true"
                android:gravity="center"
                android:text=""
                android:textColor="@color/white" />


        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/rrltUser1Rank"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@+id/rrltUser2Rank"
            >


            <TextView
                android:id="@+id/txtRankVal1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:gravity="center"
                android:text="1"
                android:textColor="@color/white" />

            <ImageView
                android:id="@+id/imgcrown"
                android:layout_width="13dp"
                android:layout_height="13dp"
                android:layout_centerHorizontal="true"
                android:gravity="center"
                android:layout_marginBottom="3dp"
                android:layout_below="@+id/txtRankVal1"
                android:src="@drawable/ic_crown"/>


            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/userRankImage1"
                android:layout_width="@dimen/_100sdp"
                android:layout_height="@dimen/_100sdp"
                android:layout_below="@+id/imgcrown"
                android:layout_centerHorizontal="true"
                android:background="@drawable/circle_background"
                android:gravity="center"
                android:padding="@dimen/_2sdp"
                android:scaleType="centerCrop"
                android:src="@drawable/avatar_bg_circle" />

            <TextView
                android:id="@+id/txtUserName1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_below="@+id/userRankImage1"
                android:layout_alignStart="@id/userRankImage1"
                android:layout_alignEnd="@id/userRankImage1"
                android:layout_centerHorizontal="true"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"
                android:paddingHorizontal="20dp"
                android:text=""
                android:textColor="@color/white"
                android:textSize="@dimen/_8sdp"
                android:textStyle="bold" />


            <TextView
                android:id="@+id/txtUserState1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_below="@+id/txtUserName1"
                android:layout_alignStart="@id/userRankImage1"
                android:layout_alignEnd="@id/userRankImage1"
                android:layout_centerHorizontal="true"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"
                android:paddingHorizontal="20dp"
                android:text=""
                android:textColor="@color/white"
                android:textSize="@dimen/_8sdp" />


            <TextView
                android:id="@+id/txtUserPoints1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/txtUserState1"
                android:layout_centerHorizontal="true"
                android:gravity="center"
                android:text=""
                android:textColor="@color/white" />


        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rrltUser3Rank"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBaseline="@id/rrltUser2Rank"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="-20dp"
            android:layout_toEndOf="@+id/rrltUser1Rank">


            <TextView
                android:id="@+id/txtRankVal3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:gravity="center"
                android:text="3"
                android:textColor="@color/white" />

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/userRankImage3"
                android:layout_width="@dimen/_70sdp"
                android:layout_height="@dimen/_70sdp"
                android:layout_below="@+id/txtRankVal3"
                android:layout_centerHorizontal="true"
                android:background="@drawable/circle_background"
                android:gravity="center"
                android:padding="@dimen/_2sdp"
                android:scaleType="centerCrop"
                android:src="@drawable/avatar_bg_circle" />

            <TextView
                android:id="@+id/txtUserName3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_below="@+id/userRankImage3"
                android:layout_alignStart="@id/userRankImage3"
                android:layout_alignEnd="@id/userRankImage3"
                android:layout_centerHorizontal="true"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"
                android:paddingHorizontal="5dp"
                android:text=""
                android:textColor="@color/white"
                android:textSize="@dimen/_8sdp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/txtUserState3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_below="@+id/txtUserName3"
                android:layout_alignStart="@id/userRankImage3"
                android:layout_alignEnd="@id/userRankImage3"
                android:layout_centerHorizontal="true"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"
                android:paddingHorizontal="5dp"
                android:text=""
                android:textColor="@color/white"
                android:textSize="@dimen/_8sdp" />


            <TextView
                android:id="@+id/txtUserPoints3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/txtUserState3"
                android:layout_centerHorizontal="true"
                android:gravity="center"
                android:text=""
                android:textColor="@color/white" />


        </RelativeLayout>



    </RelativeLayout>



    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="5dp"
        android:background="@drawable/bg_round_white"
        app:tabBackground="@drawable/tab_background"
        app:tabIndicatorHeight="0dp"
        app:tabMode="fixed"
        app:tabSelectedTextColor="@color/white"
        app:tabTextAppearance="@style/HomeTabStyle"
        app:tabTextColor="@color/primary_bg_red" />

    <RelativeLayout
        android:id="@+id/rlDatePicker"
        android:layout_width="150dp"
        android:layout_height="35dp"
        android:orientation="horizontal"
        android:layout_gravity="center"
        android:layout_marginEnd="8dp">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imagePreviousDate"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="8dp"
            android:rotation="180"
            app:srcCompat="@drawable/ic_arrow"
            tools:ignore="ContentDescription"
            android:visibility="gone"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imageNextDate"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="8dp"
            app:srcCompat="@drawable/ic_arrow"
            tools:ignore="ContentDescription"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/textDate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:drawableLeft="@drawable/ic_calender"
            android:drawablePadding="7dp"
            android:drawableTint="@color/primary_bg_red"
            android:textColor="@color/primary_bg_red"
            android:textSize="18sp"
            android:textStyle="bold"
            tools:ignore="RelativeOverlap"
            tools:text="11-10-2021" />

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/linearList"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:layout_margin="8dp"
        android:visibility="gone">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.6"
            android:text="@string/profile"
            android:textColor="@color/white"
            android:textSize="14sp"
            />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.5"
            android:text="@string/rank"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:layout_marginStart="8dp"/>

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/user"
            android:gravity="start"
            android:textColor="@color/white"
            android:textSize="14sp"/>

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.6"
            android:text="@string/score"
            android:gravity="start"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:layout_marginEnd="8dp"/>

    </LinearLayout>





    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/horizontalcardviewpager"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"/>




</LinearLayout>

    <TextView
        android:id="@+id/textComingSoon"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/coming_soon"
        android:layout_marginBottom="80dp"
        android:textColor="@color/white"
        android:gravity="center"
        android:textSize="18sp"
        android:textStyle="bold"
        android:visibility="gone"
        android:layout_centerInParent="true"/>

    <TextView
        android:id="@+id/textNoRecord"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/no_record"
        android:layout_marginTop="@dimen/_80sdp"
        android:textColor="@color/white"
        android:gravity="center"
        android:textSize="18sp"
        android:textStyle="bold"
        android:visibility="gone"
        android:layout_below="@id/container_details"
        android:layout_centerInParent="true"/>

    <com.wang.avi.AVLoadingIndicatorView
        android:id="@+id/progressLoader"
        style="@style/AVLoadingIndicatorView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        app:indicatorColor="@color/primary_bg_red"
        app:indicatorName="LineScalePulseOutRapidIndicator"
        android:elevation="10dp"
        android:visibility="gone"/>

</RelativeLayout>
    </ScrollView>
</androidx.swiperefreshlayout.widget.SwipeRefreshLayout>