<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_bg_dark"
    android:orientation="vertical"
    tools:context="com.wonderslate.prepjoy.ui.login.LoginActivity">


    <LinearLayout
        android:id="@+id/linearHead"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/_5sdp"
        android:background="@color/primary_bg_dark"
        android:orientation="vertical"
        tools:context="com.wonderslate.prepjoy.ui.login.LoginActivity">

        <include
            android:id="@+id/user_statistics"
            android:visibility="visible"
            layout="@layout/layout_user_quiz_analysis" />

        <RelativeLayout
            android:id="@+id/rrltUserProfile"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10sdp"
            android:layout_marginEnd="@dimen/_10sdp"
            android:paddingTop="2dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_toStartOf="@id/linearBadge">

                <FrameLayout
                    android:id="@+id/userImageLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:background="@drawable/circle_background"
                    android:foreground="@drawable/normal_selectable_foreground">

                    <de.hdodenhof.circleimageview.CircleImageView
                        android:id="@+id/userImageView"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:scaleType="centerCrop" />
                </FrameLayout>

                <TextView
                    android:id="@+id/txtUserProfilename"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="5dp"
                    android:layout_toStartOf="@+id/lltUserPerformance"
                    android:layout_toEndOf="@id/userImageLayout"
                    android:gravity="center|left"
                    android:maxWidth="@dimen/_90sdp"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:text="Tony Markqweeqweqweqeqweqwqeqwe"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/txtUsername"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/txtUserProfilename"
                    android:layout_marginStart="7dp"
                    android:layout_toEndOf="@id/userImageLayout"
                    android:gravity="center"
                    android:text="tonymark12345"
                    android:textColor="@color/primary_bg_red"
                    android:textSize="12sp"
                    android:visibility="gone" />

                <LinearLayout
                    android:id="@+id/lltUserPerformance"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignParentEnd="true"
                    android:layout_marginStart="5dp"
                    android:layout_marginTop="3dp"

                    android:background="@color/primary_bg_dark"
                    android:gravity="center|right"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:id="@+id/lltPoints"
                        android:layout_width="50dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center"
                        android:layout_marginEnd="@dimen/_5sdp"
                        android:background="@drawable/bg_round_scrore"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/textLifeTimePoints"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:clickable="true"
                                android:focusable="true"
                                android:text="138"
                                android:textColor="@color/white"
                                android:textSize="@dimen/_8ssp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/txtLifetime"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/_4sdp"
                                android:clickable="true"
                                android:focusable="true"
                                android:text="pts"
                                android:textColor="@color/white"
                                android:textSize="@dimen/_6ssp" />
                        </LinearLayout>


                        <TextView
                            android:id="@+id/txtMonth"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:text="@string/lifetime"
                            android:textColor="@color/primary_bg_red"
                            android:textSize="@dimen/_8ssp"
                            android:visibility="gone" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/lltMedals"
                        android:layout_width="50dp"
                        android:layout_height="20dp"
                        android:layout_marginStart="@dimen/_5sdp"
                        android:background="@drawable/bg_round_scrore"
                        android:gravity="center"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/textLifeTimeMedals"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:clickable="true"
                                android:focusable="true"
                                android:text="138"
                                android:textColor="@color/white"
                                android:textSize="8sp"
                                android:textStyle="bold" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/imgMonthMedal"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/_4sdp"
                                android:clickable="true"
                                android:focusable="true"
                                android:textColor="@color/white"
                                android:textSize="@dimen/_8ssp"
                                app:srcCompat="@drawable/medal_small_icon" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/txtMonthMedals"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="@string/lifetime"
                            android:textColor="@color/primary_bg_red"
                            android:textSize="@dimen/_8ssp"
                            android:visibility="gone" />

                    </LinearLayout>

                </LinearLayout>

            </RelativeLayout>

            <LinearLayout
                android:id="@+id/linearBadge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerInParent="true"
                android:layout_marginStart="8dp"
                android:gravity="center"

                android:orientation="vertical">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/imgPcommanderIcon"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    app:srcCompat="@drawable/commander_icon" />


                <TextView
                    android:id="@+id/textBadge"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="pCommander"
                    android:textColor="@color/white"
                    android:textSize="8sp" />
            </LinearLayout>

        </RelativeLayout>


        <androidx.appcompat.widget.Toolbar
            android:id="@+id/dashboard_toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone">

            <ImageView
                android:id="@+id/dashboard_back"
                android:layout_width="@dimen/_20sdp"
                android:layout_height="@dimen/_20sdp"
                android:contentDescription="Back to Options"
                android:src="@drawable/ic_back" />

            <TextView
                android:id="@+id/dashboard_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:text="Title"
                android:textColor="@color/white"
                android:textSize="@dimen/_14ssp" />

        </androidx.appcompat.widget.Toolbar>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/daily_tests_recyclerview"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:visibility="gone" />

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="10dp"
            android:background="@drawable/bg_round_white"
            android:visibility="gone"
            app:tabBackground="@drawable/tab_background"
            app:tabIndicatorHeight="0dp"
            app:tabMode="fixed"
            app:tabSelectedTextColor="@color/white"
            app:tabTextAppearance="@style/HomeTabStyle"
            app:tabTextColor="@color/primary_bg_red" />


    </LinearLayout>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nestedScrollview"
        android:layout_width="match_parent"
        android:layout_below="@+id/linearHead"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@color/primary_bg_dark">


            <LinearLayout
                android:id="@+id/lltDashboard"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/primary_bg_dark"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/primary_bg_dark"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_5sdp"
                        android:orientation="vertical">

                        <LinearLayout
                            android:id="@+id/rrltCircleBtn"
                            android:layout_marginEnd="@dimen/_20sdp"
                            android:layout_marginStart="@dimen/_20sdp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_horizontal"
                            android:visibility="gone"
                            android:orientation="vertical">

                            <Button
                                android:id="@+id/btnupdateUser"
                                android:layout_width="match_parent"
                                android:layout_height="40dp"
                                android:background="@drawable/button_shape_default"
                                android:gravity="center_horizontal|center_vertical"
                                android:text="Quiz"
                                android:textAllCaps="false"
                                android:textColor="@color/white"
                                android:textSize="@dimen/_10ssp"
                                android:theme="@style/PrimaryButton"
                                android:visibility="visible" />
                        </LinearLayout>


                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Last Quiz"
                            android:visibility="gone"
                            android:layout_marginStart="@dimen/_10sdp"
                            android:layout_marginEnd="@dimen/_10sdp"
                            android:textColor="@color/white"
                            />


                        <com.facebook.shimmer.ShimmerFrameLayout
                            android:id="@+id/item_home_options_new_shimmer"
                            android:layout_width="match_parent"
                            android:layout_marginTop="@dimen/_5sdp"
                            android:layout_marginStart="@dimen/_10sdp"
                            android:layout_marginEnd="@dimen/_10sdp"
                            android:visibility="gone"
                            android:layout_height="60dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="60dp"
                                android:weightSum="3.5"
                                android:background="@drawable/home_card_bg"
                                android:orientation="horizontal">

                            </LinearLayout>


                        </com.facebook.shimmer.ShimmerFrameLayout>


                        <LinearLayout
                            android:id="@+id/lltLastQuiz"
                            android:layout_width="match_parent"
                            android:layout_height="60dp"
                            android:weightSum="3.5"
                            android:visibility="gone"
                            android:layout_marginTop="@dimen/_5sdp"
                            android:layout_marginStart="@dimen/_10sdp"
                            android:layout_marginEnd="@dimen/_10sdp"
                            android:background="@drawable/home_card_bg"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:id="@+id/lltLastQuizData"
                                android:layout_width="match_parent"
                                android:orientation="horizontal"
                                android:layout_gravity="center"
                                android:visibility="visible"
                                android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/txtQuiztitle"
                                android:layout_width="80dp"
                                android:layout_height="wrap_content"
                                android:text="Last Quiz"
                                android:gravity="center"
                                android:layout_weight="1"
                                android:layout_gravity="center"
                                android:textSize="@dimen/_8sdp"
                                android:layout_marginEnd="@dimen/_20sdp"
                                android:layout_marginStart="@dimen/_20sdp"
                                android:textColor="@color/white"
                                />
                            <View
                                android:layout_height="20dp"
                                android:layout_gravity="center"
                                style="@style/Divider.Vertical"/>

                            <TextView
                                android:id="@+id/txtScore"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:layout_gravity="center"
                                android:text="Last Quiz"
                                android:layout_weight="1"
                                android:textSize="@dimen/_8sdp"
                                android:layout_marginEnd="@dimen/_20sdp"
                                android:layout_marginStart="@dimen/_20sdp"
                                android:textColor="@color/white"
                                />

                            <View
                                android:layout_height="20dp"
                                android:layout_gravity="center"
                                style="@style/Divider.Vertical"/>
                            <TextView
                                android:id="@+id/txtQuizStatus"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:layout_weight="1"
                                android:layout_gravity="center"
                                android:text="Last Quiz"
                                android:textSize="@dimen/_8sdp"
                                android:layout_marginEnd="@dimen/_20sdp"
                                android:layout_marginStart="@dimen/_20sdp"
                                android:textColor="@color/white"
                               />


                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginEnd="@dimen/_5sdp"
                                android:src="@drawable/ic_arrow_right_small"
                                />

                            </LinearLayout>

                            <TextView
                                android:id="@+id/txtNoLastQuiz"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="@color/white"
                                android:gravity="center"
                                android:visibility="gone"
                                android:layout_gravity="center"
                                android:textSize="@dimen/_8sdp"
                                android:text="You have not attempted the quiz"/>



                        </LinearLayout>

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvLearn"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_5sdp"
                        android:layout_marginStart="@dimen/_10sdp"
                        android:layout_marginEnd="@dimen/_10sdp"
                        android:text="Learn"
                        android:textColor="@color/white"
                        android:textSize="10.75sp"
                        android:fontFamily="@font/poppins_bold"/>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/fragment_home_options_recycler"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:clipToPadding="false"
                        android:adjustViewBounds="true"
                        android:scaleType="centerInside"
                        android:layout_marginStart="@dimen/_10sdp"
                        android:layout_marginEnd="@dimen/_10sdp"
                        tools:listitem="@layout/item_home_options"
                        tools:itemCount="3"/>

                    <TextView
                        android:id="@+id/tvCommunity"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="23dp"
                        android:layout_marginStart="@dimen/_10sdp"
                        android:layout_marginEnd="@dimen/_10sdp"
                        android:text="Community"
                        android:textColor="@color/white"
                        android:textSize="10.75sp"
                        android:fontFamily="@font/poppins_bold"/>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/fragment_home_options2_recycler"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:clipToPadding="false"
                        android:adjustViewBounds="true"
                        android:scaleType="centerInside"
                        android:layout_marginStart="@dimen/_10sdp"
                        android:layout_marginEnd="@dimen/_10sdp"
                        tools:listitem="@layout/item_home_options"
                        tools:itemCount="3"/>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="23dp"
                        android:layout_marginStart="@dimen/_10sdp"
                        android:layout_marginEnd="@dimen/_10sdp"
                        android:text="@string/home_trending_game_ebooks"
                        android:textColor="@color/white"
                        android:textSize="10.75sp"
                        android:fontFamily="@font/poppins_bold"/>


                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">


                        <RelativeLayout
                            android:id="@+id/layout_top"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:animateLayoutChanges="true"
                            android:clickable="true"
                            android:focusable="true"
                            android:focusableInTouchMode="true"
                            android:orientation="vertical">


                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/recycleBooks"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@color/colorShopBg"
                                android:clipToPadding="false"
                                android:nestedScrollingEnabled="false"
                                android:paddingStart="12dp"
                                android:paddingEnd="12dp"
                                android:paddingBottom="10dp"
                                android:scrollbars="none"
                                tools:listitem="@layout/item_ebook_info_new" />


                            <ProgressBar
                                android:id="@+id/idPBLoading"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_below="@+id/recycleBooks"
                                android:visibility="gone" />

                            <com.facebook.shimmer.ShimmerFrameLayout
                                android:id="@+id/shimmerBooks"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:padding="4dp"
                                android:visibility="gone"
                                tools:visibility="gone">

                                <include
                                    layout="@layout/shimmer_empty_book_layout"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content" />
                            </com.facebook.shimmer.ShimmerFrameLayout>

                        </RelativeLayout>

                    </RelativeLayout>


                    <LinearLayout
                        android:id="@+id/noDatalayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <com.airbnb.lottie.LottieAnimationView
                            android:id="@+id/lottieEmpty"
                            android:layout_width="150dp"
                            android:layout_height="150dp"
                            app:lottie_autoPlay="true"
                            app:lottie_loop="true"
                            app:lottie_rawRes="@raw/empty_lottie" />

                        <com.ws.core_ui.custom_views.WSTextView
                            android:id="@+id/emptytextview"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:padding="3dp"
                            android:text="@string/home_books_unavailable"
                            android:textColor="@color/colorShopErrorText"
                            android:textSize="12sp"
                            android:typeface="normal" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/linearNoData"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <com.airbnb.lottie.LottieAnimationView
                            android:layout_width="150dp"
                            android:layout_height="150dp"
                            app:lottie_autoPlay="true"
                            app:lottie_loop="true"
                            app:lottie_rawRes="@raw/empty_lottie" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/home_no_books"
                            android:textColor="@color/colorShopErrorText"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="@string/home_books_search_not_found"
                            android:textColor="@color/colorShopErrorTextSecondary"
                            android:textSize="12sp" />
                    </LinearLayout>


                    <com.wang.avi.AVLoadingIndicatorView
                        android:id="@+id/lvCenter"
                        style="@style/AVLoadingIndicatorView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="top|center"
                        android:visibility="visible"
                        app:indicatorColor="@color/colorAccent"
                        app:indicatorName="LineScalePulseOutRapidIndicator" />


                    <TextView
                        android:id="@+id/txtshowMore"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:visibility="gone"
                        android:text="@string/home_books_show_more"
                        android:textColor="@color/primary_bg_red"
                        android:textStyle="bold" />

                </LinearLayout>


            </LinearLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp">

                <ProgressBar
                    android:id="@+id/progressLoader"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:elevation="10dp"
                    android:visibility="gone"
                    app:indicatorColor="@color/primary_bg_red"
                    app:indicatorName="LineScalePulseOutRapidIndicator" />

                <androidx.viewpager2.widget.ViewPager2
                    android:id="@+id/viewPager1"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_300sdp"
                    android:layout_centerInParent="true"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:paddingStart="60dp"
                    android:paddingLeft="60dp"
                    android:paddingEnd="60dp"
                    android:paddingRight="60dp"
                    android:visibility="gone" />

                <com.airbnb.lottie.LottieAnimationView
                    android:id="@+id/animView"
                    android:layout_width="220dp"
                    android:layout_height="220dp"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    android:layout_marginTop="25dp"
                    app:lottie_autoPlay="true"
                    app:lottie_loop="true"
                    app:lottie_rawRes="@raw/comingsoon"
                    android:visibility="gone"/>

                <TextView
                    android:id="@+id/textComingSoon"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    android:text="@string/coming_soon"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:visibility="gone" />
            </RelativeLayout>


        </LinearLayout>

    </androidx.core.widget.NestedScrollView>


    <include
        android:layout_below="@+id/nestedScrollview"
        layout="@layout/bottom_sheet_select_test"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content" />


    <ProgressBar
        android:id="@+id/aviLoader"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        app:indicatorColor="@color/primary_bg_red"
        app:indicatorName="LineScalePulseOutRapidIndicator"
        android:elevation="10dp"
        android:visibility="gone"/>




</RelativeLayout>
