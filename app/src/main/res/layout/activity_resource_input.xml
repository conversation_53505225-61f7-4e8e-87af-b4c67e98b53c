<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/colorPrimary"
    tools:context=".ui.resource_input.ResourceInputActivity">

    <!-- Title Bar -->
    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/colorPrimary"
        android:text="GPT Sir"
        android:textColor="@android:color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:elevation="4dp" />

    <!-- Main Content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="24dp"
        android:gravity="center">

        <!-- Welcome Text -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Welcome to GPT Sir"
            android:textSize="28sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Enter Resource ID to get started"
            android:textSize="16sp"
            android:textColor="@android:color/white"
            android:alpha="0.8"
            android:layout_marginBottom="32dp" />

        <!-- Resource ID Input Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Resource ID"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/colorPrimary"
                    android:layout_marginBottom="8dp" />

                <EditText
                    android:id="@+id/etResourceId"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:hint="Enter Resource ID"
                    android:inputType="text"
                    android:textSize="16sp"
                    android:background="@drawable/edittext_background"
                    android:padding="12dp"
                    android:textColorHint="@color/colorPrimary" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center">

            <!-- First Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <Button
                    android:id="@+id/btnLearn"
                    android:layout_width="0dp"
                    android:layout_height="56dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:text="Learn"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:background="@drawable/button_background"
                    android:textColor="@color/colorPrimary"
                    android:drawableStart="@drawable/ic_book"
                    android:drawablePadding="8dp" />

                <Button
                    android:id="@+id/btnPractice"
                    android:layout_width="0dp"
                    android:layout_height="56dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="Practice"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:background="@drawable/button_background"
                    android:textColor="@color/colorPrimary"
                    android:drawableStart="@drawable/ic_edit"
                    android:drawablePadding="8dp" />

            </LinearLayout>

            <!-- Second Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btnTest"
                    android:layout_width="0dp"
                    android:layout_height="56dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:text="Test"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:background="@drawable/button_background"
                    android:textColor="@color/colorPrimary"
                    android:drawableStart="@drawable/ic_quiz"
                    android:drawablePadding="8dp" />

                <Button
                    android:id="@+id/btnPlay"
                    android:layout_width="0dp"
                    android:layout_height="56dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="Play"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:background="@drawable/button_background"
                    android:textColor="@color/colorPrimary"
                    android:drawableStart="@drawable/ic_play_circle_filled_24dp"
                    android:drawablePadding="8dp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
