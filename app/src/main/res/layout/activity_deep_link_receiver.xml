<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".Views.Activity.DeepLinkReceiverActivity">

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/deep_link_lottie"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:lottie_rawRes="@raw/book_details_deep_link2" />

    <TextView
        android:id="@+id/deep_link_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/deep_link_lottie"
        android:fontFamily="@font/poppins_medium"
        android:textSize="16sp"
        android:textColor="@color/colorActionBarText"
        android:layout_marginTop="20dp"
        android:layout_centerInParent="true"
        android:gravity="center"
        tools:text="Getting your eBook details..."/>

</RelativeLayout>