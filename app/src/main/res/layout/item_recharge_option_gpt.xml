<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardUseCompatPadding="true"
    android:layout_margin="10dp"
    app:cardCornerRadius="6dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:elevation="4dp">

        <TextView
            android:id="@+id/planTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="STARTER"
            android:textStyle="bold"
            android:textSize="18sp"
            android:background="@color/blue"
            android:textColor="@android:color/white"
            android:gravity="center"
            android:padding="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:padding="16dp">

            <TextView
                android:id="@+id/planPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="₹20.0"
                android:textSize="24sp"
                android:fontFamily="@font/poppins_medium"
                android:textColor="@android:color/black"
                tools:ignore="HardcodedText" />

            <TextView
                android:id="@+id/planDetails"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="/ 50 doubts"
                android:fontFamily="@font/poppins_medium"
                android:textSize="16sp"
                android:textColor="@android:color/darker_gray"
                tools:ignore="HardcodedText" />
        </LinearLayout>
    </LinearLayout>


</androidx.cardview.widget.CardView>