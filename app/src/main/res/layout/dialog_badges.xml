<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_bg_dark"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Badges"
        android:paddingTop="10dp"
        android:textStyle="bold"
        android:gravity="center"
        android:textSize="@dimen/_16sdp"
        android:textColor="@color/primary_bg_red"/>

    <LinearLayout
        android:id="@+id/linearList"
        android:layout_below="@id/txtTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="@dimen/_10sdp"
        android:visibility="visible">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Points"
            android:gravity="left"
            android:layout_marginLeft="18dp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:textSize="@dimen/_13sdp"
            />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Badge Name"
            android:gravity="left"
            android:textColor="@color/white"
            android:textSize="@dimen/_13sdp" />


    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclePoints"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/linearList"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="4dp"/>


    <Button
        android:id="@+id/btncancel"
        android:layout_width="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_height="wrap_content"
        android:layout_below="@+id/recyclePoints"
        android:text="Cancel"
        android:gravity="center"
        android:layout_marginBottom="15dp"
        android:background="@color/primary_bg_red"
        android:textColor="@color/white"
        />


</RelativeLayout>