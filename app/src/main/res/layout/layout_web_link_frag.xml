<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/colorWeblinkBg"
    tools:context=".ui.weblink.WeblinkFrag">

    <WebView
        android:id="@+id/webView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/clHeader" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clHeader"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_60sdp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:gravity="center_vertical"
        android:elevation="@dimen/_8sdp">

        <androidx.cardview.widget.CardView
            android:id="@+id/btnClose"
            android:layout_width="@dimen/_32sdp"
            android:layout_height="@dimen/_32sdp"
            android:backgroundTint="@color/colorWeblinkCardBg"
            app:cardCornerRadius="@dimen/_8sdp"
            android:foreground="?selectableItemBackground"
            android:layout_marginStart="@dimen/_10sdp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:src="@drawable/ic_close_24"
                android:contentDescription="Close"/>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/btnRefresh"
            android:layout_width="@dimen/_32sdp"
            android:layout_height="@dimen/_32sdp"
            android:backgroundTint="@color/colorWeblinkCardBg"
            android:layout_marginEnd="@dimen/_10sdp"
            android:visibility="visible"
            android:foreground="?selectableItemBackground"
            app:cardCornerRadius="@dimen/_8sdp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:src="@drawable/ic_refresh"
                android:contentDescription="Share book" />
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/btnNextPage"
            android:layout_width="@dimen/_32sdp"
            android:layout_height="@dimen/_32sdp"
            android:backgroundTint="@color/colorWeblinkCardBg"
            android:foreground="?selectableItemBackground"
            android:layout_marginEnd="@dimen/_10sdp"
            app:cardCornerRadius="@dimen/_8sdp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/btnRefresh"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:contentDescription="Next page"
                android:rotation="180"
                android:src="@drawable/ic_new_back_arrow" />
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/btnPreviousPage"
            android:layout_width="@dimen/_32sdp"
            android:layout_height="@dimen/_32sdp"
            android:backgroundTint="@color/colorWeblinkCardBg"
            android:foreground="?selectableItemBackground"
            android:layout_marginEnd="@dimen/_10sdp"
            app:cardCornerRadius="@dimen/_8sdp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/btnNextPage"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:contentDescription="Previous page"
                android:src="@drawable/ic_new_back_arrow" />
        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/tvResourceName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_8sdp"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/colorBookNameTitle"
            android:textSize="@dimen/_14ssp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/btnPreviousPage"
            app:layout_constraintStart_toEndOf="@+id/btnClose"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Resource name" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.wang.avi.AVLoadingIndicatorView
        android:id="@+id/lvCenter"
        style="@style/AVLoadingIndicatorView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="gone"
        app:indicatorColor="@color/colorAccent"
        app:indicatorName="BallSpinFadeLoaderIndicator"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ProgressBar
        android:id="@+id/pbProcessing"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_20sdp"
        android:max="100"
        android:progress="10"
        style="?android:attr/progressBarStyleHorizontal"
        android:progressDrawable="@drawable/custom_progress"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>