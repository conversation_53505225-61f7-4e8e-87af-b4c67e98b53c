<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="20dp"
    android:paddingHorizontal="20dp"
    android:orientation="vertical"
    android:background="@drawable/store_price_option_bottom_sheet_bg"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:id="@+id/price_parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Select an option"
            android:textColor="@color/black"
            android:fontFamily="@font/poppins_medium"
            android:textStyle="bold"
            android:textSize="12sp"
            android:padding="5dp"/>


        <LinearLayout
            android:id="@+id/price_holder"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:weightSum="2"
            android:orientation="horizontal"
            android:baselineAligned="false">

            <LinearLayout
                android:id="@+id/eBook_parent"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="10dp"
                android:orientation="vertical">

                <androidx.cardview.widget.CardView
                    android:id="@+id/eBook_price_card"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="4dp"
                    app:cardUseCompatPadding="true">

                    <RelativeLayout
                        android:id="@+id/eBook_price_holder"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingHorizontal="7dp"
                        android:paddingVertical="10dp">
                        <TextView
                            android:id="@+id/eBook_type_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="start"
                            android:fontFamily="@font/poppins_medium"
                            android:textAllCaps="false"
                            android:textColor="@color/black"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:text="eBook"/>

                        <LinearLayout
                            android:id="@+id/price_layout"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_below="@id/eBook_type_text"
                            android:layout_gravity="center|start"
                            android:layout_marginTop="8dp"
                            android:gravity="center|start"
                            android:orientation="horizontal"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/textView_badge_paid"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="8dp"
                                android:fontFamily="@font/poppins_medium"
                                android:textColor="@color/colorAccent"
                                android:textSize="17sp"
                                android:textStyle="normal"
                                android:visibility="visible"
                                android:gravity="center"
                                tools:text="400"/>

                            <TextView
                                android:id="@+id/textView_badge_free"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/poppins_medium"
                                android:textColor="@color/black"
                                android:textSize="15sp"
                                tools:text="100"/>
                        </LinearLayout>

                        <TextView
                            android:id="@+id/textView_discount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_below="@id/price_layout"
                            android:fontFamily="@font/poppins_bold"
                            android:textColor="#494D4D"
                            android:textSize="14sp"
                            android:textStyle="normal"
                            android:gravity="start"
                            android:layout_marginTop="3dp"
                            tools:text="30% off"/>
                    </RelativeLayout>

                </androidx.cardview.widget.CardView>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/test_series_parent"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/test_series_price_card"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:cardCornerRadius="6dp"
                        app:cardElevation="4dp"
                        app:cardUseCompatPadding="true">

                        <RelativeLayout
                            android:id="@+id/test_series_price_holder"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingHorizontal="7dp"
                            android:paddingVertical="10dp">

                            <TextView
                                android:id="@+id/test_series_type_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="start"
                                android:fontFamily="@font/poppins_medium"
                                android:textAllCaps="false"
                                android:textColor="@color/black"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:gravity="start"
                                android:text="Online Test Series"/>

                            <LinearLayout
                                android:id="@+id/price_layout_test_series"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_below="@id/test_series_type_text"
                                android:layout_gravity="center|start"
                                android:layout_marginTop="8dp"
                                android:gravity="center|start"
                                android:orientation="horizontal"
                                tools:visibility="visible">

                                <TextView
                                    android:id="@+id/textView_badge_paid_test_series"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginEnd="8dp"
                                    android:fontFamily="@font/poppins_medium"
                                    android:textColor="@color/colorAccent"
                                    android:textSize="17sp"
                                    android:textStyle="normal"
                                    android:visibility="visible"
                                    android:gravity="center"
                                    tools:text="400"/>

                                <TextView
                                    android:id="@+id/textView_badge_free_test_series"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:fontFamily="@font/poppins_medium"
                                    android:textColor="@color/black"
                                    android:textSize="15sp"
                                    tools:text="100"/>
                            </LinearLayout>

                            <TextView
                                android:id="@+id/textView_discount_test_series"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_below="@id/price_layout_test_series"
                                android:fontFamily="@font/poppins_bold"
                                android:textColor="#494D4D"
                                android:textSize="14sp"
                                android:textStyle="normal"
                                android:visibility="gone"
                                android:gravity="start"
                                android:layout_marginTop="3dp"
                                tools:text="30% off"/>
                        </RelativeLayout>

                    </androidx.cardview.widget.CardView>

                    <TextView
                        android:id="@+id/test_series_popular_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_alignTop="@id/test_series_price_card"
                        android:layout_marginBottom="9dp"
                        android:layout_marginEnd="5dp"
                        android:fontFamily="@font/poppins_light"
                        android:textAllCaps="false"
                        android:textColor="@color/white"
                        android:textSize="8sp"
                        android:text="Popular"
                        android:gravity="center"
                        android:paddingHorizontal="5dp"
                        android:paddingVertical="3dp"
                        android:background="@drawable/popular_price_bg"
                        android:elevation="7dp"/>
                </RelativeLayout>

            </LinearLayout>

        </LinearLayout>

        <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
            android:id="@+id/textView_buy_add_to_lib"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:gravity="center"
            android:background="@drawable/button_back_filled_disabled"
            android:text="Add to Cart"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            android:padding="10dp"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/add_to_cart_result"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/add_to_cart_success_lottie"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            app:lottie_autoPlay="true"
            app:lottie_loop="false"
            app:lottie_rawRes="@raw/add_to_cart_success"
            android:visibility="gone"/>

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/add_to_cart_failure_lottie"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            app:lottie_autoPlay="true"
            app:lottie_loop="false"
            app:lottie_rawRes="@raw/add_to_cart_failure"
            android:visibility="gone"/>

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/add_to_cart_loading_lottie"
            android:layout_width="200dp"
            android:layout_height="200dp"
            android:layout_gravity="center"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/price_option_loader"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/add_to_cart_result_txt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins_medium"
            android:textSize="14sp"
            android:textColor="@color/black"
            android:layout_gravity="center"
            android:gravity="center"
            tools:text="Test Series has been added to cart"/>

        <LinearLayout
            android:id="@+id/add_to_cart_post_result"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btnBrowseMore"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="5dp"
                android:layout_marginBottom="16dp"
                android:layout_weight="1"
                android:background="@drawable/feedback_dialog_cancle_bg"
                android:elevation="8dp"
                android:gravity="center"
                android:textAllCaps="false"
                android:text="Continue browsing"
                android:textColor="#8E8E8E"
                android:textSize="11sp" />

            <Button
                android:id="@+id/btnGotoCart"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:layout_marginBottom="16dp"
                android:layout_weight="1"
                android:background="@drawable/access_code_button"
                android:elevation="8dp"
                android:gravity="center"
                android:textAllCaps="false"
                android:text="Go to Cart"
                android:textColor="@color/white"
                android:textSize="11sp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>