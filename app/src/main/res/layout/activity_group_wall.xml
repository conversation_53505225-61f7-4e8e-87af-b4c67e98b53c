<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_bg_dark"
    tools:context=".ui.groupwall.GroupWallFragment">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipeToRefresh"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/primary_bg_dark">

                <ImageView
                    android:id="@+id/btnBack"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_marginLeft="5dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginRight="5dp"
                    android:layout_marginBottom="10dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?selectableItemBackground"
                    android:padding="5dp"
                    android:src="@drawable/ic_back_header"
                    android:visibility="gone"
                    app:tint="@color/white" />

                <TextView
                    android:id="@+id/textview_header_back"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="right"
                    android:layout_marginTop="25dp"
                    android:layout_toRightOf="@+id/btnBack"
                    android:fontFamily="@font/poppins_medium"
                    android:gravity="center|left"
                    android:text="Back"
                    android:textColor="@color/white"
                    android:textSize="14dp"
                    android:visibility="gone" />

                <FrameLayout
                    android:id="@+id/frameLayout_notification"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="13dp"
                    android:layout_toRightOf="@+id/textview_header_back"
                    android:clickable="true"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <LinearLayout
                        android:id="@+id/lltNotification"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_centerHorizontal="true"
                        android:layout_centerVertical="true"
                        android:layout_marginTop="5dp"
                        android:layout_marginRight="7dp"
                        android:background="@drawable/avatar_bg_circle_white"
                        android:clickable="false"
                        android:gravity="center"
                        android:padding="3dp">

                        <Button
                            android:id="@+id/button_notification"
                            android:layout_width="18dp"
                            android:layout_height="18dp"
                            android:layout_gravity="center"
                            android:background="@drawable/ic_notification"
                            android:clickable="false"
                            android:gravity="center" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/notification_count"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="right"
                        android:layout_marginTop="4dp"
                        android:layout_marginRight="4dp"
                        android:background="@drawable/circle_bg_green"
                        android:clickable="false"
                        android:fontFamily="@font/poppins_bold"
                        android:gravity="center"
                        android:text="0"
                        android:textColor="@color/white"
                        android:textSize="8sp" />
                </FrameLayout>

                <LinearLayout
                    android:id="@+id/layout_settings_btns"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:orientation="horizontal">

                    <RelativeLayout
                        android:id="@+id/layout_report_group"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:layout_marginRight="10dp"
                        android:background="@drawable/transperant_curved_layout_bg"
                        android:padding="5dp"
                        android:visibility="gone">

                        <ImageView
                            android:id="@+id/imageView_eye"
                            android:layout_width="12dp"
                            android:layout_height="19dp"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="5dp"
                            android:layout_marginRight="3dp"
                            android:src="@drawable/ic_eye"
                            app:tint="@color/primary_bg_red" />

                        <Button
                            android:id="@+id/button_report_group_header"
                            android:layout_width="wrap_content"
                            android:layout_height="27dp"
                            android:layout_marginLeft="5dp"
                            android:layout_marginRight="5dp"
                            android:layout_toRightOf="@+id/imageView_eye"
                            android:background="@color/transparent"
                            android:fontFamily="@font/poppins_medium"
                            android:gravity="center"
                            android:text="Report Group"
                            android:textAllCaps="false"
                            android:textColor="@color/white"
                            android:textSize="13sp" />
                    </RelativeLayout>


                    <RelativeLayout
                        android:id="@+id/layout_settings"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_marginTop="18dp"
                        android:layout_marginRight="10dp"
                        android:background="@drawable/transperant_curved_layout_bg"
                        android:visibility="gone">

                        <ImageView
                            android:id="@+id/imageView_settings"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_centerInParent="true"
                            android:layout_marginLeft="5dp"
                            android:layout_marginRight="3dp"
                            android:src="@drawable/ic_settings_dark"
                            app:tint="@color/white" />

                    </RelativeLayout>
                </LinearLayout>

                <RelativeLayout
                    android:id="@+id/layout_top"
                    android:layout_width="match_parent"
                    android:layout_height="150dp"
                    android:layout_below="@+id/frameLayout_notification"
                    android:animateLayoutChanges="true"
                    android:background="@color/primary_bg_dark"
                    android:gravity="center"
                    android:visibility="gone">

                    <TextView
                        android:id="@+id/textview_group_name_header"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:fontFamily="@font/poppins_bold"
                        android:gravity="center"
                        android:paddingTop="7dp"
                        android:text="Discuss"
                        android:textColor="@color/primary_bg_red"
                        android:textSize="26sp"
                        android:visibility="visible" />

                </RelativeLayout>

                <LinearLayout
                    android:id="@+id/layout_top_header"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="25dp"
                    android:layout_marginTop="155dp"
                    android:layout_marginRight="25dp"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <RelativeLayout
                        android:id="@+id/layout_invite_group"
                        android:layout_width="match_parent"
                        android:layout_height="50dp"
                        android:layout_alignParentRight="true"
                        android:layout_gravity="center"
                        android:layout_marginLeft="5dp"
                        android:layout_marginRight="15dp"
                        android:layout_weight="1"
                        android:background="@drawable/shadow_curved_layout_bg"
                        android:gravity="center"
                        android:visibility="gone">

                        <ImageView
                            android:id="@+id/imageView_invite"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="5dp"
                            android:src="@drawable/ic_add_res_new" />

                        <Button
                            android:id="@+id/button_inview"
                            android:layout_width="wrap_content"
                            android:layout_height="20dp"
                            android:layout_centerVertical="true"
                            android:layout_toRightOf="@+id/imageView_invite"
                            android:background="@color/transparent"
                            android:fontFamily="@font/poppins_medium"
                            android:gravity="center|left"
                            android:text="Invite a Friend"
                            android:textAllCaps="false"
                            android:textSize="12sp" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/layout_join_group"
                        android:layout_width="match_parent"
                        android:layout_height="50dp"
                        android:layout_alignParentRight="true"
                        android:layout_gravity="center"
                        android:layout_marginLeft="5dp"
                        android:layout_weight="1"
                        android:background="@drawable/shadow_curved_layout_bg"
                        android:gravity="center"
                        android:visibility="gone">

                        <ImageView
                            android:id="@+id/imageView_join"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="5dp"
                            android:src="@drawable/ic_add_res_new" />

                        <Button
                            android:id="@+id/button_join"
                            android:layout_width="wrap_content"
                            android:layout_height="20dp"
                            android:layout_centerVertical="true"
                            android:layout_toRightOf="@+id/imageView_join"
                            android:background="@color/transparent"
                            android:fontFamily="@font/poppins_medium"
                            android:gravity="center"
                            android:text="Request to Join"
                            android:textAllCaps="false"
                            android:textSize="12sp" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/layout_exit_report_spinner"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:layout_alignParentRight="true"
                        android:layout_gravity="center"
                        android:layout_marginLeft="5dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:visibility="gone">

                        <androidx.appcompat.widget.AppCompatSpinner
                            android:id="@+id/spinner_exit_report"
                            style="@style/Widget.AppCompat.Spinner"
                            android:layout_width="match_parent"
                            android:layout_height="45dp"
                            android:layout_gravity="center"
                            android:background="@drawable/custom_spinner_primery"
                            android:overlapAnchor="false" />
                    </RelativeLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/textView_private_msg"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/layout_top_header"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="5dp"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="15dp"
                        android:layout_height="15dp"
                        android:layout_below="@+id/item_view"
                        android:layout_gravity="center"
                        android:layout_toLeftOf="@+id/textView_group_public_private"
                        android:src="@drawable/ic_private"
                        android:visibility="visible"
                        app:tint="@color/primary_bg_red" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="3dp"
                        android:layout_marginTop="3dp"
                        android:fontFamily="@font/poppins_light"
                        android:gravity="center"
                        android:text="This group is Private"
                        android:textColor="@color/primary_bg_red"
                        android:textSize="10sp" />
                </LinearLayout>

                <FrameLayout
                    android:id="@+id/disable_view_top"
                    android:layout_width="match_parent"
                    android:layout_height="210dp"
                    android:alpha="0.5"
                    android:background="#80000000"
                    android:clickable="true"
                    android:enabled="false"
                    android:visibility="gone" />

                <ImageView
                    android:id="@+id/imageView_close_edit"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="100dp"
                    android:src="@drawable/ic_close_red"
                    android:visibility="gone" />

                <RelativeLayout
                    android:id="@+id/layout_post"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_below="@+id/layout_top"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="10dp"
                    android:layout_marginBottom="5dp"
                    android:background="@drawable/shadow_lite_curved_layout_bg"
                    android:visibility="gone">

                    <EditText
                        android:id="@+id/editText_post"
                        android:layout_width="match_parent"
                        android:layout_height="50dp"
                        android:layout_centerVertical="true"
                        android:layout_toLeftOf="@+id/image_attach_post"
                        android:background="@null"
                        android:enabled="true"
                        android:focusable="true"
                        android:fontFamily="@font/poppins_medium"
                        android:gravity="center|left"
                        android:hint="Post..."
                        android:inputType="textMultiLine"
                        android:maxLines="15"
                        android:padding="10dp"
                        android:singleLine="false"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <ImageView
                        android:id="@+id/image_attach_post"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_above="@+id/dummy_view"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="8dp"
                        android:layout_marginBottom="10dp"
                        android:layout_toLeftOf="@+id/button_post"
                        android:rotation="-40"
                        android:src="@drawable/ic_attach"
                        app:tint="@color/colorActionBarText" />

                    <Button
                        android:id="@+id/button_post"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_above="@+id/dummy_view"
                        android:layout_alignParentRight="true"
                        android:background="@drawable/button_right_corner_full_radius"
                        android:fontFamily="@font/poppins_regular"
                        android:text="Post"
                        android:textAllCaps="false"
                        android:textColor="@color/white"
                        android:textSize="12sp" />

                    <Button
                        android:id="@+id/button_post_shape"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_above="@+id/dummy_view"
                        android:layout_alignParentRight="true"
                        android:background="@drawable/ic_button_post"
                        android:visibility="gone" />

                    <View
                        android:id="@+id/dummy_view"
                        android:layout_width="match_parent"
                        android:layout_height="0.3dp"
                        android:layout_below="@+id/editText_post"
                        android:background="@color/white"
                        android:visibility="visible" />
                </RelativeLayout>

                <LinearLayout
                    android:id="@+id/layout_attach_post_opt"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_below="@+id/layout_post"
                    android:layout_marginLeft="30dp"
                    android:layout_marginRight="30dp"
                    android:background="@drawable/shadow_lite_curved_layout_bottom_flat_top"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    android:weightSum="2">

                    <RelativeLayout
                        android:id="@+id/layout_attach_image"
                        android:layout_width="match_parent"
                        android:layout_height="50dp"
                        android:layout_alignParentRight="true"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:visibility="visible">

                        <ImageView
                            android:id="@+id/imageView_attach"
                            android:layout_width="25dp"
                            android:layout_height="25dp"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="5dp"
                            android:rotation="-40"
                            android:src="@drawable/ic_attach"
                            android:visibility="visible"
                            app:tint="@color/video_title_color_land" />

                        <ImageView
                            android:id="@+id/imageView_attached"
                            android:layout_width="30dp"
                            android:layout_height="20dp"
                            android:layout_centerVertical="true"
                            android:paddingRight="8dp"
                            android:src="@drawable/ic_image"
                            android:visibility="gone" />

                        <Button
                            android:id="@+id/button_attach_image"
                            android:layout_width="wrap_content"
                            android:layout_height="30dp"
                            android:layout_centerVertical="true"
                            android:layout_toRightOf="@+id/imageView_attach"
                            android:background="@color/transparent"
                            android:fontFamily="@font/poppins_medium"
                            android:gravity="center|left"
                            android:text="Attach a Image"
                            android:textAllCaps="false"
                            android:textSize="11sp" />

                        <ImageView
                            android:id="@+id/imageView_attach_clear"
                            android:layout_width="22dp"
                            android:layout_height="22dp"
                            android:layout_centerVertical="true"
                            android:layout_toRightOf="@+id/button_attach_image"
                            android:src="@drawable/ic_close_red"
                            android:visibility="gone" />
                    </RelativeLayout>

                    <View
                        android:layout_width="1dp"
                        android:layout_height="30dp"
                        android:layout_below="@+id/editText_post"
                        android:layout_gravity="center"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:background="@color/light_gray"
                        android:visibility="visible" />

                    <RelativeLayout
                        android:id="@+id/layout_attach_file"
                        android:layout_width="match_parent"
                        android:layout_height="50dp"
                        android:layout_alignParentRight="true"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:visibility="visible">

                        <ImageView
                            android:id="@+id/imageView_attach_file"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="5dp"
                            android:src="@drawable/ic_upload"
                            app:tint="@color/video_title_color_land" />

                        <ImageView
                            android:id="@+id/imageView_attached_file"
                            android:layout_width="30dp"
                            android:layout_height="20dp"
                            android:layout_centerVertical="true"
                            android:paddingRight="8dp"
                            android:src="@drawable/ic_file_upload"
                            android:visibility="gone" />

                        <Button
                            android:id="@+id/button_attach_file"
                            android:layout_width="wrap_content"
                            android:layout_height="30dp"
                            android:layout_centerVertical="true"
                            android:layout_toRightOf="@+id/imageView_attach_file"
                            android:background="@color/transparent"
                            android:fontFamily="@font/poppins_medium"
                            android:gravity="center"
                            android:text="Upload a File"
                            android:textAllCaps="false"
                            android:textSize="11sp" />

                        <ImageView
                            android:id="@+id/imageView_file_attach_clear"
                            android:layout_width="22dp"
                            android:layout_height="22dp"
                            android:layout_centerVertical="true"
                            android:layout_toRightOf="@+id/button_attach_file"
                            android:src="@drawable/ic_close_red"
                            android:visibility="gone" />
                    </RelativeLayout>
                </LinearLayout>

                <AutoCompleteTextView
                    android:id="@+id/autocomplete_textView_search_group_detail"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_below="@+id/layout_attach_post_opt"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="12dp"
                    android:background="@drawable/filter_dialog_background"
                    android:fontFamily="@font/poppins_regular"
                    android:hint="Search.."
                    android:maxLines="1"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"
                    android:singleLine="true"
                    android:textSize="12sp"
                    android:visibility="gone" />

                <com.facebook.shimmer.ShimmerFrameLayout
                    android:id="@+id/shimmerLoadingLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/layout_attach_post_opt"
                    android:visibility="visible">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <include layout="@layout/book_history_shimmer_item" />

                    </LinearLayout>

                </com.facebook.shimmer.ShimmerFrameLayout>

                <androidx.core.widget.NestedScrollView
                    android:id="@+id/nested_seroll_view_post"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_above="@+id/layout_page"
                    android:layout_below="@+id/layout_attach_post_opt"
                    android:background="@color/primary_bg_dark"
                    android:fillViewport="true"
                    android:overScrollMode="never"
                    app:layout_behavior="@string/appbar_scrolling_view_behavior">

                    <RelativeLayout
                        android:id="@+id/layoutContainer"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recyclerView_groups_details"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5dp" />

                        <ProgressBar
                            android:id="@+id/idPBLoading"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/recyclerView_groups_details"
                            android:visibility="gone" />
                    </RelativeLayout>
                </androidx.core.widget.NestedScrollView>

                <FrameLayout
                    android:id="@+id/disable_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_below="@+id/autocomplete_textView_search_group_detail"
                    android:alpha="0.5"
                    android:background="#80000000"
                    android:clickable="true"
                    android:enabled="false"
                    android:visibility="gone" />

                <LinearLayout
                    android:id="@+id/empty_layout_post"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/autocomplete_textView_search_group_detail"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="200dp"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="130dp"
                        android:layout_height="130dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_no_post_empty"
                        app:tint="@color/white" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/textview_header_back"
                        android:layout_gravity="center"
                        android:layout_marginTop="5dp"
                        android:layout_toRightOf="@+id/image_page_icon"
                        android:fontFamily="@font/poppins_bold"
                        android:gravity="center"
                        android:text="No Posts Found."
                        android:textColor="@color/white"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/private_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <com.airbnb.lottie.LottieAnimationView
                        android:id="@+id/animationView"
                        android:layout_width="150dp"
                        android:layout_height="150dp"
                        android:layout_gravity="center"
                        app:lottie_autoPlay="true"
                        app:lottie_loop="true"
                        app:lottie_rawRes="@raw/lottie_private" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/textview_header_back"
                        android:layout_gravity="center"
                        android:layout_toRightOf="@+id/image_page_icon"
                        android:fontFamily="@font/poppins_bold"
                        android:gravity="center"
                        android:text="This is a Private Group"
                        android:textColor="@color/gray"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/textview_header_back"
                        android:layout_gravity="center"
                        android:layout_toRightOf="@+id/image_page_icon"
                        android:fontFamily="@font/poppins_medium"
                        android:gravity="center"
                        android:text="Please Join to see the Posts"
                        android:textColor="@color/video_title_color_land"
                        android:textSize="12sp" />
                </LinearLayout>

                <RelativeLayout
                    android:id="@+id/layout_action"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:background="@color/black"
                    android:padding="10dp"
                    android:visibility="gone">

                    <com.airbnb.lottie.LottieAnimationView
                        android:id="@+id/animationView_actions"
                        android:layout_width="150dp"
                        android:layout_height="150dp"
                        android:layout_centerInParent="true"
                        android:visibility="visible"
                        app:lottie_autoPlay="true"
                        app:lottie_loop="true"
                        app:lottie_rawRes="@raw/lottie_report" />

                    <TextView
                        android:id="@+id/textView_action"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/animationView_actions"
                        android:layout_centerHorizontal="true"
                        android:layout_gravity="center"
                        android:layout_marginTop="15dp"
                        android:layout_marginBottom="10dp"
                        android:fontFamily="@font/poppins_bold"
                        android:gravity="center"
                        android:text=""
                        android:textColor="@color/white"
                        android:textSize="14sp" />
                </RelativeLayout>

                <androidx.appcompat.widget.AlertDialogLayout
                    android:id="@+id/alertDialogLayout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_centerInParent="true"
                    android:background="#74000000"
                    android:clickable="false"
                    android:visibility="gone">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:clickable="false">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:layout_marginStart="35dp"
                            android:layout_marginLeft="35dp"
                            android:background="@drawable/button_background_rounded_no_border"
                            android:elevation="24dp"
                            android:orientation="vertical"
                            android:paddingStart="25dp"
                            android:paddingLeft="25dp"
                            android:paddingTop="19dp"
                            android:paddingEnd="25dp"
                            android:paddingRight="25dp">

                            <TextView
                                android:id="@+id/downloadMsgTxt"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginEnd="50dp"
                                android:layout_marginRight="50dp"
                                android:ellipsize="end"
                                android:lines="3"
                                android:textColor="@color/colorActionBarText"
                                android:textSize="20sp"
                                android:typeface="serif" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="20dp"
                                android:gravity="center"
                                android:orientation="horizontal">

                                <ProgressBar
                                    android:id="@+id/progressDialogBar"
                                    style="@style/Base.Widget.AppCompat.ProgressBar.Horizontal"
                                    android:layout_width="match_parent"
                                    android:layout_height="10dp"
                                    android:layout_weight="0.3"
                                    android:max="100"
                                    android:paddingBottom="2dp"
                                    android:progressDrawable="@drawable/custom_progress" />

                                <TextView
                                    android:id="@+id/downloadPercent"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="center"
                                    android:text="0%"
                                    android:textColor="@color/colorActionBarText"
                                    android:textSize="12sp"
                                    android:typeface="serif" />
                            </LinearLayout>

                        </LinearLayout>
                    </RelativeLayout>

                </androidx.appcompat.widget.AlertDialogLayout>

                <RelativeLayout
                    android:id="@+id/layout_page"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:background="@color/primary_bg_dark"
                    android:visibility="gone">

                    <RelativeLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_marginLeft="15dp"
                        android:layout_marginRight="15dp"
                        android:layout_marginBottom="3dp"
                        android:background="@drawable/rounded_yellow"
                        android:paddingLeft="10dp"
                        android:paddingRight="10dp"
                        android:visibility="visible">

                        <TextView
                            android:id="@+id/textView_page"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentLeft="true"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="5dp"
                            android:fontFamily="@font/poppins_light"
                            android:gravity="center"
                            android:paddingTop="2dp"
                            android:text="Page"
                            android:textColor="@color/gray"
                            android:textSize="10sp" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recycler_view_pages"
                            android:layout_width="wrap_content"
                            android:layout_height="35dp"
                            android:layout_marginLeft="3dp"
                            android:layout_toRightOf="@+id/textView_page"
                            android:scrollbars="horizontal"
                            android:visibility="visible" />
                    </RelativeLayout>
                </RelativeLayout>

                <include
                    layout="@layout/no_internet_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true" />
            </RelativeLayout>
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
    </RelativeLayout>

    <include layout="@layout/bottom_sheet_group_res_open" />

    <RelativeLayout
        android:id="@+id/layout_image_full_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="10dp"
        android:background="#70000000"
        android:padding="20dp"
        android:visibility="gone">

        <ImageView
            android:id="@+id/imageView_full_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginTop="15dp"
            android:layout_marginBottom="15dp"
            android:adjustViewBounds="true"
            android:scaleType="fitXY" />

        <ImageView
            android:id="@+id/imageView_full_view_close"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:src="@drawable/ic_close_red" />

        <com.wang.avi.AVLoadingIndicatorView
            android:id="@+id/progressImageFullView"
            style="@style/AVLoadingIndicatorView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_centerHorizontal="true"
            android:layout_gravity="center"
            android:visibility="visible"
            app:indicatorColor="@color/primary_bg_red"
            app:indicatorName="BallBeatIndicator" />

    </RelativeLayout>

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/animationViewInvite"
        android:layout_width="300dp"
        android:layout_height="300dp"
        android:layout_gravity="center"
        android:visibility="gone"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:lottie_rawRes="@raw/lottie_share" />

    <com.wang.avi.AVLoadingIndicatorView
        android:id="@+id/groupLoader_details"
        style="@style/AVLoadingIndicatorView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone"
        app:indicatorColor="@color/primary_bg_red"
        app:indicatorName="BallBeatIndicator" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>