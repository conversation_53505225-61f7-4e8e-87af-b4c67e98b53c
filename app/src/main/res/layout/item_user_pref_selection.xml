<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ItemView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/primary_bg_dark"
    android:gravity="center"
    android:orientation="vertical">


        <RelativeLayout
            android:id="@+id/dashboard_current_affairs"

            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_marginStart="20dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="10dp"
            android:layout_marginBottom="10dp"
            android:background="@drawable/home_card_bg">


        <LinearLayout
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="70dp"
            android:layout_gravity="center"
            android:orientation="horizontal"
            android:paddingStart="5dp"
            android:paddingEnd="5dp"
            android:clickable="true">

            <TextView
                android:id="@+id/textView_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:ellipsize="end"
                android:fontFamily="@font/poppins_regular"
                android:gravity="center"
                android:maxLines="2"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                android:text="My Books"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:textStyle="bold"
                android:visibility="visible" />
        </LinearLayout>


            <ImageView
                android:id="@+id/image_selected"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_alignParentRight="true"
                android:layout_marginRight="5dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:src="@drawable/tick"
                android:tint="@color/white"
                android:visibility="visible" />

        </RelativeLayout>


</RelativeLayout>
