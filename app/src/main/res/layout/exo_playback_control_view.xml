<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:keepScreenOn="true"
    android:layout_gravity="bottom"
    >

    <!--<com.google.android.exoplayer2.ui.AspectRatioFrameLayout
        android:id="@+id/video_frame"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_gravity="center"
        android:visibility="visible"
        android:layout_centerInParent="true"
        android:foregroundGravity="center">

        <SurfaceView android:id="@+id/surface_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"/>
    </com.google.android.exoplayer2.ui.AspectRatioFrameLayout>-->

    <!--<ProgressBar
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/pbar"
        android:visibility="gone"
        android:layout_centerInParent="true"
        />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:id="@+id/brightness_slider_container"
        android:gravity="center"
        android:visibility="gone"
        android:layout_above="@+id/root"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="20dp">
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/brightness_medium"
            android:id="@+id/brightnessIcon"/>
        <ProgressBar
            style="@android:style/Widget.ProgressBar.Horizontal"
            android:layout_width="10dp"
            android:layout_height="match_parent"
            android:max="100"
            android:progress="33"
            android:id="@+id/brightness_slider"
            android:progressDrawable="@drawable/verticalbar_design_brightness"
            android:layout_marginLeft="0dp"
            android:layout_marginTop="0dp"
            android:layout_marginRight="0dp"
            android:layout_marginBottom="0dp"
            android:visibility="visible" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:id="@+id/brightness_center_text"
        android:gravity="center"
        android:visibility="gone"
        android:layout_centerHorizontal="true" >

        <ImageView
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:id="@+id/brightness_image"
            android:src="@drawable/brightness_minimum" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text=" 22"
            android:textSize="50dp"
            android:textStyle="bold"
            android:id="@+id/brigtness_perc_center_text"
            android:textColor="#FFF" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:id="@+id/vol_center_text"
        android:gravity="center"
        android:visibility="gone"
        android:layout_centerHorizontal="true" >

        <ImageView
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:id="@+id/vol_image"
            android:src="@drawable/hplib_volume" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text=" 22"
            android:textSize="50dp"
            android:textStyle="bold"
            android:id="@+id/vol_perc_center_text"
            android:textColor="#FFF" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:id="@+id/volume_slider_container"
        android:gravity="center"
        android:layout_alignParentRight="true"
        android:layout_marginTop="20dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="20dp"
        android:visibility="gone"
        >
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/hplib_volume"
            android:id="@+id/volIcon"/>
        <com.veer.exvidplayer.Utils.VolBar
            style="@android:style/Widget.ProgressBar.Horizontal"
            android:layout_width="10dp"
            android:layout_height="match_parent"
            android:max="100"
            android:progress="33"
            android:id="@+id/volume_slider"
            android:progressDrawable="@drawable/verticalbar_design_volume"
            android:layout_marginRight="0dp"
            android:layout_marginLeft="0dp"
            android:layout_marginTop="0dp"
            android:layout_marginBottom="0dp"
            android:visibility="visible" />
    </LinearLayout>


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:id="@+id/seekbar_center_text"
        android:gravity="center"
        android:visibility="gone"
        android:layout_centerHorizontal="true">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="00:44"
            android:textSize="50dp"
            android:textStyle="bold"
            android:id="@+id/txt_seek_currTime"
            android:textColor="#ffffff" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="[ +00:10 ]"
            android:textSize="50dp"
            android:textStyle="bold"
            android:id="@+id/txt_seek_secs"
            android:textColor="#ffffff" />

    </LinearLayout>-->

    <ImageButton
        android:id="@+id/exo_resize"
        android:layout_width="34dp"
        android:layout_height="34dp"
        android:src="@drawable/ic_full_sceen_wa"
        android:layout_gravity="top|end"
        android:scaleType="fitCenter"
        android:padding="6dp"
        android:visibility="invisible"
        android:background="@drawable/button_background_black_rounded"
        android:layout_margin="10dp"
        app:tint="@color/white" />

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="#000000"
        android:visibility="visible">

        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:id="@+id/seekbar_time"
            android:gravity="center"
            android:background="#96000000"
            android:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="?android:attr/textAppearanceSmall"
                android:text="00:00:00"
                android:id="@+id/exo_position"
                android:textColor="#FFF"
                android:paddingLeft="20dp"
                android:paddingTop="10dp"
                android:paddingRight="10dp"
                android:paddingBottom="10dp" />

            <androidx.media3.ui.DefaultTimeBar
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:id="@+id/exo_progress"
                android:layout_weight="1"
                app:played_color="@color/primary_bg_red"
                app:buffered_color="@color/gray"
                app:unplayed_color="#FFFFFF" />
            <!--android:indeterminate="false"
            style="@android:style/Widget.DeviceDefault.Light.SeekBar"
            android:thumbTint="#ffffff"
            android:progress="0"
            android:secondaryProgress="0"
            android:splitTrack="false"
            android:progressTint="#2473ac"
            android:secondaryProgressTint="#9A8486"
            android:foregroundTint="#7F5C62"
            android:foreground="#7F5C62" />-->

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="?android:attr/textAppearanceSmall"
                android:text="00:00:00"
                android:id="@+id/exo_duration"
                android:textColor="#FFF"
                android:paddingLeft="10dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:paddingRight="20dp" />

        </LinearLayout>

        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:id="@+id/controls"
            android:paddingBottom="10dp"
            android:layout_gravity="center"
            android:background="#96000000"
            android:visibility="visible">

            <ImageButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/btn_lock"
                android:layout_gravity="right|center_vertical"
                android:background="@android:color/transparent"
                android:layout_weight="1"
                android:visibility="gone"
                android:src="@drawable/ic_lock_white_24dp"
                android:layout_marginLeft="20dp" />

            <!--<ImageButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/exo_prev"
                android:layout_gravity="center"
                android:background="@android:color/transparent"
                android:layout_weight="1"
                android:src="@drawable/ic_skip_previous_white_24dp"
                android:cropToPadding="false"
                android:visibility="gone"
                />-->

            <TextView
                android:id="@+id/exo_live_video"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="LIVE"
                android:typeface="serif"
                android:textSize="14sp"
                android:textColor="@color/white"
                android:drawableStart="@drawable/ic_live_video"
                android:drawablePadding="5dp"
                android:background="@android:color/transparent"
                android:layout_gravity="center"
                android:gravity="center"
                android:paddingStart="10dp"
                android:visibility="gone"/>

            <ImageButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:layout_gravity="center"
                android:id="@+id/exo_rew"
                android:layout_weight="1"
                app:srcCompat="@drawable/ic_fast_rewind_white_24" />

            <ImageButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:src="@drawable/ic_play_circle_filled_white_white_24dp"
                android:layout_gravity="center"
                android:id="@+id/exo_play"
                android:layout_weight="1"
                android:visibility="gone"
                />

            <ImageButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:src="@drawable/ic_pause_circle_filled_white_24dp"
                android:layout_gravity="center"
                android:id="@+id/exo_pause"
                android:layout_weight="1"
                />

            <ImageButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="+30s"
                android:background="@android:color/transparent"
                android:layout_gravity="center"
                android:id="@+id/exo_ffwd"
                android:layout_weight="1"
                app:srcCompat="@drawable/ic_fast_forward_white_24dp"
                />

            <!--<ImageButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/exo_next"
                android:layout_gravity="center"
                android:background="@android:color/transparent"
                android:layout_weight="1"
                android:src="@drawable/ic_skip_next_white_24dp"
                android:visibility="gone"/>-->

            <ImageButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Settings"
                android:background="@android:color/transparent"
                android:layout_gravity="right|center_vertical"
                android:id="@+id/exo_videoquality"
                android:layout_weight="1"
                app:srcCompat="@drawable/ic_switch_video_white_24dp"
                android:foregroundGravity="right" />

            <ImageButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Settings"
                android:background="@android:color/transparent"
                android:layout_gravity="right|center_vertical"
                android:id="@+id/exo_playspeed"
                android:layout_weight="1"
                android:src="@drawable/ic_slow_motion_video_white"
                android:foregroundGravity="right"
                style="@style/ExoMediaButton"/>

            <ImageButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Settings"
                android:background="@android:color/transparent"
                android:layout_gravity="right|center_vertical"
                android:id="@+id/exo_fullscreen"
                android:layout_weight="1"
                app:srcCompat="@drawable/ic_fullscreen_white"
                android:foregroundGravity="right"
                style="@style/ExoMediaButton"/>


        </LinearLayout>

    </LinearLayout>

</FrameLayout>