<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#150F36">

    <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/userRankImage"
        android:layout_width="126dp"
        android:layout_height="120dp"
        android:layout_marginStart="28dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="@dimen/_20sdp"
        android:background="@drawable/circle_background"
        android:scaleType="centerCrop"
        android:src="@drawable/prepjoy_full_icon"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textName"
        app:layout_constraintVertical_bias="0.13" />

    <TextView
        android:id="@+id/textRank"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="92dp"
        android:layout_weight="1"
        android:text="@string/rank"
        android:textColor="@color/white"
        android:textSize="@dimen/_14ssp"
        app:layout_constraintStart_toEndOf="@+id/userRankImage"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textPoints"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="124dp"
        android:layout_weight="1"
        android:text="@string/score"
        android:textColor="@color/white"
        android:textSize="@dimen/_14ssp"
        app:layout_constraintStart_toEndOf="@+id/userRankImage"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/userRank"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="80dp"
        android:layout_marginTop="92dp"
        android:layout_weight="1"
        android:textColor="@color/white"
        android:textSize="@dimen/_14ssp"
        app:layout_constraintStart_toEndOf="@+id/userRankImage"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/userPoints"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="80dp"
        android:layout_marginTop="124dp"
        android:layout_weight="1"
        android:textColor="@color/white"
        android:textSize="@dimen/_14ssp"
        app:layout_constraintStart_toEndOf="@+id/userRankImage"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginTop="32dp"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:paddingHorizontal="15dp"
        android:textColor="@color/white"
        android:textSize="@dimen/_18ssp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>