<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content" android:layout_height="wrap_content"
    android:padding="10dp">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:typeface="monospace"
        android:textSize="16sp"
        android:textColor="@color/video_title_color_land"
        android:padding="5dp"
        android:gravity="start" />

    <View
        android:id="@+id/view0"
        android:layout_width="wrap_content"
        android:layout_height="1dp"
        android:layout_below="@id/title"
        android:layout_marginTop="2dp"
        android:layout_marginBottom="5dp"
        android:background="@color/colorActionBarText"
        android:layout_alignStart="@id/title"
        android:layout_alignEnd="@id/title" />

    <TextView
        android:id="@+id/body"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/view0"
        android:textSize="16sp"
        android:textColor="@color/colorActionBarText"
        android:typeface="serif"
        android:layout_margin="10dp"
        />

    <TextView
        android:id="@+id/body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/body"
        android:textSize="16sp"
        android:textColor="@color/colorActionBarText"
        android:typeface="serif"
        android:layout_margin="10dp"
        />

    <TextView
        android:id="@+id/body3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/body2"
        android:textSize="14sp"
        android:textColor="@color/primary_bg_red"
        android:typeface="serif"
        android:layout_margin="10dp"
        android:text="**Slow network speed will effect video experience." />

    <LinearLayout
        android:id="@+id/buttonLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/body3"
        android:padding="10dp"
        android:orientation="horizontal"
        android:weightSum="2">
        <Button
            android:id="@+id/negativeBtn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Cancel"
            android:background="@color/primary_bg_red"
            android:textSize="13sp"
            android:layout_marginEnd="10dp"
            android:elevation="2dp" />

        <Button
            android:id="@+id/positiveBtn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Continue"
            android:textSize="13sp"
            android:background="@color/video_title_color_land"
            android:layout_marginStart="10dp"
            android:elevation="2dp"/>
    </LinearLayout>

</RelativeLayout>