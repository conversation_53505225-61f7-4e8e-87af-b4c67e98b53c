<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/primary_bg_dark"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imagNav"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="center"
            android:layout_marginStart="20dp"
            app:srcCompat="@drawable/hamburger" />

        <TextView
            android:id="@+id/textOpenNav"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="8dp"
            android:text="Home"
            android:textColor="@color/white"
            android:textSize="@dimen/_14sdp"
            android:textStyle="bold" />


    </LinearLayout>

    <LinearLayout
        android:layout_width="150dp"
        android:layout_height="50dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:gravity="center">

        <ImageView
            android:id="@+id/imageView_change_preferance"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:scaleType="fitXY"
            android:src="@drawable/is_settings_pref"
            android:visibility="visible"
            app:tint="@color/white" />

        <FrameLayout
            android:id="@+id/frameLayout_notification"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp">

            <ImageView
                android:id="@+id/button_notification"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:background="@drawable/ic_notification_bell"
                android:clickable="false"
                android:gravity="center"
                app:tint="@color/white"/>
        </FrameLayout>

        <TextView
            android:id="@+id/notification_count"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_alignParentRight="true"
            android:layout_gravity="right"
            android:layout_marginTop="6dp"
            android:layout_marginRight="6dp"
            android:background="@drawable/avatar_bg_circle"
            android:clickable="false"
            android:gravity="center"
            android:text="0"
            android:textColor="@color/black"
            android:textSize="11sp"
            android:visibility="gone" />

        <FrameLayout
            android:id="@+id/flCart"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:foreground="?selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:layout_marginStart="5dp">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="6dp"
                android:src="@drawable/bottom_nav_cart_black" />

            <TextView
                android:id="@+id/tvCartCount"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:textSize="10sp"
                android:background="@drawable/button_background_black_rounded"
                android:textColor="@color/white"
                tools:text="0"
                android:gravity="center"
                android:layout_gravity="end"/>

        </FrameLayout>

        <FrameLayout
            android:id="@+id/frameLayout_help"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp">

            <ImageView
                android:id="@+id/button_help"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_help"
                android:clickable="false"
                android:gravity="center"
                android:scaleType="fitXY"
                app:tint="@color/white" />
        </FrameLayout>
    </LinearLayout>

</RelativeLayout>