<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="16dp"
    android:gravity="center_vertical">

    <!-- Circular Progress Bar to simulate AI thinking process -->
    <ProgressBar
        android:id="@+id/ai_loader_progress"
        style="@style/Widget.AppCompat.ProgressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:indeterminate="true"
        android:layout_gravity="center_vertical"
        android:paddingEnd="10dp"
        android:paddingRight="10dp"
        android:theme="@style/ProgressBarTheme" />

    <!-- Optional text to simulate AI thinking (You can change/remove this) -->
    <TextView
        android:id="@+id/ai_loader_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Thinking..."
        android:textColor="@android:color/darker_gray"
        android:textSize="14sp"
        android:gravity="center_vertical" />
</LinearLayout>
