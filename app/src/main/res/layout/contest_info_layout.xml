<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="10dp"
    android:background="@color/primary_bg_dark">

    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="left"
        android:text="Instructions to Claim Reward"
        android:textStyle="bold"
        android:paddingLeft="30dp"
        android:paddingRight="30dp"
        android:paddingTop="5dp"
        android:textColor="@color/primary_bg_red"
        android:textSize="15sp" />

    <TextView
        android:id="@+id/description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="left"
        android:text="@string/contest_instructions"
        android:textStyle="bold"
        android:paddingLeft="30dp"
        android:paddingRight="30dp"
        android:paddingBottom="10dp"
        android:layout_marginTop="10dp"
        android:textColor="@color/white"
        android:textSize="15sp" />

</LinearLayout>