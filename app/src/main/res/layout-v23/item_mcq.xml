<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    app:cardCornerRadius="10dp"
    app:cardElevation="5dp"
    android:layout_margin="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:orientation="vertical">

        <!-- Question TextView -->
        <TextView
            android:id="@+id/txtQuestion"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:text="Question"
            android:textSize="14sp"
            android:fontFamily="@font/poppins_medium"
            android:textColor="@color/black"
            android:scrollHorizontally="true"
            android:focusable="true"
            android:focusableInTouchMode="true"/>

        <!-- Grid Layout for Buttons -->
        <GridLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:columnCount="2"
            android:rowCount="2"
            android:paddingTop="12dp"
            android:paddingBottom="8dp"
            android:layout_marginTop="12dp"
            android:alignmentMode="alignMargins"
            android:rowOrderPreserved="true">

            <!-- Give Hint Button -->
            <Button
                android:id="@+id/btnGiveHint"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:paddingStart="5dp"
                android:paddingEnd="5dp"
                android:layout_columnWeight="1"
                android:layout_margin="6dp"
                android:text="Give Hint"
                android:textSize="12sp"
                android:textAllCaps="false"
                android:background="@drawable/button_background_filled"
                android:backgroundTint="@color/colorAccent"
                android:drawableStart="@drawable/hint"
                android:drawableTint="@color/white"
                android:textColor="@color/white"
                android:fontFamily="@font/poppins_medium"/>

            <!-- Explain MCQ Button -->
            <Button
                android:id="@+id/btnExplainMCQ"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:paddingStart="5dp"
                android:paddingEnd="5dp"
                android:layout_columnWeight="1"
                android:layout_margin="6dp"
                android:text="Explain MCQ"
                android:textSize="12sp"
                android:textAllCaps="false"
                android:background="@drawable/button_background_filled"
                android:backgroundTint="@color/colorAccent"
                android:drawableStart="@drawable/explain"
                android:drawableTint="@color/white"
                android:textColor="@color/white"
                android:fontFamily="@font/poppins_medium"/>

            <!-- Create Similar MCQs Button -->
            <Button
                android:id="@+id/btnCreateSimilar"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:paddingStart="5dp"
                android:paddingEnd="5dp"
                android:layout_columnWeight="1"
                android:layout_margin="6dp"
                android:background="@drawable/button_background_filled"
                android:backgroundTint="@color/colorAccent"
                android:fontFamily="@font/poppins_medium"
                android:drawableStart="@drawable/similarmcq"
                android:drawableTint="@color/white"
                android:text="Similar MCQs"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="12sp" />

            <!-- Show History Button -->
            <Button
                android:id="@+id/btnShowHistory"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:paddingStart="5dp"
                android:paddingEnd="5dp"
                android:layout_columnWeight="1"
                android:layout_margin="6dp"
                android:text="Show History"
                android:textSize="12sp"
                android:textAllCaps="false"
                android:background="@drawable/button_background_filled"
                android:backgroundTint="@color/colorAccent"
                android:drawableStart="@drawable/showhistory"
                android:drawableTint="@color/white"
                android:textColor="@color/white"
                android:fontFamily="@font/poppins_medium"/>
        </GridLayout>

    </LinearLayout>
</androidx.cardview.widget.CardView>
