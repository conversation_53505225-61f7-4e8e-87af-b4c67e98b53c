<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="0.6dp"
                android:left="0.6dp"
                android:right="0.6dp"
                android:top="0.6dp" />

            <solid android:color="#00FFFFFF" />

            <corners android:radius="10dp" />

        </shape>
    </item>

    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="0.6dp"
                android:left="0.6dp"
                android:right="0.6dp"
                android:top="0.6dp" />

            <solid android:color="#00FFFFFF" />

            <corners android:radius="10dp" />
        </shape>
    </item>

    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="0.6dp"
                android:left="0.6dp"
                android:right="0.6dp"
                android:top="0.6dp" />

            <solid android:color="#30CCCCCC" />

            <corners android:radius="10dp" />
        </shape>
    </item>

    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="0.6dp"
                android:left="0.6dp"
                android:right="0.6dp"
                android:top="0.6dp" />

            <corners android:radius="10dp" />

            <gradient
                android:angle="180"
                android:centerColor="#21CCCCCC"
                android:endColor="#21CCCCCC"
                android:startColor="#21CCCCCC"
                android:type="linear" />

        </shape>
    </item>

    <item
        android:bottom="0.6dp"
        android:left="0.6dp"
        android:right="0.6dp"
        android:top="0.6dp">
        <shape android:shape="rectangle">
            <corners android:radius="10dp" />
            <gradient
                android:angle="360"
                android:endColor="@color/primary_bg_red"
                android:startColor="@color/primary_bg_red"
                android:type="linear" />
        </shape>
    </item>
    <item
        android:gravity="center_vertical|right"
        android:left="5dp"
        android:right="7dp">
        <layer-list>
            <item>
                <bitmap
                    android:gravity="center_vertical|right"
                    android:src="@drawable/ic_arrow"
                    android:tint="@color/white" />
            </item>
        </layer-list>
    </item>
</layer-list>