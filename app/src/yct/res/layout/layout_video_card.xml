<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/videoMainLayout">

    <androidx.cardview.widget.CardView
        android:id="@+id/videoCardLayout"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        app:cardElevation="2dp"
        android:padding="5dp"
        android:foreground="@drawable/ripple_dark"
        app:cardUseCompatPadding="true"
        app:cardCornerRadius="4dp"
        android:clickable="true"
        app:cardBackgroundColor="@color/white">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@color/white"
            android:clickable="false">
            <LinearLayout
                android:id="@+id/videolayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="4"
                android:baselineAligned="false">




                <RelativeLayout
                    android:id="@+id/videothumbnaillayout"
                    android:layout_width="350dp"
                    android:layout_height="110dp"
                    android:layout_marginLeft="5dp"
                    android:background="@drawable/round_edge_card_view"
                    android:layout_weight="2">


                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        app:cardCornerRadius="8dp"
                        android:layout_margin="5dp"
                        android:elevation="10dp">


                        <ImageView
                            android:id="@+id/videolinkthumbnail"
                            android:layout_width="match_parent"
                            android:layout_centerVertical="true"
                            android:layout_height="100dp"
                            android:layout_gravity="center"
                            android:adjustViewBounds="true"
                            android:scaleType="fitXY"
                            android:background="@color/white"
                            />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:srcCompat="@drawable/ic_play_circle_filled_24dp"
                            android:layout_centerInParent="true"
                            android:layout_gravity="center"
                            android:background="@android:color/transparent"
                            android:scaleType="center"/>

                    </androidx.cardview.widget.CardView>





                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_weight="2"
                    android:layout_height="match_parent"
                    android:paddingTop="10dp"
                    android:background="@color/white"
                    android:orientation="vertical"
                    android:layout_marginStart="5dp">

                    <TextView
                        android:id="@+id/videolinktitle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="5dp"
                        android:typeface="normal"
                        android:textColor="@color/colorActionBarText"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:maxLines="2"
                        android:ellipsize="middle"/>

                    <View
                        android:id="@+id/bottomLine"
                        android:layout_width="match_parent"
                        android:layout_gravity="center"
                        android:layout_height="1dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginStart="5dp"
                        android:layout_marginBottom="5dp"
                        android:layout_marginEnd="10dp"
                        android:background="@color/primary_bg_red"/>

                    <RelativeLayout
                        android:id="@+id/videoDetails"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent">

                        <TextView
                            android:id="@+id/videoDesciption"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:typeface="normal"
                            android:layout_marginStart="5dp"
                            android:textColor="@color/gray"
                            android:textSize="10sp"
                            android:gravity="start"

                            android:ellipsize="marquee" />


                    </RelativeLayout>

                </LinearLayout>


            </LinearLayout>

        </RelativeLayout>

    </androidx.cardview.widget.CardView>

</RelativeLayout>