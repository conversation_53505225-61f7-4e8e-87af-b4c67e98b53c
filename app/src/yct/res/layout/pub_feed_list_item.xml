<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardBackgroundColor="@color/purple_700"
        android:layout_margin="@dimen/_7sdp"
        app:cardCornerRadius="6dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/feed_img"
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_alignParentStart="true"
                    android:padding="@dimen/_5sdp"
                    android:scaleType="fitXY" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/_5sdp"
                    android:layout_toEndOf="@id/feed_img">
                    <TextView
                        android:id="@+id/pub_feed_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:padding="3dp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/pub_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/white"
                        android:textSize="13sp"
                        android:padding="5dp"
                        android:layout_marginTop="7dp"
                        android:textStyle="italic"
                        android:maxLines="3"
                        android:ellipsize="end" />
                </LinearLayout>

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/feed_item_bottom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_marginBottom="@dimen/_4sdp"
                android:layout_marginLeft="@dimen/_4sdp"
                android:layout_gravity="bottom">
                <TextView
                    android:id="@+id/pub_feed_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:textStyle="italic"
                    android:layout_alignParentStart="true"/>
            </RelativeLayout>
        </LinearLayout>

    </androidx.cardview.widget.CardView>

</LinearLayout>