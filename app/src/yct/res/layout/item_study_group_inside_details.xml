<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/primary_bg_dark">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shadow_line_top">

        <LinearLayout
            android:id="@+id/layout_post_author"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="6" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="15dp"
                android:layout_weight="1"
                android:background="@drawable/textview_rounded"
                android:fontFamily="@font/poppins_medium"
                android:text="admin"
                android:textAlignment="center"
                android:textColor="#ae3691"
                android:textSize="12sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/layout_user"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/layout_post_author"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="15dp"
            android:layout_marginRight="10dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/relativeLayout_avatar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_gravity="center">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/avatar_bg_circle"
                    android:gravity="center">

                    <androidx.cardview.widget.CardView
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_centerHorizontal="true"
                        android:elevation="25dp"
                        app:cardCornerRadius="60dp">

                        <ImageView
                            android:id="@+id/imageView_avatar"
                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:scaleType="centerCrop"
                            app:srcCompat="@drawable/profile_icon" />
                    </androidx.cardview.widget.CardView>
                </RelativeLayout>

            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center|left"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textview_user"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center|left"
                    android:layout_marginLeft="5dp"
                    android:fontFamily="@font/poppins_medium"
                    android:paddingTop="2dp"
                    android:text="User name"
                    android:textColor="@color/white"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/textview_date_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_gravity="center"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="1dp"
                    android:fontFamily="@font/poppins_light"
                    android:gravity="center"
                    android:paddingTop="2dp"
                    android:text="02.09.2021"
                    android:textColor="@color/light_white"
                    android:textSize="10sp" />

            </LinearLayout>

        </LinearLayout>

        <ImageView
            android:id="@+id/imageView_report_menu"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_below="@+id/layout_post_author"
            android:layout_alignParentRight="true"
            android:layout_marginTop="18dp"
            android:layout_marginRight="15dp"
            android:src="@drawable/ic_report_menu"
            app:tint="@color/white" />

        <TextView
            android:id="@+id/textView_group_details_item"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/layout_user"
            android:layout_marginLeft="25dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="10dp"
            android:autoLink="all"
            android:autoSizeMaxTextSize="14sp"
            android:autoSizeMinTextSize="8sp"
            android:ellipsize="end"
            android:fontFamily="@font/poppins_medium"
            android:text="Thermodynamics - Jargon revision\nGorup UPSC"
            android:textColor="@color/white"
            android:textColorLink="@color/video_title_color_land"
            android:textSize="13sp" />

        <TextView
            android:id="@+id/textView_group_url_details_item"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/textView_group_details_item"
            android:layout_marginLeft="25dp"
            android:layout_marginTop="1dp"
            android:layout_marginRight="10dp"
            android:autoLink="web"
            android:visibility="gone"
            android:autoSizeMaxTextSize="14sp"
            android:autoSizeMinTextSize="8sp"
            android:ellipsize="end"
            android:fontFamily="@font/poppins_medium"
            android:linksClickable="true"
            android:maxLines="10"
            android:text="Thermodynamics - Jargon revision\nGorup UPSC"
            android:textColor="@color/white"
            android:textColorLink="@color/video_title_color_land"
            android:textSize="13sp" />

        <RelativeLayout
            android:id="@+id/layout_attachment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/textView_group_url_details_item">

            <androidx.cardview.widget.CardView
                android:id="@+id/post_image_card"
                android:layout_width="match_parent"
                android:layout_height="320dp"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="20dp"
                android:layout_marginBottom="10dp"
                android:clickable="true"
                android:foreground="@drawable/ripple_dark"
                android:visibility="gone"
                app:cardCornerRadius="4dp"
                app:cardElevation="2dp"
                app:cardUseCompatPadding="true">

                <ImageView
                    android:id="@+id/imageView_post_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:scaleType="fitXY"
                    android:src="@drawable/coverone"
                    android:visibility="gone" />
            </androidx.cardview.widget.CardView>

            <ImageView
                android:id="@+id/imageView_post_file"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="25dp"
                android:layout_marginRight="10dp"
                android:scaleType="fitXY"
                android:src="@drawable/ic_file_upload"
                android:visibility="gone" />

            <TextView
                android:id="@+id/textView_file_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginRight="5dp"
                android:layout_toRightOf="@+id/imageView_post_file"
                android:fontFamily="@font/poppins_medium"
                android:paddingTop="3dp"
                android:text="file"
                android:textColor="@color/red"
                android:textSize="12sp"
                android:visibility="gone" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/layout_like_comment_date"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/layout_attachment"
            android:layout_marginLeft="25dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="1dp">

            <ImageView
                android:id="@+id/imageView_like_date"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:src="@drawable/ic_like_new" />

            <TextView
                android:id="@+id/textView_like_count_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="5dp"
                android:layout_toRightOf="@+id/imageView_like_date"
                android:fontFamily="@font/poppins_medium"
                android:gravity="center"
                android:paddingTop="3dp"
                android:text="10K"
                android:textColor="@color/white"
                android:textSize="10sp" />


            <TextView
                android:id="@+id/textView_show_comments_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginLeft="30dp"
                android:layout_marginRight="20dp"
                android:fontFamily="@font/poppins_medium"
                android:text="Show Commends"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:textStyle="italic"
                android:visibility="visible" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/layout_like_comment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/layout_like_comment_date"
            android:layout_marginLeft="25dp"
            android:layout_marginTop="1dp"
            android:layout_marginRight="25dp"
            android:layout_marginBottom="5dp">


            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:background="@drawable/shadow_curved_layout_bg"
                android:padding="5dp">

                <LinearLayout
                    android:id="@+id/layout_comment_user"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:orientation="horizontal">

                    <RelativeLayout
                        android:id="@+id/relativeLayout_comment_avatar"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_gravity="center">

                        <RelativeLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerHorizontal="true"
                            android:background="@drawable/avatar_bg_circle_white"
                            android:gravity="center">

                            <androidx.cardview.widget.CardView
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:layout_centerHorizontal="true"
                                android:elevation="12dp"
                                app:cardCornerRadius="60dp">

                                <ImageView
                                    android:id="@+id/imageView_comment_avatar"
                                    android:layout_width="20dp"
                                    android:layout_height="20dp"
                                    android:scaleType="centerCrop"
                                    app:srcCompat="@drawable/profile_icon" />
                            </androidx.cardview.widget.CardView>
                        </RelativeLayout>

                    </RelativeLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center|left"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/textview_comment_user"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentTop="true"
                            android:layout_gravity="center|left"
                            android:layout_marginLeft="5dp"
                            android:text="User name"
                            android:textColor="@color/gray"
                            android:textSize="10sp" />

                        <TextView
                            android:id="@+id/textview_comment_date_time"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentTop="true"
                            android:layout_gravity="center"
                            android:layout_marginLeft="10dp"
                            android:text="02.09.2021"
                            android:textColor="@color/crop__button_text"
                            android:textSize="8sp" />
                    </LinearLayout>
                </LinearLayout>

                <TextView
                    android:id="@+id/textView_commend_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/layout_comment_user"
                    android:layout_marginLeft="40dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="10dp"
                    android:autoLink="all"
                    android:autoSizeMaxTextSize="13sp"
                    android:autoSizeMinTextSize="10sp"
                    android:ellipsize="end"
                    android:fontFamily="@font/poppins_regular"
                    android:maxLength="200"
                    android:maxLines="5"
                    android:text="My commend here"
                    android:textColor="@color/black"
                    android:textColorLink="@color/video_title_color_land"
                    android:textSize="13sp" />


            </RelativeLayout>


        </RelativeLayout>

        <View
            android:id="@+id/bottom_divider"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_below="@+id/layout_like_comment"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:background="@color/light_white" />

        <RelativeLayout
            android:id="@+id/layout_bottom_footer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/bottom_divider"
            android:layout_marginLeft="25dp"
            android:layout_marginTop="5dp"
            android:layout_marginRight="25dp"
            android:layout_marginBottom="5dp">

            <TextView
                android:id="@+id/textView_like_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="5dp"
                android:fontFamily="@font/poppins_medium"
                android:text="10K"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:visibility="gone" />

            <ImageView
                android:id="@+id/imageView_like"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_marginLeft="40dp"
                android:layout_marginTop="5dp"
                android:layout_toRightOf="@+id/textView_like_count"
                android:src="@drawable/ic_like_new" />

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/animationViewLike"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_marginLeft="40dp"
                android:layout_marginTop="5dp"
                android:layout_toRightOf="@+id/textView_like_count"
                android:visibility="gone"
                app:lottie_autoPlay="true"
                app:lottie_loop="true"
                app:lottie_rawRes="@raw/lottie_link" />

            <TextView
                android:id="@+id/textView_group_public_private"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/imageView_like"
                android:layout_marginLeft="37dp"
                android:text="Liked"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:visibility="invisible" />

            <ImageView
                android:id="@+id/imageView_share_item"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="15dp"
                android:layout_toRightOf="@+id/textView_group_public_private"
                android:src="@drawable/is_share_circul"
                android:visibility="gone" />

            <TextView
                android:id="@+id/textView_commend_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginRight="5dp"
                android:layout_toLeftOf="@+id/imageView_commend"
                android:fontFamily="@font/poppins_medium"
                android:text="22"
                android:textColor="@color/gray"
                android:textSize="10sp"
                android:visibility="gone" />

            <ImageView
                android:id="@+id/imageView_commend"
                android:layout_width="23dp"
                android:layout_height="23dp"
                android:layout_alignParentRight="true"
                android:layout_marginTop="6dp"
                android:layout_marginRight="40dp"
                android:layout_toLeftOf="@+id/textView_group_public_private"
                android:src="@drawable/ic_comment"
                android:visibility="visible" />

            <TextView
                android:id="@+id/textView_show_comments"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/imageView_commend"
                android:layout_alignParentRight="true"
                android:layout_marginRight="25dp"
                android:layout_toLeftOf="@+id/textView_group_public_private"
                android:text="Comment"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:visibility="visible" />

            <View
                android:layout_width="0.5dp"
                android:layout_height="20dp"
                android:layout_centerInParent="true"
                android:background="@color/light_gray" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/layout_comment"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_below="@+id/layout_bottom_footer"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="5dp"
            android:layout_marginRight="5dp"
            android:layout_marginBottom="2dp"
            android:background="@drawable/shadow_curved_layout_bg"
            android:visibility="gone">

            <EditText
                android:id="@+id/editText_comment"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_toLeftOf="@+id/imageView_send_comment"
                android:background="@null"
                android:fontFamily="@font/poppins_medium"
                android:gravity="center|left"
                android:hint="Write your comment here.."
                android:maxLength="100"
                android:maxLines="2"
                android:paddingStart="15dp"
                android:textColor="@color/colorActionBarText"
                android:textColorHint="@color/gray"
                android:textSize="12sp"
                android:visibility="visible" />

            <ImageView
                android:id="@+id/imageView_send_comment"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_alignParentRight="true"
                android:background="@drawable/button_right_corner_full_radius"
                android:padding="6dp"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_send" />
        </RelativeLayout>

        <!--<TextView
            android:id="@+id/textView_show_comments"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/layout_comment"
            android:layout_centerVertical="true"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:fontFamily="@font/poppins_medium"
            android:text="Show Commends"
            android:textColor="@color/gradientColorStart"
            android:textSize="10sp"
            android:textStyle="italic"
            android:visibility="visible" />-->

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView_groups_comments_and_replay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="5dp"
            android:layout_marginRight="15dp"
            android:layout_marginBottom="5dp"
            android:background="@color/very_light_gray"
            android:visibility="gone" />
    </RelativeLayout>
</RelativeLayout>

