<?xml version="1.0" encoding="utf-8"?>
<androidx.swiperefreshlayout.widget.SwipeRefreshLayout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/merged_feed_swipe_refresh"
    tools:context="com.wonderslate.prepjoy.news.PubFeedDetails"
    android:background="@color/primary_bg_dark">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/primary_bg_dark">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/merged_feed_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/scroll_to_top"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_arrow_up"
            android:layout_alignParentBottom="true"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="10dp"
            android:layout_marginBottom="30dp"
            android:clickable="true"
            android:contentDescription="scrollToTop"
            android:focusable="true"
            app:backgroundTint="@color/primary_bg_red"
            app:fabSize="mini"
            app:rippleColor="@color/white"
            app:useCompatPadding="true"/>

    </RelativeLayout>
</androidx.swiperefreshlayout.widget.SwipeRefreshLayout>