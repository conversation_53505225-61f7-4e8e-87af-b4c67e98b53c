<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        android:backgroundTint="@color/colorPrimary"
        android:foreground="?selectableItemBackground"
        app:cardCornerRadius="@dimen/_8sdp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_16sdp">

            <LinearLayout
                android:id="@+id/linearLayout2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tvCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_20ssp"
                    android:textStyle="bold"
                    android:layout_marginEnd="@dimen/_4sdp"
                    android:text="13" />

                <ImageView
                    android:id="@+id/ivResIcon"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/_20sdp"
                    app:tint="@color/white"
                    android:alpha="0.6"
                    android:src="@drawable/ic_lock_white_24dp" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvResType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAlignment="center"
                android:layout_marginTop="@dimen/_8sdp"
                app:layout_constraintTop_toBottomOf="@+id/linearLayout2"
                android:text="Reading material"
                android:textColor="@color/white"
                android:alpha="0.6"
                android:textSize="@dimen/_10ssp"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>