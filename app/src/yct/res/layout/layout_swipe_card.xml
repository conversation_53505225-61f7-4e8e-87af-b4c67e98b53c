<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rlcard"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/lltparent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/round_edge_card_view"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="@dimen/_10sdp">

    <TextView
        android:id="@+id/txtTodayQuiz"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="Today's Quiz"
        android:textColor="@color/primary_bg_red"
        android:textSize="15sp"
        android:visibility="gone"/>

        <TextView
            android:id="@+id/txtDateVal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:text="20 Sept 2021"
            android:textColor="@color/white"
            android:textSize="30sp" />

        <Button
            android:id="@+id/btnOption1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/quiz_button_bg"
            android:gravity="center"
            android:textAllCaps="false"
            android:paddingBottom="@dimen/_5sdp"
            android:text="@string/daily_quiz_option"
            android:textColor="@color/white"
            android:textSize="@dimen/_11ssp" />


        <Button
            android:id="@+id/btnOption2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/round_edge_bg"
            android:paddingBottom="@dimen/_5sdp"
            android:text="@string/read"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="@dimen/_11ssp" />

        <Button
            android:id="@+id/btnOption3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/round_edge_bg"
            android:paddingBottom="@dimen/_5sdp"
            android:text="@string/watch"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="@dimen/_11ssp" />

        <Button
            android:id="@+id/btnOption4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/round_edge_bg"
            android:paddingBottom="@dimen/_5sdp"
            android:textColor="@color/white"
            android:textSize="@dimen/_11ssp"
            android:visibility="gone" />

        <Button
            android:id="@+id/btnOption5"
            android:layout_width="wrap_content"
            android:layout_height="60dp"
            android:background="@drawable/round_edge_bg"
            android:paddingBottom="@dimen/_5sdp"
            android:text="Read"
            android:visibility="gone"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="@dimen/_11ssp" />

        <Button
            android:id="@+id/btnOption6"
            android:layout_width="wrap_content"
            android:layout_height="60dp"
            android:background="@drawable/round_edge_bg"
            android:paddingBottom="@dimen/_5sdp"
            android:text="History"
            android:visibility="gone"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="@dimen/_11ssp" />
    </LinearLayout>
</RelativeLayout>