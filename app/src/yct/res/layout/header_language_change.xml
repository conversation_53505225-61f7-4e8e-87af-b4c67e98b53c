<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/primary_bg_dark"
    android:paddingLeft="5dp"
    android:paddingRight="5dp">

    <RelativeLayout
        android:id="@+id/rrltBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            app:srcCompat="@drawable/ic_back" />

    <TextView
        android:id="@+id/txtBack"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:textSize="17dp"
        android:gravity="center"
        android:layout_marginLeft="5dp"
        android:layout_toRightOf="@+id/imgBack"
        android:textStyle="bold"
        android:layout_centerVertical="true"
        android:text="Home"
        android:textColor="@color/white"/>

    </RelativeLayout>

    <ToggleButton
        android:id="@+id/toggleLanguage"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:background="@drawable/language_toggle"
        android:backgroundTint="@color/primary_bg_red"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:paddingTop="20dp"
        android:textOff=""
        android:textOn="" />
</RelativeLayout>