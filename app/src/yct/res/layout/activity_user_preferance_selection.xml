<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_bg_dark">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"
            android:layout_marginTop="12dp"
            android:layout_marginRight="10dp"
            android:src="@drawable/prepjoy_icon"
            android:visibility="gone" />

        <RelativeLayout
            android:id="@+id/layout_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="20dp"
            android:gravity="center">

            <TextView
                android:id="@+id/textView_title_level"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_gravity="center"
                android:ellipsize="end"
                android:fontFamily="@font/poppins_regular"
                android:gravity="center"
                android:singleLine="true"
                android:text="Choose your category"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:textStyle="normal"
                android:visibility="visible" />

            <TextView
                android:id="@+id/textView_title_level_stage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_gravity="center"
                android:layout_toRightOf="@+id/textView_title_level"
                android:ellipsize="end"
                android:fontFamily="@font/poppins_bold"
                android:gravity="center"
                android:singleLine="true"
                android:text=" Syllabus."
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                android:visibility="gone" />
        </RelativeLayout>




        <RelativeLayout
            android:id="@+id/rrltBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_below="@+id/layout_title"
            android:visibility="gone">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/imgBack"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:visibility="visible"
                app:srcCompat="@drawable/ic_back" />

            <Button
                android:id="@+id/button_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="3dp"
                android:layout_toEndOf="@+id/imgBack"
                android:background="@color/primary_bg_dark"
                android:gravity="center|left"
                android:text="Back"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="17dp"
                android:textStyle="bold" />

        </RelativeLayout>


        <Button
            android:id="@+id/button_clear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/layout_title"
            android:layout_alignParentRight="true"
            android:layout_marginRight="10dp"
            android:background="@color/primary_bg_dark"
            android:gravity="center|right"
            android:text="Clear"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="17dp"
            android:textStyle="bold"
            android:visibility="invisible" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView_level"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/button_next_level"
            android:layout_below="@+id/button_clear"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="5dp"
            android:layout_marginRight="15dp"
            android:background="@color/primary_bg_dark"
            android:nestedScrollingEnabled="false"
            android:scrollbars="none" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView_syllabus"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/button_next_level"
            android:layout_below="@+id/rrltBack"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="15dp"
            android:nestedScrollingEnabled="false"
            android:scrollbars="none"
            android:visibility="gone" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView_grade"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/button_next_level"
            android:layout_below="@+id/rrltBack"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="15dp"
            android:nestedScrollingEnabled="false"
            android:scrollbars="none"
            android:visibility="gone" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView_subject"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/button_next_level"
            android:layout_below="@+id/rrltBack"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="15dp"
            android:nestedScrollingEnabled="false"
            android:scrollbars="none"
            android:visibility="gone" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView_sub_subject"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/button_next_level"
            android:layout_below="@+id/rrltBack"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="15dp"
            android:nestedScrollingEnabled="false"
            android:scrollbars="none"
            android:visibility="gone" />

        <Button
            android:id="@+id/button_next_level"
            android:layout_width="150dp"
            android:layout_height="48dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:layout_marginBottom="10dp"
            android:background="@drawable/button_shape_disabled"
            android:gravity="center"
            android:text="Next"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <com.wang.avi.AVLoadingIndicatorView
            android:id="@+id/loaderMain"
            style="@style/AVLoadingIndicatorView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:visibility="gone"
            app:indicatorColor="@color/primary_bg_red"
            app:indicatorName="BallBeatIndicator" />

        <RelativeLayout
            android:id="@+id/layout_issue"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:visibility="gone">

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/lottie_view"
                android:layout_width="300dp"
                android:layout_height="300dp"
                android:layout_centerInParent="true"
                app:lottie_autoPlay="true"
                app:lottie_loop="true"
                app:lottie_rawRes="@raw/lottie_server" />

            <TextView
                android:id="@+id/textView_server_message"
                android:layout_width="250dp"
                android:layout_height="wrap_content"
                android:layout_below="@+id/lottie_view"
                android:layout_centerHorizontal="true"
                android:fontFamily="@font/poppins_regular"
                android:gravity="center"
                android:text=""
                android:textColor="@color/black"
                android:textSize="12sp"
                android:textStyle="bold" />
        </RelativeLayout>
    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>