<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/_65sdp"
    android:id="@+id/lltLastQuiz"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal"
    android:paddingLeft="12dp"
    android:paddingRight="12dp"
    android:gravity="center"
    android:weightSum="2.5"
    android:background="@color/primary_bg_dark"
    android:layout_margin="4dp">

    <TextView
        android:id="@+id/txtSerialNum"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="0.25"
        android:text="@string/rank"
        android:textColor="@color/white"
        android:textSize="@dimen/_10ssp"/>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="@dimen/_50sdp"
        android:layout_weight="2.25"
        android:background="@drawable/bg_round_border"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingLeft="3dp"
        android:paddingRight="3dp">


        <LinearLayout
            android:layout_width="0dp"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/txtDate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:ellipsize="end"
                android:gravity="left"
                android:maxEms="10"
                android:maxLines="1"
                android:layout_marginLeft="@dimen/_4sdp"
                android:text="@string/rank"
                android:textColor="@color/white"
                android:textSize="@dimen/_10ssp"/>


        </LinearLayout>

        <TextView
            android:id="@+id/txtScore"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="0.5"
            android:gravity="center"
            android:text="@string/rank"
            android:textColor="@color/white"
            android:textSize="@dimen/_10ssp" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:src="@drawable/ic_arrow_right_small"
            />





    </LinearLayout>
</LinearLayout>