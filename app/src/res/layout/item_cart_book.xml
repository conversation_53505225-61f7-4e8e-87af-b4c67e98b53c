<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="8dp"
        app:cardCornerRadius="8dp"
        android:layout_marginHorizontal="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            android:padding="8dp">

            <androidx.cardview.widget.CardView
                android:id="@+id/cardView2"
                android:layout_width="100dp"
                android:layout_height="0dp"
                android:elevation="0dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="9:12"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/ivBookCover"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    android:src="@drawable/doodle"
                    tools:srcCompat="@tools:sample/avatars" />

            </androidx.cardview.widget.CardView>

            <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                android:id="@+id/btnApplyDiscount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:background="@drawable/button_bg_discount_green"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?selectableItemBackground"
                android:gravity="center"
                android:paddingHorizontal="7dp"
                android:paddingVertical="4dp"
                android:textColor="@color/white"
                android:textSize="14sp"
                app:layout_constrainedWidth="true"
                app:layout_constrainedHeight="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintStart_toEndOf="@+id/tvBookPrice"
                app:layout_constraintTop_toTopOf="@+id/tvBookDiscountPrice"
                app:layout_constraintVertical_bias="0.0"
                tools:text="Apply 20% discount" />

            <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                android:id="@+id/tvBookTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="12dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="Long or very long book name sfh f gh sjfs fjh bhsj s jdhf bhjsb  bjshdb jhsb jhbf jjfh"
                android:textColor="@color/black"
                android:textSize="16sp"
                app:layout_constraintEnd_toStartOf="@+id/btnDelete"
                app:layout_constraintStart_toEndOf="@+id/cardView2"
                app:layout_constraintTop_toTopOf="@+id/cardView2" />

            <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                android:id="@+id/tvPublisherTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="ARIHANT"
                android:textSize="10sp"
                app:layout_constraintStart_toStartOf="@+id/tvBookTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvBookTitle" />

            <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                android:id="@+id/tveBookType"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="5dp"
                android:text="Online Test Series"
                android:textSize="10sp"
                app:layout_constraintStart_toStartOf="@+id/tvPublisherTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvPublisherTitle" />

            <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                android:id="@+id/tvValidityDays"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="5dp"
                tools:text="Validity: 1 year"
                android:textSize="10sp"
                app:layout_constraintStart_toStartOf="@+id/tveBookType"
                app:layout_constraintTop_toBottomOf="@+id/tveBookType" />

            <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                android:id="@+id/tvBookDiscountPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp"
                android:text="100"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/cardView2"
                app:layout_constraintStart_toStartOf="@+id/tvBookPrice"
                app:ws_font_weight="bold" />

            <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                android:id="@+id/tvBookPrice"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="₹180"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:layout_marginTop="5dp"
                app:layout_constraintBottom_toTopOf="@+id/tvBookDiscountPrice"
                app:layout_constraintStart_toStartOf="@+id/tvValidityDays"
                app:layout_constraintTop_toBottomOf="@id/tvValidityDays"
                app:ws_font_weight="bold" />

            <ImageView
                android:id="@+id/btnDelete"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?selectableItemBackground"
                android:padding="8dp"
                android:src="@drawable/ic_delete_red"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/main_theme_red" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

</FrameLayout>