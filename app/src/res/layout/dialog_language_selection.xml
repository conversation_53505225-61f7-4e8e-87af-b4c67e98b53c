<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="right">

        <Button
            android:id="@+id/btnCacle"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:backgroundTint="@color/primary_bg_red"
            android:foregroundTint="@color/primary_bg_red"
            android:background="@android:drawable/ic_menu_close_clear_cancel"/>


    </RelativeLayout>

    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="left"
        android:text="Choose a language"
        android:textStyle="bold"
        android:paddingLeft="30dp"
        android:paddingRight="30dp"
        android:textColor="@color/primary_bg_red"
        android:textSize="15sp" />

    <com.google.android.material.progressindicator.LinearProgressIndicator
        android:id="@+id/progressIndicator"
        android:layout_marginTop="@dimen/_10sdp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:indicatorDirectionLinear="startToEnd"
        app:indeterminateAnimationType="disjoint"
        app:indicatorColor="@color/primary_bg_red"
        />
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/actionContainer"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="20dp"
        android:paddingLeft="30dp"
        android:paddingRight="30dp"
        android:paddingBottom="30dp"
        android:gravity="center"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btnEnglish"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:text="English"
            android:layout_weight="1"
            android:layout_marginEnd="5dp"
            android:background="@drawable/round_edge_red"
            android:textColor="@color/white" />

        <Button
            android:id="@+id/btnHindi"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:background="@drawable/round_edge_red"
            android:text="Hindi"
            android:layout_marginStart="5dp"
            android:layout_weight="1"
            android:textColor="@color/white" />
    </LinearLayout>
    <Button
        android:id="@+id/btnOk"
        android:visibility="gone"
        android:layout_marginBottom="30dp"
        android:layout_marginTop="20dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/round_edge_red"
        android:text="Ok"
        android:layout_marginStart="5dp"
        android:textColor="@color/white" />

</LinearLayout>