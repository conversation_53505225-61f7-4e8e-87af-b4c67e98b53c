<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_bg_dark"
    tools:context=".ui.chapters_list.HistoryForQuiz">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clHeader"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_50sdp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:gravity="center_vertical"
        android:elevation="@dimen/_8sdp">

        <androidx.cardview.widget.CardView
            android:id="@+id/btnBack"
            android:layout_width="@dimen/_32sdp"
            android:layout_height="@dimen/_32sdp"
            android:backgroundTint="@color/colorChapterListCardBg"
            app:cardCornerRadius="@dimen/_8sdp"
            android:foreground="?selectableItemBackground"
            android:layout_marginStart="@dimen/_10sdp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:src="@drawable/ic_new_back_arrow"
                android:contentDescription="Go back"/>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/btnRefresh"
            android:layout_width="@dimen/_32sdp"
            android:layout_height="@dimen/_32sdp"
            android:backgroundTint="@color/colorChapterListCardBg"
            android:layout_marginEnd="@dimen/_10sdp"
            android:foreground="?selectableItemBackground"
            app:cardCornerRadius="@dimen/_8sdp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:src="@drawable/ic_refresh"
                android:contentDescription="Share book" />
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/cardBook"
            android:layout_width="@dimen/_50sdp"
            android:layout_height="@dimen/_70sdp"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="16dp"
            android:clickable="true"
            android:focusable="true"
            android:visibility="gone"
            android:foreground="?selectableItemBackground"
            app:cardCornerRadius="6dp"
            app:cardElevation="5dp"
            app:cardPreventCornerOverlap="true"
            app:cardUseCompatPadding="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/btnBack"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/ivBookCover"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:adjustViewBounds="true"
                android:scaleType="centerCrop"
                android:src="@drawable/book_cover_placeholder" />
        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/tvHistoryName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textSize="@dimen/_14ssp"
            android:layout_marginHorizontal="@dimen/_8sdp"
            android:textColor="@color/white"
            android:maxLines="2"
            android:ellipsize="end"
            android:text="History"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/btnRefresh"
            app:layout_constraintStart_toEndOf="@+id/cardBook"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <TextView
        android:id="@+id/textQuizName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:gravity="center"
        android:paddingLeft="@dimen/_20sdp"
        android:paddingRight="@dimen/_20sdp"
        android:text="Quiz"
        android:textColor="@color/white"
        android:textSize="@dimen/_12sdp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintBottom_toTopOf="@id/recHistory"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/clHeader"
        app:layout_constraintVertical_bias="0.0"
        tools:listitem="@layout/item_layout_chapter" />

    <LinearLayout
        android:id="@+id/lltTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_5sdp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textQuizName"
        app:layout_constraintVertical_bias="0.0">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/_20sdp"
            android:layout_marginEnd="@dimen/_20sdp"
            android:layout_weight="0.1"
            android:gravity="center"
            android:text=""
            android:visibility="gone"
            android:textColor="@color/white"
            android:textSize="@dimen/_8sdp" />


        <TextView

            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center|start"
            android:layout_marginStart="@dimen/_20sdp"
            android:layout_marginEnd="@dimen/_20sdp"
            android:layout_weight="2.5"
            android:gravity="center|left"
            android:text="Date"
            android:paddingLeft="70dp"
            android:textColor="@color/white"
            android:textSize="@dimen/_8sdp" />


        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/_9sdp"
            android:layout_marginEnd="@dimen/_40sdp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Score"
            android:textColor="@color/white"
            android:textSize="@dimen/_8sdp" />

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recHistory"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:paddingLeft="@dimen/_16sdp"
        android:paddingRight="@dimen/_16sdp"
        android:paddingBottom="@dimen/_16sdp"
        tools:listitem="@layout/item_quiz_history"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lltTitle" />


    <LinearLayout
        android:id="@+id/noDataLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center|top"
        android:layout_margin="@dimen/_16sdp"
        android:background="@drawable/all_course_list_item_background"
        android:elevation="6dp"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.497"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.189"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/emptyImage"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:gravity="center"
            android:src="@drawable/ic_book" />

        <com.ws.core_ui.custom_views.WSTextView
            android:id="@+id/emptytextview"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="3dp"
            android:text="You have not attempted the quiz"
            android:textColor="@color/colorBookNameTitle"
            android:textSize="12sp"
            android:typeface="normal" />

    </LinearLayout>

    <com.wang.avi.AVLoadingIndicatorView
        android:id="@+id/groupLoader_details"
        style="@style/AVLoadingIndicatorView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:indicatorColor="@color/primary_bg_red"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:indicatorName="BallBeatIndicator" />

</androidx.constraintlayout.widget.ConstraintLayout>