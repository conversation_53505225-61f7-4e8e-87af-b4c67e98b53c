<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:padding="5dp">

        <LinearLayout
            android:id="@+id/layout_user"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/relativeLayout_avatar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_gravity="center">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/avatar_bg_circle_white"
                    android:gravity="center">

                    <androidx.cardview.widget.CardView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_centerHorizontal="true"
                        android:elevation="12dp"
                        app:cardCornerRadius="60dp">

                        <ImageView
                            android:id="@+id/imageView_avatar"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:scaleType="centerCrop"
                            app:srcCompat="@drawable/profile_icon" />
                    </androidx.cardview.widget.CardView>
                </RelativeLayout>

            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center|left"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textview_user"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_gravity="center|left"
                    android:layout_marginLeft="5dp"
                    android:text="User name"
                    android:textColor="@color/gray"
                    android:textSize="10sp" />

                <TextView
                    android:id="@+id/textview_date_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_gravity="center"
                    android:layout_marginLeft="10dp"
                    android:text="02.09.2021"
                    android:textColor="@color/crop__button_text"
                    android:textSize="8sp" />
            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/textView_commend_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/layout_user"
            android:layout_marginLeft="40dp"
            android:layout_marginTop="5dp"
            android:layout_marginRight="10dp"
            android:autoLink="all"
            android:autoSizeMaxTextSize="13sp"
            android:autoSizeMinTextSize="10sp"
            android:ellipsize="end"
            android:fontFamily="@font/poppins_regular"
            android:text="My commend here"
            android:textColor="@color/black"
            android:textColorLink="@color/video_title_color_land"
            android:textSize="13sp" />

        <TextView
            android:id="@+id/textView_show_replay"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/textView_commend_text"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="5dp"
            android:fontFamily="@font/poppins_regular"
            android:text="Show Replies"
            android:textColor="@color/black"
            android:textSize="12sp"
            android:textStyle="italic"
            android:visibility="gone" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView_groups_replays"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/textView_show_replay"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="5dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="5dp"
            android:background="@color/light_gray"
            android:nestedScrollingEnabled="false"
            android:visibility="gone" />

        <TextView
            android:id="@+id/textView_replay"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/recyclerView_groups_replays"
            android:layout_centerVertical="true"
            android:layout_marginLeft="45dp"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:fontFamily="@font/poppins_regular"
            android:text="Reply"
            android:textColor="@color/gradientColorStart"
            android:textSize="11sp" />

        <RelativeLayout
            android:id="@+id/layout_replay"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_below="@+id/textView_replay"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="5dp"
            android:layout_marginRight="5dp"
            android:layout_marginBottom="3dp"
            android:background="@drawable/shadow_curved_layout_bg"
            android:visibility="gone">

            <EditText
                android:id="@+id/editText_replay"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_toLeftOf="@+id/imageView_send_replay"
                android:background="@null"
                android:fontFamily="@font/poppins_medium"
                android:gravity="center|left"
                android:hint="Write your reply here.."
                android:paddingStart="15dp"
                android:paddingTop="3dp"
                android:textColor="@color/colorActionBarText"
                android:textColorHint="@color/light_gray"
                android:textSize="12sp"
                android:visibility="visible" />

            <ImageView
                android:id="@+id/imageView_send_replay"
                android:layout_width="40dp"
                android:layout_height="50dp"
                android:layout_alignParentRight="true"
                android:background="@drawable/button_right_corner_full_radius"
                android:padding="6dp"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_send" />
        </RelativeLayout>
    </RelativeLayout>
</RelativeLayout>

