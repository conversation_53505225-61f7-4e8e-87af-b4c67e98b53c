<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/customplayerholder"
    android:fitsSystemWindows="true"
    android:isScrollContainer="false"
    android:background="@color/white">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/player_holder">
        <com.google.android.exoplayer2.ui.PlayerView
            android:id="@+id/video_view"
            android:layout_width="match_parent"
            android:layout_height="250dp"
            app:fastforward_increment="10000"
            app:resize_mode="fit"
            android:isScrollContainer="false"
            app:rewind_increment="10000"
            app:show_buffering="always"
            app:hide_on_touch="true"
            app:controller_layout_id="@layout/exo_playback_control_view" />

        <com.wang.avi.AVLoadingIndicatorView
            android:id="@+id/videoLoader"
            style="@style/AVLoadingIndicatorView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            app:indicatorColor="@color/primary_bg_red"
            app:indicatorName="BallClipRotateIndicator"
            android:elevation="10dp"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/video_loading_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/video_view"
            android:layout_marginTop="3dp"
            android:layout_centerHorizontal="true"
            android:text="Loading Video... Please Wait"
            android:background="@color/black"
            android:textColor="@color/white"
            android:typeface="monospace"
            android:textSize="12sp"
            android:elevation="10dp"
            android:visibility="gone"/>
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/chatTitleLinear"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/player_holder"
        android:gravity="center"
        android:padding="10dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/exo_videotitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:typeface="normal"
            android:textSize="16sp"
            android:textColor="@color/white"
            android:paddingStart="15dp"
            android:paddingTop="1dp"
            android:paddingEnd="10dp"
            android:paddingBottom="1dp" />


    </LinearLayout>

    <TextView
        android:id="@+id/exo_videotitle_land"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:typeface="normal"
        android:textSize="16sp"
        android:textStyle="bold"
        android:layout_alignParentTop="true"
        android:layout_alignParentStart="true"
        android:textColor="@color/primary_bg_red"
        android:paddingStart="20dp"
        android:paddingTop="10dp"
        android:paddingEnd="10dp"
        android:paddingBottom="10dp" />

    <TextView
        android:id="@+id/overlaytext"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_alignParentStart="true"
        android:layout_margin="10dp"
        android:text="sample overlay text"
        android:visibility="gone"
        android:typeface="serif"
        android:textSize="14dp"
        android:textColor="@color/white"/>

    <LinearLayout
        android:id="@+id/resumevideolayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="15dp"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:weightSum="2"
        android:visibility="gone">
        <TextView
            android:id="@+id/resumevideo"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@color/grant_permission_background"
            android:text="RESUME"
            android:gravity="center"
            android:textColor="@color/white"
            android:typeface="serif"
            android:textSize="14sp"
            android:padding="3dp"
            android:layout_marginEnd="5dp"/>

        <TextView
            android:id="@+id/startovervideo"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@color/colorAccentOld"
            android:text="START OVER"
            android:gravity="center"
            android:textColor="@color/white"
            android:typeface="serif"
            android:textSize="14sp"
            android:padding="3dp"
            android:layout_marginStart="5dp"/>
    </LinearLayout>

    <include
        layout="@layout/no_internet_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"

        android:layout_alignParentBottom="true"
        android:layout_above="@id/relativeLayout"
        android:layout_marginBottom="5dp" />
</RelativeLayout>