<?xml version="1.0" encoding="utf-8"?>
<androidx.swiperefreshlayout.widget.SwipeRefreshLayout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/paperwise_feed_swipe_refresh"
    tools:context="com.wonderslate.prepjoy.news.PubFeedDetails"
    android:background="@color/primary_bg_dark">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/primary_bg_dark">

    <RelativeLayout
        android:id="@+id/rrltBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            app:srcCompat="@drawable/ic_back" />

        <TextView
            android:id="@+id/txtBack"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="5dp"
            android:layout_toRightOf="@+id/imgBack"
            android:gravity="center"
            android:text="Home"
            android:textColor="@color/white"
            android:textSize="17dp"
            android:textStyle="bold" />

    </RelativeLayout>

    <androidx.cardview.widget.CardView
        android:id="@+id/btnNewsPref"
        android:layout_width="@dimen/_32sdp"
        android:layout_height="50dp"
        android:layout_alignParentEnd="true"
        android:layout_marginRight="10dp"
        android:visibility="gone"
        android:backgroundTint="@color/primary_bg_dark">

        <ImageView
            android:id="@+id/imageView_change_news_preference"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="right|center"
            android:scaleType="fitXY"
            android:src="@drawable/is_settings_pref"
            android:visibility="visible"
            app:tint="@color/white" />
    </androidx.cardview.widget.CardView>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/feed_list"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/rrltBack"/>

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/scroll_to_top_paper_wise"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_arrow_up"
            android:layout_alignParentBottom="true"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="10dp"
            android:layout_marginBottom="30dp"
            android:clickable="true"
            android:contentDescription="scrollToTop"
            android:focusable="true"
            app:backgroundTint="@color/primary_bg_red"
            app:fabSize="mini"
            app:rippleColor="@color/white"
            app:useCompatPadding="true"/>

    </RelativeLayout>
</androidx.swiperefreshlayout.widget.SwipeRefreshLayout>