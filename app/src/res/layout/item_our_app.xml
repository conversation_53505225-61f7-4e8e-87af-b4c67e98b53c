<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingVertical="10dp"
    android:id="@+id/layout">

    <androidx.cardview.widget.CardView
        android:id="@+id/card_circle"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_centerHorizontal="true"
        android:elevation="12dp"
        app:cardCornerRadius="60dp"
        app:layout_constraintLeft_toLeftOf="@id/txt"
        app:layout_constraintRight_toRightOf="@id/txt"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgLogo"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_centerHorizontal="true"
            android:gravity="center"
            app:srcCompat="@drawable/prepjoy_full_icon" />
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/txt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:gravity="center"
        tools:text="Current\nAffairs"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:typeface="normal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/card_circle" />
</androidx.constraintlayout.widget.ConstraintLayout>