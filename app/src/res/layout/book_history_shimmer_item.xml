<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="15dp">

                <View
                    android:id="@+id/shimmerplaceHolderCardView"
                    android:layout_width="64dp"
                    android:layout_height="80dp"
                    android:background="#C4C4C4" />

                <View
                    android:id="@+id/shimmerbookTitleTxt"
                    android:layout_width="match_parent"
                    android:layout_height="17dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginLeft="16dp"
                    android:layout_marginRight="10dp"
                    android:layout_marginStart="16dp"
                    android:layout_toEndOf="@id/shimmerplaceHolderCardView"
                    android:layout_toRightOf="@id/shimmerplaceHolderCardView"
                    android:background="#C4C4C4" />

                <View
                    android:id="@+id/shimmerauthorbookTitleTxt"
                    android:layout_width="wrap_content"
                    android:layout_height="17dp"
                    android:layout_alignLeft="@id/shimmerbookTitleTxt"
                    android:layout_alignStart="@id/shimmerbookTitleTxt"
                    android:layout_below="@id/shimmerbookTitleTxt"
                    android:layout_marginEnd="10dp"
                    android:layout_marginRight="10dp"
                    android:layout_marginTop="8dp"
                    android:background="#C4C4C4" />


                <View
                    android:layout_width="92dp"
                    android:layout_height="17dp"
                    android:layout_alignBottom="@+id/shimmerplaceHolderCardView"
                    android:layout_alignLeft="@+id/shimmerauthorbookTitleTxt"
                    android:layout_alignStart="@+id/shimmerauthorbookTitleTxt"
                    android:background="#C4C4C4" />

                <View
                    android:id="@+id/shimmerdividerView"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@id/shimmerplaceHolderCardView"
                    android:layout_marginEnd="10dp"
                    android:layout_marginRight="5dp"
                    android:layout_marginTop="16dp"
                    android:background="#8CBDBDBD" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/shimmerdividerView"
                    android:layout_marginEnd="10dp"
                    android:layout_marginRight="10dp"
                    android:layout_marginTop="16dp"
                    android:orientation="horizontal">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1">

                        <View
                            android:id="@+id/shimmeridLabel"
                            android:layout_width="53dp"
                            android:layout_height="17dp"
                            android:background="#C4C4C4" />

                        <TextView
                            android:layout_width="132dp"
                            android:layout_height="17dp"
                            android:layout_below="@id/shimmeridLabel"
                            android:layout_marginTop="5dp"
                            android:background="#C4C4C4" />

                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1">

                        <View
                            android:id="@+id/shimmeridLabelTxt"
                            android:layout_width="53dp"
                            android:layout_height="17dp"
                            android:background="#C4C4C4" />

                        <TextView
                            android:layout_width="132dp"
                            android:layout_height="17dp"
                            android:layout_below="@id/shimmeridLabelTxt"
                            android:layout_marginTop="5dp"
                            android:background="#C4C4C4" />

                    </RelativeLayout>

                </LinearLayout>

            </RelativeLayout>

        </LinearLayout>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_below="@id/shimmerplaceHolderCardView"
        android:background="#8CBDBDBD" />

</LinearLayout>