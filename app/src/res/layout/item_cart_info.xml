<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="8dp"
        app:cardCornerRadius="8dp"
        android:elevation="0dp"
        app:cardElevation="0dp"
        android:layout_marginHorizontal="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                android:id="@+id/tvOrderInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="8dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="3dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="Order information"
                android:textColor="@color/black"
                android:textSize="18sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvDelivery"
                app:ws_font_weight="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingVertical="3dp">

                <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                    android:id="@+id/tvType"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="8dp"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:layout_weight="1.4"
                    android:text="Product type"
                    android:textColor="@color/color_order_info"
                    android:textSize="14sp"
                    app:ws_font_weight="normal"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                    android:id="@+id/tvTypeValue"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:text=":  Smart eBook"
                    android:textColor="@color/color_order_info"
                    android:textSize="14sp"
                    app:ws_font_weight="normal"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/tvType"
                    app:layout_constraintTop_toTopOf="@+id/tvType" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="3dp">

                <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                    android:id="@+id/tvReturn"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.4"
                    android:paddingHorizontal="8dp"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:text="Return &amp; Refund"
                    android:textColor="@color/color_order_info"
                    android:textSize="14sp"
                    app:ws_font_weight="normal"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvType" />

                <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                    android:id="@+id/tvReturnValue"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:text=":  No"
                    android:textColor="@color/color_order_info"
                    android:textSize="14sp"
                    app:ws_font_weight="normal"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/tvReturn"
                    app:layout_constraintTop_toTopOf="@+id/tvReturn" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="3dp">

                <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                    android:id="@+id/tvPlatform"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.4"
                    android:paddingHorizontal="8dp"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:text="Platform Supported"
                    android:textColor="@color/color_order_info"
                    android:textSize="14sp"
                    app:ws_font_weight="normal"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvReturn" />

                <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                    android:id="@+id/tvPlatformValue"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:text=":  Web, Android &amp; iOS"
                    android:textColor="@color/color_order_info"
                    android:textSize="14sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/tvReturn"
                    app:layout_constraintTop_toTopOf="@+id/tvPlatform"
                    app:ws_font_weight="normal" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="3dp">

                <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                    android:id="@+id/tvPrint"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.4"
                    android:paddingHorizontal="8dp"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:text="Print/PDF download"
                    android:textColor="@color/color_order_info"
                    android:textSize="14sp"
                    app:ws_font_weight="normal"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvReturn" />

                <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                    android:id="@+id/tvPrintValue"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:text=":  No"
                    android:textColor="@color/color_order_info"
                    android:textSize="14sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/tvReturn"
                    app:layout_constraintTop_toTopOf="@+id/tvPlatform"
                    app:ws_font_weight="normal" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="3dp"
                android:visibility="gone">

                <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                    android:id="@+id/tvDelivery"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.4"
                    android:paddingHorizontal="8dp"
                    android:ellipsize="end"
                    android:text="Delivery"
                    android:textColor="@color/color_order_info"
                    android:textSize="14sp"
                    app:ws_font_weight="normal"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvPlatform" />

                <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                    android:id="@+id/tvDeliveryValue"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:text=":  Immediate access"
                    android:textColor="@color/color_order_info"
                    android:textSize="14sp"
                    app:ws_font_weight="normal"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/tvReturn"
                    app:layout_constraintTop_toTopOf="@+id/tvDelivery" />

            </LinearLayout>

            <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                android:id="@+id/tvSaveTree"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="8dp"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:ellipsize="end"
                android:gravity="start"
                android:maxLines="2"
                android:text="Use books save trees 🌳"
                android:textColor="@color/black"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvDelivery"
                app:ws_font_weight="bold" />


        </LinearLayout>

    </androidx.cardview.widget.CardView>

</FrameLayout>