<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/white"
    android:orientation="vertical"
    tools:ignore="UseCompoundDrawables">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/rl_footer"
        android:layout_alignParentTop="true"
        android:background="@color/white"
        android:orientation="vertical"
        tools:ignore="UseCompoundDrawables">

        <ImageView
            android:id="@+id/imageReadingMaterial"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/current_affairs_image" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">


                <TextView
                    android:id="@+id/textTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:padding="8dp"
                    android:text="@string/title"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/textDescription"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="8dp"
                    android:text="@string/description" />

            </LinearLayout>
        </ScrollView>
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/rl_footer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:padding="8dp">

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:contentDescription="@string/save"
            app:srcCompat="@drawable/ic_not_saved" />

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignParentEnd="true"
            android:contentDescription="@string/share_on_whatsapp"
            app:srcCompat="@drawable/ic_whatsapp" />

    </RelativeLayout>
</RelativeLayout>