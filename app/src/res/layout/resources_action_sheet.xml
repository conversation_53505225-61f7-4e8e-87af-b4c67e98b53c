<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:id="@+id/lnlUserButtons"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="20dp"
    android:paddingBottom="20dp"
    android:visibility="visible"
    tools:visibility="visible"
    android:orientation="vertical"
    android:background="@color/colorBottomSheetPrimaryDark"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <View
        android:layout_width="@dimen/_30sdp"
        android:layout_height="5dp"
        android:background="@drawable/track_selector"
        android:layout_gravity="center"
        android:layout_marginBottom="20dp"/>

    <TextView
        android:id="@+id/tvResourceName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="@dimen/_18ssp"
        android:textColor="@color/white"
        android:layout_marginHorizontal="@dimen/_16sdp"
        android:layout_marginTop="@dimen/_16sdp"
        android:text="Very very very very very loooooooooong Resource name" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16sdp"
        android:layout_marginTop="@dimen/_4sdp"
        android:visibility="visible">

        <ImageView
            android:layout_width="@dimen/_14sdp"
            android:layout_height="@dimen/_14sdp"
            android:src="@drawable/ic_quiz_24"
            android:layout_marginEnd="@dimen/_4sdp"
            app:tint="@color/colorAccent" />

        <TextView
            android:id="@+id/tvResType"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/gray"
            android:textStyle="italic"
            android:text="MCQ's" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16sdp"
        android:layout_marginHorizontal="@dimen/_12sdp"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/lltQuizPlay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_resource_icon"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="@dimen/_32sdp"
                android:layout_height="@dimen/_32sdp"
                android:layout_margin="@dimen/_8sdp"
                android:src="@drawable/ic_play_arrow_24"
                android:padding="@dimen/_4sdp"
                app:tint="@color/colorActionSheetActionIconColor" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Play"
                android:padding="10dp"
                android:textSize="16sp"
                android:textColor="@color/white"/>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/lltQuizPractice"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16sdp"
            android:background="@drawable/bg_resource_icon"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="@dimen/_32sdp"
                android:layout_height="@dimen/_32sdp"
                android:layout_margin="@dimen/_8sdp"
                android:src="@drawable/ic_assessment_24"
                android:padding="@dimen/_4sdp"
                app:tint="@color/colorActionSheetActionIconColor" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Practice"
                android:padding="10dp"
                android:textSize="16sp"
                android:textColor="@color/white"/>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/lltQuizTest"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16sdp"
            android:background="@drawable/bg_resource_icon"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="@dimen/_32sdp"
                android:layout_height="@dimen/_32sdp"
                android:layout_margin="@dimen/_8sdp"
                android:src="@drawable/ic_eye_24"
                android:padding="@dimen/_4sdp"
                app:tint="@color/colorActionSheetActionIconColor" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Test"
                android:padding="10dp"
                android:textSize="16sp"
                android:textColor="@color/white"/>

        </LinearLayout>

    </LinearLayout>


</LinearLayout>