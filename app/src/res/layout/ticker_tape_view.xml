<?xml version="1.0" encoding="utf-8"?>
<com.wonderslate.prepjoy.Utils.TickerParentView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/ticker_tape_parent"
    android:background="@color/primary_bg_dark"
    android:focusable="true">

    <TextView
        android:id="@+id/ticker_header"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_bold"
        android:textColor="@color/white"
        android:textSize="10.75sp"
        android:text="Previous Week Winners"
        android:layout_marginStart="@dimen/_10sdp"
        android:layout_marginEnd="@dimen/_10sdp"
        android:paddingTop="3dp" />

    <ImageView
        android:id="@+id/ticker_arrow_view"
        android:layout_width="10dp"
        android:layout_height="10dp"
        android:layout_toEndOf="@id/ticker_header"
        android:src="@drawable/ic_arrow"
        android:layout_alignTop="@id/ticker_header"
        android:layout_alignBottom="@id/ticker_header"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/ticker_tape_recycler"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/ticker_header"
        android:orientation="horizontal"
        android:elevation="5dp"
        android:overScrollMode="always"
        android:layout_centerVertical="true" />

</com.wonderslate.prepjoy.Utils.TickerParentView>