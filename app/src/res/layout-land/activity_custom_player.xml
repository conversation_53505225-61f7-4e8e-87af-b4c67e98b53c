<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/customplayerholder"
    android:background="@color/black">

    <com.google.android.exoplayer2.ui.PlayerView
        android:id="@+id/video_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:fastforward_increment="10000"
        app:resize_mode="fill"
        app:rewind_increment="10000"
        app:show_buffering="always"
        app:hide_on_touch="true"
        app:controller_layout_id="@layout/exo_playback_control_view"/>

    <TextView
        android:id="@+id/overlaytext"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_alignParentStart="true"
        android:layout_margin="10dp"
        android:text="sample overlay text"
        android:typeface="serif"
        android:textSize="14dp"
        android:textColor="@color/black"
        android:visibility="gone"/>

    <LinearLayout
        android:id="@+id/resumevideolayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="15dp"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:weightSum="6"
        android:layout_centerHorizontal="true"
        android:visibility="gone">
        <TextView
            android:id="@+id/resumevideo"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            android:background="@color/grant_permission_background"
            android:text="RESUME"
            android:gravity="center"
            android:textColor="@color/white"
            android:typeface="serif"
            android:textSize="14sp"
            android:padding="3dp"
            android:layout_marginEnd="5dp"/>

        <TextView
            android:id="@+id/startovervideo"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            android:background="@color/colorAccentOld"
            android:text="START OVER"
            android:gravity="center"
            android:textColor="@color/white"
            android:typeface="serif"
            android:textSize="14sp"
            android:padding="3dp"
            android:layout_marginStart="5dp"/>
    </LinearLayout>
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/chatMessageRecyclerView"
        android:layout_width="0dp"
        android:layout_height="0dp"/>
    <com.google.android.material.textfield.TextInputEditText
        android:id="@+id/commentInputText"
        android:layout_width="0dp"
        android:layout_height="0dp" />

    <ImageView
        android:id="@+id/sendCommentImageView"
        android:layout_width="0dp"
        android:layout_height="0dp" />
</RelativeLayout>