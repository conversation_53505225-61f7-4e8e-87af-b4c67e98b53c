<?xml version="1.0" encoding="utf-8"?>
<resources>
    <bool name="isTablet">false</bool>

    <declare-styleable name="ReadActionBarStyle">
        <attr name="ic_record_voice_over" format="reference" />
        <attr name="ic_format_size_white" format="reference" />
        <attr name="ic_notes" format="reference" />
    </declare-styleable>

    <declare-styleable name="LoginActionBarStyle">
        <attr name="ic_web_resource_refresh" format="reference" />
    </declare-styleable>

    <declare-styleable name="TextViewFont">
        <attr name="ws_font_weight" format="enum">
            <enum name="android_default" value="0" />
            <enum name="normal" value="1" />
            <enum name="italic" value="2" />
            <enum name="bold" value="3" />
        </attr>

        <attr name="enableGradient" format="boolean" />
    </declare-styleable>

    <declare-styleable name="ErrorMessage">
        <attr name="err_text" format="string" />
        <attr name="err_enableIcon" format="boolean" />
        <attr name="err_icon" format="reference" />
        <attr name="err_background" format="reference" />
    </declare-styleable>-
</resources>