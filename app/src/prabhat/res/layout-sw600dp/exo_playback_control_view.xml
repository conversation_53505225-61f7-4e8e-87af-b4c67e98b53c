<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:keepScreenOn="true"
    android:layout_gravity="bottom"
    >


    <ImageButton
        android:id="@+id/exo_resize"
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:src="@drawable/ic_full_sceen_wa"
        android:layout_gravity="top|end"
        android:scaleType="fitCenter"
        android:padding="6dp"
        android:background="@drawable/button_background_black_rounded"
        android:layout_margin="10dp"
        app:tint="@color/primary_bg_red" />

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/black"
        android:layout_gravity="bottom"
        android:visibility="visible">

        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:id="@+id/seekbar_time"
            android:gravity="center"
            android:background="#96000000"
            android:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="?android:attr/textAppearanceSmall"
                android:text="00:00:00"
                android:id="@+id/exo_position"
                android:textColor="#FFF"
                android:paddingLeft="20dp"
                android:paddingTop="10dp"
                android:paddingRight="10dp"
                android:paddingBottom="10dp" />

            <com.google.android.exoplayer2.ui.DefaultTimeBar
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:id="@+id/exo_progress"
                android:layout_weight="1"
                app:played_color="@color/primary_bg_red"
                app:buffered_color="@color/gray"
                app:unplayed_color="#FFFFFF" />
            <!--android:indeterminate="false"
            style="@android:style/Widget.DeviceDefault.Light.SeekBar"
            android:thumbTint="#ffffff"
            android:progress="0"
            android:secondaryProgress="0"
            android:splitTrack="false"
            android:progressTint="#2473ac"
            android:secondaryProgressTint="#9A8486"
            android:foregroundTint="#7F5C62"
            android:foreground="#7F5C62" />-->

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="?android:attr/textAppearanceSmall"
                android:text="00:00:00"
                android:id="@+id/exo_duration"
                android:textColor="#FFF"
                android:paddingLeft="10dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:paddingRight="20dp" />

        </LinearLayout>

        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:id="@+id/controls"
            android:paddingBottom="10dp"
            android:layout_gravity="center"
            android:background="#96000000"
            android:visibility="visible">

            <ImageButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/btn_lock"
                android:layout_gravity="right|center_vertical"
                android:background="@android:color/transparent"
                android:layout_weight="1"
                android:visibility="gone"
                android:src="@drawable/ic_lock_white_24dp"
                android:layout_marginLeft="20dp" />

            <!--<ImageButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/exo_prev"
                android:layout_gravity="center"
                android:background="@android:color/transparent"
                android:layout_weight="1"
                android:src="@drawable/ic_skip_previous_white_24dp"
                android:cropToPadding="false"
                android:visibility="gone"
                />-->

            <TextView
                android:id="@+id/exo_live_video"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="LIVE"
                android:typeface="serif"
                android:textSize="14sp"
                android:textColor="@color/white"
                android:drawableStart="@drawable/ic_live_video"
                android:drawablePadding="5dp"
                android:background="@android:color/transparent"
                android:layout_gravity="center"
                android:gravity="center"
                android:paddingStart="10dp"
                android:visibility="gone"/>

            <ImageButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:layout_gravity="center"
                android:id="@+id/exo_rew"
                android:layout_weight="1"
                app:srcCompat="@drawable/ic_fast_rewind_white_24" />

            <ImageButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:src="@drawable/ic_play_circle_filled_white_white_24dp"
                android:layout_gravity="center"
                android:id="@+id/exo_play"
                android:layout_weight="1"
                android:visibility="gone"
                />

            <ImageButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:src="@drawable/ic_pause_circle_filled_white_24dp"
                android:layout_gravity="center"
                android:id="@+id/exo_pause"
                android:layout_weight="1"
                />

            <ImageButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="+30s"
                android:background="@android:color/transparent"
                android:layout_gravity="center"
                android:id="@+id/exo_ffwd"
                android:layout_weight="1"
                app:srcCompat="@drawable/ic_fast_forward_white_24dp"
                />

            <!--<ImageButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/exo_next"
                android:layout_gravity="center"
                android:background="@android:color/transparent"
                android:layout_weight="1"
                android:src="@drawable/ic_skip_next_white_24dp"
                android:visibility="gone"/>-->

            <ImageButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Settings"
                android:background="@android:color/transparent"
                android:layout_gravity="right|center_vertical"
                android:id="@+id/exo_videoquality"
                android:layout_weight="1"
                app:srcCompat="@drawable/ic_switch_video_white_24dp"
                android:foregroundGravity="right" />

            <ImageButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Settings"
                android:background="@android:color/transparent"
                android:layout_gravity="right|center_vertical"
                android:id="@+id/exo_playspeed"
                android:layout_weight="1"
                android:src="@drawable/ic_slow_motion_video_white"
                android:foregroundGravity="right"
                style="@style/ExoMediaButton"/>

            <ImageButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Settings"
                android:background="@android:color/transparent"
                android:layout_gravity="right|center_vertical"
                android:id="@+id/exo_fullscreen"
                android:layout_weight="1"
                app:srcCompat="@drawable/ic_fullscreen_white"
                android:foregroundGravity="right"
                style="@style/ExoMediaButton"/>


        </LinearLayout>

    </LinearLayout>

</FrameLayout>