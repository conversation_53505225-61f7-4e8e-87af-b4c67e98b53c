<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/primary_bg_dark">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/linearLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_50sdp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:gravity="center_vertical"
        android:elevation="@dimen/_8sdp">

        <androidx.cardview.widget.CardView
            android:id="@+id/btnBack"
            android:layout_width="@dimen/_32sdp"
            android:layout_height="@dimen/_32sdp"
            android:backgroundTint="@color/colorPrimary"
            app:cardCornerRadius="@dimen/_8sdp"
            android:foreground="?selectableItemBackground"
            android:layout_marginStart="@dimen/_10sdp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:src="@drawable/ic_new_back_arrow"
                android:contentDescription="Go back"/>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/btnShare"
            android:layout_width="@dimen/_32sdp"
            android:layout_height="@dimen/_32sdp"
            android:backgroundTint="@color/colorPrimary"
            android:layout_marginEnd="@dimen/_10sdp"
            android:foreground="?selectableItemBackground"
            app:cardCornerRadius="@dimen/_8sdp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:src="@drawable/ic_fab_share"
                android:contentDescription="Share book" />
        </androidx.cardview.widget.CardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/buy_preview_btn_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_gravity="bottom"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
            android:id="@+id/previewBtn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginEnd="5dp"
            android:layout_marginBottom="7dp"
            android:layout_marginStart="5dp"
            android:layout_weight="1"
            android:background="@drawable/preview_btn_back"
            android:elevation="8dp"
            android:gravity="center"
            android:textAllCaps="false"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:paddingVertical="15dp"/>

        <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
            android:id="@+id/buyBtn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="5dp"
            android:layout_marginBottom="5dp"
            android:layout_weight="1"
            android:background="@drawable/buy_btn_back"
            android:elevation="8dp"
            android:gravity="center"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:paddingVertical="15dp"/>

    </LinearLayout>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/book_details_parent"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:layout_marginBottom="8dp"
        app:layout_constraintBottom_toTopOf="@id/buy_preview_btn_layout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:animateLayoutChanges="true"
            android:layout_marginTop="@dimen/_50sdp">

            <androidx.cardview.widget.CardView
                android:id="@+id/placeHolderCardView"
                android:layout_width="250dp"
                android:layout_height="320dp"
                app:cardCornerRadius="6dp"
                app:cardElevation="5dp"
                android:layout_gravity="center_horizontal"
                app:cardPreventCornerOverlap="true"
                app:cardUseCompatPadding="true"
                android:animateLayoutChanges="true">

                <ImageView
                    android:id="@+id/ivBookCover"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:adjustViewBounds="true"
                    android:scaleType="fitXY"
                    android:src="@drawable/book_cover_placeholder" />

            </androidx.cardview.widget.CardView>

            <TextView
                android:id="@+id/tvBookTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_32sdp"
                android:layout_marginTop="12dp"
                android:ellipsize="end"
                android:fontFamily="@font/poppins_medium"
                android:maxLines="3"
                android:textAlignment="center"
                android:textColor="@color/white"
                android:textSize="22sp"
                android:textStyle="bold"
                app:autoSizeMaxTextSize="@dimen/_20ssp"
                app:autoSizeMinTextSize="@dimen/_12ssp"
                app:autoSizeTextType="uniform"
                tools:text="Chemistry Part II - NCERT" />

            <TextView
                android:id="@+id/textView_publisher_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:fontFamily="@font/poppins_regular"
                android:textAllCaps="true"
                android:textColor="@color/white"
                android:alpha="0.7"
                android:textSize="12sp"
                android:layout_gravity="center_horizontal"
                tools:text="Publisher name" />

            <TextView
                android:id="@+id/expiry_txt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="Valid till"
                android:layout_marginTop="5dp"
                android:fontFamily="@font/poppins_regular"
                android:textAllCaps="true"
                android:textColor="@color/white"
                android:alpha="0.7"
                android:textSize="12sp"
                android:layout_gravity="center_horizontal"/>

            <TextView
                android:id="@+id/price_option_help_txt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Select an option"
                android:textColor="@color/white"
                android:fontFamily="@font/poppins_medium"
                android:textStyle="bold"
                android:textSize="12sp"
                android:padding="3dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="7dp"/>

            <LinearLayout
                android:id="@+id/llPrice"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:baselineAligned="false"
                android:paddingHorizontal="8dp">

                <LinearLayout
                    android:id="@+id/eBook_parent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/eBook_price_card"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:cardCornerRadius="6dp"
                        app:cardElevation="4dp"
                        app:cardUseCompatPadding="true">

                        <RelativeLayout
                            android:id="@+id/eBook_price_holder"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingHorizontal="7dp"
                            android:paddingVertical="10dp">
                            <TextView
                                android:id="@+id/eBook_type_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="start"
                                android:fontFamily="@font/poppins_medium"
                                android:textAllCaps="false"
                                android:textColor="@color/black"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:text="eBook"/>

                            <LinearLayout
                                android:id="@+id/price_layout"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_below="@id/eBook_type_text"
                                android:layout_gravity="center|start"
                                android:layout_marginTop="8dp"
                                android:gravity="center|start"
                                android:orientation="horizontal"
                                tools:visibility="visible">

                                <TextView
                                    android:id="@+id/tvOfferPrice"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginEnd="8dp"
                                    android:fontFamily="@font/poppins_medium"
                                    android:textColor="@color/colorAccent"
                                    android:textSize="15sp"
                                    android:textStyle="normal"
                                    android:visibility="visible"
                                    android:gravity="center"
                                    tools:text="400"/>

                                <TextView
                                    android:id="@+id/tvListPrice"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:fontFamily="@font/poppins_medium"
                                    android:textColor="@color/black"
                                    android:textSize="13sp"
                                    tools:text="100"/>

                                <TextView
                                    android:id="@+id/textView_discount"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:fontFamily="@font/poppins_bold"
                                    android:textColor="#20aa66"
                                    android:textSize="12sp"
                                    android:textStyle="normal"
                                    android:gravity="start"
                                    android:layout_marginStart="10dp"
                                    tools:text="Save 30%"/>
                            </LinearLayout>
                        </RelativeLayout>

                    </androidx.cardview.widget.CardView>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/test_series_parent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/test_series_price_card"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:cardCornerRadius="6dp"
                        app:cardElevation="4dp"
                        app:cardUseCompatPadding="true">

                        <RelativeLayout
                            android:id="@+id/test_series_price_holder"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingHorizontal="7dp"
                            android:paddingVertical="10dp">

                            <TextView
                                android:id="@+id/test_series_type_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="start"
                                android:fontFamily="@font/poppins_medium"
                                android:textAllCaps="false"
                                android:textColor="@color/black"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:gravity="start"
                                android:text="Online Test Series"/>

                            <LinearLayout
                                android:id="@+id/price_layout_test_series"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_below="@id/test_series_type_text"
                                android:layout_gravity="center|start"
                                android:layout_marginTop="8dp"
                                android:gravity="center|start"
                                android:orientation="horizontal"
                                tools:visibility="visible">

                                <TextView
                                    android:id="@+id/tv_test_series_offer_price"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginEnd="8dp"
                                    android:fontFamily="@font/poppins_medium"
                                    android:textColor="@color/colorAccent"
                                    android:textSize="15sp"
                                    android:textStyle="normal"
                                    android:visibility="visible"
                                    android:gravity="center"
                                    tools:text="400"/>

                                <TextView
                                    android:id="@+id/tv_test_series_list_price"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:fontFamily="@font/poppins_medium"
                                    android:textColor="@color/black"
                                    android:textSize="13sp"
                                    tools:text="100"/>

                                <TextView
                                    android:id="@+id/textView_discount_test_series"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:fontFamily="@font/poppins_bold"
                                    android:textColor="#20aa66"
                                    android:textSize="12sp"
                                    android:textStyle="normal"
                                    android:gravity="start"
                                    android:layout_marginStart="10dp"
                                    tools:text="Save 30%"
                                    tools:visibility="visible"/>
                            </LinearLayout>
                        </RelativeLayout>

                    </androidx.cardview.widget.CardView>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/paperback_parent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/paperback_price_card"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:cardCornerRadius="6dp"
                        app:cardElevation="4dp"
                        app:cardUseCompatPadding="true">

                        <RelativeLayout
                            android:id="@+id/paperback_price_holder"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingHorizontal="7dp"
                            android:paddingVertical="10dp">

                            <TextView
                                android:id="@+id/paperback_type_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="start"
                                android:fontFamily="@fonts/poppins_medium"
                                android:textAllCaps="false"
                                android:textColor="@color/black"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:gravity="start"
                                android:text="Paperback"/>

                            <LinearLayout
                                android:id="@+id/price_layout_paperback"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_below="@id/paperback_type_text"
                                android:layout_gravity="center|start"
                                android:layout_marginTop="8dp"
                                android:gravity="center|start"
                                android:orientation="horizontal"
                                tools:visibility="visible">

                                <TextView
                                    android:id="@+id/tv_paperback_offer_price"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginEnd="8dp"
                                    android:fontFamily="@fonts/poppins_medium"
                                    android:textColor="@color/colorAccent"
                                    android:textSize="17sp"
                                    android:textStyle="normal"
                                    android:visibility="visible"
                                    android:gravity="center"
                                    tools:text="400"/>

                                <TextView
                                    android:id="@+id/tv_paperback_list_price"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:fontFamily="@fonts/poppins_medium"
                                    android:textColor="@color/black"
                                    android:textSize="15sp"
                                    tools:text="100"/>

                                <TextView
                                    android:id="@+id/textView_discount_paperback"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:fontFamily="@font/poppins_bold"
                                    android:textColor="#20aa66"
                                    android:textSize="12sp"
                                    android:textStyle="normal"
                                    android:gravity="start"
                                    android:layout_marginStart="10dp"
                                    tools:text="Save 30%"
                                    tools:visibility="visible"/>
                            </LinearLayout>
                            <TextView
                                android:id="@+id/paperback_textView_outofstock"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="10dp"
                                android:layout_centerVertical="true"
                                android:layout_alignParentEnd="true"
                                android:fontFamily="@fonts/poppins_bold"
                                android:text="Out of stock"
                                android:textColor="#F05A2A"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:gravity="start"
                                android:visibility="gone"
                                tools:visibility="visible"/>
                        </RelativeLayout>

                    </androidx.cardview.widget.CardView>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/combo_parent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/combo_price_card"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:cardCornerRadius="6dp"
                        app:cardElevation="4dp"
                        app:cardUseCompatPadding="true">

                        <RelativeLayout
                            android:id="@+id/combo_price_holder"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingHorizontal="7dp"
                            android:paddingVertical="10dp">
                            <TextView
                                android:id="@+id/combo_type_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="start"
                                android:fontFamily="@fonts/poppins_medium"
                                android:textAllCaps="false"
                                android:textColor="@color/black"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:text="Paperback + eBook"/>

                            <LinearLayout
                                android:id="@+id/combo_price_layout"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_below="@id/combo_type_text"
                                android:layout_gravity="center|start"
                                android:layout_marginTop="8dp"
                                android:gravity="center|start"
                                android:orientation="horizontal"
                                tools:visibility="visible">

                                <TextView
                                    android:id="@+id/tv_combo_offer_price"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginEnd="8dp"
                                    android:fontFamily="@fonts/poppins_medium"
                                    android:textColor="@color/colorAccent"
                                    android:textSize="17sp"
                                    android:textStyle="normal"
                                    android:visibility="visible"
                                    android:gravity="center"
                                    tools:text="400"/>

                                <TextView
                                    android:id="@+id/tv_combo_list_price"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:fontFamily="@fonts/poppins_medium"
                                    android:textColor="@color/black"
                                    android:textSize="15sp"
                                    tools:text="100"/>

                                <TextView
                                    android:id="@+id/textView_discount_combo"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:fontFamily="@font/poppins_bold"
                                    android:textColor="#20aa66"
                                    android:textSize="12sp"
                                    android:textStyle="normal"
                                    android:gravity="start"
                                    android:layout_marginStart="10dp"
                                    tools:text="Save 30%"
                                    tools:visibility="visible"/>
                            </LinearLayout>

                            <TextView
                                android:id="@+id/combo_textView_outofstock"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="10dp"
                                android:layout_centerVertical="true"
                                android:layout_alignParentEnd="true"
                                android:fontFamily="@fonts/poppins_bold"
                                android:text="Out of stock"
                                android:textColor="#F05A2A"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:visibility="gone"
                                android:gravity="start"
                                tools:visibility="visible"/>
                        </RelativeLayout>

                    </androidx.cardview.widget.CardView>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/affiliation_price_parent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/affiliation_price_card"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:cardCornerRadius="6dp"
                        app:cardElevation="4dp"
                        app:cardUseCompatPadding="true">

                        <RelativeLayout
                            android:id="@+id/affiliation_price_holder"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingHorizontal="7dp"
                            android:paddingVertical="10dp">
                            <TextView
                                android:id="@+id/affiliation_type_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="start"
                                android:fontFamily="@font/poppins_medium"
                                android:textAllCaps="false"
                                android:textColor="@color/black"
                                android:textStyle="bold"
                                android:textSize="12sp"
                                android:text="Paperback &amp; Hardcover prices"/>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_below="@id/affiliation_type_text"
                                android:weightSum="2"
                                android:orientation="horizontal">

                                <RelativeLayout
                                    android:id="@+id/amazon_affiliation_price_layout"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:layout_gravity="center|start"
                                    android:layout_marginTop="8dp"
                                    android:layout_marginEnd="10dp"
                                    android:layout_marginBottom="5dp"
                                    android:gravity="center|start"
                                    android:orientation="horizontal"
                                    tools:visibility="visible"
                                    android:visibility="gone"
                                    android:background="@drawable/test_series_price_bg"
                                    android:padding="10dp">

                                    <ImageView
                                        android:id="@+id/amazon_img"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginBottom="12dp"
                                        app:srcCompat="@drawable/amazonlogo"
                                        android:scaleType="fitXY"
                                        android:layout_centerHorizontal="true"
                                        android:layout_alignParentTop="true"
                                        android:layout_marginTop="5dp"/>

                                    <TextView
                                        android:id="@+id/textView_amazon_price"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_below="@id/amazon_img"
                                        android:layout_centerHorizontal="true"
                                        android:layout_marginBottom="10dp"
                                        android:fontFamily="@font/poppins_medium"
                                        android:textColor="@color/black"
                                        android:textSize="17sp"
                                        android:textStyle="normal"
                                        android:visibility="visible"
                                        android:gravity="center"
                                        tools:text="400"/>
                                </RelativeLayout>

                                <RelativeLayout
                                    android:id="@+id/flipkart_affiliation_price_layout"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:layout_gravity="center|start"
                                    android:layout_marginTop="8dp"
                                    android:layout_marginBottom="5dp"
                                    android:gravity="center|start"
                                    android:orientation="horizontal"
                                    tools:visibility="visible"
                                    android:visibility="gone"
                                    android:background="@drawable/test_series_price_bg"
                                    android:padding="10dp">

                                    <ImageView
                                        android:id="@+id/flipkart_img"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginBottom="12dp"
                                        app:srcCompat="@drawable/flipkartlogo"
                                        android:scaleType="fitXY"
                                        android:layout_centerHorizontal="true"
                                        android:layout_alignParentTop="true"
                                        android:layout_marginTop="5dp"/>

                                    <TextView
                                        android:id="@+id/textView_flipkart_price"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_below="@id/flipkart_img"
                                        android:layout_centerHorizontal="true"
                                        android:layout_marginBottom="10dp"
                                        android:fontFamily="@font/poppins_medium"
                                        android:textColor="@color/black"
                                        android:textSize="17sp"
                                        android:textStyle="normal"
                                        android:visibility="visible"
                                        android:gravity="center"
                                        tools:text="400"/>
                                </RelativeLayout>

                            </LinearLayout>

                        </RelativeLayout>

                    </androidx.cardview.widget.CardView>

                </LinearLayout>

            </LinearLayout>

            <com.facebook.shimmer.ShimmerFrameLayout
                android:id="@+id/shimmer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:layout_marginHorizontal="16dp"
                android:visibility="gone">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <View
                        android:id="@+id/view"
                        android:layout_width="0dp"
                        android:layout_height="50dp"
                        android:background="@drawable/bg_extra_rounded"
                        android:backgroundTint="@color/light_gray"
                        app:layout_constraintEnd_toStartOf="@+id/view2"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <View
                        android:id="@+id/view2"
                        android:layout_width="0dp"
                        android:layout_height="50dp"
                        android:background="@drawable/bg_extra_rounded"
                        android:backgroundTint="@color/light_gray"
                        android:layout_marginHorizontal="16dp"
                        app:layout_constraintEnd_toStartOf="@+id/view3"
                        app:layout_constraintStart_toEndOf="@+id/view"
                        app:layout_constraintTop_toTopOf="parent" />

                    <View
                        android:id="@+id/view3"
                        android:layout_width="0dp"
                        android:layout_height="50dp"
                        android:background="@drawable/bg_extra_rounded"
                        android:backgroundTint="@color/light_gray"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/view2"
                        app:layout_constraintTop_toTopOf="parent" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="150dp"
                        android:layout_marginTop="16dp"
                        android:backgroundTint="@color/light_gray"
                        android:background="@drawable/bg_extra_rounded"
                        app:layout_constraintTop_toBottomOf="@+id/view2" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.facebook.shimmer.ShimmerFrameLayout>

            <LinearLayout
                android:id="@+id/noDataLayout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:layout_gravity="center"
                android:layout_margin="@dimen/_16sdp"
                android:padding="16dp"
                android:background="@drawable/all_course_list_item_background"
                android:elevation="6dp"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/emptyImage"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:gravity="center"
                    android:src="@drawable/ic_book" />

                <com.ws.core_ui.custom_views.WSTextView
                    android:id="@+id/emptytextview"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:padding="3dp"
                    android:text="Books details not available now,\nPlease check after some time."
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:typeface="normal" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recBookContains"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:nestedScrollingEnabled="false"
                android:layout_marginHorizontal="@dimen/_8sdp"/>

            <TextView
                android:id="@+id/tvLanguages"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8sdp"
                android:layout_marginHorizontal="@dimen/_16sdp"
                android:background="@drawable/bg_extra_rounded"
                android:padding="@dimen/_8sdp"
                android:textAlignment="center"
                android:textColor="@color/white"
                android:visibility="gone"
                tools:visibility="visible"/>

            <TextView
                android:id="@+id/tvBookDesc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16sdp"
                android:layout_marginHorizontal="@dimen/_16sdp"
                android:textColor="@color/white"
                android:layout_gravity="center|start"
                android:fontFamily="@font/poppins_regular"
                android:alpha="0.8"
                android:justificationMode="inter_word"
                android:layout_marginBottom="3dp"/>

            <TextView
                android:id="@+id/readMore"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"
                android:text="Read More"
                android:textColor="@color/colorAccent"
                android:layout_gravity="end"
                android:fontFamily="@font/font_regular" />

            <TextView
                android:id="@+id/readLess"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:text="Read Less"
                tools:visibility="visible"
                android:textColor="@color/colorAccent"
                android:layout_gravity="end"
                android:fontFamily="@font/font_regular"/>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <LinearLayout
        android:id="@+id/upgrade_loader"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:background="@color/white"
        android:visibility="gone"
        tools:visibility="gone">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/upgrade_loader_animation_view"
            android:layout_width="200dp"
            android:layout_height="200dp"
            android:layout_gravity="center"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/anim_payment_processing" />

        <TextView
            android:id="@+id/tvMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="@color/black"
            android:text="Getting book details..."
            android:gravity="center"
            android:textStyle="bold"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginTop="10dp"/>

    </LinearLayout>

    <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
        android:id="@+id/openInLibraryBtn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:layout_marginEnd="15dp"
        android:layout_marginBottom="5dp"
        android:background="@drawable/preview_btn_back"
        android:elevation="8dp"
        android:gravity="center"
        android:textAllCaps="false"
        android:textColor="@color/black"
        android:textSize="14sp"
        android:text="Open"
        android:paddingVertical="15dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:visibility="gone"/>


</androidx.constraintlayout.widget.ConstraintLayout>