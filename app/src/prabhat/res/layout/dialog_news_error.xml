<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/errorDialogTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="left"
        android:textStyle="bold"
        android:paddingLeft="20dp"
        android:paddingRight="30dp"
        android:textColor="@color/primary_bg_red"
        android:textSize="17sp"
        android:layout_marginTop="10dp"/>

    <TextView
        android:id="@+id/errorDialogMessage"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="left"
        android:layout_marginTop="20dp"
        android:textStyle="bold"
        android:paddingLeft="30dp"
        android:paddingRight="30dp"
        android:textColor="@color/purple_200"
        android:textSize="15sp" />


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="20dp"
        android:paddingLeft="20dp"
        android:paddingRight="20dp"
        android:paddingBottom="10dp"
        android:gravity="center"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btnNegative"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="left"
            android:text="Cancel"
            android:layout_weight="1"
            android:layout_marginRight="5dp"
            android:background="@drawable/round_edge_red"
            android:textColor="@color/white" />

        <Button
            android:id="@+id/btnPositive"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:background="@drawable/round_edge_purple"
            android:text="Okay"
            android:layout_marginLeft="5dp"
            android:layout_weight="1"
            android:textColor="@color/white" />
    </LinearLayout>
</LinearLayout>