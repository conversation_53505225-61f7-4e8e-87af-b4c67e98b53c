<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:background="@color/white">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/scHeader"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_70sdp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:gravity="center_vertical"
        android:elevation="@dimen/_8sdp">

        <androidx.cardview.widget.CardView
            android:id="@+id/btnBack"
            android:layout_width="@dimen/_32sdp"
            android:layout_height="@dimen/_32sdp"
            android:backgroundTint="@color/colorChapterListCardBg"
            app:cardCornerRadius="@dimen/_8sdp"
            android:foreground="?selectableItemBackground"
            android:layout_marginStart="@dimen/_10sdp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:src="@drawable/ic_new_back_arrow"
                android:contentDescription="Go back"/>
        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/tvCartTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textSize="@dimen/_14ssp"
            android:layout_marginHorizontal="@dimen/_8sdp"
            android:textColor="@color/black"
            android:maxLines="2"
            android:ellipsize="end"
            tools:text="Book name"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/btnBack"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nsvCartContent"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/llDiscount"
        app:layout_constraintTop_toBottomOf="@+id/scHeader">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recCartBooks"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:orientation="vertical"
                android:paddingBottom="20dp"
                android:nestedScrollingEnabled="false"
                tools:visibility="visible"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/item_cart_book" />

            <LinearLayout
                android:id="@+id/llAddress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible"
                android:paddingHorizontal="16dp"
                android:paddingVertical="8dp">

                <LinearLayout
                    android:id="@+id/llShippingAddress"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                            android:id="@+id/tvShippingAddress"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:maxLines="1"
                            android:text="Shipping Address"
                            android:textColor="@color/ws_new_solid_color"
                            android:textSize="18sp"
                            android:layout_marginVertical="8dp"
                            app:ws_font_weight="bold" />

                    </RelativeLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etShippingName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Name"
                            android:inputType="text"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etShippingLastName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Last Name"
                            android:inputType="text"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etShippingMobile"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Mobile"
                            android:inputType="phone"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etShippingEmail"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Email"
                            android:inputType="textEmailAddress"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etShippingAddress1"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:maxLines="1"
                            android:hint="Address Line 1"
                            android:inputType="textPostalAddress"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etShippingAddress2"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:maxLines="1"
                            android:hint="Address Line 2"
                            android:inputType="textPostalAddress"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etShippingPincode"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Pincode"
                            android:inputType="number"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etShippingCity"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="City"
                            android:inputType="none"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etShippingState"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="State"
                            android:inputType="none"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <CheckBox
                        android:id="@+id/cbShowBillingAddress"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:text="Billing address different from Shipping Address" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llBillingAddress"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginTop="12dp"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                        android:id="@+id/tvBillingAddress"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:maxLines="1"
                        android:text="Billing Address"
                        android:textColor="@color/ws_new_solid_color"
                        android:textSize="18sp"
                        android:layout_marginVertical="8dp"
                        app:ws_font_weight="bold" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etBillingName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Name"
                            android:inputType="text"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etBillingLastName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Last Name"
                            android:inputType="text"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etBillingMobile"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Mobile"
                            android:inputType="phone"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etBillingEmail"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Email"
                            android:inputType="textEmailAddress"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etBillingAddress1"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:maxLines="1"
                            android:hint="Address Line 1" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etBillingAddress2"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:maxLines="1"
                            android:hint="Address Line 2" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etBillingPincode"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Pincode"
                            android:inputType="number"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etBillingCity"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="City"
                            android:inputType="none"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etBillingState"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="State"
                            android:inputType="none"/>
                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>


            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <LinearLayout
        android:id="@+id/llDiscount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/grant_permission_background"
        android:gravity="center"
        android:paddingVertical="4dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/llTotal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/animationView"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:scaleX="1.5"
            android:scaleY="1.5"
            android:visibility="visible"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/anim_discount"
            app:lottie_speed="0.8" />

        <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
            android:id="@+id/tvTotalDiscount"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:gravity="center"
            android:maxLines="1"
            android:paddingStart="8dp"
            android:paddingEnd="12dp"
            android:text="You saved ₹500"
            android:textColor="@color/white"
            app:ws_font_weight="normal" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/llOrderSummaryParent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/bg_white_bottom_flat"
        android:backgroundTint="@color/light_grey"
        android:animateLayoutChanges="true"
        app:layout_constraintBottom_toTopOf="@+id/llDiscount"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!--    Extra Parent is required here for smooth animation while showing and hiding summary  -->

        <LinearLayout
            android:id="@+id/llOrderSummary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible"
            android:padding="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical">

                <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                    android:id="@+id/tvSummary"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:text="Order summary"
                    android:textColor="@color/ws_new_solid_color"
                    android:textSize="18sp"
                    android:layout_marginVertical="8dp"
                    app:layout_constraintBottom_toTopOf="@+id/vBorder"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:ws_font_weight="bold" />

                <ImageView
                    android:id="@+id/btnDismissSummary"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="4dp"
                    android:src="@drawable/ic_close_video_exp"
                    android:foreground="?selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"/>

            </LinearLayout>


            <View
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:layout_marginVertical="8dp"
                android:background="#D5D4D4"/>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="8dp">

                <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                    android:id="@+id/tvSummarySubTotalTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:layout_weight="1"
                    android:maxLines="1"
                    android:text="Subtotal"
                    android:textColor="@color/ws_new_solid_color"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toTopOf="@+id/vBorder"
                    app:layout_constraintEnd_toStartOf="@+id/tvTotal"
                    app:layout_constraintStart_toStartOf="parent"
                    app:ws_font_weight="normal" />

                <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                    android:id="@+id/tvSummarySubTotal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    tools:text="₹5000"
                    android:textColor="@color/ws_new_solid_color"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toTopOf="@+id/vBorder"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:ws_font_weight="bold" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="8dp">

                <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                    android:id="@+id/tvSummaryDiscountTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:layout_weight="1"
                    android:maxLines="1"
                    android:text="Discount"
                    android:textColor="@color/ws_new_solid_color"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toTopOf="@+id/vBorder"
                    app:layout_constraintEnd_toStartOf="@+id/tvTotal"
                    app:layout_constraintStart_toStartOf="parent"
                    app:ws_font_weight="normal" />

                <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                    android:id="@+id/tvSummaryDiscount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    tools:text="₹0"
                    android:textColor="@color/ws_new_solid_color"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toTopOf="@+id/vBorder"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:ws_font_weight="bold" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="8dp">

                <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                    android:id="@+id/tvSummaryShippingTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:layout_weight="1"
                    android:maxLines="1"
                    android:text="Shipping charges"
                    android:textColor="@color/ws_new_solid_color"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toTopOf="@+id/vBorder"
                    app:layout_constraintEnd_toStartOf="@+id/tvTotal"
                    app:layout_constraintStart_toStartOf="parent"
                    app:ws_font_weight="normal" />

                <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                    android:id="@+id/tvSummaryShipping"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    tools:text="-"
                    text = "-"
                    android:textColor="@color/ws_new_solid_color"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toTopOf="@+id/vBorder"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:ws_font_weight="bold" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="8dp">

                <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                    android:id="@+id/tvSummaryTotalTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:layout_weight="1"
                    android:maxLines="1"
                    android:text="Total"
                    android:textColor="@color/ws_new_solid_color"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toTopOf="@+id/vBorder"
                    app:layout_constraintEnd_toStartOf="@+id/tvTotal"
                    app:layout_constraintStart_toStartOf="parent"
                    app:ws_font_weight="normal" />

                <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
                    android:id="@+id/tvSummaryTotal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    tools:text="₹5000"
                    android:textColor="@color/ws_new_solid_color"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toTopOf="@+id/vBorder"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:ws_font_weight="bold" />

            </LinearLayout>

        </LinearLayout>

        <View
            android:id="@+id/line1"
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:background="#D5D4D4"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/llTotal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="32dp"
        android:paddingVertical="6dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:clickable="true"
        android:focusable="true"
        android:foreground="?selectableItemBackground"
        android:gravity="center_vertical"
        android:background="@color/light_grey"
        app:layout_constraintBottom_toTopOf="@+id/vBorder"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
            android:id="@+id/tvTotalTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:text="Total: "
            android:textColor="@color/ws_new_solid_color"
            android:textSize="14sp"
            app:layout_constraintBottom_toTopOf="@+id/vBorder"
            app:layout_constraintEnd_toStartOf="@+id/tvTotal"
            app:layout_constraintStart_toStartOf="parent"
            app:ws_font_weight="normal" />

        <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
            android:id="@+id/tvTotal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:maxLines="1"
            tools:text="₹5000"
            android:textColor="@color/ws_new_solid_color"
            android:textSize="14sp"
            app:layout_constraintBottom_toTopOf="@+id/vBorder"
            app:layout_constraintEnd_toEndOf="parent"
            app:ws_font_weight="bold" />

        <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
            android:id="@+id/tvViewSummary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical|end"
            android:maxLines="1"
            android:layout_weight="1"
            android:text="View Summary"
            android:textColor="@color/lib_sort_btn"
            android:textSize="12sp"
            app:layout_constraintBottom_toTopOf="@+id/vBorder"
            app:layout_constraintEnd_toEndOf="parent"
            app:ws_font_weight="normal" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:srcCompat="@drawable/ic_keyboard_arrow_right"
            app:tint="@color/lib_sort_btn"/>
    </LinearLayout>

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lottieDiscountApplied"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:lottie_rawRes="@raw/anim_confetti"
        app:layout_constraintDimensionRatio="16:14"
        android:visibility="gone"
        app:lottie_repeatMode="restart"
        app:layout_constraintBottom_toBottomOf="@+id/llDiscount"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <View
        android:id="@+id/vBorder"
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:layout_marginBottom="8dp"
        android:background="@color/light_grey"
        app:layout_constraintBottom_toTopOf="@+id/btnBuy" />

    <Button
        android:id="@+id/btnContinue"
        android:layout_width="wrap_content"
        android:layout_height="60dp"
        android:layout_margin="8dp"
        android:background="@drawable/button_background_rounded_no_border"
        android:text="Continue to\nshopping"
        android:paddingHorizontal="24dp"
        android:textAllCaps="false"
        android:textSize="12sp"
        android:visibility="gone"
        android:foreground="?selectableItemBackground"
        app:layout_constraintBottom_toBottomOf="@+id/btnBuy"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/btnBuy" />

    <Button
        android:id="@+id/btnBuy"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:layout_gravity="center"
        android:drawableEnd="@drawable/ic_settings_arrow_right"
        android:drawablePadding="8dp"
        android:gravity="center"
        android:maxLines="1"
        android:textAllCaps="false"
        android:paddingHorizontal="16dp"
        android:text="@string/proceed_to_buy"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:foreground="?selectableItemBackground"
        android:background="@drawable/button_bg_discount_yellow"
        android:layout_margin="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/btnContinue"
        app:ws_font_weight="bold" />

    <com.wang.avi.AVLoadingIndicatorView
        android:id="@+id/cartLoader"
        style="@style/AVLoadingIndicatorView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="visible"
        tools:visibility="gone"
        app:indicatorColor="@color/loader_color"
        app:indicatorName="BallBeatIndicator"
        app:layout_constraintBottom_toTopOf="@+id/llDiscount"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/scHeader" />

    <LinearLayout
        android:id="@+id/llEmpty"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:visibility="gone"
        tools:visibility="gone"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@+id/llDiscount"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/scHeader">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/ivEmpty"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:lottie_rawRes="@raw/anim_empty_box"
            app:layout_constraintDimensionRatio="16:14"
            android:visibility="visible"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:layout_constraintBottom_toBottomOf="@+id/llDiscount"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
            android:id="@+id/tvError"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="Looks like you don’t have any books in your cart"
            android:textColor="@color/ws_new_solid_color"
            android:layout_marginHorizontal="36dp"
            android:textSize="16sp"
            app:layout_constraintBottom_toTopOf="@+id/vBorder"
            app:layout_constraintEnd_toStartOf="@+id/tvTotal"
            app:layout_constraintStart_toStartOf="parent"
            app:ws_font_weight="normal" />

        <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
            android:id="@+id/btnShop"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:drawablePadding="8dp"
            android:gravity="center"
            android:maxLines="1"
            android:textAllCaps="false"
            android:paddingHorizontal="16dp"
            android:text="Go to shop"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:foreground="?selectableItemBackground"
            android:background="@drawable/button_bg_discount_yellow"
            android:layout_margin="24dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/btnContinue"
            app:ws_font_weight="bold" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>