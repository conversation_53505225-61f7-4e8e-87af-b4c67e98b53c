<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/colorPrimaryDark">

    <RelativeLayout
        android:id="@+id/grant_perm_text_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="56dp"
        android:paddingLeft="32dp"
        android:paddingStart="32dp"
        android:paddingEnd="32dp"
        android:paddingRight="32dp">

        <TextView
            android:id="@+id/grant_perm_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="Grant Permission"
            android:textSize="24sp"
            android:typeface="monospace"
            android:textColor="@color/white" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/grant_perm_text"
            android:text="@string/grantlocationpermdesctext"
            android:textSize="16sp"
            android:typeface="normal"
            android:textColor="@color/white" />

    </RelativeLayout>

    <View
        android:id="@+id/view1"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/line_view_color"
        android:layout_above="@id/grantpermbottomToolbar" />

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/grantpermbottomToolbar"
        android:layout_width="match_parent"
        android:layout_height="?android:attr/actionBarSize"
        android:layout_alignParentBottom="true"
        android:background="@color/primary_bg_red"
        app:contentInsetLeft="0dp"
        app:contentInsetStart="0dp"
        android:elevation="1dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:weightSum="2">

            <TextView
                android:id="@+id/backbtn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:typeface="serif"
                android:text="Cancel"
                android:gravity="center"
                android:paddingTop="17dp"
                android:paddingBottom="16dp"
                android:paddingStart="70dp"
                android:paddingEnd="71dp" />

            <View
                android:id="@+id/view2"
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/line_view_color"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp" />

            <TextView
                android:id="@+id/settingsbtn"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:typeface="serif"
                android:text="Okay"
                android:gravity="center"
                android:paddingTop="17dp"
                android:paddingBottom="16dp"
                android:paddingStart="70dp"
                android:paddingEnd="71dp" />

        </LinearLayout>

    </androidx.appcompat.widget.Toolbar>

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/grant_perm_anim"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/grant_perm_text_layout"
        android:layout_above="@id/view1"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="25dp"
        android:layout_marginBottom="10dp"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:lottie_rawRes="@raw/notification_perm_anim" />

</RelativeLayout>