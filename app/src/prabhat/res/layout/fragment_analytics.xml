<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_bg_dark"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeToRefresh"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/primary_bg_dark">

            <TextView
                android:id="@+id/txtLastQuiz"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_20sdp"
                android:layout_marginEnd="@dimen/_20sdp"
                android:gravity="center"
                android:visibility="gone"
                android:text="Last Quiz"
                android:textColor="@color/white" />

            <com.facebook.shimmer.ShimmerFrameLayout
                android:id="@+id/item_home_options_new_shimmer"
                android:layout_width="match_parent"
                android:visibility="gone"
                android:layout_below="@+id/txtLastQuiz"
                android:layout_height="70dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="60dp"
                    android:weightSum="3.5"
                    android:layout_marginTop="@dimen/_4sdp"
                    android:layout_marginEnd="@dimen/_20sdp"
                    android:layout_marginStart="@dimen/_20sdp"
                    android:background="@drawable/home_card_bg"
                    android:orientation="horizontal">

                </LinearLayout>


            </com.facebook.shimmer.ShimmerFrameLayout>

            <LinearLayout
                android:id="@+id/lltLastQuiz"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_below="@+id/txtLastQuiz"
                android:weightSum="3.5"
                android:visibility="gone"
                android:layout_marginTop="@dimen/_4sdp"
                android:layout_marginEnd="@dimen/_20sdp"
                android:layout_marginStart="@dimen/_20sdp"
                android:background="@drawable/home_card_bg"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/txtQuiztitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Last Quiz"
                    android:gravity="center"
                    android:layout_weight="1"
                    android:layout_gravity="center"
                    android:textSize="@dimen/_8sdp"
                    android:layout_marginEnd="@dimen/_20sdp"
                    android:layout_marginStart="@dimen/_20sdp"
                    android:textColor="@color/white"
                    />
                <View
                    android:layout_height="20dp"
                    android:layout_gravity="center"
                    style="@style/Divider.Vertical"/>

                <TextView
                    android:id="@+id/txtScore"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:text="Last Quiz"
                    android:layout_weight="1"
                    android:textSize="@dimen/_8sdp"
                    android:layout_marginEnd="@dimen/_20sdp"
                    android:layout_marginStart="@dimen/_20sdp"
                    android:textColor="@color/white"
                    />

                <View
                    android:layout_height="20dp"
                    android:layout_gravity="center"
                    style="@style/Divider.Vertical"/>
                <TextView
                    android:id="@+id/txtQuizStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:layout_weight="1"
                    android:layout_gravity="center"
                    android:text="Last Quiz"
                    android:textSize="@dimen/_8sdp"
                    android:layout_marginEnd="@dimen/_20sdp"
                    android:layout_marginStart="@dimen/_20sdp"
                    android:textColor="@color/white"
                    />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:src="@drawable/ic_arrow_right_small"
                    />

            </LinearLayout>

            <TextView
                android:id="@+id/txtheadder"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/lltLastQuiz"
                android:layout_marginStart="@dimen/_10sdp"
                android:layout_marginTop="@dimen/_5sdp"
                android:layout_marginEnd="@dimen/_10sdp"
                android:gravity="center"
                android:text="Quiz History"
                android:textColor="@color/white" />

            <com.github.mikephil.charting.charts.PieChart
                android:id="@+id/paichart"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:layout_below="@+id/txtheadder"
                android:layout_marginStart="@dimen/_10sdp"
                android:layout_marginTop="@dimen/_5sdp"
                android:visibility="gone"
                android:layout_marginEnd="@dimen/_10sdp" />



            <androidx.core.widget.NestedScrollView
                android:id="@+id/nested_seroll_view_post"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/layout_page"
                android:layout_below="@+id/paichart"
                android:background="@color/primary_bg_dark"
                android:fillViewport="true"
                android:overScrollMode="never"
                app:layout_behavior="@string/appbar_scrolling_view_behavior">

                <RelativeLayout
                    android:id="@+id/layoutContainer"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">


                    <LinearLayout
                        android:id="@+id/lltTitle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_10sdp"
                        android:layout_marginTop="@dimen/_5sdp"
                        android:layout_marginEnd="@dimen/_10sdp"
                        android:orientation="horizontal"
                        android:visibility="gone"
                        android:weightSum="3.5">

                        <TextView
                            android:layout_width="70dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="@dimen/_20sdp"
                            android:layout_marginEnd="@dimen/_20sdp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="Name"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_8sdp" />


                        <TextView

                            android:layout_width="70dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="@dimen/_20sdp"
                            android:layout_marginEnd="@dimen/_20sdp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="Score"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_8sdp" />


                        <TextView
                            android:layout_width="70dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="@dimen/_9sdp"
                            android:layout_marginEnd="@dimen/_40sdp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="Status"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_8sdp" />

                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerView_groups_details"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/lltTitle"
                        android:layout_marginStart="@dimen/_10sdp"
                        android:layout_marginTop="@dimen/_5sdp"
                        android:layout_marginEnd="@dimen/_10sdp" />

                    <ProgressBar
                        android:id="@+id/idPBLoading"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/recyclerView_groups_details"
                        android:visibility="gone" />


                    <LinearLayout
                        android:id="@+id/noDataLayout"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center|top"
                        android:layout_margin="@dimen/_16sdp"
                        android:layout_below="@id/recyclerView_groups_details"
                        android:background="@drawable/all_course_list_item_background"
                        android:elevation="6dp"
                        android:gravity="center"
                        android:layout_centerInParent="true"
                        android:orientation="vertical"
                        android:padding="16dp"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.497"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="0.189">

                        <ImageView
                            android:id="@+id/emptyImage"
                            android:layout_width="60dp"
                            android:layout_height="60dp"
                            android:gravity="center"
                            android:src="@drawable/ic_book" />

                        <com.ws.core_ui.custom_views.WSTextView
                            android:id="@+id/emptytextview"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:padding="3dp"
                            android:text="You have not attempted the quiz"
                            android:textColor="@color/colorBookNameTitle"
                            android:textSize="12sp"
                            android:typeface="normal" />

                    </LinearLayout>
                </RelativeLayout>
            </androidx.core.widget.NestedScrollView>

            <RelativeLayout
                android:id="@+id/layout_page"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:background="@color/primary_bg_dark"
                android:visibility="gone">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:layout_marginLeft="15dp"
                    android:layout_marginRight="15dp"
                    android:layout_marginBottom="3dp"
                    android:background="@drawable/round_primary_red"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:visibility="visible">

                    <TextView
                        android:id="@+id/textView_page"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentLeft="true"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="5dp"
                        android:fontFamily="@font/poppins_light"
                        android:gravity="center"
                        android:paddingTop="2dp"
                        android:text="Page"
                        android:textColor="@color/white"
                        android:textSize="10sp" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_view_pages"
                        android:layout_width="wrap_content"
                        android:layout_height="35dp"
                        android:layout_marginLeft="3dp"
                        android:layout_toRightOf="@+id/textView_page"
                        android:scrollbars="horizontal"
                        android:visibility="visible" />
                </RelativeLayout>
            </RelativeLayout>

            <include
                layout="@layout/no_internet_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true" />

        </RelativeLayout>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
    <com.wang.avi.AVLoadingIndicatorView
        android:id="@+id/groupLoader_details"
        style="@style/AVLoadingIndicatorView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="visible"
        android:layout_centerInParent="true"
        app:indicatorColor="@color/primary_bg_red"
        app:indicatorName="BallBeatIndicator" />


</RelativeLayout>