<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/_65sdp"
    android:id="@+id/item_leader_board_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal"
    app:layout_constraintTop_toBottomOf="@+id/tabLayout"
    android:paddingLeft="12dp"
    android:paddingRight="12dp"
    android:gravity="center"
    android:weightSum="2.5"
    android:background="@color/primary_bg_dark"
    android:layout_margin="4dp">

        <TextView
            android:id="@+id/textRank"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.25"
            android:text="@string/rank"
            android:textColor="@color/white"
            android:textSize="@dimen/_14ssp"/>


        <TextView
            android:id="@+id/userRank"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.25"
            android:text="@string/rank"
            android:textColor="@color/primary_bg_red"
            android:textSize="@dimen/_18ssp"
            android:visibility="gone" />


        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="@dimen/_50sdp"
            android:layout_weight="2.25"
            android:background="@drawable/bg_round_border"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingLeft="3dp"
            android:paddingRight="3dp">

                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.3">

                        <de.hdodenhof.circleimageview.CircleImageView
                            android:id="@+id/userRankImage"
                            android:layout_width="@dimen/_40sdp"
                            android:layout_height="@dimen/_40sdp"
                            android:background="@drawable/circle_background"
                            android:scaleType="centerCrop"
                            android:src="@drawable/avatar_bg_circle" />
                </RelativeLayout>

                <LinearLayout
                    android:id="@+id/lltTextNameState"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/textName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:ellipsize="end"
                    android:gravity="left"
                    android:maxEms="10"
                    android:maxLines="1"
                    android:layout_marginLeft="@dimen/_4sdp"
                    android:text="@string/rank"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_14ssp"/>

                <TextView
                    android:id="@+id/textState"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:ellipsize="end"
                    android:gravity="left"
                    android:maxEms="10"
                    android:maxLines="1"
                    android:layout_marginLeft="@dimen/_4sdp"
                    android:text="@string/rank"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_11ssp" />


                </LinearLayout>

                <TextView
                    android:id="@+id/textScore"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_weight="0.5"
                    android:gravity="center"
                    android:text="@string/rank"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_14ssp" />




                <LinearLayout
                    android:id="@+id/lltNameState"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:visibility="gone"
                    android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/userName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:ellipsize="end"
                            android:gravity="left"
                            android:maxEms="10"
                            android:maxLines="1"
                            android:layout_marginLeft="@dimen/_4sdp"
                            android:text="@string/rank"
                            android:textColor="@color/primary_bg_red"
                            android:textSize="@dimen/_18ssp" />

                        <TextView
                            android:id="@+id/userState"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:ellipsize="end"
                            android:gravity="left"
                            android:maxEms="10"
                            android:maxLines="1"
                            android:layout_marginLeft="@dimen/_4sdp"
                            android:text="@string/rank"
                            android:textColor="@color/primary_bg_red"
                            android:textSize="@dimen/_18ssp"/>


                </LinearLayout>


                <TextView
                    android:id="@+id/userScore"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_weight="0.5"
                    android:gravity="center"
                    android:text="@string/rank"
                    android:textColor="@color/primary_bg_red"
                    android:textSize="@dimen/_18ssp"
                    android:visibility="gone" />

        </LinearLayout>
</LinearLayout>