<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="horizontal"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:id="@+id/ticker_item_parent"
    android:padding="5dp"
    android:gravity="center_vertical"
    android:background="@color/transparent">

    <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/ticker_item_image"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"/>

    <TextView
        android:id="@+id/ticker_item_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_medium"
        android:textSize="12sp"
        android:textColor="@color/white"
        android:layout_marginEnd="10dp"
        android:gravity="center"
        tools:text="User name"/>

    <TextView
        android:id="@+id/ticker_item_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_medium"
        android:textSize="12sp"
        android:textColor="@color/white"
        android:gravity="center"
        tools:text="Sample text"/>

</LinearLayout>