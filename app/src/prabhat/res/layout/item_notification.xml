<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/notificationItemHolder"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginTop="5dp"
    android:layout_marginBottom="5dp"
    android:background="@drawable/bg_rounded"
    android:padding="8dp">

    <TextView
        android:id="@+id/notificationSentTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:maxLines="1"
        android:padding="5dp"
        android:layout_marginStart="15dp"
        android:layout_marginBottom="10dp"
        android:textSize="12sp"
        android:textStyle="italic"
        android:textColor="@color/white"
        android:typeface="normal"
        android:ellipsize="end"
        tools:text="@tools:sample/date/mmddyy" />

    <ImageView
        android:id="@+id/notificationArrow"
        android:layout_width="10dp"
        android:layout_height="10dp"
        android:layout_below="@id/notificationSentTime"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="10dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="5dp"
        android:src="@drawable/ic_arrow"
        android:layout_centerVertical="true"/>

    <TextView
        android:id="@+id/textTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/notificationSentTime"
        android:layout_toStartOf="@id/notificationSentTime"
        android:layout_marginStart="5dp"
        android:typeface="normal"
        android:textSize="14sp"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:maxLines="2"
        android:ellipsize="middle"
        tools:text="Prepjoy"/>

    <TextView
        android:id="@+id/textDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/textTitle"
        android:layout_toStartOf="@id/notificationArrow"
        android:typeface="normal"
        android:layout_marginStart="5dp"
        android:textSize="12sp"
        android:gravity="start"
        android:textColor="@color/white"
        android:maxLines="3"
        android:ellipsize="end"
        android:layout_marginTop="15dp"
        tools:text="Description"/>

</RelativeLayout>