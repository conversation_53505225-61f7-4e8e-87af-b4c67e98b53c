<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Drop Shadow -->
    <item>
        <shape android:shape="rectangle">
            <!--<padding
                android:bottom="2dp"
                android:left="1dp"
                android:right="1dp"
                android:top="0.5dp" />-->

            <solid android:color="#05E8CDDC" />
            <corners android:radius="20dp" />

        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <!--<padding
                android:bottom="2dp"
                android:left="1dp"
                android:right="1dp"
                android:top="0.5dp" />-->

            <solid android:color="#10E3C1D4" />
            <corners android:radius="20dp" />

        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <!--<padding
                android:bottom="2dp"
                android:left="1dp"
                android:right="1dp"
                android:top="0.5dp" />-->

            <solid android:color="#20D8A3C1" />
            <corners android:radius="20dp" />

        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <!--<padding
                android:bottom="2dp"
                android:left="1dp"
                android:right="1dp"
                android:top="0.5dp" />-->

            <solid android:color="#15DA8BB8" />
            <corners android:radius="20dp" />

        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <!--<padding
                android:bottom="2dp"
                android:left="1dp"
                android:right="1dp"
                android:top="0.5dp" />-->

            <solid android:color="#20ED95C7" />
            <corners android:radius="20dp" />

        </shape>
    </item>

    <!-- Background Color (white) -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/purple_700" />
            <corners android:radius="20dp" />

        </shape>
    </item>

</layer-list>