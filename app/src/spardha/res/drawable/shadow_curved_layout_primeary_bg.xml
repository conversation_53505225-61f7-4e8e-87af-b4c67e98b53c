<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Drop Shadow Stack -->
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="1dp"
                android:left="1dp"
                android:right="1dp"
                android:top="1dp" />

            <solid android:color="#00891DCD" />

            <corners android:radius="10dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="1dp"
                android:left="1dp"
                android:right="1dp"
                android:top="1dp" />

            <solid android:color="#10891DCD" />

            <corners android:radius="10dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="1dp"
                android:left="1dp"
                android:right="1dp"
                android:top="1dp" />

            <solid android:color="#20891DCD" />

            <corners android:radius="10dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="1dp"
                android:left="1dp"
                android:right="1dp"
                android:top="1dp" />

            <solid android:color="#30891DCD" />

            <corners android:radius="10dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="1dp"
                android:left="1dp"
                android:right="1dp"
                android:top="1dp" />

            <solid android:color="#50891DCD" />

            <corners android:radius="10dp" />
        </shape>
    </item>

    <!-- Background -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/primary_bg_red" />

            <corners android:radius="10dp" />
        </shape>
    </item>

</layer-list>