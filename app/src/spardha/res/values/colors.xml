<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="purple_200">#150F36</color>
    <color name="purple_500">#150F36</color>
    <color name="purple_700">#150F36</color>
    <color name="purple_lite">#20184F</color>
    <color name="teal_200">#E83500</color>
    <color name="teal_700">#E83500</color>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    <color name="primary_bg_dark">#04001D</color>
    <color name="primary_bg_red">#E83500</color>
    <color name="heighlight_bg_blue">#150F36</color>
    <color name="gray">#7e7e7e</color>
    <color name="tab_bg_gray">#A9A9A9</color>
    <color name="driftColorAccent">#444444</color>
    <color name="disablecolor">#fa8072</color>
    <color name="video_title_color_land">#0091ea</color>
    <color name="grant_permission_background">#20aa66</color>
    <color name="colorAccentOld">#F79420</color>
    <color name="colorActionBarText">#444444</color>
    <color name="quiz_wrong_selection">#B72319</color>
    <color name="light_gray">#D3D3D3</color>
    <color name="very_light_gray">#E9E9E9</color>
    <color name="light_white">#83FFFFFF</color>
    <color name="very_light_white">#45FFFFFF</color>
    <color name="transparent">#00000000</color>
    <color name="gradientColorStart">#D13F7F</color>
    <color name="primary_bg_lite">#4B4953</color>
    <color name="colorAccent">#E83500</color>
    <color name="colorPrimary">#150F36</color>


    <!--  Configurable colors for shop books -->
    <color name="colorShopBg">@color/primary_bg_dark</color>
    <color name="colorShopBookCardBg">@color/purple_500</color>
    <color name="colorShopBookTitle">@color/white</color>
    <color name="colorShopBookPublisher">@color/white</color>
    <color name="colorShopBookOfferPrice">@color/main_theme_red</color>
    <color name="colorShopSearchText">@color/white</color>
    <color name="colorShopSearchHint">@color/gray</color>
    <color name="colorShopErrorText">@color/white</color>
    <color name="colorShopErrorTextSecondary">@color/light_gray</color>
    <color name="colorShopBookOriginalPrice">@color/white</color>

    <!--  Configurable colors for Library books  -->
    <color name="colorLibraryBg">@color/primary_bg_dark</color>
    <color name="colorLibraryCardBg">@color/purple_500</color>
    <color name="colorLibraryCardTitle">@color/white</color>
    <color name="colorLibrarySearchText">@color/white</color>
    <color name="colorLibrarySearchHint">@color/gray</color>
    <color name="colorLibraryErrorText">@color/light_gray</color>

    <!--  Configurable colors for Chapters list  -->
    <color name="colorChapterListCardBg">@color/purple_500</color>
    <color name="colorChapterListBg">@color/primary_bg_dark</color>
    <color name="colorBookNameTitle">@color/white</color>
    <color name="colorChapterTitleTextColor">@color/white</color>
    <color name="colorResourceCountTextColor">@color/gray</color>
    <color name="colorBottomSheetBg">@color/primary_bg_dark</color>
    <color name="colorActionSheetActionCardBg">@color/primary_bg_dark</color>

    <color name="colorBottomSheetPrimaryDark">@color/primary_bg_dark</color>

    <!--  Configurable colors for Resources  -->
    <color name="colorWeblinkBg">@color/primary_bg_dark</color>
    <color name="colorWeblinkCardBg">@color/purple_500</color>
    <color name="colorReadingBg">@color/primary_bg_dark</color>
    <color name="colorReadingCardBg">@color/purple_500</color>
    <color name="discount_green">#27AE60</color>
    <color name="gradientColorEnd">#151515</color>
    <color name="color_red_delete">#CE0000</color>
    <color name="color_order_info">#4B4953</color>
</resources>