<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="TabLayoutStyle" parent="Base.Widget.Design.TabLayout">
        <item name="android:textSize">@dimen/tabtextsize</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="PrimaryButton" parent="Theme.AppCompat.Light">
        <item name="android:colorBackground">@color/white</item>
    </style>

    <!-- It change activity fonts, add into activity theme -->
    <style name="AppTheme1" parent="Theme.AppCompat.Light">
        <item name="android:typeface">monospace</item>

        <item name="colorPrimaryDark">@color/primary_bg_dark</item>



      <!--  <item name="colorPrimary">@color/primary_bg_dark</item>
        <item name="colorAccent">@color/primary_bg_dark</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:textColorSecondary">@color/white</item>-->
    </style>

    <style name="AppTheme.NoActionBar" parent="Theme.AppCompat.Light">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="HomeTabStyle" parent="Base.Widget.Design.TabLayout">
        <item name="android:textSize">@dimen/hometabtextsize</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="datepicker" parent="Theme.PreopJoy">
        <item name="android:windowIsFloating">true</item>
    </style>

    <style name="NewsActionBarStyle" parent="Theme.AppCompat.Light">
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">@color/white</item>
        <item name="actionMenuTextColor">@color/white</item>
        <item name="android:background">@color/primary_bg_dark</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:drawablePadding">5dp</item>
    </style>


    <style name="Divider.Vertical" parent="Divider">
        <item name="android:layout_width">1dp</item>
        <item name="android:layout_height">match_parent</item>
    </style>

    <style name="Divider">
        <item name="android:background">@color/gray</item>
    </style>

</resources>