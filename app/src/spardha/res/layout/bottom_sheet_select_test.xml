<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/bottom_sheet"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shadow_lite_curved_layout_bottom_flat_top_curve"
    android:visibility="visible"
    app:behavior_hideable="true"
    app:behavior_peekHeight="0dp"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

    <LinearLayout
        android:id="@+id/layout_back"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:orientation="vertical">

        <View
            android:layout_width="50dp"
            android:layout_height="5dp"
            android:layout_gravity="center"
            android:layout_marginTop="25dp"
            android:background="@drawable/bg_solid_white" />

        <RelativeLayout
            android:id="@+id/layout_play"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginStart="20dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/button_background_rounded_white_border">

            <TextView
                android:id="@+id/button_play"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fontFamily="@font/poppins_bold"
                android:gravity="center"
                android:paddingTop="4dp"
                android:text="PLAY"
                android:textColor="@color/white"
                android:textSize="14sp" />

            <ImageView
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="25dp"
                android:src="@drawable/ic_play" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/layout_practice"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginStart="20dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/button_background_rounded_white_border">

            <TextView
                android:id="@+id/button_practice"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fontFamily="@font/poppins_bold"
                android:gravity="center"
                android:paddingTop="4dp"
                android:text="PRACTICE"
                android:textColor="@color/white"
                android:textSize="14sp" />

            <ImageView
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="25dp"
                android:src="@drawable/is_practice" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/layout_test"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginStart="20dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/button_background_rounded_white_border">

            <TextView
                android:id="@+id/button_test"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fontFamily="@font/poppins_bold"
                android:gravity="center"
                android:paddingTop="4dp"
                android:text="TEST"
                android:textColor="@color/white"
                android:textSize="14sp" />

            <ImageView
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="25dp"
                android:src="@drawable/ic_test" />
        </RelativeLayout>
    </LinearLayout>

    <com.wang.avi.AVLoadingIndicatorView
        android:id="@+id/progressLoaderTest"
        style="@style/AVLoadingIndicatorView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:elevation="6dp"
        android:visibility="gone"
        app:indicatorColor="@color/white"
        app:indicatorName="LineScalePulseOutRapidIndicator" />
</RelativeLayout>