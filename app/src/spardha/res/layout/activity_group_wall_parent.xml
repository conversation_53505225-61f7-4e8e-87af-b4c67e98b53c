<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_bg_dark"
    tools:context=".news.NewsPreferenceActivity">

    <RelativeLayout
        android:id="@+id/rrltBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            app:srcCompat="@drawable/ic_back" />

        <TextView
            android:id="@+id/txtBack"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="5dp"
            android:layout_toRightOf="@+id/imgBack"
            android:gravity="center"
            android:text="Home"
            android:textColor="@color/white"
            android:textSize="17dp"
            android:textStyle="bold" />

    </RelativeLayout>
    <FrameLayout
        android:id="@+id/group_wall"
        android:layout_width="match_parent"
        android:layout_below="@+id/rrltBack"
        android:layout_height="match_parent" />

</RelativeLayout>