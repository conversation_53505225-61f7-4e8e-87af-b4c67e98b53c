<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <include
        android:id="@+id/header"
        layout="@layout/header_language_change"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/header"
        android:background="@color/primary_bg_dark"
        android:scrollbarStyle="insideOverlay"
        android:scrollbars="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <RelativeLayout
                android:id="@+id/settingsdatalayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="24dp"
                android:paddingLeft="24dp"
                android:paddingRight="24dp"
                android:paddingTop="16dp">

                <TextView
                    android:id="@+id/settingsabouttext"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_alignParentLeft="true"
                    android:text="About"
                    android:textColor="@color/light_gray"
                    android:textSize="12sp"
                    android:typeface="normal" />

                <TextView
                    android:id="@+id/privacypolicytext"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_below="@id/settingsabouttext"
                    android:layout_alignStart="@id/settingsabouttext"
                    android:layout_centerInParent="true"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:background="@drawable/bg_extra_rounded"
                    android:gravity="center|left"
                    android:padding="10dp"
                    android:text="Privacy Policy"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:typeface="normal" />

                <TextView
                    android:id="@+id/termsofservicetext"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_below="@id/privacypolicytext"
                    android:layout_alignStart="@id/privacypolicytext"
                    android:layout_alignLeft="@id/privacypolicytext"
                    android:foreground="?attr/selectableItemBackgroundBorderless"
                    android:background="@drawable/bg_extra_rounded"
                    android:gravity="center|left"
                    android:layout_marginTop="10dp"
                    android:padding="10dp"
                    android:text="Terms of Service"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:typeface="normal" />

            </RelativeLayout>

            <View
                android:id="@+id/view2"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:visibility="gone"
                android:layout_below="@id/settingsdatalayout"
                android:layout_marginTop="9dp"
                android:background="#8CBDBDBD" />



        </RelativeLayout>
    </ScrollView>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="18dp"
        android:paddingBottom="20dp"
        android:paddingStart="24dp"
        android:paddingLeft="24dp"
        android:paddingEnd="42dp"
        android:layout_above="@id/relativeLayout"
        android:paddingRight="42dp"
        android:foreground="?attr/selectableItemBackgroundBorderless">


        <TextView
            android:id="@+id/txtAppVerions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="10sp"
            android:typeface="normal" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/relativeLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="20dp"
        android:gravity="center">


        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgFooterLogo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="10dp"
            app:srcCompat="@drawable/ws_logo" />

        <TextView
            android:id="@+id/txtPoweredBy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@+id/imgFooterLogo"
            android:text="@string/footer_powered_by"
            android:textColor="@color/white"
            android:textSize="10sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/txtPoweredBy"
            android:layout_toEndOf="@+id/imgFooterLogo"
            android:text="@string/footer_wonderslate"
            android:textColor="@color/white"
            android:textSize="15sp" />
    </RelativeLayout>

    <com.wang.avi.AVLoadingIndicatorView
        android:id="@+id/logoutLoader"
        style="@style/AVLoadingIndicatorView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="gone"
        app:indicatorColor="@color/loader_color"
        app:indicatorName="BallBeatIndicator" />

    <include
        layout="@layout/no_internet_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true" />

</RelativeLayout>