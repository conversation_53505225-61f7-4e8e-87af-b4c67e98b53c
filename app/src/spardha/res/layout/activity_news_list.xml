<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_bg_dark"
    android:id="@+id/newsListParent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <RelativeLayout
        android:id="@+id/rrltBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            app:srcCompat="@drawable/ic_back" />

        <TextView
            android:id="@+id/txtBack"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="5dp"
            android:layout_toRightOf="@+id/imgBack"
            android:gravity="center"
            android:text="Home"
            android:textColor="@color/white"
            android:textSize="17dp"
            android:textStyle="bold" />

    </RelativeLayout>

    <androidx.cardview.widget.CardView
        android:id="@+id/btnNewsPref"
        android:layout_width="@dimen/_32sdp"
        android:layout_height="50dp"
        android:layout_alignParentEnd="true"
        android:layout_marginRight="10dp"
        android:backgroundTint="@color/primary_bg_dark">

        <ImageView
            android:id="@+id/imageView_change_news_preference"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="right|center"
            android:scaleType="fitXY"
            android:src="@drawable/is_settings_pref"
            android:visibility="visible"
            app:tint="@color/white" />
    </androidx.cardview.widget.CardView>

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/rrltBack"
        android:background="@color/primary_bg_dark"
        app:tabIndicatorColor="@color/primary_bg_red"
        app:tabSelectedTextColor="@color/primary_bg_red"
        app:tabTextColor="@color/primary_bg_red"></com.google.android.material.tabs.TabLayout>

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/viewPager"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/tabLayout"
        android:layout_centerInParent="true"
        android:background="@color/primary_bg_dark"
        tools:layout_editor_absoluteX="8dp" />

    <com.wang.avi.AVLoadingIndicatorView
        android:id="@+id/mergedNewsLoader"
        style="@style/AVLoadingIndicatorView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_gravity="center"
        android:visibility="gone"
        app:indicatorColor="@color/primary_bg_red"
        app:indicatorName="LineScalePulseOutRapidIndicator" />
</RelativeLayout>