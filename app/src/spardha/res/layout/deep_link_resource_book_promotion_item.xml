<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/deep_link_res_promotion_container_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/colorShopBg">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <androidx.cardview.widget.CardView
            android:id="@+id/book_item_frame"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?selectableItemBackground"
            android:backgroundTint="@color/colorShopBookCardBg"
            app:cardCornerRadius="4dp"
            app:cardElevation="3dp"
            app:cardPreventCornerOverlap="true"
            app:cardUseCompatPadding="true">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="8dp">

                <androidx.cardview.widget.CardView
                    android:id="@+id/placeHolderCardView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="8dp"
                    android:elevation="0dp"
                    android:innerRadius="0dp"
                    android:shape="rectangle"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="0dp"
                    app:layout_constraintDimensionRatio="13:16"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:id="@+id/ivBookCover"
                        android:layout_width="120dp"
                        android:layout_height="120dp"
                        android:layout_below="@id/buy_book_label"
                        android:adjustViewBounds="true"
                        android:scaleType="fitXY"
                        android:src="@drawable/book_cover_placeholder"
                        android:contentDescription="Book Cover Image" />

                </androidx.cardview.widget.CardView>

                <TextView
                    android:id="@+id/tvBookType"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:background="@drawable/bg_book_type"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="5dp"
                    android:textColor="@color/price_simmer_badge_color"
                    android:textSize="12sp"
                    android:layout_marginStart="-5dp"
                    android:layout_marginBottom="40dp"
                    app:layout_constraintBottom_toBottomOf="@+id/placeHolderCardView"
                    app:layout_constraintStart_toStartOf="@id/placeHolderCardView"
                    tools:text="ebook"
                    android:visibility="gone"/>

                <TextView
                    android:id="@+id/bookTitleTxt"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="2dp"
                    android:layout_marginEnd="8dp"
                    android:ellipsize="end"
                    android:fontFamily="@font/poppins_medium"
                    android:gravity="center|left"
                    android:lines="2"
                    android:maxLines="2"
                    android:textColor="@color/colorShopBookTitle"
                    android:textSize="16sp"
                    android:typeface="normal"
                    app:autoSizeMaxTextSize="14sp"
                    app:autoSizeMinTextSize="12sp"
                    app:autoSizeTextType="uniform"
                    app:layout_constraintStart_toEndOf="@id/placeHolderCardView"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/placeHolderCardView"
                    tools:text="Book name" />

                <TextView
                    android:id="@+id/bookPublisherTxt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="2dp"
                    android:fontFamily="@font/poppins_light"
                    android:textColor="@color/colorShopBookPublisher"
                    android:alpha="0.6"
                    android:textSize="12sp"
                    app:layout_constraintStart_toEndOf="@id/placeHolderCardView"
                    app:layout_constraintTop_toBottomOf="@id/bookTitleTxt"
                    tools:text="Publisher name" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.cardview.widget.CardView>

        <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
            android:id="@+id/buyBookBtn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:background="@drawable/button_background_filled"
            android:elevation="8dp"
            android:gravity="center"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="14sp"
            tools:text="Buy eBook"
            android:paddingVertical="15dp"
            android:paddingHorizontal="15dp"/>

    </LinearLayout>

</FrameLayout>