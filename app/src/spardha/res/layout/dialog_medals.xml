<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/_10sdp"
    android:background="@color/primary_bg_dark"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Medals"
        android:paddingTop="10dp"
        android:textStyle="bold"
        android:gravity="center"
        android:textSize="@dimen/_16sdp"
        android:textColor="@color/primary_bg_red"/>

    <LinearLayout
        android:id="@+id/linearList"
        android:layout_below="@id/txtTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="@dimen/_10sdp"
        android:visibility="visible">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Consecutive Wins"
            android:gravity="left"
            android:textStyle="bold"
            android:layout_marginLeft="@dimen/_3sdp"
            android:textColor="@color/white"
            android:textSize="@dimen/_13sdp"
            />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Medal Name"
            android:gravity="left"
            android:textStyle="bold"
            android:layout_marginLeft="@dimen/_10sdp"
            android:textColor="@color/white"
            android:textSize="@dimen/_13sdp" />


    </LinearLayout>

    <LinearLayout
        android:id="@+id/lltBronze"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:orientation="horizontal"
        android:layout_below="@+id/linearList"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:gravity="center"
        android:layout_margin="4dp"
        android:background="@drawable/bg_rounded">
        <TextView

            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Three"
            android:textColor="@color/white"
            android:gravity="left"
            android:textSize="@dimen/_12ssp"/>

        <TextView

            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Bronze"
            android:layout_gravity="center_vertical"
            android:gravity="left"
            android:ellipsize="end"
            android:maxEms="10"
            android:maxLines="1"
            android:textColor="@color/white"
            android:layout_marginLeft="@dimen/_10sdp"
            android:textSize="@dimen/_12ssp"/>


    </LinearLayout>

    <LinearLayout
        android:id="@+id/lltSilver"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:orientation="horizontal"
        android:layout_below="@+id/lltBronze"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:gravity="center"
        android:layout_margin="4dp"
        android:background="@drawable/bg_rounded">
        <TextView

            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Five"
            android:textColor="@color/white"
            android:gravity="left"

            android:textSize="@dimen/_12ssp"/>

        <TextView

            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Silver"
            android:layout_gravity="center_vertical"
            android:gravity="left"
            android:ellipsize="end"
            android:maxEms="10"
            android:maxLines="1"
            android:layout_marginLeft="@dimen/_10sdp"
            android:textColor="@color/white"
            android:textSize="@dimen/_12ssp"/>


    </LinearLayout>

    <LinearLayout
        android:id="@+id/lltGold"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:orientation="horizontal"
        android:layout_below="@+id/lltSilver"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:gravity="center"
        android:layout_margin="4dp"
        android:background="@drawable/bg_rounded">
        <TextView

            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Eight"
            android:textColor="@color/white"
            android:gravity="left"

            android:textSize="@dimen/_12ssp"/>

        <TextView

            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Gold"
            android:layout_gravity="center_vertical"
            android:gravity="left"
            android:ellipsize="end"
            android:maxEms="10"
            android:maxLines="1"
            android:textColor="@color/white"
            android:layout_marginLeft="@dimen/_10sdp"
            android:textSize="@dimen/_12ssp"/>


    </LinearLayout>


    <Button
        android:id="@+id/btncancel"
        android:layout_width="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_height="wrap_content"
        android:layout_below="@+id/lltGold"
        android:text="Cancel"
        android:gravity="center"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="15dp"
        android:background="@color/primary_bg_red"
        android:textColor="@color/white"
        />


</RelativeLayout>