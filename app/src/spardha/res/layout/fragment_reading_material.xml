<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rlRoot"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_bg_dark"
    tools:context="com.wonderslate.prepjoy.ui.reading_material.ReadingMaterialFragment">

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/instruction_guide"
        android:layout_width="270dp"
        android:layout_height="240dp"
        android:visibility="visible"
        android:layout_centerInParent="true"
        app:lottie_autoPlay="true"
        android:tint="@color/primary_bg_red"
        app:lottie_loop="true"
        android:layout_marginTop="100dp"
        app:lottie_rawRes="@raw/swipe_up"
      />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/rl_footer"
        android:layout_alignParentTop="true"
        android:orientation="vertical"
        tools:ignore="UseCompoundDrawables">

        <ImageView
            android:id="@+id/imageReadingMaterial"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_180sdp"
            android:scaleType="fitXY"
            android:contentDescription="@string/current_affairs_image" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp">


            <TextView
                android:id="@+id/textTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="@string/title"
                android:textColor="@color/white"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/textDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@string/description"
                android:textColor="@color/white"
                android:textSize="@dimen/_11ssp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">


                <TextView
                    android:id="@+id/txtReadnews"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text="To read full news "
                    android:textColor="@color/light_gray"
                    android:textSize="@dimen/_9ssp" />

                <TextView
                    android:id="@+id/textRefLink"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:focusable="true"
                    android:text="click here"
                    android:textColor="@color/primary_bg_red"
                    android:textColorLink="@color/primary_bg_red"
                    android:textSize="@dimen/_9ssp" />


            </LinearLayout>

        </LinearLayout>


    </LinearLayout>

    <RelativeLayout
        android:id="@+id/rl_footer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="20dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="10dp">

        <RelativeLayout
            android:id="@+id/rlDatePicker"
            android:layout_width="150dp"
            android:layout_height="35dp"
            android:layout_centerHorizontal="true"
            android:background="@drawable/item_rounded_corner"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/imagePreviousDate"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_centerVertical="true"
                android:layout_marginStart="8dp"
                android:rotation="180"
                app:srcCompat="@drawable/ic_arrow"
                tools:ignore="ContentDescription" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/imageNextDate"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="8dp"
                app:srcCompat="@drawable/ic_arrow"
                tools:ignore="ContentDescription" />

            <TextView
                android:id="@+id/textDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text=""
                android:textColor="@color/white"
                android:textSize="12sp"
                tools:ignore="RelativeOverlap" />

        </RelativeLayout>


        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imageSave"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_centerVertical="true"
            android:contentDescription="@string/save"
            android:visibility="gone"
            app:srcCompat="@drawable/ic_not_saved" />


        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imageWhatsAppShare"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignLeft="@+id/imageTextToSpeach"
            android:layout_centerVertical="true"
            android:layout_marginLeft="-30dp"
            android:contentDescription="@string/share_on_whatsapp"
            app:srcCompat="@drawable/ic_share" />




        <ToggleButton
            android:id="@+id/imageTextToSpeach"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            app:tint="@color/primary_bg_red"
            android:background="@drawable/tts_toggle"
            android:textOn=""
            android:textOff=""
            android:paddingTop="20dp"
            android:focusable="false"
            android:backgroundTint="@color/primary_bg_red"
            android:focusableInTouchMode="false" />

    </RelativeLayout>
</RelativeLayout>

