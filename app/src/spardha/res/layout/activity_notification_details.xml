<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_bg_dark"
    android:padding="16dp"
    android:paddingLeft="5dp"
    android:paddingRight="5dp"
    tools:context=".ui.notification.NotificationDetailsActivity">

    <include
        android:id="@+id/header"
        layout="@layout/header_language_change"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/header">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/notificationTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:autoLink="all"
                android:clickable="true"
                android:focusable="true"
                android:padding="15dp"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:typeface="normal"
                android:textStyle="bold"
                tools:text="@tools:sample/lorem" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/notificationImageVIew"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:layout_below="@id/notificationTitle"
                android:layout_marginTop="10dp"
                tools:srcCompat="@tools:sample/backgrounds/scenic[0]" />

            <TextView
                android:id="@+id/notificationTxt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/notificationImageVIew"
                android:autoLink="all"
                android:clickable="true"
                android:focusable="true"
                android:padding="15dp"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:typeface="normal"
                tools:text="@tools:sample/lorem" />

            <TextView
                android:id="@+id/notificationLinkTxt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/notificationTxt"
                android:autoLink="all"
                android:clickable="true"
                android:focusable="true"
                android:padding="15dp"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:typeface="normal"
                tools:text="@tools:sample/lorem" />

            <TextView
                android:id="@+id/notificationSentTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/notificationLinkTxt"
                android:layout_marginTop="5dp"
                android:maxLines="1"
                android:padding="15dp"
                android:textSize="12sp"
                android:textStyle="italic"
                android:textColor="@color/white"
                android:typeface="normal"
                tools:text="@tools:sample/date/mmddyy" />
        </RelativeLayout>

    </ScrollView>

    <com.wang.avi.AVLoadingIndicatorView
        android:id="@+id/notificationDetailsLoader"
        style="@style/AVLoadingIndicatorView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="visible"
        android:layout_centerInParent="true"
        app:indicatorColor="@color/primary_bg_red"
        app:indicatorName="BallBeatIndicator" />

</RelativeLayout>