{"v": "5.5.2", "fr": 29.9700012207031, "ip": 0, "op": 30.0000012219251, "w": 500, "h": 500, "nm": "Composição 1", "ddd": 0, "assets": [{"id": "image_0", "w": 170, "h": 124, "u": "", "p": "data:image/png;base64,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", "e": 1}], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "página 2", "parent": 4, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 7, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 9, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 20, "s": [100]}, {"t": 22.0000008960784, "s": [5]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [85, 62, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 8, "s": [36.743, 106.982, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 15, "s": [-21.035, 106.982, 100]}, {"t": 22.0000008960784, "s": [-77.498, 104.07, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.289, -67.898], [23.961, -67.568], [18.57, -66.644], [10.864, -64.389], [5.099, -63.482], [1.74, -62.839], [-1.074, -61.871], [-1.432, -33.5], [-2.432, 8], [-1.432, 28], [-0.932, 44], [9.461, 29.185], [19.444, 23.405], [29.746, 21.117], [42.837, 21.215], [53.929, 22.674], [64.036, 22.553], [65.944, 13.706], [66.459, -22.24], [67.15, -44.748], [66.642, -53.99], [66.729, -65.27], [58.151, -65.267], [51.466, -66.709], [43.484, -67.24], [37.716, -67.353]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 13, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[23.95, -69.398], [18.769, -68.281], [14.809, -67.16], [8.823, -65.299], [3.917, -64.195], [1.167, -62.937], [0.358, -61.871], [0, -33.5], [-1, 8], [0, 28], [0.5, 44], [10.034, 27.217], [15.433, 22.126], [22.583, 19.543], [31.95, 16.984], [37.885, 16.18], [46.274, 15.567], [53.912, 15.28], [54.999, -22.24], [55.977, -44.748], [56.042, -55.072], [55.843, -75.306], [47.838, -75.303], [40.83, -74.901], [33.421, -74.054], [27.08, -71.116]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 18, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[70.24, -60.912], [54.424, -58.196], [39.205, -55.722], [30.091, -54.23], [22.683, -52.757], [14.303, -51.13], [2.235, -48.957], [0, -33.5], [-1, 8], [0, 28], [0.5, 44], [18.166, 36.441], [38.578, 32.334], [51.984, 29.629], [59.474, 28.054], [78.545, 23.067], [98.819, 16.674], [122.095, 5.318], [122.557, -22.363], [121.658, -45.732], [123.6, -56.548], [122.15, -84.53], [111.017, -76.287], [103.383, -71.826], [96.6, -68.273], [84.004, -64.106]], "c": true}]}, {"t": 22.0000008960784, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[31.839, -60.42], [26.496, -59.672], [21.252, -59.412], [16.127, -57.674], [10.465, -55.955], [5.326, -54.574], [-0.259, -52.401], [-0.997, -33.746], [-1.997, 7.754], [-0.997, 27.754], [0.251, 44], [18.256, 38.655], [32.842, 36.516], [43.256, 36.516], [51.29, 35.925], [63.947, 36.35], [99.507, 42.748], [100.681, 16.879], [100.554, 2.973], [100.312, -14.738], [101.121, -52.858], [76.768, -58.702], [56.976, -60.544], [45.782, -60.511], [41.492, -60.402], [37.872, -60.908]], "c": true}]}], "ix": 2}, "nm": "Caminho 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Traçado 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.217878035003, 0.635987225701, 0.770481004902, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Preenchimento 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 101.622], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transformar"}], "nm": "Forma 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 7.00000028511585, "op": 23.0000009368092, "st": 7.00000028511585, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "p<PERSON><PERSON><PERSON>", "parent": 4, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 2, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 27, "s": [100]}, {"t": 29.0000011811942, "s": [5]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [85, 62, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [106.708, 106.982, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 5, "s": [54.076, 106.982, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 15, "s": [36.743, 106.982, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 24, "s": [-21.035, 106.982, 100]}, {"t": 30.0000012219251, "s": [-105.534, 106.982, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[36.5, -61.25], [31.625, -60.625], [25.875, -59.75], [14.625, -58.75], [8.5, -57], [3.25, -55.25], [-0.5, -54], [0, -33.5], [-1, 8], [0, 28], [0.5, 44], [8.5, 40.5], [23.5, 37.5], [38.553, 37.008], [54.5, 39], [69.5, 42.5], [72.875, 42.625], [72.875, 17.125], [72.875, -21.625], [72.625, -44.625], [72.875, -53.75], [68.5, -55.75], [64.125, -57.5], [57.375, -59.25], [49.25, -60.125], [43.625, -61]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[35.527, -59.098], [30.165, -58.949], [24.415, -58.074], [13.165, -57.074], [7.04, -55.324], [1.79, -53.574], [-2.447, -49.864], [0, -33.5], [-1, 8], [0, 28], [0.5, 44], [12.88, 37.917], [24.96, 32.457], [38.31, 29.014], [57.906, 29.899], [71.447, 31.308], [91.854, 32.294], [93.314, 15.526], [95.261, -22.978], [96.714, -44.748], [96.477, -52.397], [75.07, -55.796], [64.125, -56.685], [58.835, -58.681], [50.953, -59.556], [44.842, -59.263]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 10, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[33.664, -67.523], [27.05, -67.39], [21.3, -66.515], [13.165, -64.162], [7.185, -63.303], [3.674, -62.814], [0.358, -61.871], [0, -33.5], [-1, 8], [0, 28], [0.5, 44], [11.108, 29.677], [22.237, 23.725], [33.327, 21.511], [47.349, 22.273], [59.73, 24.297], [70.267, 24.299], [70.743, 13.312], [71.114, -22.24], [71.733, -44.748], [71.083, -53.719], [71.242, -62.761], [62.52, -62.758], [55.915, -64.662], [47.79, -65.537], [42.165, -66.412]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[23.95, -69.398], [18.769, -68.281], [14.809, -67.16], [8.823, -65.299], [3.917, -64.195], [1.167, -62.937], [0.358, -61.871], [0, -33.5], [-1, 8], [0, 28], [0.5, 44], [10.034, 27.217], [15.433, 22.126], [22.583, 19.543], [31.95, 16.984], [37.885, 16.18], [46.274, 15.567], [53.912, 15.28], [54.999, -22.24], [55.977, -44.748], [56.042, -55.072], [55.843, -75.306], [47.838, -75.303], [40.83, -74.901], [33.421, -74.054], [27.08, -71.116]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 24, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[70.24, -60.912], [54.424, -58.196], [39.205, -55.722], [30.091, -54.23], [22.683, -52.757], [14.303, -51.13], [2.235, -48.957], [0, -33.5], [-1, 8], [0, 28], [0.5, 44], [18.166, 36.441], [38.578, 32.334], [51.984, 29.629], [59.474, 28.054], [78.545, 23.067], [98.819, 16.674], [122.095, 5.318], [122.557, -22.363], [121.658, -45.732], [123.6, -56.548], [122.15, -84.53], [111.017, -76.287], [103.383, -71.826], [96.6, -68.273], [84.004, -64.106]], "c": true}]}, {"t": 30.0000012219251, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[31.839, -60.42], [26.496, -59.672], [21.252, -59.412], [16.127, -57.674], [10.465, -55.955], [5.326, -54.574], [-0.259, -52.401], [-0.997, -33.746], [-1.997, 7.754], [-0.997, 27.754], [0.251, 44], [17.916, 37.917], [32.842, 36.516], [43.256, 36.516], [52.99, 37.647], [62.586, 40.532], [72.636, 42.502], [73.47, 17.125], [73.683, 2.481], [73.781, -16.952], [73.23, -42.035], [73.026, -53.29], [50.173, -59.068], [45.782, -60.511], [41.492, -60.402], [37.872, -60.908]], "c": true}]}], "ix": 2}, "nm": "Caminho 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Traçado 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.96862745285, 0.964705884457, 0.933333337307, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Preenchimento 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transformar"}], "nm": "Forma 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 30.0000012219251, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "página 3", "parent": 4, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 9, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 11, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 19, "s": [100]}, {"t": 21.0000008553475, "s": [5]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [85, 62, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 11, "s": [-21.035, 106.982, 100]}, {"t": 19.0000007738859, "s": [-89.972, 105.479, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 10, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[70.24, -60.912], [54.424, -58.196], [39.205, -55.722], [30.091, -54.23], [22.683, -52.757], [14.303, -51.13], [2.235, -48.957], [0, -33.5], [-1, 8], [0, 28], [0.5, 44], [18.166, 36.441], [38.578, 32.334], [51.984, 29.629], [59.474, 28.054], [78.545, 23.067], [98.819, 16.674], [122.095, 5.318], [122.557, -22.363], [121.658, -45.732], [123.6, -56.548], [122.15, -84.53], [111.017, -76.287], [103.383, -71.826], [96.6, -68.273], [84.004, -64.106]], "c": true}]}, {"t": 19.0000007738859, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[36.105, -60.474], [29.599, -59.508], [23.246, -59.002], [17.679, -57.291], [11.822, -55.599], [6.324, -54.191], [0.018, -52.018], [-0.887, -33.719], [-1.887, 7.781], [-0.887, 27.781], [0.278, 44], [17.944, 37.753], [33.48, 36.051], [44.226, 35.751], [53.711, 36.581], [64.359, 38.592], [86.673, 41.737], [88.077, 16.797], [88.317, -1.509], [87.711, -21.134], [87.436, -43.648], [87.095, -53.072], [56.934, -60.981], [52.182, -61.768], [47.615, -61.276], [42.998, -61.263]], "c": true}]}], "ix": 2}, "nm": "Caminho 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Traçado 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.895818014706, 0.43845205868, 0.257465377508, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Preenchimento 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transformar"}], "nm": "Forma 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 10.0000004073083, "op": 22.0000008960784, "st": 9.00000036657752, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 2, "nm": "open-book.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 6, "s": [5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 14, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24, "s": [-5]}, {"t": 29.0000011811942, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [250, 243, 0], "ix": 2}, "a": {"a": 0, "k": [85, 62, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 6, "s": [83, 83, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 14, "s": [110, 110, 100]}, {"t": 24.00000097754, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "ip": 0, "op": 30.0000012219251, "st": 0, "bm": 0}], "markers": []}