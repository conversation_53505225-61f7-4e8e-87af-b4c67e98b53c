<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/item_home_options_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:gravity="center"
    android:orientation="vertical"
    xmlns:tools="http://schemas.android.com/tools">

    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/item_home_options_new_shimmer"
        android:layout_gravity="end"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/item_home_options_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:background="@drawable/bg_home_book_type"
            android:paddingHorizontal="8dp"
            android:paddingVertical="5dp"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:layout_marginBottom="5dp"
            android:layout_marginEnd="-5dp"
            android:text="New   " />
    </com.facebook.shimmer.ShimmerFrameLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/home_card_bg"
        android:orientation="vertical"
        android:padding="13dp">
        <ImageView
            android:id="@+id/item_home_options_image"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:scaleType="centerCrop"
            android:visibility="gone"/>
    </LinearLayout>

    <TextView
        android:id="@+id/item_home_options_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text=""
        android:maxLines="2"
        android:layout_marginTop="5dp"
        android:textColor="@color/white"
        android:textSize="11sp"
        android:fontFamily="@font/font_thin"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"
        tools:text="@string/home_current_affairs"/>

    <TextView
        android:id="@+id/item_home_options_sub_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingStart="6dp"
        android:paddingEnd="2dp"
        android:text=""
        android:layout_marginTop="@dimen/_5sdp"
        android:textColor="@color/primary_bg_red"
        android:textSize="@dimen/_8ssp"
        tools:text="@string/home_current_affairs_sub_title"
        android:visibility="gone"/>

</LinearLayout>