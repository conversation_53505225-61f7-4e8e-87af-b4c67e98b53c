<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_bg_dark"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/primary_bg_dark"
        android:orientation="vertical">


        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipeToRefresh"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/ebook_search_layout">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/primary_bg_dark"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8sdp"
                    android:layout_marginBottom="@dimen/_8sdp"
                    android:orientation="horizontal"
                    android:visibility="visible"
                    android:weightSum="2">


                    <androidx.cardview.widget.CardView
                        android:id="@+id/dashboard_current_affairs"
                        android:layout_width="130dp"
                        android:layout_height="130dp"
                        android:layout_gravity="center"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/purple_700"
                        app:cardCornerRadius="@dimen/_10sdp">


                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:text="Current Affairs"
                                android:textColor="@color/white"
                                android:textSize="@dimen/_16ssp" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:paddingStart="6dp"
                                android:paddingEnd="2dp"
                                android:text="Watch it, Read it,\nPlay it"
                                android:textColor="@color/primary_bg_red"
                                android:textSize="@dimen/_8sdp" />

                        </LinearLayout>


                    </androidx.cardview.widget.CardView>


                    <androidx.cardview.widget.CardView
                        android:id="@+id/dashboard_daily_tests"
                        android:layout_width="130dp"
                        android:layout_height="130dp"
                        android:layout_gravity="center"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="20dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/purple_700"
                        app:cardCornerRadius="@dimen/_10sdp">


                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:text="Daily Tests"
                                android:textColor="@color/white"
                                android:textSize="@dimen/_16ssp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:paddingStart="6dp"
                                android:paddingEnd="2dp"
                                android:text="@string/daily_test_subtitle"
                                android:textColor="@color/primary_bg_red"
                                android:textSize="@dimen/_8sdp" />

                        </LinearLayout>


                    </androidx.cardview.widget.CardView>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/_8sdp"
                    android:orientation="horizontal"
                    android:visibility="visible">


                    <androidx.cardview.widget.CardView
                        android:id="@+id/dashboard_ebooks"
                        android:layout_width="130dp"
                        android:layout_height="130dp"
                        android:layout_gravity="center"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/purple_700"
                        app:cardCornerRadius="@dimen/_10sdp">


                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:text="books"
                                android:textColor="@color/white"
                                android:textSize="@dimen/_16ssp" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:paddingStart="6dp"
                                android:paddingEnd="2dp"
                                android:text="Smart Game\books"
                                android:textColor="@color/primary_bg_red"
                                android:textSize="@dimen/_8sdp" />

                        </LinearLayout>


                    </androidx.cardview.widget.CardView>


                    <androidx.cardview.widget.CardView
                        android:id="@+id/dashboard_cafe"
                        android:layout_width="130dp"
                        android:layout_height="130dp"
                        android:layout_gravity="center"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="20dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/purple_700"
                        app:cardCornerRadius="@dimen/_10sdp">


                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:text="Cafe"
                                android:textColor="@color/white"
                                android:textSize="@dimen/_16ssp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:paddingStart="6dp"
                                android:paddingEnd="2dp"
                                android:text="News &amp; Blogs"
                                android:textColor="@color/primary_bg_red"
                                android:textSize="@dimen/_8sdp" />

                        </LinearLayout>


                    </androidx.cardview.widget.CardView>

                </LinearLayout>


                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:text="Online Tests"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_15sdp" />


                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">


                    <RelativeLayout
                        android:id="@+id/layout_top"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="3dp"
                        android:animateLayoutChanges="true"
                        android:clickable="true"
                        android:focusable="true"
                        android:focusableInTouchMode="true"
                        android:orientation="vertical">


                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recycleBooks"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"

                            android:background="@color/colorShopBg"

                            android:clipToPadding="false"
                            android:nestedScrollingEnabled="false"
                            android:paddingStart="12dp"
                            android:paddingEnd="12dp"
                            android:paddingBottom="70dp"
                            android:scrollbars="none"
                            tools:listitem="@layout/item_ebook_info_new" />


                        <ProgressBar
                            android:id="@+id/idPBLoading"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/recycleBooks"
                            android:visibility="gone" />

                        <com.facebook.shimmer.ShimmerFrameLayout
                            android:id="@+id/shimmerBooks"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:padding="4dp"
                            android:visibility="visible"
                            tools:visibility="gone">

                            <include
                                layout="@layout/shimmer_empty_book_layout"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content" />
                        </com.facebook.shimmer.ShimmerFrameLayout>

                    </RelativeLayout>

                </RelativeLayout>


                <LinearLayout
                    android:id="@+id/noDatalayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <com.airbnb.lottie.LottieAnimationView
                        android:id="@+id/lottieEmpty"
                        android:layout_width="150dp"
                        android:layout_height="150dp"
                        android:layout_centerHorizontal="true"
                        app:lottie_autoPlay="true"
                        app:lottie_loop="true"
                        app:lottie_rawRes="@raw/empty_lottie" />

                    <com.ws.core_ui.custom_views.WSTextView
                        android:id="@+id/emptytextview"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:padding="3dp"
                        android:text="Books not available now,\nPlease check your internet connection."
                        android:textColor="@color/colorShopErrorText"
                        android:textSize="12sp"
                        android:typeface="normal" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/linearNoData"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <com.airbnb.lottie.LottieAnimationView
                        android:layout_width="150dp"
                        android:layout_height="150dp"
                        android:layout_centerHorizontal="true"
                        app:lottie_autoPlay="true"
                        app:lottie_loop="true"
                        app:lottie_rawRes="@raw/empty_lottie" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="No books found."
                        android:textColor="@color/colorShopErrorText"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="We can't find any books matching your preference."
                        android:textColor="@color/colorShopErrorTextSecondary"
                        android:textSize="12sp" />
                </LinearLayout>


                <com.wang.avi.AVLoadingIndicatorView
                    android:id="@+id/lvCenter"
                    style="@style/AVLoadingIndicatorView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:visibility="gone"
                    app:indicatorColor="@color/colorAccent"
                    app:indicatorName="BallSpinFadeLoaderIndicator" />


                <TextView
                    android:id="@+id/txtshowMore"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="Show more"
                    android:textColor="@color/primary_bg_red"
                    android:textStyle="bold" />

            </LinearLayout>

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
    </LinearLayout>


</LinearLayout>