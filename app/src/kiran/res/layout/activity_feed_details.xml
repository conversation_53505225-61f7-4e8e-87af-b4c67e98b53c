<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.wonderslate.prepjoy.news.FeedDetailsWebViewActivity"
    android:background="@color/cardview_shadow_end_color">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/feed_detail_toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary" />

    <TextView
        android:id="@+id/feed_details_page_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/feed_detail_toolbar"
        android:layout_marginTop="5dp"
        android:textSize="20sp"
        android:textColor="@color/white"
        android:padding="15dp"
        android:visibility="gone"/>

    <WebView
        android:id="@+id/feed_details_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/feed_details_page_header"/>

</RelativeLayout>