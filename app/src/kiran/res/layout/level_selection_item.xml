<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/primary_bg_dark"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/dashboard_current_affairs"

        android:layout_width="140dp"
        android:layout_height="140dp"
        android:layout_gravity="center"
        android:layout_marginStart="20dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="10dp"
        android:background="@drawable/home_card_bg">


        <ImageView
            android:id="@+id/imgIcon"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="20dp"
            android:src="@drawable/user_college"/>


        <TextView
            android:id="@+id/txtValues"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:layout_marginStart="5dp"
            android:maxLines="2"
            android:gravity="center"
            android:layout_below="@+id/imgIcon"
            android:text="@string/home_current_affairs"
            android:textColor="@color/white"
            android:textSize="@dimen/_8ssp" />

        <ImageView
            android:id="@+id/chkBox"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:layout_alignParentBottom="true"
            android:layout_alignParentEnd="true"
           android:layout_marginEnd="10dp"
            android:layout_marginBottom="10dp"
            android:background="@drawable/ic_action_checked_icon"
            android:backgroundTint="@color/white"
            android:visibility="gone" />

    </RelativeLayout>

</RelativeLayout>