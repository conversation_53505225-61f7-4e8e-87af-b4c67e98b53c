<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/bottom_sheet"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#fff"
    android:visibility="visible"
    app:behavior_hideable="true"
    app:behavior_peekHeight="0dp"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

    <LinearLayout
        android:id="@+id/layout_back"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentTop="true"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@color/primary_bg_red">

            <ImageView
                android:id="@+id/back_webView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="10dp"
                android:src="@drawable/ic_close_red"
                app:tint="@color/white" />

            <TextView
                android:id="@+id/textView_close_webview"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="5dp"
                android:layout_toRightOf="@+id/back_webView"
                android:fontFamily="@font/poppins_bold"
                android:paddingTop="3dp"
                android:text="Close"
                android:textColor="@color/white"
                android:textSize="14sp" />

            <RelativeLayout
                android:id="@+id/relativeLayout_avatar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_gravity="center"
                android:layout_marginRight="10dp">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/avatar_bg_circle"
                    android:gravity="center">

                    <androidx.cardview.widget.CardView
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_centerHorizontal="true"
                        android:elevation="12dp"
                        app:cardCornerRadius="60dp">

                        <ImageView
                            android:id="@+id/imageView_avatar_webView"
                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:background="@drawable/trending_gradient_shape"
                            android:scaleType="centerCrop"
                            app:srcCompat="@drawable/profile_icon" />
                    </androidx.cardview.widget.CardView>
                </RelativeLayout>
            </RelativeLayout>

            <TextView
                android:id="@+id/textview_user_webView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginRight="5dp"
                android:layout_toLeftOf="@+id/relativeLayout_avatar"
                android:fontFamily="@font/poppins_medium"
                android:text="User name"
                android:textColor="@color/white"
                android:textSize="10sp" />
        </RelativeLayout>

        <WebView
            android:id="@+id/webView_group_res_open"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="10dp"
            android:scrollbarStyle="insideInset"
            android:scrollbars="vertical" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/nohistorylayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <ImageView
            android:id="@+id/emptyImage"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_centerInParent="true"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:src="@drawable/empty_data" />

        <com.wonderslate.prepjoy.Utils.CustomViews.WSTextView
            android:id="@+id/emptytextview"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/emptyImage"
            android:layout_alignBaseline="@id/emptyImage"
            android:layout_alignParentBottom="true"
            android:layout_centerInParent="true"
            android:layout_centerHorizontal="true"
            android:gravity="center"
            android:padding="3dp"
            android:text="No Data Available, Please try again."
            android:textColor="@color/black"
            android:textSize="12sp"
            android:typeface="normal" />

    </LinearLayout>
</RelativeLayout>