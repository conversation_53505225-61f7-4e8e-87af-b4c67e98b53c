<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingLeft="5dp"
    android:paddingRight="5dp"
    android:background="@color/primary_bg_dark"
    tools:context=".ui.videos.VideoActivity">



    <include
        android:id="@+id/header"
        layout="@layout/header_language_change"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <com.wang.avi.AVLoadingIndicatorView
        android:id="@+id/progressLoader"
        style="@style/AVLoadingIndicatorView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        app:indicatorColor="@color/primary_bg_red"
        app:indicatorName="LineScalePulseOutRapidIndicator"
        android:elevation="10dp"
        android:visibility="visible"/>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycleVideo"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/header"
        android:layout_marginTop="15dp"
        android:background="@color/primary_bg_dark" />


    <LinearLayout
        android:id="@+id/linearNoData"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:orientation="vertical"
        android:visibility="gone">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/empty_ready_material"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="visible"
            app:lottie_autoPlay="true"
            android:tint="@color/primary_bg_red"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/empty_ready_material"
            android:layout_gravity="center"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_gravity="center"
            android:layout_marginTop="8dp"
            android:text="@string/no_data_available_video"
            android:textColor="@color/white"
            android:textSize="16sp"/>
    </LinearLayout>

    <include
        layout="@layout/no_internet_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"

        android:layout_alignParentBottom="true"
        android:layout_above="@id/relativeLayout"
        android:layout_marginBottom="5dp" />

</RelativeLayout>