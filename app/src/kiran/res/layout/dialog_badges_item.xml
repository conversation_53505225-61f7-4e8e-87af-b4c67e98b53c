<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="35dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal"
    app:layout_constraintTop_toBottomOf="@+id/tabLayout"
    android:paddingLeft="12dp"
    android:paddingRight="12dp"
    android:gravity="center"
    android:layout_margin="4dp"
    android:background="@drawable/bg_rounded">
    <TextView
        android:id="@+id/textPoints"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="@string/rank"
        android:textColor="@color/white"
        android:gravity="left"
        android:layout_marginStart="@dimen/_5sdp"
        android:textSize="@dimen/_12ssp"/>

    <TextView
        android:id="@+id/textBadgeName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="@string/rank"
        android:layout_gravity="center_vertical"
        android:gravity="left"
        android:ellipsize="end"
        android:maxEms="10"
        android:maxLines="1"
        android:layout_marginLeft="@dimen/_5sdp"
        android:textColor="@color/white"
        android:textSize="@dimen/_12ssp"/>


</LinearLayout>