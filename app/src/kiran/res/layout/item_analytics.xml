<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginBottom="5dp"
    android:layout_width="match_parent"
    android:layout_height="@dimen/_60sdp"
    app:cardCornerRadius="@dimen/_13sdp"
    android:layout_marginTop="5dp"
    app:cardBackgroundColor="@color/purple_700"
    android:id="@+id/item_daily_test_container">

    <LinearLayout
        android:id="@+id/lltLastQuiz"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:weightSum="3.5"
        android:visibility="visible"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/txtQuiztitle"
            android:layout_width="70dp"
            android:layout_height="wrap_content"
            android:text="Last Quiz"
            android:gravity="center"
            android:layout_weight="1"
            android:layout_gravity="center"
            android:textSize="@dimen/_10sdp"
            android:layout_marginEnd="@dimen/_20sdp"
            android:layout_marginStart="@dimen/_20sdp"
            android:textColor="@color/white"
            />
        <View
            android:layout_height="20dp"
            android:layout_gravity="center"
            style="@style/Divider.Vertical"/>

        <TextView
            android:id="@+id/txtScore"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_gravity="center"
            android:text="Last Quiz"
            android:layout_weight="1"
            android:textSize="@dimen/_10sdp"
            android:layout_marginEnd="@dimen/_20sdp"
            android:layout_marginStart="@dimen/_20sdp"
            android:textColor="@color/white"
            />

        <View
            android:layout_height="20dp"
            android:layout_gravity="center"
            style="@style/Divider.Vertical"/>
        <TextView
            android:id="@+id/txtQuizStatus"
            android:layout_width="70dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_weight="1"
            android:layout_gravity="center"
            android:text="Last Quiz"
            android:textSize="@dimen/_10sdp"
            android:layout_marginEnd="@dimen/_20sdp"
            android:layout_marginStart="@dimen/_20sdp"
            android:textColor="@color/white"
            />
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:src="@drawable/ic_arrow_right_small"
            />

    </LinearLayout>

</androidx.cardview.widget.CardView>