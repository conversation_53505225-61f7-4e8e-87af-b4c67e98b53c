<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"

    android:background="@color/primary_bg_dark"
    tools:context=".ui.quiz_instruction.QuizInstructions">

    <RelativeLayout
        android:id="@+id/quizInstructionHolder"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/header"
        android:visibility="gone">
        <com.wang.avi.AVLoadingIndicatorView
            android:id="@+id/progressLoader"
            style="@style/AVLoadingIndicatorView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_centerInParent="true"
            android:gravity="center"
            app:indicatorColor="@color/primary_bg_red"
            app:indicatorName="LineScalePulseOutRapidIndicator"
            android:elevation="10dp"
            android:visibility="gone"/>

        <include
            android:id="@+id/header"
            layout="@layout/header_language_change"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/background_image"
            android:layout_width="270dp"
            android:layout_height="240dp"
            android:visibility="gone"
            android:layout_centerVertical="true"
            android:layout_centerHorizontal="true"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/quiz_instruction"
            />

        <TableLayout
            android:id="@+id/tableInstuctions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/header"
            android:layout_margin="20dp"
            android:layout_marginTop="20dp"
            >

            <TableRow
                android:padding="10dp">

                <TextView
                    android:layout_column="2"
                    android:layout_width="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="20dp"
                    android:textStyle="bold"
                    android:text="Settings" />
            </TableRow>

            <TableRow
                android:visibility="gone"
                android:padding="10dp">

                <TextView
                    android:layout_column="1"
                    android:textColor="@color/white"
                    android:text="• "
                    android:textSize="18dp"/>

                <TextView
                    android:layout_column="2"
                    android:layout_width="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="18dp"
                    android:text="If the users that the question intended for passes it." />
            </TableRow>
        </TableLayout>

        <RelativeLayout
            android:id="@+id/controlSettings"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tableInstuctions"
            android:gravity="center"

            >

            <LinearLayout
                android:id="@+id/spinnerLanag"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:orientation="vertical"
                android:weightSum="2">

                <TextView
                    android:id="@+id/spinner_language_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/spinnerLanag"
                    android:layout_gravity="left"
                    android:layout_marginStart="@dimen/_2sdp"
                    android:layout_weight="1"
                    android:text="Language"
                    android:textColor="@color/gray"
                    android:textSize="@dimen/_11ssp" />


                <androidx.appcompat.widget.AppCompatSpinner
                    android:id="@+id/spinnerlanguage"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_weight="2"
                    android:background="@drawable/ic_round_card_lang_bg"
                    android:gravity="center"
                    android:textColor="@color/primary_bg_red" />
            </LinearLayout>

            <TextView
                android:id="@+id/txttimeout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/spinnerLanag"
                android:layout_gravity="left"
                android:layout_marginLeft="22dp"
                android:layout_marginTop="20dp"
                android:layout_marginRight="20dp"
                android:layout_weight="1"
                android:gravity="left"
                android:text="Timeout(Seconds)"
                android:textColor="@color/gray"
                android:textSize="@dimen/_11ssp" />

            <LinearLayout
                android:id="@+id/lltspinnertime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/txttimeout"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="20dp"
                android:background="@drawable/ic_round_card_lang_bg"
                android:orientation="horizontal"
                android:weightSum="2">


                <androidx.appcompat.widget.AppCompatSpinner
                    android:id="@+id/spinnertime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_weight="2"
                    android:background="@drawable/ic_round_card_lang_bg"
                    android:gravity="center"
                    android:textColor="@color/primary_bg_red" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/lltsoundControl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/lltspinnertime"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="20dp"
                android:layout_marginRight="20dp"
                android:orientation="horizontal"
                android:weightSum="2">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="8dp"
                    android:layout_weight="1"
                    android:gravity="left"
                    android:text="Sound"
                    android:textColor="@color/gray"
                    android:textSize="@dimen/_11ssp" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switchgamesound"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_weight="1"
                    android:checked="true"
                    android:gravity="center"
                    android:textColor="@color/primary_bg_red"
                    android:theme="@style/Theme.PreopJoy" />
            </LinearLayout>

            <TableLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/lltsoundControl"
                android:layout_marginStart="@dimen/_10sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_marginEnd="@dimen/_10sdp"
                android:layout_marginBottom="@dimen/_10sdp">

                <TableRow android:padding="10dp">

                    <TextView
                        android:layout_column="1"
                        android:text="• "
                        android:textColor="@color/white"
                        android:textSize="18dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_column="2"
                        android:text="@string/quiz_instructions_2"
                        android:textColor="@color/white"
                        android:textSize="16dp" />
                </TableRow>
            </TableLayout>
        </RelativeLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginEnd="@dimen/_10sdp"
            android:layout_marginTop="@dimen/_50sdp"
            android:layout_below="@id/controlSettings">

            <Button
                android:layout_width="170dp"
                android:layout_height="wrap_content"
                android:text="@string/btn_challenge"
                android:textColor="@color/primary_bg_red"
                android:id="@+id/btnChallengeFriend"
                android:paddingStart="@dimen/_10sdp"
                android:paddingEnd="@dimen/_10sdp"
                android:maxWidth="170dp"
                android:maxHeight="45dp"
                android:background="@drawable/bg_button_round"
                android:visibility="gone"/>

            <Button
                android:layout_width="170dp"
                android:layout_height="wrap_content"
                android:text="@string/btn_challenge"
                android:paddingStart="@dimen/_10sdp"
                android:layout_marginTop="@dimen/_5sdp"
                android:maxWidth="170dp"
                android:maxHeight="45dp"
                android:paddingEnd="@dimen/_10sdp"
                android:textColor="@color/primary_bg_red"
                android:id="@+id/btnChallenge"
                android:background="@drawable/bg_button_round"
                style="?android:attr/buttonBarButtonStyle" />

            <Button
                android:layout_width="170dp"
                android:layout_height="wrap_content"
                android:text="@string/btn_start_game"
                android:maxWidth="170dp"
                android:maxHeight="40dp"
                android:paddingStart="@dimen/_10sdp"
                android:paddingEnd="@dimen/_10sdp"
                android:layout_marginTop="@dimen/_5sdp"
                android:background="@drawable/bg_button_round"
                android:textColor="@color/primary_bg_red"
                android:id="@+id/btnStart"
                style="?android:attr/buttonBarButtonStyle" />

        </LinearLayout>
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true">




        </FrameLayout>
    </RelativeLayout>

    <include
        layout="@layout/no_internet_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"

        android:layout_alignParentBottom="true"
        android:layout_above="@id/relativeLayout"
        android:layout_marginBottom="50dp" />





</RelativeLayout>