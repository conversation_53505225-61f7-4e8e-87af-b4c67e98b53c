<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_marginStart="15dp"
    android:layout_marginEnd="5dp"
    android:layout_marginTop="10dp"
    android:layout_marginBottom="13dp"
    android:layout_height="40dp"
    android:elevation="5dp">

    <RelativeLayout
        android:id="@+id/code_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shadow_lite_curved_layout_bg"
        android:layout_marginEnd="15dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <Button
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:ems="2"
            android:layout_marginEnd="10dp"
            android:layout_marginStart="5dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:padding="@dimen/_5sdp"
            android:textColor="@color/white"
            android:background="@drawable/info_icon_bg"
            android:id="@+id/infojoinGame"
            android:elevation="5dp"/>

        <Button
            android:id="@+id/joinGame"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:background="@drawable/button_right_corner_full_radius"
            android:gravity="center"
            android:layout_centerVertical="true"
            android:text="@string/home_join_game"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="15sp"
            android:fontFamily="@font/poppins_medium" />

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/home_game_code"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_toStartOf="@id/joinGame"
            android:layout_toEndOf="@id/infojoinGame"
            android:layout_centerVertical="true"
            android:gravity="center_vertical"
            android:inputType="textShortMessage"
            android:hint="@string/home_enter_code"
            android:textColorHint="@color/gray"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:fontFamily="@font/font_thin"
            android:elevation="@dimen/_3sdp"
            android:background="@null" />
    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>