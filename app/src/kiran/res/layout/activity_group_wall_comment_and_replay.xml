<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_bg_dark"
    tools:context=".ui.groupwall.GroupWallCommentAndReplayActivity">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include
            android:id="@+id/header"
            layout="@layout/header_language_change"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <androidx.core.widget.NestedScrollView
            android:id="@+id/group_wall_comment_reply_nested_scroll"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/header"
            android:fillViewport="true"
            android:animateLayoutChanges="true">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:id="@+id/layout_user"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="15dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="10dp"
                    android:orientation="horizontal">

                    <RelativeLayout
                        android:id="@+id/relativeLayout_avatar"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_gravity="center">

                        <RelativeLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerHorizontal="true"
                            android:background="@drawable/avatar_bg_circle"
                            android:gravity="center">

                            <androidx.cardview.widget.CardView
                                android:layout_width="30dp"
                                android:layout_height="30dp"
                                android:layout_centerHorizontal="true"
                                android:elevation="25dp"
                                app:cardCornerRadius="60dp">

                                <ImageView
                                    android:id="@+id/imageView_avatar"
                                    android:layout_width="30dp"
                                    android:layout_height="30dp"
                                    android:scaleType="centerCrop"
                                    app:srcCompat="@drawable/profile_icon" />
                            </androidx.cardview.widget.CardView>
                        </RelativeLayout>

                    </RelativeLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center|left"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/textview_user"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center|left"
                            android:layout_marginLeft="5dp"
                            android:fontFamily="@font/poppins_medium"
                            android:paddingTop="2dp"
                            android:textColor="@color/white"
                            android:textSize="12sp" />

                        <TextView
                            android:id="@+id/textview_date_time"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentTop="true"
                            android:layout_gravity="center"
                            android:layout_marginLeft="10dp"
                            android:layout_marginTop="1dp"
                            android:fontFamily="@font/poppins_light"
                            android:gravity="center"
                            android:paddingTop="2dp"
                            android:textColor="@color/light_white"
                            android:textSize="10sp" />

                    </LinearLayout>

                </LinearLayout>

                <TextView
                    android:id="@+id/textView_group_details_item"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/layout_user"
                    android:layout_marginLeft="25dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="10dp"
                    android:autoLink="web"
                    android:autoSizeMaxTextSize="14sp"
                    android:autoSizeMinTextSize="8sp"
                    android:ellipsize="end"
                    android:fontFamily="@font/poppins_medium"
                    android:linksClickable="true"
                    android:textColor="@color/white"
                    android:textColorLink="@color/video_title_color_land"
                    android:textSize="13sp" />


                <RelativeLayout
                    android:id="@+id/layout_attachment"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/textView_group_details_item">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/post_image_card"
                        android:layout_width="match_parent"
                        android:layout_height="320dp"
                        android:layout_marginLeft="20dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginRight="20dp"
                        android:layout_marginBottom="10dp"
                        android:clickable="true"
                        android:foreground="@drawable/ripple_dark"
                        android:visibility="gone"
                        app:cardCornerRadius="4dp"
                        app:cardElevation="2dp"
                        app:cardUseCompatPadding="true">

                        <ImageView
                            android:id="@+id/imageView_post_content"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:scaleType="fitXY"
                            android:visibility="gone" />
                    </androidx.cardview.widget.CardView>

                    <ImageView
                        android:id="@+id/imageView_post_file"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="30dp"
                        android:layout_marginTop="25dp"
                        android:layout_marginRight="10dp"
                        android:scaleType="fitXY"
                        android:src="@drawable/ic_file_upload"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/textView_file_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="5dp"
                        android:layout_toRightOf="@+id/imageView_post_file"
                        android:fontFamily="@font/poppins_medium"
                        android:paddingTop="3dp"
                        android:text="file"
                        android:textColor="@color/red"
                        android:textSize="12sp"
                        android:visibility="gone" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/layout_comment"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_below="@+id/layout_attachment"
                    android:layout_marginLeft="15dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="15dp"
                    android:layout_marginBottom="2dp"
                    android:background="@drawable/shadow_curved_layout_bg"
                    android:visibility="visible">

                    <EditText
                        android:id="@+id/editText_comment"
                        android:layout_width="match_parent"
                        android:layout_height="50dp"
                        android:layout_toLeftOf="@+id/imageView_send_comment"
                        android:background="@null"
                        android:fontFamily="@font/poppins_medium"
                        android:gravity="center|left"
                        android:hint="Write your comment here.."
                        android:paddingStart="15dp"
                        android:paddingTop="4dp"
                        android:textColor="@color/colorActionBarText"
                        android:textColorHint="@color/gray"
                        android:textSize="12sp"
                        android:inputType="textMultiLine|textImeMultiLine"
                        android:visibility="visible" />

                    <ImageView
                        android:id="@+id/imageView_send_comment"
                        android:layout_width="40dp"
                        android:layout_height="50dp"
                        android:layout_alignParentEnd="true"
                        android:background="@drawable/button_right_corner_full_radius"
                        android:padding="6dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/ic_send" />
                </RelativeLayout>

                <TextView
                    android:id="@+id/textView_comments"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/layout_comment"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="15dp"
                    android:fontFamily="@font/poppins_medium"
                    android:text="Comments"
                    android:textColor="@color/white"
                    android:textSize="10sp"
                    android:textStyle="italic"
                    android:visibility="gone" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView_groups_comments_and_replay"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/textView_comments"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="0dp"
                    android:layout_marginRight="10dp"
                    android:layout_marginBottom="0dp"
                    android:background="@drawable/shadow_curved_layout_bg"
                    android:paddingBottom="10dp"
                    android:visibility="visible" />

                <RelativeLayout
                    android:id="@+id/layout_action"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:background="@color/white"
                    android:padding="10dp"
                    android:visibility="gone">

                    <com.airbnb.lottie.LottieAnimationView
                        android:id="@+id/animationView_actions"
                        android:layout_width="150dp"
                        android:layout_height="150dp"
                        android:layout_centerInParent="true"
                        android:visibility="visible"
                        app:lottie_autoPlay="true"
                        app:lottie_loop="true"
                        app:lottie_rawRes="@raw/lottie_report" />

                    <TextView
                        android:id="@+id/textView_action"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/animationView_actions"
                        android:layout_centerHorizontal="true"
                        android:layout_gravity="center"
                        android:layout_marginTop="15dp"
                        android:layout_marginBottom="10dp"
                        android:fontFamily="@font/poppins_bold"
                        android:gravity="center"
                        android:text=""
                        android:textColor="@color/black"
                        android:textSize="14sp" />
                </RelativeLayout>

                <com.wang.avi.AVLoadingIndicatorView
                    android:id="@+id/groupLoader_details"
                    style="@style/AVLoadingIndicatorView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_gravity="center"
                    app:indicatorColor="@color/primary_bg_red"
                    app:indicatorName="BallBeatIndicator" />
            </RelativeLayout>
        </androidx.core.widget.NestedScrollView>
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>