<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:background="@color/primary_bg_dark"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/rrltBack"
        android:layout_width="wrap_content"
        android:gravity="center"
        android:layout_above="@id/recycleGrade"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgBack"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_centerInParent="true"
            android:padding="@dimen/_10sdp"
            app:srcCompat="@drawable/ic_back" />



    </RelativeLayout>


    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_toEndOf="@+id/rrltBack"
        android:gravity="center|left"
        android:padding="@dimen/_10sdp"
        android:text="Select Grade"
        android:textColor="@color/white"
        android:textSize="@dimen/_18sdp"
        android:textStyle="bold" />

    <Button
        android:id="@+id/BtnSave"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:padding="@dimen/_10sdp"
        android:textColor="@color/white"
        android:background="@color/primary_bg_dark"
        android:layout_alignEnd="@id/txtTitle"
        android:text="Save" />


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycleGrade"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/txtTitle"
        android:background="@color/primary_bg_dark"
        android:scrollbars="none"
        android:padding="@dimen/_10sdp" />

    <com.wang.avi.AVLoadingIndicatorView
        android:id="@+id/progressLoader"
        style="@style/AVLoadingIndicatorView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        app:indicatorColor="@color/primary_bg_red"
        app:indicatorName="LineScalePulseOutRapidIndicator"
        android:elevation="10dp"
        android:visibility="visible"/>


</RelativeLayout>