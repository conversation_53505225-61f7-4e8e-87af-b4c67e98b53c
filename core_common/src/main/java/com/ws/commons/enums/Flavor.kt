package com.ws.commons.enums

enum class Flavor(
    val variantString: String,
    val siteId: String
) {
    ARIHANT(
        "arihant",
        "3"
    ),
    LIBWONDER(
        "libWonder",
        "25"
    ),
    EBOOKS_ACCESS(
        "ebooksaccess",
        "26"
    ),
    PREPJOY(
        "current_affairs",
        "27"
    ),
    KARNATAKA(
        "karnataka",
        "30"
    ),
    ENGINEERING(
        "engineering",
        "31"
    ),
    NEET(
        "neet",
        "32"
    ),
    CTET(
        "ctet",
        "33"
    ),
    CA(
    "ca",
    "35"
    ),
    YCT(
            "yct",
            "52"
    ),
    PRABHAT(
            "prabhat",
            "57"
    ),
    KIRAN(
            "kiran",
            "71"
    ),
    SPARDHA(
            "spardha",
            "75"
    );

    companion object {

        @JvmStatic
        fun getFlavorFor(variant: String): Flavor {
            for (flavor in values()) {
                if (flavor.variantString == variant) {
                    return flavor
                }
            }

            throw IllegalArgumentException(
                "Cannot find any whitelabel associated with: $variant. Hint: If you have added a new flavor " +
                        "then also add it to ${Flavor::class.java.name} or if already added then check \"variantString\"."
            )
        }
    }
}