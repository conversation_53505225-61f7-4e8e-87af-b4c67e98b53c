package com.ws.commons.models

import java.io.Serializable

data class BookCoverUrlRequest(
    val fileName: String,
    val bookId: String,
    val type: String = "books",
    val imgType: String = "passport"
): Serializable

data class BookDetailsCoverUrlRequest(
        val fileName: String,
        val bookId: String,
        val type: String = "books",
        val imgType: String = "webp"
): Serializable

data class SmallBookCoverUrlRequest(
        val fileName: String,
        val bookId: String,
        val type: String = "books",
        val imgType: String = "thumbnail"
): Serializable