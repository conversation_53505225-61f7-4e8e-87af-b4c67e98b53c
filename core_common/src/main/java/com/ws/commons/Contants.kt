package com.ws.commons

object CoreApiEndpoints {
    const val BOOK_COVER_ENDPOINT = "funlearn/showProfileImage"
}

object CoreApiParams {
    const val PARAM_FILE_NAME = "fileName"
    const val PARAM_ID = "id"
    const val PARAM_TYPE = "type"
    const val PARAM_IMAGE_TYPE = "imgType"
}

object Templates {
    const val PAYMENT_AMOUNT_TEMPLATE = "₹ %s" //₹ <Amount>
}

object ErrorMessageConstants {
    const val updatingLibraryBooksError = "Problem while updating books. Please try again."
    const val errorExtractingChaptersZip = "Problem while processing chapters. Please try again."
    const val updatingChaptersError = "Problem while updating chapters. Please try again"
}