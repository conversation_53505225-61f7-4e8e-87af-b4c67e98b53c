package com.ws.purchase.data.repositories

import com.ws.commons.*
import com.ws.purchase.data.models.AddFreeBookResponse
import com.ws.purchase.data.models.BuyBookRequest
import com.ws.purchase.data.models.PurchaseBookResponse

interface PurchaseRepository {

    /**
     * Checks if book is bought or not
     */
    suspend fun isBookAlreadyPurchased(bookId: String): Result<Boolean>

    /**
     * Adds a free book to user's library
     */
    suspend fun addFreeBookToLibrary(bookId: String): Result<AddFreeBookResponse>

    /**
     * After payment is successful, this method is used for informing server that book is bought successfully
     */
    suspend fun purchaseBook(request: BuyBookRequest): Result<PurchaseBookResponse>

}