package com.ws.purchase.data.services

import com.ws.commons.Result
import com.ws.networking.NetworkRequest
import com.ws.networking.Networking
import com.ws.purchase.data.enums.BookType
import com.ws.purchase.data.enums.Currency
import com.ws.purchase.data.enums.PaymentPlatform
import com.ws.purchase.data.models.BuyBookRequest
import com.ws.purchase.data.network.PurchaseRequests

interface PurchaseService {

    /**
     * API call for checking if book is bought or not
     */
    suspend fun paymentCheck(bookId: String): Result<String>

    /**
     * API call for adding a free book to user's library
     */
    suspend fun addFreeToLibraryBook(bookId: String): Result<String>

    /**
     * After payment is successful, this API is used for informing server that book is bought successfully
     */
    suspend fun purchaseBook(buyRequest: BuyBookRequest): Result<String>

}