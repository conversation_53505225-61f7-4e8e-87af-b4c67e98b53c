package com.ws.purchase.data.models

import androidx.annotation.DrawableRes
import com.ws.purchase.data.enums.Currency
import java.io.Serializable

data class PaymentDetails(
    val bookName: String,
    val bookId: String,
    val coverImage: String,
    val author: String,
    val amount: String,
    val organization: String,
    @DrawableRes val logo: Int,
    val themeColor: String,
    val description: String = "",
    val currency: Currency = Currency.INR,
    val bookType: String = ""
): Serializable