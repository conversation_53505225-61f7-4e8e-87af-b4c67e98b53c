package com.ws.purchase.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ws.commons.Data
import com.ws.commons.Result
import com.ws.commons.Status
import com.ws.commons.exceptions.NoInternetException
import com.ws.purchase.data.models.AddFreeBookResponse
import com.ws.purchase.data.models.BuyBookRequest
import com.ws.purchase.data.models.PurchaseBookResponse
import com.ws.purchase.data.repositories.PurchaseRepository
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

class PaymentViewModel(
    private val repository: PurchaseRepository
): ViewModel() {

    private val _buyBookState: MutableStateFlow<Data<PurchaseBookResponse>> = MutableStateFlow(Data(Status.UNKNOWN))
    val buyBookState: StateFlow<Data<PurchaseBookResponse>> = _buyBookState

    private val _addFreeBookState: MutableStateFlow<Data<AddFreeBookResponse>> = MutableStateFlow(Data(Status.UNKNOWN))
    val addFreeBookState: StateFlow<Data<AddFreeBookResponse>> = _addFreeBookState

    fun buyBook(request: BuyBookRequest) = viewModelScope.launch {
        _buyBookState.emit(Data(Status.LOADING))
        when(val result = repository.purchaseBook(request)) {
            is Result.Success -> {

                _buyBookState.emit(Data(
                    responseType = Status.SUCCESSFUL,
                    data = result.data
                ))
            }

            is Result.Failure -> {
                _buyBookState.emit(Data(
                    responseType = if(result.exception is NoInternetException) Status.HTTP_UNAVAILABLE else Status.ERROR,
                    error = result.exception
                ))
            }
        }
    }

    fun addFreeBookToLibrary(bookId: String) = viewModelScope.launch {
        when(val result = repository.addFreeBookToLibrary(bookId)) {
            is Result.Success -> {
                _addFreeBookState.emit(Data(
                    responseType = Status.SUCCESSFUL,
                    data = result.data
                ))
            }

            is Result.Failure -> {
                _addFreeBookState.emit(Data(
                    responseType = if(result.exception is NoInternetException) Status.HTTP_UNAVAILABLE else Status.ERROR,
                    error = result.exception
                ))
            }
        }
    }

    suspend fun isBookPurchased(bookId: String): Result<Boolean> {
        return repository.isBookAlreadyPurchased(bookId)
    }

}