package com.wonderslate.domain.usecase.user_details

import com.wonderslate.commons.Result
import com.wonderslate.data.repository.UserSelectedCategoryRepository
import com.wonderslate.domain.UseCase
import com.wonderslate.domain.entities.UserSelectedCategoryData
import org.json.JSONObject

class UserSelectedCategoryUseCase (private val userSelectedCategoryRepository: UserSelectedCategoryRepository):
    UseCase<UserSelectedCategoryData, JSONObject> {
    override suspend fun execute(input: UserSelectedCategoryData): Result<JSONObject> {
        return when (val result = userSelectedCategoryRepository.getUserSelectedCategory()) {
            is Result.Success -> {
                Result.Success(result.data)
            }

            is Result.Failure -> {
                Result.Failure(result.exception)
            }
        }
    }
}