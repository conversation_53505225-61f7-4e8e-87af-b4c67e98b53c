package com.wonderslate.domain.usecase.tests

import com.wonderslate.commons.Result
import com.wonderslate.data.repository.HomeViewRepository
import com.wonderslate.domain.UseCase
import org.json.JSONObject

class DTDatesUseCase(val repository: HomeViewRepository): UseCase<String, JSONObject> {
    override suspend fun execute(input: String): Result<JSONObject> {
        return when(val response = repository.fetchDailyTestDates(input)) {
            is Result.Success -> Result.Success(response.data)
            is Result.Failure -> Result.Failure(response.exception)
        }
    }
}