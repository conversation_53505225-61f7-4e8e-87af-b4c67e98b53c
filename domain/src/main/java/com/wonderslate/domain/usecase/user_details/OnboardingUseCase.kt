package com.wonderslate.domain.usecase.user_details

import com.wonderslate.commons.Result
import com.wonderslate.data.repository.OnBoardingRepository
import com.wonderslate.domain.UseCase
import com.wonderslate.domain.entities.OnBoardingData
import org.json.JSONObject

class OnBoardingUseCase (private val onBoardingRepository: OnBoardingRepository) :
    UseCase<OnBoardingData, JSONObject> {
    override suspend fun execute(input: OnBoardingData): Result<JSONObject> {
        return when (val result = onBoardingRepository.getDefaultStoreCategory()) {
            is Result.Success -> {
                Result.Success(result.data)
            }

            is Result.Failure -> {
                Result.Failure(result.exception)
            }
        }
    }

}