package com.wonderslate.domain.usecase.report

import com.wonderslate.commons.Result
import com.wonderslate.data.repository.HomeViewRepository
import com.wonderslate.domain.UseCase
import com.wonderslate.domain.entities.report.QuizReportInput
import org.json.JSONObject

class QuizReportUseCase(val repository: HomeViewRepository): UseCase<QuizReportInput, JSONObject> {
    override suspend fun execute(input: QuizReportInput): Result<JSONObject> {
        return when(val result = repository.submitQuizIssue(input.issueText,
                                                            input.issueId,
                                                            input.selectedIssue)) {
            is Result.Success -> Result.Success(data = result.data)
            is Result.Failure -> Result.Failure(exception = result.exception)
        }
    }
}