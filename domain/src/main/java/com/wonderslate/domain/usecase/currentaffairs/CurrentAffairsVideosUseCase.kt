package com.wonderslate.domain.usecase.currentaffairs

import com.wonderslate.commons.Result
import com.wonderslate.data.repository.DashBoardRepository
import com.wonderslate.domain.UseCase
import com.wonderslate.domain.mappers.DataMapper
import org.json.JSONObject

class CurrentAffairsVideosUseCase (private val dashboardRepository: DashBoardRepository) : UseCase<String, JSONObject>
{
    override suspend fun execute(input: String): Result<JSONObject> {
        return when (val result = dashboardRepository.getCurrentAffairsVideos(DataMapper.currentAffairsForVideosRequest(input))) {
            is Result.Success -> {
                Result.Success(result.data)
            }

            is Result.Failure -> {
                Result.Failure(result.exception)
            }
        }
    }

}