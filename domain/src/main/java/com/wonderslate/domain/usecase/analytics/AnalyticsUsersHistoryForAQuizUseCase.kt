package com.wonderslate.domain.usecase.analytics

import com.wonderslate.commons.Result
import com.wonderslate.data.repository.HomeViewRepository
import com.wonderslate.domain.NoInputUseCase
import com.wonderslate.domain.UseCase
import com.wonderslate.domain.entities.UserHistoryForQuizData
import com.wonderslate.domain.entities.UserHistoryforAllQuiz
import com.wonderslate.domain.mappers.DataMapper
import org.json.JSONObject

class AnalyticsUsersHistoryForAQuizUseCase (private val dashboardRepository: HomeViewRepository) :
    UseCase<UserHistoryForQuizData, JSONObject>
{
    override suspend fun execute(input: UserHistoryForQuizData): Result<JSONObject> {
        return when (val result = dashboardRepository.fetchUsersHistoryForAQuiz(DataMapper.userHistoryForQuizData(input))) {
            is Result.Success -> {
                Result.Success(result.data)
            }

            is Result.Failure -> {
                Result.Failure(result.exception)
            }
        }
    }
}