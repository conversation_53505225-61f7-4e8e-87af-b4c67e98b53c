package com.wonderslate.domain.usecase.user_details

import com.wonderslate.commons.Result
import com.wonderslate.data.network.WSAPIManager
import com.wonderslate.data.preferences.WonderPubSharedPrefs
import com.wonderslate.domain.UseCase
import org.json.JSONObject

class CacheUserDetailsUseCase(
    private val sharedPrefs: WonderPubSharedPrefs
): UseCase<JSONObject, Boolean> {
    override suspend fun execute(jObj: JSONObject): Result<<PERSON>olean> {
        sharedPrefs.username = jObj.getString("name")
        sharedPrefs.usermobile = jObj.getString("mobile")
        sharedPrefs.userId = jObj.getString("id")
        sharedPrefs.userState = jObj.getString("state")
        sharedPrefs.userDistrict =
            jObj.getString("district")
        sharedPrefs.userImage =
            WSAPIManager.SERVICE.toString()
                .plus("funlearn/showProfileImage?id=" + jObj.getString("id"))
                .plus("&fileName=")
                .plus(jObj.getString("profilePic") + "&type=user&imgType=passport")

        jObj.optString("email").takeIf { it.isNotBlank() && it != "null" }?.let {
            sharedPrefs.setUserEmail(it)
        }
        return Result.Success(true)
    }

}