package com.wonderslate.domain.usecase.currentaffairs

import com.wonderslate.commons.Result
import com.wonderslate.data.repository.DashBoardRepository
import com.wonderslate.domain.UseCase
import com.wonderslate.domain.entities.CurrentAffairsData
import com.wonderslate.domain.entities.ShopBooksData
import com.wonderslate.domain.mappers.DataMapper
import org.json.JSONObject


class ShopBooksUseCase (private val dashboardRepository: DashBoardRepository) : UseCase<ShopBooksData, JSONObject>
{
    override suspend fun execute(input: ShopBooksData): Result<JSONObject> {
        return when (val result = dashboardRepository.getShopBookDetails(DataMapper.ShopBooksRequest(input))) {
            is Result.Success -> {
                Result.Success(result.data)
            }

            is Result.Failure -> {
                Result.Failure(result.exception)
            }
        }
    }

}