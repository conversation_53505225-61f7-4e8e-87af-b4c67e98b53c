package com.wonderslate.domain.di



import com.wonderslate.domain.usecase.analytics.*
import com.wonderslate.domain.usecase.currentaffairs.*
import com.wonderslate.domain.usecase.log.LogResourceActivityUseCase
import com.wonderslate.domain.usecase.login.LoginUseCase
import com.wonderslate.domain.usecase.otp.SendOtpUseCase
import com.wonderslate.domain.usecase.otp.ValidateOtpUseCase
import com.wonderslate.domain.usecase.report.QuizReportUseCase
import com.wonderslate.domain.usecase.signup.SignupUseCase
import com.wonderslate.domain.usecase.signup.UpdateUserImageUseCase
import com.wonderslate.domain.usecase.signup.UpdateUserProfileUseCase
import com.wonderslate.domain.usecase.tests.DTDatesUseCase
import com.wonderslate.domain.usecase.tests.DTQuizUseCase
import com.wonderslate.domain.usecase.tests.DailyTestsUseCase
import com.wonderslate.domain.usecase.user_details.*
import org.koin.core.context.loadKoinModules
import org.koin.dsl.module

object DomainDependencySetup {

    private val useCaseModules = module {
        factory { LoginUseCase(get()) }
        factory { SendOtpUseCase(get()) }
        factory { ValidateOtpUseCase(get()) }
        factory { UserDetailsUseCase(get()) }
        factory { CacheUserDetailsUseCase(get()) }
        factory { SignupUseCase(get()) }
        factory { OnBoardingUseCase(get()) }
        factory { UserSelectedCategoryUseCase(get())}
        factory { UpdateUserSelectedCategoryUseCase(get())}

        factory { CurrentAffairsStartEndDatesUseCase(get()) }
        factory { CurrentAffairsDatesUseCase(get()) }
        factory { CurrentAffairsQuizUseCase(get()) }
        factory { CFWeeklyQuizUseCase(get()) }
        factory { CurrentAffairsReadingMaterialsUseCase(get()) }
        factory { CAWeeklyCurrentAffairsReadingMaterials(get()) }
        factory { CurrentAffairsVideosUseCase(get()) }
        factory { CurrentAffairsAudioUseCase(get()) }
        factory { QuizQuestionAnswersUseCase(get()) }
        factory { CurrentAffairsUserDetailsUseCase(get()) }
        factory { CurrentAffairsUserRankUseCase(get()) }

        factory { UpdateUserProfileUseCase(get()) }
        factory { UpdateUserImageUseCase(get()) }
        factory { CheckForReadingMaterialCardUseCase(get()) }
        factory { SendFirebaseTokenToServerUseCase(get()) }

        factory { ClearAllCachedDataUseCase(get()) }

        factory { DailyTestsUseCase(get()) }
        factory { DTDatesUseCase(get()) }
        factory { DTQuizUseCase(get()) }

        factory { QuizReportUseCase(get()) }
        factory { ShopBooksUseCase(get()) }


        factory { AnalyticsQuizzesAttemptedUseCase(get()) }
        factory { AnalyticsUsersHistoryForAllQuizzesUseCase(get()) }
        factory { AnalyticsUsersHistoryForAQuizUseCase(get()) }
        factory { AnalyticsUsersLastQuizUseCase(get()) }
        factory { AnalyticsFullQuizDetailsUseCase(get()) }

        factory { LogResourceActivityUseCase(get()) }
    }

    fun inject() {
        loadKoinModules(
            useCaseModules
        )
    }
}