package com.naveed.ytextractor;

import android.app.Activity;
import android.graphics.Color;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;
import com.naveed.ytextractor.model.YoutubeMedia;
import com.naveed.ytextractor.model.YoutubeMeta;
import com.naveed.ytextractor.utils.ContextUtils;
import com.naveed.ytextractor.utils.LogUtils;
import java.util.List;

public class MainActivity extends Activity {






	private EditText edit;

	private Button btn;




    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.main);
		ContextUtils.init(this);
		edit = (EditText)findViewById(R.id.mainEditText1);
		btn = (Button)findViewById(R.id.mainButton1);
		edit.setText("4H4Oizo7oOE");
		edit.setHint("id or url");
		btn.setOnClickListener((new OnClickListener(){

			@Override
			public void onClick(View p1) {
			   Toast.makeText(getApplicationContext(), "Extracting", Toast.LENGTH_LONG).show();

			   new YoutubeStreamExtractor(new YoutubeStreamExtractor.ExtractorListner(){

				   @Override
					   public void onExtractionDone(List<YoutubeMedia> adativeStream, final List<YoutubeMedia> muxedStream, YoutubeMeta meta) {

						   Toast.makeText(getApplicationContext(), meta.getTitle(), Toast.LENGTH_LONG).show();
						   Toast.makeText(getApplicationContext(), meta.getAuthor(), Toast.LENGTH_LONG).show();


						   if (muxedStream.isEmpty()) {LogUtils.log("null ha");
							   return;}
						   String url = muxedStream.get(0).getUrl();
						   LogUtils.log(url);


					   }

				   @Override
				   public void onExtractionGoesWrong(ExtractorException e, String message, String data) {
					   Toast.makeText(getApplicationContext(), e.getMessage(), Toast.LENGTH_LONG).show();
				   }
				   }).Extract(edit.getText().toString());

			}
		}));

    }




}
